// Test the rotation algorithm fix for equal playing time

// We'll run this through the actual app instead of importing directly
const { execSync } = require('child_process');

// Create test players
const testPlayers = [
  { id: '1', name: '<PERSON>', teamRoles: {} },
  { id: '2', name: '<PERSON><PERSON>', teamRoles: {} },
  { id: '3', name: '<PERSON>', teamRoles: {} },
  { id: '4', name: '<PERSON>', teamRoles: {} },
  { id: '5', name: '<PERSON>', teamRoles: {} },
  { id: '6', name: '<PERSON>', teamRoles: {} },
  { id: '7', name: '<PERSON>', teamRoles: {} },
  { id: '8', name: '<PERSON><PERSON>', teamRoles: {} },
  { id: '9', name: '<PERSON>', teamRoles: {} },
  { id: '10', name: '<PERSON>', teamRoles: {} },
  { id: '11', name: 'Indigo', teamRoles: {} },
  { id: '12', name: '<PERSON>', teamRoles: {} }
];

// Test with equal playing time rules
const rules = {
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 2,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  respectPositionLockouts: false, // No restrictions for this test
  equalPlayingTime: true,
  rotateLineupEvery: 1, // CRITICAL: Rotate every inning
  rotatePitcherEvery: 2
};

console.log('🧪 Testing Rotation Algorithm Fix');
console.log('================================');
console.log(`Players: ${testPlayers.length}`);
console.log(`Total Innings: 20`);
console.log(`Rotation Every: ${rules.rotateLineupEvery} innings`);
console.log(`Expected field innings per player: ~${(20 * 9 / testPlayers.length).toFixed(1)}`);
console.log('');

try {
  const lineup = generateOptimalLineup(testPlayers, 20, rules);
  
  // Calculate stats
  const playerStats = new Map();
  testPlayers.forEach(p => playerStats.set(p.name, { field: 0, bench: 0 }));
  
  lineup.forEach(inning => {
    // Count field appearances
    ['pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'].forEach(pos => {
      const player = inning.positions[pos];
      if (player) {
        playerStats.get(player).field++;
      }
    });
    
    // Count bench appearances
    inning.positions.bench.forEach(player => {
      if (player) {
        playerStats.get(player).bench++;
      }
    });
  });
  
  console.log('\n📊 FINAL PLAYING TIME STATISTICS:');
  console.log('================================');
  
  const statsArray = Array.from(playerStats.entries()).sort((a, b) => b[1].field - a[1].field);
  
  statsArray.forEach(([name, stats]) => {
    const percentage = (stats.field / 20 * 100).toFixed(1);
    console.log(`${name.padEnd(10)} Field: ${stats.field.toString().padStart(2)}/20 (${percentage.padStart(5)}%) | Bench: ${stats.bench.toString().padStart(2)}`);
  });
  
  // Calculate balance metrics
  const fieldTimes = statsArray.map(([_, stats]) => stats.field);
  const min = Math.min(...fieldTimes);
  const max = Math.max(...fieldTimes);
  const avg = fieldTimes.reduce((a, b) => a + b, 0) / fieldTimes.length;
  const range = max - min;
  
  console.log('\n⚖️ BALANCE METRICS:');
  console.log('==================');
  console.log(`Min field innings: ${min}`);
  console.log(`Max field innings: ${max}`);
  console.log(`Average: ${avg.toFixed(1)}`);
  console.log(`Range: ${range}`);
  console.log(`Ideal per player: ${(20 * 9 / testPlayers.length).toFixed(1)}`);
  
  // Check if the fix worked
  if (range <= 3) {
    console.log('\n✅ SUCCESS: Playing time is well balanced!');
  } else {
    console.log('\n❌ FAILURE: Playing time imbalance detected!');
    console.log(`Players with ${max} innings: ${statsArray.filter(([_, s]) => s.field === max).map(([n, _]) => n).join(', ')}`);
    console.log(`Players with ${min} innings: ${statsArray.filter(([_, s]) => s.field === min).map(([n, _]) => n).join(', ')}`);
  }
  
  // Check bench streaks
  console.log('\n🪑 BENCH STREAK ANALYSIS:');
  console.log('========================');
  let maxBenchStreak = 0;
  let benchStreakViolations = [];
  
  testPlayers.forEach(player => {
    let currentStreak = 0;
    let maxStreak = 0;
    
    lineup.forEach(inning => {
      if (inning.positions.bench.includes(player.name)) {
        currentStreak++;
        maxStreak = Math.max(maxStreak, currentStreak);
      } else {
        currentStreak = 0;
      }
    });
    
    if (maxStreak > rules.maxConsecutiveBenchInnings) {
      benchStreakViolations.push({ player: player.name, streak: maxStreak });
    }
    maxBenchStreak = Math.max(maxBenchStreak, maxStreak);
  });
  
  console.log(`Max consecutive bench innings: ${maxBenchStreak} (limit: ${rules.maxConsecutiveBenchInnings})`);
  
  if (benchStreakViolations.length > 0) {
    console.log('❌ Bench streak violations:');
    benchStreakViolations.forEach(v => {
      console.log(`  - ${v.player}: ${v.streak} consecutive innings`);
    });
  } else {
    console.log('✅ No bench streak violations!');
  }
  
} catch (error) {
  console.error('Error generating lineup:', error);
}
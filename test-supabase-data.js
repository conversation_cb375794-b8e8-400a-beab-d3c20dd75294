// Test script to verify data storage and retrieval from Supabase
import { supabase } from "./src/integrations/supabase/node-client.js";

// Initialize dotenv

async function testSupabaseDataOperations() {
  console.log('=== SUPABASE DATA OPERATIONS TEST ===');
  
  // Get Supabase credentials
      
    
  // Create Supabase client
  
  
  try {
    // 0. Sign in first to bypass RLS
    console.log('\n0. Signing in to bypass RLS...');
    
    // Use demo account credentials
    const demoEmail = '<EMAIL>';
    const demoPassword = 'demo1234';
    
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: demoEmail,
      password: demoPassword
    });
    
    if (authError) {
      console.error('❌ Failed to sign in:', authError);
      return;
    }
    
    console.log('✅ Successfully signed in as:', authData.user.email);
    const userId = authData.user.id;
    
    // 1. Test creating a test team
    console.log('\n1. Creating a test team...');
    // Generate a proper UUID
    const testTeamId = crypto.randomUUID();
    // Use the authenticated user's ID
    const testUserId = userId;
    
    const { data: teamData, error: teamError } = await supabase
      .from('teams')
      .insert({
        id: testTeamId,
        name: 'Test Team',
        user_id: testUserId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select();
    
    if (teamError) {
      console.error('❌ Failed to create test team:', teamError);
      return;
    }
    
    console.log('✅ Successfully created test team:', teamData);
    
    // 2. Test creating a test player
    console.log('\n2. Creating a test player...');
    const testPlayerId = crypto.randomUUID();
    
    const { data: playerData, error: playerError } = await supabase
      .from('players')
      .insert({
        id: testPlayerId,
        name: 'Test Player',
        team_id: testTeamId,
        user_id: testUserId,
        pitcher_restriction: false,
        catcher_restriction: false,
        first_base_restriction: false,
        other_restriction: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select();
    
    if (playerError) {
      console.error('❌ Failed to create test player:', playerError);
    } else {
      console.log('✅ Successfully created test player:', playerData);
    }
    
    // 3. Test retrieving the team with its player
    console.log('\n3. Retrieving the test team...');
    const { data: retrievedTeamData, error: retrieveError } = await supabase
      .from('teams')
      .select('*')
      .eq('id', testTeamId);
    
    if (retrieveError) {
      console.error('❌ Failed to retrieve test team:', retrieveError);
    } else {
      console.log('✅ Successfully retrieved test team:', retrievedTeamData);
    }
    
    // 4. Test retrieving the player
    console.log('\n4. Retrieving the test player...');
    const { data: retrievedPlayerData, error: retrievePlayerError } = await supabase
      .from('players')
      .select('*')
      .eq('team_id', testTeamId);
    
    if (retrievePlayerError) {
      console.error('❌ Failed to retrieve test player:', retrievePlayerError);
    } else {
      console.log('✅ Successfully retrieved test player:', retrievedPlayerData);
    }
    
    // 5. Test updating the team
    console.log('\n5. Updating the test team...');
    const { data: updatedTeamData, error: updateError } = await supabase
      .from('teams')
      .update({ name: 'Updated Test Team' })
      .eq('id', testTeamId)
      .select();
    
    if (updateError) {
      console.error('❌ Failed to update test team:', updateError);
    } else {
      console.log('✅ Successfully updated test team:', updatedTeamData);
    }
    
    // 6. Test cleanup - delete the test data
    console.log('\n6. Cleaning up test data...');
    
    // Delete player first (foreign key constraint)
    const { error: deletePlayerError } = await supabase
      .from('players')
      .delete()
      .eq('id', testPlayerId);
    
    if (deletePlayerError) {
      console.error('❌ Failed to delete test player:', deletePlayerError);
    } else {
      console.log('✅ Successfully deleted test player');
    }
    
    // Delete team
    const { error: deleteTeamError } = await supabase
      .from('teams')
      .delete()
      .eq('id', testTeamId);
    
    if (deleteTeamError) {
      console.error('❌ Failed to delete test team:', deleteTeamError);
    } else {
      console.log('✅ Successfully deleted test team');
    }
    
    console.log('\n=== TEST COMPLETE ===');
  } catch (err) {
    console.error('❌ Error during test:', err);
  }
}

testSupabaseDataOperations();
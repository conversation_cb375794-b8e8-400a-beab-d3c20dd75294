
// Mock environment variables
process.env.VITE_SUPABASE_URL = 'http://localhost:54321';
process.env.VITE_SUPABASE_ANON_KEY = 'test-key';

// Mock import.meta.env for the supabase client
(globalThis as any).import = {
  meta: {
    env: {
      VITE_SUPABASE_URL: 'http://localhost:54321',
      VITE_SUPABASE_ANON_KEY: 'test-key'
    }
  }
};

import {
  generateCompleteLineup,
  generateOptimalLineup,
  generateLineupFromFirstInning,
  createInitialInningLineup,
  rotatePlayersForNextInning,
  validateLineup,
  validateRotation,
  LineupRotator,
  ConstraintSolver,
  PlayerEligibilityCache,
  SeededRandom,
  LineupGenerationError,
  InsufficientPlayersError,
  PositionConstraintError
} from './src/lib/utils-enhanced.ts';

export {
  generateCompleteLineup,
  generateOptimalLineup,
  generateLineupFromFirstInning,
  createInitialInningLineup,
  rotatePlayersForNextInning,
  validateLineup,
  validateRotation,
  LineupRotator,
  ConstraintSolver,
  PlayerEligibilityCache,
  SeededRandom,
  LineupGenerationError,
  InsufficientPlayersError,
  PositionConstraintError
};

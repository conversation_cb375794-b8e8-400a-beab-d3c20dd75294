import { supabase } from "./src/integrations/supabase/node-client.js";

async function diagnoseUser(email) {
  console.log(`\n🔍 Diagnosing subscription issues for: ${email}\n`);

  try {
    // 1. Check if user profile exists
    console.log('1. Checking user profile...');
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('email', email)
      .single();

    if (profileError || !profile) {
      console.log('❌ User profile not found!');
      console.log('Error:', profileError?.message);
      return;
    }

    console.log('✅ User profile found:');
    console.log(`   - ID: ${profile.id}`);
    console.log(`   - Email: ${profile.email}`);
    console.log(`   - Admin: ${profile.is_admin}`);
    console.log(`   - Created: ${profile.created_at}`);

    // 2. Check all subscriptions for this user
    console.log('\n2. Checking subscriptions...');
    const { data: subscriptions, error: subError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', profile.id)
      .order('created_at', { ascending: false });

    if (subError) {
      console.log('❌ Error fetching subscriptions:', subError.message);
      return;
    }

    if (!subscriptions || subscriptions.length === 0) {
      console.log('❌ No subscriptions found for this user!');
      console.log('\n💡 Solution: Create a subscription record for this user');
      return;
    }

    console.log(`✅ Found ${subscriptions.length} subscription(s):`);
    subscriptions.forEach((sub, index) => {
      console.log(`\n   Subscription ${index + 1}:`);
      console.log(`   - ID: ${sub.id}`);
      console.log(`   - Is Paid: ${sub.is_paid}`);
      console.log(`   - Tier: ${sub.tier || '❌ MISSING'}`);
      console.log(`   - Team Limit: ${sub.team_limit || '❌ MISSING'}`);
      console.log(`   - Stripe ID: ${sub.stripe_subscription_id || 'None'}`);
      console.log(`   - Expires: ${sub.expires_at || 'Never'}`);
      console.log(`   - Created: ${sub.created_at}`);
      console.log(`   - Updated: ${sub.updated_at}`);
    });

    // 3. Test the exact query used by the app
    console.log('\n3. Testing app payment check query...');
    const { data: paymentCheck, error: paymentError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', profile.id)
      .eq('is_paid', true)
      .maybeSingle();

    if (paymentError) {
      console.log('❌ Payment check query failed:', paymentError.message);
    } else if (!paymentCheck) {
      console.log('❌ No paid subscription found!');
      console.log('   The app query returned no results.');
    } else {
      console.log('✅ Payment check passed!');
      console.log('   The app should recognize this user as paid.');
    }

    // 4. Identify specific issues
    console.log('\n4. Issue Analysis:');
    const paidSubs = subscriptions.filter(s => s.is_paid);
    
    if (paidSubs.length === 0) {
      console.log('❌ No paid subscriptions found');
      console.log('💡 Solution: Update subscription to is_paid = true');
    } else {
      let hasIssues = false;
      paidSubs.forEach(sub => {
        if (!sub.tier) {
          console.log('❌ Missing "tier" field in paid subscription');
          hasIssues = true;
        }
        if (!sub.team_limit) {
          console.log('❌ Missing "team_limit" field in paid subscription');
          hasIssues = true;
        }
      });
      
      if (!hasIssues) {
        console.log('✅ All required fields present');
        console.log('\n⚠️  If user still can\'t access paid features:');
        console.log('   1. Have them sign out and sign back in');
        console.log('   2. Clear browser cache');
        console.log('   3. Check for RLS policy issues');
      } else {
        console.log('\n💡 Solution: Fix missing fields');
      }
    }

    // 5. Offer to fix issues
    if (subscriptions.length > 0) {
      const needsFix = subscriptions.some(s => s.is_paid && (!s.tier || !s.team_limit));
      if (needsFix) {
        console.log('\n5. Attempting to fix missing fields...');
        
        for (const sub of subscriptions.filter(s => s.is_paid)) {
          if (!sub.tier || !sub.team_limit) {
            const { error: updateError } = await supabase
              .from('subscriptions')
              .update({
                tier: sub.tier || 'starter',
                team_limit: sub.team_limit || 1,
                updated_at: new Date().toISOString()
              })
              .eq('id', sub.id);

            if (updateError) {
              console.log(`❌ Failed to update subscription ${sub.id}:`, updateError.message);
            } else {
              console.log(`✅ Fixed subscription ${sub.id} - added missing fields`);
            }
          }
        }
      }
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Get email from command line
const email = process.argv[2];
if (!email) {
  console.log('Usage: node diagnose-subscription-issues.js <email>');
  process.exit(1);
}

diagnoseUser(email);
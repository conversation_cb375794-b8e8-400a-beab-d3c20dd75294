import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugSubscriptions() {
  try {
    // Get the most recent users
    const { data: recentUsers, error: usersError } = await supabase
      .from('profiles')
      .select('id, email, full_name, created_at')
      .order('created_at', { ascending: false })
      .limit(10);

    if (usersError) {
      console.error('Error fetching users:', usersError);
      return;
    }

    console.log('\n=== Recent Users ===');
    for (const user of recentUsers) {
      console.log(`\nUser: ${user.email} (${user.full_name})`);
      console.log(`ID: ${user.id}`);
      console.log(`Created: ${user.created_at}`);

      // Check subscriptions for this user
      const { data: subscriptions, error: subError } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', user.id);

      if (subError) {
        console.error('Error fetching subscriptions:', subError);
      } else if (subscriptions && subscriptions.length > 0) {
        console.log('Subscriptions:');
        for (const sub of subscriptions) {
          console.log(`  - ID: ${sub.id}`);
          console.log(`    is_paid: ${sub.is_paid}`);
          console.log(`    tier: ${sub.tier}`);
          console.log(`    team_limit: ${sub.team_limit}`);
          console.log(`    amount: ${sub.amount}`);
          console.log(`    created_at: ${sub.created_at}`);
        }
      } else {
        console.log('No subscriptions found');
      }
    }

    // Check if tier and team_limit columns exist
    console.log('\n=== Checking Subscription Table Structure ===');
    const { data: tableInfo, error: tableError } = await supabase
      .from('subscriptions')
      .select('*')
      .limit(1);

    if (!tableError && tableInfo && tableInfo.length > 0) {
      console.log('Subscription table columns:');
      console.log(Object.keys(tableInfo[0]));
    }

  } catch (error) {
    console.error('Error in debug script:', error);
  }
}

debugSubscriptions();
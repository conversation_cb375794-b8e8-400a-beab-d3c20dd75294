#!/usr/bin/env node

/**
 * Direct Test for Lineup Rotation Algorithm
 * 
 * This test directly calls the rotation functions to debug the
 * "Invalid rotation plan" error without UI interaction.
 */

// Since we can't easily import TypeScript modules in Node.js,
// let's create a standalone test that replicates the core logic

console.log('🚀 Starting Lineup Rotation Debug Test');
console.log('=' .repeat(60));

// Mock <PERSON>'s player data (simplified)
const PLAYERS = [
  { id: '1', name: 'Avalon', positionRestrictions: {} },
  { id: '2', name: '<PERSON>', positionRestrictions: {} },
  { id: '3', name: '<PERSON>', positionRestrictions: {} },
  { id: '4', name: '<PERSON>', positionRestrictions: {} },
  { id: '5', name: '<PERSON>', positionRestrictions: {} },
  { id: '6', name: '<PERSON> <PERSON>', positionRestrictions: {} },
  { id: '7', name: '<PERSON>', positionRestrictions: {} },
  { id: '8', name: '<PERSON><PERSON>', positionRestrictions: {} },
  { id: '9', name: '<PERSON><PERSON><PERSON>', positionRestrictions: {} }
];

const RULES = {
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 2,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  respectPositionLockouts: true,
  equalPlayingTime: true,
  rotateLineupEvery: 1,
  rotatePitcherEvery: 2
};

// Replicate the validation logic from utils-enhanced.ts
function validatePlan(plan, players) {
  const errors = [];
  
  // Only check the 9 required baseball field positions
  const requiredPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase',
                            'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
  const assignedPositions = new Set(plan.fieldAssignments.keys());

  console.log('🔍 VALIDATING ROTATION PLAN:');
  console.log('  Required positions:', requiredPositions);
  console.log('  Assigned positions:', Array.from(assignedPositions));
  console.log('  Field assignments:', Object.fromEntries(plan.fieldAssignments));
  console.log('  Bench assignments:', plan.benchAssignments);
  console.log('  Available players:', players.map(p => p.name));

  for (const pos of requiredPositions) {
    if (!assignedPositions.has(pos)) {
      errors.push(`Position ${pos} not assigned`);
      console.log(`❌ Missing position: ${pos}`);
    }
  }

  // Check no duplicate assignments
  const assignedPlayers = new Set();
  for (const player of plan.fieldAssignments.values()) {
    if (assignedPlayers.has(player)) {
      errors.push(`Player ${player} assigned to multiple positions`);
      console.log(`❌ Duplicate player assignment: ${player}`);
    }
    assignedPlayers.add(player);
  }

  // Validate that all assigned players exist in the available players list
  const availablePlayerNames = new Set(players.map(p => p.name));
  for (const playerName of assignedPlayers) {
    if (!availablePlayerNames.has(playerName)) {
      errors.push(`Assigned player ${playerName} not found in available players`);
      console.log(`❌ Unknown player: ${playerName}`);
    }
  }

  console.log('🎯 Validation result:', { valid: errors.length === 0, errors });
  return { valid: errors.length === 0, errors };
}

// Simplified rotation plan creation to test the core logic
function createTestRotationPlan(currentInning, players, inningNumber) {
  console.log(`\n📋 Creating rotation plan for inning ${inningNumber}`);
  
  const plan = {
    moves: [],
    benchAssignments: [],
    fieldAssignments: new Map()
  };

  // Check if we should rotate (simplified logic)
  const shouldRotate = (inningNumber - 1) % RULES.rotateLineupEvery === 0 && inningNumber > 1;
  console.log(`🔄 Should rotate: ${shouldRotate} (inning ${inningNumber}, rotateEvery ${RULES.rotateLineupEvery})`);

  if (!shouldRotate) {
    console.log('⏸️ No rotation needed, copying current lineup');
    
    // CRITICAL: When no rotation is needed, we must copy all current assignments
    const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase',
                           'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
    
    fieldPositions.forEach(pos => {
      const player = currentInning.positions[pos];
      if (player) {
        plan.fieldAssignments.set(pos, player);
        console.log(`  📍 Copied ${pos}: ${player}`);
      } else {
        console.log(`  ❌ Missing position in current inning: ${pos}`);
      }
    });
    
    plan.benchAssignments = [...(currentInning.positions.bench || [])];
    
    console.log('📋 No-rotation plan created:', {
      fieldAssignments: Object.fromEntries(plan.fieldAssignments),
      benchAssignments: plan.benchAssignments
    });
    
    return plan;
  }

  // For rotation case, we'd need to implement the full constraint solver logic
  // For now, let's just create a simple rotation
  console.log('🔄 Implementing rotation logic...');

  const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase',
                         'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];

  // Simple rotation: shift everyone one position
  for (let i = 0; i < fieldPositions.length; i++) {
    const currentPos = fieldPositions[i];
    const nextPos = fieldPositions[(i + 1) % fieldPositions.length];
    const player = currentInning.positions[currentPos];

    if (player) {
      plan.fieldAssignments.set(nextPos, player);
      console.log(`  🔄 Rotated ${player}: ${currentPos} → ${nextPos}`);
    }
  }

  // CRITICAL FIX: Ensure all positions are assigned (like the real fix)
  const missingPositions = fieldPositions.filter(pos => !plan.fieldAssignments.has(pos));
  if (missingPositions.length > 0) {
    console.log(`🚨 EMERGENCY: Missing positions: ${missingPositions.join(', ')}`);

    // Emergency assignment from bench
    const assignedPlayers = new Set(plan.fieldAssignments.values());
    const availablePlayers = players.filter(p => !assignedPlayers.has(p.name));

    for (let i = 0; i < missingPositions.length && i < availablePlayers.length; i++) {
      const position = missingPositions[i];
      const player = availablePlayers[i];
      plan.fieldAssignments.set(position, player.name);
      console.log(`  🚨 Emergency assigned ${player.name} to ${position}`);
    }
  }

  // Update bench assignments
  const finalAssignedPlayers = new Set(plan.fieldAssignments.values());
  plan.benchAssignments = players
    .map(p => p.name)
    .filter(name => !finalAssignedPlayers.has(name));

  return plan;
}

// Test the rotation logic
function testRotation() {
  console.log('\n🧪 Testing Rotation Logic');
  console.log('-'.repeat(40));
  
  // Create a first inning
  const firstInning = {
    inning: 1,
    positions: {
      pitcher: 'Avalon',
      catcher: 'Avery',
      firstBase: 'Bella',
      secondBase: 'Bernie',
      shortstop: 'Charlotte',
      thirdBase: 'Elle B',
      leftField: 'Elle Bizzare',
      centerField: 'Everly P',
      rightField: 'Eveylynn',
      bench: []
    }
  };

  console.log('📋 First inning setup:', firstInning.positions);

  // Test rotation to second inning (should NOT rotate due to rotateLineupEvery = 1)
  console.log('\n🔄 Testing rotation to inning 2...');
  const secondInningPlan = createTestRotationPlan(firstInning, PLAYERS, 2);
  const secondValidation = validatePlan(secondInningPlan, PLAYERS);
  
  if (secondValidation.valid) {
    console.log('✅ Second inning plan is VALID');
  } else {
    console.log('❌ Second inning plan is INVALID');
    console.log('Errors:', secondValidation.errors);
  }

  // Test rotation to third inning (should rotate)
  console.log('\n🔄 Testing rotation to inning 3...');
  const secondInning = {
    inning: 2,
    positions: {
      ...Object.fromEntries(secondInningPlan.fieldAssignments),
      bench: secondInningPlan.benchAssignments
    }
  };
  
  const thirdInningPlan = createTestRotationPlan(secondInning, PLAYERS, 3);
  const thirdValidation = validatePlan(thirdInningPlan, PLAYERS);
  
  if (thirdValidation.valid) {
    console.log('✅ Third inning plan is VALID');
  } else {
    console.log('❌ Third inning plan is INVALID');
    console.log('Errors:', thirdValidation.errors);
  }
}

// Test edge cases
function testEdgeCases() {
  console.log('\n🧪 Testing Edge Cases');
  console.log('-'.repeat(40));
  
  // Test with incomplete first inning (missing positions)
  const incompleteInning = {
    inning: 1,
    positions: {
      pitcher: 'Avalon',
      catcher: 'Avery',
      firstBase: 'Bella',
      // Missing other positions
      bench: ['Bernie', 'Charlotte', 'Elle B', 'Elle Bizzare', 'Everly P', 'Eveylynn']
    }
  };

  console.log('📋 Testing with incomplete inning:', incompleteInning.positions);
  
  const plan = createTestRotationPlan(incompleteInning, PLAYERS, 2);
  const validation = validatePlan(plan, PLAYERS);
  
  if (validation.valid) {
    console.log('✅ Incomplete inning test passed (unexpected)');
  } else {
    console.log('❌ Incomplete inning test failed (expected)');
    console.log('Errors:', validation.errors);
  }
}

// Run all tests
function runAllTests() {
  console.log('🚀 Running Lineup Rotation Debug Tests');
  console.log('Using simplified logic to identify the root cause\n');
  
  try {
    testRotation();
    testEdgeCases();
    
    console.log('\n' + '='.repeat(60));
    console.log('🎯 TEST SUMMARY');
    console.log('='.repeat(60));
    console.log('✅ Tests completed successfully');
    console.log('📋 Check the validation output above to identify issues');
    console.log('🔍 Look for "❌ Missing position" messages');
    
  } catch (error) {
    console.log('\n❌ Test failed with error:', error.message);
    console.log('Stack:', error.stack);
  }
}

// Run the tests
runAllTests();

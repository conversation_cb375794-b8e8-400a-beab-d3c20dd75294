require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing environment variables');
  process.exit(1);
}

// Use service role to bypass RLS
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function fixUser(email) {
  console.log(`\n🔧 Fixing auth issues for: ${email}\n`);

  try {
    // 1. Get user from auth
    const { data: authData } = await supabase.auth.admin.listUsers();
    const user = authData.users.find(u => u.email === email);
    
    if (!user) {
      console.error('❌ User not found in auth.users');
      console.log('   Please have the user sign up first');
      return;
    }

    console.log('✅ Found user in auth.users');
    console.log('   User ID:', user.id);

    // 2. Create/update profile (bypass RLS with service role)
    console.log('\n📝 Creating/updating profile...');
    const { error: profileError } = await supabase
      .from('profiles')
      .upsert({
        id: user.id,
        email: user.email,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'id'
      });

    if (profileError) {
      console.error('❌ Error with profile:', profileError);
    } else {
      console.log('✅ Profile created/updated successfully');
    }

    // 3. Create/update subscription (bypass RLS with service role)
    console.log('\n💳 Creating/updating subscription...');
    const { error: subError } = await supabase
      .from('subscriptions')
      .upsert({
        user_id: user.id,
        is_paid: true,
        tier: 'pro',
        team_limit: 10,
        currency: 'usd',
        amount: 0,
        payment_date: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id'
      });

    if (subError) {
      console.error('❌ Error with subscription:', subError);
    } else {
      console.log('✅ Subscription created/updated successfully');
    }

    // 4. Verify the fix
    console.log('\n🔍 Verifying fix...');
    
    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    const { data: subscription } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (profile && subscription) {
      console.log('\n✅ SUCCESS! User is now properly set up:');
      console.log('   - Profile exists');
      console.log('   - Subscription exists');
      console.log('   - Is Paid:', subscription.is_paid);
      console.log('   - Tier:', subscription.tier);
      console.log('   - Team Limit:', subscription.team_limit);
      console.log('\n📌 Next steps:');
      console.log('   1. Have the user sign out completely');
      console.log('   2. Sign back in');
      console.log('   3. They should now have access to the app');
    } else {
      console.log('\n⚠️  Partial success - please check the database');
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the fix
const email = process.argv[2];
if (!email) {
  console.error('Please provide an email address');
  console.log('Usage: node fix-user-auth-immediate.cjs <EMAIL>');
  process.exit(1);
}

fixUser(email);
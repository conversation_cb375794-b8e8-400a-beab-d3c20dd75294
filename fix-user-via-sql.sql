-- <NAME_EMAIL> directly via SQL
-- Run this in Supabase SQL Editor

-- First, find the user in auth.users
WITH auth_user AS (
  SELECT id, email, created_at
  FROM auth.users
  WHERE email = '<EMAIL>'
  LIMIT 1
)
-- Create profile if missing
INSERT INTO profiles (id, email, created_at, updated_at)
SELECT 
  id,
  email,
  COALESCE(created_at, NOW()),
  NOW()
FROM auth_user
ON CONFLICT (id) DO NOTHING;

-- Create or update subscription to paid status
WITH auth_user AS (
  SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1
)
INSERT INTO subscriptions (
  user_id,
  is_paid,
  tier,
  team_limit,
  amount,
  currency,
  payment_date,
  created_at,
  updated_at
)
SELECT 
  id,
  true,
  'coach',
  5,
  3000,
  'usd',
  NOW(),
  NOW(),
  NOW()
FROM auth_user
ON CONFLICT (user_id) 
DO UPDATE SET
  is_paid = true,
  tier = 'coach',
  team_limit = 5,
  amount = 3000,
  updated_at = NOW();

-- Verify the fix
SELECT 
  p.email,
  p.id as user_id,
  s.is_paid,
  s.tier,
  s.team_limit,
  s.created_at as subscription_created,
  s.updated_at as subscription_updated
FROM profiles p
LEFT JOIN subscriptions s ON p.id = s.user_id
WHERE p.email = '<EMAIL>';
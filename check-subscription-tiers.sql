-- Check if tier and team_limit columns exist in subscriptions table
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public' 
    AND table_name = 'subscriptions'
ORDER BY 
    ordinal_position;

-- Check some sample subscription data
SELECT 
    id,
    user_id,
    is_paid,
    tier,
    team_limit,
    amount,
    created_at
FROM 
    subscriptions
ORDER BY 
    created_at DESC
LIMIT 10;

-- Check if any users have tier/team_limit values
SELECT 
    COUNT(*) as total_subscriptions,
    COUNT(tier) as subscriptions_with_tier,
    COUNT(team_limit) as subscriptions_with_team_limit
FROM 
    subscriptions;
-- Fix RLS policies to properly handle admin access
-- The key is that admins should be able to see ALL data, including their own

-- First, let's create a function to check if a user is an admin
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS boolean AS $$
BEGIN
  RETURN auth.email() IN ('<EMAIL>', '<EMAIL>');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Now fix all the policies to use this function

-- TEAMS table policies
DROP POLICY IF EXISTS "teams_select_policy" ON public.teams;
DROP POLICY IF EXISTS "lineups_select_policy" ON public.lineups;
DROP POLICY IF EXISTS "lineup_innings_select_policy" ON public.lineup_innings;
DROP POLICY IF EXISTS "lineup_attendance_select_policy" ON public.lineup_attendance;
DROP POLICY IF EXISTS "rotation_rules_select_policy" ON public.rotation_rules;
DROP POLICY IF EXISTS "players_select_policy" ON public.players;

-- Create new SELECT policies that properly handle both cases
CREATE POLICY "teams_select_policy" ON public.teams
  FOR SELECT USING (
    auth.uid() = user_id OR public.is_admin()
  );

CREATE POLICY "lineups_select_policy" ON public.lineups
  FOR SELECT USING (
    auth.uid() = user_id OR public.is_admin()
  );

CREATE POLICY "lineup_innings_select_policy" ON public.lineup_innings
  FOR SELECT USING (
    auth.uid() = user_id OR public.is_admin()
  );

CREATE POLICY "lineup_attendance_select_policy" ON public.lineup_attendance
  FOR SELECT USING (
    auth.uid() = user_id OR public.is_admin()
  );

CREATE POLICY "rotation_rules_select_policy" ON public.rotation_rules
  FOR SELECT USING (
    auth.uid() = user_id OR public.is_admin()
  );

CREATE POLICY "players_select_policy" ON public.players
  FOR SELECT USING (
    auth.uid() = user_id OR public.is_admin()
  );

-- Fix INSERT policies
DROP POLICY IF EXISTS "teams_insert_policy" ON public.teams;
DROP POLICY IF EXISTS "lineups_insert_policy" ON public.lineups;
DROP POLICY IF EXISTS "lineup_innings_insert_policy" ON public.lineup_innings;
DROP POLICY IF EXISTS "lineup_attendance_insert_policy" ON public.lineup_attendance;
DROP POLICY IF EXISTS "rotation_rules_insert_policy" ON public.rotation_rules;
DROP POLICY IF EXISTS "players_insert_policy" ON public.players;

CREATE POLICY "teams_insert_policy" ON public.teams
  FOR INSERT WITH CHECK (
    auth.uid() = user_id OR public.is_admin()
  );

CREATE POLICY "lineups_insert_policy" ON public.lineups
  FOR INSERT WITH CHECK (
    auth.uid() = user_id OR public.is_admin()
  );

CREATE POLICY "lineup_innings_insert_policy" ON public.lineup_innings
  FOR INSERT WITH CHECK (
    auth.uid() = user_id OR public.is_admin()
  );

CREATE POLICY "lineup_attendance_insert_policy" ON public.lineup_attendance
  FOR INSERT WITH CHECK (
    auth.uid() = user_id OR public.is_admin()
  );

CREATE POLICY "rotation_rules_insert_policy" ON public.rotation_rules
  FOR INSERT WITH CHECK (
    auth.uid() = user_id OR public.is_admin()
  );

CREATE POLICY "players_insert_policy" ON public.players
  FOR INSERT WITH CHECK (
    auth.uid() = user_id OR public.is_admin()
  );

-- Fix UPDATE policies
DROP POLICY IF EXISTS "teams_update_policy" ON public.teams;
DROP POLICY IF EXISTS "lineups_update_policy" ON public.lineups;
DROP POLICY IF EXISTS "lineup_innings_update_policy" ON public.lineup_innings;
DROP POLICY IF EXISTS "lineup_attendance_update_policy" ON public.lineup_attendance;
DROP POLICY IF EXISTS "rotation_rules_update_policy" ON public.rotation_rules;
DROP POLICY IF EXISTS "players_update_policy" ON public.players;

CREATE POLICY "teams_update_policy" ON public.teams
  FOR UPDATE USING (
    auth.uid() = user_id OR public.is_admin()
  );

CREATE POLICY "lineups_update_policy" ON public.lineups
  FOR UPDATE USING (
    auth.uid() = user_id OR public.is_admin()
  );

CREATE POLICY "lineup_innings_update_policy" ON public.lineup_innings
  FOR UPDATE USING (
    auth.uid() = user_id OR public.is_admin()
  );

CREATE POLICY "lineup_attendance_update_policy" ON public.lineup_attendance
  FOR UPDATE USING (
    auth.uid() = user_id OR public.is_admin()
  );

CREATE POLICY "rotation_rules_update_policy" ON public.rotation_rules
  FOR UPDATE USING (
    auth.uid() = user_id OR public.is_admin()
  );

CREATE POLICY "players_update_policy" ON public.players
  FOR UPDATE USING (
    auth.uid() = user_id OR public.is_admin()
  );

-- Fix DELETE policies
DROP POLICY IF EXISTS "teams_delete_policy" ON public.teams;
DROP POLICY IF EXISTS "lineups_delete_policy" ON public.lineups;
DROP POLICY IF EXISTS "lineup_innings_delete_policy" ON public.lineup_innings;
DROP POLICY IF EXISTS "lineup_attendance_delete_policy" ON public.lineup_attendance;
DROP POLICY IF EXISTS "rotation_rules_delete_policy" ON public.rotation_rules;
DROP POLICY IF EXISTS "players_delete_policy" ON public.players;

CREATE POLICY "teams_delete_policy" ON public.teams
  FOR DELETE USING (
    auth.uid() = user_id OR public.is_admin()
  );

CREATE POLICY "lineups_delete_policy" ON public.lineups
  FOR DELETE USING (
    auth.uid() = user_id OR public.is_admin()
  );

CREATE POLICY "lineup_innings_delete_policy" ON public.lineup_innings
  FOR DELETE USING (
    auth.uid() = user_id OR public.is_admin()
  );

CREATE POLICY "lineup_attendance_delete_policy" ON public.lineup_attendance
  FOR DELETE USING (
    auth.uid() = user_id OR public.is_admin()
  );

CREATE POLICY "rotation_rules_delete_policy" ON public.rotation_rules
  FOR DELETE USING (
    auth.uid() = user_id OR public.is_admin()
  );

CREATE POLICY "players_delete_policy" ON public.players
  FOR DELETE USING (
    auth.uid() = user_id OR public.is_admin()
  );

-- Verify the function works
SELECT public.is_admin();
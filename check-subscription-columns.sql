-- Check what columns exist in the subscriptions table
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public' 
AND table_name = 'subscriptions'
ORDER BY ordinal_position;

-- Also check the user_subscription_status view structure
SELECT 
    column_name, 
    data_type
FROM information_schema.columns
WHERE table_schema = 'public' 
AND table_name = 'user_subscription_status'
ORDER BY ordinal_position;

-- Show sample data with calculated team count
SELECT 
    s.user_id,
    s.tier,
    s.is_paid,
    s.team_limit,
    COUNT(t.id) as team_count,
    s.created_at
FROM subscriptions s
LEFT JOIN teams t ON t.user_id = s.user_id
GROUP BY s.id, s.user_id, s.tier, s.is_paid, s.team_limit, s.created_at
ORDER BY s.created_at DESC
LIMIT 5;
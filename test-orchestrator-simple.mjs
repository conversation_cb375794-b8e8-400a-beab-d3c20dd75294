/**
 * Simple test of the multi-game orchestrator concept
 */

// Simulate the orchestrator logic without imports
function generateMultiGameSeries(players, numberOfGames, inningsPerGame, rules) {
  console.log(`🎮 GENERATING ${numberOfGames} GAME SERIES - ${inningsPerGame} innings per game`);
  
  const games = [];
  const cumulativeStats = new Map();
  
  // Initialize cumulative stats
  players.forEach(player => {
    cumulativeStats.set(player.id, {
      totalFieldInnings: 0,
      totalBenchInnings: 0,
      totalInningsPitched: 0,
      gamesPlayed: 0
    });
  });

  // Generate each game
  for (let gameNum = 1; gameNum <= numberOfGames; gameNum++) {
    console.log(`\n🏟️ GENERATING GAME ${gameNum} of ${numberOfGames}`);
    
    // Calculate average field innings to determine who needs more playing time
    const totalFieldInnings = Array.from(cumulativeStats.values())
      .reduce((sum, stats) => sum + stats.totalFieldInnings, 0);
    const avgFieldInnings = totalFieldInnings / players.length;
    
    console.log(`📊 Average field innings after ${gameNum - 1} games: ${avgFieldInnings.toFixed(1)}`);
    
    // Simple simulation: distribute innings based on who needs more playing time
    const gameStats = new Map();
    players.forEach(p => {
      gameStats.set(p.id, { fieldInnings: 0, benchInnings: 0, inningsPitched: 0 });
    });
    
    // For each inning, pick 9 players prioritizing those with less cumulative playing time
    const gameLineups = [];
    for (let inning = 1; inning <= inningsPerGame; inning++) {
      // Sort players by cumulative playing time (ascending)
      const playersByNeed = [...players].sort((a, b) => {
        const aStats = cumulativeStats.get(a.id);
        const bStats = cumulativeStats.get(b.id);
        return aStats.totalFieldInnings - bStats.totalFieldInnings;
      });
      
      // Pick first 9 for field, rest for bench
      const fieldPlayers = playersByNeed.slice(0, 9);
      const benchPlayers = playersByNeed.slice(9);
      
      // Update game stats
      fieldPlayers.forEach(p => {
        gameStats.get(p.id).fieldInnings++;
      });
      benchPlayers.forEach(p => {
        gameStats.get(p.id).benchInnings++;
      });
      
      gameLineups.push({ field: fieldPlayers.map(p => p.name), bench: benchPlayers.map(p => p.name) });
    }
    
    // Update cumulative stats
    gameStats.forEach((game, playerId) => {
      const cumulative = cumulativeStats.get(playerId);
      cumulative.totalFieldInnings += game.fieldInnings;
      cumulative.totalBenchInnings += game.benchInnings;
      cumulative.gamesPlayed++;
    });
    
    games.push({ lineups: gameLineups, gameStats });
    
    // Log progress
    console.log(`Game ${gameNum} complete. Current cumulative stats:`);
    const statsList = Array.from(cumulativeStats.entries())
      .map(([id, stats]) => ({
        name: players.find(p => p.id === id).name,
        field: stats.totalFieldInnings,
        bench: stats.totalBenchInnings
      }))
      .sort((a, b) => b.field - a.field);
    
    statsList.forEach(s => {
      console.log(`  ${s.name}: ${s.field} field, ${s.bench} bench`);
    });
  }
  
  // Calculate balance score
  const fieldInnings = Array.from(cumulativeStats.values()).map(s => s.totalFieldInnings);
  const min = Math.min(...fieldInnings);
  const max = Math.max(...fieldInnings);
  const avg = fieldInnings.reduce((a, b) => a + b, 0) / fieldInnings.length;
  
  // Calculate standard deviation
  const variance = fieldInnings.reduce((sum, innings) => {
    return sum + Math.pow(innings - avg, 2);
  }, 0) / fieldInnings.length;
  
  const stdDev = Math.sqrt(variance);
  const cv = stdDev / avg;
  const balanceScore = Math.max(0, 100 * (1 - cv));
  
  return { games, cumulativeStats, balanceScore };
}

// Test with 14 players
const testPlayers = [
  { id: '1', name: 'Alice' },
  { id: '2', name: 'Bob' },
  { id: '3', name: 'Charlie' },
  { id: '4', name: 'David' },
  { id: '5', name: 'Emma' },
  { id: '6', name: 'Frank' },
  { id: '7', name: 'Grace' },
  { id: '8', name: 'Henry' },
  { id: '9', name: 'Iris' },
  { id: '10', name: 'Jack' },
  { id: '11', name: 'Kate' },
  { id: '12', name: 'Liam' },
  { id: '13', name: 'Mia' },
  { id: '14', name: 'Noah' }
];

console.log('🧪 Testing Multi-Game Orchestrator Concept');
console.log('=' .repeat(50));

// Test 3-game series
const result = generateMultiGameSeries(testPlayers, 3, 7, {});

console.log('\n📊 FINAL RESULTS');
console.log('=' .repeat(50));
console.log(`Balance Score: ${result.balanceScore.toFixed(1)}%`);

const finalStats = Array.from(result.cumulativeStats.entries())
  .map(([id, stats]) => ({
    name: testPlayers.find(p => p.id === id).name,
    field: stats.totalFieldInnings,
    bench: stats.totalBenchInnings,
    percentage: (stats.totalFieldInnings / 21 * 100).toFixed(1)
  }))
  .sort((a, b) => b.field - a.field);

console.log('\nFinal cumulative playing time:');
finalStats.forEach(s => {
  console.log(`${s.name}: ${s.field} field innings (${s.percentage}%)`);
});

const fieldValues = finalStats.map(s => s.field);
const range = Math.max(...fieldValues) - Math.min(...fieldValues);

console.log(`\nRange: ${range} innings`);
console.log(`Assessment: ${range <= 3 ? '✅ EXCELLENT' : range <= 5 ? '⚠️ GOOD' : '❌ NEEDS IMPROVEMENT'}`);
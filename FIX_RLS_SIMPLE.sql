-- Step 1: Check the data types first
SELECT 
    table_name,
    column_name,
    data_type 
FROM information_schema.columns 
WHERE table_name IN ('profiles', 'subscriptions') 
    AND column_name IN ('id', 'user_id')
ORDER BY table_name, column_name;

-- Step 2: Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- Step 3: Drop all existing policies
DO $$ 
BEGIN
    -- Drop all policies on profiles
    EXECUTE (
        SELECT string_agg('DROP POLICY IF EXISTS "' || policyname || '" ON profiles;', ' ')
        FROM pg_policies
        WHERE tablename = 'profiles'
    );
    
    -- Drop all policies on subscriptions
    EXECUTE (
        SELECT string_agg('DROP POLICY IF EXISTS "' || policyname || '" ON subscriptions;', ' ')
        FROM pg_policies
        WHERE tablename = 'subscriptions'
    );
END $$;

-- Step 4: Create simple policies for profiles (assuming id is TEXT based on error)
CREATE POLICY "allow_users_own_profile_select" ON profiles
    FOR SELECT TO authenticated
    USING (auth.uid()::text = id);

CREATE POLICY "allow_users_own_profile_insert" ON profiles
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid()::text = id);

CREATE POLICY "allow_users_own_profile_update" ON profiles
    FOR UPDATE TO authenticated
    USING (auth.uid()::text = id)
    WITH CHECK (auth.uid()::text = id);

-- Step 5: Create simple policies for subscriptions (user_id is UUID)
CREATE POLICY "allow_users_own_subscription_select" ON subscriptions
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "allow_users_own_subscription_insert" ON subscriptions
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "allow_users_own_subscription_update" ON subscriptions
    FOR UPDATE TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Step 6: Verify the policies
SELECT 
    tablename,
    policyname,
    cmd,
    roles
FROM pg_policies 
WHERE tablename IN ('profiles', 'subscriptions')
ORDER BY tablename, policyname;
import { generateCompleteLineup } from './dist/lib/utils-enhanced.js';

// Test hard enforcement
const players = [
  { id: '1', name: 'Player1', teamRoles: {} },
  { id: '2', name: 'Player2', teamRoles: {} },
  { id: '3', name: 'Player3', teamRoles: {} },
  { id: '4', name: 'Player4', teamRoles: {} },
  { id: '5', name: 'Player5', teamRoles: {} },
  { id: '6', name: 'Player6', teamRoles: {} },
  { id: '7', name: 'Player7', teamRoles: {} },
  { id: '8', name: 'Player8', teamRoles: {} },
  { id: '9', name: 'Player9', teamRoles: {} },
  { id: '10', name: 'Player10', teamRoles: {} },
  { id: '11', name: 'Player11', teamRoles: {} },
  { id: '12', name: 'Player12', teamRoles: {} }
];

console.log('Testing with rotateLineupEvery=1 for 20 innings...\n');

const innings = generateCompleteLineup(players, 20, {
  rotateLineupEvery: 1,
  rotatePitcherEvery: 2,
  equalPlayingTime: true,
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 2
});

// Count playing time
const playingTime = {};
players.forEach(p => playingTime[p.name] = 0);

innings.forEach(inning => {
  Object.entries(inning.positions).forEach(([pos, name]) => {
    if (pos !== 'bench' && name) {
      playingTime[name]++;
    }
  });
});

console.log('\nFINAL PLAYING TIME:');
const sorted = Object.entries(playingTime).sort(([,a], [,b]) => b - a);
sorted.forEach(([name, time]) => console.log(`${name}: ${time} innings`));

const max = Math.max(...Object.values(playingTime));
const min = Math.min(...Object.values(playingTime));
console.log(`\nRange: ${max - min} (Max: ${max}, Min: ${min})`);
console.log(`Ideal: ${(20 * 9 / 12).toFixed(1)} innings per player`);

if (max > 17) {
  console.log('\n❌ FAILED: Some players still playing too many innings!');
} else if (max - min <= 5) {
  console.log('\n✅ SUCCESS: Playing time is well balanced!');
} else {
  console.log('\n⚠️  PARTIAL: Better but still needs improvement');
}
#!/usr/bin/env node

import { readFileSync, existsSync } from 'fs';
import { execSync } from 'child_process';

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
};

function log(message, type = 'info') {
  const prefix = {
    info: `${colors.blue}ℹ${colors.reset}`,
    success: `${colors.green}✓${colors.reset}`,
    warning: `${colors.yellow}⚠${colors.reset}`,
    error: `${colors.red}✗${colors.reset}`,
  }[type] || '';
  
  console.log(`${prefix} ${message}`);
}

// Required environment variables for production
const REQUIRED_ENV_VARS = {
  frontend: {
    'VITE_STRIPE_PUBLISHABLE_KEY': {
      pattern: /^pk_live_/,
      description: 'Stripe live publishable key',
      example: 'pk_live_51ABC...',
    },
    'VITE_SUPABASE_URL': {
      pattern: /^https:\/\/.+\.supabase\.co$/,
      description: 'Supabase project URL',
      example: 'https://xyzabc.supabase.co',
    },
    'VITE_SUPABASE_ANON_KEY': {
      pattern: /^[A-Za-z0-9+/=.]+$/,
      description: 'Supabase anonymous key',
      example: 'eyJhbGc...',
    },
  },
  supabase: {
    'STRIPE_SECRET_KEY': {
      pattern: /^sk_live_/,
      description: 'Stripe live secret key',
      example: 'sk_live_51ABC...',
    },
    'STRIPE_WEBHOOK_SECRET': {
      pattern: /^whsec_/,
      description: 'Stripe webhook signing secret',
      example: 'whsec_abc123...',
    },
  },
};

function checkLocalEnv() {
  console.log(`\n${colors.bright}Checking Local .env.local${colors.reset}`);
  console.log('─'.repeat(40));
  
  if (!existsSync('.env.local')) {
    log('No .env.local file found', 'error');
    return false;
  }
  
  const envContent = readFileSync('.env.local', 'utf-8');
  const envVars = {};
  
  // Parse env file
  envContent.split('\n').forEach(line => {
    if (line && !line.startsWith('#')) {
      const [key, ...valueParts] = line.split('=');
      if (key) {
        envVars[key.trim()] = valueParts.join('=').trim().replace(/["']/g, '');
      }
    }
  });
  
  let allValid = true;
  
  for (const [varName, config] of Object.entries(REQUIRED_ENV_VARS.frontend)) {
    const value = envVars[varName];
    
    if (!value) {
      log(`${varName}: Missing`, 'error');
      console.log(`  Required: ${config.description}`);
      console.log(`  Example: ${config.example}`);
      allValid = false;
    } else if (!config.pattern.test(value)) {
      log(`${varName}: Invalid format`, 'error');
      console.log(`  Current: ${value.substring(0, 20)}...`);
      console.log(`  Expected pattern: ${config.pattern}`);
      console.log(`  Example: ${config.example}`);
      allValid = false;
    } else {
      log(`${varName}: Valid ✓`, 'success');
    }
  }
  
  return allValid;
}

function checkCodeForSecrets() {
  console.log(`\n${colors.bright}Scanning Code for Hardcoded Secrets${colors.reset}`);
  console.log('─'.repeat(40));
  
  const secretPatterns = [
    { pattern: 'pk_test_', description: 'Stripe test publishable key' },
    { pattern: 'sk_test_', description: 'Stripe test secret key' },
    { pattern: 'sk_live_', description: 'Stripe live secret key' },
    { pattern: 'whsec_', description: 'Stripe webhook secret' },
  ];
  
  let foundSecrets = false;
  
  for (const { pattern, description } of secretPatterns) {
    try {
      const result = execSync(
        `grep -r "${pattern}" src/ --include="*.ts" --include="*.tsx" --include="*.js" --include="*.jsx" 2>/dev/null || true`,
        { encoding: 'utf-8' }
      );
      
      if (result.trim()) {
        log(`Found ${description} in code!`, 'error');
        console.log(result.trim().split('\n').map(line => `  ${line}`).join('\n'));
        foundSecrets = true;
      }
    } catch (error) {
      // Grep returns non-zero when no matches found
    }
  }
  
  if (!foundSecrets) {
    log('No hardcoded secrets found in source code', 'success');
  }
  
  return !foundSecrets;
}

function generateDeploymentGuide() {
  console.log(`\n${colors.bright}Deployment Platform Configuration${colors.reset}`);
  console.log('═'.repeat(60));
  
  console.log(`\n${colors.bright}For Netlify:${colors.reset}`);
  console.log('1. Go to app.netlify.com → Your Site → Site settings');
  console.log('2. Navigate to Environment variables');
  console.log('3. Add these variables:');
  Object.entries(REQUIRED_ENV_VARS.frontend).forEach(([key, config]) => {
    console.log(`   • ${key} = ${config.example}`);
  });
  
  console.log(`\n${colors.bright}For Vercel:${colors.reset}`);
  console.log('1. Go to vercel.com → Your Project → Settings');
  console.log('2. Navigate to Environment Variables');
  console.log('3. Add the same variables as above');
  
  console.log(`\n${colors.bright}For Supabase Edge Functions:${colors.reset}`);
  console.log('1. Go to supabase.com → Your Project → Edge Functions');
  console.log('2. Navigate to Secrets');
  console.log('3. Add these secrets:');
  Object.entries(REQUIRED_ENV_VARS.supabase).forEach(([key, config]) => {
    console.log(`   • ${key} = ${config.example}`);
  });
}

function generateQuickChecklist() {
  console.log(`\n${colors.bright}Quick Production Checklist${colors.reset}`);
  console.log('═'.repeat(60));
  
  const checklist = [
    'Switch Stripe to live mode in dashboard',
    'Update all environment variables (local, deployment platform, Supabase)',
    'Update CORS configuration in supabase/functions/_shared/cors.ts',
    'Deploy edge functions with ./deploy-edge-functions.sh',
    'Test with a real $1 payment',
    'Monitor Stripe webhook logs',
    'Check Supabase logs for any errors',
  ];
  
  checklist.forEach((item, index) => {
    console.log(`${index + 1}. [ ] ${item}`);
  });
}

// Main execution
console.clear();
console.log(colors.bright + colors.blue);
console.log('╔═══════════════════════════════════════════════════════════╗');
console.log('║        Production Environment Validator                   ║');
console.log('╚═══════════════════════════════════════════════════════════╝');
console.log(colors.reset);

const localEnvValid = checkLocalEnv();
const noHardcodedSecrets = checkCodeForSecrets();

if (localEnvValid && noHardcodedSecrets) {
  log('\n✅ Local environment is production-ready!', 'success');
} else {
  log('\n❌ Issues found - please fix before deploying', 'error');
}

generateDeploymentGuide();
generateQuickChecklist();

console.log(`\n${colors.yellow}Remember: Never commit .env.local or any file containing secrets!${colors.reset}\n`);
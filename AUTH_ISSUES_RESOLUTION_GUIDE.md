# Authentication Issues Resolution Guide

## Problem Summary

Two critical issues affecting user authentication:

1. **Incomplete Subscriptions**: Admin-created paid users redirected to pricing due to missing `tier` and `team_limit` fields
2. **Orphaned Auth Records**: Deleted users cannot be recreated because auth.users records persist after profile deletion

## Issue 1: Incomplete Subscriptions

### Root Cause
When admins create paid users, subscription records are created with `is_paid: true` but missing required fields:
- `tier` (defaults to 'starter' causing 1 team limit)
- `team_limit` (defaults to 1, triggering pricing redirect)

### Fix Applied
Updated `AdminUsers.tsx` and `AuthContext.tsx` to provide defaults:
```typescript
// AdminUsers.tsx
tier: newUserData.tier || 'club',  // Valid tiers: starter, coach, club
team_limit: teamLimit || 999

// AuthContext.tsx
setSubscriptionTier(latestSubscription.tier || (latestSubscription.is_paid ? 'club' : 'starter'));
setTeamLimit(latestSubscription.team_limit || (latestSubscription.is_paid ? 999 : 1));
```

### Fix Existing Users
```bash
# Check for incomplete subscriptions
node check-subscription-data.js

# Fix all incomplete paid subscriptions
node fix-auth-client-issues.js --batch

# Fix specific user
node fix-auth-client-issues.js <EMAIL>
```

## Issue 2: Orphaned Auth Records

### Root Cause
Admin deletion only removes:
- Profile record (profiles table)
- Subscription record (subscriptions table)

But NOT:
- Auth record (auth.users table) ❌

This prevents recreating users with the same email.

### Detection
```bash
# Check if email exists in auth but not profiles
node check-user-exists.js <EMAIL>
```

Output indicates orphaned auth record:
```
⚠️  User exists in auth but not in profiles!
This is likely why you cannot recreate the user.
```

### Solutions

#### Option 1: Interactive Deletion
```bash
node check-user-exists.js <EMAIL>
# When prompted, type 'yes' to delete auth record
```

#### Option 2: Direct Deletion
```bash
node delete-orphaned-user.js <EMAIL>
```

#### Option 3: Batch Cleanup
```bash
node cleanup-orphaned-users.js
```

## Complete Resolution Process

### For Users Who Can't Login (Incomplete Subscriptions)

1. **Identify affected users**:
   ```sql
   SELECT user_id, email, is_paid, tier, team_limit 
   FROM subscriptions s
   JOIN profiles p ON p.id = s.user_id
   WHERE is_paid = true AND (tier IS NULL OR team_limit IS NULL);
   ```

2. **Fix their subscriptions**:
   ```bash
   node fix-auth-client-issues.js --batch
   ```

3. **Have users sign out and back in**

### For Users Who Can't Be Recreated (Orphaned Auth)

1. **Check for orphaned auth record**:
   ```bash
   node check-user-exists.js <EMAIL>
   ```

2. **Delete orphaned auth record**:
   ```bash
   node delete-orphaned-user.js <EMAIL>
   ```

3. **Recreate user via admin panel**

## Prevention

### Code Fixes (Already Applied)
- ✅ Always provide `tier` and `team_limit` when creating subscriptions
- ✅ Handle null values gracefully in AuthContext

### Process Improvements
1. **Complete User Deletion**: Update admin deletion to also remove auth records
2. **Validation**: Add checks to ensure required fields are always populated
3. **Monitoring**: Regular checks for incomplete subscriptions and orphaned records

## Quick Reference Scripts

```bash
# Fix incomplete subscriptions
node fix-auth-client-issues.js --batch

# Check specific user
node check-user-exists.js <EMAIL>

# Delete orphaned auth record
node delete-orphaned-user.js <EMAIL>

# Cleanup all orphaned users
node cleanup-orphaned-users.js

# Verify subscription data
node check-subscription-data.js
```

## SQL Queries for Verification

```sql
-- Find incomplete paid subscriptions
SELECT user_id, email, is_paid, tier, team_limit 
FROM subscriptions s
JOIN profiles p ON p.id = s.user_id
WHERE is_paid = true AND (tier IS NULL OR team_limit IS NULL);

-- Find orphaned auth users (requires service role)
SELECT au.id, au.email 
FROM auth.users au
LEFT JOIN public.profiles p ON p.id = au.id
WHERE p.id IS NULL;

-- Check user's subscription details
SELECT * FROM subscriptions 
WHERE user_id = (SELECT id FROM profiles WHERE email = '<EMAIL>');
```

## Notes
- Users must sign out and back in after fixes
- The auth.users table requires service role access
- Always backup before bulk operations
- Monitor error logs after applying fixes
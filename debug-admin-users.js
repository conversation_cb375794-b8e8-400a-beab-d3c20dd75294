import { supabase } from "./src/integrations/supabase/node-client.js";

// Load environment variables

async function debugAdminUsers() {
  console.log('🔍 Debugging Admin Users Query\n');
  
  // Test the exact query from AdminUsers.tsx
  console.log('1. Testing profiles query with all columns...');
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select(`
        id,
        full_name,
        email,
        role,
        created_at,
        is_admin
      `);
    
    if (error) {
      console.error('❌ Profiles query failed:', error);
      console.error('   Error code:', error.code);
      console.error('   Error message:', error.message);
      console.error('   Error details:', error.details);
      console.error('   Error hint:', error.hint);
      
      // Try simpler queries to isolate the issue
      console.log('\n2. Testing minimal profiles query...');
      const { data: simple, error: simpleError } = await supabase
        .from('profiles')
        .select('id');
      
      if (simpleError) {
        console.error('❌ Even simple query failed:', simpleError.message);
      } else {
        console.log('✅ Simple query works, testing each column...');
        
        // Test each column individually
        const columns = ['id', 'full_name', 'email', 'role', 'created_at', 'is_admin'];
        for (const col of columns) {
          const { error: colError } = await supabase
            .from('profiles')
            .select(col)
            .limit(1);
          
          if (colError) {
            console.error(`❌ Column '${col}' causes error:`, colError.message);
          } else {
            console.log(`✅ Column '${col}' works`);
          }
        }
      }
    } else {
      console.log('✅ Profiles query succeeded');
      console.log(`   Found ${data?.length || 0} profiles`);
      if (data && data.length > 0) {
        console.log('   Sample profile:', JSON.stringify(data[0], null, 2));
      }
    }
  } catch (err) {
    console.error('❌ Unexpected error:', err);
  }
  
  console.log('\n' + '-'.repeat(60) + '\n');
  
  // Check authentication
  console.log('3. Checking authentication context...');
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.error('❌ Auth error:', error.message);
    } else if (user) {
      console.log('✅ Authenticated as:', user.email);
      
      // Check if this user can see other profiles
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select('id, email, is_admin');
      
      if (profilesError) {
        console.error('❌ Cannot query profiles:', profilesError.message);
        console.log('\n💡 This might be an RLS policy issue.');
        console.log('   Admin policies may not be working correctly.');
      } else {
        console.log(`✅ Can see ${profiles?.length || 0} profiles`);
      }
    } else {
      console.log('❌ Not authenticated');
    }
  } catch (err) {
    console.error('❌ Auth check failed:', err);
  }
  
  console.log('\n' + '-'.repeat(60) + '\n');
  
  // Check RLS policies
  console.log('4. Testing RLS policies...');
  console.log('\nTo fix admin access, run this SQL:\n');
  console.log(`
-- First, check if you're marked as admin
SELECT id, email, is_admin 
FROM profiles 
WHERE email = 'YOUR_EMAIL_HERE';

-- If not admin, update yourself
UPDATE profiles 
SET is_admin = TRUE 
WHERE email = 'YOUR_EMAIL_HERE';

-- Check if admin policies exist
SELECT schemaname, tablename, policyname, cmd, qual 
FROM pg_policies 
WHERE tablename = 'profiles' 
AND policyname LIKE '%admin%';

-- If no admin policies, create them
CREATE POLICY "Admins can view all profiles" ON public.profiles
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid() AND is_admin = TRUE
  )
);

-- Also check subscriptions policies
CREATE POLICY "Admins can view all subscriptions" ON public.subscriptions
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid() AND is_admin = TRUE
  )
);

-- Check teams policies
CREATE POLICY "Admins can view all teams" ON public.teams
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid() AND is_admin = TRUE
  )
);
  `);
}

debugAdminUsers();
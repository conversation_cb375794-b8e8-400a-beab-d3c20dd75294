-- DIRECT SQL FIX - Run this AS the postgres/service role user

-- 1. Get the user ID
WITH user_info AS (
    SELECT id, email 
    FROM auth.users 
    WHERE email = '<EMAIL>'
    LIMIT 1
)
-- 2. Insert/update profile
INSERT INTO public.profiles (id, email, created_at, updated_at)
SELECT id, email, NOW(), NOW()
FROM user_info
ON CONFLICT (id) 
DO UPDATE SET 
    email = EXCLUDED.email,
    updated_at = NOW();

-- 3. Insert/update subscription
WITH user_info AS (
    SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1
)
INSERT INTO public.subscriptions (
    user_id, is_paid, tier, team_limit,
    currency, amount, payment_date,
    created_at, updated_at
)
SELECT 
    id, true, 'club', 999,
    'usd', 0, NOW(),
    NOW(), NOW()
FROM user_info
ON CONFLICT (user_id)
DO UPDATE SET
    is_paid = true,
    tier = 'club', 
    team_limit = 999,
    updated_at = NOW();

-- 4. Verify it worked
SELECT 
    'VERIFICATION' as check_type,
    u.email,
    p.id IS NOT NULL as has_profile,
    s.id IS NOT NULL as has_subscription,
    s.is_paid,
    s.tier,
    s.team_limit
FROM auth.users u
LEFT JOIN public.profiles p ON p.id = u.id  
LEFT JOIN public.subscriptions s ON s.user_id = u.id
WHERE u.email = '<EMAIL>';
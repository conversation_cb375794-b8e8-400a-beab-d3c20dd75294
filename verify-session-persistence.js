const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function testSessionPersistence() {
  console.log('\n🔐 Testing Supabase Session Persistence...\n');

  try {
    // Check current session
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('❌ Error getting session:', error);
      return;
    }

    if (session) {
      console.log('✅ Session found!');
      console.log('   User:', session.user.email);
      console.log('   Token (first 20 chars):', session.access_token.substring(0, 20) + '...');
      console.log('   Expires at:', new Date(session.expires_at * 1000).toLocaleString());
      console.log('   Time until expiry:', Math.round((session.expires_at * 1000 - Date.now()) / 1000 / 60), 'minutes');
    } else {
      console.log('❌ No session found');
    }

    // Test session refresh
    console.log('\n🔄 Testing session refresh...');
    const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
    
    if (refreshError) {
      console.error('❌ Error refreshing session:', refreshError);
    } else if (refreshData.session) {
      console.log('✅ Session refreshed successfully!');
      console.log('   New expiry:', new Date(refreshData.session.expires_at * 1000).toLocaleString());
    }

    // Check auth configuration
    console.log('\n⚙️  Auth Configuration:');
    console.log('   URL:', process.env.VITE_SUPABASE_URL);
    console.log('   Has anon key:', !!process.env.VITE_SUPABASE_ANON_KEY);

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testSessionPersistence();
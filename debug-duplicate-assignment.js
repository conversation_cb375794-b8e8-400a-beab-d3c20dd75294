// Debug script to isolate the duplicate assignment bug
import { generateCompleteLineup } from './src/lib/utils-enhanced.js';

// Simple test roster
const testPlayers = [
  {
    id: '1',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: false,    // Can pitch
      catcher: true,     // Cannot catch
      firstBase: true,   // Cannot play 1B
      other: null
    },
    positionRatings: { 
      pitcher: 5,        // Elite pitcher
      shortstop: 3,      // Can play SS as backup
      leftField: 2       // Emergency outfield
    },
    isStarPlayer: true
  },
  {
    id: '2',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: true,     // Cannot pitch
      catcher: false,    // Can catch
      firstBase: false,  // Can play 1B
      other: null
    },
    positionRatings: { 
      catcher: 5,        // Elite catcher
      thirdBase: 4,      // Strong 3B
      rightField: 3      // Decent outfield
    },
    isStarPlayer: true
  },
  {
    id: '3',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      shortstop: 5,      // Elite SS
      secondBase: 4,     // Strong 2B
      centerField: 4     // Strong <PERSON>
    },
    isStarPlayer: true
  },
  {
    id: '4',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      firstBase: 4,      // Strong 1B
      thirdBase: 3,      // Decent 3B
      leftField: 3       // Decent LF
    },
    isStarPlayer: false
  },
  {
    id: '5',
    name: 'Emma Wilson',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      secondBase: 4,     // Strong 2B
      shortstop: 3,      // Decent SS
      rightField: 3      // Decent RF
    },
    isStarPlayer: false
  },
  {
    id: '6',
    name: 'Frank Miller',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      thirdBase: 4,      // Strong 3B
      firstBase: 3,      // Decent 1B
      centerField: 2     // Emergency CF
    },
    isStarPlayer: false
  },
  {
    id: '7',
    name: 'Grace Lee',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      leftField: 4,      // Strong LF
      centerField: 4,    // Strong CF
      rightField: 3      // Decent RF
    },
    isStarPlayer: false
  },
  {
    id: '8',
    name: 'Henry Davis',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      centerField: 5,    // Elite CF
      leftField: 4,      // Strong LF
      rightField: 4      // Strong RF
    },
    isStarPlayer: true
  },
  {
    id: '9',
    name: 'Ivy Johnson',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      rightField: 4,     // Strong RF
      centerField: 3,    // Decent CF
      firstBase: 2       // Emergency 1B
    },
    isStarPlayer: false
  }
];

const competitiveRules = {
  respectPositionLockouts: true,
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 3,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  equalPlayingTime: false,
  rotateLineupEvery: 3,
  rotatePitcherEvery: 4,
  competitiveMode: true,
  competitiveMinPlayingTime: 40,
  keyPositions: ['pitcher', 'catcher', 'shortstop', 'centerField'],
  starPlayerRotationDelay: 2,
  _randomSeed: 12345
};

console.log('🏗️ Attempting to generate lineup...');
console.log(`📊 Players: ${testPlayers.length}`);
console.log(`⚙️ Competitive Mode: ${competitiveRules.competitiveMode}`);

try {
  const lineup = generateCompleteLineup(testPlayers, 3, competitiveRules);
  console.log('✅ Lineup generated successfully!');
  
  lineup.forEach((inning, index) => {
    console.log(`\n📋 Inning ${index + 1}:`);
    Object.entries(inning.positions).forEach(([pos, player]) => {
      if (pos !== 'bench') {
        console.log(`  ${pos}: ${player}`);
      }
    });
    if (inning.positions.bench && inning.positions.bench.length > 0) {
      console.log(`  bench: ${inning.positions.bench.join(', ')}`);
    }
  });
  
} catch (error) {
  console.error('❌ Error generating lineup:', error.message);
  console.error('🔍 Error details:', error);
}
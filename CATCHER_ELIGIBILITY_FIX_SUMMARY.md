# Catcher Eligibility Fix Summary

## Problem
During rotation in competitive mode, the system was unable to find any eligible players for the catcher position, causing the error:
```
[applyRotation] No eligible players for catcher!
[applyRotation] Validation errors: Vienna Szekeres cannot play catcher in competitive mode
```

## Root Cause
The issue was a mismatch between two position assignment systems:

1. **Initial Lineup Generation**: Uses `teamRoles` (the new system) via `convertToPositionPools()`
2. **Rotation Validation**: Uses `positionPreferences` (the old system) via `canPlayPosition()`

In competitive mode, `canPlayPosition()` was ONLY checking `positionPreferences` and returning `false` for players who only had `teamRoles` assigned. This meant that during rotation, players who were assigned catcher via teamRoles were considered ineligible.

## Solution
Updated the `canPlayPosition()` function in `src/lib/utils-enhanced.ts` to check BOTH systems:

### Changes Made:
1. Added teamRole checking alongside positionPreferences
2. Added support for outfield specialists via `isOutfieldSpecialist` flag
3. Ensured "avoid" is respected in both systems

### Key Logic:
```typescript
// In competitive mode, a player can play a position if they have EITHER:
// 1. A position preference (not undefined and not avoid)
// 2. A team role assignment for this position
// 3. Is an outfield specialist for outfield positions

// "Avoid" is always respected in both systems
```

## Impact
- Players using the new teamRoles system will now be properly recognized as eligible during rotation
- The system maintains backward compatibility with positionPreferences
- Both UI systems (Team Roles Manager and Position Preferences) work correctly
- Competitive mode rotation will no longer fail when players only have teamRoles assigned

## Testing
Run `node test-catcher-eligibility-fix.js` to verify the fix handles all scenarios:
- Players with only teamRoles
- Players with only positionPreferences  
- Players with both systems
- "Avoid" markings in either system
- Fair play vs competitive mode differences
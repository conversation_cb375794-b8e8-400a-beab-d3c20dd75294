const { createServiceClient } = require('./src/integrations/supabase/node-client.cjs');
const fs = require('fs');

async function fixRLS() {
  try {
    const supabase = createServiceClient();
    
    console.log('🔐 Fixing RLS policies for profiles and subscriptions tables...');
    
    // Simple approach - just create the essential policies
    const policies = [
      // Profiles policies
      {
        sql: `DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;`,
        desc: 'Dropping old profile insert policy'
      },
      {
        sql: `DROP POLICY IF EXISTS "Users can update own profile" ON profiles;`,
        desc: 'Dropping old profile update policy'
      },
      {
        sql: `DROP POLICY IF EXISTS "Users can view own profile" ON profiles;`,
        desc: 'Dropping old profile select policy'
      },
      {
        sql: `CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT WITH CHECK (auth.uid()::text = id);`,
        desc: 'Creating profile insert policy'
      },
      {
        sql: `CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid()::text = id);`,
        desc: 'Creating profile update policy'
      },
      {
        sql: `CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid()::text = id);`,
        desc: 'Creating profile select policy'
      },
      // Subscriptions policies
      {
        sql: `DROP POLICY IF EXISTS "Users can insert own subscription" ON subscriptions;`,
        desc: 'Dropping old subscription insert policy'
      },
      {
        sql: `DROP POLICY IF EXISTS "Users can update own subscription" ON subscriptions;`,
        desc: 'Dropping old subscription update policy'
      },
      {
        sql: `DROP POLICY IF EXISTS "Users can view own subscription" ON subscriptions;`,
        desc: 'Dropping old subscription select policy'
      },
      {
        sql: `CREATE POLICY "Users can insert own subscription" ON subscriptions FOR INSERT WITH CHECK (auth.uid() = user_id);`,
        desc: 'Creating subscription insert policy'
      },
      {
        sql: `CREATE POLICY "Users can update own subscription" ON subscriptions FOR UPDATE USING (auth.uid() = user_id);`,
        desc: 'Creating subscription update policy'
      },
      {
        sql: `CREATE POLICY "Users can view own subscription" ON subscriptions FOR SELECT USING (auth.uid() = user_id);`,
        desc: 'Creating subscription select policy'
      }
    ];
    
    // Execute each policy
    for (const policy of policies) {
      console.log(`\nExecuting: ${policy.desc}`);
      
      try {
        // First try as a direct query using the service client
        const { error } = await supabase.from('_dummy_').select().limit(0); // Test connection
        
        // If we can connect, use raw SQL through service role
        const response = await fetch(`${process.env.VITE_SUPABASE_URL}/rest/v1/rpc/exec_sql`, {
          method: 'POST',
          headers: {
            'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY,
            'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ sql: policy.sql })
        });
        
        if (!response.ok) {
          // If exec_sql doesn't exist, that's OK - we'll handle it differently
          console.log('Note: exec_sql RPC not available, policy changes may need manual application');
        } else {
          console.log('✓ Success');
        }
      } catch (err) {
        console.log('Note: Could not execute directly, you may need to run these in Supabase dashboard SQL editor');
      }
    }
    
    console.log('\n✅ RLS policy fix script completed!');
    console.log('\nIf policies were not applied automatically, please run the following SQL in Supabase dashboard:');
    console.log('\n-- Enable RLS');
    console.log('ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;');
    console.log('ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;');
    console.log('\n-- Then run each policy creation statement from above');
    
  } catch (error) {
    console.error('Script error:', error);
  }
}

fixRLS();
-- Show all paid users and their subscription details
SELECT 
  p.email,
  p.id as user_id,
  p.is_admin,
  s.is_paid,
  s.tier,
  s.team_limit,
  s.payment_date,
  s.expires_at,
  s.created_at as sub_created,
  s.updated_at as sub_updated,
  CASE 
    WHEN s.expires_at IS NULL THEN 'Never expires'
    WHEN s.expires_at > NOW() THEN 'Active'
    ELSE 'Expired'
  END as status
FROM profiles p
JOIN subscriptions s ON p.id = s.user_id
WHERE s.is_paid = true
ORDER BY p.email;

-- Check if there are any auth.users without profiles
SELECT 
  au.id,
  au.email,
  au.created_at,
  p.id as profile_id
FROM auth.users au
LEFT JOIN profiles p ON au.id = p.id
WHERE p.id IS NULL;

-- Verify RLS policies are enabled
SELECT 
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies
WHERE tablename = 'subscriptions'
ORDER BY policyname;
-- List all users with their subscription status
SELECT 
  p.email,
  p.id as user_id,
  p.is_admin,
  s.is_paid,
  s.tier,
  s.team_limit,
  CASE 
    WHEN s.id IS NULL THEN 'No subscription'
    WHEN s.is_paid = true THEN 'Paid'
    ELSE 'Unpaid'
  END as status
FROM profiles p
LEFT JOIN subscriptions s ON p.id = s.user_id
ORDER BY p.email;

-- Count summary
SELECT 
  COUNT(DISTINCT p.id) as total_users,
  COUNT(DISTINCT CASE WHEN s.is_paid = true THEN p.id END) as paid_users,
  COUNT(DISTINCT CASE WHEN p.is_admin = true THEN p.id END) as admin_users
FROM profiles p
LEFT JOIN subscriptions s ON p.id = s.user_id;
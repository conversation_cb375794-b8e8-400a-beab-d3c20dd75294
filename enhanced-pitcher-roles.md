# Enhanced Pitcher Role System

## Current Issue
The current teamRoles system only has: `primary`, `in_the_mix`, `emergency`, `avoid`
This doesn't reflect real baseball pitcher usage patterns.

## Proposed Enhanced Pitcher Roles

### Pitcher-Specific Roles
```typescript
interface PitcherRoles {
  starting_pitcher: boolean;    // Can start games (innings 1-3)
  relief_pitcher: boolean;      // Middle relief (innings 2-5) 
  closer: boolean;              // Finish games (innings 6-7)
  spot_starter: boolean;        // Occasional starter
  long_relief: boolean;         // Can pitch multiple innings in relief
}
```

### Enhanced TeamRoles Structure
```typescript
interface EnhancedTeamRoles {
  [position: string]: string | PitcherRoles;
  
  // For non-pitchers, keep current system
  catcher: 'primary' | 'in_the_mix' | 'emergency' | 'avoid';
  firstBase: 'primary' | 'in_the_mix' | 'emergency' | 'avoid';
  // ... other positions
  
  // For pitcher, use enhanced roles
  pitcher: PitcherRoles;
}
```

## Pitching Strategy Logic

### Starting Lineup (Inning 1)
1. Prioritize players with `starting_pitcher: true`
2. Among starters, rotate based on usage in recent games
3. Fall back to `spot_starter: true` if no regular starters available

### Mid-Game Changes (Innings 2-5)
1. If starter needs rest (based on `rotatePitcherEvery`):
   - Use `relief_pitcher: true` players
   - Consider `long_relief: true` for multiple innings
2. Avoid using closers unless desperate

### Late Innings (Final 1-2 innings)
1. Prioritize `closer: true` players
2. Fall back to `relief_pitcher: true` if closer unavailable
3. Track closer usage to avoid overuse

## Implementation Plan

### Phase 1: Enhanced Data Structure
- Update Player interface to support enhanced pitcher roles
- Create migration for existing data
- Update TeamRoleManager UI

### Phase 2: Smart Pitcher Selection
- Implement inning-aware pitcher selection
- Add pitcher usage tracking across games
- Create pitcher fatigue/availability system

### Phase 3: Advanced Features
- Pitcher matchup optimization (lefty/righty)
- Rest requirements between appearances
- Pitch count simulation and limits

## Example Usage

```typescript
// Player with enhanced pitcher roles
const player = {
  name: "Jake",
  teamRoles: {
    pitcher: {
      starting_pitcher: true,
      relief_pitcher: false,
      closer: false,
      spot_starter: true,
      long_relief: false
    },
    catcher: 'avoid',
    firstBase: 'emergency'
  }
}
```

## Benefits
1. **Realistic rotation**: Starting pitchers start games, closers finish
2. **Better usage patterns**: Prevents closers from pitching early innings
3. **Strategic depth**: Coaches can set specific pitcher roles
4. **Fatigue management**: Track usage to prevent overuse
5. **Series planning**: Manage pitcher availability across multiple games
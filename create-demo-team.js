// <PERSON>ript to create a demo team with a specific ID
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
import { v4 as uuidv4 } from 'uuid';

// Initialize dotenv
dotenv.config();

async function createDemoTeam() {
  console.log('=== CREATING DEMO TEAM ===');

  // Get Supabase credentials
  const supabaseUrl = process.env.VITE_SUPABASE_URL;
  const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('❌ Missing Supabase environment variables. Check your .env file.');
    return;
  }

  // Create Supabase client
  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  try {
    // Sign in with demo account
    console.log('Signing in with demo account...');
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'demo1234'
    });

    if (error) {
      console.error('❌ Failed to sign in with demo account:', error);
      return;
    }

    console.log('✅ Successfully signed in as demo user');
    const userId = data.user.id;

    // Define the specific team ID we want to use
    const teamId = '83bd9832-f5db-4c7d-b234-41fd38f90007';

    // Check if the team already exists
    const { data: existingTeam, error: existingTeamError } = await supabase
      .from('teams')
      .select('*')
      .eq('id', teamId);

    if (existingTeamError) {
      console.error('❌ Error checking for existing team:', existingTeamError);
      return;
    }

    if (existingTeam && existingTeam.length > 0) {
      console.log('✅ Team already exists with ID:', teamId);
      console.log('Deleting existing team and all related data...');

      // Delete all related data first

      // Delete lineup innings
      const { error: deleteInningsError } = await supabase
        .from('lineup_innings')
        .delete()
        .eq('user_id', userId);

      if (deleteInningsError) {
        console.error('❌ Error deleting lineup innings:', deleteInningsError);
      } else {
        console.log('✅ Deleted lineup innings');
      }

      // Delete lineup attendance
      const { error: deleteAttendanceError } = await supabase
        .from('lineup_attendance')
        .delete()
        .eq('user_id', userId);

      if (deleteAttendanceError) {
        console.error('❌ Error deleting lineup attendance:', deleteAttendanceError);
      } else {
        console.log('✅ Deleted lineup attendance');
      }

      // Delete batting orders
      const { error: deleteBattingOrderError } = await supabase
        .from('batting_orders')
        .delete()
        .eq('user_id', userId);

      if (deleteBattingOrderError) {
        console.error('❌ Error deleting batting orders:', deleteBattingOrderError);
      } else {
        console.log('✅ Deleted batting orders');
      }

      // Delete lineups
      const { error: deleteLineupsError } = await supabase
        .from('lineups')
        .delete()
        .eq('team_id', teamId);

      if (deleteLineupsError) {
        console.error('❌ Error deleting lineups:', deleteLineupsError);
      } else {
        console.log('✅ Deleted lineups');
      }

      // Delete rotation rules
      const { error: deleteRulesError } = await supabase
        .from('rotation_rules')
        .delete()
        .eq('team_id', teamId);

      if (deleteRulesError) {
        console.error('❌ Error deleting rotation rules:', deleteRulesError);
      } else {
        console.log('✅ Deleted rotation rules');
      }

      // Delete players
      const { error: deletePlayersError } = await supabase
        .from('players')
        .delete()
        .eq('team_id', teamId);

      if (deletePlayersError) {
        console.error('❌ Error deleting players:', deletePlayersError);
      } else {
        console.log('✅ Deleted players');
      }

      // Delete team
      const { error: deleteTeamError } = await supabase
        .from('teams')
        .delete()
        .eq('id', teamId);

      if (deleteTeamError) {
        console.error('❌ Error deleting team:', deleteTeamError);
      } else {
        console.log('✅ Deleted team');
      }
    }

    // Create the team with the specific ID
    console.log('Creating team with ID:', teamId);
    const { error: teamError } = await supabase
      .from('teams')
      .insert({
        id: teamId,
        name: "Demo Softball Team",
        user_id: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (teamError) {
      console.error('❌ Failed to create team:', teamError);
      return;
    }

    console.log('✅ Successfully created team with ID:', teamId);

    // Create default players
    console.log('Creating default players...');
    const defaultPlayers = [
      { name: "Mikayla", teamRoles: { catcher: 'go-to', shortstop: 'capable' } },
      { name: "Finn", teamRoles: { pitcher: 'go-to', firstBase: 'capable' } },
      { name: "Avalon", teamRoles: { secondBase: 'capable', centerField: 'capable' } },
      { name: "Grace", teamRoles: { catcher: 'capable', rightField: 'capable' } },
      { name: "Bella", teamRoles: { thirdBase: 'capable', leftField: 'capable' } },
      { name: "Kenzie", teamRoles: { shortstop: 'capable', centerField: 'capable' } },
      { name: "Presley", teamRoles: { firstBase: 'capable', rightField: 'capable' } },
      { name: "Avery", teamRoles: { secondBase: 'capable', leftField: 'capable' } },
      { name: "Elle", teamRoles: { pitcher: 'capable', firstBase: 'capable' } },
      { name: "Vienna", teamRoles: { pitcher: 'capable', catcher: 'capable', firstBase: 'capable' } },
      { name: "Katelyn", teamRoles: { thirdBase: 'capable', leftField: 'capable' } },
      { name: "Morgan", teamRoles: { shortstop: 'capable', rightField: 'capable' } }
    ];

    const createdPlayers = [];

    for (const player of defaultPlayers) {
      const playerId = uuidv4();
      const { error: playerError } = await supabase
        .from('players')
        .insert({
          id: playerId,
          name: player.name,
          team_id: teamId,
          user_id: userId,
          position_preferences: {
            teamRoles: player.teamRoles || {}
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (playerError) {
        console.error(`❌ Failed to add player ${player.name}:`, playerError);
      } else {
        console.log(`✅ Added player: ${player.name}`);
        createdPlayers.push({
          id: playerId,
          name: player.name,
          positionRestrictions: player.positionRestrictions
        });
      }
    }

    // Create default rotation rules
    console.log('Creating default rotation rules...');
    const defaultRules = {
      rotationMethod: "standard",
      equalPlayingTime: true,
      rotatePlayers: true,
      respectPositionLockouts: true,
      allowPitcherRotation: false,
      allowCatcherRotation: true,
      prioritizeOutfieldRotation: true,
      limitBenchTime: true
    };

    const { error: rulesError } = await supabase
      .from('rotation_rules')
      .insert({
        id: uuidv4(),
        team_id: teamId,
        user_id: userId,
        rotation_method: defaultRules.rotationMethod || 'standard',
        equal_playing_time: defaultRules.equalPlayingTime || true,
        rotate_players: defaultRules.rotatePlayers || true,
        respect_position_lockouts: defaultRules.respectPositionLockouts || true,
        allow_pitcher_rotation: defaultRules.allowPitcherRotation || false,
        allow_catcher_rotation: defaultRules.allowCatcherRotation || true,
        prioritize_outfield_rotation: defaultRules.prioritizeOutfieldRotation || true,
        limit_bench_time: defaultRules.limitBenchTime || true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (rulesError) {
      console.error('❌ Failed to add rotation rules:', rulesError);
    } else {
      console.log('✅ Created default rotation rules');
    }

    // Create sample lineups
    console.log('Creating sample lineups...');

    // Create attendance data for all players
    const attendance = {};
    createdPlayers.forEach(player => {
      attendance[player.id] = true;
    });

    // Create sample lineups
    const sampleLineups = [
      {
        id: uuidv4(),
        name: "Forest Glade Tournament",
        gameDate: new Date().toISOString().split('T')[0], // Today's date
        createdDate: new Date().toISOString(),
        attendance,
        innings: [],
        battingOrder: createdPlayers.slice(0, Math.min(9, createdPlayers.length)).map(p => p.id)
      },
      {
        id: uuidv4(),
        name: "Essex Game",
        gameDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Next week
        createdDate: new Date().toISOString(),
        attendance,
        innings: [],
        battingOrder: createdPlayers.slice(0, Math.min(9, createdPlayers.length)).map(p => p.id)
      },
      {
        id: uuidv4(),
        name: "Sarnia Championship",
        gameDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Two weeks from now
        createdDate: new Date().toISOString(),
        attendance,
        innings: [],
        battingOrder: createdPlayers.slice(0, Math.min(9, createdPlayers.length)).map(p => p.id)
      }
    ];

    // Helper function to check if a player can play a position based on restrictions
    const canPlayPosition = (player, position) => {
      // Check position restrictions
      if (position === 'pitcher' && player.positionRestrictions.pitcher) {
        return false;
      }
      if (position === 'catcher' && player.positionRestrictions.catcher) {
        return false;
      }
      if (position === 'firstBase' && player.positionRestrictions.firstBase) {
        return false;
      }

      // Check other restrictions
      if (player.positionRestrictions.other) {
        const otherRestriction = player.positionRestrictions.other;
        if (otherRestriction === 'Shortstop' && position === 'shortstop') {
          return false;
        }
        if (otherRestriction === 'Middle Infield' && (position === 'shortstop' || position === 'secondBase')) {
          return false;
        }
        if (otherRestriction === '3B/MI/SS/2B' &&
            (position === 'thirdBase' || position === 'shortstop' || position === 'secondBase')) {
          return false;
        }
      }

      return true;
    };

    // Helper function to find a suitable player for a position
    const findPlayerForPosition = (availablePlayers, position) => {
      for (let i = 0; i < availablePlayers.length; i++) {
        if (canPlayPosition(availablePlayers[i], position)) {
          return availablePlayers.splice(i, 1)[0];
        }
      }
      return null;
    };

    // Generate innings for each lineup
    for (const lineup of sampleLineups) {
      console.log(`Generating innings for lineup: ${lineup.name}`);

      // Generate 6 innings with player rotations
      for (let i = 1; i <= 6; i++) {
        console.log(`Generating inning ${i} for ${lineup.name}`);

        // Create a copy of players for this inning
        const availablePlayers = [...createdPlayers];

        // Shuffle players for variety, but respect restrictions
        availablePlayers.sort(() => Math.random() - 0.5);

        // Find players for each position respecting restrictions
        const pitcherPlayer = findPlayerForPosition(availablePlayers, 'pitcher') || availablePlayers.splice(0, 1)[0];
        const catcherPlayer = findPlayerForPosition(availablePlayers, 'catcher') || availablePlayers.splice(0, 1)[0];
        const firstBasePlayer = findPlayerForPosition(availablePlayers, 'firstBase') || availablePlayers.splice(0, 1)[0];
        const secondBasePlayer = findPlayerForPosition(availablePlayers, 'secondBase') || availablePlayers.splice(0, 1)[0];
        const thirdBasePlayer = findPlayerForPosition(availablePlayers, 'thirdBase') || availablePlayers.splice(0, 1)[0];
        const shortstopPlayer = findPlayerForPosition(availablePlayers, 'shortstop') || availablePlayers.splice(0, 1)[0];
        const leftFieldPlayer = findPlayerForPosition(availablePlayers, 'leftField') || availablePlayers.splice(0, 1)[0];
        const centerFieldPlayer = findPlayerForPosition(availablePlayers, 'centerField') || availablePlayers.splice(0, 1)[0];
        const rightFieldPlayer = findPlayerForPosition(availablePlayers, 'rightField') || availablePlayers.splice(0, 1)[0];

        lineup.innings.push({
          inning: i,
          positions: {
            leftField: leftFieldPlayer.id,
            centerField: centerFieldPlayer.id,
            rightField: rightFieldPlayer.id,
            thirdBase: thirdBasePlayer.id,
            shortstop: shortstopPlayer.id,
            secondBase: secondBasePlayer.id,
            firstBase: firstBasePlayer.id,
            catcher: catcherPlayer.id,
            pitcher: pitcherPlayer.id,
            bench: availablePlayers.map(p => p.id)
          }
        });
      }
    }

    // Insert lineups one by one
    for (const lineup of sampleLineups) {
      // Insert the lineup
      const { error: lineupError } = await supabase
        .from('lineups')
        .insert({
          id: lineup.id,
          name: lineup.name,
          game_date: lineup.gameDate,
          team_id: teamId,
          user_id: userId,
          created_at: lineup.createdDate,
          updated_at: new Date().toISOString()
        });

      if (lineupError) {
        console.error(`❌ Failed to add lineup ${lineup.name}:`, lineupError);
        continue;
      }

      console.log(`✅ Added lineup: ${lineup.name}`);

      // Insert attendance
      const attendanceEntries = Object.entries(lineup.attendance).map(([playerId, isAttending]) => ({
        id: uuidv4(),
        lineup_id: lineup.id,
        player_id: playerId,
        is_present: isAttending,
        user_id: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

      const { error: attendanceError } = await supabase
        .from('lineup_attendance')
        .insert(attendanceEntries);

      if (attendanceError) {
        console.error(`❌ Failed to add attendance for lineup ${lineup.name}:`, attendanceError);
      } else {
        console.log(`✅ Added attendance for lineup: ${lineup.name}`);
      }

      // Insert innings
      for (const inning of lineup.innings) {
        const { error: inningError } = await supabase
          .from('lineup_innings')
          .insert({
            id: uuidv4(),
            lineup_id: lineup.id,
            inning_number: inning.inning,
            positions: inning.positions,
            user_id: userId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (inningError) {
          console.error(`❌ Failed to add inning ${inning.inning} for lineup ${lineup.name}:`, inningError);
        } else {
          console.log(`✅ Added inning ${inning.inning} for lineup: ${lineup.name}`);
        }
      }

      // Insert batting order
      const { error: battingOrderError } = await supabase
        .from('batting_orders')
        .insert({
          id: uuidv4(),
          lineup_id: lineup.id,
          player_order: lineup.battingOrder,
          user_id: userId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (battingOrderError) {
        console.error(`❌ Failed to add batting order for lineup ${lineup.name}:`, battingOrderError);
      } else {
        console.log(`✅ Added batting order for lineup: ${lineup.name}`);
      }
    }

    console.log('\n=== DEMO TEAM CREATION COMPLETE ===');
    console.log('Team ID:', teamId);
    console.log('Team Name: Demo Softball Team');
    console.log('Players:', createdPlayers.length);
    console.log('Lineups:', sampleLineups.length);

  } catch (err) {
    console.error('❌ Error during demo team creation:', err);
  }
}

createDemoTeam();
-- EMERGENCY FIX: Get user working immediately

-- 1. First, let's see what we're dealing with
SELECT 
    u.id,
    u.email,
    p.id as profile_id,
    s.id as subscription_id,
    s.is_paid,
    s.tier,
    s.subscription_tier,
    s.team_limit
FROM auth.users u
LEFT JOIN profiles p ON p.id = u.id
LEFT JOIN subscriptions s ON s.user_id = u.id
WHERE u.email = '<EMAIL>';

-- 2. Create the profile with service role bypass
INSERT INTO profiles (id, email, created_at, updated_at)
SELECT 
    id,
    email,
    NOW(),
    NOW()
FROM auth.users
WHERE email = '<EMAIL>'
ON CONFLICT (id) DO UPDATE
SET 
    email = EXCLUDED.email,
    updated_at = NOW();

-- 3. Delete any existing subscription that might be corrupted
DELETE FROM subscriptions
WHERE user_id IN (
    SELECT id FROM auth.users WHERE email = '<EMAIL>'
);

-- 4. Create a clean subscription without using the problematic tier column
INSERT INTO subscriptions (
    user_id,
    is_paid,
    team_limit,
    currency,
    amount,
    payment_date,
    created_at,
    updated_at
)
SELECT 
    id as user_id,
    true as is_paid,
    10 as team_limit,
    'usd' as currency,
    0 as amount,
    NOW() as payment_date,
    NOW() as created_at,
    NOW() as updated_at
FROM auth.users
WHERE email = '<EMAIL>';

-- 5. Now try to update the tier columns if they exist
UPDATE subscriptions
SET 
    tier = CASE 
        WHEN tier IS NOT NULL THEN tier 
        ELSE NULL 
    END,
    subscription_tier = 'pro'
WHERE user_id IN (
    SELECT id FROM auth.users WHERE email = '<EMAIL>'
);

-- 6. Verify everything is set up
SELECT 
    'User Status Report' as report,
    u.email,
    CASE WHEN p.id IS NOT NULL THEN '✅ Profile exists' ELSE '❌ Profile missing' END as profile_status,
    CASE WHEN s.id IS NOT NULL THEN '✅ Subscription exists' ELSE '❌ Subscription missing' END as subscription_status,
    s.is_paid,
    s.tier,
    s.subscription_tier,
    s.team_limit
FROM auth.users u
LEFT JOIN profiles p ON p.id = u.id
LEFT JOIN subscriptions s ON s.user_id = u.id
WHERE u.email = '<EMAIL>';

-- 7. Also check RLS policies to ensure user can access their own data
SELECT 
    tablename,
    policyname,
    cmd
FROM pg_policies
WHERE schemaname = 'public'
AND tablename IN ('profiles', 'subscriptions')
AND cmd = 'INSERT'
ORDER BY tablename;
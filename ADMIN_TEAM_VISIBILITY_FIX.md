# Admin Team Visibility Fix - Complete Solution

## Problem
<PERSON> (<EMAIL>) cannot see his teams when logged in as an admin. The error shows:
- `infinite recursion detected in policy for relation "profiles"`
- Teams are being filtered by user_id in the application layer

## Root Causes
1. **Database Level**: Infinite recursion in RLS policies for the profiles table
2. **Application Level**: Team services are filtering by user_id even for admins

## Solutions Applied

### 1. Database Fix (Run this SQL in Supabase Dashboard)

```sql
-- Fix infinite recursion in profiles table
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

-- Drop ALL existing policies on profiles
DO $$
DECLARE
  policy record;
BEGIN
  FOR policy IN 
    SELECT policyname 
    FROM pg_policies 
    WHERE schemaname = 'public' AND tablename = 'profiles'
  LOOP
    EXECUTE format('DROP POLICY IF EXISTS %I ON public.profiles', policy.policyname);
  END LOOP;
END $$;

-- Re-enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Create simple, non-recursive policies for profiles
CREATE POLICY "profiles_select" ON public.profiles
  FOR SELECT USING (true);  -- Allow all authenticated users to read profiles

CREATE POLICY "profiles_insert" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid()::text = id);

CREATE POLICY "profiles_update" ON public.profiles
  FOR UPDATE USING (auth.uid()::text = id);

CREATE POLICY "profiles_delete" ON public.profiles
  FOR DELETE USING (auth.uid()::text = id);

-- Fix teams table policies too
DO $$
DECLARE
  policy record;
BEGIN
  FOR policy IN 
    SELECT policyname 
    FROM pg_policies 
    WHERE schemaname = 'public' AND tablename = 'teams'
  LOOP
    EXECUTE format('DROP POLICY IF EXISTS %I ON public.teams', policy.policyname);
  END LOOP;
END $$;

-- Create simple teams policies
CREATE POLICY "teams_select" ON public.teams
  FOR SELECT USING (
    auth.uid() = user_id 
    OR auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

CREATE POLICY "teams_insert" ON public.teams
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "teams_update" ON public.teams
  FOR UPDATE USING (
    auth.uid() = user_id 
    OR auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

CREATE POLICY "teams_delete" ON public.teams
  FOR DELETE USING (
    auth.uid() = user_id 
    OR auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

-- Grant permissions
GRANT ALL ON public.profiles TO authenticated;
GRANT ALL ON public.teams TO authenticated;
```

### 2. Application Layer Fixes (Already Applied)

#### Updated `src/services/teamService.ts`:
- Added admin detection to fetch ALL teams for admin users
- Regular users still only see their own teams

#### Updated `src/services/teamServiceOptimized.ts`:
- Same admin detection logic for the optimized service
- Admins see all teams with a reasonable limit (100)

### 3. How to Apply the Database Fix

1. Go to your Supabase Dashboard
2. Navigate to the SQL Editor
3. Copy and paste the SQL from section 1 above
4. Click "Run" to execute the SQL
5. Check for any errors in the output

### 4. Verification Steps

After applying the fix:

1. Clear your browser cache/local storage
2. Log out and log back <NAME_EMAIL>
3. Check the browser console for:
   - "Admin user detected, fetching all teams"
   - No more recursion errors
4. You should now see all teams in the system

### 5. Alternative Solution (If Still Not Working)

If the above doesn't work, you can create a dedicated admin view:

```sql
-- Create a view that bypasses RLS for admins
CREATE OR REPLACE VIEW public.admin_all_teams AS
SELECT 
  t.*,
  u.email as owner_email,
  p.full_name as owner_name
FROM public.teams t
LEFT JOIN auth.users u ON t.user_id = u.id
LEFT JOIN public.profiles p ON p.id = u.id::text
WHERE auth.email() IN ('<EMAIL>', '<EMAIL>');

-- Grant access
GRANT SELECT ON public.admin_all_teams TO authenticated;
```

Then update the team services to use this view for admins.

## Summary

The main issues were:
1. Circular/recursive RLS policies in the profiles table causing 500 errors
2. Application services filtering teams by user_id even for admins

Both have been fixed. The database fix is critical and must be applied via the Supabase SQL editor.
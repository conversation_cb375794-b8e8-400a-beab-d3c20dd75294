import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://mhuuptkgohuztjrovpxz.supabase.co';
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1odXVwdGtnb2h1enRqcm92cHh6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY0NzU0NDEsImV4cCI6MjA2MjA1MTQ0MX0.aGUpltDKIHYJECOuFHeES7VJp7RKlMjArSg7NxFai_k';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testCompetitiveModeAlgorithm() {
  console.log('🧪 Testing Competitive Mode Algorithm Fix\n');
  
  try {
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error('❌ No user logged in');
      return;
    }

    // Get user's teams
    const { data: teams, error: teamsError } = await supabase
      .from('teams')
      .select('*')
      .eq('user_id', user.id);

    if (teamsError || !teams?.length) {
      console.error('❌ Error fetching teams:', teamsError);
      return;
    }

    console.log(`✅ Found ${teams.length} teams\n`);

    for (const team of teams) {
      console.log(`\n📋 Testing Team: ${team.name}`);
      console.log(`   Team ID: ${team.id}`);
      console.log(`   Rotation Rules Competitive Mode: ${team.rotation_rules?.competitiveMode || false}`);
      
      // Get players for this team
      const { data: players, error: playersError } = await supabase
        .from('players')
        .select('*')
        .eq('team_id', team.id)
        .eq('is_active', true);

      if (playersError || !players?.length) {
        console.error('   ❌ Error fetching players:', playersError);
        continue;
      }

      console.log(`   Total Players: ${players.length}`);
      
      // Check how many players have position assignments
      let playersWithTeamRoles = 0;
      let playersWithPositionPrefs = 0;
      let playersWithEither = 0;
      
      players.forEach(player => {
        const hasTeamRoles = player.team_roles && Object.keys(player.team_roles).length > 0;
        const hasPositionPrefs = player.position_preferences && Object.keys(player.position_preferences).length > 0;
        
        if (hasTeamRoles) playersWithTeamRoles++;
        if (hasPositionPrefs) playersWithPositionPrefs++;
        if (hasTeamRoles || hasPositionPrefs) playersWithEither++;
      });
      
      console.log(`   Players with team_roles: ${playersWithTeamRoles}`);
      console.log(`   Players with position_preferences: ${playersWithPositionPrefs}`);
      console.log(`   Players with either: ${playersWithEither}`);
      
      // Get lineups for this team
      const { data: lineups, error: lineupsError } = await supabase
        .from('lineups')
        .select('*')
        .eq('team_id', team.id)
        .order('created_at', { ascending: false })
        .limit(5);

      if (lineupsError) {
        console.error('   ❌ Error fetching lineups:', lineupsError);
        continue;
      }

      console.log(`\n   Recent Lineups (${lineups.length}):`);
      
      lineups.forEach(lineup => {
        const storedCompetitiveMode = lineup.rotation_settings?.competitiveMode ?? false;
        const teamCompetitiveMode = team.rotation_rules?.competitiveMode ?? false;
        const mismatch = storedCompetitiveMode !== teamCompetitiveMode;
        
        console.log(`\n   📄 ${lineup.name}`);
        console.log(`      Created: ${new Date(lineup.created_at).toLocaleString()}`);
        console.log(`      Stored Competitive Mode: ${storedCompetitiveMode}`);
        console.log(`      Team's Current Mode: ${teamCompetitiveMode}`);
        console.log(`      Mode Mismatch: ${mismatch ? '⚠️ YES' : '✅ NO'}`);
        
        if (mismatch) {
          console.log(`      ⚠️ This lineup will now use team's mode: ${teamCompetitiveMode}`);
        }
      });
      
      // Test scenario summary
      console.log(`\n   🎯 Test Scenario Summary:`);
      console.log(`      - Team is in ${team.rotation_rules?.competitiveMode ? 'COMPETITIVE' : 'FAIR PLAY'} mode`);
      console.log(`      - ${playersWithEither} of ${players.length} players have position assignments`);
      
      if (team.rotation_rules?.competitiveMode) {
        if (playersWithEither === 0) {
          console.log(`      ❌ ISSUE: Competitive mode requires position assignments!`);
          console.log(`      💡 Solution: Assign positions in Team Roster page`);
        } else {
          console.log(`      ✅ Good: Players have position assignments for competitive mode`);
        }
      } else {
        console.log(`      ✅ Fair play mode: Position assignments optional`);
      }
    }
    
    console.log('\n\n✅ Test Complete!');
    console.log('\n💡 Key Fixes Applied:');
    console.log('   1. ViewLineup now uses team\'s current competitive mode setting');
    console.log('   2. Algorithm checks both team_roles and position_preferences');
    console.log('   3. Fair play mode allows players without position assignments');
    console.log('   4. Competitive mode enforces position assignments');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testCompetitiveModeAlgorithm();
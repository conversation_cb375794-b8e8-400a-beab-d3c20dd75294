-- Fix the authentication system properly
-- This ensures profiles and subscriptions are created automatically

-- 1. Create or replace the function that handles new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETUR<PERSON> trigger
SECURITY DEFINER SET search_path = public
LANGUAGE plpgsql
AS $$
BEGIN
  -- Create profile
  INSERT INTO public.profiles (id, email, created_at, updated_at)
  VALUES (new.id, new.email, new.created_at, new.created_at)
  ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    updated_at = NOW();

  -- Create default subscription (unpaid)
  INSERT INTO public.subscriptions (
    user_id, 
    is_paid, 
    tier, 
    team_limit, 
    created_at, 
    updated_at
  )
  VALUES (
    new.id, 
    false, 
    'starter', 
    0, 
    NOW(), 
    NOW()
  )
  ON CONFLICT (user_id) DO NOTHING;

  RETURN new;
END;
$$;

-- 2. Create trigger for new users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 3. Fix any existing users without profiles
INSERT INTO public.profiles (id, email, created_at, updated_at)
SELECT id, email, created_at, NOW()
FROM auth.users
WHERE id NOT IN (SELECT id FROM public.profiles);

-- 4. Fix any profiles without subscriptions
INSERT INTO public.subscriptions (user_id, is_paid, tier, team_limit, created_at, updated_at)
SELECT id, false, 'starter', 0, NOW(), NOW()
FROM public.profiles
WHERE id NOT IN (SELECT user_id FROM public.subscriptions);

-- 5. Ensure RLS policies allow users to read their own data
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
CREATE POLICY "Users can view own profile" ON public.profiles
  FOR SELECT USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
CREATE POLICY "Users can insert own profile" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
CREATE POLICY "Users can update own profile" ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

-- 6. Fix subscription RLS to ensure it works
DROP POLICY IF EXISTS "Users can view their own subscription" ON public.subscriptions;
CREATE POLICY "Users can view their own subscription" ON public.subscriptions
  FOR SELECT USING (auth.uid() = user_id);

-- 7. Verify the setup
SELECT 
  'Users in auth.users' as check_type,
  COUNT(*) as count
FROM auth.users
UNION ALL
SELECT 
  'Profiles created',
  COUNT(*)
FROM public.profiles
UNION ALL
SELECT 
  'Subscriptions created',
  COUNT(*)
FROM public.subscriptions
UNION ALL
SELECT 
  'Users missing profiles',
  COUNT(*)
FROM auth.users a
LEFT JOIN public.profiles p ON a.id = p.id
WHERE p.id IS NULL
UNION ALL
SELECT 
  'Profiles missing subscriptions',
  COUNT(*)
FROM public.profiles p
LEFT JOIN public.subscriptions s ON p.id = s.user_id
WHERE s.user_id IS NULL;
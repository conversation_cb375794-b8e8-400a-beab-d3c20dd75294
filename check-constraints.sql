-- Check for any constraints or triggers on the subscriptions table

-- Check constraints
SELECT 
    con.conname AS constraint_name,
    pg_get_constraintdef(con.oid) AS constraint_definition
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
WHERE nsp.nspname = 'public' 
AND rel.relname = 'subscriptions';

-- Check triggers
SELECT 
    trg.tgname AS trigger_name,
    CASE trg.tgtype
        WHEN 1 THEN 'BEFORE'
        WHEN 2 THEN 'AFTER'
        WHEN 66 THEN 'INSTEAD OF'
    END AS trigger_timing,
    CASE 
        WHEN trg.tgtype & 4 = 4 THEN 'INSERT'
        WHEN trg.tgtype & 8 = 8 THEN 'DELETE'
        WHEN trg.tgtype & 16 = 16 THEN 'UPDATE'
    END AS trigger_event,
    p.proname AS function_name
FROM pg_trigger trg
JOIN pg_class tbl ON trg.tgrelid = tbl.oid
JOIN pg_proc p ON p.oid = trg.tgfoid
JOIN pg_namespace n ON n.oid = tbl.relnamespace
WHERE n.nspname = 'public' 
AND tbl.relname = 'subscriptions'
AND trg.tgisinternal = false;

-- Check column defaults
SELECT 
    column_name,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public' 
AND table_name = 'subscriptions'
AND column_name IN ('is_paid', 'tier', 'team_limit');
-- CORRECT FIX: Using valid tier values (starter, coach, club)

-- Step 1: Check current user status
SELECT 
    u.id,
    u.email,
    p.id IS NOT NULL as has_profile,
    s.id IS NOT NULL as has_subscription,
    s.tier,
    s.team_limit,
    s.is_paid
FROM auth.users u
LEFT JOIN profiles p ON p.id = u.id
LEFT JOIN subscriptions s ON s.user_id = u.id
WHERE u.email = '<EMAIL>';

-- Step 2: Create profile if missing
INSERT INTO profiles (id, email, created_at, updated_at)
SELECT id, email, NOW(), NOW()
FROM auth.users
WHERE email = '<EMAIL>'
ON CONFLICT (id) DO NOTHING;

-- Step 3: Create/Update subscription with VALID tier
INSERT INTO subscriptions (
    user_id,
    is_paid,
    tier,        -- Use 'club' which gives maximum access
    team_limit,
    currency,
    amount,
    payment_date,
    created_at,
    updated_at
)
SELECT 
    id,
    true,
    'club',      -- Valid tier with 999 team limit
    999,         -- Maximum teams
    'usd',
    0,
    NOW(),
    NOW(),
    NOW()
FROM auth.users
WHERE email = '<EMAIL>'
ON CONFLICT (user_id) DO UPDATE
SET 
    is_paid = true,
    tier = 'club',
    team_limit = 999,
    updated_at = NOW();

-- Step 4: Verify the fix worked
SELECT 
    p.email,
    s.is_paid,
    s.tier,
    s.team_limit,
    s.created_at,
    CASE 
        WHEN s.is_paid = true AND s.tier = 'club' THEN '✅ User is fully set up!'
        ELSE '❌ Something went wrong'
    END as status
FROM profiles p
JOIN subscriptions s ON s.user_id = p.id
WHERE p.email = '<EMAIL>';
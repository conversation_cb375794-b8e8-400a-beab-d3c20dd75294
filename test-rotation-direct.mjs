// Import directly from source
import { generateCompleteLineup } from './src/lib/utils-enhanced.ts';

// Test with 12 players like in your screenshot
const players = [
  { id: '1', name: '<PERSON>', teamRoles: {} },
  { id: '2', name: '<PERSON><PERSON>', teamRoles: {} },
  { id: '3', name: '<PERSON>', teamRoles: {} },
  { id: '4', name: '<PERSON><PERSON><PERSON>', teamRoles: {} },
  { id: '5', name: '<PERSON>', teamRoles: {} },
  { id: '6', name: '<PERSON>', teamRoles: {} },
  { id: '7', name: '<PERSON>', teamRoles: {} },
  { id: '8', name: 'Player8', teamRoles: {} },
  { id: '9', name: 'Player9', teamRoles: {} },
  { id: '10', name: 'Player10', teamRoles: {} },
  { id: '11', name: 'Player11', teamRoles: {} },
  { id: '12', name: 'Player12', teamRoles: {} }
];

console.log('===========================================');
console.log('TESTING ROTATION WITH rotateLineupEvery = 1');
console.log('===========================================');
console.log('Players: 12');
console.log('Total Innings: 20 (like your screenshot)');
console.log('Expected: ~15 innings per player');
console.log('===========================================\n');

const innings = generateCompleteLineup(players, 20, {
  rotateLineupEvery: 1,
  rotatePitcherEvery: 2,
  equalPlayingTime: true,
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 2
});

// Count playing time
const playingTime = {};
const benchTime = {};
players.forEach(p => {
  playingTime[p.name] = 0;
  benchTime[p.name] = 0;
});

innings.forEach((inning, idx) => {
  // Count field time
  Object.entries(inning.positions).forEach(([pos, playerName]) => {
    if (pos !== 'bench' && playerName) {
      playingTime[playerName]++;
    }
  });
  
  // Count bench time
  if (inning.positions.bench) {
    inning.positions.bench.forEach(playerName => {
      benchTime[playerName]++;
    });
  }
  
  // Show progress every 5 innings
  if ((idx + 1) % 5 === 0) {
    const times = Object.values(playingTime);
    const max = Math.max(...times);
    const min = Math.min(...times);
    console.log(`After inning ${idx + 1}: Range = ${max - min} (Max: ${max}, Min: ${min})`);
  }
});

console.log('\n===========================================');
console.log('FINAL PLAYING TIME RESULTS:');
console.log('===========================================\n');

// Sort by playing time
const sorted = Object.entries(playingTime).sort(([,a], [,b]) => b - a);
sorted.forEach(([name, time]) => {
  const bench = benchTime[name];
  const bar = '█'.repeat(time);
  const benchBar = '░'.repeat(bench);
  console.log(`${name.padEnd(10)} Field: ${time} ${bar} Bench: ${bench} ${benchBar}`);
});

// Calculate metrics
const times = Object.values(playingTime);
const max = Math.max(...times);
const min = Math.min(...times);
const avg = times.reduce((a, b) => a + b) / times.length;
const range = max - min;

console.log('\n===========================================');
console.log('BALANCE METRICS:');
console.log('===========================================');
console.log(`Maximum: ${max} innings`);
console.log(`Minimum: ${min} innings`);
console.log(`Average: ${avg.toFixed(1)} innings`);
console.log(`Range: ${range} innings`);
console.log(`Ideal: 15.0 innings (20 × 9 ÷ 12)`);

// Verdict
console.log('\n===========================================');
if (max === 20) {
  console.log('❌ FAILED: Some players played ALL 20 innings!');
  console.log('This is exactly the problem from your screenshot.');
} else if (range <= 3) {
  console.log('✅ EXCELLENT: Very balanced playing time!');
} else if (range <= 5) {
  console.log('✅ GOOD: Reasonable balance achieved.');
} else {
  console.log('⚠️  NEEDS WORK: Balance could be better.');
}

// Compare to screenshot
console.log('\n===========================================');
console.log('COMPARISON TO YOUR SCREENSHOT:');
console.log('===========================================');
console.log('Your screenshot showed:');
console.log('- Finn, Evelynn, Avery: 20 innings (never benched)');
console.log('- Others: 11-12 innings');
console.log('- Range: 9 innings (TERRIBLE)');
console.log('\nCurrent algorithm:');
console.log(`- Range: ${range} innings`);
console.log(`- Max player: ${sorted[0][0]} with ${sorted[0][1]} innings`);
console.log(`- Min player: ${sorted[sorted.length-1][0]} with ${sorted[sorted.length-1][1]} innings`);
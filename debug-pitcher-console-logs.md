# Debug Console Logs for Pitcher Role Save Issue

To debug why the pitcher role isn't saving, add these console.log statements:

## 1. In TeamRoleManager.tsx (line 185-194)

```javascript
const handleRoleChange = (position: string, role: PlayerRole) => {
  if (disabled) return;

  console.log(`TeamRoleManager: Changing ${player.name} - ${position} to ${role}`);
  console.log('Current teamRoles:', player.teamRoles);
  console.log('Is pitcher position?', position === 'pitcher'); // ADD THIS

  const updatedRoles = {
    ...player.teamRoles,
    [position]: role === 'unset' ? undefined : role
  };

  console.log('Updated teamRoles:', updatedRoles);
  console.log('Pitcher in updatedRoles?', 'pitcher' in updatedRoles); // ADD THIS
  console.log('Pitcher value:', updatedRoles.pitcher); // ADD THIS
  
  onRoleChange(player.id, updatedRoles);
  // ... rest of function
};
```

## 2. In TeamRoster.tsx (line 170-182)

```javascript
const handleTeamRoleChange = (playerId: string, teamRoles: TeamRolePreferences) => {
  console.log(`TeamRoster: handleTeamRoleChange for player ${playerId}`);
  console.log('New teamRoles:', teamRoles);
  console.log('Has pitcher?', 'pitcher' in teamRoles); // ADD THIS
  console.log('Pitcher value:', teamRoles.pitcher); // ADD THIS
  
  setLocalPlayers(localPlayers.map(player =>
    player.id === playerId
      ? {
          ...player,
          teamRoles: teamRoles
        }
      : player
  ));
  
  // ADD THIS - verify the update worked
  const updatedPlayer = localPlayers.find(p => p.id === playerId);
  console.log('Updated player teamRoles:', updatedPlayer?.teamRoles);
  
  setHasUnsavedChanges(true);
};
```

## 3. In TeamRoster.tsx handleSaveIndividualPlayer (line 185-208)

```javascript
const handleSaveIndividualPlayer = async (playerId: string) => {
  console.log(`TeamRoster: Saving individual player ${playerId}`);
  
  const player = localPlayers.find(p => p.id === playerId);
  if (!player) {
    toast.error("Player not found");
    return;
  }
  
  // ADD THESE
  console.log('Player to save:', player.name);
  console.log('Player teamRoles:', player.teamRoles);
  console.log('Has pitcher role?', 'pitcher' in (player.teamRoles || {}));
  console.log('Pitcher value:', player.teamRoles?.pitcher);
  
  // ... rest of function
};
```

## 4. In teamService.ts updatePlayer (line 294-314)

```javascript
export const updatePlayer = async (player: Player): Promise<Player> => {
  try {
    console.log('teamService.updatePlayer: Starting update for player:', {
      id: player.id,
      name: player.name,
      teamRoles: player.teamRoles,
      pitcherStrategy: player.pitcherStrategy
    });
    
    // ADD THESE
    console.log('Has pitcher in teamRoles?', 'pitcher' in (player.teamRoles || {}));
    console.log('Pitcher role value:', player.teamRoles?.pitcher);

    const updateData = {
      name: player.name,
      pitcher_restriction: player.positionRestrictions?.pitcher || false,
      catcher_restriction: player.positionRestrictions?.catcher || false,
      first_base_restriction: player.positionRestrictions?.firstBase || false,
      other_restriction: player.positionRestrictions?.other || null,
      position_preferences: {
        ...(player.positionPreferences || {}),
        teamRoles: player.teamRoles || {},
        pitcherStrategy: player.pitcherStrategy || null
      }
    };

    console.log('teamService.updatePlayer: Update data prepared:', updateData);
    
    // ADD THIS
    console.log('Pitcher in position_preferences.teamRoles?', 
      'pitcher' in (updateData.position_preferences.teamRoles || {}));
    
    // ... rest of function
};
```

## 5. Check Browser Console

When testing:
1. Open browser developer tools (F12)
2. Go to Console tab
3. Clear console
4. Try to set a player as Primary pitcher
5. Click Save
6. Look for the console logs in this order:
   - TeamRoleManager: Changing...
   - TeamRoster: handleTeamRoleChange...
   - TeamRoster: Saving individual player...
   - teamService.updatePlayer...

## 6. Check Network Tab

1. Go to Network tab in developer tools
2. Clear network log
3. Try to save pitcher role
4. Look for the request to `players` table
5. Click on it and check:
   - Request payload (what's being sent)
   - Response (what comes back)

## Expected Flow

1. User clicks "Primary" for Pitcher position
2. `handleRoleChange` updates local state with pitcher: 'go-to'
3. `onRoleChange` calls `handleTeamRoleChange` in TeamRoster
4. TeamRoster updates its localPlayers state
5. User clicks Save
6. `handleSaveIndividualPlayer` gets the player with updated teamRoles
7. `updatePlayer` from TeamContext is called
8. `teamService.updatePlayer` sends to database

## Possible Issues

1. **State not updating**: The localPlayers state might not be updating correctly
2. **Object reference**: The player object might be stale
3. **Database filter**: Something might be filtering out the pitcher key
4. **JSON size**: The position_preferences JSON might be too large

## Quick Test

Try this in browser console while on the Team Roster page:

```javascript
// Get the current player data
const playerElements = document.querySelectorAll('[data-player-id]');
console.log('Players found:', playerElements.length);

// Check localStorage for any filters
console.log('LocalStorage keys:', Object.keys(localStorage));
```
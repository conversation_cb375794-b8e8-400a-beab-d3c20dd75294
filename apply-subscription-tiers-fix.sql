-- Fix subscription tiers migration
-- This ensures all required columns exist and have proper values

-- First, add columns if they don't exist (without constraints)
ALTER TABLE public.subscriptions 
ADD COLUMN IF NOT EXISTS tier TEXT,
ADD COLUMN IF NOT EXISTS price_id TEXT,
ADD COLUMN IF NOT EXISTS product_id TEXT,
ADD COLUMN IF NOT EXISTS team_limit INTEGER,
ADD COLUMN IF NOT EXISTS subscription_period TEXT,
ADD COLUMN IF NOT EXISTS expires_at TIMESTAMPTZ;

-- Update any NULL values to defaults
UPDATE public.subscriptions 
SET 
    tier = COALESCE(tier, 'starter'),
    team_limit = COALESCE(team_limit, 
        CASE 
            WHEN tier = 'starter' THEN 1
            WHEN tier = 'coach' THEN 5
            WHEN tier = 'club' THEN 999
            ELSE 1
        END
    ),
    subscription_period = COALESCE(subscription_period, 'annual')
WHERE is_paid = true;

-- Now add the constraint if it doesn't exist
DO $$ 
BEGIN 
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'valid_tier' 
    ) THEN
        ALTER TABLE public.subscriptions 
        ADD CONSTRAINT valid_tier CHECK (tier IN ('starter', 'coach', 'club'));
    END IF;
END $$;

-- Create indexes if they don't exist
CREATE INDEX IF NOT EXISTS idx_subscriptions_tier ON public.subscriptions(user_id, tier);
CREATE INDEX IF NOT EXISTS idx_subscriptions_expires ON public.subscriptions(expires_at);

-- Show current subscription status
SELECT 
    p.email,
    s.is_paid,
    s.tier,
    s.team_limit,
    s.expires_at,
    s.created_at,
    s.updated_at
FROM subscriptions s
JOIN profiles p ON s.user_id = p.id
ORDER BY s.created_at DESC;
-- IMPORTANT: Run this in steps!

-- Step 1: First, run ONLY this query to check the data types
SELECT 
    table_name,
    column_name,
    data_type,
    udt_name
FROM information_schema.columns 
WHERE table_name IN ('profiles', 'subscriptions') 
    AND column_name IN ('id', 'user_id')
ORDER BY table_name, column_name;

-- STOP HERE! Look at the results above before continuing.
-- Based on the results, use the appropriate section below:

-- ============================================
-- SECTION A: If profiles.id is UUID (not text)
-- ============================================
-- Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "allow_users_own_profile_select" ON profiles;
DROP POLICY IF EXISTS "allow_users_own_profile_insert" ON profiles;
DROP POLICY IF EXISTS "allow_users_own_profile_update" ON profiles;
DROP POLICY IF EXISTS "allow_users_own_subscription_select" ON subscriptions;
DROP POLICY IF EXISTS "allow_users_own_subscription_insert" ON subscriptions;
DROP POLICY IF EXISTS "allow_users_own_subscription_update" ON subscriptions;

-- Create policies for profiles (id is UUID)
CREATE POLICY "allow_users_own_profile_select" ON profiles
    FOR SELECT TO authenticated
    USING (auth.uid() = id);

CREATE POLICY "allow_users_own_profile_insert" ON profiles
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = id);

CREATE POLICY "allow_users_own_profile_update" ON profiles
    FOR UPDATE TO authenticated
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

-- Create policies for subscriptions (user_id is UUID)
CREATE POLICY "allow_users_own_subscription_select" ON subscriptions
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "allow_users_own_subscription_insert" ON subscriptions
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "allow_users_own_subscription_update" ON subscriptions
    FOR UPDATE TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- ============================================
-- SECTION B: If profiles.id is TEXT (not uuid)
-- ============================================
/*
-- Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "allow_users_own_profile_select" ON profiles;
DROP POLICY IF EXISTS "allow_users_own_profile_insert" ON profiles;
DROP POLICY IF EXISTS "allow_users_own_profile_update" ON profiles;
DROP POLICY IF EXISTS "allow_users_own_subscription_select" ON subscriptions;
DROP POLICY IF EXISTS "allow_users_own_subscription_insert" ON subscriptions;
DROP POLICY IF EXISTS "allow_users_own_subscription_update" ON subscriptions;

-- Create policies for profiles (id is TEXT)
CREATE POLICY "allow_users_own_profile_select" ON profiles
    FOR SELECT TO authenticated
    USING (auth.uid()::text = id);

CREATE POLICY "allow_users_own_profile_insert" ON profiles
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid()::text = id);

CREATE POLICY "allow_users_own_profile_update" ON profiles
    FOR UPDATE TO authenticated
    USING (auth.uid()::text = id)
    WITH CHECK (auth.uid()::text = id);

-- Create policies for subscriptions (user_id is UUID)
CREATE POLICY "allow_users_own_subscription_select" ON subscriptions
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "allow_users_own_subscription_insert" ON subscriptions
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "allow_users_own_subscription_update" ON subscriptions
    FOR UPDATE TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);
*/

-- Step 3: Verify the policies were created
SELECT 
    tablename,
    policyname,
    cmd,
    roles,
    qual,
    with_check
FROM pg_policies 
WHERE tablename IN ('profiles', 'subscriptions')
ORDER BY tablename, policyname;
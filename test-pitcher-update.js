import { supabase } from "./src/integrations/supabase/node-client.js";

async function testPitcherUpdate() {
  try {
    // First, fetch a player
    const { data: players, error: fetchError } = await supabase
      .from('players')
      .select('id, name, position_preferences')
      .limit(1);

    if (fetchError) {
      console.error('Fetch error:', fetchError);
      return;
    }

    if (\!players || players.length === 0) {
      console.log('No players found');
      return;
    }

    const player = players[0];
    console.log('Current player:', player);

    // Try to update with pitcher role
    const updatedPrefs = {
      ...(player.position_preferences || {}),
      teamRoles: {
        ...(player.position_preferences?.teamRoles || {}),
        pitcher: 'in_the_mix'
      }
    };

    console.log('Updating with:', updatedPrefs);

    const { data: updated, error: updateError } = await supabase
      .from('players')
      .update({ position_preferences: updatedPrefs })
      .eq('id', player.id)
      .select()
      .single();

    if (updateError) {
      console.error('Update error:', updateError);
      return;
    }

    console.log('Updated player:', updated);
    console.log('Pitcher role after update:', updated.position_preferences?.teamRoles?.pitcher);

  } catch (error) {
    console.error('Error:', error);
  }
}

testPitcherUpdate();
EOF < /dev/null
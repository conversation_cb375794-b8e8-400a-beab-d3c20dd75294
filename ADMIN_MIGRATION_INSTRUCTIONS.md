# Admin Database Migration Instructions

The admin pages are failing because the database is missing required columns and tables. You need to run the migration manually in Supabase.

## Quick Fix (Minimal Changes)

If you need to get admin working immediately, run this smaller migration first:
1. Go to your Supabase SQL Editor
2. Copy and paste the contents of: `supabase/migrations/20250129100001_admin_quick_fix.sql`
3. Click "Run"

This will add just the essential columns needed for admin pages to work.

## Full Migration (Recommended)

For the complete admin setup with all features:

1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Copy and paste the entire contents of: `supabase/migrations/20250129100000_add_admin_columns_and_tables.sql`
4. Click "Run" to execute the migration

## Option 2: Use Supabase CLI

If you have the Supabase CLI installed and configured:

```bash
supabase migration up
```

## What This Migration Does:

1. **Adds columns to profiles table:**
   - `email` - Stores user email
   - `is_admin` - Boolean flag for admin users
   - `is_paid` - Boolean flag for paid users

2. **Creates subscriptions table** for payment tracking

3. **Creates admin_audit_logs table** for admin activity logging

4. **Sets up proper RLS policies** so:
   - Regular users see only their own data
   - Admins can see all data

5. **Creates triggers** to automatically:
   - Create profile records when users sign up
   - Keep email in sync between auth.users and profiles

6. **Marks admin users** based on email addresses:
   - <EMAIL>
   - <EMAIL>

## After Running the Migration:

The admin pages should start working immediately. If you still see errors:

1. Clear your browser cache
2. Sign out and sign back in
3. Make sure you're logged in with an admin email address

## Verification:

You can verify the migration worked by running these queries in the SQL editor:

```sql
-- Check if profiles has email column
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'profiles' AND column_name = 'email';

-- Check if subscriptions table exists
SELECT * FROM information_schema.tables 
WHERE table_name = 'subscriptions';

-- Check admin users
SELECT id, email, is_admin FROM profiles 
WHERE is_admin = true;
```
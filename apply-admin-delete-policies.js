#!/usr/bin/env node

import { createServiceClient } from "./src/integrations/supabase/node-client.js";
import fs from 'fs';

// Load environment variables

if (!supabaseUrl) {
  console.error('Missing VITE_SUPABASE_URL in .env.local');
  process.exit(1);
}

if (!supabaseServiceKey) {
  console.error('\n⚠️  Missing SUPABASE_SERVICE_ROLE_KEY in .env.local');
  console.error('\nTo fix this:');
  console.error('1. Go to your Supabase dashboard');
  console.error('2. Navigate to Settings > API');
  console.error('3. Copy the "service_role" key (starts with "eyJ...")');
  console.error('4. Add it to your .env.local file:');
  console.error('   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here\n');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createServiceClient();

async function applyMigration() {
  console.log('🔧 Applying admin delete policies migration...\n');

  try {
    // Read the migration file
    const migrationSQL = fs.readFileSync('./supabase/migrations/add_admin_delete_policies.sql', 'utf8');
    
    // Execute the migration
    const { error } = await supabase.rpc('exec_sql', { 
      sql: migrationSQL 
    });

    if (error) {
      // If exec_sql doesn't exist, try direct execution
      console.log('Direct RPC failed, trying alternative method...');
      
      // Split the SQL into individual statements
      const statements = migrationSQL
        .split(';')
        .map(s => s.trim())
        .filter(s => s.length > 0);

      console.log(`Executing ${statements.length} SQL statements...\n`);

      for (const statement of statements) {
        console.log(`Executing: ${statement.substring(0, 50)}...`);
        
        // This is a workaround - we'll need to apply via Supabase dashboard
        console.log('✅ Statement prepared');
      }

      console.log('\n⚠️  Unable to directly execute SQL via API.');
      console.log('\n📋 Please run the following SQL in your Supabase SQL Editor:');
      console.log('   1. Go to your Supabase dashboard');
      console.log('   2. Navigate to SQL Editor');
      console.log('   3. Copy and paste the contents of:');
      console.log('      supabase/migrations/add_admin_delete_policies.sql');
      console.log('   4. Click "Run"\n');
      
      return;
    }

    console.log('✅ Migration applied successfully!');
    console.log('\nAdmin users can now delete any user data.');

  } catch (error) {
    console.error('❌ Error applying migration:', error.message);
    console.error('\nPlease apply the migration manually in your Supabase dashboard.');
  }
}

// Run the migration
applyMigration();
-- <PERSON><PERSON><PERSON> to restore U15 Selects roster based on lineup data
-- This script will create players for the U15 Selects team based on the players found in existing lineups

-- First, let's identify the players that should be in U15 Selects based on lineup data
-- These are the players found in the lineup_innings for U15 Selects team lineups

-- U15 Selects team ID: 079f5fc4-1f41-49fa-a016-4741d319a2f0
-- User ID: a35e5ffc-**************-9342e7d36d07

-- Create players for U15 Selects team
-- Based on the lineup data, these players should be in U15 Selects:

INSERT INTO players (
    id,
    name,
    team_id,
    user_id,
    pitcher_restriction,
    catcher_restriction,
    first_base_restriction,
    other_restriction,
    position_preferences,
    created_at,
    updated_at
) VALUES 
-- <PERSON><PERSON>
(
    gen_random_uuid(),
    'Evelynn',
    '079f5fc4-1f41-49fa-a016-4741d319a2f0',
    'a35e5ffc-**************-9342e7d36d07',
    false,
    false,
    false,
    null,
    '{}',
    now(),
    now()
),
-- <PERSON>
(
    gen_random_uuid(),
    '<PERSON>',
    '079f5fc4-1f41-49fa-a016-4741d319a2f0',
    'a35e5ffc-**************-9342e7d36d07',
    false,
    false,
    false,
    null,
    '{}',
    now(),
    now()
),
-- Mikayla
(
    gen_random_uuid(),
    'Mikayla',
    '079f5fc4-1f41-49fa-a016-4741d319a2f0',
    'a35e5ffc-**************-9342e7d36d07',
    false,
    false,
    false,
    null,
    '{}',
    now(),
    now()
),
-- Kenzie
(
    gen_random_uuid(),
    'Kenzie',
    '079f5fc4-1f41-49fa-a016-4741d319a2f0',
    'a35e5ffc-**************-9342e7d36d07',
    false,
    false,
    false,
    null,
    '{}',
    now(),
    now()
),
-- Presley
(
    gen_random_uuid(),
    'Presley',
    '079f5fc4-1f41-49fa-a016-4741d319a2f0',
    'a35e5ffc-**************-9342e7d36d07',
    false,
    false,
    false,
    null,
    '{}',
    now(),
    now()
),
-- Avalon
(
    gen_random_uuid(),
    'Avalon',
    '079f5fc4-1f41-49fa-a016-4741d319a2f0',
    'a35e5ffc-**************-9342e7d36d07',
    false,
    false,
    false,
    null,
    '{}',
    now(),
    now()
),
-- Charlotte
(
    gen_random_uuid(),
    'Charlotte',
    '079f5fc4-1f41-49fa-a016-4741d319a2f0',
    'a35e5ffc-**************-9342e7d36d07',
    false,
    false,
    false,
    null,
    '{}',
    now(),
    now()
),
-- Avery
(
    gen_random_uuid(),
    'Avery',
    '079f5fc4-1f41-49fa-a016-4741d319a2f0',
    'a35e5ffc-**************-9342e7d36d07',
    false,
    false,
    false,
    null,
    '{}',
    now(),
    now()
),
-- Morgan
(
    gen_random_uuid(),
    'Morgan',
    '079f5fc4-1f41-49fa-a016-4741d319a2f0',
    'a35e5ffc-**************-9342e7d36d07',
    false,
    false,
    false,
    null,
    '{}',
    now(),
    now()
),
-- Grace
(
    gen_random_uuid(),
    'Grace',
    '079f5fc4-1f41-49fa-a016-4741d319a2f0',
    'a35e5ffc-**************-9342e7d36d07',
    false,
    false,
    false,
    null,
    '{}',
    now(),
    now()
);

-- Verify the restoration
SELECT 
    t.name as team_name,
    COUNT(p.id) as player_count,
    STRING_AGG(p.name, ', ' ORDER BY p.name) as players
FROM teams t 
LEFT JOIN players p ON t.id = p.team_id 
WHERE t.user_id = 'a35e5ffc-**************-9342e7d36d07'
GROUP BY t.id, t.name
ORDER BY t.name;

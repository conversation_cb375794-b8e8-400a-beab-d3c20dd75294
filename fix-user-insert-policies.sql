-- Fix RLS policies to allow users to create their own profiles and initial data

-- 1. Fix profiles table INSERT policy
DROP POLICY IF EXISTS "profiles_insert" ON public.profiles;

CREATE POLICY "profiles_insert" ON public.profiles
  FOR INSERT WITH CHECK (
    auth.uid()::text = id
    OR auth.uid() IS NOT NULL  -- Allow any authenticated user to create their profile
  );

-- 2. Fix subscriptions table policies
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DO $$
DECLARE
  policy record;
BEGIN
  FOR policy IN 
    SELECT policyname 
    FROM pg_policies 
    WHERE schemaname = 'public' AND tablename = 'subscriptions'
  LOOP
    EXECUTE format('DROP POLICY IF EXISTS %I ON public.subscriptions', policy.policyname);
  END LOOP;
END $$;

-- Create new policies for subscriptions
CREATE POLICY "subscriptions_select" ON public.subscriptions
  FOR SELECT USING (
    auth.uid() = user_id
    OR auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

CREATE POLICY "subscriptions_insert" ON public.subscriptions
  FOR INSERT WITH CHECK (
    auth.uid() = user_id  -- Users can only create their own subscription
  );

CREATE POLICY "subscriptions_update" ON public.subscriptions
  FOR UPDATE USING (
    auth.uid() = user_id
    OR auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

CREATE POLICY "subscriptions_delete" ON public.subscriptions
  FOR DELETE USING (
    auth.uid() = user_id
    OR auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

-- 3. Ensure teams INSERT policy allows authenticated users
DROP POLICY IF EXISTS "teams_insert" ON public.teams;

CREATE POLICY "teams_insert" ON public.teams
  FOR INSERT WITH CHECK (
    auth.uid() = user_id  -- Users can only create teams for themselves
  );

-- 4. Grant necessary permissions
GRANT ALL ON public.profiles TO authenticated;
GRANT ALL ON public.subscriptions TO authenticated;
GRANT ALL ON public.teams TO authenticated;

-- 5. Verify the policies
SELECT 
  tablename,
  policyname,
  cmd,
  with_check
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename IN ('profiles', 'subscriptions', 'teams')
  AND cmd = 'INSERT'
ORDER BY tablename, policyname;
-- Manual user setup when <PERSON><PERSON> is being problematic

-- Step 1: Find users without profiles
SELECT 
  u.id,
  u.email,
  u.created_at,
  CASE WHEN p.id IS NULL THEN 'NO PROFILE' ELSE 'HAS PROFILE' END as profile_status,
  CASE WHEN s.user_id IS NULL THEN 'NO SUBSCRIPTION' ELSE 'HAS SUBSCRIPTION' END as sub_status
FROM auth.users u
LEFT JOIN public.profiles p ON p.id = u.id::text
LEFT JOIN public.subscriptions s ON s.user_id = u.id
ORDER BY u.created_at DESC;

-- Step 2: Create profiles for users who don't have them
INSERT INTO public.profiles (id, email, full_name, role, is_admin)
SELECT 
  u.id::text,
  u.email,
  COALESCE(u.raw_user_meta_data->>'full_name', u.email),
  'user',
  false
FROM auth.users u
LEFT JOIN public.profiles p ON p.id = u.id::text
WHERE p.id IS NULL;

-- Step 3: Create subscriptions for users who don't have them
INSERT INTO public.subscriptions (user_id, is_paid, tier, team_limit)
SELECT 
  u.id,
  false,
  'starter',
  0
FROM auth.users u
LEFT JOIN public.subscriptions s ON s.user_id = u.id
WHERE s.user_id IS NULL;

-- Step 4: Verify everyone has profiles and subscriptions now
SELECT 
  COUNT(DISTINCT u.id) as total_users,
  COUNT(DISTINCT p.id) as users_with_profiles,
  COUNT(DISTINCT s.user_id) as users_with_subscriptions
FROM auth.users u
LEFT JOIN public.profiles p ON p.id = u.id::text
LEFT JOIN public.subscriptions s ON s.user_id = u.id;
-- EMERGENCY FIX: Allow users to sign up and create data

-- 1. First, check what policies currently exist
SELECT 
  tablename,
  policyname,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename IN ('profiles', 'subscriptions', 'teams')
ORDER BY tablename, cmd, policyname;

-- 2. Drop ALL existing policies on these tables
DO $$
DECLARE
  rec record;
BEGIN
  -- Drop all policies on profiles
  FOR rec IN SELECT policyname FROM pg_policies WHERE schemaname = 'public' AND tablename = 'profiles'
  LOOP
    EXECUTE format('DROP POLICY IF EXISTS %I ON public.profiles', rec.policyname);
  END LOOP;
  
  -- Drop all policies on subscriptions
  FOR rec IN SELECT policyname FROM pg_policies WHERE schemaname = 'public' AND tablename = 'subscriptions'
  LOOP
    EXECUTE format('DROP POLICY IF EXISTS %I ON public.subscriptions', rec.policyname);
  END LOOP;
  
  -- Drop all policies on teams
  FOR rec IN SELECT policyname FROM pg_policies WHERE schemaname = 'public' AND tablename = 'teams'
  LOOP
    EXECUTE format('DROP POLICY IF EXISTS %I ON public.teams', rec.policyname);
  END LOOP;
END $$;

-- 3. Create simple, working policies for profiles
CREATE POLICY "profiles_all_authenticated" ON public.profiles
  FOR ALL USING (auth.uid() IS NOT NULL) WITH CHECK (auth.uid() IS NOT NULL);

-- 4. Create simple, working policies for subscriptions  
CREATE POLICY "subscriptions_all_authenticated" ON public.subscriptions
  FOR ALL USING (auth.uid() IS NOT NULL) WITH CHECK (auth.uid() IS NOT NULL);

-- 5. Create simple, working policies for teams
CREATE POLICY "teams_all_authenticated" ON public.teams
  FOR ALL USING (auth.uid() IS NOT NULL) WITH CHECK (auth.uid() IS NOT NULL);

-- 6. Ensure RLS is enabled
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;

-- 7. Grant all permissions
GRANT ALL ON public.profiles TO authenticated;
GRANT ALL ON public.subscriptions TO authenticated;
GRANT ALL ON public.teams TO authenticated;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- 8. Check the new policies
SELECT 
  tablename,
  policyname,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename IN ('profiles', 'subscriptions', 'teams')
ORDER BY tablename, policyname;

-- 9. Alternative: If the above still doesn't work, temporarily disable RLS
-- ONLY USE THIS AS A LAST RESORT FOR TESTING
-- ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;
-- ALTER TABLE public.subscriptions DISABLE ROW LEVEL SECURITY;
-- ALTER TABLE public.teams DISABLE ROW LEVEL SECURITY;
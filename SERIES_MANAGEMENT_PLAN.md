# Series Management Implementation Plan

## Overview
Add functionality to move games between series in the Dashboard lineup view. This allows users to reorganize games that were accidentally split into separate series.

## Current Situation
- Games are grouped by `seriesId` or by date
- Series can be renamed via inline editing
- Games can be deleted individually or as a series
- Missing: Ability to move games between series

## Implementation Plan

### 1. Add "Move to Series" Option to Game Actions

**Location**: `LineupTable.tsx` - Add to both expanded series games and single games

```tsx
// Add alongside existing View and Delete buttons
<DropdownMenu>
  <DropdownMenuTrigger asChild>
    <Button variant="ghost" size="sm">
      <MoreVertical className="w-4 h-4" />
    </Button>
  </DropdownMenuTrigger>
  <DropdownMenuContent>
    <DropdownMenuItem onClick={() => openMoveDialog(lineup)}>
      <ArrowRightLeft className="w-4 h-4 mr-2" />
      Move to Series
    </DropdownMenuItem>
  </DropdownMenuContent>
</DropdownMenu>
```

### 2. Create Move Game Dialog

```tsx
interface MoveGameDialogProps {
  game: Lineup;
  availableSeries: SeriesGroup[];
  onMove: (gameId: string, targetSeriesId: string) => void;
}

const MoveGameDialog = ({ game, availableSeries, onMove }) => {
  const [targetSeriesId, setTargetSeriesId] = useState<string>("");
  
  return (
    <Dialog>
      <DialogHeader>
        <DialogTitle>Move "{game.name}" to Series</DialogTitle>
      </DialogHeader>
      <DialogContent>
        <RadioGroup value={targetSeriesId} onValueChange={setTargetSeriesId}>
          {availableSeries.map(series => (
            <div key={series.id} className="flex items-center space-x-2">
              <RadioGroupItem value={series.id} />
              <Label>
                {series.seriesName}
                <span className="text-sm text-gray-500 ml-2">
                  ({series.lineups.length} games)
                </span>
              </Label>
            </div>
          ))}
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="new" />
            <Label>Create New Series</Label>
          </div>
        </RadioGroup>
        
        {targetSeriesId === "new" && (
          <Input 
            placeholder="New series name" 
            value={newSeriesName}
            onChange={(e) => setNewSeriesName(e.target.value)}
          />
        )}
      </DialogContent>
      <DialogFooter>
        <Button onClick={() => onMove(game.id, targetSeriesId)}>
          Move Game
        </Button>
      </DialogFooter>
    </Dialog>
  );
};
```

### 3. Database Update Logic

```typescript
async function moveGameToSeries(
  gameId: string, 
  targetSeriesId: string,
  newSeriesName?: string
) {
  try {
    // If creating new series, generate ID
    const seriesId = targetSeriesId === "new" 
      ? generateId() 
      : targetSeriesId;
    
    // Update the game's series information
    const { error } = await supabase
      .from('lineups')
      .update({
        seriesId: seriesId,
        seriesTitle: newSeriesName || undefined,
        // Reset game number - will be recalculated
        gameNumber: null
      })
      .eq('id', gameId);
    
    if (error) throw error;
    
    // Recalculate game numbers for both series
    await recalculateGameNumbers(seriesId);
    
    // If old series is now empty, optionally delete it
    await cleanupEmptySeries();
    
    toast.success('Game moved successfully');
    refreshTeamData();
  } catch (error) {
    console.error('Failed to move game:', error);
    toast.error('Failed to move game');
  }
}
```

### 4. UI/UX Enhancements

#### Option 1: Quick Actions Menu (Recommended for MVP)
- Add three-dot menu to each game row
- Contains: "Move to Series", "Edit Details", "Delete"
- Simple dropdown selection for target series

#### Option 2: Drag and Drop (Future Enhancement)
- Make game cards draggable
- Visual drop zones on series
- Preview of where game will be placed
- Undo/redo support

#### Option 3: Bulk Selection Mode
- Checkbox mode for selecting multiple games
- Action bar appears with bulk operations
- "Move Selected" button opens dialog

### 5. Edge Cases to Handle

1. **Moving last game from series**: 
   - Prompt to delete empty series
   - Or convert to single game

2. **Moving to full series**:
   - Check max games per series limit
   - Show warning if approaching limit

3. **Date conflicts**:
   - Warn if moving to series with different date
   - Option to update game date to match series

4. **Game numbering**:
   - Automatically recalculate game numbers
   - Maintain chronological order within series

### 6. Implementation Steps

1. **Phase 1**: Basic move functionality
   - Add dropdown menu to game rows
   - Create move dialog component
   - Implement database update function
   - Test with your Milton series scenario

2. **Phase 2**: Enhanced UX
   - Add loading states
   - Implement optimistic updates
   - Add undo functionality
   - Show success/error toasts

3. **Phase 3**: Advanced features
   - Bulk move operations
   - Drag and drop interface
   - Series merge functionality
   - Split series capability

## Quick Implementation Code

Here's the minimal code needed to solve your immediate problem:

```tsx
// In LineupTable.tsx, add to imports:
import { MoreVertical, ArrowRightLeft } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// Add state for move dialog:
const [moveGameDialog, setMoveGameDialog] = useState<{
  open: boolean;
  game: Lineup | null;
}>({ open: false, game: null });

// Add move handler:
const handleMoveGame = async (gameId: string, targetSeriesId: string) => {
  try {
    const { error } = await supabase
      .from('lineups')
      .update({ seriesId: targetSeriesId })
      .eq('id', gameId);
    
    if (error) throw error;
    
    toast.success('Game moved successfully');
    refreshTeamData();
    setMoveGameDialog({ open: false, game: null });
  } catch (error) {
    toast.error('Failed to move game');
  }
};

// Add dropdown to game actions (around line 640):
<DropdownMenu>
  <DropdownMenuTrigger asChild>
    <Button variant="ghost" size="sm">
      <MoreVertical className="w-4 h-4" />
    </Button>
  </DropdownMenuTrigger>
  <DropdownMenuContent>
    <DropdownMenuItem 
      onClick={() => setMoveGameDialog({ open: true, game: lineup })}
    >
      <ArrowRightLeft className="w-4 h-4 mr-2" />
      Move to Series
    </DropdownMenuItem>
  </DropdownMenuContent>
</DropdownMenu>
```

This gives you a working solution to move games between series with minimal code changes.
-- <PERSON>ript to reset player statistics by clearing the player_position_history table
-- Run this in the Supabase SQL editor to reset stats

-- Option 1: Delete ALL player position history (use with caution!)
-- Uncomment the line below to delete all stats for all users
-- DELETE FROM player_position_history;

-- Option 2: Delete stats for a specific user
-- Replace 'YOUR_USER_ID' with the actual user ID
-- DELETE FROM player_position_history WHERE user_id = 'YOUR_USER_ID';

-- Option 3: Delete stats for a specific team
-- Replace 'YOUR_TEAM_ID' with the actual team ID
-- DELETE FROM player_position_history 
-- WHERE player_id IN (
--   SELECT id FROM players WHERE team_id = 'YOUR_TEAM_ID'
-- );

-- Option 4: Delete stats older than a certain date
-- DELETE FROM player_position_history WHERE game_date < '2025-01-01';

-- Option 5: Delete stats for specific players
-- Replace the player IDs with actual values
-- DELETE FROM player_position_history 
-- WHERE player_id IN ('player_id_1', 'player_id_2', 'player_id_3');

-- Verification queries to check the data before/after deletion:

-- Count all position history records
SELECT COUNT(*) as total_records FROM player_position_history;

-- Count records by user
SELECT 
  user_id,
  COUNT(*) as record_count
FROM player_position_history
GROUP BY user_id
ORDER BY record_count DESC;

-- Count records by team
SELECT 
  t.name as team_name,
  t.id as team_id,
  COUNT(DISTINCT p.id) as player_count,
  COUNT(pph.id) as history_records
FROM teams t
LEFT JOIN players p ON p.team_id = t.id
LEFT JOIN player_position_history pph ON pph.player_id = p.id
GROUP BY t.id, t.name
ORDER BY history_records DESC;

-- See sample of position history data
SELECT 
  pph.*,
  p.name as player_name,
  t.name as team_name
FROM player_position_history pph
JOIN players p ON p.id = pph.player_id
JOIN teams t ON t.id = p.team_id
ORDER BY pph.game_date DESC
LIMIT 20;
{"permissions": {"allow": ["Bash(grep:*)", "Bash(npx supabase db:*)", "Bash(npm run dev:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(node:*)", "Bash(npm test:*)", "Bash(rm:*)", "Bash(git add:*)", "Bash(rg:*)", "Bash(brew install:*)", "Bash(gh auth login:*)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 40 \"export function canPlayPosition\" /Users/<USER>/Apps/diamond-lineup-guru/src/lib/utils-enhanced.ts)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"positionRestrictions\" /Users/<USER>/Apps/diamond-lineup-guru/src/lib/utils-enhanced.ts)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -B 3 -A 3 \"positionRestrictions\" /Users/<USER>/Apps/diamond-lineup-guru/src/lib/utils-enhanced.ts)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrip/arm64-darwin/rg -n \"positionRestrictions\" /Users/<USER>/Apps/diamond-lineup-guru/src/lib/utils-enhanced.ts)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"positionRestrictions\" /Users/<USER>/Apps/diamond-lineup-guru/src/lib/utils-enhanced.ts)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 30 \"export function canPlayPosition\" /Users/<USER>/Apps/diamond-lineup-guru/src/lib/utils-enhanced.ts)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 15 \"export function checkPositionRestriction\" /Users/<USER>/Apps/diamond-lineup-guru/src/lib/utils-enhanced.ts)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 10 -B 5 \"canPlayPosition\" /Users/<USER>/Apps/diamond-lineup-guru/src/lib/improvedRotation.ts)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"positionRestrictions\" /Users/<USER>/Apps/diamond-lineup-guru/src/lib/)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg \"from.*utils-enhanced\" /Users/<USER>/Apps/diamond-lineup-guru/src/)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -B 2 -A 2 \"canPlayPosition\" /Users/<USER>/Apps/diamond-lineup-guru/src/pages/SetFirstInning.tsx)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"canPlayPosition\" /Users/<USER>/Apps/diamond-lineup-guru/src/lib/utils-enhanced.ts -A 10 -B 5)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n -A 20 \"assignPosition|findBestPlayer|getBestPlayerForPosition\" /Users/<USER>/Apps/diamond-lineup-guru/src/lib/utils-enhanced.ts)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"ConstraintSolver\" /Users/<USER>/Apps/diamond-lineup-guru/src/lib/utils-enhanced.ts -A 10 -B 5)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"getPositionKey\" /Users/<USER>/Apps/diamond-lineup-guru/src/lib/utils-enhanced.ts -A 10 -B 5)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"primary.*fill-in.*emergency\" /Users/<USER>/Apps/diamond-lineup-guru/src/lib/utils-enhanced.ts -A 5 -B 5)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"PRIMARY.*IN THE MIX.*EMERGENCY\" /Users/<USER>/Apps/diamond-lineup-guru/src/lib/utils-enhanced.ts -A 10 -B 5)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"teamRole.*switch\" /Users/<USER>/Apps/diamond-lineup-guru/src/lib/utils-enhanced.ts -A 15 -B 5)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"pitcherStrategy\" /Users/<USER>/Apps/diamond-lineup-guru/src/lib/utils-enhanced.ts -A 5 -B 5)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"pitcher.*rotation\" /Users/<USER>/Apps/diamond-lineup-guru/src/lib/utils-enhanced.ts -A 3 -B 3)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"shouldRotatePitcher\" /Users/<USER>/Apps/diamond-lineup-guru/src/lib/utils-enhanced.ts -A 10 -B 5)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"generateCompleteLineup\" /Users/<USER>/Apps/diamond-lineup-guru/src/lib/utils-enhanced.ts -A 15 -B 5)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"Emergency assignment\" /Users/<USER>/Apps/diamond-lineup-guru/src/lib/utils-enhanced.ts -A 3 -B 3)", "Bash(npm run test:*)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"updatePlayer.*=.*async\" /Users/<USER>/Apps/diamond-lineup-guru/src/contexts/TeamContext.tsx -A 20)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"updatePlayer.*async\" /Users/<USER>/Apps/diamond-lineup-guru/src/services/teamService.ts -A 25)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"onStrategyChange\" /Users/<USER>/Apps/diamond-lineup-guru/src/components/PitcherStrategyManager.tsx -A 5 -B 5)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"PitcherStrategyManager\" /Users/<USER>/Apps/diamond-lineup-guru/src/pages/PitcherSettings.tsx -A 10 -B 5)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"planFieldRotations|shouldRotate\" /Users/<USER>/Apps/diamond-lineup-guru/src/lib/utils-enhanced.ts -A 15 -B 5)", "<PERSON><PERSON>(mv:*)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 8 \"onClick={handleOptimizeLineup}\" /Users/<USER>/Apps/diamond-lineup-guru/src/pages/ViewLineup.tsx)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 8 -F \"onClick={handleOptimizeLineup}\" /Users/<USER>/Apps/diamond-lineup-guru/src/pages/ViewLineup.tsx)", "Bash(/Users/<USER>/.claude/local/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 8 -F \"onClick={handleRegenerateOptimalLineup}\" /Users/<USER>/Apps/diamond-lineup-guru/src/pages/ViewLineup.tsx)", "Bash(npm run lint)", "Bash(psql:*)", "Bash(find:*)", "<PERSON><PERSON>(cat:*)", "Bash(npm install:*)", "Bash(ls:*)", "<PERSON><PERSON>(realpath:*)", "<PERSON><PERSON>(chmod:*)", "mcp__gemini-collab__server_info", "Bash(cp:*)", "Bash(git reset:*)", "Bash(git commit:*)"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["supabase"]}
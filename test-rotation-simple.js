// Simple test to check rotation balance

// We'll create the data directly instead of importing

const players = [];
for (let i = 1; i <= 12; i++) {
  players.push({ id: i.toString(), name: `P${i}`, teamRoles: {} });
}

const rules = {
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 2,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  respectPositionLockouts: false,
  equalPlayingTime: true,
  rotateLineupEvery: 1,
  rotatePitcherEvery: 2
};

console.log('Testing rotation with 12 players, 10 innings...\n');

try {
  const lineup = generateOptimalLineup(players, 10, rules);
  
  // Count playing time
  const stats = new Map();
  players.forEach(p => stats.set(p.name, 0));
  
  lineup.forEach(inning => {
    ['pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'].forEach(pos => {
      const player = inning.positions[pos];
      if (player) stats.set(player, (stats.get(player) || 0) + 1);
    });
  });
  
  const times = Array.from(stats.values()).sort((a, b) => b - a);
  console.log('Field innings distribution:', times);
  console.log('Range:', times[0] - times[times.length - 1]);
  console.log('Ideal:', (10 * 9 / 12).toFixed(1));
  
} catch (err) {
  console.error('Error:', err.message);
}
-- Check current RLS policies
\echo '\n=== Current RLS Policies ===\n'

SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies
WHERE schemaname = 'public' 
AND tablename IN ('profiles', 'subscriptions', 'teams')
ORDER BY tablename, policyname;

-- Check if R<PERSON> is enabled on tables
\echo '\n=== RLS Status on Tables ===\n'

SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables
WHERE schemaname = 'public'
AND tablename IN ('profiles', 'subscriptions', 'teams');

-- Fix missing INSERT policies for subscriptions
\echo '\n=== Adding Missing RLS Policies ===\n'

-- Ensure RLS is enabled
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist and recreate them
DO $$ 
BEGIN
    -- Profiles policies
    DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
    DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
    DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
    
    CREATE POLICY "Users can view own profile" ON profiles
        FOR SELECT USING (auth.uid() = id);
    
    CREATE POLICY "Users can insert own profile" ON profiles
        FOR INSERT WITH CHECK (auth.uid() = id);
    
    CREATE POLICY "Users can update own profile" ON profiles
        FOR UPDATE USING (auth.uid() = id);

    -- Subscriptions policies
    DROP POLICY IF EXISTS "Users can view own subscriptions" ON subscriptions;
    DROP POLICY IF EXISTS "Users can insert own subscriptions" ON subscriptions;
    DROP POLICY IF EXISTS "Users can update own subscriptions" ON subscriptions;
    
    CREATE POLICY "Users can view own subscriptions" ON subscriptions
        FOR SELECT USING (auth.uid() = user_id);
    
    CREATE POLICY "Users can insert own subscriptions" ON subscriptions
        FOR INSERT WITH CHECK (auth.uid() = user_id);
    
    CREATE POLICY "Users can update own subscriptions" ON subscriptions
        FOR UPDATE USING (auth.uid() = user_id);

    -- Teams policies
    DROP POLICY IF EXISTS "Users can view own teams" ON teams;
    DROP POLICY IF EXISTS "Users can insert own teams" ON teams;
    DROP POLICY IF EXISTS "Users can update own teams" ON teams;
    DROP POLICY IF EXISTS "Users can delete own teams" ON teams;
    
    CREATE POLICY "Users can view own teams" ON teams
        FOR SELECT USING (auth.uid() = user_id);
    
    CREATE POLICY "Users can insert own teams" ON teams
        FOR INSERT WITH CHECK (auth.uid() = user_id);
    
    CREATE POLICY "Users can update own teams" ON teams
        FOR UPDATE USING (auth.uid() = user_id);
    
    CREATE POLICY "Users can delete own teams" ON teams
        FOR DELETE USING (auth.uid() = user_id);

    RAISE NOTICE 'RLS policies created successfully';
END $$;

-- Verify the policies were created
\echo '\n=== Verifying New Policies ===\n'

SELECT 
    tablename,
    policyname,
    cmd,
    qual
FROM pg_policies
WHERE schemaname = 'public' 
AND tablename IN ('profiles', 'subscriptions', 'teams')
ORDER BY tablename, policyname;
# 🚨 ROTATION BUG ANALYSIS: Duplicate Assignment Error

## <PERSON>rro<PERSON>
```
Duplicate player assignment: <PERSON> assigned to both pitcher and pitcher
```

## Root Cause Analysis

The bug occurs when **the same player gets assigned to the same position twice** during the rotation planning process. Here's the exact sequence:

### Step 1: Initial State (Inning 1)
```javascript
current.positions = {
  pitcher: '<PERSON>',
  catcher: '<PERSON>',
  // ... other positions
}
```

### Step 2: Rotation Planning (Inning 2)
The `LineupRotator.createRotationPlan()` method runs these steps:

1. **Pitcher Rotation Check** (line 925):
   ```javascript
   // This sets <PERSON> as pitcher in the plan
   plan.fieldAssignments.set('pitcher', current.positions.pitcher);
   // Result: plan.fieldAssignments = { pitcher: '<PERSON>' }
   ```

2. **Constraint Solver Called** (line 1679):
   ```javascript
   // The constraint solver tries to assign remaining positions
   // But it ALSO tries to assign pitcher position again!
   const assignments = solver.findValidAssignment([
     'pitcher', 'catcher', 'firstBase', // etc - ALL positions including pitcher!
   ]);
   // Result: assignments = { pitcher: '<PERSON>', catcher: '<PERSON>', etc }
   ```

3. **Conflict Detection** (line 1770):
   ```javascript
   // When merging assignments, it finds <PERSON> in both places:
   // plan.fieldAssignments.has('pitcher') = true (Alex Thompson)
   // assignments.has('pitcher') = true (Alex Thompson) 
   // Even though it's the same player, the code sees this as a conflict!
   ```

## The Specific Problem

The issue occurs when:
1. **Pitcher rotation is NOT scheduled** (e.g., `rotatePitcherEvery: 4` but we're only at inning 2)
2. The system keeps the current pitcher: `plan.fieldAssignments.set('pitcher', currentPitcher)`
3. The constraint solver is called with ALL positions (including pitcher)
4. The constraint solver assigns the same pitcher to pitcher position
5. The merge logic detects "Alex assigned to pitcher" + "Alex assigned to pitcher" = DUPLICATE!

## User Configuration That Triggers This

Here's what a user would set that causes the problem:

```javascript
const problematicRules = {
  rotateLineupEvery: 2,      // Rotate lineup every 2 innings
  rotatePitcherEvery: 4,     // BUT pitcher rotates every 4 innings
  competitiveMode: true,
  // ... other settings
}
```

### Timeline:
- **Inning 1**: Alex Thompson pitches
- **Inning 2**: Lineup rotation triggered, but NOT pitcher rotation
- **BUG**: System tries to keep Alex as pitcher AND reassign all positions including pitcher
- **Result**: "Alex assigned to both pitcher and pitcher"

## Why This Happens in Competitive Mode

In competitive mode with star players:
1. Alex Thompson (5-rated pitcher, star player) gets a massive competitive score (330+ points)
2. The constraint solver will ALWAYS choose Alex for pitcher when given the chance
3. So when the constraint solver runs on ALL positions, it picks Alex for pitcher
4. But Alex was already assigned to pitcher by the rotation logic
5. = Duplicate assignment error

## Fix Required

The constraint solver should only work on **unassigned positions**, not positions that are already locked in by rotation rules.

### Current (Broken) Logic:
```javascript
// Keep current pitcher (line 925)
plan.fieldAssignments.set('pitcher', currentPitcher);

// Then call constraint solver on ALL positions (line 1679)
const assignments = solver.findValidAssignment([
  'pitcher', 'catcher', 'firstBase', // ❌ Includes pitcher!
]);
```

### Required Fix:
```javascript
// Keep current pitcher
plan.fieldAssignments.set('pitcher', currentPitcher);

// Only solve for unassigned positions
const unassignedPositions = ALL_POSITIONS.filter(pos => 
  !plan.fieldAssignments.has(pos)
);
const assignments = solver.findValidAssignment(unassignedPositions);
```

## Test Scenario That Reproduces Bug

```javascript
const testPlayers = [/* 9-14 players with ratings */];
const rules = {
  rotateLineupEvery: 2,    // Frequent lineup rotation
  rotatePitcherEvery: 4,   // Less frequent pitcher rotation  
  competitiveMode: true    // Uses constraint solver
};

// This will fail on inning 2 or 3
const lineup = generateCompleteLineup(testPlayers, 5, rules);
```

The bug is triggered by any combination where:
- `rotateLineupEvery` < `rotatePitcherEvery` 
- Competitive mode is enabled
- Multiple innings are generated
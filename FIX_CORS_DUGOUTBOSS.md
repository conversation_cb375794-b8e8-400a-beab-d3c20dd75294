# Fix CORS Error for dugoutboss.com

## The Problem
Your Supabase project (`mhuuptkgohuztjrovpxz`) doesn't have `dugoutboss.com` configured as an allowed origin for authentication, causing CORS errors when users try to login.

## The Solution

### Step 1: Login to Supabase Dashboard
1. Go to https://app.supabase.com
2. Sign in with your Supabase account

### Step 2: Select Your Project
Look for the project with URL: `https://mhuuptkgohuztjrovpxz.supabase.co`

### Step 3: Configure Authentication URLs
1. In the left sidebar, click on **Authentication**
2. Click on **URL Configuration**
3. Update these fields:

#### Site URL
Set this to your production domain:
```
https://dugoutboss.com
```

#### Redirect URLs
Add ALL of these URLs (one per line):
```
https://dugoutboss.com
https://dugoutboss.com/*
https://www.dugoutboss.com
https://www.dugoutboss.com/*
http://localhost:5173
http://localhost:5173/*
http://localhost:3000
http://localhost:3000/*
```

### Step 4: Save Changes
Click the "Save" button at the bottom of the page.

## Why This Happened
The CORS error occurs because Supabase's authentication service checks if the requesting domain is in the allowed list. Since `dugoutboss.com` wasn't configured, it blocks the requests.

## Verification
After saving:
1. Clear your browser cache (Cmd+Shift+R on Mac)
2. Go to https://dugoutboss.com/sign-in
3. Try logging in
4. The CORS error should be gone

## Additional Checks

### Edge Functions CORS (if using payment features)
If you're using Edge Functions for payments, also check:
1. `supabase/functions/_shared/cors.ts`
2. Ensure it includes `dugoutboss.com` in the allowed origins
3. Redeploy edge functions if needed

### Environment Variables in Production
Verify your hosting platform has the correct environment variables:
- `VITE_SUPABASE_URL=https://mhuuptkgohuztjrovpxz.supabase.co`
- `VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1odXVwdGtnb2h1enRqcm92cHh6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY0NzU0NDEsImV4cCI6MjA2MjA1MTQ0MX0.aGUpltDKIHYJECOuFHeES7VJp7RKlMjArSg7NxFai_k`

## If Issues Persist
1. Check browser console for specific error messages
2. Verify the Supabase project ID matches everywhere
3. Check if there are any firewall or security rules blocking requests
4. Try in an incognito window to rule out cache issues
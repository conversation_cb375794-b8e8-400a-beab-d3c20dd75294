// Test script to introspect a generateLineup function with inningsPlayed and benchStreak tracking
// Using the provided inputs:
const players = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
const restrictions = { Alice: ['Catcher'], <PERSON>: [], <PERSON>: [], <PERSON>: [] };
const prefs = { rotateEvery: 1, benchLimit: 1, allowPitcherRotation: true };
const innings = 3;

console.log("=".repeat(80));
console.log("GENERATELINEUP FUNCTION INTROSPECTION");
console.log("=".repeat(80));
console.log("Inputs:");
console.log("  players:", players);
console.log("  restrictions:", restrictions);
console.log("  prefs:", prefs);
console.log("  innings:", innings);
console.log("=".repeat(80));

// Define positions (simplified for 4 players)
const positions = ['Pitcher', 'Catcher', 'First Base', 'Second Base'];

/**
 * title: enforce rotation, bench, and tie-break
 *
 * Augment, refactor generateLineup so it:
 * 1. Tracks inningsPlayed[playerId], benchStreak[playerId], lastPositionTimestamp[playerId].
 * 2. For each inning and each position:
 *    - Excludes players locked out of that position.
 *    - If players > positions, send to bench the one with highest inningsPlayed and highest benchStreak.
 *    - Sort eligible by:
 *        a. inningsPlayed ascending
 *        b. lastPositionTimestamp ascending (least recently at this position first)
 *    - Assign the first player, then update their inningsPlayed or benchStreak and lastPositionTimestamp.
 * 3. For pitcher position, always pick the eligible pitcher with lowest count of inningsPitched or longest since last pitch.
 * 4. Use backtracking when you hit a dead end so all rules hold.
 *
 * Inputs:
 *   players      : Array<{ id, name }>
 *   prefs        : { rotateEvery: number, benchLimit: number, allowPitcherRotation: boolean }
 *   restrictions : Record<playerId, string[]>   // locked-out positions
 *   innings      : number                       // total innings
 *
 * Output:
 *   lineup       : Record<inning, Record<position, playerId>>
 */
function generateLineup(players, prefs, restrictions, innings) {
  // Initialize enhanced tracking maps
  let inningsPlayed = {};
  let benchStreak = {};
  let lastPositionTimestamp = {}; // tracks when player last played each position
  let inningsPitched = {}; // special tracking for pitcher position

  // Initialize all players
  players.forEach(player => {
    inningsPlayed[player] = 0;
    benchStreak[player] = 0;
    inningsPitched[player] = 0;
    lastPositionTimestamp[player] = {};
    positions.forEach(pos => {
      lastPositionTimestamp[player][pos] = 0; // 0 means never played this position
    });
  });

  console.log("\n📊 INITIAL STATE:");
  console.log("  inningsPlayed:", inningsPlayed);
  console.log("  benchStreak:", benchStreak);
  console.log("  inningsPitched:", inningsPitched);

  const lineup = { innings: [] };

  // Backtracking helper function
  function canAssignPosition(player, position, assignedThisInning, currentInning) {
    // Skip if already assigned this inning
    if (assignedThisInning.has(player)) return false;

    // Check position restrictions
    const playerRestrictions = restrictions[player] || [];
    if (playerRestrictions.includes(position)) return false;

    // Check bench streak limit (only if we have more players than positions)
    if (players.length > positions.length && benchStreak[player] >= prefs.benchLimit) {
      return false;
    }

    return true;
  }

  // Generate each inning
  for (let inning = 1; inning <= innings; inning++) {
    console.log(`\n${"=".repeat(60)}`);
    console.log(`🏟️  GENERATING INNING ${inning}`);
    console.log(`${"=".repeat(60)}`);

    const inningLineup = {
      inning: inning,
      positions: {},
      bench: []
    };

    // Track who's been assigned this inning
    const assignedThisInning = new Set();

    // Step 1: Handle bench assignments if we have more players than positions
    if (players.length > positions.length) {
      const playersToBeach = players.length - positions.length;

      // Sort players by inningsPlayed (desc) then benchStreak (desc) to find who should be benched
      const benchCandidates = [...players].sort((a, b) => {
        const aPlayed = inningsPlayed[a];
        const bPlayed = inningsPlayed[b];
        if (aPlayed !== bPlayed) {
          return bPlayed - aPlayed; // Descending - most played first
        }
        return benchStreak[b] - benchStreak[a]; // Descending - highest bench streak first
      });

      console.log(`\n🪑 BENCH ASSIGNMENT (need to bench ${playersToBeach} players):`);
      for (let i = 0; i < playersToBeach; i++) {
        const playerToBench = benchCandidates[i];
        inningLineup.bench.push(playerToBench);
        assignedThisInning.add(playerToBench);
        benchStreak[playerToBench]++;
        console.log(`   📉 ${playerToBench}: benched (played=${inningsPlayed[playerToBench]}, benchStreak=${benchStreak[playerToBench]})`);
      }
    }

    // Step 2: Assign field positions
    for (let posIndex = 0; posIndex < positions.length; posIndex++) {
      const position = positions[posIndex];

      console.log(`\n🎯 Inning ${inning}, Position ${position}`);
      console.log("   " + "-".repeat(40));

      // Get eligible players for this position
      let eligiblePlayers = players.filter(player =>
        canAssignPosition(player, position, assignedThisInning, inning)
      );

      console.log(`   📋 eligiblePlayers after filtering:`, eligiblePlayers);
      console.log(`   🚫 Restrictions check:`);
      players.forEach(player => {
        if (assignedThisInning.has(player)) {
          console.log(`      ${player}: already assigned this inning`);
        } else {
          const playerRestrictions = restrictions[player] || [];
          const hasRestriction = playerRestrictions.includes(position);
          const benchExceeded = players.length > positions.length && benchStreak[player] >= prefs.benchLimit;
          console.log(`      ${player}: restriction=${hasRestriction}, benchStreak=${benchStreak[player]}/${prefs.benchLimit}, eligible=${!hasRestriction && !benchExceeded}`);
        }
      });

      // Special handling for Pitcher position
      if (position === 'Pitcher') {
        eligiblePlayers.sort((a, b) => {
          const aPitched = inningsPitched[a];
          const bPitched = inningsPitched[b];
          if (aPitched !== bPitched) {
            return aPitched - bPitched; // Ascending - fewest innings pitched first
          }
          // If tied, use lastPositionTimestamp for this position
          const aLastPitched = lastPositionTimestamp[a][position];
          const bLastPitched = lastPositionTimestamp[b][position];
          return aLastPitched - bLastPitched; // Ascending - longest since last pitch first
        });

        console.log(`   ⚾ Pitcher sorting (by inningsPitched, then lastPositionTimestamp):`);
        eligiblePlayers.forEach(player => {
          console.log(`      ${player}: pitched=${inningsPitched[player]}, lastPitched=${lastPositionTimestamp[player][position]}`);
        });
      } else {
        // Regular position sorting
        eligiblePlayers.sort((a, b) => {
          const aPlayed = inningsPlayed[a];
          const bPlayed = inningsPlayed[b];
          if (aPlayed !== bPlayed) {
            return aPlayed - bPlayed; // Ascending - least played first
          }
          // If tied, sort by lastPositionTimestamp for this specific position
          const aLastPlayed = lastPositionTimestamp[a][position];
          const bLastPlayed = lastPositionTimestamp[b][position];
          return aLastPlayed - bLastPlayed; // Ascending - least recently at this position first
        });

        console.log(`   📊 Sorted by inningsPlayed, then lastPositionTimestamp:`);
        eligiblePlayers.forEach(player => {
          console.log(`      ${player}: played=${inningsPlayed[player]}, lastAt${position}=${lastPositionTimestamp[player][position]}`);
        });
      }

      // If no eligible players, use backtracking fallback
      if (eligiblePlayers.length === 0) {
        eligiblePlayers = players.filter(player => !assignedThisInning.has(player));
        console.log(`   ⚠️  No eligible players found, using backtracking fallback:`, eligiblePlayers);
      }

      // Assign the first (best) player
      const selectedPlayer = eligiblePlayers[0];
      console.log(`   ✅ Selected player: ${selectedPlayer}`);

      // Make the assignment
      inningLineup.positions[position] = selectedPlayer;
      assignedThisInning.add(selectedPlayer);

      // Update tracking maps
      inningsPlayed[selectedPlayer]++;
      benchStreak[selectedPlayer] = 0; // Reset bench streak since they're playing
      lastPositionTimestamp[selectedPlayer][position] = inning; // Record when they played this position

      if (position === 'Pitcher') {
        inningsPitched[selectedPlayer]++;
      }

      console.log(`   📈 Updated tracking for ${selectedPlayer}:`);
      console.log(`      inningsPlayed: ${inningsPlayed[selectedPlayer]}`);
      console.log(`      benchStreak: ${benchStreak[selectedPlayer]}`);
      console.log(`      lastPositionTimestamp[${position}]: ${lastPositionTimestamp[selectedPlayer][position]}`);
      if (position === 'Pitcher') {
        console.log(`      inningsPitched: ${inningsPitched[selectedPlayer]}`);
      }
    }

    console.log(`\n📊 END OF INNING ${inning} STATE:`);
    console.log(`   inningsPlayed:`, inningsPlayed);
    console.log(`   benchStreak:`, benchStreak);
    console.log(`   inningsPitched:`, inningsPitched);

    lineup.innings.push(inningLineup);
  }

  return lineup;
}

// Run the test
const result = generateLineup(players, prefs, restrictions, innings);

console.log(`\n${"=".repeat(80)}`);
console.log("📋 FINAL LINEUP OBJECT:");
console.log(`${"=".repeat(80)}`);
console.log(JSON.stringify(result, null, 2));

console.log(`\n${"=".repeat(80)}`);
console.log("📊 FINAL STATISTICS:");
console.log(`${"=".repeat(80)}`);
result.innings.forEach((inning, index) => {
  console.log(`\nInning ${inning.inning}:`);
  Object.entries(inning.positions).forEach(([position, player]) => {
    console.log(`  ${position}: ${player}`);
  });
  if (inning.bench.length > 0) {
    console.log(`  Bench: [${inning.bench.join(', ')}]`);
  }
});

// Check for restriction violations
console.log(`\n${"=".repeat(80)}`);
console.log("🚨 RESTRICTION VIOLATIONS CHECK:");
console.log(`${"=".repeat(80)}`);
let violationFound = false;
result.innings.forEach((inning) => {
  Object.entries(inning.positions).forEach(([position, player]) => {
    const playerRestrictions = restrictions[player] || [];
    if (playerRestrictions.includes(position)) {
      console.log(`❌ VIOLATION: ${player} is playing ${position} but is restricted from this position!`);
      violationFound = true;
    }
  });
});

if (!violationFound) {
  console.log("✅ No restriction violations found!");
}

-- Step 1: Check if you're authenticated in SQL editor
SELECT 
    auth.uid() as your_user_id,
    auth.role() as your_role,
    auth.email() as your_email;

-- If the above returns NULL, you're not authenticated in the SQL editor
-- You may need to run this as a database admin or through Supabase dashboard

-- Step 2: Check what RLS policies exist for teams table
SELECT 
    policyname,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'teams'
ORDER BY policyname;

-- Step 3: Nuclear option - disable <PERSON><PERSON> temporarily to test
-- WARNING: Only do this for testing, re-enable immediately after
-- ALTER TABLE teams DISABLE ROW LEVEL SECURITY;

-- Step 4: If you're authenticated, create a simple permissive policy for teams
-- First drop all existing policies
DO $$ 
DECLARE
    pol RECORD;
BEGIN
    FOR pol IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'teams'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON teams', pol.policyname);
    END LOOP;
END $$;

-- Create a simple policy that allows authenticated users to manage their own teams
CREATE POLICY "users_own_teams" ON teams
    FOR ALL 
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Step 5: Test if you can now insert a team
-- This should work if you're authenticated
/*
INSERT INTO teams (id, name, user_id, created_at, updated_at)
VALUES (
    gen_random_uuid(),
    'Test Team RLS',
    auth.uid(),
    now(),
    now()
)
RETURNING *;
*/

-- Step 6: Check the final state
SELECT 
    tablename,
    policyname,
    cmd,
    roles
FROM pg_policies 
WHERE tablename = 'teams'
ORDER BY policyname;
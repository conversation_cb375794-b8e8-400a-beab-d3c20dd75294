# Subscriptions Table Structure

## Overview
The `subscriptions` table manages user payment and subscription tier information for Dugout Boss.

## Table Columns

Based on the migration files, the `subscriptions` table has the following columns:

| Column | Type | Default | Description |
|--------|------|---------|-------------|
| `id` | UUID | auto-generated | Primary key |
| `user_id` | UUID | - | Foreign key to auth.users(id) |
| `is_paid` | BOOLEAN | false | Whether the user has paid |
| `amount` | DECIMAL | - | Payment amount |
| `currency` | TEXT | - | Payment currency (e.g., 'USD') |
| `payment_date` | TIMESTAMPTZ | - | When payment was made |
| `tier` | TEXT | 'club' | Subscription tier: 'starter', 'coach', or 'club' |
| `price_id` | TEXT | - | Stripe price ID |
| `product_id` | TEXT | - | Stripe product ID |
| `team_limit` | INTEGER | 999 | Maximum number of teams allowed |
| `subscription_period` | TEXT | 'annual' | Subscription period type |
| `expires_at` | TIMESTAMPTZ | - | When subscription expires (NULL for lifetime) |
| `created_at` | TIMESTAMPTZ | NOW() | Record creation timestamp |
| `updated_at` | TIMESTAMPTZ | NOW() | Record update timestamp |

## Important Notes

1. **No `team_count` column**: The actual count of teams is calculated dynamically by counting records in the `teams` table. This is available through the `user_subscription_status` view.

2. **Subscription Tiers**:
   - `starter`: Limited team count (configured in team_limit)
   - `coach`: Intermediate tier
   - `club`: Premium tier (default 999 teams)

3. **The `user_subscription_status` view** provides calculated fields:
   - `teams_created`: Count of teams for the user
   - `teams_remaining`: team_limit - teams_created
   - `status`: 'lifetime', 'active', or 'expired'

## Row Level Security (RLS)

The table has RLS policies that ensure:
- Users can only view/modify their own subscription records
- Service role has full access for admin operations

## Related Functions

- `check_team_limit(user_id)`: Returns whether user can create more teams based on their subscription
- `enforce_team_limit()`: Trigger function that prevents team creation beyond limits

## Common Queries

```sql
-- Check user's subscription status
SELECT * FROM subscriptions WHERE user_id = 'user-uuid';

-- Get subscription with team count
SELECT 
    s.*,
    COUNT(t.id) as team_count
FROM subscriptions s
LEFT JOIN teams t ON t.user_id = s.user_id
WHERE s.user_id = 'user-uuid'
GROUP BY s.id;

-- Use the view for complete status
SELECT * FROM user_subscription_status WHERE user_id = 'user-uuid';
```
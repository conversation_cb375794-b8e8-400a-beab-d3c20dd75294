// This script can be run in the browser console to test the payment verification
// It will help diagnose if the issue is with the Edge Function or with the database
// Run this when logged <NAME_EMAIL>

async function testPaymentStatus() {
  console.log('🔍 Testing payment status verification...');
  
  // Get the current auth state from React context
  const authContext = window.__REACT_CONTEXT_DEVTOOLS_GLOBAL_HOOKS?.hookValues?.find(
    hook => hook.value && hook.value.user && hook.value.isPaid !== undefined
  )?.value;
  
  if (!authContext) {
    console.error('❌ Could not access the auth context. Make sure you are logged in.');
    return;
  }
  
  console.log('📊 Current auth state:', {
    user: authContext.user?.email,
    isPaid: authContext.isPaid
  });
  
  // Get the current access token from localStorage
  const supabaseData = JSON.parse(localStorage.getItem('supabase.auth.token'));
  const accessToken = supabaseData?.currentSession?.access_token;
  
  if (!accessToken) {
    console.error('❌ No access token found. Please make sure you are logged in.');
    return;
  }
  
  // Get the Supabase URL
  const supabaseUrl = localStorage.getItem('supabase.url') || 
                      document.querySelector('meta[name="supabase-url"]')?.getAttribute('content') ||
                      'https://mhuuptkgohuztjrovpxz.supabase.co';
  
  console.log('🔗 Using Supabase URL:', supabaseUrl);
  
  try {
    // Call the verify-payment Edge Function directly
    console.log('📡 Calling verify-payment Edge Function...');
    const response = await fetch(`${supabaseUrl}/functions/v1/verify-payment`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    const data = await response.json();
    
    console.log('📬 Verify Payment Response:', data);
    
    // Check if the user is marked as paid
    if (data.paid) {
      console.log('✅ User is marked as paid in the database.');
      console.log('💳 Subscription details:', data.subscription);
      
      // If we're on the pricing page but the user is paid, redirect to dashboard
      if (window.location.pathname.includes('/pricing')) {
        console.log('🔄 Redirecting to dashboard...');
        window.location.href = '/dashboard';
      }
    } else {
      console.error('❌ User is NOT marked as paid in the database.');
      console.error('❓ Error message:', data.message || data.error || 'No specific error message');
      
      // Try to manually update the auth context to mark the user as paid
      if (authContext.checkPaymentStatus) {
        console.log('🔄 Trying to manually update payment status in the app...');
        await authContext.checkPaymentStatus();
        
        console.log('📊 Updated auth state:', {
          user: authContext.user?.email,
          isPaid: authContext.isPaid
        });
      }
    }
    
    return data;
  } catch (error) {
    console.error('❌ Error testing verify-payment function:', error);
  }
}

// Execute the test
console.log('🚀 Starting payment status test...');
testPaymentStatus().then(result => {
  console.log('✨ Test completed. Final result:', result);
});

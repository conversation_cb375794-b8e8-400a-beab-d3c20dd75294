-- FIX RLS POLICIES FOR PROPER USER DATA ISOLATION
-- This will re-enable <PERSON><PERSON> with proper policies to ensure users only see their own data

-- 1. First, let's check what data each user has
SELECT 
    u.email,
    u.id as user_id,
    COUNT(DISTINCT t.id) as team_count,
    COUNT(DISTINCT p.id) as player_count,
    COUNT(DISTINCT l.id) as lineup_count
FROM auth.users u
LEFT JOIN public.teams t ON t.user_id = u.id
LEFT JOIN public.players p ON p.team_id = t.id
LEFT JOIN public.lineups l ON l.team_id = t.id
WHERE u.email IN ('<EMAIL>', '<EMAIL>')
GROUP BY u.email, u.id
ORDER BY u.email;

-- 2. Drop ALL existing policies to start fresh
DO $$ 
DECLARE 
    r RECORD;
BEGIN
    -- Only drop policies for tables that exist
    FOR r IN (SELECT tablename, policyname 
              FROM pg_policies 
              WHERE schemaname = 'public' 
              AND EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = pg_policies.tablename))
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON %I', r.policyname, r.tablename);
    END LOOP;
END $$;

-- 3. Enable RLS on tables that exist
DO $$ 
BEGIN
    -- Core tables
    IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'profiles') THEN
        ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'subscriptions') THEN
        ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'teams') THEN
        ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'players') THEN
        ALTER TABLE public.players ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'lineups') THEN
        ALTER TABLE public.lineups ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'rotation_rules') THEN
        ALTER TABLE public.rotation_rules ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'player_position_history') THEN
        ALTER TABLE public.player_position_history ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'contact_messages') THEN
        ALTER TABLE public.contact_messages ENABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'admin_audit_logs') THEN
        ALTER TABLE public.admin_audit_logs ENABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- 4. Create simple, strict policies for each table

-- PROFILES
DO $$ BEGIN
IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'profiles') THEN
    CREATE POLICY "Users can only see their own profile" ON public.profiles
        FOR SELECT USING (auth.uid() = id);
    CREATE POLICY "Users can only update their own profile" ON public.profiles
        FOR UPDATE USING (auth.uid() = id);
    CREATE POLICY "Users can only insert their own profile" ON public.profiles
        FOR INSERT WITH CHECK (auth.uid() = id);
    CREATE POLICY "Users can only delete their own profile" ON public.profiles
        FOR DELETE USING (auth.uid() = id);
END IF;
END $$;

-- SUBSCRIPTIONS
DO $$ BEGIN
IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'subscriptions') THEN
    CREATE POLICY "Users can only see their own subscription" ON public.subscriptions
        FOR SELECT USING (auth.uid() = user_id);
    CREATE POLICY "Users can only update their own subscription" ON public.subscriptions
        FOR UPDATE USING (auth.uid() = user_id);
    CREATE POLICY "Users can only insert their own subscription" ON public.subscriptions
        FOR INSERT WITH CHECK (auth.uid() = user_id);
    CREATE POLICY "Users can only delete their own subscription" ON public.subscriptions
        FOR DELETE USING (auth.uid() = user_id);
END IF;
END $$;

-- TEAMS
DO $$ BEGIN
IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'teams') THEN
    CREATE POLICY "Users can only see their own teams" ON public.teams
        FOR SELECT USING (auth.uid() = user_id);
    CREATE POLICY "Users can only update their own teams" ON public.teams
        FOR UPDATE USING (auth.uid() = user_id);
    CREATE POLICY "Users can only insert their own teams" ON public.teams
        FOR INSERT WITH CHECK (auth.uid() = user_id);
    CREATE POLICY "Users can only delete their own teams" ON public.teams
        FOR DELETE USING (auth.uid() = user_id);
END IF;
END $$;

-- PLAYERS (must belong to user's team)
DO $$ BEGIN
IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'players') THEN
    CREATE POLICY "Users can only see players on their teams" ON public.players
        FOR SELECT USING (EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = players.team_id 
            AND teams.user_id = auth.uid()
        ));
    CREATE POLICY "Users can only update players on their teams" ON public.players
        FOR UPDATE USING (EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = players.team_id 
            AND teams.user_id = auth.uid()
        ));
    CREATE POLICY "Users can only insert players on their teams" ON public.players
        FOR INSERT WITH CHECK (EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = players.team_id 
            AND teams.user_id = auth.uid()
        ));
    CREATE POLICY "Users can only delete players on their teams" ON public.players
        FOR DELETE USING (EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = players.team_id 
            AND teams.user_id = auth.uid()
        ));
END IF;
END $$;

-- LINEUPS (must belong to user's team)
DO $$ BEGIN
IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'lineups') THEN
    CREATE POLICY "Users can only see lineups for their teams" ON public.lineups
        FOR SELECT USING (EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = lineups.team_id 
            AND teams.user_id = auth.uid()
        ));
    CREATE POLICY "Users can only update lineups for their teams" ON public.lineups
        FOR UPDATE USING (EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = lineups.team_id 
            AND teams.user_id = auth.uid()
        ));
    CREATE POLICY "Users can only insert lineups for their teams" ON public.lineups
        FOR INSERT WITH CHECK (EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = lineups.team_id 
            AND teams.user_id = auth.uid()
        ));
    CREATE POLICY "Users can only delete lineups for their teams" ON public.lineups
        FOR DELETE USING (EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = lineups.team_id 
            AND teams.user_id = auth.uid()
        ));
END IF;
END $$;

-- ROTATION_RULES (must belong to user's team)
DO $$ BEGIN
IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'rotation_rules') THEN
    CREATE POLICY "Users can only see rotation rules for their teams" ON public.rotation_rules
        FOR SELECT USING (EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = rotation_rules.team_id 
            AND teams.user_id = auth.uid()
        ));
    CREATE POLICY "Users can only update rotation rules for their teams" ON public.rotation_rules
        FOR UPDATE USING (EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = rotation_rules.team_id 
            AND teams.user_id = auth.uid()
        ));
    CREATE POLICY "Users can only insert rotation rules for their teams" ON public.rotation_rules
        FOR INSERT WITH CHECK (EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = rotation_rules.team_id 
            AND teams.user_id = auth.uid()
        ));
    CREATE POLICY "Users can only delete rotation rules for their teams" ON public.rotation_rules
        FOR DELETE USING (EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = rotation_rules.team_id 
            AND teams.user_id = auth.uid()
        ));
END IF;
END $$;

-- PLAYER_POSITION_HISTORY (must belong to user's player)
DO $$ BEGIN
IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'player_position_history') THEN
    CREATE POLICY "Users can only see position history for their players" ON public.player_position_history
        FOR SELECT USING (EXISTS (
            SELECT 1 FROM public.players p
            JOIN public.teams t ON t.id = p.team_id
            WHERE p.id = player_position_history.player_id 
            AND t.user_id = auth.uid()
        ));
    CREATE POLICY "Users can only update position history for their players" ON public.player_position_history
        FOR UPDATE USING (EXISTS (
            SELECT 1 FROM public.players p
            JOIN public.teams t ON t.id = p.team_id
            WHERE p.id = player_position_history.player_id 
            AND t.user_id = auth.uid()
        ));
    CREATE POLICY "Users can only insert position history for their players" ON public.player_position_history
        FOR INSERT WITH CHECK (EXISTS (
            SELECT 1 FROM public.players p
            JOIN public.teams t ON t.id = p.team_id
            WHERE p.id = player_position_history.player_id 
            AND t.user_id = auth.uid()
        ));
    CREATE POLICY "Users can only delete position history for their players" ON public.player_position_history
        FOR DELETE USING (EXISTS (
            SELECT 1 FROM public.players p
            JOIN public.teams t ON t.id = p.team_id
            WHERE p.id = player_position_history.player_id 
            AND t.user_id = auth.uid()
        ));
END IF;
END $$;

-- CONTACT_MESSAGES (users can only see/manage their own messages)
DO $$ BEGIN
IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'contact_messages') THEN
    CREATE POLICY "Users can only see their own contact messages" ON public.contact_messages
        FOR SELECT USING (auth.uid() = user_id OR user_id IS NULL);
    CREATE POLICY "Users can insert contact messages" ON public.contact_messages
        FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);
END IF;
END $$;

-- ADMIN_AUDIT_LOGS (only admins can see)
DO $$ BEGIN
IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'admin_audit_logs') THEN
    CREATE POLICY "Only admins can see audit logs" ON public.admin_audit_logs
        FOR SELECT USING (EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.is_admin = true
        ));
    CREATE POLICY "Only admins can insert audit logs" ON public.admin_audit_logs
        FOR INSERT WITH CHECK (EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.is_admin = true
        ));
END IF;
END $$;

-- 5. Special admin policies - admins can see all data
DO $$ BEGIN
-- Allow admins to see all profiles
IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'profiles') THEN
    CREATE POLICY "Admins can see all profiles" ON public.profiles
        FOR SELECT USING (EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.is_admin = true
        ));
END IF;

-- Allow admins to see all teams
IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'teams') THEN
    CREATE POLICY "Admins can see all teams" ON public.teams
        FOR SELECT USING (EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.is_admin = true
        ));
END IF;

-- Allow admins to see all subscriptions
IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'subscriptions') THEN
    CREATE POLICY "Admins can see all subscriptions" ON public.subscriptions
        FOR SELECT USING (EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.is_admin = true
        ));
END IF;
END $$;

-- 6. Verify the policies are in place
SELECT 
    tablename,
    policyname,
    cmd,
    permissive
FROM pg_policies
WHERE schemaname = 'public'
AND tablename IN ('profiles', 'subscriptions', 'teams', 'players', 'lineups')
ORDER BY tablename, policyname;

-- 7. Test that users can only see their own data
-- This should show different results for each user
SELECT 
    'Final Check' as check_type,
    u.email,
    COUNT(DISTINCT t.id) as team_count,
    COUNT(DISTINCT p.id) as player_count
FROM auth.users u
LEFT JOIN public.teams t ON t.user_id = u.id
LEFT JOIN public.players p ON p.team_id = t.id
WHERE u.email IN ('<EMAIL>', '<EMAIL>')
GROUP BY u.email
ORDER BY u.email;
-- CRITICAL FIX: Proper RLS Isolation and Authentication Bug Resolution
-- This migration addresses critical security vulnerabilities and authentication failures

-- Issue 1: Paid users redirected to payment page despite valid subscriptions
-- Issue 2: RLS policies causing data leakage between users
-- Issue 3: User data isolation not working properly
-- Issue 4: Infinite recursion in admin policies

BEGIN;

-- ===== STEP 1: CLEAN UP ALL EXISTING PROBLEMATIC POLICIES =====

-- Drop ALL existing policies to start fresh and avoid conflicts
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile" ON profiles;
DROP POLICY IF EXISTS "Allow admins to read all profiles" ON profiles;
DROP POLICY IF EXISTS "Allow admins to update all profiles" ON profiles;
DROP POLICY IF EXISTS "Allow admins to insert all profiles" ON profiles;
DROP POLICY IF EXISTS "Allow admins to delete all profiles" ON profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON profiles;

-- Teams table policies
DROP POLICY IF EXISTS "Users can view their own teams" ON teams;
DROP POLICY IF EXISTS "Users can insert their own teams" ON teams;
DROP POLICY IF EXISTS "Users can update their own teams" ON teams;
DROP POLICY IF EXISTS "Users can delete their own teams" ON teams;
DROP POLICY IF EXISTS "Allow admins to read all teams" ON teams;
DROP POLICY IF EXISTS "Allow admins to update all teams" ON teams;
DROP POLICY IF EXISTS "Allow admins to insert all teams" ON teams;
DROP POLICY IF EXISTS "Allow admins to delete all teams" ON teams;
DROP POLICY IF EXISTS "Admins can view all teams" ON teams;

-- Players table policies
DROP POLICY IF EXISTS "Users can view their own players" ON players;
DROP POLICY IF EXISTS "Users can insert their own players" ON players;
DROP POLICY IF EXISTS "Users can update their own players" ON players;
DROP POLICY IF EXISTS "Users can delete their own players" ON players;
DROP POLICY IF EXISTS "Allow admins to read all players" ON players;
DROP POLICY IF EXISTS "Allow admins to update all players" ON players;
DROP POLICY IF EXISTS "Allow admins to insert all players" ON players;
DROP POLICY IF EXISTS "Allow admins to delete all players" ON players;
DROP POLICY IF EXISTS "Admins can view all players" ON players;

-- Subscriptions table policies
DROP POLICY IF EXISTS "Users can view their own subscription" ON subscriptions;
DROP POLICY IF EXISTS "Users can view their own subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Users can insert their own subscription" ON subscriptions;
DROP POLICY IF EXISTS "Users can insert their own subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Users can update their own subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Users can delete their own subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Allow admins to view all subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Admins can view all subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Admin users can view all subscriptions" ON subscriptions;

-- Lineups table policies
DROP POLICY IF EXISTS "Users can view their own lineups" ON lineups;
DROP POLICY IF EXISTS "Users can insert their own lineups" ON lineups;
DROP POLICY IF EXISTS "Users can update their own lineups" ON lineups;
DROP POLICY IF EXISTS "Users can delete their own lineups" ON lineups;
DROP POLICY IF EXISTS "Allow admins to read all lineups" ON lineups;
DROP POLICY IF EXISTS "Allow admins to update all lineups" ON lineups;
DROP POLICY IF EXISTS "Allow admins to insert all lineups" ON lineups;
DROP POLICY IF EXISTS "Allow admins to delete all lineups" ON lineups;

-- ===== STEP 2: CREATE SECURE, NON-RECURSIVE POLICIES =====

-- PROFILES TABLE: Secure user isolation with admin access
CREATE POLICY "profiles_select_policy" ON profiles
    FOR SELECT USING (
        -- Users can see their own profile
        auth.uid() = id
        OR
        -- Admin users can see all profiles (using direct email check to avoid recursion)
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

CREATE POLICY "profiles_insert_policy" ON profiles
    FOR INSERT WITH CHECK (
        -- Users can only insert their own profile
        auth.uid() = id
        OR
        -- Admin users can insert any profile
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

CREATE POLICY "profiles_update_policy" ON profiles
    FOR UPDATE USING (
        -- Users can update their own profile
        auth.uid() = id
        OR
        -- Admin users can update any profile
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

CREATE POLICY "profiles_delete_policy" ON profiles
    FOR DELETE USING (
        -- Only admin users can delete profiles
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

-- SUBSCRIPTIONS TABLE: Critical for payment verification
CREATE POLICY "subscriptions_select_policy" ON subscriptions
    FOR SELECT USING (
        -- Users can see their own subscriptions
        auth.uid() = user_id
        OR
        -- Admin users can see all subscriptions
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

CREATE POLICY "subscriptions_insert_policy" ON subscriptions
    FOR INSERT WITH CHECK (
        -- Users can only insert their own subscriptions
        auth.uid() = user_id
        OR
        -- Admin users can insert any subscription
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

CREATE POLICY "subscriptions_update_policy" ON subscriptions
    FOR UPDATE USING (
        -- Users can update their own subscriptions
        auth.uid() = user_id
        OR
        -- Admin users can update any subscription
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

CREATE POLICY "subscriptions_delete_policy" ON subscriptions
    FOR DELETE USING (
        -- Only admin users can delete subscriptions
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

-- TEAMS TABLE: Proper user isolation
CREATE POLICY "teams_select_policy" ON teams
    FOR SELECT USING (
        -- Users can see their own teams
        auth.uid() = user_id
        OR
        -- Admin users can see all teams
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

CREATE POLICY "teams_insert_policy" ON teams
    FOR INSERT WITH CHECK (
        -- Users can only insert their own teams
        auth.uid() = user_id
        OR
        -- Admin users can insert any team
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

CREATE POLICY "teams_update_policy" ON teams
    FOR UPDATE USING (
        -- Users can update their own teams
        auth.uid() = user_id
        OR
        -- Admin users can update any team
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

CREATE POLICY "teams_delete_policy" ON teams
    FOR DELETE USING (
        -- Users can delete their own teams
        auth.uid() = user_id
        OR
        -- Admin users can delete any team
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

-- PLAYERS TABLE: Secure through team ownership
CREATE POLICY "players_select_policy" ON players
    FOR SELECT USING (
        -- Users can see players from their own teams
        auth.uid() = user_id
        OR
        EXISTS (
            SELECT 1 FROM teams t
            WHERE t.id = players.team_id
            AND t.user_id = auth.uid()
        )
        OR
        -- Admin users can see all players
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

CREATE POLICY "players_insert_policy" ON players
    FOR INSERT WITH CHECK (
        -- Users can only insert players for their own teams
        auth.uid() = user_id
        OR
        EXISTS (
            SELECT 1 FROM teams t
            WHERE t.id = players.team_id
            AND t.user_id = auth.uid()
        )
        OR
        -- Admin users can insert any player
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

CREATE POLICY "players_update_policy" ON players
    FOR UPDATE USING (
        -- Users can update players from their own teams
        auth.uid() = user_id
        OR
        EXISTS (
            SELECT 1 FROM teams t
            WHERE t.id = players.team_id
            AND t.user_id = auth.uid()
        )
        OR
        -- Admin users can update any player
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

CREATE POLICY "players_delete_policy" ON players
    FOR DELETE USING (
        -- Users can delete players from their own teams
        auth.uid() = user_id
        OR
        EXISTS (
            SELECT 1 FROM teams t
            WHERE t.id = players.team_id
            AND t.user_id = auth.uid()
        )
        OR
        -- Admin users can delete any player
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

-- LINEUPS TABLE: Secure through team ownership
CREATE POLICY "lineups_select_policy" ON lineups
    FOR SELECT USING (
        -- Users can see lineups from their own teams
        auth.uid() = user_id
        OR
        EXISTS (
            SELECT 1 FROM teams t
            WHERE t.id = lineups.team_id
            AND t.user_id = auth.uid()
        )
        OR
        -- Admin users can see all lineups
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

CREATE POLICY "lineups_insert_policy" ON lineups
    FOR INSERT WITH CHECK (
        -- Users can only insert lineups for their own teams
        auth.uid() = user_id
        OR
        EXISTS (
            SELECT 1 FROM teams t
            WHERE t.id = lineups.team_id
            AND t.user_id = auth.uid()
        )
        OR
        -- Admin users can insert any lineup
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

CREATE POLICY "lineups_update_policy" ON lineups
    FOR UPDATE USING (
        -- Users can update lineups from their own teams
        auth.uid() = user_id
        OR
        EXISTS (
            SELECT 1 FROM teams t
            WHERE t.id = lineups.team_id
            AND t.user_id = auth.uid()
        )
        OR
        -- Admin users can update any lineup
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

CREATE POLICY "lineups_delete_policy" ON lineups
    FOR DELETE USING (
        -- Users can delete lineups from their own teams
        auth.uid() = user_id
        OR
        EXISTS (
            SELECT 1 FROM teams t
            WHERE t.id = lineups.team_id
            AND t.user_id = auth.uid()
        )
        OR
        -- Admin users can delete any lineup
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

-- ===== STEP 3: FIX AUTHENTICATION AND PAYMENT ISSUES =====

-- Ensure <EMAIL> has proper admin setup
DO $$
DECLARE
    noah_user_id UUID;
BEGIN
    -- Get noah's user ID from auth.users
    SELECT id INTO noah_user_id
    FROM auth.users
    WHERE email = '<EMAIL>';

    IF noah_user_id IS NOT NULL THEN
        -- Ensure noah has a profile with admin privileges
        INSERT INTO profiles (id, email, is_admin, created_at, updated_at)
        VALUES (noah_user_id, '<EMAIL>', true, NOW(), NOW())
        ON CONFLICT (id) DO UPDATE SET
            email = '<EMAIL>',
            is_admin = true,
            updated_at = NOW();

        -- Ensure noah has a paid subscription
        INSERT INTO subscriptions (user_id, is_paid, tier, team_limit, amount, currency, payment_date, created_at, updated_at)
        VALUES (noah_user_id, true, 'club', 999, 4900, 'usd', NOW(), NOW(), NOW())
        ON CONFLICT (user_id) DO UPDATE SET
            is_paid = true,
            tier = 'club',
            team_limit = 999,
            updated_at = NOW();

        RAISE NOTICE '<NAME_EMAIL> setup completed';
    ELSE
        RAISE NOTICE 'User <EMAIL> not found in auth.users';
    END IF;
END $$;

-- ===== STEP 4: CREATE FUNCTION TO ENSURE USER RECORDS =====

-- Function to automatically create profile and subscription for new users
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Create profile for new user
    INSERT INTO public.profiles (id, email, is_admin, created_at, updated_at)
    VALUES (
        NEW.id,
        NEW.email,
        CASE WHEN NEW.email IN ('<EMAIL>', '<EMAIL>') THEN true ELSE false END,
        NOW(),
        NOW()
    );

    -- Create subscription record for new user
    INSERT INTO public.subscriptions (user_id, is_paid, tier, team_limit, created_at, updated_at)
    VALUES (
        NEW.id,
        CASE WHEN NEW.email IN ('<EMAIL>', '<EMAIL>') THEN true ELSE false END,
        CASE WHEN NEW.email IN ('<EMAIL>', '<EMAIL>') THEN 'club' ELSE 'free' END,
        CASE WHEN NEW.email IN ('<EMAIL>', '<EMAIL>') THEN 999 ELSE 0 END,
        NOW(),
        NOW()
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically handle new user registration
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- ===== STEP 5: FIX EXISTING USERS WITHOUT PROPER RECORDS =====

-- Ensure all existing users have profiles
INSERT INTO profiles (id, email, is_admin, created_at, updated_at)
SELECT
    u.id,
    u.email,
    CASE WHEN u.email IN ('<EMAIL>', '<EMAIL>') THEN true ELSE false END,
    u.created_at,
    NOW()
FROM auth.users u
LEFT JOIN profiles p ON p.id = u.id
WHERE p.id IS NULL;

-- Ensure all existing users have subscription records
INSERT INTO subscriptions (user_id, is_paid, tier, team_limit, created_at, updated_at)
SELECT
    u.id,
    CASE WHEN u.email IN ('<EMAIL>', '<EMAIL>') THEN true ELSE false END,
    CASE WHEN u.email IN ('<EMAIL>', '<EMAIL>') THEN 'club' ELSE 'free' END,
    CASE WHEN u.email IN ('<EMAIL>', '<EMAIL>') THEN 999 ELSE 0 END,
    u.created_at,
    NOW()
FROM auth.users u
LEFT JOIN subscriptions s ON s.user_id = u.id
WHERE s.user_id IS NULL;

-- ===== STEP 6: VERIFICATION AND CLEANUP =====

-- Verify the fix worked
DO $$
DECLARE
    user_count INTEGER;
    profile_count INTEGER;
    subscription_count INTEGER;
    noah_profile_exists BOOLEAN;
    noah_subscription_exists BOOLEAN;
BEGIN
    SELECT COUNT(*) INTO user_count FROM auth.users;
    SELECT COUNT(*) INTO profile_count FROM profiles;
    SELECT COUNT(*) INTO subscription_count FROM subscriptions;

    SELECT EXISTS(
        SELECT 1 FROM profiles
        WHERE email = '<EMAIL>' AND is_admin = true
    ) INTO noah_profile_exists;

    SELECT EXISTS(
        SELECT 1 FROM subscriptions s
        JOIN auth.users u ON u.id = s.user_id
        WHERE u.email = '<EMAIL>' AND s.is_paid = true
    ) INTO noah_subscription_exists;

    RAISE NOTICE 'Migration verification:';
    RAISE NOTICE 'Users: %, Profiles: %, Subscriptions: %', user_count, profile_count, subscription_count;
    RAISE NOTICE 'Noah admin profile: %, Noah paid subscription: %', noah_profile_exists, noah_subscription_exists;

    IF user_count = profile_count AND user_count = subscription_count AND noah_profile_exists AND noah_subscription_exists THEN
        RAISE NOTICE 'SUCCESS: All users have proper <NAME_EMAIL> is properly configured';
    ELSE
        RAISE WARNING 'ISSUE: Some users may be missing <NAME_EMAIL> is not properly configured';
    END IF;
END $$;

COMMIT;

-- ===== POST-MIGRATION INSTRUCTIONS =====
-- After running this migration:
-- 1. Paid users should be able to access the dashboard without payment redirects
-- 2. User data should be properly isolated (users only see their own data)
-- 3. RLS policies should work correctly without data leakage
-- 4. Admin users should retain proper access to admin functions
-- 5. New user registration should automatically create required records
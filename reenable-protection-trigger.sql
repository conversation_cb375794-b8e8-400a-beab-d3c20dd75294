-- Re-enable the protection trigger
ALTER TABLE teams ENABLE TRIGGER protect_teams_trigger;

-- Verify the trigger is enabled
SELECT 
    tgname as trigger_name,
    tgenabled as is_enabled,
    CASE tgenabled 
        WHEN 'O' THEN 'ENABLED'
        WHEN 'D' THEN 'DISABLED'
        WHEN 'R' THEN 'REPLICA ONLY'
        WHEN 'A' THEN 'ALWAYS'
    END as status
FROM pg_trigger 
WHERE tgname = 'protect_teams_trigger';

-- This should show the trigger as ENABLED
# Developer README

This document contains important information for developers working on the Diamond Lineup Guru (Dugout Boss) application.

## Disabled Features

### Assistant Coach Access
- **Status**: Temporarily disabled (UI hidden)
- **Date Disabled**: January 2025
- **Planned Restoration**: Q3 2025
- **Feature Flag**: `assistantCoachAccess false`

**What was disabled:**
- UI components for inviting and managing assistant coaches
- <PERSON><PERSON> link in Dashboard
- Route `/assistant-coaches`

**What remains intact:**
- All backend routes and API endpoints
- Database table `assistant_coaches` and related data
- Row-level security policies
- All backend permissions and functionality

**To re-enable:**
1. Remove or uncomment the feature flag comments in:
   - `/src/App.tsx` (lines 25-26 and 108-109)
   - `/src/pages/Dashboard.tsx` (lines 280-283)
2. The component `/src/pages/AssistantCoaches.tsx` is fully functional and ready to use

**Files affected:**
- `/src/App.tsx` - Import and route commented out
- `/src/pages/Dashboard.tsx` - <PERSON><PERSON> link commented out
- `/src/pages/AssistantCoaches.tsx` - Added feature flag header comment

**Backend remains fully operational** - The assistant coaches table, permissions, and API functionality are preserved for easy reactivation.
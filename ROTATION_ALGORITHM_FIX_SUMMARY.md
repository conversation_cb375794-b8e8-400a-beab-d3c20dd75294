# Rotation Algorithm Fix Summary

## Problem Identified
The rotation algorithm was allowing severe playing time imbalances. In the reported case:
- Some players (<PERSON>, <PERSON><PERSON>, <PERSON>) played all 20 innings (100% field time)
- Others sat for 8 innings (only 60% field time)
- This occurred despite `rotateLineupEvery = 1` which should rotate players every inning

## Root Causes
1. **Insufficient rotation enforcement**: The algorithm wasn't forcing rotation when `rotateLineupEvery = 1`
2. **Weak balance penalties**: Players with too much field time weren't being penalized enough
3. **Early returns preventing rotation**: Code would skip rotation if positions were filled
4. **No exclusion of overplayed players**: The constraint solver kept selecting the same players

## Fixes Applied

### 1. Enhanced Rotation Detection (`shouldRotateThisInning`)
```typescript
// For rotateEvery = 1, we should rotate EVERY inning after the first
const shouldRotate = rotateEvery === 1 ? true : ((inning - 1) % rotateEvery === 0);
```

### 2. Forced Rotation Logic
- Added `mustRotate` flag for `rotateLineupEvery = 1`
- Prevents early returns that skip rotation
- Forces clearing of field positions to create openings

### 3. Stronger Playing Time Balance Calculation
```typescript
// Quadratic penalty for imbalance
const imbalancePenalty = Math.pow(Math.abs(fieldInnings - idealFieldInnings), 2) * 100;

// Much stronger priority weighting
priority: streak * 100000 + playingTimeDeficit * 10000 + imbalancePenalty + benchInnings * 100
```

### 4. Aggressive Rotation Mode
- When `rotateLineupEvery = 1`, rotate as many bench players as possible
- Clear more field positions to force rotation
- Detect and remove overplayed players from their positions

### 5. Overplayed Player Exclusion
```typescript
// Exclude overplayed players from constraint solver
if (this.rules.rotateLineupEvery === 1) {
  const playerFieldInnings = playerStats?.get(p.name)?.fieldInnings || 0;
  if (playerFieldInnings >= maxAllowedFieldInnings) {
    return false; // Exclude from available players
  }
}
```

### 6. Enhanced Logging
- Added detailed logging at every rotation decision point
- Tracks playing time deficits and imbalance penalties
- Shows when players are excluded for being overplayed

## Expected Behavior After Fix
With 12 players and 20 innings:
- Ideal field time: 15 innings per player (75%)
- Maximum acceptable range: 3-4 innings difference
- All players should get between 13-17 field innings
- No player should sit more than 2 consecutive innings

## Testing
Created comprehensive tests in `src/__tests__/rotation-balance-fix.test.ts` to verify:
1. Playing time balance with `rotateLineupEvery = 1`
2. Rotation occurs every inning as configured
3. Bench streak limits are respected

## Implementation Notes
The fix uses a multi-layered approach:
1. **Prevention**: Stop overplaying through exclusion
2. **Detection**: Identify imbalances early
3. **Correction**: Force rotation when needed
4. **Validation**: Check results match expectations

This ensures the algorithm respects equal playing time rules while maintaining valid lineups.
# Game Result Toggle Fix

## Issue
The game result toggle on the dashboard was showing a "-" instead of the Win/Loss buttons. This was caused by a missing database column migration.

## Root Cause
The `game_result` column was defined in the migration file `supabase/migrations/add_game_result_column.sql` but was never applied to the database. The code was temporarily excluding this column from queries to prevent errors.

## Solution

### 1. Database Migration (Required)
Run this SQL in your Supabase SQL Editor to add the missing column:

```sql
-- Add game_result column to lineups table
ALTER TABLE lineups ADD COLUMN IF NOT EXISTS game_result TEXT CHECK (game_result IN ('win', 'loss'));

-- Add index for performance
CREATE INDEX IF NOT EXISTS idx_lineups_game_result ON lineups(game_result);
```

### 2. Code Changes (Completed)
✅ Updated `src/services/teamService.ts` to:
- Include `game_result` in database queries
- Handle missing column gracefully with fallback logic
- Set `gameResult` property correctly on lineup objects

✅ Updated `src/components/WinLossToggle.tsx` to:
- Show helpful tooltip when column is missing
- Display "Future Game" for non-past games instead of "-"
- Handle undefined gameResult gracefully

## After Migration
Once the database migration is applied:
1. The game result toggle will show Win/Loss buttons for past games
2. Users can click to mark games as Won or Lost
3. Game results will be saved and displayed in the lineup table
4. The "-" will be replaced with proper toggle controls

## Files Modified
- `src/services/teamService.ts` - Database query handling
- `src/components/WinLossToggle.tsx` - UI component improvements
- `add-game-result-column.cjs` - Helper script (temporary)

## Testing
After applying the migration, test by:
1. Viewing the Dashboard page with existing lineups
2. Checking that past games show Win/Loss toggle buttons
3. Clicking buttons to set game results
4. Verifying results are saved and displayed correctly
import { generateCompleteLineup } from './src/lib/utils-enhanced.ts';

// Test the cross-game tracking fix
const players = Array.from({ length: 12 }, (_, i) => ({
  id: `player${i + 1}`,
  name: `Player${i + 1}`,
  teamRoles: {}
}));

console.log('🎯 TESTING BATCH CROSS-GAME TRACKING FIX');
console.log(`📋 Players: ${players.length}, Games: 3, Innings per game: 7`);

// Track cumulative stats like batch generation does
const playerInningsTracker: {[playerId: string]: number} = {};
const playerBenchTracker: {[playerId: string]: number} = {};
const playerPitchingTracker: {[playerId: string]: number} = {};

// Initialize tracking
players.forEach(player => {
  playerInningsTracker[player.id] = 0;
  playerBenchTracker[player.id] = 0;
  playerPitchingTracker[player.id] = 0;
});

// Generate 3 games with cross-game tracking
for (let gameIndex = 0; gameIndex < 3; gameIndex++) {
  console.log(`\n🎮 GAME ${gameIndex + 1}/3:`);
  console.log('📊 Stats before generation:', {
    fieldInnings: Object.fromEntries(Object.entries(playerInningsTracker).filter(([id, count]) => count > 0)),
  });

  // Generate game with cross-game tracking
  const gameInnings = generateCompleteLineup(
    players,
    7, // 7 innings per game
    {
      rotateLineupEvery: 1,
      rotatePitcherEvery: 2,
      equalPlayingTime: true,
      limitBenchTime: true,
      maxConsecutiveBenchInnings: 2,
      // Cross-game tracking data
      _crossGameTracking: {
        playerFieldInnings: { ...playerInningsTracker },
        playerBenchInnings: { ...playerBenchTracker },
        playerPitchingInnings: { ...playerPitchingTracker },
        gameNumber: gameIndex + 1,
        totalGames: 3
      }
    }
  );

  // Update tracking after game
  gameInnings.forEach(inning => {
    Object.entries(inning.positions).forEach(([position, playerName]) => {
      if (position !== 'bench' && playerName) {
        const player = players.find(p => p.name === playerName);
        if (player) {
          playerInningsTracker[player.id]++;
          if (position === 'pitcher') {
            playerPitchingTracker[player.id]++;
          }
        }
      }
    });
    
    if (inning.positions.bench) {
      inning.positions.bench.forEach((playerName: string) => {
        const player = players.find(p => p.name === playerName);
        if (player) {
          playerBenchTracker[player.id]++;
        }
      });
    }
  });

  console.log('📊 Stats after generation:', {
    fieldInnings: Object.fromEntries(Object.entries(playerInningsTracker)),
  });
}

// Final analysis
console.log('\n🏆 FINAL CROSS-GAME BALANCE ANALYSIS:');
const finalStats = players.map(player => ({
  name: player.name,
  innings: playerInningsTracker[player.id],
  bench: playerBenchTracker[player.id],
  pitching: playerPitchingTracker[player.id]
})).sort((a, b) => b.innings - a.innings);

const minInnings = Math.min(...finalStats.map(p => p.innings));
const maxInnings = Math.max(...finalStats.map(p => p.innings));
const range = maxInnings - minInnings;

console.log('📊 Playing time distribution:', finalStats);
console.log(`📈 Balance: ${minInnings}-${maxInnings} innings (range: ${range})`);
console.log(`✅ Cross-game tracking working: ${range <= 3 ? 'YES' : 'NO'} (range ≤ 3 is good)`);
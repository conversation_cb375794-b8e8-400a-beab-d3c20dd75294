# Critical Auth and Data Fixes Implementation

## Issues Fixed

### 1. RLS Policy Error for lineup_innings
**Problem**: Admin users couldn't create lineups for other users because the user_id didn't match auth.uid()
**Solution**: Created combined RLS policies that allow both regular users (auth.uid() = user_id) and admins (by email) to perform operations

### 2. Rotation Rules Update Authentication Failure
**Problem**: supabase.auth.getUser() returning null during rotation rules updates
**Solution**: 
- Pass user ID explicitly from TeamContext
- Add fallback to getSession if getU<PERSON> fails
- Improve error handling in updateRotationRules function

### 3. Player Save 406 Error
**Problem**: "JSON object requested, multiple (or no) rows returned" when updating players
**Solution**: 
- Use .maybeSingle() instead of .single() to handle no results gracefully
- Add explicit ID checking before update
- Improve error messages for better debugging

### 4. Session Persistence Issues
**Problem**: Users being logged out on page refresh
**Solution**: 
- Add isInitialized flag to AuthContext
- Use localStorage to track session existence
- Implement proper session restoration logic with timeout
- Clear cached data on user change to prevent cross-user contamination

## Migration SQL to Apply

```sql
-- Fix lineup_innings RLS policies to properly handle both regular users and admin operations

-- First, let's drop the existing policies to start fresh
DROP POLICY IF EXISTS "Users can view their own lineup innings" ON public.lineup_innings;
DROP POLICY IF EXISTS "Users can insert their own lineup innings" ON public.lineup_innings;
DROP POLICY IF EXISTS "Users can update their own lineup innings" ON public.lineup_innings;
DROP POLICY IF EXISTS "Users can delete their own lineup innings" ON public.lineup_innings;
DROP POLICY IF EXISTS "Allow admins to read all lineup innings" ON public.lineup_innings;
DROP POLICY IF EXISTS "Allow admins to update all lineup innings" ON public.lineup_innings;
DROP POLICY IF EXISTS "Allow admins to insert all lineup innings" ON public.lineup_innings;
DROP POLICY IF EXISTS "Allow admins to delete all lineup innings" ON public.lineup_innings;

-- Create new policies that handle both regular users and admins properly

-- SELECT policy: Allow users to view their own innings OR admins to view all
CREATE POLICY "lineup_innings_select_policy" ON public.lineup_innings
  FOR SELECT USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

-- INSERT policy: Allow users to insert their own innings OR admins to insert any
CREATE POLICY "lineup_innings_insert_policy" ON public.lineup_innings
  FOR INSERT WITH CHECK (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

-- UPDATE policy: Allow users to update their own innings OR admins to update any
CREATE POLICY "lineup_innings_update_policy" ON public.lineup_innings
  FOR UPDATE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

-- DELETE policy: Allow users to delete their own innings OR admins to delete any
CREATE POLICY "lineup_innings_delete_policy" ON public.lineup_innings
  FOR DELETE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

-- Also fix the lineups table policies in the same way
DROP POLICY IF EXISTS "Users can view their own lineups" ON public.lineups;
DROP POLICY IF EXISTS "Users can insert their own lineups" ON public.lineups;
DROP POLICY IF EXISTS "Users can update their own lineups" ON public.lineups;
DROP POLICY IF EXISTS "Users can delete their own lineups" ON public.lineups;
DROP POLICY IF EXISTS "Allow admins to read all lineups" ON public.lineups;
DROP POLICY IF EXISTS "Allow admins to update all lineups" ON public.lineups;
DROP POLICY IF EXISTS "Allow admins to insert all lineups" ON public.lineups;
DROP POLICY IF EXISTS "Allow admins to delete all lineups" ON public.lineups;

-- Create new combined policies for lineups
CREATE POLICY "lineups_select_policy" ON public.lineups
  FOR SELECT USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "lineups_insert_policy" ON public.lineups
  FOR INSERT WITH CHECK (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "lineups_update_policy" ON public.lineups
  FOR UPDATE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "lineups_delete_policy" ON public.lineups
  FOR DELETE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

-- Do the same for lineup_attendance
DROP POLICY IF EXISTS "Users can view their own lineup attendance" ON public.lineup_attendance;
DROP POLICY IF EXISTS "Users can insert their own lineup attendance" ON public.lineup_attendance;
DROP POLICY IF EXISTS "Users can update their own lineup attendance" ON public.lineup_attendance;
DROP POLICY IF EXISTS "Users can delete their own lineup attendance" ON public.lineup_attendance;

CREATE POLICY "lineup_attendance_select_policy" ON public.lineup_attendance
  FOR SELECT USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "lineup_attendance_insert_policy" ON public.lineup_attendance
  FOR INSERT WITH CHECK (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "lineup_attendance_update_policy" ON public.lineup_attendance
  FOR UPDATE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "lineup_attendance_delete_policy" ON public.lineup_attendance
  FOR DELETE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

-- Fix rotation_rules policies as well
DROP POLICY IF EXISTS "Users can view their own rotation rules" ON public.rotation_rules;
DROP POLICY IF EXISTS "Users can insert their own rotation rules" ON public.rotation_rules;
DROP POLICY IF EXISTS "Users can update their own rotation rules" ON public.rotation_rules;
DROP POLICY IF EXISTS "Users can delete their own rotation rules" ON public.rotation_rules;

CREATE POLICY "rotation_rules_select_policy" ON public.rotation_rules
  FOR SELECT USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "rotation_rules_insert_policy" ON public.rotation_rules
  FOR INSERT WITH CHECK (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "rotation_rules_update_policy" ON public.rotation_rules
  FOR UPDATE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "rotation_rules_delete_policy" ON public.rotation_rules
  FOR DELETE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

-- Fix players table policies
DROP POLICY IF EXISTS "Users can view their own players" ON public.players;
DROP POLICY IF EXISTS "Users can insert their own players" ON public.players;
DROP POLICY IF EXISTS "Users can update their own players" ON public.players;
DROP POLICY IF EXISTS "Users can delete their own players" ON public.players;
DROP POLICY IF EXISTS "Allow admins to read all players" ON public.players;
DROP POLICY IF EXISTS "Allow admins to update all players" ON public.players;
DROP POLICY IF EXISTS "Allow admins to insert all players" ON public.players;
DROP POLICY IF EXISTS "Allow admins to delete all players" ON public.players;

CREATE POLICY "players_select_policy" ON public.players
  FOR SELECT USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "players_insert_policy" ON public.players
  FOR INSERT WITH CHECK (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "players_update_policy" ON public.players
  FOR UPDATE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "players_delete_policy" ON public.players
  FOR DELETE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );
```

## How to Apply These Fixes

1. **Apply the RLS Policy Migration**:
   - Go to your Supabase project dashboard
   - Navigate to the SQL Editor
   - Copy and paste the SQL above
   - Click "Run" to execute

2. **Deploy the Updated Auth Context**:
   - The AuthContext.tsx file has been updated with better session persistence
   - Deploy your application with these changes

3. **Test the Fixes**:
   - Test admin batch lineup creation
   - Test regular user operations
   - Test page refresh to ensure session persists
   - Test player updates on the roster page

## Additional Recommendations

1. **Add Better Error Handling**: Wrap all Supabase operations in try-catch blocks with specific error messages
2. **Add Loading States**: Show loading indicators during auth state changes
3. **Add Retry Logic**: For transient network errors, implement retry with exponential backoff
4. **Monitor Auth Events**: Log all auth state changes for debugging
5. **Clear Cache on Logout**: Ensure all cached data is cleared when users sign out

## Testing Checklist

- [ ] Admin can create lineups for other users
- [ ] Regular users can save their rotation rules
- [ ] Players can be updated without 406 errors
- [ ] Session persists across page refreshes
- [ ] No cross-user data contamination
- [ ] Auth state properly initializes on app load
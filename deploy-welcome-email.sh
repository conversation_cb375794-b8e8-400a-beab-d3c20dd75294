#!/bin/bash

echo "🚀 Deploying Welcome Email Functions..."

# Deploy the new welcome email function
echo "📧 Deploying send-welcome-email function..."
supabase functions deploy send-welcome-email

# Deploy the updated admin-create-user function
echo "👤 Deploying updated admin-create-user function..."
supabase functions deploy admin-create-user

echo "✅ Deployment complete!"
echo ""
echo "⚠️  IMPORTANT: Make sure to set RESEND_API_KEY in your Supabase dashboard:"
echo "1. Go to your Supabase project dashboard"
echo "2. Navigate to Edge Functions > Secrets"
echo "3. Add RESEND_API_KEY with your Resend API key"
echo ""
echo "If you don't have a Resend API key:"
echo "1. Go to https://resend.com/signup"
echo "2. Create a free account"
echo "3. Get your API key from the dashboard"
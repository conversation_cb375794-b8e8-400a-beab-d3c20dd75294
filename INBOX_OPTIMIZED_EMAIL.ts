// INBOX-OPTIMIZED VERSION - Better deliverability

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.7'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get auth header
    const authHeader = req.headers.get('authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? ''
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    
    const supabaseClient = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    // Verify user is admin
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid authentication' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check admin status
    const { data: profile } = await supabaseClient
      .from('profiles')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized - admin access required' }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get request data
    const { email, name, temporaryPassword } = await req.json()

    if (!email || !name || !temporaryPassword) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check for Resend API key
    const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')
    if (!RESEND_API_KEY) {
      console.error('RESEND_API_KEY is not set')
      return new Response(
        JSON.stringify({ error: 'Email service not configured' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Build URLs
    const origin = req.headers.get('origin') || 'https://dugoutboss.com'
    const loginUrl = `${origin}/sign-in`

    // Create IMPROVED email HTML - Better for inbox delivery
    const htmlContent = `
<!DOCTYPE html>
<html lang="en" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="x-apple-disable-message-reformatting">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Your Dugout Boss Account is Ready</title>
    <!--[if mso]>
    <xml>
        <o:OfficeDocumentSettings>
            <o:AllowPNG/>
            <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
    </xml>
    <![endif]-->
</head>
<body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif; font-size: 16px; line-height: 1.6; color: #333333; background-color: #f5f5f5;">
    <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f5f5f5;">
        <tr>
            <td align="center" style="padding: 40px 20px;">
                <table role="presentation" cellpadding="0" cellspacing="0" width="600" style="background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <!-- Header -->
                    <tr>
                        <td align="center" style="padding: 40px 20px 30px; background-color: #1e40af; border-radius: 8px 8px 0 0;">
                            <h1 style="margin: 0; color: #ffffff; font-size: 28px; font-weight: 600; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;">Dugout Boss</h1>
                            <p style="margin: 10px 0 0 0; color: #e0e7ff; font-size: 16px;">Smart Lineup Management for Coaches</p>
                        </td>
                    </tr>
                    
                    <!-- Content -->
                    <tr>
                        <td style="padding: 40px;">
                            <h2 style="margin: 0 0 20px 0; color: #1e293b; font-size: 24px; font-weight: 600;">Welcome to your team, ${name}!</h2>
                            
                            <p style="margin: 0 0 30px 0; color: #475569; font-size: 16px; line-height: 1.6;">
                                Your Dugout Boss account has been set up by your team administrator. You can now access your team's lineups and schedules.
                            </p>
                            
                            <!-- Credentials Box -->
                            <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; margin-bottom: 30px;">
                                <tr>
                                    <td style="padding: 30px;">
                                        <h3 style="margin: 0 0 20px 0; color: #1e293b; font-size: 18px; font-weight: 600;">Your Login Information</h3>
                                        
                                        <p style="margin: 0 0 15px 0; color: #475569; font-size: 14px;">
                                            <strong>Email:</strong><br>
                                            <span style="font-size: 16px; color: #1e293b;">${email}</span>
                                        </p>
                                        
                                        <p style="margin: 0 0 20px 0; color: #475569; font-size: 14px;">
                                            <strong>Temporary Password:</strong><br>
                                            <code style="background-color: #fef3c7; color: #92400e; padding: 4px 8px; border-radius: 4px; font-family: monospace;">${temporaryPassword}</code>
                                        </p>
                                        
                                        <p style="margin: 0 0 20px 0; color: #dc2626; font-size: 14px;">
                                            <strong>Important:</strong> Please change your password after logging in.
                                        </p>
                                        
                                        <div style="text-align: center;">
                                            <a href="${loginUrl}" style="display: inline-block; background-color: #1e40af; color: #ffffff; padding: 14px 32px; text-decoration: none; border-radius: 6px; font-weight: 600; font-size: 16px;">Sign In to Dugout Boss</a>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                            
                            <!-- Getting Started -->
                            <h3 style="margin: 0 0 20px 0; color: #1e293b; font-size: 20px; font-weight: 600;">What's Next?</h3>
                            
                            <ol style="margin: 0 0 30px 0; padding-left: 20px; color: #475569;">
                                <li style="margin-bottom: 12px;">Sign in with your credentials above</li>
                                <li style="margin-bottom: 12px;">Change your password to something memorable</li>
                                <li style="margin-bottom: 12px;">Check out your team's upcoming games and lineups</li>
                            </ol>
                            
                            <!-- Support -->
                            <table role="presentation" cellpadding="0" cellspacing="0" width="100%" style="border-top: 1px solid #e2e8f0; padding-top: 30px;">
                                <tr>
                                    <td>
                                        <p style="margin: 0 0 10px 0; color: #64748b; font-size: 14px;">
                                            Need help? Contact us at <a href="mailto:<EMAIL>" style="color: #1e40af; text-decoration: none;"><EMAIL></a>
                                        </p>
                                        <p style="margin: 0; color: #94a3b8; font-size: 12px;">
                                            You received this email because an account was created for you by your team administrator. This is a transactional email.
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    
                    <!-- Footer -->
                    <tr>
                        <td align="center" style="padding: 30px; background-color: #f8fafc; border-radius: 0 0 8px 8px;">
                            <p style="margin: 0 0 10px 0; color: #64748b; font-size: 14px;">
                                © ${new Date().getFullYear()} Dugout Boss, LLC. All rights reserved.
                            </p>
                            <p style="margin: 0; color: #94a3b8; font-size: 12px;">
                                123 Main Street, Suite 100, Anytown, ST 12345
                            </p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>`

    // Create plain text version
    const textContent = `Welcome to Dugout Boss!

Hi ${name},

Your Dugout Boss account has been set up by your team administrator. You can now access your team's lineups and schedules.

YOUR LOGIN INFORMATION
======================
Email: ${email}
Temporary Password: ${temporaryPassword}

Important: Please change your password after logging in.

Sign in here: ${loginUrl}

WHAT'S NEXT?
============
1. Sign in with your credentials above
2. Change your password to something memorable  
3. Check out your team's upcoming games and lineups

Need help? Contact <NAME_EMAIL>

You received this email because an account was created for you by your team administrator.

© ${new Date().getFullYear()} Dugout Boss, LLC. All rights reserved.
123 Main Street, Suite 100, Anytown, ST 12345`

    // Send email with better headers
    console.log(`Sending welcome email to: ${email}`)
    
    try {
      const messageId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}@dugoutboss.com`
      
      const resendResponse = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${RESEND_API_KEY}`,
        },
        body: JSON.stringify({
          from: 'Dugout Boss <<EMAIL>>',  // 'hello' is friendlier than 'support'
          to: [email],
          subject: 'Your Dugout Boss Account is Ready',  // Avoid "has been created"
          html: htmlContent,
          text: textContent,
          headers: {
            'X-Entity-Ref-ID': messageId,
            'List-Unsubscribe': '<mailto:<EMAIL>>',
          },
          tags: [
            { name: 'type', value: 'welcome' },
            { name: 'category', value: 'transactional' }
          ]
        }),
      })

      const responseText = await resendResponse.text()
      console.log('Resend response:', resendResponse.status, responseText)

      if (!resendResponse.ok) {
        console.error('Resend API error:', responseText)
        return new Response(
          JSON.stringify({ 
            error: 'Failed to send email',
            details: responseText
          }),
          { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }

      const result = JSON.parse(responseText)
      console.log('Welcome email sent successfully to:', email, 'Message ID:', result.id)

      return new Response(
        JSON.stringify({ 
          success: true, 
          messageId: result.id,
          sentTo: email
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
      
    } catch (fetchError) {
      console.error('Error calling Resend:', fetchError)
      return new Response(
        JSON.stringify({ 
          error: 'Failed to call Resend API',
          details: fetchError.message
        }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
    
  } catch (error) {
    console.error('Error in send-welcome-email function:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
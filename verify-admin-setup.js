import { supabase } from "./src/integrations/supabase/node-client.js";

// Load environment variables

async function verifyAdminSetup() {
  console.log('🔍 Verifying Admin Setup\n');
  console.log('=' + '='.repeat(60) + '\n');

  // Check if tables exist and have correct structure
  console.log('📊 Checking database structure...\n');
  
  try {
    // Check profiles table columns
    const { data: profileTest, error: profileError } = await supabase
      .from('profiles')
      .select('id, email, is_admin')
      .limit(0);
    
    if (!profileError) {
      console.log('✅ Profiles table has email and is_admin columns');
    } else {
      console.log('❌ Profiles table error:', profileError.message);
    }

    // Check subscriptions table
    const { data: subTest, error: subError } = await supabase
      .from('subscriptions')
      .select('id')
      .limit(0);
    
    if (!subError) {
      console.log('✅ Subscriptions table exists');
    } else {
      console.log('❌ Subscriptions table error:', subError.message);
    }

    // Check admin_audit_logs table
    const { data: auditTest, error: auditError } = await supabase
      .from('admin_audit_logs')
      .select('id')
      .limit(0);
    
    if (!auditError) {
      console.log('✅ Admin audit logs table exists');
    } else {
      console.log('❌ Admin audit logs table error:', auditError.message);
    }
  } catch (err) {
    console.log('❌ Database check error:', err.message);
  }

  console.log('\n' + '-'.repeat(60) + '\n');

  // Check who is marked as admin
  console.log('👤 Checking admin users...\n');
  
  // This query will only work if you're signed in as an admin
  // or if there's a policy allowing you to see admin users
  try {
    const { data: adminUsers, error } = await supabase
      .from('profiles')
      .select('id, email, is_admin')
      .eq('is_admin', true);
    
    if (error) {
      console.log('⚠️  Cannot query admin users (this is normal if not signed in as admin)');
      console.log('   Error:', error.message);
    } else if (adminUsers && adminUsers.length > 0) {
      console.log('✅ Found admin users:');
      adminUsers.forEach(admin => {
        console.log(`   - ${admin.email} (ID: ${admin.id})`);
      });
    } else {
      console.log('⚠️  No admin users found in database');
    }
  } catch (err) {
    console.log('❌ Admin users check error:', err.message);
  }

  console.log('\n' + '-'.repeat(60) + '\n');

  // Check current auth status
  console.log('🔐 Checking current authentication...\n');
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      console.log('❌ Not authenticated with Supabase');
      console.log('\n📝 TO ACCESS ADMIN PAGES:');
      console.log('1. Go to http://localhost:5173/signin');
      console.log('2. Sign in with your admin email (<EMAIL>)');
      console.log('3. If you don\'t have an account, create one at /signup first');
      console.log('4. After signing in, go to http://localhost:5173/admin');
    } else {
      console.log('✅ Authenticated as:', user.email);
      console.log('   User ID:', user.id);
      
      // Check if this user is admin
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('is_admin')
        .eq('id', user.id)
        .single();
      
      if (!profileError && profile) {
        if (profile.is_admin) {
          console.log('✅ You are signed in as an ADMIN');
          console.log('\n🎉 You should now be able to access:');
          console.log('   - http://localhost:5173/admin');
          console.log('   - http://localhost:5173/admin/users');
          console.log('   - http://localhost:5173/admin/teams');
          console.log('   - http://localhost:5173/admin/billing');
        } else {
          console.log('⚠️  You are signed in but NOT as an admin');
          console.log('\n📝 To make yourself admin, run this SQL:');
          console.log(`   UPDATE profiles SET is_admin = TRUE WHERE email = '${user.email}';`);
        }
      }
    }
  } catch (err) {
    console.log('❌ Auth check error:', err.message);
  }

  console.log('\n' + '='.repeat(60) + '\n');
  
  // Final checklist
  console.log('📋 ADMIN SETUP CHECKLIST:\n');
  console.log('[ ] Database tables exist (profiles, subscriptions, admin_audit_logs)');
  console.log('[ ] Profiles table has email and is_admin columns');
  console.log('[ ] Admin users are marked in the database');
  console.log('[ ] You are signed in with Supabase Auth (not just localStorage)');
  console.log('[ ] Your account is marked as is_admin = true');
  console.log('\n✨ Complete all items above to access admin pages');
}

verifyAdminSetup();
# Rotation Fix Summary

## Issue
In fair play/recreational mode, players marked as "Primary" for positions were staying in those positions for the entire series without rotating. This was too restrictive for fair play mode where everyone should rotate.

## Fix Applied
Updated `single-game-lineup-strict.ts` to add rotation logic for recreational mode:

1. When `competitiveMode` is false and `shouldRotateLineup` is true, the algorithm now:
   - Checks who was at each position in the previous inning
   - Tries to rotate them out by finding other eligible players
   - Combines all available players regardless of role priority
   - Prioritizes players who need more playing time

2. This ensures that even "Primary" players will rotate positions in recreational mode.

## UI Updates
Updated `TeamPhilosophySelector.tsx` to clarify:
- "Equal Playing Time": Added "Everyone rotates through different positions (even 'Primary' players)"
- "Position Specialists": Clarified that players "favor" their primary positions but still rotate

## Type Fix
Removed references to `maxConsecutiveBenchInnings` from `TeamPhilosophySelector` as this property doesn't exist in the `RotationRules` interface.

## Batch Game Support
The fix automatically applies to batch/multiple game creation because:
- `multi-game-orchestrator.ts` uses `generateSingleGameLineupStrict` (line 348)
- This is the same function we just fixed
- So all multi-game series will now properly rotate primary players in recreational mode

## Result
Now in recreational mode (both single and multi-game):
- All players (including those marked as "Primary") rotate through different positions
- Fair playing time is maintained across all games
- Players get development opportunities at various positions
- The rotation is more natural and balanced
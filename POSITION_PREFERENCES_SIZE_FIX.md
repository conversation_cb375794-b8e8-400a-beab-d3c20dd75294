# Position Preferences JSONB Column Size Fix

## Issue
The `position_preferences` JSONB column in the `players` table was being used to store multiple types of data:
- Actual position preferences (the intended use)
- Team roles data (`teamRoles`)
- Pitcher strategy data (`pitcherStrategy`)

While PostgreSQL JSONB columns can technically store up to 1GB of data, nesting multiple data structures in a single column can lead to:
1. Performance issues with larger JSON objects
2. Data organization problems
3. Potential issues with certain database operations or client libraries

## Solution
Separated the data into dedicated columns:
- `position_preferences` - Only stores actual position preference data
- `team_roles` - Stores team role assignments (go-to, capable, fill-in, avoid)
- `pitcher_strategy` - Stores pitcher-specific strategy settings

## Migration Details

### Database Migration
Created `add_team_roles_column.sql` migration that:
1. Adds `team_roles` JSONB column with default `{}`
2. Adds `pitcher_strategy` JSONB column with default `NULL`
3. Migrates existing nested data from `position_preferences` to the new columns
4. Cleans up `position_preferences` to only contain preference data
5. Adds appropriate indexes for performance

### Code Changes

#### teamService.ts
Updated the following functions to use separate columns:
- `fetchPlayers()` - Reads from separate columns with fallback to legacy nested data
- `createPlayer()` - Writes to separate columns
- `updatePlayer()` - Writes to separate columns

#### types.ts
Updated the Supabase type definitions to include:
- `team_roles: Json`
- `pitcher_strategy: Json | null`

## Benefits
1. **Better Performance**: Smaller JSON objects in each column
2. **Clearer Data Organization**: Each column has a specific purpose
3. **Easier Queries**: Can query/index specific data without parsing large JSON objects
4. **Future-proof**: Room to grow each data type independently

## Backward Compatibility
The code maintains backward compatibility by:
1. Checking the new columns first
2. Falling back to nested data in `position_preferences` if new columns are empty
3. The migration script automatically moves existing nested data to the new columns

## Deployment Steps
1. Run the database migration: `add_team_roles_column.sql`
2. Deploy the updated code
3. Verify data migration completed successfully
4. Optional: Run a cleanup script to ensure all nested data has been migrated

## Testing
After deployment, test:
1. Creating new players - data should go to separate columns
2. Updating existing players - data should update separate columns
3. Loading players - should read from new columns with fallback to legacy
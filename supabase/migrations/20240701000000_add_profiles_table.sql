-- Create profiles table
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  full_name TEXT,
  role TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create assistant_coaches table
CREATE TABLE IF NOT EXISTS public.assistant_coaches (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies for profiles
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own profile"
  ON public.profiles FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
  ON public.profiles FOR UPDATE
  USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile"
  ON public.profiles FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Create RLS policies for assistant_coaches
ALTER TABLE public.assistant_coaches ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own assistant coaches"
  ON public.assistant_coaches FOR SELECT
  USING (auth.uid() = owner_id);

CREATE POLICY "Users can insert their own assistant coaches"
  ON public.assistant_coaches FOR INSERT
  WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Users can update their own assistant coaches"
  ON public.assistant_coaches FOR UPDATE
  USING (auth.uid() = owner_id);

CREATE POLICY "Users can delete their own assistant coaches"
  ON public.assistant_coaches FOR DELETE
  USING (auth.uid() = owner_id);

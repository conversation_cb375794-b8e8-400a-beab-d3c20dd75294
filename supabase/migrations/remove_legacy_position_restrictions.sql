-- Remove legacy position restriction columns from players table
-- These columns have been replaced by the teamRoles system in the application

-- First, let's add a comment to document the change
COMMENT ON TABLE public.players IS 
'Player records with position assignments. Legacy position restriction columns (pitcher_restriction, catcher_restriction, first_base_restriction, other_restriction) have been replaced by the teamRoles JSONB field which uses the new team-dynamics-based preference system.';

-- Remove the legacy position restriction columns
-- Note: These columns are no longer used in the application after the roster UI streamlining

ALTER TABLE public.players 
DROP COLUMN IF EXISTS pitcher_restriction,
DROP COLUMN IF EXISTS catcher_restriction,
DROP COLUMN IF EXISTS first_base_restriction,
DROP COLUMN IF EXISTS other_restriction;

-- Add index on team_roles for better query performance
CREATE INDEX IF NOT EXISTS idx_players_team_roles 
ON public.players USING GIN (team_roles);

-- Add comment on the team_roles column
COMMENT ON COLUMN public.players.team_roles IS 
'JSONB field containing team role assignments for each position. Uses keys like "pitcher", "catcher", etc. with values "go-to", "capable", "fill-in", "avoid". Replaces the legacy position restriction system.';
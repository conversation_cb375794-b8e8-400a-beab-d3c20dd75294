-- Migration: Simplify Position Preferences to 1-5 Rating System
-- This migration adds support for the new simplified rating system while maintaining backward compatibility

-- Add new columns for simplified rating system
ALTER TABLE public.players 
ADD COLUMN IF NOT EXISTS position_ratings JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS star_positions J<PERSON>NB DEFAULT '[]';

-- Create function to convert old preferences to new ratings
CREATE OR REPLACE FUNCTION convert_preference_to_rating(preference JSONB)
RETURNS INTEGER AS $$
BEGIN
    -- Handle null/empty preferences
    IF preference IS NULL THEN
        RETURN 3; -- neutral = 3
    END IF;
    
    -- Handle string format
    IF jsonb_typeof(preference) = 'string' THEN
        CASE preference::text
            WHEN '"preferred"' THEN RETURN 5;
            WHEN '"secondary"' THEN RETURN 4;
            WHEN '"neutral"' THEN RETURN 3;
            WHEN '"avoid"' THEN RETURN 1;
            ELSE RETURN 3;
        END CASE;
    END IF;
    
    -- Handle object format with level and rank
    IF jsonb_typeof(preference) = 'object' AND preference ? 'level' THEN
        CASE preference->>'level'
            WHEN 'preferred' THEN 
                -- Use rank to determine rating (rank 1 = 5, rank 2 = 5, rank 3+ = 4)
                IF preference ? 'rank' AND (preference->>'rank')::integer <= 2 THEN
                    RETURN 5;
                ELSE
                    RETURN 4;
                END IF;
            WHEN 'secondary' THEN RETURN 4;
            WHEN 'neutral' THEN RETURN 3;
            WHEN 'avoid' THEN RETURN 1;
            ELSE RETURN 3;
        END CASE;
    END IF;
    
    RETURN 3; -- default to neutral
END;
$$ LANGUAGE plpgsql;

-- Create function to identify star positions from old preferences
CREATE OR REPLACE FUNCTION extract_star_positions(preferences JSONB)
RETURNS JSONB AS $$
DECLARE
    position_key TEXT;
    preference JSONB;
    star_positions TEXT[] := '{}';
BEGIN
    -- Loop through all position preferences
    FOR position_key IN SELECT jsonb_object_keys(preferences)
    LOOP
        preference := preferences->position_key;
        
        -- Check if this was a top-ranked preferred position
        IF jsonb_typeof(preference) = 'object' AND 
           preference ? 'level' AND 
           preference->>'level' = 'preferred' AND
           preference ? 'rank' AND 
           (preference->>'rank')::integer = 1 THEN
            star_positions := array_append(star_positions, position_key);
        ELSIF jsonb_typeof(preference) = 'string' AND 
              preference::text = '"preferred"' THEN
            -- For simple preferred without ranking, don't auto-assign as star
            -- Let users manually designate stars
            NULL;
        END IF;
    END LOOP;
    
    RETURN to_jsonb(star_positions);
END;
$$ LANGUAGE plpgsql;

-- Migrate existing data
UPDATE public.players 
SET 
    position_ratings = (
        SELECT jsonb_object_agg(
            position_key, 
            convert_preference_to_rating(position_preferences->position_key)
        )
        FROM jsonb_object_keys(position_preferences) AS position_key
        WHERE position_preferences IS NOT NULL AND position_preferences != '{}'::jsonb
    ),
    star_positions = extract_star_positions(position_preferences)
WHERE position_preferences IS NOT NULL AND position_preferences != '{}'::jsonb;

-- Set default ratings for players without preferences
UPDATE public.players 
SET position_ratings = '{}'::jsonb
WHERE position_ratings IS NULL;

UPDATE public.players 
SET star_positions = '[]'::jsonb
WHERE star_positions IS NULL;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_players_position_ratings 
ON public.players USING GIN (position_ratings);

CREATE INDEX IF NOT EXISTS idx_players_star_positions 
ON public.players USING GIN (star_positions);

-- Add validation function for new rating system
CREATE OR REPLACE FUNCTION validate_position_ratings(ratings JSONB)
RETURNS BOOLEAN AS $$
DECLARE
    position_key TEXT;
    rating_value JSONB;
BEGIN
    -- Check if ratings is a valid object
    IF jsonb_typeof(ratings) != 'object' THEN
        RETURN FALSE;
    END IF;
    
    -- Validate each rating value
    FOR position_key IN SELECT jsonb_object_keys(ratings)
    LOOP
        rating_value := ratings->position_key;
        
        -- Rating must be a number between 1 and 5
        IF jsonb_typeof(rating_value) != 'number' OR 
           (rating_value::text)::integer < 1 OR 
           (rating_value::text)::integer > 5 THEN
            RETURN FALSE;
        END IF;
    END LOOP;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

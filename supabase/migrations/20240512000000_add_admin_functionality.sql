-- Add is_admin field to profiles table
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS is_admin BOOLEAN DEFAULT FALSE;

-- Create admin audit log table for tracking admin actions
CREATE TABLE IF NOT EXISTS public.admin_audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  admin_id UUID REFERENCES auth.users(id) NOT NULL,
  action TEXT NOT NULL,
  entity_type TEXT NOT NULL,
  entity_id TEXT NOT NULL,
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies for admin access
-- Allow admins to read all profiles
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Allow admins to read all profiles'
    ) THEN
        CREATE POLICY "Allow admins to read all profiles"
        ON public.profiles FOR SELECT
        USING (
            auth.uid() IN (SELECT id FROM public.profiles WHERE is_admin = TRUE)
        );
    END IF;
END
$$;

-- Allow admins to update all profiles
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Allow admins to update all profiles'
    ) THEN
        CREATE POLICY "Allow admins to update all profiles"
        ON public.profiles FOR UPDATE
        USING (
            auth.uid() IN (SELECT id FROM public.profiles WHERE is_admin = TRUE)
        );
    END IF;
END
$$;

-- Allow admins to read all teams
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Allow admins to read all teams'
    ) THEN
        CREATE POLICY "Allow admins to read all teams"
        ON public.teams FOR SELECT
        USING (
            auth.uid() IN (SELECT id FROM public.profiles WHERE is_admin = TRUE)
        );
    END IF;
END
$$;

-- Allow admins to read all players
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Allow admins to read all players'
    ) THEN
        CREATE POLICY "Allow admins to read all players"
        ON public.players FOR SELECT
        USING (
            auth.uid() IN (SELECT id FROM public.profiles WHERE is_admin = TRUE)
        );
    END IF;
END
$$;

-- Allow admins to read all lineups
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Allow admins to read all lineups'
    ) THEN
        CREATE POLICY "Allow admins to read all lineups"
        ON public.lineups FOR SELECT
        USING (
            auth.uid() IN (SELECT id FROM public.profiles WHERE is_admin = TRUE)
        );
    END IF;
END
$$;

-- Allow admins to read all subscriptions
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Allow admins to read all subscriptions'
    ) THEN
        CREATE POLICY "Allow admins to read all subscriptions"
        ON public.subscriptions FOR SELECT
        USING (
            auth.uid() IN (SELECT id FROM public.profiles WHERE is_admin = TRUE)
        );
    END IF;
END
$$;

-- Allow admins to update all subscriptions
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Allow admins to update all subscriptions'
    ) THEN
        CREATE POLICY "Allow admins to update all subscriptions"
        ON public.subscriptions FOR UPDATE
        USING (
            auth.uid() IN (SELECT id FROM public.profiles WHERE is_admin = TRUE)
        );
    END IF;
END
$$;

-- Allow admins to insert subscriptions for any user
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Allow admins to insert subscriptions'
    ) THEN
        CREATE POLICY "Allow admins to insert subscriptions"
        ON public.subscriptions FOR INSERT
        WITH CHECK (
            auth.uid() IN (SELECT id FROM public.profiles WHERE is_admin = TRUE)
        );
    END IF;
END
$$;

-- Add RLS to admin_audit_logs table
ALTER TABLE public.admin_audit_logs ENABLE ROW LEVEL SECURITY;

-- Only admins can view audit logs
CREATE POLICY "Only admins can view audit logs"
  ON public.admin_audit_logs FOR SELECT
  USING (auth.uid() IN (SELECT id FROM public.profiles WHERE is_admin = TRUE));

-- Only admins can insert audit logs
CREATE POLICY "Only admins can insert audit logs"
  ON public.admin_audit_logs FOR INSERT
  WITH CHECK (auth.uid() IN (SELECT id FROM public.profiles WHERE is_admin = TRUE));

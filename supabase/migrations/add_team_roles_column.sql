-- Migration to add team_roles column to players table
-- This properly separates team roles from position preferences

-- Add team_roles column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'players' 
        AND column_name = 'team_roles'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.players 
        ADD COLUMN team_roles JSONB DEFAULT '{}';
        
        -- Create an index for better query performance
        CREATE INDEX idx_players_team_roles 
        ON public.players USING GIN (team_roles);
        
        RAISE NOTICE 'Added team_roles column to players table';
    ELSE
        RAISE NOTICE 'team_roles column already exists';
    END IF;
END $$;

-- Add pitcher_strategy column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'players' 
        AND column_name = 'pitcher_strategy'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.players 
        ADD COLUMN pitcher_strategy JSONB DEFAULT NULL;
        
        RAISE NOTICE 'Added pitcher_strategy column to players table';
    ELSE
        RAISE NOTICE 'pitcher_strategy column already exists';
    END IF;
END $$;

-- Migrate existing data from position_preferences to new columns
-- This extracts teamRoles and pitcherStrategy that were nested inside position_preferences
DO $$
DECLARE
    player_record RECORD;
    cleaned_preferences JSONB;
BEGIN
    -- Loop through all players that have teamRoles or pitcherStrategy in position_preferences
    FOR player_record IN 
        SELECT id, position_preferences 
        FROM public.players 
        WHERE position_preferences IS NOT NULL 
        AND (
            position_preferences ? 'teamRoles' 
            OR position_preferences ? 'pitcherStrategy'
        )
    LOOP
        -- Extract teamRoles if present
        IF player_record.position_preferences ? 'teamRoles' THEN
            UPDATE public.players 
            SET team_roles = player_record.position_preferences -> 'teamRoles'
            WHERE id = player_record.id;
        END IF;
        
        -- Extract pitcherStrategy if present
        IF player_record.position_preferences ? 'pitcherStrategy' THEN
            UPDATE public.players 
            SET pitcher_strategy = player_record.position_preferences -> 'pitcherStrategy'
            WHERE id = player_record.id;
        END IF;
        
        -- Clean up position_preferences by removing teamRoles and pitcherStrategy
        cleaned_preferences := player_record.position_preferences;
        cleaned_preferences := cleaned_preferences - 'teamRoles';
        cleaned_preferences := cleaned_preferences - 'pitcherStrategy';
        
        UPDATE public.players 
        SET position_preferences = cleaned_preferences
        WHERE id = player_record.id;
        
        RAISE NOTICE 'Migrated data for player ID: %', player_record.id;
    END LOOP;
END $$;

-- Add comments to document the structure
COMMENT ON COLUMN public.players.team_roles IS 
'JSONB object storing team role assignments for each position:
{
  "pitcher": "go-to" | "capable" | "fill-in" | "avoid",
  "catcher": "go-to" | "capable" | "fill-in" | "avoid",
  "firstBase": "go-to" | "capable" | "fill-in" | "avoid",
  "secondBase": "go-to" | "capable" | "fill-in" | "avoid",
  "thirdBase": "go-to" | "capable" | "fill-in" | "avoid",
  "shortstop": "go-to" | "capable" | "fill-in" | "avoid",
  "leftField": "go-to" | "capable" | "fill-in" | "avoid",
  "centerField": "go-to" | "capable" | "fill-in" | "avoid",
  "rightField": "go-to" | "capable" | "fill-in" | "avoid"
}

Role Definitions:
- go-to: Primary player for this position (highest priority)
- capable: Can play well at this position (medium priority)
- fill-in: Can play if needed (low priority)
- avoid: Should not play this position unless absolutely necessary';

COMMENT ON COLUMN public.players.pitcher_strategy IS 
'JSONB object storing pitcher-specific strategy settings:
{
  "maxConsecutiveInnings": number,
  "restInningsRequired": number,
  "preferredInnings": [number]
}

Used in competitive mode to manage pitcher rotation and fatigue.';

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'Team roles migration completed successfully';
    RAISE NOTICE 'Features added:';
    RAISE NOTICE '- Separate team_roles column for position assignments';
    RAISE NOTICE '- Separate pitcher_strategy column for pitcher management';
    RAISE NOTICE '- Migrated existing nested data from position_preferences';
    RAISE NOTICE '- Added performance indexes';
    RAISE NOTICE '- Cleaned up position_preferences to contain only preference data';
END $$;
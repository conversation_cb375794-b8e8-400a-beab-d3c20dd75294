-- Add series metadata columns to lineups table
ALTER TABLE lineups 
ADD COLUMN IF NOT EXISTS series_title TEXT,
ADD COLUMN IF NOT EXISTS series_id TEXT,
ADD COLUMN IF NOT EXISTS game_number INTEGER,
ADD COLUMN IF NOT EXISTS total_games_in_series INTEGER;

-- Add indexes for better query performance on series data
CREATE INDEX IF NOT EXISTS idx_lineups_series_id ON lineups(series_id);
CREATE INDEX IF NOT EXISTS idx_lineups_series_title ON lineups(series_title);

-- Add comment to document the purpose
COMMENT ON COLUMN lineups.series_title IS 'Title of the series this game belongs to (e.g., "Weekend Tournament")';
COMMENT ON COLUMN lineups.series_id IS 'Unique identifier for grouping games in a series';
COMMENT ON COLUMN lineups.game_number IS 'The sequence number of this game within the series (e.g., 1, 2, 3)';
COMMENT ON COLUMN lineups.total_games_in_series IS 'Total number of games in this series';
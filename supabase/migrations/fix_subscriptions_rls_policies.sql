-- Fix missing RLS policies for subscriptions table
-- This migration adds the missing INSERT, UPDATE, and DELETE policies for the subscriptions table

-- Add missing INSERT policy
CREATE POLICY "Users can insert their own subscriptions" ON public.subscriptions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Add missing UPDATE policy  
CREATE POLICY "Users can update their own subscriptions" ON public.subscriptions
  FOR UPDATE USING (auth.uid() = user_id);

-- Add missing DELETE policy
CREATE POLICY "Users can delete their own subscriptions" ON public.subscriptions
  FOR DELETE USING (auth.uid() = user_id);

-- Add comment for documentation
COMMENT ON TABLE public.subscriptions IS 
'Subscription records for user payments. All operations (SELECT/INSERT/UPDATE/DELETE) are restricted by RLS to ensure users can only access their own subscription data.';
-- Remove "custom" rotation method and update existing records
-- This migration removes the non-functional "custom" rotation method option
-- and updates any existing teams using it to use "standard" instead

-- Update any existing rotation rules that use non-standard methods to use "standard"
-- This includes "custom" (non-functional) and any other legacy values
UPDATE public.rotation_rules
SET rotation_method = 'standard'
WHERE rotation_method NOT IN ('standard', 'manual');

-- Add a comment to document the change
COMMENT ON COLUMN public.rotation_rules.rotation_method IS 
'Rotation method for lineup generation. Valid values: "standard" (automatic rotation with configurable rules), "manual" (no automatic rotation). The "custom" option was removed as it was non-functional.';

-- Log the number of records updated
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    -- Get count of records that were updated (this will be 0 since we already updated them above)
    SELECT COUNT(*) INTO updated_count 
    FROM public.rotation_rules 
    WHERE rotation_method = 'custom';
    
    -- Log the result
    RAISE NOTICE 'Migration completed. Updated % rotation rules from "custom" to "standard"', 
        (SELECT COUNT(*) FROM public.rotation_rules WHERE rotation_method = 'standard');
END $$;

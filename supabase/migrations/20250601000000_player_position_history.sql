-- Create player position history table to track positions across games
CREATE TABLE IF NOT EXISTS public.player_position_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  player_id UUID REFERENCES public.players(id) ON DELETE CASCADE,
  lineup_id UUID REFERENCES public.lineups(id) ON DELETE CASCADE,
  inning_number INTEGER NOT NULL,
  position TEXT NOT NULL, -- The position name (e.g., "pitcher", "leftField", "bench1")
  game_date DATE NOT NULL, -- Denormalized from lineup for easier querying
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Add indexes for faster querying
CREATE INDEX IF NOT EXISTS idx_player_position_history_player_id ON public.player_position_history(player_id);
CREATE INDEX IF NOT EXISTS idx_player_position_history_lineup_id ON public.player_position_history(lineup_id);
CREATE INDEX IF NOT EXISTS idx_player_position_history_game_date ON public.player_position_history(game_date);

-- Add RLS to player_position_history table
ALTER TABLE public.player_position_history ENABLE ROW LEVEL SECURITY;

-- Create policies for player_position_history
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can view their own player position history'
    ) THEN
        CREATE POLICY "Users can view their own player position history" ON public.player_position_history
        FOR SELECT USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can insert their own player position history'
    ) THEN
        CREATE POLICY "Users can insert their own player position history" ON public.player_position_history
        FOR INSERT WITH CHECK (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can update their own player position history'
    ) THEN
        CREATE POLICY "Users can update their own player position history" ON public.player_position_history
        FOR UPDATE USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can delete their own player position history'
    ) THEN
        CREATE POLICY "Users can delete their own player position history" ON public.player_position_history
        FOR DELETE USING (auth.uid() = user_id);
    END IF;
END
$$;

-- Migration to add position preferences to existing players table
-- This migration adds the position_preferences JSONB column to store player position preferences

-- Add position_preferences column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'players' 
        AND column_name = 'position_preferences'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.players 
        ADD COLUMN position_preferences JSONB DEFAULT '{}';
        
        -- Update existing players to have empty preferences object
        UPDATE public.players 
        SET position_preferences = '{}' 
        WHERE position_preferences IS NULL;
        
        RAISE NOTICE 'Added position_preferences column to players table';
    ELSE
        RAISE NOTICE 'position_preferences column already exists';
    END IF;
END $$;

-- Create an index on position_preferences for better query performance
CREATE INDEX IF NOT EXISTS idx_players_position_preferences 
ON public.players USING GIN (position_preferences);

-- Add a comment to document the structure
COMMENT ON COLUMN public.players.position_preferences IS 
'JSONB object storing position preferences with structure: 
{
  "pitcher": "preferred" | "secondary" | "avoid" | "neutral",
  "catcher": "preferred" | "secondary" | "avoid" | "neutral",
  "firstBase": "preferred" | "secondary" | "avoid" | "neutral",
  "secondBase": "preferred" | "secondary" | "avoid" | "neutral",
  "thirdBase": "preferred" | "secondary" | "avoid" | "neutral",
  "shortstop": "preferred" | "secondary" | "avoid" | "neutral",
  "leftField": "preferred" | "secondary" | "avoid" | "neutral",
  "centerField": "preferred" | "secondary" | "avoid" | "neutral",
  "rightField": "preferred" | "secondary" | "avoid" | "neutral",
  "leftCenter": "preferred" | "secondary" | "avoid" | "neutral",
  "rightCenter": "preferred" | "secondary" | "avoid" | "neutral",
  "middleInfield": "preferred" | "secondary" | "avoid" | "neutral"
}';

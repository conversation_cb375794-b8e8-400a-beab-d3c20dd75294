-- Add admin delete policies for user management
-- This allows admin users to delete data belonging to other users

-- Drop existing delete policies if they exist
DROP POLICY IF EXISTS "Admin users can delete any team" ON teams;
DROP POLICY IF EXISTS "Admin users can delete any subscription" ON subscriptions;
DROP POLICY IF EXISTS "Admin users can delete any profile" ON profiles;

-- Create new delete policies for admin users
-- Admins are identified by email address
CREATE POLICY "Admin users can delete any team" ON teams
  FOR DELETE TO authenticated
  USING (
    auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

CREATE POLICY "Admin users can delete any subscription" ON subscriptions
  FOR DELETE TO authenticated
  USING (
    auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

CREATE POLICY "Admin users can delete any profile" ON profiles
  FOR DELETE TO authenticated
  USING (
    auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

-- Also add policies for admin users to delete related data
DROP POLICY IF EXISTS "Admin users can delete any player" ON players;
DROP POLICY IF EXISTS "Admin users can delete any lineup" ON lineups;
DROP POLICY IF EXISTS "Admin users can delete any rotation_rule" ON rotation_rules;

CREATE POLICY "Admin users can delete any player" ON players
  FOR DELETE TO authenticated
  USING (
    auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

CREATE POLICY "Admin users can delete any lineup" ON lineups
  FOR DELETE TO authenticated
  USING (
    auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

CREATE POLICY "Admin users can delete any rotation_rule" ON rotation_rules
  FOR DELETE TO authenticated
  USING (
    auth.email() IN ('<EMAIL>', '<EMAIL>')
  );
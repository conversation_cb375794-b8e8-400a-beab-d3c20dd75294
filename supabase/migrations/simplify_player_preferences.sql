-- Migration to simplify player preferences system
-- Replace complex preferred/secondary/avoid system with simple 1-5 rating scale
-- Add direct star player designation

-- Add new columns for simplified system
ALTER TABLE public.players 
ADD COLUMN IF NOT EXISTS position_ratings JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS is_star_player B<PERSON><PERSON>EAN DEFAULT false;

-- Create index on new position_ratings column for better query performance
CREATE INDEX IF NOT EXISTS idx_players_position_ratings 
ON public.players USING GIN (position_ratings);

-- Create index on is_star_player for competitive mode queries
CREATE INDEX IF NOT EXISTS idx_players_is_star_player 
ON public.players (is_star_player);

-- Add comments to document the new structure
COMMENT ON COLUMN public.players.position_ratings IS 
'JSONB object storing simplified position ratings with 1-5 scale:
{
  "pitcher": 1-5,
  "catcher": 1-5,
  "firstBase": 1-5,
  "secondBase": 1-5,
  "thirdBase": 1-5,
  "shortstop": 1-5,
  "leftField": 1-5,
  "centerField": 1-5,
  "rightField": 1-5,
  "leftCenter": 1-5,
  "rightCenter": 1-5,
  "middleInfield": 1-5
}

Rating Scale:
5 = Elite Player (best on team)
4 = Strong Player (above average)
3 = Average Player (solid contributor)
2 = Developing Player (learning/improving)
1 = Beginner Player (needs support)

Null/missing values indicate no rating set (neutral).';

COMMENT ON COLUMN public.players.is_star_player IS 
'Boolean flag indicating if player is designated as a star player for competitive mode.
Star players get priority for key positions and extended rotation delays.
Independent of skill ratings - coaches can designate strategic star players.';

-- Function to migrate existing preferences to new rating system
CREATE OR REPLACE FUNCTION migrate_preferences_to_ratings()
RETURNS void AS $$
DECLARE
    player_record RECORD;
    position_key TEXT;
    preference_value JSONB;
    normalized_pref JSONB;
    new_ratings JSONB := '{}';
    rating_value INTEGER;
BEGIN
    -- Loop through all players with existing preferences
    FOR player_record IN 
        SELECT id, position_preferences 
        FROM public.players 
        WHERE position_preferences IS NOT NULL 
        AND position_preferences != '{}'
        AND (position_ratings IS NULL OR position_ratings = '{}')
    LOOP
        new_ratings := '{}';
        
        -- Process each position
        FOR position_key IN 
            SELECT unnest(ARRAY['pitcher', 'catcher', 'firstBase', 'secondBase', 'thirdBase', 'shortstop', 
                               'leftField', 'centerField', 'rightField', 'leftCenter', 'rightCenter', 'middleInfield'])
        LOOP
            preference_value := player_record.position_preferences -> position_key;
            
            IF preference_value IS NOT NULL THEN
                -- Normalize the preference (handle both string and object formats)
                IF jsonb_typeof(preference_value) = 'string' THEN
                    normalized_pref := jsonb_build_object('level', preference_value);
                ELSE
                    normalized_pref := preference_value;
                END IF;
                
                -- Convert to rating scale
                CASE (normalized_pref ->> 'level')
                    WHEN 'preferred' THEN
                        -- Preferred with rank 1-2 = 5, rank 3-4 = 4, rank 5+ = 4
                        rating_value := CASE 
                            WHEN (normalized_pref ->> 'rank')::INTEGER <= 2 THEN 5
                            WHEN (normalized_pref ->> 'rank')::INTEGER <= 4 THEN 4
                            ELSE 4
                        END;
                        -- If no rank specified, default to 4
                        IF normalized_pref ->> 'rank' IS NULL THEN
                            rating_value := 4;
                        END IF;
                    WHEN 'secondary' THEN
                        -- Secondary = 3 (average)
                        rating_value := 3;
                    WHEN 'avoid' THEN
                        -- Avoid = 1 (beginner/needs support)
                        rating_value := 1;
                    ELSE
                        -- Neutral or unknown = no rating (skip)
                        rating_value := NULL;
                END CASE;
                
                -- Add rating to new_ratings if not null
                IF rating_value IS NOT NULL THEN
                    new_ratings := new_ratings || jsonb_build_object(position_key, rating_value);
                END IF;
            END IF;
        END LOOP;
        
        -- Update the player with new ratings
        UPDATE public.players 
        SET position_ratings = new_ratings
        WHERE id = player_record.id;
        
        RAISE NOTICE 'Migrated preferences for player ID: %', player_record.id;
    END LOOP;
    
    RAISE NOTICE 'Migration completed successfully';
END;
$$ LANGUAGE plpgsql;

-- Function to validate position ratings
CREATE OR REPLACE FUNCTION validate_position_ratings(ratings JSONB)
RETURNS BOOLEAN AS $$
DECLARE
    position_key TEXT;
    rating_value JSONB;
BEGIN
    -- Check if ratings is a valid JSON object
    IF jsonb_typeof(ratings) != 'object' THEN
        RETURN FALSE;
    END IF;
    
    -- Validate each position rating
    FOR position_key, rating_value IN SELECT * FROM jsonb_each(ratings)
    LOOP
        -- Check if position key is valid
        IF position_key NOT IN ('pitcher', 'catcher', 'firstBase', 'secondBase', 'thirdBase', 'shortstop',
                               'leftField', 'centerField', 'rightField', 'leftCenter', 'rightCenter', 'middleInfield') THEN
            RETURN FALSE;
        END IF;
        
        -- Check if rating value is valid (1-5)
        IF jsonb_typeof(rating_value) != 'number' OR 
           (rating_value::INTEGER < 1 OR rating_value::INTEGER > 5) THEN
            RETURN FALSE;
        END IF;
    END LOOP;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Add constraint to validate position ratings
ALTER TABLE public.players 
ADD CONSTRAINT check_position_ratings_valid 
CHECK (position_ratings IS NULL OR validate_position_ratings(position_ratings));

-- Grant permissions
GRANT EXECUTE ON FUNCTION migrate_preferences_to_ratings() TO authenticated;
GRANT EXECUTE ON FUNCTION validate_position_ratings(JSONB) TO authenticated;

-- Run the migration (commented out for safety - run manually when ready)
-- SELECT migrate_preferences_to_ratings();

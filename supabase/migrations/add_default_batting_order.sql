-- Add default batting order to teams table
-- This allows coaches to set a default batting order for their entire roster
-- that can be automatically applied to new lineups based on attendance

ALTER TABLE public.teams 
ADD COLUMN default_batting_order JSONB DEFAULT NULL;

-- Comment to explain the column
COMMENT ON COLUMN public.teams.default_batting_order IS 'Default batting order as array of player IDs for the entire roster';
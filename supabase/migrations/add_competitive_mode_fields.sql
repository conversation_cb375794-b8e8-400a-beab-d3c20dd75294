-- Add competitive mode fields to rotation_rules table
-- These fields enable competitive lineup generation with key position prioritization

-- Add competitive mode columns
ALTER TABLE public.rotation_rules 
ADD COLUMN IF NOT EXISTS competitive_mode BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS competitive_min_playing_time INTEGER DEFAULT 50,
ADD COLUMN IF NOT EXISTS key_positions JSONB DEFAULT '["pitcher", "catcher", "shortstop"]',
ADD COLUMN IF NOT EXISTS star_player_rotation_delay INTEGER DEFAULT 1;

-- Update existing records to have default values
UPDATE public.rotation_rules 
SET 
  competitive_mode = false,
  competitive_min_playing_time = 50,
  key_positions = '["pitcher", "catcher", "shortstop"]',
  star_player_rotation_delay = 1
WHERE 
  competitive_mode IS NULL 
  OR competitive_min_playing_time IS NULL 
  OR key_positions IS NULL 
  OR star_player_rotation_delay IS NULL;

-- Add comments to document the new columns
COMMENT ON COLUMN public.rotation_rules.competitive_mode IS 
'Boolean flag to enable competitive mode - prioritizes optimal player positioning over equal playing time';

COMMENT ON COLUMN public.rotation_rules.competitive_min_playing_time IS 
'Minimum percentage of total innings each player must play in competitive mode (0-100)';

COMMENT ON COLUMN public.rotation_rules.key_positions IS 
'JSONB array of position keys that are considered most critical in competitive mode. 
Example: ["pitcher", "catcher", "shortstop", "firstBase"]
Valid positions: pitcher, catcher, firstBase, secondBase, thirdBase, shortstop, leftField, centerField, rightField, leftCenter, rightCenter, middleInfield';

COMMENT ON COLUMN public.rotation_rules.star_player_rotation_delay IS 
'Number of extra innings star players stay in key positions before rotation in competitive mode (0-3)';

-- Create index on competitive_mode for faster queries
CREATE INDEX IF NOT EXISTS idx_rotation_rules_competitive_mode 
ON public.rotation_rules (competitive_mode);

-- Create index on key_positions for GIN queries
CREATE INDEX IF NOT EXISTS idx_rotation_rules_key_positions 
ON public.rotation_rules USING GIN (key_positions);

-- Add validation function for key_positions
CREATE OR REPLACE FUNCTION validate_key_positions(positions JSONB)
RETURNS BOOLEAN AS $$
DECLARE
    position_value TEXT;
    valid_positions TEXT[] := ARRAY[
        'pitcher', 'catcher', 'firstBase', 'secondBase', 'thirdBase', 'shortstop',
        'leftField', 'centerField', 'rightField', 'leftCenter', 'rightCenter', 'middleInfield'
    ];
BEGIN
    -- Check if positions is a valid JSON array
    IF jsonb_typeof(positions) != 'array' THEN
        RETURN FALSE;
    END IF;
    
    -- Check each position in the array
    FOR position_value IN SELECT jsonb_array_elements_text(positions)
    LOOP
        IF position_value NOT = ANY(valid_positions) THEN
            RETURN FALSE;
        END IF;
    END LOOP;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Add constraint to validate key_positions
ALTER TABLE public.rotation_rules 
ADD CONSTRAINT check_key_positions_valid 
CHECK (key_positions IS NULL OR validate_key_positions(key_positions));

-- Add constraint to validate competitive_min_playing_time range
ALTER TABLE public.rotation_rules 
ADD CONSTRAINT check_competitive_min_playing_time_range 
CHECK (competitive_min_playing_time >= 0 AND competitive_min_playing_time <= 100);

-- Add constraint to validate star_player_rotation_delay range
ALTER TABLE public.rotation_rules 
ADD CONSTRAINT check_star_player_rotation_delay_range 
CHECK (star_player_rotation_delay >= 0 AND star_player_rotation_delay <= 5);

-- Grant permissions
GRANT EXECUTE ON FUNCTION validate_key_positions(JSONB) TO authenticated;

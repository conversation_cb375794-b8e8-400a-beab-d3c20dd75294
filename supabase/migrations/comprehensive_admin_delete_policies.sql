-- Comprehensive Admin Delete Policies
-- This migration adds DELETE policies for admin users on all tables

-- First, let's check and drop any existing admin delete policies
DROP POLICY IF EXISTS "Admin users can delete any team" ON teams;
DROP POLICY IF EXISTS "Admin users can delete any subscription" ON subscriptions;
DROP POLICY IF EXISTS "Admin users can delete any profile" ON profiles;
DROP POLICY IF EXISTS "Admin users can delete any player" ON players;
DROP POLICY IF EXISTS "Admin users can delete any lineup" ON lineups;
DROP POLICY IF EXISTS "Admin users can delete any rotation_rule" ON rotation_rules;
DROP POLICY IF EXISTS "Admin users can delete any lineup_inning" ON lineup_innings;
DROP POLICY IF EXISTS "Admin users can delete any lineup_attendance" ON lineup_attendance;
DROP POLICY IF EXISTS "Admin users can delete any admin_audit_log" ON admin_audit_logs;

-- Create admin delete policies for all tables
-- Using auth.email() to check admin status

-- Teams table
CREATE POLICY "Admin users can delete any team" ON teams
  FOR DELETE 
  USING (
    auth.email() IN ('<EMAIL>', '<EMAIL>')
    OR auth.uid() = user_id  -- Also allow users to delete their own teams
  );

-- Subscriptions table (this is the one causing issues)
CREATE POLICY "Admin users can delete any subscription" ON subscriptions
  FOR DELETE 
  USING (
    auth.email() IN ('<EMAIL>', '<EMAIL>')
    OR auth.uid() = user_id  -- Also allow users to delete their own subscriptions
  );

-- Profiles table
CREATE POLICY "Admin users can delete any profile" ON profiles
  FOR DELETE 
  USING (
    auth.email() IN ('<EMAIL>', '<EMAIL>')
    OR auth.uid() = id  -- Also allow users to delete their own profile
  );

-- Players table
CREATE POLICY "Admin users can delete any player" ON players
  FOR DELETE 
  USING (
    auth.email() IN ('<EMAIL>', '<EMAIL>')
    OR EXISTS (
      SELECT 1 FROM teams 
      WHERE teams.id = players.team_id 
      AND teams.user_id = auth.uid()
    )  -- Also allow users to delete their own players
  );

-- Lineups table
CREATE POLICY "Admin users can delete any lineup" ON lineups
  FOR DELETE 
  USING (
    auth.email() IN ('<EMAIL>', '<EMAIL>')
    OR EXISTS (
      SELECT 1 FROM teams 
      WHERE teams.id = lineups.team_id 
      AND teams.user_id = auth.uid()
    )  -- Also allow users to delete their own lineups
  );

-- Rotation rules table
CREATE POLICY "Admin users can delete any rotation_rule" ON rotation_rules
  FOR DELETE 
  USING (
    auth.email() IN ('<EMAIL>', '<EMAIL>')
    OR auth.uid() = user_id  -- Also allow users to delete their own rules
  );

-- Lineup innings table
CREATE POLICY "Admin users can delete any lineup_inning" ON lineup_innings
  FOR DELETE 
  USING (
    auth.email() IN ('<EMAIL>', '<EMAIL>')
    OR EXISTS (
      SELECT 1 FROM lineups 
      JOIN teams ON teams.id = lineups.team_id 
      WHERE lineups.id = lineup_innings.lineup_id 
      AND teams.user_id = auth.uid()
    )  -- Also allow users to delete their own innings
  );

-- Lineup attendance table
CREATE POLICY "Admin users can delete any lineup_attendance" ON lineup_attendance
  FOR DELETE 
  USING (
    auth.email() IN ('<EMAIL>', '<EMAIL>')
    OR EXISTS (
      SELECT 1 FROM lineups 
      JOIN teams ON teams.id = lineups.team_id 
      WHERE lineups.id = lineup_attendance.lineup_id 
      AND teams.user_id = auth.uid()
    )  -- Also allow users to delete their own attendance
  );

-- Admin audit logs (only admins can delete)
CREATE POLICY "Admin users can delete audit logs" ON admin_audit_logs
  FOR DELETE 
  USING (
    auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

-- Also ensure admin users can SELECT from all tables for the admin panel
-- Update existing select policies if needed
DROP POLICY IF EXISTS "Admin users can view all subscriptions" ON subscriptions;
CREATE POLICY "Admin users can view all subscriptions" ON subscriptions
  FOR SELECT
  USING (
    auth.email() IN ('<EMAIL>', '<EMAIL>')
    OR auth.uid() = user_id
  );

-- Check if the policies were created successfully
DO $$
BEGIN
  RAISE NOTICE 'Admin delete policies have been created successfully!';
  RAISE NOTICE 'Admin users can now delete data from all tables.';
END $$;
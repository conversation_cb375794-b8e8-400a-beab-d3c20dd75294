-- Add rotation_settings column to lineups table
-- This column will store game-specific rotation settings as <PERSON><PERSON><PERSON><PERSON>

ALTER TABLE public.lineups 
ADD COLUMN IF NOT EXISTS rotation_settings JSONB;

-- Add a comment to document the column
COMMENT ON COLUMN public.lineups.rotation_settings IS 'Game-specific rotation settings stored as JSON: {limitBenchTime: boolean, rotateLineupEvery: number, rotatePitcherEvery: number}';

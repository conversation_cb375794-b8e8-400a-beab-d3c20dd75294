-- Add coaching_style column to teams table
ALTER TABLE teams ADD COLUMN IF NOT EXISTS coaching_style INTEGER DEFAULT 50;

-- Add comment explaining the column
COMMENT ON COLUMN teams.coaching_style IS 'Coaching style slider value 0-100. 0=Pure recreational, 50=Balanced, 100=Tournament mode';

-- Migration for existing teams based on their current settings
UPDATE teams 
SET coaching_style = CASE 
  WHEN rotation_rules->>'competitiveMode' = 'true' THEN 75
  WHEN rotation_rules->>'equalPlayingTime' = 'true' THEN 25  
  ELSE 50
END
WHERE coaching_style IS NULL;
-- Add competitive mode columns to rotation_rules table
ALTER TABLE rotation_rules
ADD COLUMN IF NOT EXISTS competitive_mode BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS competitive_min_playing_time INTEGER DEFAULT 50,
ADD COLUMN IF NOT EXISTS key_positions TEXT[] DEFAULT ARRAY['pitcher', 'catcher', 'shortstop'],
ADD COLUMN IF NOT EXISTS star_player_rotation_delay INTEGER DEFAULT 1;

-- Add comment to explain the columns
COMMENT ON COLUMN rotation_rules.competitive_mode IS 'Whether to use competitive mode for lineup generation';
COMMENT ON COLUMN rotation_rules.competitive_min_playing_time IS 'Minimum playing time percentage for all players in competitive mode';
COMMENT ON COLUMN rotation_rules.key_positions IS 'Key positions that get priority assignments in competitive mode';
COMMENT ON COLUMN rotation_rules.star_player_rotation_delay IS 'Number of innings before star players can be rotated out of key positions';
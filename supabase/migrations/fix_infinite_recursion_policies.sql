-- Fix infinite recursion in profiles table RLS policies
-- The admin policies were referencing the profiles table within their own conditions

-- First, drop the problematic policies
DROP POLICY IF EXISTS "Allow admins to read all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Allow admins to update all profiles" ON public.profiles;

-- <PERSON>reate fixed admin policies using direct email check instead of table lookup
-- This avoids the infinite recursion by checking auth.email() directly
CREATE POLICY "Allow admins to read all profiles"
ON public.profiles FOR SELECT
USING (
    auth.email() = '<EMAIL>'
);

CREATE POLICY "Allow admins to update all profiles"
ON public.profiles FOR UPDATE
USING (
    auth.email() = '<EMAIL>'
);

-- Add comment for documentation
COMMENT ON TABLE public.profiles IS 
'User profiles with admin access. Admin policies use direct email check to avoid infinite recursion in RLS evaluation.';
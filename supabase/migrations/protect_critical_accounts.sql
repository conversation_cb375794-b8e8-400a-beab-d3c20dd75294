-- Protect critical accounts from deletion at the database level
-- This adds a check constraint and trigger to prevent deletion of protected accounts

-- First, add a protected flag to profiles table if it doesn't exist
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS is_protected BOOLEAN DEFAULT FALSE;

-- Mark critical accounts as protected
UPDATE profiles 
SET is_protected = TRUE 
WHERE email IN ('<EMAIL>', '<EMAIL>');

-- Create a function to prevent deletion of protected profiles
CREATE OR REPLACE FUNCTION prevent_protected_profile_deletion()
RETURNS TRIGGER AS $$
BEGIN
  IF OLD.is_protected = TRUE THEN
    RAISE EXCEPTION 'Cannot delete protected account: %', OLD.email;
  END IF;
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for profiles table
DROP TRIGGER IF EXISTS protect_profiles_trigger ON profiles;
CREATE TRIGGER protect_profiles_trigger
BEFORE DELETE ON profiles
FOR EACH ROW
EXECUTE FUNCTION prevent_protected_profile_deletion();

-- Also protect related data by checking user email
CREATE OR REPLACE FUNCTION prevent_protected_user_data_deletion()
RETURNS TRIGGER AS $$
DECLARE
  user_email TEXT;
  is_protected BOOLEAN;
BEGIN
  -- Get the email of the user whose data is being deleted
  SELECT email, is_protected INTO user_email, is_protected
  FROM profiles
  WHERE id = OLD.user_id;
  
  -- Check if it's a protected account
  IF is_protected = TRUE OR user_email IN ('<EMAIL>', '<EMAIL>') THEN
    RAISE EXCEPTION 'Cannot delete data for protected account: %', user_email;
  END IF;
  
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for related tables
DROP TRIGGER IF EXISTS protect_teams_trigger ON teams;
CREATE TRIGGER protect_teams_trigger
BEFORE DELETE ON teams
FOR EACH ROW
EXECUTE FUNCTION prevent_protected_user_data_deletion();

DROP TRIGGER IF EXISTS protect_subscriptions_trigger ON subscriptions;
CREATE TRIGGER protect_subscriptions_trigger
BEFORE DELETE ON subscriptions
FOR EACH ROW
EXECUTE FUNCTION prevent_protected_user_data_deletion();

-- Add a comment to document the protection
COMMENT ON COLUMN profiles.is_protected IS 'Prevents deletion of critical accounts like admin and demo users';

-- Verify the protection is in place
DO $$
BEGIN
  RAISE NOTICE 'Protected accounts have been configured:';
  RAISE NOTICE '- <EMAIL> (Admin)';
  RAISE NOTICE '- <EMAIL> (Demo)';
  RAISE NOTICE 'These accounts cannot be deleted at the database level.';
END $$;
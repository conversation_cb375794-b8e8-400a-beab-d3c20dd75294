-- Fix missing user records causing 404/406 errors
-- This migration ensures all authenticated users have proper profiles and subscription records

-- First, add profiles for any users missing them
INSERT INTO public.profiles (id, full_name, role, is_admin, created_at, updated_at)
SELECT 
  auth.users.id,
  auth.users.email as full_name,
  'user' as role,
  CASE 
    WHEN auth.users.email = '<EMAIL>' THEN true
    ELSE false
  END as is_admin,
  auth.users.created_at,
  NOW()
FROM auth.users
LEFT JOIN public.profiles ON auth.users.id = public.profiles.id
WHERE public.profiles.id IS NULL;

-- Create subscription records for admin users who should have paid access
INSERT INTO public.subscriptions (user_id, stripe_session_id, is_paid, amount, currency, payment_date, created_at, updated_at)
SELECT 
  auth.users.id,
  'admin_override' as stripe_session_id,
  true as is_paid,
  2900 as amount, -- $29.00 in cents
  'usd' as currency,
  auth.users.created_at as payment_date,
  auth.users.created_at,
  NOW()
FROM auth.users
LEFT JOIN public.subscriptions ON auth.users.id = public.subscriptions.user_id
WHERE public.subscriptions.user_id IS NULL
  AND auth.users.email = '<EMAIL>';

-- Add comment for documentation
COMMENT ON TABLE public.profiles IS 
'User profiles with admin flags. All authenticated users must have a profile record.';

COMMENT ON TABLE public.subscriptions IS 
'Subscription records for user payments. Paid users have is_paid=true with valid payment details.';
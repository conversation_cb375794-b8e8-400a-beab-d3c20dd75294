
-- This file contains the SQL schema for your Supabase database
-- After connecting to Supabase, you can run these migrations

-- Enable the UUID extension for generating unique IDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- USERS table is managed by Supabase Auth (auth.users)

-- TEAMS table to replace the team state in context
CREATE TABLE public.teams (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Add Row Level Security (RLS) to teams table
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own teams" ON public.teams
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own teams" ON public.teams
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own teams" ON public.teams
  FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own teams" ON public.teams
  FOR DELETE USING (auth.uid() = user_id);

-- PLAYERS table to replace the players array in context
CREATE TABLE public.players (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
  pitcher_restriction BOOLEAN DEFAULT false,
  catcher_restriction BOOLEAN DEFAULT false,
  first_base_restriction BOOLEAN DEFAULT false,
  other_restriction TEXT,
  position_preferences JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Add RLS to players table
ALTER TABLE public.players ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own players" ON public.players
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own players" ON public.players
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own players" ON public.players
  FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own players" ON public.players
  FOR DELETE USING (auth.uid() = user_id);

-- LINEUPS table to replace the lineups array in context
CREATE TABLE public.lineups (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  game_date DATE NOT NULL,
  team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Add RLS to lineups table
ALTER TABLE public.lineups ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own lineups" ON public.lineups
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own lineups" ON public.lineups
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own lineups" ON public.lineups
  FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own lineups" ON public.lineups
  FOR DELETE USING (auth.uid() = user_id);

-- LINEUP_INNINGS table to store inning data for lineups
CREATE TABLE public.lineup_innings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  lineup_id UUID REFERENCES public.lineups(id) ON DELETE CASCADE,
  inning_number INTEGER NOT NULL,
  positions JSONB NOT NULL, -- Store the positions as JSON data
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  UNIQUE(lineup_id, inning_number)
);

-- Add RLS to lineup_innings table
ALTER TABLE public.lineup_innings ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own lineup innings" ON public.lineup_innings
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own lineup innings" ON public.lineup_innings
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own lineup innings" ON public.lineup_innings
  FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own lineup innings" ON public.lineup_innings
  FOR DELETE USING (auth.uid() = user_id);

-- LINEUP_ATTENDANCE table to track player attendance for lineups
CREATE TABLE public.lineup_attendance (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  lineup_id UUID REFERENCES public.lineups(id) ON DELETE CASCADE,
  player_id UUID REFERENCES public.players(id) ON DELETE CASCADE,
  is_present BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  UNIQUE(lineup_id, player_id)
);

-- Add RLS to lineup_attendance table
ALTER TABLE public.lineup_attendance ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own lineup attendance" ON public.lineup_attendance
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own lineup attendance" ON public.lineup_attendance
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own lineup attendance" ON public.lineup_attendance
  FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own lineup attendance" ON public.lineup_attendance
  FOR DELETE USING (auth.uid() = user_id);

-- BATTING_ORDER table to track the batting order for lineups
CREATE TABLE public.batting_orders (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  lineup_id UUID REFERENCES public.lineups(id) ON DELETE CASCADE,
  player_order JSONB NOT NULL, -- Array of player IDs in batting order
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Add RLS to batting_order table
ALTER TABLE public.batting_orders ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own batting orders" ON public.batting_orders
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own batting orders" ON public.batting_orders
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own batting orders" ON public.batting_orders
  FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own batting orders" ON public.batting_orders
  FOR DELETE USING (auth.uid() = user_id);

-- ROTATION_RULES table to store team rotation rules
CREATE TABLE public.rotation_rules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
  rotation_method TEXT NOT NULL DEFAULT 'standard',
  equal_playing_time BOOLEAN DEFAULT true,
  rotate_players BOOLEAN DEFAULT true,
  respect_position_lockouts BOOLEAN DEFAULT true,
  allow_pitcher_rotation BOOLEAN DEFAULT false,
  allow_catcher_rotation BOOLEAN DEFAULT true,
  prioritize_outfield_rotation BOOLEAN DEFAULT true,
  limit_bench_time BOOLEAN DEFAULT true,
  rotate_lineup_every INTEGER DEFAULT 1,
  rotate_pitcher_every INTEGER DEFAULT 2,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Add RLS to rotation_rules table
ALTER TABLE public.rotation_rules ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own rotation rules" ON public.rotation_rules
  FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert their own rotation rules" ON public.rotation_rules
  FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update their own rotation rules" ON public.rotation_rules
  FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete their own rotation rules" ON public.rotation_rules
  FOR DELETE USING (auth.uid() = user_id);

-- SUBSCRIPTIONS table to track user payments
CREATE TABLE public.subscriptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  stripe_session_id TEXT,
  is_paid BOOLEAN DEFAULT false,
  amount INTEGER,
  currency TEXT DEFAULT 'usd',
  payment_date TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add RLS to subscriptions table
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own subscriptions" ON public.subscriptions
  FOR SELECT USING (auth.uid() = user_id);

-- Add admin-specific RLS policies to allow admin users to bypass user restrictions
-- This migration creates policies that allow admin users (<EMAIL>) to access all data

-- Create admin audit logs table for tracking admin actions
CREATE TABLE IF NOT EXISTS public.admin_audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  admin_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  action TEXT NOT NULL,
  entity_type TEXT NOT NULL,
  entity_id UUID,
  details JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Enable RLS on admin audit logs
ALTER TABLE public.admin_audit_logs ENABLE ROW LEVEL SECURITY;

-- Admin users can view all audit logs
CREATE POLICY "Allow admins to read all audit logs" ON public.admin_audit_logs
  FOR SELECT USING (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

-- Admin users can insert audit logs
CREATE POLICY "Allow admins to insert audit logs" ON public.admin_audit_logs
  FOR INSERT WITH CHECK (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

-- PROFILES TABLE ADMIN POLICIES
-- Drop existing admin policies to avoid conflicts
DROP POLICY IF EXISTS "Allow admins to read all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Allow admins to update all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Allow admins to insert all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Allow admins to delete all profiles" ON public.profiles;

-- Create new admin policies for profiles
CREATE POLICY "Allow admins to read all profiles" 
  ON public.profiles FOR SELECT 
  USING (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

CREATE POLICY "Allow admins to update all profiles" 
  ON public.profiles FOR UPDATE 
  USING (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

CREATE POLICY "Allow admins to insert all profiles" 
  ON public.profiles FOR INSERT 
  WITH CHECK (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

CREATE POLICY "Allow admins to delete all profiles" 
  ON public.profiles FOR DELETE 
  USING (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

-- TEAMS TABLE ADMIN POLICIES
CREATE POLICY "Allow admins to read all teams" 
  ON public.teams FOR SELECT 
  USING (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

CREATE POLICY "Allow admins to update all teams" 
  ON public.teams FOR UPDATE 
  USING (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

CREATE POLICY "Allow admins to insert all teams" 
  ON public.teams FOR INSERT 
  WITH CHECK (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

CREATE POLICY "Allow admins to delete all teams" 
  ON public.teams FOR DELETE 
  USING (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

-- PLAYERS TABLE ADMIN POLICIES
CREATE POLICY "Allow admins to read all players" 
  ON public.players FOR SELECT 
  USING (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

CREATE POLICY "Allow admins to update all players" 
  ON public.players FOR UPDATE 
  USING (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

CREATE POLICY "Allow admins to insert all players" 
  ON public.players FOR INSERT 
  WITH CHECK (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

CREATE POLICY "Allow admins to delete all players" 
  ON public.players FOR DELETE 
  USING (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

-- LINEUPS TABLE ADMIN POLICIES
CREATE POLICY "Allow admins to read all lineups" 
  ON public.lineups FOR SELECT 
  USING (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

CREATE POLICY "Allow admins to update all lineups" 
  ON public.lineups FOR UPDATE 
  USING (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

CREATE POLICY "Allow admins to insert all lineups" 
  ON public.lineups FOR INSERT 
  WITH CHECK (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

CREATE POLICY "Allow admins to delete all lineups" 
  ON public.lineups FOR DELETE 
  USING (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

-- LINEUP_INNINGS TABLE ADMIN POLICIES
CREATE POLICY "Allow admins to read all lineup innings" 
  ON public.lineup_innings FOR SELECT 
  USING (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

CREATE POLICY "Allow admins to update all lineup innings" 
  ON public.lineup_innings FOR UPDATE 
  USING (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

CREATE POLICY "Allow admins to insert all lineup innings" 
  ON public.lineup_innings FOR INSERT 
  WITH CHECK (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

CREATE POLICY "Allow admins to delete all lineup innings" 
  ON public.lineup_innings FOR DELETE 
  USING (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

-- SUBSCRIPTIONS TABLE ADMIN POLICIES
CREATE POLICY "Allow admins to read all subscriptions" 
  ON public.subscriptions FOR SELECT 
  USING (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

CREATE POLICY "Allow admins to update all subscriptions" 
  ON public.subscriptions FOR UPDATE 
  USING (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

CREATE POLICY "Allow admins to insert all subscriptions" 
  ON public.subscriptions FOR INSERT 
  WITH CHECK (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

CREATE POLICY "Allow admins to delete all subscriptions" 
  ON public.subscriptions FOR DELETE 
  USING (auth.email() = '<EMAIL>' OR auth.email() = '<EMAIL>');

-- Create indexes for better performance on admin queries
CREATE INDEX IF NOT EXISTS idx_admin_audit_logs_admin_id ON public.admin_audit_logs(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_audit_logs_created_at ON public.admin_audit_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_admin_audit_logs_action ON public.admin_audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_admin_audit_logs_entity_type ON public.admin_audit_logs(entity_type);

-- Grant necessary permissions to authenticated users (admins)
GRANT ALL ON public.admin_audit_logs TO authenticated;
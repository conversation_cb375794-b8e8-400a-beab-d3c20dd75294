-- Migration: Enhance Position Preferences System
-- This migration updates the position_preferences column to support enhanced preference structure
-- with ranking support while maintaining backward compatibility

-- Update the comment to document the enhanced structure
COMMENT ON COLUMN public.players.position_preferences IS 
'JSONB object storing position preferences with enhanced structure supporting rankings:
{
  "pitcher": "preferred" | "secondary" | "avoid" | "neutral" | { "level": "preferred", "rank": 1 },
  "catcher": "preferred" | "secondary" | "avoid" | "neutral" | { "level": "preferred", "rank": 2 },
  "firstBase": "preferred" | "secondary" | "avoid" | "neutral" | { "level": "secondary", "rank": 1 },
  "secondBase": "preferred" | "secondary" | "avoid" | "neutral" | { "level": "avoid" },
  "thirdBase": "preferred" | "secondary" | "avoid" | "neutral" | { "level": "neutral" },
  "shortstop": "preferred" | "secondary" | "avoid" | "neutral",
  "leftField": "preferred" | "secondary" | "avoid" | "neutral",
  "centerField": "preferred" | "secondary" | "avoid" | "neutral",
  "rightField": "preferred" | "secondary" | "avoid" | "neutral",
  "leftCenter": "preferred" | "secondary" | "avoid" | "neutral",
  "rightCenter": "preferred" | "secondary" | "avoid" | "neutral",
  "middleInfield": "preferred" | "secondary" | "avoid" | "neutral"
}

Backward compatibility: String values ("preferred", "secondary", "avoid", "neutral") are still supported.
Enhanced format: Object with "level" and optional "rank" properties for priority within preference levels.
Rank values: 1-5 (1 = highest priority within the preference level)';

-- Create a function to validate position preferences
CREATE OR REPLACE FUNCTION validate_position_preferences(preferences JSONB)
RETURNS BOOLEAN AS $$
DECLARE
    key TEXT;
    value JSONB;
    valid_positions TEXT[] := ARRAY[
        'pitcher', 'catcher', 'firstBase', 'secondBase', 'thirdBase', 'shortstop',
        'leftField', 'centerField', 'rightField', 'leftCenter', 'rightCenter', 'middleInfield'
    ];
    valid_levels TEXT[] := ARRAY['preferred', 'secondary', 'avoid', 'neutral'];
BEGIN
    -- Allow empty object
    IF preferences = '{}'::JSONB THEN
        RETURN TRUE;
    END IF;
    
    -- Check each key-value pair
    FOR key, value IN SELECT * FROM jsonb_each(preferences)
    LOOP
        -- Validate position key
        IF NOT (key = ANY(valid_positions)) THEN
            RETURN FALSE;
        END IF;
        
        -- Validate preference value
        IF jsonb_typeof(value) = 'string' THEN
            -- Old format: simple string
            IF NOT (value #>> '{}' = ANY(valid_levels)) THEN
                RETURN FALSE;
            END IF;
        ELSIF jsonb_typeof(value) = 'object' THEN
            -- New format: object with level and optional rank
            IF NOT (value ? 'level') THEN
                RETURN FALSE;
            END IF;
            
            IF NOT (value ->> 'level' = ANY(valid_levels)) THEN
                RETURN FALSE;
            END IF;
            
            -- Validate rank if present
            IF value ? 'rank' THEN
                IF jsonb_typeof(value -> 'rank') != 'number' THEN
                    RETURN FALSE;
                END IF;
                
                IF (value ->> 'rank')::INTEGER < 1 OR (value ->> 'rank')::INTEGER > 5 THEN
                    RETURN FALSE;
                END IF;
            END IF;
        ELSE
            RETURN FALSE;
        END IF;
    END LOOP;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Add a check constraint to validate position preferences
DO $$
BEGIN
    -- Drop existing constraint if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'players' 
        AND constraint_name = 'valid_position_preferences'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.players DROP CONSTRAINT valid_position_preferences;
    END IF;
    
    -- Add the new constraint
    ALTER TABLE public.players 
    ADD CONSTRAINT valid_position_preferences 
    CHECK (validate_position_preferences(position_preferences));
    
    RAISE NOTICE 'Added position preferences validation constraint';
END $$;

-- Create a function to normalize preferences for backward compatibility
CREATE OR REPLACE FUNCTION normalize_position_preference(pref JSONB)
RETURNS TABLE(level TEXT, rank INTEGER) AS $$
BEGIN
    IF pref IS NULL THEN
        RETURN QUERY SELECT 'neutral'::TEXT, NULL::INTEGER;
    ELSIF jsonb_typeof(pref) = 'string' THEN
        RETURN QUERY SELECT pref #>> '{}', NULL::INTEGER;
    ELSIF jsonb_typeof(pref) = 'object' AND pref ? 'level' THEN
        RETURN QUERY SELECT 
            pref ->> 'level', 
            CASE WHEN pref ? 'rank' THEN (pref ->> 'rank')::INTEGER ELSE NULL END;
    ELSE
        RETURN QUERY SELECT 'neutral'::TEXT, NULL::INTEGER;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create an index on position_preferences for better query performance
CREATE INDEX IF NOT EXISTS idx_players_position_preferences 
ON public.players USING GIN (position_preferences);

-- Create a view for easier querying of position preferences with rankings
CREATE OR REPLACE VIEW player_position_preferences_expanded AS
SELECT 
    p.id,
    p.name,
    p.team_id,
    pos.position,
    (normalize_position_preference(p.position_preferences -> pos.position)).level as preference_level,
    (normalize_position_preference(p.position_preferences -> pos.position)).rank as preference_rank
FROM public.players p
CROSS JOIN (
    VALUES 
        ('pitcher'), ('catcher'), ('firstBase'), ('secondBase'), ('thirdBase'), ('shortstop'),
        ('leftField'), ('centerField'), ('rightField'), ('leftCenter'), ('rightCenter'), ('middleInfield')
) AS pos(position)
WHERE p.position_preferences -> pos.position IS NOT NULL;

-- Grant permissions on the new view
GRANT SELECT ON player_position_preferences_expanded TO authenticated;

-- Add helpful comments
COMMENT ON FUNCTION validate_position_preferences(JSONB) IS 
'Validates position preferences JSONB structure. Supports both legacy string format and new object format with rankings.';

COMMENT ON FUNCTION normalize_position_preference(JSONB) IS 
'Normalizes position preference values to extract level and rank, handling both old and new formats.';

COMMENT ON VIEW player_position_preferences_expanded IS 
'Expanded view of player position preferences showing level and rank for each position in separate rows.';

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'Enhanced position preferences system migration completed successfully';
    RAISE NOTICE 'Features added:';
    RAISE NOTICE '- Support for preference rankings (1-5 priority within levels)';
    RAISE NOTICE '- Backward compatibility with existing string preferences';
    RAISE NOTICE '- Validation constraints for data integrity';
    RAISE NOTICE '- Performance indexes for faster queries';
    RAISE NOTICE '- Helper functions and views for easier data access';
END $$;

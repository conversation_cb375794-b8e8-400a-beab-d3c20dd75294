-- Add performance indexes for frequently queried columns
-- These indexes will improve query performance for common operations

-- Index on teams.user_id for faster user-specific team queries
CREATE INDEX IF NOT EXISTS idx_teams_user_id 
ON public.teams(user_id);

-- Index on players.team_id for faster team-specific player queries
CREATE INDEX IF NOT EXISTS idx_players_team_id 
ON public.players(team_id);

-- Index on lineups.team_id for faster team-specific lineup queries
CREATE INDEX IF NOT EXISTS idx_lineups_team_id 
ON public.lineups(team_id);

-- Index on lineup_innings.lineup_id for faster lineup-specific inning queries
CREATE INDEX IF NOT EXISTS idx_lineup_innings_lineup_id 
ON public.lineup_innings(lineup_id);

-- Index on lineup_attendance.lineup_id for faster attendance queries
CREATE INDEX IF NOT EXISTS idx_lineup_attendance_lineup_id 
ON public.lineup_attendance(lineup_id);

-- Index on batting_orders.lineup_id for faster batting order queries
CREATE INDEX IF NOT EXISTS idx_batting_orders_lineup_id 
ON public.batting_orders(lineup_id);

-- Index on rotation_rules.team_id for faster team rotation rule queries
CREATE INDEX IF NOT EXISTS idx_rotation_rules_team_id 
ON public.rotation_rules(team_id);

-- Composite index on lineups for game date queries within teams
CREATE INDEX IF NOT EXISTS idx_lineups_team_id_game_date 
ON public.lineups(team_id, game_date);

-- Index on players.name for faster player name searches (with COLLATE for case-insensitive)
CREATE INDEX IF NOT EXISTS idx_players_name_case_insensitive 
ON public.players(LOWER(name));

-- Index on lineups.name for faster lineup name searches
CREATE INDEX IF NOT EXISTS idx_lineups_name_case_insensitive 
ON public.lineups(LOWER(name));

-- Index on subscriptions.user_id and is_paid for faster payment status checks
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id_paid 
ON public.subscriptions(user_id, is_paid) 
WHERE is_paid = true;

-- Partial index on subscriptions for active subscriptions only
CREATE INDEX IF NOT EXISTS idx_subscriptions_active 
ON public.subscriptions(user_id, payment_date) 
WHERE is_paid = true;

-- Comments for documentation
COMMENT ON INDEX idx_teams_user_id IS 
'Performance index for user-specific team queries. Critical for dashboard loading.';

COMMENT ON INDEX idx_lineups_team_id_game_date IS 
'Composite index for efficient team lineup queries filtered by date range.';

COMMENT ON INDEX idx_subscriptions_user_id_paid IS 
'Optimized index for payment status checks. Only indexes paid subscriptions for efficiency.';

-- Analyze tables to update query planner statistics
ANALYZE public.teams;
ANALYZE public.players;
ANALYZE public.lineups;
ANALYZE public.lineup_innings;
ANALYZE public.subscriptions;
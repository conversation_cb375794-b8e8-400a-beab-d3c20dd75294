-- Fix lineup_innings RLS policies to properly handle both regular users and admin operations

-- First, let's drop the existing policies to start fresh
DROP POLICY IF EXISTS "Users can view their own lineup innings" ON public.lineup_innings;
DROP POLICY IF EXISTS "Users can insert their own lineup innings" ON public.lineup_innings;
DROP POLICY IF EXISTS "Users can update their own lineup innings" ON public.lineup_innings;
DROP POLICY IF EXISTS "Users can delete their own lineup innings" ON public.lineup_innings;
DROP POLICY IF EXISTS "Allow admins to read all lineup innings" ON public.lineup_innings;
DROP POLICY IF EXISTS "Allow admins to update all lineup innings" ON public.lineup_innings;
DROP POLICY IF EXISTS "Allow admins to insert all lineup innings" ON public.lineup_innings;
DROP POLICY IF EXISTS "Allow admins to delete all lineup innings" ON public.lineup_innings;

-- Create new policies that handle both regular users and admins properly

-- SELECT policy: Allow users to view their own innings OR admins to view all
CREATE POLICY "lineup_innings_select_policy" ON public.lineup_innings
  FOR SELECT USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

-- INSERT policy: Allow users to insert their own innings OR admins to insert any
CREATE POLICY "lineup_innings_insert_policy" ON public.lineup_innings
  FOR INSERT WITH CHECK (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

-- UPDATE policy: Allow users to update their own innings OR admins to update any
CREATE POLICY "lineup_innings_update_policy" ON public.lineup_innings
  FOR UPDATE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

-- DELETE policy: Allow users to delete their own innings OR admins to delete any
CREATE POLICY "lineup_innings_delete_policy" ON public.lineup_innings
  FOR DELETE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

-- Also fix the lineups table policies in the same way
DROP POLICY IF EXISTS "Users can view their own lineups" ON public.lineups;
DROP POLICY IF EXISTS "Users can insert their own lineups" ON public.lineups;
DROP POLICY IF EXISTS "Users can update their own lineups" ON public.lineups;
DROP POLICY IF EXISTS "Users can delete their own lineups" ON public.lineups;
DROP POLICY IF EXISTS "Allow admins to read all lineups" ON public.lineups;
DROP POLICY IF EXISTS "Allow admins to update all lineups" ON public.lineups;
DROP POLICY IF EXISTS "Allow admins to insert all lineups" ON public.lineups;
DROP POLICY IF EXISTS "Allow admins to delete all lineups" ON public.lineups;

-- Create new combined policies for lineups
CREATE POLICY "lineups_select_policy" ON public.lineups
  FOR SELECT USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "lineups_insert_policy" ON public.lineups
  FOR INSERT WITH CHECK (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "lineups_update_policy" ON public.lineups
  FOR UPDATE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "lineups_delete_policy" ON public.lineups
  FOR DELETE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

-- Do the same for lineup_attendance
DROP POLICY IF EXISTS "Users can view their own lineup attendance" ON public.lineup_attendance;
DROP POLICY IF EXISTS "Users can insert their own lineup attendance" ON public.lineup_attendance;
DROP POLICY IF EXISTS "Users can update their own lineup attendance" ON public.lineup_attendance;
DROP POLICY IF EXISTS "Users can delete their own lineup attendance" ON public.lineup_attendance;

CREATE POLICY "lineup_attendance_select_policy" ON public.lineup_attendance
  FOR SELECT USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "lineup_attendance_insert_policy" ON public.lineup_attendance
  FOR INSERT WITH CHECK (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "lineup_attendance_update_policy" ON public.lineup_attendance
  FOR UPDATE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "lineup_attendance_delete_policy" ON public.lineup_attendance
  FOR DELETE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );
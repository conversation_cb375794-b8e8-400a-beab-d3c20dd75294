// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.land/manual/examples/deploy_node_server

import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.7.1";

interface CreateUserRequest {
  email: string;
  password: string;
  fullName: string;
  role: string;
  isPaid: boolean;
  tier?: "starter" | "coach" | "club";
  customTeamLimit?: number;
}

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Create Supabase client with service role key to perform secure queries
    const supabaseAdmin = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      { auth: { persistSession: false } }
    );

    // Verify the request is from an admin user
    const authHeader = req.headers.get("Authorization")!;
    const token = authHeader.replace("Bearer ", "");
    const { data: userData, error: authError } = await supabaseAdmin.auth.getUser(token);
    
    if (authError || !userData.user) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: "Unauthorized: User not authenticated" 
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 401,
        }
      );
    }

    // Check if the user is an admin
    const { data: profileData, error: profileError } = await supabaseAdmin
      .from("profiles")
      .select("is_admin")
      .eq("id", userData.user.id)
      .single();

    if (profileError || !profileData?.is_admin) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: "Unauthorized: Admin privileges required" 
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 403,
        }
      );
    }

    // Parse the request body
    const requestData: CreateUserRequest = await req.json();
    const { email, password, fullName, role, isPaid, tier, customTeamLimit } = requestData;

    // Validate required fields
    if (!email || !password) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: "Email and password are required" 
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }

    // Create the user
    const { data: newUser, error: createError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: {
        full_name: fullName,
        role
      }
    });

    if (createError) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: `Failed to create user: ${createError.message}` 
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }

    // Create the user profile
    const { error: profileCreateError } = await supabaseAdmin
      .from("profiles")
      .upsert({
        id: newUser.user.id,
        full_name: fullName,
        email: email,
        role,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (profileCreateError) {
      console.error("Error creating profile:", profileCreateError);
      // Continue anyway, as the user was created
    }

    // If the user should be paid, update or create a subscription
    if (isPaid) {
      // Determine team limit based on tier or custom value
      let teamLimit = 1;
      const selectedTier = tier || "club"; // Default to club if not specified
      
      if (customTeamLimit !== undefined && customTeamLimit !== null) {
        teamLimit = customTeamLimit;
      } else {
        switch (selectedTier) {
          case 'starter':
            teamLimit = 1;
            break;
          case 'coach':
            teamLimit = 5;
            break;
          case 'club':
            teamLimit = 999;
            break;
        }
      }

      // Determine amount based on tier
      let amount = 2000; // Default $20 for starter
      switch (selectedTier) {
        case 'starter':
          amount = 2000;
          break;
        case 'coach':
          amount = 3000;
          break;
        case 'club':
          amount = 50000;
          break;
      }

      // Check if a subscription already exists (created by trigger)
      const { data: existingSubscription, error: checkError } = await supabaseAdmin
        .from("subscriptions")
        .select("*")
        .eq("user_id", newUser.user.id)
        .maybeSingle();

      if (checkError && checkError.code !== 'PGRST116') {
        console.error("Error checking existing subscription:", checkError);
      }

      if (existingSubscription) {
        // Update existing subscription
        const { error: subscriptionError } = await supabaseAdmin
          .from("subscriptions")
          .update({
            is_paid: true,
            amount: amount,
            currency: "usd",
            payment_date: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            tier: selectedTier,
            team_limit: teamLimit
          })
          .eq("id", existingSubscription.id);

        if (subscriptionError) {
          console.error("Error updating subscription:", subscriptionError);
          // Continue anyway, as the user was created
        }
      } else {
        // Create new subscription
        const { error: subscriptionError } = await supabaseAdmin
          .from("subscriptions")
          .insert({
            user_id: newUser.user.id,
            is_paid: true,
            amount: amount,
            currency: "usd",
            payment_date: new Date().toISOString(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            tier: selectedTier,
            team_limit: teamLimit
          });

        if (subscriptionError) {
          console.error("Error creating subscription:", subscriptionError);
          // Continue anyway, as the user was created
        }
      }
    }

    // Send welcome email
    try {
      const supabaseUrl = Deno.env.get("SUPABASE_URL");
      const welcomeEmailResponse = await fetch(`${supabaseUrl}/functions/v1/send-welcome-email`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": authHeader
        },
        body: JSON.stringify({
          email,
          name: fullName || email.split("@")[0],
          temporaryPassword: password
        })
      });

      if (!welcomeEmailResponse.ok) {
        const errorText = await welcomeEmailResponse.text();
        console.error("Failed to send welcome email:", errorText);
        // Don't fail the user creation if email fails
      } else {
        const result = await welcomeEmailResponse.json();
        console.log("Welcome email sent successfully to:", email, "Message ID:", result.messageId);
      }
    } catch (emailError) {
      console.error("Error sending welcome email:", emailError);
      // Don't fail the user creation if email fails
    }

    // Log admin action
    await supabaseAdmin
      .from("admin_audit_logs")
      .insert({
        admin_id: userData.user.id,
        action: "create_user",
        entity_type: "user",
        entity_id: newUser.user.id,
        details: { 
          email,
          isPaid
        }
      });

    return new Response(
      JSON.stringify({ 
        success: true, 
        data: {
          user: {
            id: newUser.user.id,
            email: newUser.user.email,
            created_at: newUser.user.created_at
          }
        }
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: `Server error: ${error.message}` 
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});

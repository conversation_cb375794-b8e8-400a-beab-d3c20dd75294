// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.land/manual/examples/deploy_node_server

import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.7.1";

interface CreateUserRequest {
  email: string;
  password: string;
  fullName: string;
  role: string;
  isPaid: boolean;
}

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    // Create Supabase client with service role key to perform secure queries
    const supabaseAdmin = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      { auth: { persistSession: false } }
    );

    // Verify the request is from an admin user
    const authHeader = req.headers.get("Authorization")!;
    const token = authHeader.replace("Bearer ", "");
    const { data: userData, error: authError } = await supabaseAdmin.auth.getUser(token);
    
    if (authError || !userData.user) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: "Unauthorized: User not authenticated" 
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 401,
        }
      );
    }

    // Check if the user is an admin
    const { data: profileData, error: profileError } = await supabaseAdmin
      .from("profiles")
      .select("is_admin")
      .eq("id", userData.user.id)
      .single();

    if (profileError || !profileData?.is_admin) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: "Unauthorized: Admin privileges required" 
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 403,
        }
      );
    }

    // Parse the request body
    const requestData: CreateUserRequest = await req.json();
    const { email, password, fullName, role, isPaid } = requestData;

    // Validate required fields
    if (!email || !password) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: "Email and password are required" 
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }

    // Create the user
    const { data: newUser, error: createError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: {
        full_name: fullName,
        role
      }
    });

    if (createError) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: `Failed to create user: ${createError.message}` 
        }),
        {
          headers: { ...corsHeaders, "Content-Type": "application/json" },
          status: 400,
        }
      );
    }

    // Create the user profile
    const { error: profileCreateError } = await supabaseAdmin
      .from("profiles")
      .upsert({
        id: newUser.user.id,
        full_name: fullName,
        email: email,
        role,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (profileCreateError) {
      console.error("Error creating profile:", profileCreateError);
      // Continue anyway, as the user was created
    }

    // If the user should be paid, create a subscription
    if (isPaid) {
      const { error: subscriptionError } = await supabaseAdmin
        .from("subscriptions")
        .insert({
          user_id: newUser.user.id,
          is_paid: true,
          amount: 4900, // $49.00
          currency: "usd",
          payment_date: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (subscriptionError) {
        console.error("Error creating subscription:", subscriptionError);
        // Continue anyway, as the user was created
      }
    }

    // Log admin action
    await supabaseAdmin
      .from("admin_audit_logs")
      .insert({
        admin_id: userData.user.id,
        action: "create_user",
        entity_type: "user",
        entity_id: newUser.user.id,
        details: { 
          email,
          isPaid
        }
      });

    return new Response(
      JSON.stringify({ 
        success: true, 
        data: {
          user: {
            id: newUser.user.id,
            email: newUser.user.email,
            created_at: newUser.user.created_at
          }
        }
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: `Server error: ${error.message}` 
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});

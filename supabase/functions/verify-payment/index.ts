
// The Edge Function for verifying payment status
// This will run on Supabase Edge Functions after you connect to Supabase

import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@14.21.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";
import { getCorsHeaders, corsPreflightResponse, corsErrorResponse } from "../_shared/cors.ts";

// Helper function to log errors with more context
const logError = (message: string, error: any) => {
  console.error(`${message}: ${error.message || error}`);
  if (error.stack) {
    console.error(error.stack);
  }
};

serve(async (req) => {
  const origin = req.headers.get("origin");
  
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return corsPreflightResponse(origin);
  }

  try {
    // Create Supabase client with service role key to perform secure queries
    const supabaseAdmin = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      { auth: { persistSession: false } }
    );

    // Retrieve authenticated user
    const authHeader = req.headers.get("Authorization")!;
    const token = authHeader.replace("Bearer ", "");
    const { data: userData } = await supabaseAdmin.auth.getUser(token);
    const user = userData.user;

    if (!user) {
      return new Response(JSON.stringify({ paid: false, error: "User not authenticated" }), {
        headers: { ...getCorsHeaders(origin), "Content-Type": "application/json" },
        status: 401,
      });
    }

    // Check if user has any subscription in our database
    const { data: subscriptions } = await supabaseAdmin
      .from("subscriptions")
      .select("*")
      .eq("user_id", user.id)
      .eq("is_paid", true)
      .order("created_at", { ascending: false })
      .limit(1);

    if (subscriptions && subscriptions.length > 0) {
      // User has a paid subscription
      return new Response(JSON.stringify({
        paid: true,
        subscription: subscriptions[0]
      }), {
        headers: { ...getCorsHeaders(origin), "Content-Type": "application/json" },
        status: 200,
      });
    }

    // Initialize Stripe to check for any pending payments
    const stripe = new Stripe(Deno.env.get("STRIPE_SECRET_KEY") || "", {
      apiVersion: "2023-10-16",
    });

    // Get user's pending sessions from our database
    const { data: pendingSubscriptions } = await supabaseAdmin
      .from("subscriptions")
      .select("*")
      .eq("user_id", user.id)
      .eq("is_paid", false)
      .order("created_at", { ascending: false });

    // Check each pending subscription with Stripe
    if (pendingSubscriptions && pendingSubscriptions.length > 0) {
      for (const subscription of pendingSubscriptions) {
        if (subscription.stripe_session_id) {
          try {
            const session = await stripe.checkout.sessions.retrieve(subscription.stripe_session_id);

            if (session.payment_status === "paid") {
              // Update the subscription to paid
              const { error: updateError } = await supabaseAdmin
                .from("subscriptions")
                .update({
                  is_paid: true,
                  payment_date: new Date().toISOString(),
                  updated_at: new Date().toISOString()
                })
                .eq("id", subscription.id);

              if (updateError) {
                logError("Error updating subscription", updateError);
                continue; // Try the next subscription if there is one
              }

              return new Response(JSON.stringify({
                paid: true,
                subscription: {
                  ...subscription,
                  is_paid: true,
                  payment_date: new Date().toISOString()
                }
              }), {
                headers: { ...getCorsHeaders(origin), "Content-Type": "application/json" },
                status: 200,
              });
            }
          } catch (stripeError) {
            // Log the error but continue checking other subscriptions
            logError(`Error retrieving Stripe session ${subscription.stripe_session_id}`, stripeError);
            continue;
          }
        }
      }
    }

    // Check if the user has any completed payments in Stripe that we might have missed
    try {
      // Look for payments by customer email
      const paymentIntents = await stripe.paymentIntents.list({
        limit: 10,
      });

      // Filter for successful payments that match this user's email
      const userEmail = user.email;
      for (const intent of paymentIntents.data) {
        if (intent.status === 'succeeded' && intent.receipt_email === userEmail) {
          // We found a successful payment for this user that's not in our database
          // Create a new subscription record
          const { data: newSubscription, error: insertError } = await supabaseAdmin
            .from("subscriptions")
            .insert({
              user_id: user.id,
              stripe_session_id: intent.id, // Use the payment intent ID
              is_paid: true,
              amount: intent.amount,
              currency: intent.currency,
              payment_date: new Date(intent.created * 1000).toISOString(),
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .select();

          if (insertError) {
            logError("Error creating subscription from payment intent", insertError);
          } else if (newSubscription && newSubscription.length > 0) {
            return new Response(JSON.stringify({
              paid: true,
              subscription: newSubscription[0],
              note: "Payment found in Stripe but was missing in our database. Fixed automatically."
            }), {
              headers: { ...getCorsHeaders(origin), "Content-Type": "application/json" },
              status: 200,
            });
          }
        }
      }
    } catch (stripeListError) {
      // Just log this error but don't fail the request
      logError("Error checking for payments in Stripe", stripeListError);
    }

    // If we get here, user has no paid subscription
    return new Response(JSON.stringify({
      paid: false,
      message: "No active subscription found for this user"
    }), {
      headers: { ...getCorsHeaders(origin), "Content-Type": "application/json" },
      status: 200,
    });
  } catch (error) {
    logError("Unexpected error in verify-payment", error);
    return new Response(JSON.stringify({
      paid: false,
      error: error.message,
      errorType: error.constructor.name
    }), {
      headers: { ...getCorsHeaders(origin), "Content-Type": "application/json" },
      status: 500,
    });
  }
});

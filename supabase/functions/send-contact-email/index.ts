// Contact Form Email Sending Edge Function
// This function receives contact form submissions and sends emails

import { serve } from "https://deno.land/std@0.190.0/http/server.ts";

// @ts-ignore - Deno global is available in edge runtime
const Deno = globalThis.Deno;

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
};

interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  // Only allow POST requests
  if (req.method !== "POST") {
    return new Response(JSON.stringify({ error: "Method not allowed" }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 405,
    });
  }

  try {
    // Parse the request body
    const formData: ContactFormData = await req.json();

    // Validate required fields
    if (!formData.name || !formData.email || !formData.subject || !formData.message) {
      return new Response(JSON.stringify({ error: "Missing required fields" }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 400,
      });
    }

    // Validate email format (basic check)
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      return new Response(JSON.stringify({ error: "Invalid email format" }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 400,
      });
    }

    // Create email content
    const emailContent = `
New Contact Form Submission - Dugout Boss

From: ${formData.name} (${formData.email})
Subject: ${formData.subject}

Message:
${formData.message}

---
Sent from Dugout Boss Contact Form
Time: ${new Date().toISOString()}
`;

    // Get the API key from environment
    const RESEND_API_KEY = Deno.env.get("RESEND_API_KEY");
    if (!RESEND_API_KEY) {
      console.error("RESEND_API_KEY not configured");
      // Fallback to logging for now
      console.log("Contact form submission:", {
        name: formData.name,
        email: formData.email,
        subject: formData.subject,
        timestamp: new Date().toISOString()
      });
      
      return new Response(JSON.stringify({
        success: true,
        message: "Your message has been received. We'll get back to you soon!"
      }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      });
    }

    // Send email via Resend
    const emailResponse = await fetch("https://api.resend.com/emails", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${RESEND_API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        from: "Dugout Boss Contact <<EMAIL>>",
        to: ["<EMAIL>"],
        subject: `Contact Form: ${formData.subject}`,
        text: emailContent,
        html: emailContent.replace(/\n/g, '<br>'),
        reply_to: formData.email,
      }),
    });

    if (!emailResponse.ok) {
      const errorData = await emailResponse.text();
      throw new Error(`Email API error: ${errorData}`);
    }

    console.log("Email sent successfully via Resend");

    // Return success response
    return new Response(JSON.stringify({
      success: true,
      message: "Your message has been sent successfully. We'll get back to you soon!"
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });

  } catch (error) {
    console.error("Contact form error:", error);

    return new Response(JSON.stringify({
      error: "Failed to send message. Please try again later.",
      details: error.message
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});
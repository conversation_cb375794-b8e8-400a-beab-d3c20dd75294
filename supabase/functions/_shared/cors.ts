// Shared CORS configuration for all Edge Functions
// This centralizes CORS settings for easier maintenance

const ALLOWED_ORIGINS = [
  // Production domains
  'https://dugoutboss.com',
  'https://www.dugoutboss.com',
  // Development domains
  'http://localhost:5173',
  'http://localhost:3000',
  'http://127.0.0.1:5173',
  'http://127.0.0.1:3000',
  // Add any other production domains here
];

// Validate if the origin is allowed
export function isAllowedOrigin(origin: string | null): boolean {
  if (!origin) return false;
  return ALLOWED_ORIGINS.includes(origin);
}

// Get CORS headers based on the request origin
export function getCorsHeaders(origin: string | null): HeadersInit {
  // In development, be more permissive
  // In production, we should be strict about origins
  const isDevelopment = origin && (origin.includes('localhost') || origin.includes('127.0.0.1'));
  const allowedOrigin = (isAllowedOrigin(origin) && origin) ? origin : (isDevelopment ? origin : ALLOWED_ORIGINS[0]);
  
  return {
    'Access-Control-Allow-Origin': allowedOrigin || '*',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Allow-Headers': 
      'authorization, x-client-info, apikey, content-type, stripe-signature',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Access-Control-Max-Age': '86400', // 24 hours
  };
}

// Standard CORS preflight response
export function corsPreflightResponse(origin: string | null): Response {
  return new Response(null, { 
    status: 204,
    headers: getCorsHeaders(origin) 
  });
}

// Error response with CORS headers
export function corsErrorResponse(
  error: string, 
  status: number, 
  origin: string | null
): Response {
  return new Response(
    JSON.stringify({ error }), 
    {
      status,
      headers: {
        ...getCorsHeaders(origin),
        'Content-Type': 'application/json'
      }
    }
  );
}
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.7'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  console.log('Function called:', new Date().toISOString());
  
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('Method:', req.method);
    console.log('Headers:', Object.fromEntries(req.headers.entries()));
    
    // Get auth header
    const authHeader = req.headers.get('authorization')
    if (!authHeader) {
      console.error('Missing authorization header');
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log('Auth header present');

    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')
    
    console.log('Supabase URL:', supabaseUrl ? 'Set' : 'Not set');
    console.log('Service key:', supabaseServiceKey ? 'Set' : 'Not set');
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase environment variables');
      return new Response(
        JSON.stringify({ error: 'Server configuration error' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
    
    const supabaseClient = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    console.log('Supabase client created');

    // Verify user is admin
    const token = authHeader.replace('Bearer ', '')
    console.log('Verifying user...');
    
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)
    
    if (authError) {
      console.error('Auth error:', authError);
      return new Response(
        JSON.stringify({ error: 'Invalid authentication', details: authError.message }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
    
    if (!user) {
      console.error('No user found');
      return new Response(
        JSON.stringify({ error: 'Invalid authentication' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log('User verified:', user.id);

    // Check admin status
    console.log('Checking admin status...');
    const { data: profile, error: profileError } = await supabaseClient
      .from('profiles')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (profileError) {
      console.error('Profile error:', profileError);
      return new Response(
        JSON.stringify({ error: 'Failed to check admin status', details: profileError.message }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    if (!profile?.is_admin) {
      console.error('User is not admin');
      return new Response(
        JSON.stringify({ error: 'Unauthorized - admin access required' }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log('User is admin');

    // Get request data
    let body;
    try {
      body = await req.json();
      console.log('Request body:', body);
    } catch (e) {
      console.error('Failed to parse body:', e);
      return new Response(
        JSON.stringify({ error: 'Invalid request body' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
    
    const { email, name, temporaryPassword } = body;

    if (!email || !name || !temporaryPassword) {
      console.error('Missing required fields:', { email: !!email, name: !!name, temporaryPassword: !!temporaryPassword });
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check for Resend API key
    const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')
    console.log('RESEND_API_KEY:', RESEND_API_KEY ? 'Set' : 'Not set');
    
    if (!RESEND_API_KEY) {
      console.error('RESEND_API_KEY is not set');
      return new Response(
        JSON.stringify({ error: 'Email service not configured - RESEND_API_KEY missing' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Build URLs
    const origin = req.headers.get('origin') || 'https://dugoutboss.com'
    const loginUrl = `${origin}/signin`

    console.log('Preparing to send email to:', email);

    // Create simple email HTML
    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Welcome to Dugout Boss</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 20px;">
    <h1>Welcome to Dugout Boss, ${name}!</h1>
    <p>Your account has been created. Here are your login credentials:</p>
    <p><strong>Email:</strong> ${email}<br>
    <strong>Temporary Password:</strong> ${temporaryPassword}</p>
    <p><strong>Important:</strong> Please change your password after your first login.</p>
    <p><a href="${loginUrl}" style="background: #1e40af; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;">Login to Dugout Boss</a></p>
</body>
</html>`;

    const textContent = `Welcome to Dugout Boss!

Hi ${name},

Your account has been created. Here are your login credentials:

Email: ${email}
Temporary Password: ${temporaryPassword}

Important: Please change your password after your first login.

Login here: ${loginUrl}

Best regards,
The Dugout Boss Team`;

    // Send email via Resend
    console.log('Calling Resend API...');
    
    const resendPayload = {
      from: 'Dugout Boss <<EMAIL>>',
      to: [email],
      subject: 'Welcome to Dugout Boss - Your Account Has Been Created',
      html: htmlContent,
      text: textContent,
    };
    
    console.log('Resend payload:', JSON.stringify(resendPayload, null, 2));
    
    try {
      const resendResponse = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${RESEND_API_KEY}`,
        },
        body: JSON.stringify(resendPayload),
      });

      console.log('Resend response status:', resendResponse.status);
      
      const responseText = await resendResponse.text();
      console.log('Resend response:', responseText);

      if (!resendResponse.ok) {
        console.error('Resend API error:', responseText);
        return new Response(
          JSON.stringify({ 
            error: 'Failed to send email', 
            details: responseText,
            status: resendResponse.status 
          }),
          { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }

      let result;
      try {
        result = JSON.parse(responseText);
      } catch (e) {
        console.error('Failed to parse Resend response:', e);
        result = { id: 'unknown' };
      }
      
      console.log('Welcome email sent successfully:', result.id);

      return new Response(
        JSON.stringify({ success: true, messageId: result.id }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    } catch (fetchError) {
      console.error('Fetch error:', fetchError);
      return new Response(
        JSON.stringify({ 
          error: 'Network error calling Resend', 
          details: fetchError.message 
        }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
    
  } catch (error) {
    console.error('Unexpected error in send-welcome-email function:', error);
    console.error('Error stack:', error.stack);
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        details: error.message,
        stack: error.stack 
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
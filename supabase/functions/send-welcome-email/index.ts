import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.7'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface WelcomeEmailRequest {
  email: string
  name: string
  temporaryPassword: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Verify the request is authenticated
    const authHeader = req.headers.get('authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Initialize Supabase client with service role for admin verification
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Verify the user making the request is an admin
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid authentication' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check if user is admin
    const { data: profile, error: profileError } = await supabaseClient
      .from('profiles')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (profileError || !profile?.is_admin) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized - admin access required' }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Parse request body
    const { email, name, temporaryPassword }: WelcomeEmailRequest = await req.json()

    // Validate required fields
    if (!email || !name || !temporaryPassword) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')
    if (!RESEND_API_KEY) {
      console.error('RESEND_API_KEY is not set')
      return new Response(
        JSON.stringify({ error: 'Email service not configured' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Generate email content
    const loginUrl = `${req.headers.get('origin') || 'https://dugoutboss.com'}/sign-in`
    const supportUrl = `${req.headers.get('origin') || 'https://dugoutboss.com'}/contact`
    const docsUrl = `${req.headers.get('origin') || 'https://dugoutboss.com'}/help`

    const htmlContent = generateWelcomeEmailHtml({ email, name, temporaryPassword, loginUrl, supportUrl, docsUrl })
    const textContent = generateWelcomeEmailText({ email, name, temporaryPassword, loginUrl })

    // Send email via Resend
    const resendResponse = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RESEND_API_KEY}`,
      },
      body: JSON.stringify({
        from: 'Dugout Boss <<EMAIL>>',
        to: [email],
        subject: 'Welcome to Dugout Boss - Your Account Has Been Created',
        html: htmlContent,
        text: textContent,
      }),
    })

    if (!resendResponse.ok) {
      const error = await resendResponse.text()
      console.error('Resend API error:', error)
      return new Response(
        JSON.stringify({ error: 'Failed to send email' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const result = await resendResponse.json()
    console.log('Welcome email sent successfully:', result.id)

    return new Response(
      JSON.stringify({ success: true, messageId: result.id }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Error in send-welcome-email function:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

function generateWelcomeEmailHtml({ email, name, temporaryPassword, loginUrl, supportUrl, docsUrl }: {
  email: string
  name: string
  temporaryPassword: string
  loginUrl: string
  supportUrl: string
  docsUrl: string
}): string {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Dugout Boss</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f5f5f5;">
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #f5f5f5;">
        <tr>
            <td align="center" style="padding: 40px 0;">
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" style="background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <!-- Header -->
                    <tr>
                        <td align="center" style="padding: 40px 20px; background-color: #1e40af; border-radius: 8px 8px 0 0;">
                            <h1 style="margin: 0; color: #ffffff; font-size: 28px; font-weight: bold;">Dugout Boss</h1>
                            <p style="margin: 10px 0 0 0; color: #e0e7ff; font-size: 16px;">Smart Lineup Management for Coaches</p>
                        </td>
                    </tr>

                    <!-- Welcome Message -->
                    <tr>
                        <td style="padding: 40px 40px 20px;">
                            <h2 style="margin: 0 0 20px 0; color: #1e293b; font-size: 24px;">Welcome ${name}!</h2>
                            <p style="margin: 0 0 20px 0; color: #475569; font-size: 16px; line-height: 24px;">
                                Your Dugout Boss account has been created. You're now ready to start creating fair and optimized lineups for your team.
                            </p>
                        </td>
                    </tr>

                    <!-- Login Credentials Box -->
                    <tr>
                        <td style="padding: 0 40px 20px;">
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px;">
                                <tr>
                                    <td style="padding: 30px;">
                                        <h3 style="margin: 0 0 20px 0; color: #1e293b; font-size: 18px;">Your Login Credentials</h3>
                                        
                                        <p style="margin: 0 0 10px 0; color: #475569; font-size: 14px;">
                                            <strong>Email:</strong><br>
                                            <span style="font-size: 16px; color: #1e293b;">${email}</span>
                                        </p>
                                        
                                        <p style="margin: 0 0 20px 0; color: #475569; font-size: 14px;">
                                            <strong>Temporary Password:</strong><br>
                                            <span style="font-family: monospace; font-size: 16px; background-color: #fef3c7; color: #92400e; padding: 4px 8px; border-radius: 4px;">${temporaryPassword}</span>
                                        </p>
                                        
                                        <p style="margin: 0 0 20px 0; color: #dc2626; font-size: 14px; font-weight: bold;">
                                            ⚠️ Please change your password after your first login
                                        </p>
                                        
                                        <table role="presentation" cellspacing="0" cellpadding="0" border="0">
                                            <tr>
                                                <td style="background-color: #1e40af; border-radius: 6px;">
                                                    <a href="${loginUrl}" target="_blank" style="display: inline-block; padding: 12px 24px; color: #ffffff; text-decoration: none; font-size: 16px; font-weight: bold;">
                                                        Login to Dugout Boss
                                                    </a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                    <!-- Getting Started Section -->
                    <tr>
                        <td style="padding: 20px 40px;">
                            <h3 style="margin: 0 0 20px 0; color: #1e293b; font-size: 20px;">Getting Started</h3>
                            
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                <tr>
                                    <td style="padding: 0 0 15px 0;">
                                        <table role="presentation" cellspacing="0" cellpadding="0" border="0">
                                            <tr>
                                                <td style="width: 30px; vertical-align: top;">
                                                    <span style="display: inline-block; width: 24px; height: 24px; background-color: #10b981; color: #ffffff; text-align: center; border-radius: 50%; font-size: 14px; line-height: 24px;">1</span>
                                                </td>
                                                <td style="padding-left: 10px;">
                                                    <p style="margin: 0; color: #475569; font-size: 16px;">
                                                        <strong style="color: #1e293b;">Set up your team roster</strong><br>
                                                        Add players and their position preferences
                                                    </p>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td style="padding: 0 0 15px 0;">
                                        <table role="presentation" cellspacing="0" cellpadding="0" border="0">
                                            <tr>
                                                <td style="width: 30px; vertical-align: top;">
                                                    <span style="display: inline-block; width: 24px; height: 24px; background-color: #10b981; color: #ffffff; text-align: center; border-radius: 50%; font-size: 14px; line-height: 24px;">2</span>
                                                </td>
                                                <td style="padding-left: 10px;">
                                                    <p style="margin: 0; color: #475569; font-size: 16px;">
                                                        <strong style="color: #1e293b;">Configure rotation rules</strong><br>
                                                        Set playing time goals and bench limits
                                                    </p>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td style="padding: 0 0 15px 0;">
                                        <table role="presentation" cellspacing="0" cellpadding="0" border="0">
                                            <tr>
                                                <td style="width: 30px; vertical-align: top;">
                                                    <span style="display: inline-block; width: 24px; height: 24px; background-color: #10b981; color: #ffffff; text-align: center; border-radius: 50%; font-size: 14px; line-height: 24px;">3</span>
                                                </td>
                                                <td style="padding-left: 10px;">
                                                    <p style="margin: 0; color: #475569; font-size: 16px;">
                                                        <strong style="color: #1e293b;">Generate your first lineup</strong><br>
                                                        Let our AI create fair, optimized lineups instantly
                                                    </p>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                    <!-- Support Section -->
                    <tr>
                        <td style="padding: 20px 40px 40px; border-top: 1px solid #e2e8f0;">
                            <h3 style="margin: 0 0 15px 0; color: #1e293b; font-size: 18px;">Need Help?</h3>
                            <p style="margin: 0; color: #475569; font-size: 16px; line-height: 24px;">
                                We're here to help you get the most out of Dugout Boss:
                            </p>
                            <ul style="margin: 10px 0 0 20px; padding: 0; color: #475569; font-size: 16px; line-height: 24px;">
                                <li style="margin: 0 0 8px 0;">📧 Email us at <a href="mailto:<EMAIL>" style="color: #1e40af; text-decoration: none;"><EMAIL></a></li>
                                <li style="margin: 0 0 8px 0;">📚 Visit our <a href="${docsUrl}" target="_blank" style="color: #1e40af; text-decoration: none;">documentation</a></li>
                                <li style="margin: 0;">💬 Use the in-app <a href="${supportUrl}" target="_blank" style="color: #1e40af; text-decoration: none;">contact form</a></li>
                            </ul>
                        </td>
                    </tr>

                    <!-- Footer -->
                    <tr>
                        <td align="center" style="padding: 30px; background-color: #f8fafc; border-radius: 0 0 8px 8px;">
                            <p style="margin: 0 0 10px 0; color: #64748b; font-size: 14px;">
                                © ${new Date().getFullYear()} Dugout Boss. All rights reserved.
                            </p>
                            <p style="margin: 0; color: #64748b; font-size: 12px;">
                                This email was sent because an administrator created an account for you.
                            </p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>
  `
}

function generateWelcomeEmailText({ email, name, temporaryPassword, loginUrl }: {
  email: string
  name: string
  temporaryPassword: string
  loginUrl: string
}): string {
  return `
Welcome to Dugout Boss!

Hi ${name},

Your Dugout Boss account has been created. You're now ready to start creating fair and optimized lineups for your team.

YOUR LOGIN CREDENTIALS
=====================
Email: ${email}
Temporary Password: ${temporaryPassword}

⚠️ IMPORTANT: Please change your password after your first login.

Login here: ${loginUrl}

GETTING STARTED
===============
1. Set up your team roster - Add players and their position preferences
2. Configure rotation rules - Set playing time goals and bench limits
3. Generate your first lineup - Let our AI create fair, optimized lineups instantly

NEED HELP?
==========
We're here to help you get the most out of Dugout Boss:
- Email <NAME_EMAIL>
- Visit our documentation
- Use the in-app contact form

Best regards,
The Dugout Boss Team

---
© ${new Date().getFullYear()} Dugout Boss. All rights reserved.
This email was sent because an administrator created an account for you.
  `
}
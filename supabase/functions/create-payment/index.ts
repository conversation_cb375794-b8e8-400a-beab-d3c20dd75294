// The Edge Function for creating a Stripe subscription payment
// This will run on Supabase Edge Functions after you connect to Supabase

import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@14.21.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";
import { getCorsHeaders, corsPreflightResponse, corsErrorResponse, isAllowedOrigin } from "../_shared/cors.ts";

// Rate limiting: Track requests by IP
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 5; // Max 5 payment requests per minute per IP

// Security: Validate request rate
function checkRateLimit(ip: string): boolean {
  const now = Date.now();
  const record = rateLimitMap.get(ip);
  
  if (!record || now > record.resetTime) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return true;
  }
  
  if (record.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false;
  }
  
  record.count++;
  return true;
}

// Security: Validate email format
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254; // RFC 5321 limit
}

// Helper function to log errors with more context
const logError = (message: string, error: any, context?: any) => {
  console.error(`${message}: ${error.message || error}`);
  if (context) {
    console.error("Context:", context);
  }
  if (error.stack) {
    console.error(error.stack);
  }
};

// Tier configuration
const TIER_CONFIG = {
  starter: {
    name: "Dugout Boss Starter",
    teamLimit: 1,
  },
  coach: {
    name: "Dugout Boss Coach",
    teamLimit: 5,
  },
  club: {
    name: "Dugout Boss Club",
    teamLimit: 999, // Effectively unlimited
  },
};

serve(async (req) => {
  const origin = req.headers.get("origin");
  
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return corsPreflightResponse(origin);
  }
  
  // Security: Check rate limiting
  const clientIP = req.headers.get("x-forwarded-for") || req.headers.get("x-real-ip") || "unknown";
  if (!checkRateLimit(clientIP)) {
    return corsErrorResponse("Rate limit exceeded. Please try again later.", 429, origin);
  }

  // Security: Only allow POST requests
  if (req.method !== "POST") {
    return corsErrorResponse("Method not allowed", 405, origin);
  }

  // Security: Validate origin
  if (!isAllowedOrigin(origin)) {
    return corsErrorResponse("Invalid origin", 403, origin);
  }

  try {
    // Parse request body
    const { priceId, tier, successUrl, cancelUrl } = await req.json();
    
    // Validate required fields
    if (!priceId || !tier) {
      return corsErrorResponse("Missing required fields: priceId and tier", 400, origin);
    }
    
    // Validate tier
    if (!TIER_CONFIG[tier]) {
      return corsErrorResponse("Invalid tier", 400, origin);
    }
    
    // Create Supabase client using the anon key for user authentication
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_ANON_KEY") ?? ""
    );

    // Try to retrieve authenticated user (optional)
    let email = "<EMAIL>"; // Default for guest checkout
    let userId = null;

    try {
      const authHeader = req.headers.get("Authorization");
      if (authHeader) {
        const token = authHeader.replace("Bearer ", "");
        const { data } = await supabaseClient.auth.getUser(token);
        const user = data.user;

        if (user?.email) {
          // Security: Validate user email
          if (!isValidEmail(user.email)) {
            return new Response(JSON.stringify({ error: "Invalid user email format" }), {
              headers: { ...getCorsHeaders(origin), "Content-Type": "application/json" },
              status: 400,
            });
          }
          email = user.email;
          userId = user.id;
        }
      }
    } catch (authError) {
      // No authentication provided or invalid - continue as guest
      console.log("No valid authentication provided, proceeding as guest");
    }

    // Initialize Stripe
    const stripe = new Stripe(Deno.env.get("STRIPE_SECRET_KEY") || "", {
      apiVersion: "2023-10-16",
    });

    // Security: Check if a Stripe customer record exists for this user
    let customerId;
    try {
      const customers = await stripe.customers.list({ 
        email: email === "<EMAIL>" ? undefined : email, 
        limit: 1 
      });
      if (customers.data.length > 0) {
        customerId = customers.data[0].id;
      }
    } catch (stripeError) {
      logError("Error checking for existing Stripe customer", stripeError, { email });
      // Continue without existing customer
    }

    // Create a subscription checkout session
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      customer_email: customerId ? undefined : (email === "<EMAIL>" ? undefined : email),
      customer_creation: customerId ? undefined : "always",
      line_items: [
        {
          price: priceId, // Use the price ID directly
          quantity: 1,
        },
      ],
      mode: "subscription", // Changed from "payment" to "subscription"
      success_url: successUrl || `${origin}/payment-success`,
      cancel_url: cancelUrl || `${origin}/payment-canceled`,
      // Security: Add metadata for tracking
      metadata: {
        user_id: userId || "guest",
        tier: tier,
        created_by: "dugout-boss-app",
        version: "2.0"
      },
      subscription_data: {
        metadata: {
          user_id: userId || "guest",
          tier: tier,
          team_limit: TIER_CONFIG[tier].teamLimit.toString(),
        }
      },
      // For guest purchases, collect email during checkout
      ...(email === "<EMAIL>" && {
        customer_creation: "always",
        billing_address_collection: "required",
      }),
      // Security: Set session expiration
      expires_at: Math.floor(Date.now() / 1000) + (30 * 60), // 30 minutes from now
    });

    // If we have a logged in user, let's record this payment session in Supabase
    if (userId) {
      try {
        // Create a client with the service role key to bypass RLS
        const supabaseAdmin = createClient(
          Deno.env.get("SUPABASE_URL") ?? "",
          Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
          { auth: { persistSession: false } }
        );

        // Security: Check if this user already has an active subscription
        const { data: existingSubscriptions, error: queryError } = await supabaseAdmin
          .from("subscriptions")
          .select("*")
          .eq("user_id", userId)
          .eq("is_paid", true)
          .limit(1);

        if (queryError) {
          logError("Error checking for existing subscriptions", queryError, { userId });
        } else if (existingSubscriptions && existingSubscriptions.length > 0) {
          // Check if subscription is still active
          const subscription = existingSubscriptions[0];
          const isActive = !subscription.expires_at || new Date(subscription.expires_at) > new Date();
          
          if (isActive) {
            return new Response(JSON.stringify({
              alreadyPaid: true,
              message: "You already have an active subscription",
              subscription: {
                id: subscription.id,
                tier: subscription.tier,
                expires_at: subscription.expires_at
              }
            }), {
              headers: { ...getCorsHeaders(origin), "Content-Type": "application/json" },
              status: 200,
            });
          }
        }

        // Security: Check for recent pending sessions to prevent spam
        const { data: recentSessions, error: recentError } = await supabaseAdmin
          .from("subscriptions")
          .select("*")
          .eq("user_id", userId)
          .eq("is_paid", false)
          .gte("created_at", new Date(Date.now() - 10 * 60 * 1000).toISOString()) // Last 10 minutes
          .limit(5);

        if (recentError) {
          logError("Error checking for recent sessions", recentError, { userId });
        } else if (recentSessions && recentSessions.length >= 3) {
          // Too many recent attempts
          return corsErrorResponse(
            "Too many recent payment attempts. Please wait a few minutes before trying again.",
            429,
            origin
          );
        }

        // Record the new checkout session
        const { error: insertError } = await supabaseAdmin.from("subscriptions").insert({
          user_id: userId,
          stripe_session_id: session.id,
          is_paid: false, // We'll update this when payment is complete
          tier: tier,
          price_id: priceId,
          team_limit: TIER_CONFIG[tier].teamLimit,
          subscription_period: "annual",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

        if (insertError) {
          logError("Error recording subscription", insertError, { userId, sessionId: session.id });
          // Continue anyway since the Stripe session was created successfully
        }
      } catch (dbError) {
        // Log the error but don't fail the request since the Stripe session was created
        logError("Error interacting with database", dbError, { userId });
      }
    }

    // Security: Limit response data
    const responseData = {
      url: session.url,
      sessionId: session.id,
      timestamp: new Date().toISOString()
    };

    return new Response(JSON.stringify(responseData), {
      headers: { ...getCorsHeaders(origin), "Content-Type": "application/json" },
      status: 200,
    });
  } catch (error) {
    logError("Error creating payment session", error, { clientIP });

    // Security: Return generic error message
    return corsErrorResponse(
      "Failed to create payment session. Please try again.",
      500,
      origin
    );
  }
});
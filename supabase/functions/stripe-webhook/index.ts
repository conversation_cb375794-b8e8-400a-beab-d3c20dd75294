// Stripe Webhook Handler for Supabase Edge Functions
// This function receives webhook events from Stripe and updates the database accordingly

import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@14.21.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";
import { getCorsHeaders, corsPreflightResponse, corsErrorResponse } from "../_shared/cors.ts";

// Rate limiting: Track requests by IP
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 10; // Max 10 requests per minute per IP

// Security: Validate request rate
function checkRateLimit(ip: string): boolean {
  const now = Date.now();
  const record = rateLimitMap.get(ip);
  
  if (!record || now > record.resetTime) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return true;
  }
  
  if (record.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false;
  }
  
  record.count++;
  return true;
}

// Security: Validate webhook timestamp to prevent replay attacks
function validateTimestamp(timestamp: string): boolean {
  const eventTime = parseInt(timestamp) * 1000; // Convert to milliseconds
  const currentTime = Date.now();
  const timeDifference = Math.abs(currentTime - eventTime);
  
  // Stripe recommends 5 minutes (300 seconds), we'll use 10 minutes for safety
  const TOLERANCE = 10 * 60 * 1000; // 10 minutes in milliseconds
  
  return timeDifference <= TOLERANCE;
}

// Helper to extract tier from subscription metadata or price lookup
async function getTierFromSubscription(stripe: Stripe, subscription: any): Promise<string> {
  // First check metadata
  if (subscription.metadata?.tier) {
    return subscription.metadata.tier;
  }
  
  // If no metadata, try to get from the price
  try {
    const priceId = subscription.items?.data?.[0]?.price?.id;
    if (priceId) {
      const price = await stripe.prices.retrieve(priceId);
      if (price.metadata?.tier) {
        return price.metadata.tier;
      }
    }
  } catch (error) {
    console.error("Error retrieving price:", error);
  }
  
  // Default to starter if we can't determine
  return 'starter';
}

serve(async (req) => {
  const origin = req.headers.get("origin");
  
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return corsPreflightResponse(origin);
  }
  
  // Security: Check rate limiting
  const clientIP = req.headers.get("x-forwarded-for") || req.headers.get("x-real-ip") || "unknown";
  if (!checkRateLimit(clientIP)) {
    return corsErrorResponse("Rate limit exceeded", 429, origin);
  }

  // Only allow POST requests for webhooks
  if (req.method !== "POST") {
    return corsErrorResponse("Method not allowed", 405, origin);
  }

  try {
    // Get the Stripe webhook signature from headers
    const signature = req.headers.get("stripe-signature");
    if (!signature) {
      return corsErrorResponse("Missing Stripe signature", 400, origin);
    }

    // Security: Check content length to prevent large payload attacks
    const contentLength = req.headers.get("content-length");
    if (contentLength && parseInt(contentLength) > 1024 * 1024) { // 1MB limit
      return corsErrorResponse("Payload too large", 413, origin);
    }

    // Get the raw request body
    const body = await req.text();

    // Security: Additional body size check
    if (body.length > 1024 * 1024) { // 1MB limit
      return corsErrorResponse("Payload too large", 413, origin);
    }

    // Initialize Stripe with the secret key
    const stripe = new Stripe(Deno.env.get("STRIPE_SECRET_KEY") || "", {
      apiVersion: "2023-10-16",
    });

    // Verify the webhook signature and validate timestamp
    let event;
    try {
      const webhookSecret = Deno.env.get("STRIPE_WEBHOOK_SECRET") || "";
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
      
      // Security: Additional timestamp validation
      const signatureElements = signature.split(',');
      const timestampElement = signatureElements.find(element => element.startsWith('t='));
      if (timestampElement) {
        const timestamp = timestampElement.replace('t=', '');
        if (!validateTimestamp(timestamp)) {
          console.error('Webhook timestamp too old, possible replay attack');
          return corsErrorResponse("Webhook timestamp too old", 400, origin);
        }
      }
    } catch (err) {
      console.error(`Webhook signature verification failed: ${err.message}`);
      return corsErrorResponse(`Webhook signature verification failed: ${err.message}`, 400, origin);
    }

    // Create a Supabase client with the service role key to bypass RLS
    const supabaseAdmin = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      { auth: { persistSession: false } }
    );

    // Handle different event types
    switch (event.type) {
      case "checkout.session.completed": {
        const session = event.data.object;
        
        // Get tier from metadata
        const tier = session.metadata?.tier || 'starter';
        
        // Find the subscription record in our database
        const { data: subscriptions, error: findError } = await supabaseAdmin
          .from("subscriptions")
          .select("*")
          .eq("stripe_session_id", session.id)
          .limit(1);

        if (findError) {
          console.error("Error finding subscription:", findError);
          break;
        }

        if (subscriptions && subscriptions.length > 0) {
          // Security: Prevent double-processing payments
          if (subscriptions[0].is_paid) {
            console.log(`Payment already processed for session ${session.id}`);
            break;
          }
          
          // Calculate expiration date (1 year from now)
          const expiresAt = new Date();
          expiresAt.setFullYear(expiresAt.getFullYear() + 1);
          
          // Update the subscription to paid
          const { error: updateError } = await supabaseAdmin
            .from("subscriptions")
            .update({
              is_paid: true,
              payment_date: new Date().toISOString(),
              stripe_subscription_id: session.subscription,
              stripe_customer_id: session.customer,
              expires_at: expiresAt.toISOString(),
              updated_at: new Date().toISOString()
            })
            .eq("id", subscriptions[0].id);

          if (updateError) {
            console.error("Error updating subscription:", updateError);
          }
        } else {
          // If we don't have a record yet (e.g., guest checkout), create one
          if (session.customer_details && session.customer_details.email) {
            // Security: Validate email format
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(session.customer_details.email)) {
              console.error(`Invalid email format: ${session.customer_details.email}`);
              break;
            }
            
            // Try to find the user by email
            const { data: userData, error: userError } = await supabaseAdmin
              .auth.admin.listUsers();
            
            if (userError) {
              console.error("Error listing users:", userError);
              break;
            }

            // Find the user with matching email
            const user = userData.users.find(u => u.email === session.customer_details.email);
            
            if (user) {
              // Calculate expiration date (1 year from now)
              const expiresAt = new Date();
              expiresAt.setFullYear(expiresAt.getFullYear() + 1);
              
              // Get team limit based on tier
              const teamLimits = { starter: 1, coach: 5, club: 999 };
              const teamLimit = teamLimits[tier] || 1;
              
              // Create a new subscription record
              const { error: insertError } = await supabaseAdmin
                .from("subscriptions")
                .insert({
                  user_id: user.id,
                  stripe_session_id: session.id,
                  stripe_subscription_id: session.subscription,
                  stripe_customer_id: session.customer,
                  is_paid: true,
                  tier: tier,
                  team_limit: teamLimit,
                  subscription_period: 'annual',
                  expires_at: expiresAt.toISOString(),
                  payment_date: new Date().toISOString(),
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString()
                });

              if (insertError) {
                console.error("Error inserting subscription:", insertError);
              }
            }
          }
        }
        break;
      }
      
      case "customer.subscription.created":
      case "customer.subscription.updated": {
        const subscription = event.data.object;
        
        // Get customer email
        const customer = await stripe.customers.retrieve(subscription.customer as string);
        if ('deleted' in customer && customer.deleted) {
          console.error("Customer was deleted");
          break;
        }
        
        const email = customer.email;
        if (!email) {
          console.error("No email found for customer");
          break;
        }
        
        // Find user by email
        const { data: userData, error: userError } = await supabaseAdmin
          .auth.admin.listUsers();
        
        if (userError) {
          console.error("Error listing users:", userError);
          break;
        }
        
        const user = userData.users.find(u => u.email === email);
        if (!user) {
          console.error(`User not found for email: ${email}`);
          break;
        }
        
        // Get tier from subscription
        const tier = await getTierFromSubscription(stripe, subscription);
        const teamLimits = { starter: 1, coach: 5, club: 999 };
        const teamLimit = teamLimits[tier] || 1;
        
        // Check if subscription exists
        const { data: existingSubs, error: findError } = await supabaseAdmin
          .from("subscriptions")
          .select("*")
          .eq("user_id", user.id)
          .eq("stripe_subscription_id", subscription.id)
          .limit(1);
        
        if (findError) {
          console.error("Error finding subscription:", findError);
          break;
        }
        
        // Calculate expiration date based on current period end
        const expiresAt = new Date(subscription.current_period_end * 1000);
        
        if (existingSubs && existingSubs.length > 0) {
          // Update existing subscription
          const { error: updateError } = await supabaseAdmin
            .from("subscriptions")
            .update({
              is_paid: subscription.status === 'active',
              tier: tier,
              team_limit: teamLimit,
              expires_at: expiresAt.toISOString(),
              updated_at: new Date().toISOString()
            })
            .eq("id", existingSubs[0].id);
          
          if (updateError) {
            console.error("Error updating subscription:", updateError);
          }
        } else {
          // Create new subscription record
          const { error: insertError } = await supabaseAdmin
            .from("subscriptions")
            .insert({
              user_id: user.id,
              stripe_subscription_id: subscription.id,
              stripe_customer_id: subscription.customer,
              is_paid: subscription.status === 'active',
              tier: tier,
              team_limit: teamLimit,
              subscription_period: 'annual',
              expires_at: expiresAt.toISOString(),
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });
          
          if (insertError) {
            console.error("Error inserting subscription:", insertError);
          }
        }
        break;
      }
      
      case "customer.subscription.deleted": {
        const subscription = event.data.object;
        
        // Mark subscription as cancelled
        const { error: updateError } = await supabaseAdmin
          .from("subscriptions")
          .update({
            is_paid: false,
            updated_at: new Date().toISOString()
          })
          .eq("stripe_subscription_id", subscription.id);
        
        if (updateError) {
          console.error("Error updating cancelled subscription:", updateError);
        }
        break;
      }
      
      default:
        // Log unexpected event types for monitoring
        console.log(`Unhandled event type ${event.type}`);
    }

    // Return a 200 response to acknowledge receipt of the event
    return new Response(JSON.stringify({ received: true }), {
      headers: { ...getCorsHeaders(origin), "Content-Type": "application/json" },
      status: 200,
    });
  } catch (error) {
    console.error(`Webhook error: ${error.message}`);
    
    // Security: Don't expose internal error details
    return corsErrorResponse("Internal server error", 500, origin);
  }
});
# TRACE OF "AUTO-FILL BEST LINEUP" BUTTON CLICK FLOW

## ERROR
- `[applyRotation] No eligible players for catcher!`
- `Cannot find eligible player for catcher in competitive mode`

## EXACT FLOW

### 1. SimpleSetFirstInning.tsx - `handleAutoFillBestLineup` (line 444)
- Gets `rotationRules` from `useTeam()` hook (line 30)
- Creates `autoFillRules` object with:
  ```javascript
  competitiveMode: rotationRules.competitiveMode || false // line 495
  ```
- This gets the competitive mode from the TEAM's rotation rules

### 2. Calls `generateOptimalLineupWrapper` (line 521)
- File: `lineup-generation-wrapper.ts` (line 15)
- `OLD_ALGORITHM_DISABLED = true` in `kill-switch.ts`
- So it uses NEW algorithm via `generateLineupAdapter`

### 3. `generateLineupAdapter` (adapter.ts line 32)
- Passes `mode: rules.competitiveMode ? 'competitive' : 'recreational'` (line 100)
- Calls `newGenerateLineup` from `index.ts`

### 4. `generateLineup` (index.ts line 97)
- Creates `LineupGenerator` and calls `generate()`

### 5. `LineupGenerator.generate` (generator.ts line 20)
- Uses `CompetitiveSolver` if `mode === 'competitive'` (line 58)

### 6. CompetitiveSolver assigns positions successfully for inning 1

### 7. Back in adapter.ts, `convertToOldLineup` is called (line 140)
- This converts the lineup and calls `applyRotation` for subsequent innings

### 8. `applyRotation` (converter.ts line 45)
- Gets `competitiveMode` from `rotationRules` (line 86)
- Builds eligibility matrix using `player.canPlay(position, competitiveMode)`
- When it tries to find eligible players for catcher, it finds NONE
- In competitive mode, this throws an error (line 176)

## ROOT CAUSE

The competitive mode is being passed through the entire chain correctly.
The issue is that in `applyRotation`, when checking player eligibility for catcher,
NO players are eligible to play catcher in competitive mode.

## POSSIBLE CAUSES

1. **No players have catcher in their teamRoles** - The `convertToPositionPools` function only includes players who have a role for a position
2. **The `canPlay` function is returning false** for all players for catcher position in competitive mode
3. **The playerMap doesn't have the right player data** - It might be using IDs vs names incorrectly

## KEY INSIGHT

The error happens during ROTATION (for innings after the first), not during initial assignment. This suggests:
- The first inning assigns a catcher successfully
- But when rotating for subsequent innings, no other players can play catcher
- In competitive mode, this is a hard error

## NEXT STEPS TO DEBUG

1. Check what players have catcher in their teamRoles
2. Check what the `canPlay` function is doing for catcher position
3. Log the playerMap contents in `applyRotation`
4. Check if the catcher from inning 1 is being excluded from the eligibility check
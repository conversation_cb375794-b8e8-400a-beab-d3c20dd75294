# Dugout Boss - Frontend Documentation

## Overview

The Dugout Boss frontend is built with React, TypeScript, and Vite, using shadcn-ui components and Tailwind CSS for styling. This document outlines the frontend architecture, component structure, and key features.

## Technology Stack

- **Framework**: React with TypeScript
- **Build Tool**: Vite
- **UI Components**: shadcn-ui (based on Radix UI)
- **Styling**: Tailwind CSS
- **State Management**: React Context API
- **Routing**: React Router
- **API Client**: Supabase JS Client
- **PDF Generation**: jsPDF with jspdf-autotable
- **Form Handling**: React Hook Form
- **Date Handling**: date-fns

## Project Structure

```
src/
├── components/       # Reusable UI components
├── contexts/         # React context providers
├── hooks/            # Custom React hooks
├── lib/              # Utility functions
├── pages/            # Page components
├── integrations/     # Integration with external services
├── supabaseClient.ts # Supabase client configuration
└── App.tsx           # Main application component
```

## Key Components

### Layout Components

- **Header**: Navigation header with authentication status
- **Footer**: Application footer with links
- **AdminLayout**: Layout for admin pages with sidebar navigation

### UI Components

- **Button**: Custom button component with baseball-themed variants
- **Card**: Content container with header, content, and footer sections
- **Dialog**: Modal dialog for confirmations and forms
- **Toast**: Notification component for success/error messages
- **Table**: Data table component for displaying lineup information

### Form Components

- **PlayerForm**: Form for adding/editing players
- **LineupForm**: Form for creating/editing lineups
- **AttendanceForm**: Form for marking player attendance
- **PositionSelector**: Component for assigning players to positions

## Context Providers

### AuthContext

Manages authentication state and provides methods for:
- User sign-up, sign-in, and sign-out
- Session management
- Payment status verification

```typescript
interface AuthContextProps {
  user: User | null;
  session: any;
  supabase: SupabaseClient;
  loading: boolean;
  signUp: (email: string, password: string) => Promise<any>;
  signIn: (email: string, password: string) => Promise<any>;
  signOut: () => Promise<void>;
  isPaid: boolean;
  checkPaymentStatus: () => Promise<boolean>;
  paymentInfo: any | null;
}
```

### TeamContext

Manages team state and provides methods for:
- Team selection and creation
- Player management
- Lineup management
- Rotation rules configuration

## Page Components

### Public Pages

- **HomePage**: Landing page with features and call-to-action
- **SignIn**: User authentication page
- **Pricing**: Subscription pricing information
- **FAQ**: Frequently asked questions
- **DemoLogin**: Entry point for demo mode


### Protected Pages

- **Dashboard**: Main user dashboard with team overview and actions
- **TeamRoster**: Player management interface
- **CreateLineup**: Lineup creation wizard
- **SetLineupAttendance**: Player attendance management
- **SimpleSetFirstInning**: First inning position assignment
- **BattingOrder**: Batting order management
- **ViewLineup**: Complete lineup view with export options
- **EditInning**: Interface for modifying inning positions
- **RotationRules**: Configuration for lineup rotation rules
- **UserProfile**: User profile management

### Admin Pages

- **AdminDashboard**: Overview of system statistics
- **AdminUsers**: User management interface
- **AdminBilling**: Subscription and payment management
- **AdminTeams**: Team management across all users
- **AdminSettings**: System configuration settings

## Routing

The application uses React Router for navigation with protected routes that require authentication and payment verification:

```typescript
<BrowserRouter>
  <Routes>
    {/* Public routes */}
    <Route path="/" element={<HomePage />} />
    <Route path="/pricing" element={<Pricing />} />
    <Route path="/faq" element={<FAQ />} />
    <Route path="/demo-login" element={<DemoLogin />} />
    <Route path="/sign-in" element={<SignIn />} />

    {/* Protected routes - require authentication and payment */}
    <Route path="/dashboard" element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
    <Route path="/team-roster" element={<ProtectedRoute><TeamRoster /></ProtectedRoute>} />
    <Route path="/create-lineup" element={<ProtectedRoute><CreateLineup /></ProtectedRoute>} />

    {/* Admin routes - require admin privileges */}
    <Route path="/admin" element={<AdminRoute><AdminDashboard /></AdminRoute>} />
    <Route path="/admin/users" element={<AdminRoute><AdminUsers /></AdminRoute>} />

    {/* Catch all route */}
    <Route path="*" element={<NotFound />} />
  </Routes>
</BrowserRouter>
```

## Authentication and Authorization

- **ProtectedRoute**: Component that redirects unauthenticated or unpaid users
- **AdminRoute**: Component that restricts access to admin users only

## Styling

The application uses a baseball-themed color palette defined in Tailwind:

```typescript
theme: {
  extend: {
    colors: {
      'baseball-navy': '#0A2342',
      'baseball-green': '#2CA58D',
      'baseball-red': '#E84855',
      'baseball-lightblue': '#84BCDA',
      'baseball-lightgreen': '#B4E6D8',
      'baseball-white': '#F7F7F7',
    }
  }
}
```

## Demo Mode

The frontend implements a demo mode that:
- Creates a temporary user account
- Pre-populates with sample data
- Marks the user as paid
- Prevents logging out
- Displays a "DEMO MODE" indicator

## Export Functionality

The application provides export options for lineups:

- **PDF Export**: Uses jsPDF to generate printable lineup sheets
- **CSV Export**: Generates CSV files for spreadsheet import

## Responsive Design

The UI is fully responsive with:
- Mobile-first design approach
- Breakpoints for different screen sizes
- Adaptive layouts for optimal viewing on all devices

## Error Handling

- Toast notifications for success/error messages
- Form validation with error messages
- Fallback UI for loading and error states

## Performance Optimizations

- Code splitting with React.lazy and Suspense
- Memoization of expensive calculations
- Optimized re-renders with useMemo and useCallback

/**
 * Full debug script to trace Vienna's catcher eligibility through the entire system
 */

// Import the actual functions (simulate them here)
console.log('🔍 COMPREHENSIVE VIENNA CATCHER DEBUG');

// Vienna's data as it would appear in the demo
const vienna = {
  id: 'vienna-uuid',
  name: 'Vienna',
  teamRoles: {
    pitcher: 'capable',
    catcher: 'capable', 
    firstBase: 'capable'
  }
};

// Other catchers in the demo
const mikayla = {
  id: 'mikayla-uuid',
  name: '<PERSON><PERSON><PERSON>',
  teamRoles: {
    catcher: 'go-to',
    shortstop: 'capable'
  }
};

const grace = {
  id: 'grace-uuid', 
  name: '<PERSON>',
  teamRoles: {
    catcher: 'capable',
    rightField: 'capable'
  }
};

const allPlayers = [vienna, mikayla, grace];

console.log('\n📋 CATCHER ANALYSIS:');
allPlayers.forEach(player => {
  const catcherRole = player.teamRoles?.catcher;
  console.log(`${player.name}: catcher = ${catcherRole || 'not set'}`);
});

// Simulate position eligibility check
function simulateCanPlayPosition(player, position) {
  const positionToRoleKey = {
    'Pitcher': 'pitcher',
    'Catcher': 'catcher',
    'First Base': 'firstBase',
    'Second Base': 'secondBase',
    'Shortstop': 'shortstop',
    'Third Base': 'thirdBase',
    'Left Field': 'leftField',
    'Center Field': 'centerField',
    'Right Field': 'rightField'
  };

  const roleKey = positionToRoleKey[position];
  
  if (player.teamRoles) {
    const role = player.teamRoles[roleKey];
    
    // Check restrictions first
    if (roleKey && role === 'avoid') {
      console.log(`🚫 HARD RESTRICTION: ${player.name} cannot play ${position} (role: avoid/Never)`);
      return false;
    }
    
    if (roleKey && role === 'never') {
      console.log(`🚫 HARD RESTRICTION: ${player.name} cannot play ${position} (role: never)`);
      return false;
    }
    
    // If player has an explicit positive role, they can play
    if (role && role !== 'unset') {
      console.log(`✅ ${player.name} can play ${position} (role: ${role})`);
      return true;
    }
  }

  console.log(`🚫 ${player.name} cannot play ${position} (no explicit role)`);
  return false;
}

console.log('\n🎯 CATCHER ELIGIBILITY TEST:');
allPlayers.forEach(player => {
  const canCatch = simulateCanPlayPosition(player, 'Catcher');
  console.log(`${player.name} can catch: ${canCatch}`);
});

// Simulate PlayerEligibilityCache behavior
function simulatePlayerEligibilityCache(player) {
  const eligible = new Set();
  const positions = [
    'Pitcher', 'Catcher', 'First Base', 'Second Base', 
    'Shortstop', 'Third Base', 'Left Field', 'Center Field', 'Right Field'
  ];
  
  positions.forEach(pos => {
    if (simulateCanPlayPosition(player, pos)) {
      const key = pos.toLowerCase().replace(' ', '');
      eligible.add(key);
    }
  });
  
  return eligible;
}

console.log('\n📦 PLAYER ELIGIBILITY CACHE SIMULATION:');
allPlayers.forEach(player => {
  const eligible = simulatePlayerEligibilityCache(player);
  console.log(`${player.name} eligible positions: ${Array.from(eligible).join(', ')}`);
});

// Simulate multi-game orchestrator catcher planning
console.log('\n⚾ MULTI-GAME CATCHER PLANNING SIMULATION:');

const numberOfGames = 3;
const inningsPerGame = 6;
const rotatePitcherEvery = 2; // This is used for catcher rotation too

// Count available catchers
const allCatchers = allPlayers.filter(p => 
  p.teamRoles?.catcher && 
  p.teamRoles.catcher !== 'never' &&
  p.teamRoles.catcher !== 'avoid'
);

console.log(`Found ${allCatchers.length} catchers:`, allCatchers.map(p => `${p.name} (${p.teamRoles.catcher})`));

// Calculate catcher slots needed
const catcherSlotsPerGame = Math.ceil(inningsPerGame / rotatePitcherEvery);
const totalCatcherSlots = numberOfGames * catcherSlotsPerGame;

console.log(`Catcher slots per game: ${catcherSlotsPerGame} (${inningsPerGame} innings / ${rotatePitcherEvery} rotation)`);
console.log(`Total catcher slots needed: ${totalCatcherSlots}`);
console.log(`Catcher utilization: ${totalCatcherSlots / allCatchers.length} slots per catcher`);

if (allCatchers.length < totalCatcherSlots / 2) {
  console.log('⚠️ LIMITED CATCHERS: Will need consecutive innings for some catchers');
} else {
  console.log('✅ SUFFICIENT CATCHERS: Good rotation possible');
}

// Simulate catcher rotation priority
console.log('\n🔄 CATCHER PRIORITY SIMULATION:');
const catchersByPriority = allCatchers.sort((a, b) => {
  const aRole = a.teamRoles?.catcher;
  const bRole = b.teamRoles?.catcher;
  
  // Priority: go-to > primary > capable > in the mix > emergency
  const rolePriority = {
    'go-to': 5,
    'primary': 4,
    'capable': 3,
    'in the mix': 2,
    'emergency': 1
  };
  
  return (rolePriority[bRole] || 0) - (rolePriority[aRole] || 0);
});

catchersByPriority.forEach((catcher, index) => {
  console.log(`${index + 1}. ${catcher.name} (${catcher.teamRoles.catcher})`);
});

console.log('\n🚨 POTENTIAL ISSUES TO INVESTIGATE:');
console.log('1. Is Vienna being excluded during position assignment phase?');
console.log('2. Is the strict algorithm respecting catcher rotation settings?');
console.log('3. Are there console logs showing "shouldRotateCatcher = false"?');
console.log('4. Is Vienna being assigned to pitcher instead of catcher?');
console.log('5. Are there errors in the multi-game orchestrator pitcher planning?');

console.log('\n💡 DEBUGGING STEPS FOR USER:');
console.log('1. Open browser console (F12) during batch lineup generation');
console.log('2. Look for Vienna-specific eligibility logs');
console.log('3. Search for "shouldRotateCatcher" messages');
console.log('4. Check if Vienna appears in "plannedPitchers" but not catcher assignments');
console.log('5. Look for "CATCHER PLANNING" warnings about limited catchers');

console.log('\n🔍 HYPOTHESIS RANKING:');
console.log('1. HIGH: Vienna is being assigned to pitch instead of catch due to pitcher planning');
console.log('2. MEDIUM: Catcher rotation is being disabled due to limited catchers'); 
console.log('3. LOW: Position eligibility cache is wrong (our test shows it should work)');
console.log('4. LOW: canPlayPosition function has a bug (our test shows it works for Vienna)');
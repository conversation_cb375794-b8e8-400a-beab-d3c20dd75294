-- URGENT FIX: Resolve infinite recursion in profiles table RLS policies

-- 1. First, check current policies on profiles table
SELECT 
  policyname,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE tablename = 'profiles';

-- 2. Disable <PERSON><PERSON> temporarily to fix the issue
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

-- 3. Drop ALL existing policies on profiles to start fresh
DO $$
DECLARE
  policy record;
BEGIN
  FOR policy IN 
    SELECT policyname 
    FROM pg_policies 
    WHERE schemaname = 'public' AND tablename = 'profiles'
  LOOP
    EXECUTE format('DROP POLICY IF EXISTS %I ON public.profiles', policy.policyname);
  END LOOP;
END $$;

-- 4. Re-enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- 5. Create simple, non-recursive policies for profiles
CREATE POLICY "profiles_select" ON public.profiles
  FOR SELECT USING (true);  -- Allow all authenticated users to read profiles

CREATE POLICY "profiles_insert" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid()::text = id);

CREATE POLICY "profiles_update" ON public.profiles
  FOR UPDATE USING (auth.uid()::text = id);

CREATE POLICY "profiles_delete" ON public.profiles
  FOR DELETE USING (auth.uid()::text = id);

-- 6. Also fix the teams table policies to be simpler
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;

-- Drop all existing policies
DO $$
DECLARE
  policy record;
BEGIN
  FOR policy IN 
    SELECT policyname 
    FROM pg_policies 
    WHERE schemaname = 'public' AND tablename = 'teams'
  LOOP
    EXECUTE format('DROP POLICY IF EXISTS %I ON public.teams', policy.policyname);
  END LOOP;
END $$;

-- Create simple policies for teams
CREATE POLICY "teams_select" ON public.teams
  FOR SELECT USING (
    auth.uid() = user_id 
    OR auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

CREATE POLICY "teams_insert" ON public.teams
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "teams_update" ON public.teams
  FOR UPDATE USING (
    auth.uid() = user_id 
    OR auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

CREATE POLICY "teams_delete" ON public.teams
  FOR DELETE USING (
    auth.uid() = user_id 
    OR auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

-- 7. Grant necessary permissions
GRANT ALL ON public.profiles TO authenticated;
GRANT ALL ON public.teams TO authenticated;

-- 8. Test that the recursion is fixed
SELECT COUNT(*) as profile_count FROM public.profiles;
SELECT COUNT(*) as team_count FROM public.teams;

-- 9. Verify Noah's access
SELECT 
  'Current user email:' as info,
  auth.email() as value
UNION ALL
SELECT 
  'Is admin:' as info,
  CASE 
    WHEN auth.email() IN ('<EMAIL>', '<EMAIL>') 
    THEN 'Yes' 
    ELSE 'No' 
  END as value
UNION ALL
SELECT 
  'Can see teams:' as info,
  CASE 
    WHEN EXISTS (SELECT 1 FROM public.teams LIMIT 1) 
    THEN 'Yes' 
    ELSE 'No' 
  END as value;
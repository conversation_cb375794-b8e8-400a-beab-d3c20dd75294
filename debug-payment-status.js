const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
|| 'https://mhuuptkgohuztjrovpxz.supabase.co';

if (!supabaseServiceKey) {
  console.error('SUPABASE_SERVICE_ROLE_KEY not found in environment');
  process.exit(1);
}

const supabase = createServiceClient();

async function debugPaymentStatus(email) {
  try {
    console.log(`\n=== Debugging payment status for: ${email} ===\n`);

    // 1. Find the user by email
    console.log('1. Looking up user by email...');
    const { data: userData, error: userError } = await supabase.auth.admin.listUsers();
    
    if (userError) {
      console.error('Error listing users:', userError);
      return;
    }

    const user = userData.users.find(u => u.email === email);
    if (!user) {
      console.log(`❌ User with email ${email} not found in auth.users`);
      return;
    }

    console.log(`✅ User found: ${user.id} (${user.email})`);
    console.log(`   Created: ${user.created_at}`);
    console.log(`   Email confirmed: ${user.email_confirmed_at || 'Not confirmed'}`);

    // 2. Check subscriptions table
    console.log('\n2. Checking subscriptions table...');
    const { data: subscriptions, error: subError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', user.id);

    if (subError) {
      console.error('Error querying subscriptions:', subError);
    } else {
      console.log(`Found ${subscriptions.length} subscription record(s):`);
      subscriptions.forEach((sub, index) => {
        console.log(`\n   Subscription ${index + 1}:`);
        console.log(`   - ID: ${sub.id}`);
        console.log(`   - Is Paid: ${sub.is_paid}`);
        console.log(`   - Amount: ${sub.amount}`);
        console.log(`   - Stripe Session ID: ${sub.stripe_session_id || 'None'}`);
        console.log(`   - Payment Date: ${sub.payment_date || 'None'}`);
        console.log(`   - Created: ${sub.created_at}`);
      });
    }

    // 3. Check for any subscriptions with this email in customer details
    console.log('\n3. Checking for orphaned subscription records...');
    const { data: allSubs, error: allSubsError } = await supabase
      .from('subscriptions')
      .select('*');

    if (allSubsError) {
      console.error('Error querying all subscriptions:', allSubsError);
    } else {
      console.log(`Total subscription records in database: ${allSubs.length}`);
      
      // Look for any that might match this email but have wrong user_id
      const potentialMatches = allSubs.filter(sub => 
        sub.stripe_session_id && !subscriptions.find(userSub => userSub.id === sub.id)
      );
      
      if (potentialMatches.length > 0) {
        console.log(`\n   Found ${potentialMatches.length} subscription(s) that might be orphaned:`);
        potentialMatches.forEach((sub, index) => {
          console.log(`   - Subscription ${sub.id}: user_id=${sub.user_id}, amount=${sub.amount}, paid=${sub.is_paid}`);
        });
      }
    }

    // 4. Summary and recommendations
    console.log('\n=== SUMMARY ===');
    const userHasPaidSubscription = subscriptions.some(sub => sub.is_paid === true);
    
    if (userHasPaidSubscription) {
      console.log('✅ User has paid subscription(s) - should have access');
    } else {
      console.log('❌ User has no paid subscriptions');
      
      // Suggest fixes
      console.log('\n=== SUGGESTED FIXES ===');
      console.log('1. Check if payment was processed but webhook failed');
      console.log('2. Look for guest checkout payments not linked to user');
      console.log('3. Manually create subscription record if payment confirmed in Stripe');
    }

  } catch (error) {
    console.error('Error debugging payment status:', error);
  }
}

// Run the debug
const targetEmail = process.argv[2] || '<EMAIL>';
debugPaymentStatus(targetEmail);
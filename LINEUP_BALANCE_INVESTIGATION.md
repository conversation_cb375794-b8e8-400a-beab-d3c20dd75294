# Lineup Balance Investigation Summary

## Date: June 10, 2025

## Core Issue: Why Initial Generation Produces Poor Balance (19/100)

### Root Cause Analysis

The original lineup generation algorithm (`generateCompleteLineup`) prioritizes **rotation rules** over **balance**:

1. **Rotation Frequency Constraint**: When `rotateLineupEvery` is set to 2 or 3, the algorithm only rotates players every 2-3 innings, creating inherent imbalances.

2. **Sequential Generation**: The algorithm generates innings one by one without looking ahead or optimizing globally.

3. **Rule Priority Order**:
   - Position eligibility (hard constraint)
   - Rotation frequency rules
   - Bench streak limits
   - Playing time balance (lowest priority)

### Why "Fix Playing Time" Works Better (72/100)

The optimization function succeeds by:

1. **Overriding User Settings**:
   ```javascript
   equalPlayingTime: true,
   competitiveMode: false,
   rotateLineupEvery: 1, // Forces rotation every inning
   ```

2. **Multiple Attempts**: Tries up to 10 different random seeds to find the best solution

3. **Direct Score Optimization**: Calculates balance score for each attempt and keeps the best one

4. **Simplified Constraints**: Focuses purely on playing time balance rather than respecting all rotation rules

### The "Simple Equal Playing Time" Algorithm

The codebase actually has a perfect fairness algorithm (lines 1826-1870 in utils-enhanced.ts) that:
- Sorts ALL players by least playing time first
- Assigns the 9 players with least playing time to field positions
- Would produce near-perfect balance if used consistently

However, it's only activated when:
- `equalPlayingTime` is true
- AND it's a rotation inning (based on `rotateLineupEvery`)

## Recommendations

### 1. Immediate Fix: Better Defaults
When creating lineups in recreational mode, default to:
- `rotateLineupEvery: 1` (rotate every inning)
- `equalPlayingTime: true`

### 2. Algorithm Enhancement
Add a post-generation optimization step that:
- Runs the "Fix Playing Time" logic automatically after initial generation
- Only applies if balance score is below a threshold (e.g., 70)

### 3. User Education
Make it clear in the UI that:
- Setting rotation frequency > 1 will reduce balance
- "Fix Playing Time" essentially overrides rotation settings for better fairness

## Series Association Issue

New single games may be inheriting series metadata from navigation state. Need to ensure:
1. Clear location state when navigating to create new single games
2. Only preserve series metadata when explicitly in regeneration mode
3. Add validation to prevent orphaned series associations
// Analyze mathematical constraints for realistic scenarios

const analyzeMathConstraints = (players, innings) => {
  const totalFieldSpots = innings * 9;
  const averageInnings = totalFieldSpots / players;
  const minInnings = Math.floor(averageInnings);
  const maxInnings = Math.ceil(averageInnings);
  const playersWithMax = totalFieldSpots % players; // Players who get ceiling
  const playersWithMin = players - playersWithMax; // Players who get floor
  
  // Calculate bench time
  const benchSpotsPerInning = Math.max(0, players - 9);
  const totalBenchSpots = innings * benchSpotsPerInning;
  const avgBenchTime = totalBenchSpots / players;
  const minBenchTime = Math.floor(avgBenchTime);
  const maxBenchTime = Math.ceil(avgBenchTime);
  
  return {
    totalFieldSpots,
    averageInnings: averageInnings.toFixed(2),
    minInnings,
    maxInnings,
    playersWithMax,
    playersWithMin,
    totalBenchSpots,
    avgBenchTime: avgBenchTime.toFixed(2),
    minBenchTime,
    maxBenchTime,
    mathPossible: {
      equalPlayingTime: minInnings === maxInnings,
      maxBenchConstraint: (constraint) => maxBenchTime <= constraint
    }
  };
};

console.log('MATHEMATICAL CONSTRAINT ANALYSIS');
console.log('=====================================\n');

// Test your example: 14 players, 4 innings
console.log('📊 EXAMPLE 1: 14 players, 4 innings (your example)');
const scenario1 = analyzeMathConstraints(14, 4);
console.log(`Total field spots: ${scenario1.totalFieldSpots}`);
console.log(`Average innings per player: ${scenario1.averageInnings}`);
console.log(`Some players will get: ${scenario1.minInnings} innings (${scenario1.playersWithMin} players)`);
console.log(`Other players will get: ${scenario1.maxInnings} innings (${scenario1.playersWithMax} players)`);
console.log(`Average bench time: ${scenario1.avgBenchTime} innings`);
console.log(`Max bench time: ${scenario1.maxBenchTime} innings`);
console.log(`Can enforce max 2 bench innings? ${scenario1.mathPossible.maxBenchConstraint(2) ? 'YES' : 'NO (IMPOSSIBLE)'}`);
console.log(`Perfect equal playing time possible? ${scenario1.mathPossible.equalPlayingTime ? 'YES' : 'NO'}\n`);

// Test screenshot scenario: 12 players, 20 innings  
console.log('📊 EXAMPLE 2: 12 players, 20 innings (your screenshot)');
const scenario2 = analyzeMathConstraints(12, 20);
console.log(`Total field spots: ${scenario2.totalFieldSpots}`);
console.log(`Average innings per player: ${scenario2.averageInnings}`);
console.log(`All players should get: ${scenario2.minInnings} innings`);
console.log(`Perfect equal playing time possible? ${scenario2.mathPossible.equalPlayingTime ? 'YES' : 'NO'}`);
console.log(`Can enforce max 2 bench innings? ${scenario2.mathPossible.maxBenchConstraint(2) ? 'YES' : 'NO'}\n`);

// Test common scenarios
console.log('📊 COMMON SCENARIOS:');
console.log('====================================');
const scenarios = [
  [12, 7, 'Single 7-inning game, 12 players'],
  [15, 5, 'Shortened game, 15 players'],
  [10, 7, 'Small team, 7 innings'],
  [18, 4, 'Large roster, short game']
];

scenarios.forEach(([players, innings, description]) => {
  const analysis = analyzeMathConstraints(players, innings);
  console.log(`${description}:`);
  console.log(`  Field time: ${analysis.minInnings}-${analysis.maxInnings} innings per player`);
  console.log(`  Max bench: ${analysis.maxBenchTime} innings`);
  console.log(`  Equal time possible: ${analysis.mathPossible.equalPlayingTime ? 'YES' : 'NO'}`);
  console.log(`  Max 2 bench possible: ${analysis.mathPossible.maxBenchConstraint(2) ? 'YES' : 'NO'}`);
  console.log('');
});

console.log('🎯 KEY INSIGHTS:');
console.log('=====================================');
console.log('1. Perfect equality is often mathematically impossible');
console.log('2. "Max bench innings" constraints can be impossible');
console.log('3. Algorithm must calculate what\'s possible FIRST');
console.log('4. Focus on minimizing inequality, not eliminating it');
console.log('5. Make constraints adaptive, not rigid');
#!/bin/bash

# Deploy function using remote deployment (no Docker needed)
echo "Deploying send-contact-email function remotely..."

# First, let's set the Resend API key secret
echo "Enter your Resend API key (starts with 're_'):"
read -s RESEND_KEY

if [ -z "$RESEND_KEY" ]; then
    echo "No API key provided. The function will be deployed but won't send emails."
else
    echo "Setting Resend API key..."
    supabase secrets set RESEND_API_KEY=$RESEND_KEY --project-ref mhuuptkgohuztjrovpxz
fi

# Deploy using --no-verify-jwt flag to bypass Docker requirement
echo "Deploying function..."
supabase functions deploy send-contact-email --project-ref mhuuptkgohuztjrovpxz --no-verify-jwt

echo "Done! Test your contact form now."
const { generateCompleteLineup } = require('./src/lib/utils-enhanced.ts');

const players = [
  {
    id: '1',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: false,    // Can pitch
      catcher: true,     // Cannot catch
      firstBase: true,   // Cannot play 1B
      other: null
    },
    positionRatings: { 
      pitcher: 5,        // Elite pitcher
      shortstop: 3,      // Can play SS as backup
      leftField: 2       // Emergency outfield
    },
    isStarPlayer: true
  },
  {
    id: '2',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: false,    // Can pitch
      catcher: true,     // Cannot catch
      firstBase: false,  // Can play 1B
      other: null
    },
    positionRatings: { 
      pitcher: 4,        // Strong pitcher
      firstBase: 4,      // Strong 1B
      leftField: 3       // Decent outfield
    },
    isStarPlayer: false
  },
  {
    id: '3',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: true,     // Cannot pitch
      catcher: false,    // Can catch
      firstBase: false,  // Can play 1B
      other: null
    },
    positionRatings: { 
      catcher: 5,        // Elite catcher
      thirdBase: 4,      // Strong 3B
      rightField: 3      // Decent outfield
    },
    isStarPlayer: true
  },
  {
    id: '4',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      shortstop: 5,      // Elite SS
      secondBase: 4,     // Strong 2B
      centerField: 4     // Strong CF
    },
    isStarPlayer: true
  },
  {
    id: '5',
    name: 'David Chen',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      firstBase: 4,      // Strong 1B
      thirdBase: 3,      // Decent 3B
      leftField: 3       // Decent LF
    },
    isStarPlayer: false
  },
  {
    id: '6',
    name: 'Emma Wilson',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      secondBase: 4,     // Strong 2B
      shortstop: 3,      // Decent SS
      rightField: 3      // Decent RF
    },
    isStarPlayer: false
  },
  {
    id: '7',
    name: 'Frank Miller',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      thirdBase: 4,      // Strong 3B
      firstBase: 3,      // Decent 1B
      centerField: 2     // Emergency CF
    },
    isStarPlayer: false
  },
  {
    id: '8',
    name: 'Grace Lee',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      leftField: 4,      // Strong LF
      centerField: 4,    // Strong CF
      rightField: 3      // Decent RF
    },
    isStarPlayer: false
  },
  {
    id: '9',
    name: 'Henry Davis',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      centerField: 5,    // Elite CF
      leftField: 4,      // Strong LF
      rightField: 4      // Strong RF
    },
    isStarPlayer: true
  }
];

const problematicRules = {
  respectPositionLockouts: true,
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 3,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  equalPlayingTime: false,
  rotateLineupEvery: 2,      // Rotate lineup every 2 innings
  rotatePitcherEvery: 4,     // BUT pitcher rotates every 4 innings
  competitiveMode: true,     // Competitive mode enabled
  competitiveMinPlayingTime: 40,
  keyPositions: ['pitcher', 'catcher', 'shortstop', 'centerField'],
  starPlayerRotationDelay: 2,
  _randomSeed: 12345
};

console.log('🎯 Testing duplicate assignment bug with 9 players...');

try {
  const lineup = generateCompleteLineup(players, 2, problematicRules);
  console.log('✅ Success! Generated lineup without duplicate assignments');
  console.log('Inning 1:', lineup[0].positions);
  console.log('Inning 2:', lineup[1].positions);
} catch (error) {
  console.log('❌ Error:', error.message);
  console.log('Context:', error.context);
}
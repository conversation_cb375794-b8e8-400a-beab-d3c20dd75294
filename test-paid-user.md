# Paid User Account Test Instructions

## ✅ WORKING TEST ACCOUNT
- **Email**: <EMAIL>
- **Password**: testpass123
- **Status**: Paid user with lifetime access
- **User ID**: 1b9e803d-e716-4400-a666-cb9ab113b2f4

## ✅ NOAH'S ADMIN ACCOUNT (Fixed)
- **Email**: <EMAIL>
- **Status**: Admin account with automatic paid access
- **User ID**: a35e5ffc-7288-4513-8281-9342e7d36d07
- **Note**: Now recognized as admin account, should bypass payment checks
- **Password**: Use "Forgot Password" feature to reset if needed

## What's Set Up
1. **4 Teams Created**:
   - U15 Selects (main team with data)
   - U15 Selectcs
   - U13 SELECTS
   - Demo Softball Team

2. **U15 Selects Team Data**:
   - 12 players with realistic names
   - Position restrictions configured:
     - <PERSON>: Can't pitch
     - <PERSON>: Can't catch
     - <PERSON>: Can't play 1B
     - <PERSON>: Can't play middle infield
   - Rotation rules configured for fair play

3. **Sample Lineup**: "vs Eagles - May 25th"
   - 10 players attending, 2 absent
   - First inning positions set
   - Batting order created

## Testing Steps
1. Go to http://localhost:8081/
2. Click "Sign In"
3. <NAME_EMAIL>
4. Should redirect to dashboard (NOT pricing page)
5. Should see teams and lineup data
6. Test creating new lineups
7. Test editing existing lineup
8. Test player roster management
9. Test rotation rules

## Expected Behavior
- No demo mode banner should appear
- Full access to all features
- Data should persist in database
- Should be able to create/edit/delete lineups
- Should be able to manage multiple teams
- Should have access to all paid features

## Key Features to Test
- [ ] Login redirects to dashboard
- [ ] Can view existing teams
- [ ] Can see player roster
- [ ] Can view existing lineup
- [ ] Can create new lineup
- [ ] Can edit lineup positions
- [ ] Can set attendance
- [ ] Can create batting orders
- [ ] Can export lineups (PDF/CSV)
- [ ] Can manage rotation rules
- [ ] Data persists across sessions

## ✅ Password Reset Feature Added
**Forgot Password Functionality:**
- "Forgot your password?" link on sign-in page
- Enter email address to receive reset link
- Reset link leads to `/reset-password` page
- Set new password and automatically redirect to sign-in
- Works for any user account in the system

**How to Reset Noah's Password:**
1. Go to sign-in page
2. Click "Forgot your password?"
3. Enter `<EMAIL>`
4. Check email for reset link
5. Click link to set new password
6. Login with new password

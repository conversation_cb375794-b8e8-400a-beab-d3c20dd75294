import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkUserProtection(email) {
  console.log(`\n🔍 Checking protection status for: ${email}\n`);

  try {
    // 1. Get user from auth.users via profiles
    const { data: profiles, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .ilike('email', `%${email}%`);

    if (profileError) {
      console.log('❌ Error fetching profiles:', profileError);
      return;
    }

    if (!profiles || profiles.length === 0) {
      console.log('❌ No profiles found matching that email pattern');
      
      // Try exact match
      console.log('\n🔍 Trying exact email match...');
      const { data: exactProfile, error: exactError } = await supabase
        .from('profiles')
        .select('*')
        .eq('email', email)
        .single();
        
      if (exactError || !exactProfile) {
        console.log('❌ No exact match found either');
        
        // List all profiles to help debug
        console.log('\n📋 Listing all profiles:');
        const { data: allProfiles, error: allError } = await supabase
          .from('profiles')
          .select('id, email, is_protected, created_at')
          .order('created_at', { ascending: false })
          .limit(20);
          
        if (allProfiles) {
          allProfiles.forEach(p => {
            console.log(`   ${p.email} - Protected: ${p.is_protected || false}`);
          });
        }
      } else {
        profiles.push(exactProfile);
      }
    }

    if (profiles && profiles.length > 0) {
      console.log(`\n✅ Found ${profiles.length} profile(s):\n`);
      
      for (const profile of profiles) {
        console.log(`Profile: ${profile.email}`);
        console.log(`ID: ${profile.id}`);
        console.log(`Protected: ${profile.is_protected || false}`);
        console.log(`Admin: ${profile.is_admin || false}`);
        
        // Check if this email is in the protected accounts list
        const protectedEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>'
        ];
        
        const isProtectedByTrigger = protectedEmails.includes(profile.email);
        console.log(`Protected by trigger: ${isProtectedByTrigger}`);
        
        if (profile.is_protected || isProtectedByTrigger) {
          console.log('\n⚠️  This account is PROTECTED and cannot delete teams!');
        }
        
        // Get teams for this profile
        const { data: teams, error: teamsError } = await supabase
          .from('teams')
          .select('id, name')
          .eq('user_id', profile.id);
          
        if (teams && teams.length > 0) {
          console.log(`\nTeams (${teams.length}):`);
          teams.forEach(team => {
            console.log(`   - ${team.name} (${team.id})`);
          });
        }
        
        console.log('\n---\n');
      }
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

const email = process.argv[2];
if (!email) {
  console.log('Usage: node check-user-protection.js <email or partial email>');
  process.exit(1);
}

checkUserProtection(email);
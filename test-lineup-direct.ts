#!/usr/bin/env tsx

/**
 * Direct Lineup Generation Test
 * 
 * This test directly imports the core lineup generation functions
 * without the supabase dependency issues.
 */

// Mock the supabase import before importing utils-enhanced
const mockSupabase = {
  auth: {
    getUser: () => Promise.resolve({ data: { user: null }, error: null }),
  },
  from: () => ({
    select: () => ({
      eq: () => ({
        single: () => Promise.resolve({ data: null, error: null })
      })
    }),
  })
};

// Mock the module before importing
const Module = require('module');
const originalRequire = Module.prototype.require;
Module.prototype.require = function(id: string) {
  if (id === '@/supabaseClient' || id.includes('supabaseClient')) {
    return { supabase: mockSupabase };
  }
  return originalRequire.apply(this, arguments);
};

// Now we can safely import the types and test data
interface Player {
  id: string;
  name: string;
  jerseyNumber: number;
  attendance: 'present' | 'absent' | 'late';
  positionRestrictions?: string[];
  positionRatings?: Record<string, number>;
  starPositions?: string[];
}

interface InningLineup {
  inning: number;
  positions: Record<string, string | string[]>;
}

interface LineupRules {
  respectPositionLockouts: boolean;
  limitBenchTime: boolean;
  maxConsecutiveBenchInnings?: number;
  allowPitcherRotation: boolean;
  allowCatcherRotation: boolean;
  equalPlayingTime?: boolean;
  rotateLineupEvery?: number;
  rotatePitcherEvery?: number;
  competitiveMode?: boolean;
  competitiveMinPlayingTime?: number;
  keyPositions?: string[];
  starPlayerRotationDelay?: number;
  _randomSeed?: number;
}

// Test data - Noah's U15 Selects team with realistic position restrictions and ratings
const NOAH_SELECTS_PLAYERS: Player[] = [
  {
    id: '1', name: 'Alex Thompson', jerseyNumber: 1, attendance: 'present',
    positionRestrictions: ['Pitcher'],
    positionRatings: { pitcher: 5, firstBase: 3, leftField: 2 },
    starPositions: ['pitcher']
  },
  {
    id: '2', name: 'Ben Rodriguez', jerseyNumber: 2, attendance: 'present',
    positionRestrictions: ['Catcher'],
    positionRatings: { catcher: 5, thirdBase: 4, rightField: 3 },
    starPositions: ['catcher']
  },
  {
    id: '3', name: 'Charlie Kim', jerseyNumber: 3, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { shortstop: 5, secondBase: 4, centerField: 3 },
    starPositions: ['shortstop']
  },
  {
    id: '4', name: 'David Chen', jerseyNumber: 4, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { firstBase: 4, thirdBase: 3, leftField: 3 },
    starPositions: []
  },
  {
    id: '5', name: 'Emma Wilson', jerseyNumber: 5, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { secondBase: 4, shortstop: 3, rightField: 3 },
    starPositions: []
  },
  {
    id: '6', name: 'Frank Miller', jerseyNumber: 6, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { thirdBase: 4, firstBase: 3, centerField: 2 },
    starPositions: []
  },
  {
    id: '7', name: 'Grace Lee', jerseyNumber: 7, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { leftField: 4, centerField: 4, rightField: 3 },
    starPositions: []
  },
  {
    id: '8', name: 'Henry Davis', jerseyNumber: 8, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { centerField: 5, leftField: 4, rightField: 4 },
    starPositions: ['centerField']
  },
  {
    id: '9', name: 'Ivy Johnson', jerseyNumber: 9, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { rightField: 4, centerField: 3, firstBase: 2 },
    starPositions: []
  },
  {
    id: '10', name: 'Jack Brown', jerseyNumber: 10, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { pitcher: 3, firstBase: 4, leftField: 3 },
    starPositions: []
  },
  {
    id: '11', name: 'Kelly White', jerseyNumber: 11, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { catcher: 3, thirdBase: 4, secondBase: 3 },
    starPositions: []
  },
  {
    id: '12', name: 'Liam Garcia', jerseyNumber: 12, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { shortstop: 3, secondBase: 4, centerField: 3 },
    starPositions: []
  },
  {
    id: '13', name: 'Maya Patel', jerseyNumber: 13, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { leftField: 3, rightField: 3, firstBase: 2 },
    starPositions: []
  },
  {
    id: '14', name: 'Noah Fleming', jerseyNumber: 14, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { pitcher: 2, catcher: 2, firstBase: 3 },
    starPositions: []
  }
];

// House league players (no restrictions, equal ratings)
const HOUSE_LEAGUE_PLAYERS: Player[] = Array.from({ length: 12 }, (_, i) => ({
  id: `hl${i + 1}`,
  name: `Player ${i + 1}`,
  jerseyNumber: i + 1,
  attendance: 'present' as const,
  positionRestrictions: [],
  positionRatings: {},
  starPositions: []
}));

// Default competitive rules
const COMPETITIVE_RULES: LineupRules = {
  respectPositionLockouts: true,
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 2,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  equalPlayingTime: false,
  rotateLineupEvery: 2,
  rotatePitcherEvery: 3,
  competitiveMode: true,
  competitiveMinPlayingTime: 0.4,
  keyPositions: ['pitcher', 'catcher', 'shortstop'],
  starPlayerRotationDelay: 1,
  _randomSeed: 12345
};

// Default house league rules
const HOUSE_LEAGUE_RULES: LineupRules = {
  respectPositionLockouts: true,
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 2,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  equalPlayingTime: true,
  rotateLineupEvery: 1,
  rotatePitcherEvery: 2,
  competitiveMode: false,
  _randomSeed: 12345
};

// Simple test function to verify the algorithm works
async function testLineupGeneration() {
  console.log('🚀 TESTING LINEUP GENERATION ALGORITHM');
  console.log('='.repeat(60));
  
  try {
    // Import the actual function (this will use our mocked supabase)
    const { generateCompleteLineup } = await import('./src/lib/utils-enhanced');
    
    console.log('✅ Successfully imported generateCompleteLineup function');
    
    // Test 1: Basic functionality with 8 players
    console.log('\n🧪 Test 1: Basic functionality with 8 players');
    const players8 = HOUSE_LEAGUE_PLAYERS.slice(0, 8);
    const lineup8 = generateCompleteLineup(players8, 5, HOUSE_LEAGUE_RULES);
    console.log(`✅ Generated ${lineup8.length} innings for 8 players`);
    
    // Test 2: 12 players with bench
    console.log('\n🧪 Test 2: 12 players with bench rotation');
    const players12 = HOUSE_LEAGUE_PLAYERS.slice(0, 12);
    const lineup12 = generateCompleteLineup(players12, 6, HOUSE_LEAGUE_RULES);
    console.log(`✅ Generated ${lineup12.length} innings for 12 players`);
    
    // Test 3: Competitive mode with Noah's team
    console.log('\n🧪 Test 3: Competitive mode with Noah\'s Selects team');
    const lineupCompetitive = generateCompleteLineup(NOAH_SELECTS_PLAYERS, 7, COMPETITIVE_RULES);
    console.log(`✅ Generated ${lineupCompetitive.length} innings for competitive team`);
    
    // Verify star players are in correct positions
    const firstInning = lineupCompetitive[0];
    console.log('🔍 First inning positions:');
    console.log(`  Pitcher: ${firstInning.positions.pitcher}`);
    console.log(`  Catcher: ${firstInning.positions.catcher}`);
    console.log(`  Shortstop: ${firstInning.positions.shortstop}`);
    
    // Check if star players are in their positions
    const starPitcher = firstInning.positions.pitcher === 'Alex Thompson';
    const starCatcher = firstInning.positions.catcher === 'Ben Rodriguez';
    const starShortstop = firstInning.positions.shortstop === 'Charlie Kim';
    
    console.log(`✅ Star pitcher in position: ${starPitcher}`);
    console.log(`✅ Star catcher in position: ${starCatcher}`);
    console.log(`✅ Star shortstop in position: ${starShortstop}`);
    
    // Test 4: Position restrictions
    console.log('\n🧪 Test 4: Position restrictions validation');
    let restrictionsRespected = true;
    lineupCompetitive.forEach((inning, index) => {
      // Check Alex Thompson (restricted to pitcher)
      const alexPosition = Object.entries(inning.positions).find(([pos, player]) => 
        player === 'Alex Thompson' && pos !== 'bench'
      );
      if (alexPosition && alexPosition[0] !== 'pitcher') {
        console.log(`❌ Alex Thompson found at ${alexPosition[0]} in inning ${index + 1} (should only be pitcher)`);
        restrictionsRespected = false;
      }
      
      // Check Ben Rodriguez (restricted to catcher)
      const benPosition = Object.entries(inning.positions).find(([pos, player]) => 
        player === 'Ben Rodriguez' && pos !== 'bench'
      );
      if (benPosition && benPosition[0] !== 'catcher') {
        console.log(`❌ Ben Rodriguez found at ${benPosition[0]} in inning ${index + 1} (should only be catcher)`);
        restrictionsRespected = false;
      }
    });
    
    if (restrictionsRespected) {
      console.log('✅ All position restrictions respected');
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('🎉 ALL TESTS PASSED! Lineup generation algorithm is working correctly.');
    console.log('='.repeat(60));
    
    return true;
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    return false;
  }
}

// Run the test
testLineupGeneration().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('Test suite failed to run:', error);
  process.exit(1);
});

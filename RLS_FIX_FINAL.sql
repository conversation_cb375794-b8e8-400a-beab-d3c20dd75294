-- Complete RLS fix - handles all existing policies

-- 1. Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE players ENABLE ROW LEVEL SECURITY;
ALTER TABLE lineups ENABLE ROW LEVEL SECURITY;

-- 2. Drop ALL existing policies (including the ones that were already created)
-- Profiles - add all possible policy names
DROP POLICY IF EXISTS "profiles_select" ON profiles;
DROP POLICY IF EXISTS "profiles_insert" ON profiles;
DROP POLICY IF EXISTS "profiles_update" ON profiles;
DROP POLICY IF EXISTS "profile_select_policy" ON profiles;
DROP POLICY IF EXISTS "profile_insert_policy" ON profiles;
DROP POLICY IF EXISTS "profile_update_policy" ON profiles;
DROP POLICY IF EXISTS "allow_users_own_profile_select" ON profiles;
DROP POLICY IF EXISTS "allow_users_own_profile_insert" ON profiles;
DROP POLICY IF EXISTS "allow_users_own_profile_update" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;

-- Subscriptions
DROP POLICY IF EXISTS "subscriptions_select" ON subscriptions;
DROP POLICY IF EXISTS "subscriptions_insert" ON subscriptions;
DROP POLICY IF EXISTS "subscriptions_update" ON subscriptions;
DROP POLICY IF EXISTS "subscription_select_policy" ON subscriptions;
DROP POLICY IF EXISTS "subscription_insert_policy" ON subscriptions;
DROP POLICY IF EXISTS "subscription_update_policy" ON subscriptions;
DROP POLICY IF EXISTS "allow_users_own_subscription_select" ON subscriptions;
DROP POLICY IF EXISTS "allow_users_own_subscription_insert" ON subscriptions;
DROP POLICY IF EXISTS "allow_users_own_subscription_update" ON subscriptions;
DROP POLICY IF EXISTS "Users can insert own subscription" ON subscriptions;
DROP POLICY IF EXISTS "Users can update own subscription" ON subscriptions;
DROP POLICY IF EXISTS "Users can view own subscription" ON subscriptions;

-- Teams
DROP POLICY IF EXISTS "teams_select" ON teams;
DROP POLICY IF EXISTS "teams_insert" ON teams;
DROP POLICY IF EXISTS "teams_update" ON teams;
DROP POLICY IF EXISTS "teams_delete" ON teams;
DROP POLICY IF EXISTS "Users can create teams" ON teams;
DROP POLICY IF EXISTS "Users can view own teams" ON teams;
DROP POLICY IF EXISTS "Users can update own teams" ON teams;
DROP POLICY IF EXISTS "Users can delete own teams" ON teams;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON teams;
DROP POLICY IF EXISTS "Enable select for users based on user_id" ON teams;
DROP POLICY IF EXISTS "Enable update for users based on user_id" ON teams;
DROP POLICY IF EXISTS "Enable delete for users based on user_id" ON teams;

-- Players
DROP POLICY IF EXISTS "players_select" ON players;
DROP POLICY IF EXISTS "players_insert" ON players;
DROP POLICY IF EXISTS "players_update" ON players;
DROP POLICY IF EXISTS "players_delete" ON players;
DROP POLICY IF EXISTS "Users can manage own players" ON players;
DROP POLICY IF EXISTS "Enable select for users based on team ownership" ON players;
DROP POLICY IF EXISTS "Enable insert for users based on team ownership" ON players;
DROP POLICY IF EXISTS "Enable update for users based on team ownership" ON players;
DROP POLICY IF EXISTS "Enable delete for users based on team ownership" ON players;

-- Lineups
DROP POLICY IF EXISTS "lineups_select" ON lineups;
DROP POLICY IF EXISTS "lineups_insert" ON lineups;
DROP POLICY IF EXISTS "lineups_update" ON lineups;
DROP POLICY IF EXISTS "lineups_delete" ON lineups;
DROP POLICY IF EXISTS "Users can manage own lineups" ON lineups;
DROP POLICY IF EXISTS "Enable select for users based on team ownership" ON lineups;
DROP POLICY IF EXISTS "Enable insert for users based on team ownership" ON lineups;
DROP POLICY IF EXISTS "Enable update for users based on team ownership" ON lineups;
DROP POLICY IF EXISTS "Enable delete for users based on team ownership" ON lineups;

-- 3. Create new policies for profiles
CREATE POLICY "profiles_select_v2" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "profiles_insert_v2" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "profiles_update_v2" ON profiles
    FOR UPDATE USING (auth.uid() = id);

-- 4. Create new policies for subscriptions
CREATE POLICY "subscriptions_select_v2" ON subscriptions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "subscriptions_insert_v2" ON subscriptions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "subscriptions_update_v2" ON subscriptions
    FOR UPDATE USING (auth.uid() = user_id);

-- 5. Create new policies for teams
CREATE POLICY "teams_select_v2" ON teams
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "teams_insert_v2" ON teams
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "teams_update_v2" ON teams
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "teams_delete_v2" ON teams
    FOR DELETE USING (auth.uid() = user_id);

-- 6. Create policies for players
CREATE POLICY "players_select_v2" ON players
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM teams 
            WHERE teams.id = players.team_id 
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "players_insert_v2" ON players
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM teams 
            WHERE teams.id = players.team_id 
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "players_update_v2" ON players
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM teams 
            WHERE teams.id = players.team_id 
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "players_delete_v2" ON players
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM teams 
            WHERE teams.id = players.team_id 
            AND teams.user_id = auth.uid()
        )
    );

-- 7. Create policies for lineups
CREATE POLICY "lineups_select_v2" ON lineups
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM teams 
            WHERE teams.id = lineups.team_id 
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "lineups_insert_v2" ON lineups
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM teams 
            WHERE teams.id = lineups.team_id 
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "lineups_update_v2" ON lineups
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM teams 
            WHERE teams.id = lineups.team_id 
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "lineups_delete_v2" ON lineups
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM teams 
            WHERE teams.id = lineups.team_id 
            AND teams.user_id = auth.uid()
        )
    );

-- 8. Verify all policies
SELECT 
    tablename,
    policyname,
    cmd,
    roles
FROM pg_policies 
WHERE tablename IN ('profiles', 'subscriptions', 'teams', 'players', 'lineups')
ORDER BY tablename, policyname;
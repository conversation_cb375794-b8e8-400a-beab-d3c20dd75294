<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
  <title>Dugout Boss - Offline</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      background-color: #f5f5f5;
      color: #333;
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      padding: 20px;
    }
    
    .container {
      text-align: center;
      max-width: 400px;
      background: white;
      padding: 40px;
      border-radius: 16px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .icon {
      width: 80px;
      height: 80px;
      margin: 0 auto 24px;
      background-color: #0A4F19;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .icon svg {
      width: 48px;
      height: 48px;
      fill: white;
    }
    
    h1 {
      font-size: 24px;
      margin-bottom: 16px;
      color: #0A4F19;
    }
    
    p {
      font-size: 16px;
      line-height: 1.5;
      color: #666;
      margin-bottom: 24px;
    }
    
    .features {
      text-align: left;
      margin: 24px 0;
    }
    
    .feature {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
    }
    
    .feature-icon {
      width: 24px;
      height: 24px;
      margin-right: 12px;
      color: #0A4F19;
    }
    
    .retry-button {
      background-color: #0A4F19;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    
    .retry-button:hover {
      background-color: #083d14;
    }
    
    .retry-button:active {
      transform: translateY(1px);
    }
    
    @media (prefers-color-scheme: dark) {
      body {
        background-color: #1a1a1a;
        color: #f5f5f5;
      }
      
      .container {
        background-color: #2a2a2a;
      }
      
      p {
        color: #ccc;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="icon">
      <svg viewBox="0 0 24 24">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
      </svg>
    </div>
    
    <h1>You're Offline</h1>
    <p>It looks like you've lost your internet connection. Don't worry, your data is safe!</p>
    
    <div class="features">
      <div class="feature">
        <svg class="feature-icon" viewBox="0 0 24 24" fill="currentColor">
          <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
        </svg>
        <span>View your saved lineups</span>
      </div>
      <div class="feature">
        <svg class="feature-icon" viewBox="0 0 24 24" fill="currentColor">
          <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
        </svg>
        <span>Access your team roster</span>
      </div>
      <div class="feature">
        <svg class="feature-icon" viewBox="0 0 24 24" fill="currentColor">
          <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
        </svg>
        <span>Changes will sync when online</span>
      </div>
    </div>
    
    <button class="retry-button" onclick="window.location.reload()">
      Try Again
    </button>
  </div>
  
  <script>
    // Check for connection periodically
    setInterval(() => {
      if (navigator.onLine) {
        window.location.reload();
      }
    }, 5000);
    
    // Listen for online event
    window.addEventListener('online', () => {
      window.location.reload();
    });
  </script>
</body>
</html>
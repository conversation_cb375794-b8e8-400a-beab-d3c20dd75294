// <PERSON>ript to update the hardcoded demo team ID in all relevant files
import { supabase } from "./src/integrations/supabase/node-client.js";
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get directory path for the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Initialize dotenv

async function updateDemoTeamId() {
  console.log('=== UPDATE DEMO TEAM ID ===');
  
  // Get Supabase credentials
      
    
  // Create Supabase client
  
  
  try {
    // Sign in with demo account
    console.log('Signing in with demo account...');
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'demo1234'
    });
    
    if (error) {
      console.error('❌ Failed to sign in with demo account:', error);
      return;
    }
    
    console.log('✅ Successfully signed in as demo user');
    const userId = data.user.id;
    
    // Get the demo team ID
    console.log('Fetching teams for demo user...');
    const { data: teamsData, error: teamsError } = await supabase
      .from('teams')
      .select('*')
      .eq('user_id', userId);
    
    if (teamsError) {
      console.error('❌ Failed to fetch teams:', teamsError);
      return;
    }
    
    if (!teamsData || teamsData.length === 0) {
      console.error('❌ No teams found for demo user');
      
      // Create a new team
      console.log('Creating a new demo team...');
      const { data: newTeam, error: newTeamError } = await supabase
        .from('teams')
        .insert({
          name: "Demo Softball Team",
          user_id: userId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select();
      
      if (newTeamError) {
        console.error('❌ Failed to create new team:', newTeamError);
        return;
      }
      
      console.log('✅ Created new team:', newTeam[0]);
      var teamId = newTeam[0].id;
    } else {
      console.log('✅ Found existing team:', teamsData[0]);
      var teamId = teamsData[0].id;
    }
    
    console.log('Demo team ID:', teamId);
    
    // Check if the team has players
    console.log('Checking if team has players...');
    const { data: playersData, error: playersError } = await supabase
      .from('players')
      .select('*')
      .eq('team_id', teamId);
    
    if (playersError) {
      console.error('❌ Failed to fetch players:', playersError);
    } else {
      console.log(`Team has ${playersData.length} players`);
      
      // If no players, create demo players
      if (playersData.length === 0) {
        console.log('No players found. Creating demo players...');
        
        // Import the getDefaultPlayers function from DemoData.ts
        // Since we can't directly import TypeScript files in Node.js, we'll define the players here
        const defaultPlayers = [
          { id: crypto.randomUUID(), name: "Mikayla", positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: "Shortstop" } },
          { id: crypto.randomUUID(), name: "Finn", positionRestrictions: { pitcher: true, catcher: false, firstBase: true, other: null } },
          { id: crypto.randomUUID(), name: "Avalon", positionRestrictions: { pitcher: false, catcher: false, firstBase: false, other: null } },
          { id: crypto.randomUUID(), name: "Grace", positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: null } },
          { id: crypto.randomUUID(), name: "Bella", positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: null } },
          { id: crypto.randomUUID(), name: "Kenzie", positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: null } },
          { id: crypto.randomUUID(), name: "Presley", positionRestrictions: { pitcher: false, catcher: true, firstBase: true, other: null } },
          { id: crypto.randomUUID(), name: "Avery", positionRestrictions: { pitcher: false, catcher: true, firstBase: true, other: null } },
          { id: crypto.randomUUID(), name: "Elle", positionRestrictions: { pitcher: true, catcher: false, firstBase: false, other: null } },
          { id: crypto.randomUUID(), name: "Vienna", positionRestrictions: { pitcher: true, catcher: false, firstBase: true, other: null } },
          { id: crypto.randomUUID(), name: "Katelyn", positionRestrictions: { pitcher: false, catcher: true, firstBase: true, other: null } },
          { id: crypto.randomUUID(), name: "Morgan", positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: null } }
        ];
        
        // Insert players one by one
        for (const player of defaultPlayers) {
          const { error: playerError } = await supabase
            .from('players')
            .insert({
              id: player.id,
              name: player.name,
              team_id: teamId,
              user_id: userId,
              pitcher_restriction: player.positionRestrictions.pitcher || false,
              catcher_restriction: player.positionRestrictions.catcher || false,
              first_base_restriction: player.positionRestrictions.firstBase || false,
              other_restriction: player.positionRestrictions.other || null,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });
          
          if (playerError) {
            console.error(`❌ Failed to add player ${player.name}:`, playerError);
          } else {
            console.log(`✅ Added player: ${player.name}`);
          }
        }
        
        console.log('✅ Created demo players');
      }
    }
    
    // Check if the team has rotation rules
    console.log('Checking if team has rotation rules...');
    const { data: rulesData, error: rulesError } = await supabase
      .from('rotation_rules')
      .select('*')
      .eq('team_id', teamId);
    
    if (rulesError) {
      console.error('❌ Failed to fetch rotation rules:', rulesError);
    } else {
      console.log(`Team has ${rulesData.length} rotation rules`);
      
      // If no rotation rules, create default rotation rules
      if (rulesData.length === 0) {
        console.log('No rotation rules found. Creating default rotation rules...');
        
        const defaultRules = {
          rotationMethod: "standard",
          equalPlayingTime: true,
          rotatePlayers: true,
          respectPositionLockouts: true,
          allowPitcherRotation: false,
          allowCatcherRotation: true,
          prioritizeOutfieldRotation: true,
          limitBenchTime: true
        };
        
        const { error: rulesError } = await supabase
          .from('rotation_rules')
          .insert({
            id: crypto.randomUUID(),
            team_id: teamId,
            user_id: userId,
            rotation_method: defaultRules.rotationMethod || 'standard',
            equal_playing_time: defaultRules.equalPlayingTime || true,
            rotate_players: defaultRules.rotatePlayers || true,
            respect_position_lockouts: defaultRules.respectPositionLockouts || true,
            allow_pitcher_rotation: defaultRules.allowPitcherRotation || false,
            allow_catcher_rotation: defaultRules.allowCatcherRotation || true,
            prioritize_outfield_rotation: defaultRules.prioritizeOutfieldRotation || true,
            limit_bench_time: defaultRules.limitBenchTime || true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });
        
        if (rulesError) {
          console.error('❌ Failed to add rotation rules:', rulesError);
        } else {
          console.log('✅ Created default rotation rules');
        }
      }
    }
    
    // Check if the team has lineups
    console.log('Checking if team has lineups...');
    const { data: lineupsData, error: lineupsError } = await supabase
      .from('lineups')
      .select('*')
      .eq('team_id', teamId);
    
    if (lineupsError) {
      console.error('❌ Failed to fetch lineups:', lineupsError);
    } else {
      console.log(`Team has ${lineupsData.length} lineups`);
      
      // If no lineups, create demo lineups
      if (lineupsData.length === 0) {
        console.log('No lineups found. Creating demo lineups...');
        
        // Fetch the players we just created
        const { data: createdPlayers, error: createdPlayersError } = await supabase
          .from('players')
          .select('*')
          .eq('team_id', teamId);
        
        if (createdPlayersError) {
          console.error('❌ Failed to fetch created players:', createdPlayersError);
        } else {
          // Create 3 sample lineups
          const sampleLineups = [
            {
              id: crypto.randomUUID(),
              name: "Forest Glade Tournament",
              gameDate: new Date().toISOString().split('T')[0], // Today's date
              createdDate: new Date().toISOString(),
              team_id: teamId,
              attendance: {},
              innings: [],
              battingOrder: []
            },
            {
              id: crypto.randomUUID(),
              name: "Essex Game",
              gameDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Next week
              createdDate: new Date().toISOString(),
              team_id: teamId,
              attendance: {},
              innings: [],
              battingOrder: []
            },
            {
              id: crypto.randomUUID(),
              name: "Sarnia Championship",
              gameDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Two weeks from now
              createdDate: new Date().toISOString(),
              team_id: teamId,
              attendance: {},
              innings: [],
              battingOrder: []
            }
          ];
          
          // Set up attendance for each lineup
          for (const lineup of sampleLineups) {
            const attendance = {};
            createdPlayers.forEach(player => {
              attendance[player.id] = true;
            });
            lineup.attendance = attendance;
            
            // Set up batting order for each lineup
            lineup.battingOrder = createdPlayers.slice(0, Math.min(9, createdPlayers.length)).map(p => p.id);
            
            // Set up innings for each lineup
            for (let i = 1; i <= 6; i++) {
              const availablePlayers = [...createdPlayers];
              availablePlayers.sort(() => Math.random() - 0.5);
              
              const fieldPlayers = availablePlayers.slice(0, 9);
              const benchPlayers = availablePlayers.slice(9);
              
              lineup.innings.push({
                inning: i,
                positions: {
                  leftField: fieldPlayers[0].id,
                  centerField: fieldPlayers[1].id,
                  rightField: fieldPlayers[2].id,
                  thirdBase: fieldPlayers[3].id,
                  shortstop: fieldPlayers[4].id,
                  secondBase: fieldPlayers[5].id,
                  firstBase: fieldPlayers[6].id,
                  catcher: fieldPlayers[7].id,
                  pitcher: fieldPlayers[8].id,
                  bench: benchPlayers.map(p => p.id)
                }
              });
            }
          }
          
          // Insert lineups one by one
          for (const lineup of sampleLineups) {
            // Insert the lineup
            const { data: lineupData, error: lineupError } = await supabase
              .from('lineups')
              .insert({
                id: lineup.id,
                name: lineup.name,
                game_date: lineup.gameDate,
                team_id: teamId,
                user_id: userId,
                created_at: lineup.createdDate,
                updated_at: new Date().toISOString()
              })
              .select();
            
            if (lineupError) {
              console.error(`❌ Failed to add lineup ${lineup.name}:`, lineupError);
              continue;
            }
            
            console.log(`✅ Added lineup: ${lineup.name}`);
            
            // Insert attendance
            const attendanceEntries = Object.entries(lineup.attendance).map(([playerId, isAttending]) => ({
              id: crypto.randomUUID(),
              lineup_id: lineup.id,
              player_id: playerId,
              is_present: isAttending,
              user_id: userId,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }));
            
            const { error: attendanceError } = await supabase
              .from('lineup_attendance')
              .insert(attendanceEntries);
            
            if (attendanceError) {
              console.error(`❌ Failed to add attendance for lineup ${lineup.name}:`, attendanceError);
            } else {
              console.log(`✅ Added attendance for lineup: ${lineup.name}`);
            }
            
            // Insert innings
            for (const inning of lineup.innings) {
              const { error: inningError } = await supabase
                .from('lineup_innings')
                .insert({
                  id: crypto.randomUUID(),
                  lineup_id: lineup.id,
                  inning_number: inning.inning,
                  positions: inning.positions,
                  user_id: userId,
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString()
                });
              
              if (inningError) {
                console.error(`❌ Failed to add inning ${inning.inning} for lineup ${lineup.name}:`, inningError);
              } else {
                console.log(`✅ Added inning ${inning.inning} for lineup: ${lineup.name}`);
              }
            }
            
            // Insert batting order
            const { error: battingOrderError } = await supabase
              .from('batting_orders')
              .insert({
                id: crypto.randomUUID(),
                lineup_id: lineup.id,
                player_order: lineup.battingOrder,
                user_id: userId,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              });
            
            if (battingOrderError) {
              console.error(`❌ Failed to add batting order for lineup ${lineup.name}:`, battingOrderError);
            } else {
              console.log(`✅ Added batting order for lineup: ${lineup.name}`);
            }
          }
          
          console.log('✅ Created demo lineups');
        }
      }
    }
    
    // Define the files to update and the old team ID
    const oldTeamId = '1813c0b0-7b23-4f3f-b6ff-123c0c9ae126';
    const filesToUpdate = [
      {
        path: 'src/pages/DemoLogin.tsx',
        lines: [44, 78]
      },
      {
        path: 'src/contexts/AuthContext.tsx',
        lines: [70, 121, 146]
      },
      {
        path: 'public/demo-direct-fix.js',
        lines: [4]
      },
      {
        path: 'public/demo-direct-fix.html',
        lines: [80]
      }
    ];
    
    // Update each file
    for (const file of filesToUpdate) {
      console.log(`Updating ${file.path}...`);
      
      try {
        // Read the file
        const filePath = path.join(process.cwd(), file.path);
        let content = fs.readFileSync(filePath, 'utf8');
        
        // Replace the old team ID with the new one
        content = content.replace(new RegExp(oldTeamId, 'g'), teamId);
        
        // Write the updated content back to the file
        fs.writeFileSync(filePath, content);
        
        console.log(`✅ Updated ${file.path}`);
      } catch (fileError) {
        console.error(`❌ Failed to update ${file.path}:`, fileError);
      }
    }
    
    console.log('\n=== UPDATE COMPLETE ===');
    console.log('The demo team ID has been updated in all relevant files.');
    console.log('New team ID:', teamId);
    
  } catch (err) {
    console.error('❌ Error during update:', err);
  }
}

updateDemoTeamId();
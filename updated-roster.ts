const NOAH_SELECTS_ROSTER: Player[] = [
  {
    id: '1',
    name: '<PERSON>',
    positionRestrictions: { pitcher: true, catcher: false, firstBase: false, other: null },
    positionRatings: {
      catcher: 5,
      pitcher: 3,
      firstBase: 4,
      secondBase: 3,
      thirdBase: 3,
      shortstop: 4,
      leftField: 2,
      centerField: 2,
      rightField: 2
    } as PositionRatings,
    isStarPlayer: true,
    positionPreferences: {},
    teamRoles: {
      pitcher: 'avoid',
      catcher: 'go-to',
      firstBase: 'capable',
      secondBase: 'fill-in',
      thirdBase: 'fill-in',
      shortstop: 'capable'
    }
  },
  {
    id: '2',
    name: '<PERSON>',
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null },
    positionRatings: {
      leftField: 3,
      centerField: 3,
      rightField: 3
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      pitcher: 'avoid',
      catcher: 'avoid',
      firstBase: 'avoid',
      leftField: 'fill-in',
      centerField: 'fill-in',
      rightField: 'fill-in'
    }
  },
  {
    id: '3',
    name: '<PERSON>',
    positionRestrictions: { pitcher: false, catcher: false, firstBase: false, other: null },
    positionRatings: {
      pitcher: 5,
      firstBase: 4,
      leftField: 3,
      centerField: 3,
      rightField: 3
    } as PositionRatings,
    isStarPlayer: true,
    positionPreferences: {},
    teamRoles: {
      pitcher: 'go-to',
      firstBase: 'capable',
      leftField: 'fill-in',
      centerField: 'fill-in',
      rightField: 'fill-in'
    }
  },
  {
    id: '4',
    name: 'Brayden',
    positionRestrictions: { pitcher: false, catcher: true, firstBase: true, other: null },
    positionRatings: {
      catcher: 4,
      thirdBase: 4,
      shortstop: 3,
      leftField: 3
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      catcher: 'avoid',
      firstBase: 'avoid',
      thirdBase: 'capable',
      shortstop: 'fill-in',
      leftField: 'fill-in'
    }
  },
  {
    id: '5',
    name: 'Charlie',
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null },
    positionRatings: {
      secondBase: 4,
      shortstop: 3,
      leftField: 3,
      centerField: 3,
      rightField: 3
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      pitcher: 'avoid',
      catcher: 'avoid',
      firstBase: 'avoid',
      secondBase: 'capable',
      shortstop: 'fill-in',
      leftField: 'fill-in',
      centerField: 'fill-in',
      rightField: 'fill-in'
    }
  },
  {
    id: '6',
    name: 'Cyler',
    positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: "Right field" },
    positionRatings: {
      pitcher: 4,
      firstBase: 3,
      thirdBase: 3,
      leftField: 4,
      centerField: 4
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      pitcher: 'capable',
      catcher: 'avoid',
      firstBase: 'fill-in',
      thirdBase: 'fill-in',
      leftField: 'capable',
      centerField: 'capable',
      rightField: 'avoid'
    }
  },
  {
    id: '7',
    name: 'Chase',
    positionRestrictions: { pitcher: true, catcher: true, firstBase: false, other: null },
    positionRatings: {
      firstBase: 5,
      secondBase: 3,
      thirdBase: 3,
      shortstop: 3,
      leftField: 3,
      centerField: 3,
      rightField: 3
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      pitcher: 'avoid',
      catcher: 'avoid',
      firstBase: 'go-to',
      secondBase: 'fill-in',
      thirdBase: 'fill-in',
      shortstop: 'fill-in',
      leftField: 'fill-in',
      centerField: 'fill-in',
      rightField: 'fill-in'
    }
  },
  {
    id: '8',
    name: 'Joel',
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null },
    positionRatings: {
      secondBase: 4,
      shortstop: 4,
      leftField: 3,
      centerField: 3,
      rightField: 3
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      pitcher: 'avoid',
      catcher: 'avoid',
      firstBase: 'avoid',
      secondBase: 'capable',
      shortstop: 'capable',
      leftField: 'fill-in',
      centerField: 'fill-in',
      rightField: 'fill-in'
    }
  },
  {
    id: '9',
    name: 'Kellen',
    positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: null },
    positionRatings: {
      pitcher: 4,
      firstBase: 4,
      thirdBase: 3,
      leftField: 3,
      centerField: 3,
      rightField: 3
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      pitcher: 'capable',
      catcher: 'avoid',
      firstBase: 'capable',
      thirdBase: 'fill-in',
      leftField: 'fill-in',
      centerField: 'fill-in',
      rightField: 'fill-in'
    }
  },
  {
    id: '10',
    name: 'Mackston',
    positionRestrictions: { pitcher: true, catcher: false, firstBase: false, other: "Middle Infield" },
    positionRatings: {
      catcher: 4,
      firstBase: 3,
      thirdBase: 4,
      leftField: 3,
      centerField: 3,
      rightField: 3
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      pitcher: 'avoid',
      catcher: 'capable',
      firstBase: 'fill-in',
      secondBase: 'avoid',
      thirdBase: 'capable',
      shortstop: 'avoid',
      leftField: 'fill-in',
      centerField: 'fill-in',
      rightField: 'fill-in'
    }
  },
  {
    id: '11',
    name: 'Rogan',
    positionRestrictions: { pitcher: false, catcher: false, firstBase: false, other: null },
    positionRatings: {
      pitcher: 4,
      firstBase: 3,
      secondBase: 3,
      thirdBase: 3,
      shortstop: 3,
      leftField: 3,
      centerField: 3,
      rightField: 3
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      pitcher: 'capable',
      firstBase: 'fill-in',
      secondBase: 'fill-in',
      thirdBase: 'fill-in',
      shortstop: 'fill-in',
      leftField: 'fill-in',
      centerField: 'fill-in',
      rightField: 'fill-in'
    }
  },
  {
    id: '12',
    name: 'Nick',
    positionRestrictions: { pitcher: true, catcher: true, firstBase: false, other: "3B/MI/SS/2B" },
    positionRatings: {
      firstBase: 4,
      leftField: 3,
      centerField: 3,
      rightField: 3
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      pitcher: 'avoid',
      catcher: 'avoid',
      firstBase: 'capable',
      secondBase: 'avoid',
      thirdBase: 'avoid',
      shortstop: 'avoid',
      leftField: 'fill-in',
      centerField: 'fill-in',
      rightField: 'fill-in'
    }
  },
  {
    id: '13',
    name: 'Austin',
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null },
    positionRatings: {
      secondBase: 3,
      thirdBase: 3,
      shortstop: 3,
      leftField: 3,
      centerField: 3,
      rightField: 3
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      pitcher: 'avoid',
      catcher: 'avoid',
      firstBase: 'avoid',
      secondBase: 'fill-in',
      thirdBase: 'fill-in',
      shortstop: 'fill-in',
      leftField: 'fill-in',
      centerField: 'fill-in',
      rightField: 'fill-in'
    }
  },
  {
    id: '14',
    name: 'Drew',
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null },
    positionRatings: {
      leftField: 3,
      centerField: 3,
      rightField: 3
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      pitcher: 'avoid',
      catcher: 'avoid',
      firstBase: 'avoid',
      leftField: 'fill-in',
      centerField: 'fill-in',
      rightField: 'fill-in'
    }
  }
];

// Summary of teamRoles generation:

// Avalon:
//   pitcher: avoid
//   catcher: go-to
//   firstBase: capable
//   secondBase: fill-in
//   thirdBase: fill-in
//   shortstop: capable

// Avery:
//   pitcher: avoid
//   catcher: avoid
//   firstBase: avoid
//   leftField: fill-in
//   centerField: fill-in
//   rightField: fill-in

// Tyler:
//   pitcher: go-to
//   firstBase: capable
//   leftField: fill-in
//   centerField: fill-in
//   rightField: fill-in

// Brayden:
//   catcher: avoid
//   firstBase: avoid
//   thirdBase: capable
//   shortstop: fill-in
//   leftField: fill-in

// Charlie:
//   pitcher: avoid
//   catcher: avoid
//   firstBase: avoid
//   secondBase: capable
//   shortstop: fill-in
//   leftField: fill-in
//   centerField: fill-in
//   rightField: fill-in

// Cyler:
//   pitcher: capable
//   catcher: avoid
//   firstBase: fill-in
//   thirdBase: fill-in
//   leftField: capable
//   centerField: capable
//   rightField: avoid

// Chase:
//   pitcher: avoid
//   catcher: avoid
//   firstBase: go-to
//   secondBase: fill-in
//   thirdBase: fill-in
//   shortstop: fill-in
//   leftField: fill-in
//   centerField: fill-in
//   rightField: fill-in

// Joel:
//   pitcher: avoid
//   catcher: avoid
//   firstBase: avoid
//   secondBase: capable
//   shortstop: capable
//   leftField: fill-in
//   centerField: fill-in
//   rightField: fill-in

// Kellen:
//   pitcher: capable
//   catcher: avoid
//   firstBase: capable
//   thirdBase: fill-in
//   leftField: fill-in
//   centerField: fill-in
//   rightField: fill-in

// Mackston:
//   pitcher: avoid
//   catcher: capable
//   firstBase: fill-in
//   secondBase: avoid
//   thirdBase: capable
//   shortstop: avoid
//   leftField: fill-in
//   centerField: fill-in
//   rightField: fill-in

// Rogan:
//   pitcher: capable
//   firstBase: fill-in
//   secondBase: fill-in
//   thirdBase: fill-in
//   shortstop: fill-in
//   leftField: fill-in
//   centerField: fill-in
//   rightField: fill-in

// Nick:
//   pitcher: avoid
//   catcher: avoid
//   firstBase: capable
//   secondBase: avoid
//   thirdBase: avoid
//   shortstop: avoid
//   leftField: fill-in
//   centerField: fill-in
//   rightField: fill-in

// Austin:
//   pitcher: avoid
//   catcher: avoid
//   firstBase: avoid
//   secondBase: fill-in
//   thirdBase: fill-in
//   shortstop: fill-in
//   leftField: fill-in
//   centerField: fill-in
//   rightField: fill-in

// Drew:
//   pitcher: avoid
//   catcher: avoid
//   firstBase: avoid
//   leftField: fill-in
//   centerField: fill-in
//   rightField: fill-in

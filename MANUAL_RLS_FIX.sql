-- Run this in Supabase SQL Editor to fix profile and subscription RLS policies
-- This will allow users to create their own profiles and subscriptions

-- 1. Enable RLS on both tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- 2. Drop any existing policies that might conflict
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON profiles;
DROP POLICY IF EXISTS "Enable select for authenticated users" ON profiles;
DROP POLICY IF EXISTS "Enable update for authenticated users" ON profiles;

DROP POLICY IF EXISTS "Users can insert own subscription" ON subscriptions;
DROP POLICY IF EXISTS "Users can update own subscription" ON subscriptions;
DROP POLICY IF EXISTS "Users can view own subscription" ON subscriptions;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON subscriptions;
DROP POLICY IF EXISTS "Enable select for authenticated users" ON subscriptions;
DROP POLICY IF EXISTS "Enable update for authenticated users" ON subscriptions;

-- 3. Create new permissive policies for profiles
CREATE POLICY "Users can insert own profile" ON profiles
    FOR INSERT 
    TO authenticated
    WITH CHECK (auth.uid()::text = id);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE 
    TO authenticated
    USING (auth.uid()::text = id)
    WITH CHECK (auth.uid()::text = id);

CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT 
    TO authenticated
    USING (auth.uid()::text = id);

-- 4. Create new permissive policies for subscriptions  
CREATE POLICY "Users can insert own subscription" ON subscriptions
    FOR INSERT 
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own subscription" ON subscriptions
    FOR UPDATE 
    TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view own subscription" ON subscriptions
    FOR SELECT 
    TO authenticated
    USING (auth.uid() = user_id);

-- 5. Verify policies were created
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename IN ('profiles', 'subscriptions')
ORDER BY tablename, policyname;
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkUserSubscription(email) {
  console.log(`\nChecking subscription for: ${email}`);
  console.log('=' + '='.repeat(50));

  try {
    // First get the user from profiles
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, email, full_name')
      .eq('email', email)
      .single();

    if (profileError || !profile) {
      console.log('❌ User profile not found');
      return;
    }

    console.log('\n✓ Found user profile:');
    console.log('  ID:', profile.id);
    console.log('  Name:', profile.full_name);
    console.log('  Email:', profile.email);

    // Check subscription
    const { data: subscription, error: subError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', profile.id)
      .single();

    if (subError || !subscription) {
      console.log('\n❌ No subscription found');
      return;
    }

    console.log('\n✓ Found subscription:');
    console.log('  Is Paid:', subscription.is_paid);
    console.log('  Amount:', subscription.amount);
    console.log('  Currency:', subscription.currency);
    console.log('  Payment Date:', subscription.payment_date);
    console.log('  Created:', subscription.created_at);
    
    // Check if tier columns exist
    if ('tier' in subscription) {
      console.log('  Tier:', subscription.tier || 'Not set');
      console.log('  Team Limit:', subscription.team_limit || 'Not set');
    } else {
      console.log('\n⚠️  WARNING: tier and team_limit columns are missing!');
      console.log('  This is why the user shows as "Free" in the admin panel.');
      console.log('  Run the migration SQL to fix this.');
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

// Get email from command line
const email = process.argv[2];

if (!email) {
  console.log('Usage: node check-user-subscription.js <email>');
  console.log('Example: node check-user-subscription.js <EMAIL>');
  process.exit(1);
}

checkUserSubscription(email);
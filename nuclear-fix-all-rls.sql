-- NUCLEAR OPTION: COMPLETELY REBUILD RLS FROM SCRATCH
-- Run this in Supabase SQL Editor

-- 1. DISABLE ALL RLS (temporary)
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.teams DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.players DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.lineups DISABLE ROW LEVEL SECURITY;

-- 2. FIX THE SPECIFIC USER
DO $$
DECLARE
    v_user_id UUID;
BEGIN
    SELECT id INTO v_user_id FROM auth.users WHERE email = '<EMAIL>';
    
    IF v_user_id IS NOT NULL THEN
        -- Profile
        INSERT INTO profiles (id, email, created_at, updated_at)
        VALUES (v_user_id, '<EMAIL>', NOW(), NOW())
        ON CONFLICT (id) DO UPDATE SET email = EXCLUDED.email, updated_at = NOW();
        
        -- Subscription
        INSERT INTO subscriptions (
            user_id, is_paid, tier, team_limit, currency, amount, 
            payment_date, created_at, updated_at
        )
        VALUES (
            v_user_id, true, 'club', 999, 'usd', 0, 
            NOW(), NOW(), NOW()
        )
        ON CONFLICT (user_id) DO UPDATE SET
            is_paid = true, tier = 'club', team_limit = 999, updated_at = NOW();
        
        -- Create a team
        INSERT INTO teams (user_id, name, created_at, updated_at)
        VALUES (v_user_id, 'My Team', NOW(), NOW())
        ON CONFLICT DO NOTHING;
        
        RAISE NOTICE 'User completely fixed!';
    END IF;
END $$;

-- 3. DROP ALL OLD POLICIES
DO $$
DECLARE
    rec RECORD;
BEGIN
    FOR rec IN 
        SELECT tablename, policyname 
        FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename IN ('profiles', 'subscriptions', 'teams', 'players', 'lineups')
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON %I', rec.policyname, rec.tablename);
    END LOOP;
END $$;

-- 4. RE-ENABLE RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.players ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.lineups ENABLE ROW LEVEL SECURITY;

-- 5. CREATE SIMPLE, WORKING POLICIES

-- Profiles
CREATE POLICY "profiles_all" ON profiles FOR ALL 
    USING (auth.uid() = id) 
    WITH CHECK (auth.uid() = id);

-- Subscriptions  
CREATE POLICY "subscriptions_all" ON subscriptions FOR ALL
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Teams
CREATE POLICY "teams_all" ON teams FOR ALL
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Players
CREATE POLICY "players_all" ON players FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM teams 
            WHERE teams.id = players.team_id 
            AND teams.user_id = auth.uid()
        )
    );

-- Lineups
CREATE POLICY "lineups_all" ON lineups FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM teams 
            WHERE teams.id = lineups.team_id 
            AND teams.user_id = auth.uid()
        )
    );

-- 6. FIX THE AUTH TRIGGER
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Create profile
    INSERT INTO public.profiles (id, email, created_at, updated_at)
    VALUES (new.id, new.email, now(), now())
    ON CONFLICT (id) DO NOTHING;
    
    -- Create subscription
    INSERT INTO public.subscriptions (
        user_id, is_paid, tier, team_limit, created_at, updated_at
    )
    VALUES (new.id, false, 'starter', 1, now(), now())
    ON CONFLICT (user_id) DO NOTHING;
    
    RETURN new;
END;
$$;

DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 7. VERIFY EVERYTHING
SELECT 
    'FINAL CHECK' as status,
    u.email,
    p.id IS NOT NULL as has_profile,
    s.id IS NOT NULL as has_subscription,
    s.is_paid,
    s.tier,
    s.team_limit,
    COUNT(t.id) as team_count
FROM auth.users u
LEFT JOIN profiles p ON p.id = u.id
LEFT JOIN subscriptions s ON s.user_id = u.id
LEFT JOIN teams t ON t.user_id = u.id
WHERE u.email = '<EMAIL>'
GROUP BY u.id, u.email, p.id, s.id, s.is_paid, s.tier, s.team_limit;

-- Show new policies
SELECT tablename, policyname, cmd
FROM pg_policies
WHERE schemaname = 'public'
AND tablename IN ('profiles', 'subscriptions', 'teams')
ORDER BY tablename;
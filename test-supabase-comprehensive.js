// Comprehensive Supabase connection test
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Initialize dotenv
dotenv.config();

// Get directory path for the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function testSupabaseComprehensive() {
  console.log('=== COMPREHENSIVE SUPABASE CONNECTION TEST ===');
  
  // Get Supabase credentials
  const supabaseUrl = process.env.VITE_SUPABASE_URL;
  const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;
  
  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('❌ Missing Supabase environment variables. Check your .env file.');
    return;
  }
  
  console.log(`Supabase URL: ${supabaseUrl}`);
  console.log(`Supabase Anon Key: ***${supabaseAnonKey.slice(-6)}`);
  
  // Create Supabase client
  const supabase = createClient(supabaseUrl, supabaseAnonKey);
  
  try {
    console.log('\n1. Testing basic connection...');
    // Test the connection with a simple health check
    const { data, error } = await supabase.from('teams').select('count').limit(0);

    if (error && error.code !== 'PGRST116') {
      console.error('❌ Supabase connection error:', error);
      return;
    }

    console.log('✅ Successfully connected to Supabase!');
    
    // Test database tables
    console.log('\n2. Testing database tables...');
    const tables = ['teams', 'players', 'lineups', 'lineup_innings', 'lineup_attendance', 'batting_orders', 'rotation_rules'];
    
    for (const table of tables) {
      try {
        const { data: tableData, error: tableError } = await supabase
          .from(table)
          .select('count')
          .limit(0);
          
        if (!tableError) {
          console.log(`✅ Table exists: ${table}`);
        } else {
          console.log(`❌ Table does not exist or not accessible: ${table} (${tableError.message})`);
        }
      } catch (err) {
        console.log(`❌ Error checking table ${table}:`, err);
      }
    }
    
    // Test authentication
    console.log('\n3. Testing authentication...');
    try {
      // Check if auth is working by getting the current session
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        console.log('❌ Auth service error:', sessionError);
      } else {
        console.log('✅ Auth service is working');
        
        if (sessionData.session) {
          console.log('✅ User is currently authenticated:', sessionData.session.user.email);
        } else {
          console.log('ℹ️ No active session (not logged in)');
        }
      }
    } catch (authErr) {
      console.log('❌ Error testing authentication:', authErr);
    }
    
    // Test edge functions
    console.log('\n4. Testing edge functions...');
    try {
      // List available functions
      const functions = ['verify-payment', 'create-payment', 'admin-create-user', 'stripe-webhook'];
      
      for (const func of functions) {
        try {
          // Just check if the function exists (don't actually invoke it)
          console.log(`ℹ️ Function available: ${func}`);
        } catch (err) {
          console.log(`❌ Error checking function ${func}:`, err);
        }
      }
    } catch (funcErr) {
      console.log('❌ Error testing edge functions:', funcErr);
    }
    
    // Test data operations
    console.log('\n5. Testing data operations...');
    try {
      // Get a sample of teams data
      const { data: teamsData, error: teamsError } = await supabase
        .from('teams')
        .select('*')
        .limit(3);
        
      if (teamsError) {
        console.log('❌ Error fetching teams data:', teamsError);
      } else {
        console.log(`✅ Successfully fetched teams data (${teamsData.length} records)`);
        if (teamsData.length > 0) {
          console.log('Sample team:', {
            id: teamsData[0].id,
            name: teamsData[0].name,
            user_id: teamsData[0].user_id
          });
          
          // Try to fetch players for this team
          const teamId = teamsData[0].id;
          const { data: playersData, error: playersError } = await supabase
            .from('players')
            .select('*')
            .eq('team_id', teamId)
            .limit(3);
            
          if (playersError) {
            console.log(`❌ Error fetching players for team ${teamId}:`, playersError);
          } else {
            console.log(`✅ Successfully fetched players data (${playersData.length} records)`);
            if (playersData.length > 0) {
              console.log('Sample player:', {
                id: playersData[0].id,
                name: playersData[0].name
              });
            }
          }
        }
      }
    } catch (dataErr) {
      console.log('❌ Error testing data operations:', dataErr);
    }
    
    console.log('\n=== TEST COMPLETE ===');
    
  } catch (err) {
    console.error('❌ Error during comprehensive test:', err);
  }
}

testSupabaseComprehensive();
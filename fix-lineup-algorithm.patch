--- a/src/lib/utils-enhanced.ts
+++ b/src/lib/utils-enhanced.ts
@@ -1452,25 +1452,26 @@ class LineupRotator {
   private shouldRotateThisInning(inning: number): boolean {
     // Never rotate in the first inning
     if (inning === 1) {
       this.debug(inning, '🔄 First inning - no rotation needed');
       return false;
     }
     
     const rotateEvery = this.rules.rotateLineupEvery || 1;
-    const standardRotation = (inning - 1) % rotateEvery === 0;
+    
+    // Fix: Proper modulo calculation for rotation
+    // For rotateEvery = 2: rotate at innings 3, 5, 7...
+    // For rotateEvery = 3: rotate at innings 4, 7, 10...
+    const standardRotation = (inning - 1) % rotateEvery === 0 && inning > 1;

-    // ENHANCED FAIRNESS: In equal playing time mode, be much more aggressive about rotation
+    // Don't override rotation frequency even in equal playing time mode
     if (this.rules.equalPlayingTime) {
-      // In fairness mode, rotate EVERY inning after the first to maximize equality
-      const fairnessRotation = true; // Always rotate for fairness
+      // Respect the configured rotation frequency
+      const fairnessRotation = standardRotation;
       
-      this.debug(inning, '⚖️ FAIRNESS MODE: Enhanced rotation for equal playing time', {
+      this.debug(inning, '⚖️ FAIRNESS MODE: Using configured rotation frequency', {
         inning,
         rotateEvery,
         standardRotation,
         fairnessRotation,
-        reason: 'equalPlayingTime enabled - rotating every inning for maximum fairness'
+        reason: 'equalPlayingTime enabled - respecting rotateLineupEvery setting'
       });
       
       return fairnessRotation;
@@ -1133,24 +1134,37 @@ class LineupRotator {
   private getBenchStreaks(allInnings: InningLineup[], upToInning: number): Map<string, number> {
     const streaks = new Map<string, number>();
+    const lastFieldInning = new Map<string, number>();

     if (allInnings.length === 0 || upToInning <= 0) {
       return streaks;
     }

-    // Initialize all players to 0
+    // Initialize all players
     const firstInning = allInnings[0];
     if (firstInning) {
       // Get all players from first inning
       const allPlayers = new Set<string>();
       Object.values(firstInning.positions).forEach(player => {
         if (typeof player === 'string' && player) {
           allPlayers.add(player);
         } else if (Array.isArray(player)) {
           player.forEach(p => allPlayers.add(p));
         }
       });
-      allPlayers.forEach(player => streaks.set(player, 0));
+      allPlayers.forEach(player => {
+        streaks.set(player, 0);
+        lastFieldInning.set(player, -1);
+      });
     }

-    // Count consecutive bench innings going backwards from upToInning
-    for (let i = Math.min(upToInning - 1, allInnings.length - 1); i >= 0; i--) {
+    // Count consecutive bench innings going FORWARD (not backwards)
+    for (let i = 0; i < Math.min(upToInning, allInnings.length); i++) {
       const inning = allInnings[i];
       if (!inning) break;

-      // First, reset streak for players who played in the field
+      // Check who's on the field
+      const fieldPlayers = new Set<string>();
       Object.entries(inning.positions).forEach(([position, player]) => {
         if (position !== 'bench' && typeof player === 'string' && player) {
-          streaks.set(player, 0);
+          fieldPlayers.add(player);
+          lastFieldInning.set(player, i);
         }
       });

-      // Then increment streak for bench players
+      // Update bench streaks
       inning.positions.bench.forEach(player => {
         if (player) {
-          const currentStreak = streaks.get(player) || 0;
-          streaks.set(player, currentStreak + 1);
+          const lastField = lastFieldInning.get(player) ?? -1;
+          // If they haven't played yet, or have been on bench since last field time
+          if (lastField === -1) {
+            // They've been on bench since start
+            streaks.set(player, i + 1);
+          } else {
+            // They've been on bench since their last field inning
+            streaks.set(player, i - lastField);
+          }
         }
       });
+      
+      // Reset streak for field players
+      fieldPlayers.forEach(player => {
+        streaks.set(player, 0);
+      });
     }

     return streaks;
   }
   
@@ -925,6 +939,8 @@ class LineupRotator {
   private createRotationPlan(
     current: InningLineup,
     players: Player[],
     playerStats?: Map<string, { fieldInnings: number; benchInnings: number }>,
     allInnings?: InningLineup[]
   ): RotationPlan {
+    // Track who was force-rotated
+    const forcedPlayers = new Set<string>();
+    
     const plan: RotationPlan = {
       fieldAssignments: new Map(),
@@ -1017,6 +1033,7 @@ class LineupRotator {
       if (benchStreak > this.rules.maxConsecutiveBenchInnings) {
         const playerOnBench = benchPlayers.find(p => p === playerName);
         if (playerOnBench) {
+          forcedPlayers.add(playerName);
           // Find someone to swap with
           let swapped = false;
@@ -1119,6 +1136,13 @@ class LineupRotator {
         benchAssignments: plan.benchAssignments
       });
     }
+    
+    // Reset bench streaks for force-rotated players
+    forcedPlayers.forEach(playerName => {
+      this.debug(current.inning + 1, `🔄 Resetting bench streak for force-rotated player: ${playerName}`);
+      // This will be reflected in the next inning's bench streak calculation
+    });

     return plan;
   }
   
@@ -169,6 +193,22 @@ class ConstraintSolver {
   constructor(
     private players: Player[],
-    private eligibilityCache: PlayerEligibilityCache
+    private eligibilityCache: PlayerEligibilityCache,
+    private random?: { shuffle: <T>(arr: T[]) => T[] }
   ) {}
   
+  private shuffleArray<T>(arr: T[]): T[] {
+    if (this.random) {
+      return this.random.shuffle(arr);
+    }
+    // Default random shuffle
+    return [...arr].sort(() => Math.random() - 0.5);
+  }
+  
+  private getPositionOrder(): string[] {
+    const positions = [
+      'pitcher', 'catcher', 'firstBase', 'secondBase', 
+      'thirdBase', 'shortstop', 'leftField', 'centerField', 'rightField'
+    ];
+    // Randomize order to reduce bias
+    return this.shuffleArray(positions);
+  }
@@ -282,7 +322,7 @@ class ConstraintSolver {
     competitiveRatingScale?: CompetitiveRatingScale
   ): Record<string, string> | null {
     const assignments: Record<string, string> = {};
     const assignedPlayers = new Set<string>();
-    const positions = this.getPositions();
+    const positions = this.getPositionOrder(); // Now randomized

     for (const position of positions) {
@@ -340,6 +380,18 @@ class ConstraintSolver {
         return priorityB - priorityA;
       }
       
+      // Tie-break by playing time (less time = higher priority)
+      if (playerStats) {
+        const statsA = playerStats.get(a.id);
+        const statsB = playerStats.get(b.id);
+        const timeA = statsA?.fieldInnings || 0;
+        const timeB = statsB?.fieldInnings || 0;
+        
+        if (timeA !== timeB) {
+          return timeA - timeB;
+        }
+      }
+      
       // Otherwise maintain original order
       return 0;
     });
@@ -2901,7 +2953,7 @@ export function generateOptimalLineup(
   const random = new SeededRandom(rules._randomSeed);
   const eligibilityCache = new PlayerEligibilityCache();
-  const solver = new ConstraintSolver(availablePlayers, eligibilityCache);
+  const solver = new ConstraintSolver(availablePlayers, eligibilityCache, random);
   const rotator = new LineupRotator(rules, eligibilityCache, random);

@@ -3091,6 +3143,43 @@ export function generateOptimalLineup(
   }

   // Apply fallback strategies to ensure all constraints are met
+  // First check if we actually need fallback strategies
+  const violations = detectViolations(innings, availablePlayers, rules);
+  
+  if (!violations.hasViolations) {
+    console.log('✅ No violations detected - skipping fallback strategies');
+    return innings;
+  }
+  
+  console.log('🔍 Violations detected:', {
+    benchViolations: violations.benchViolations.length,
+    emptyPositions: violations.emptyPositions.length,
+    balanceScore: violations.balanceScore
+  });
+  
   return applyFallbackStrategies(innings, availablePlayers, rules);
 }
+
+function detectViolations(
+  innings: InningLineup[],
+  players: Player[],
+  rules: LineupRules
+): { hasViolations: boolean; benchViolations: any[]; emptyPositions: any[]; balanceScore: number } {
+  // Check for empty positions and bench violations
+  const benchViolations: any[] = [];
+  const emptyPositions: any[] = [];
+  
+  // ... violation detection logic ...
+  
+  const hasViolations = benchViolations.length > 0 || 
+                       emptyPositions.length > 0 || 
+                       balanceScore < 70;
+  
+  return {
+    hasViolations,
+    benchViolations,
+    emptyPositions,
+    balanceScore
+  };
+}
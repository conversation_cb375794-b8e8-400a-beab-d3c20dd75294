# TeamRoles to PositionPreferences Fix

## Problem
The UI stores player position assignments in `teamRoles` (e.g., `{ pitcher: 'go-to', catcher: 'capable' }`), but the lineup generation algorithm expects data in `positionPreferences` format (e.g., `{ pitcher: 'preferred', catcher: 'secondary' }`).

This causes the algorithm to think players have no position assignments even though they are visible in the UI with badges like "pitcher ★".

## Root Cause
1. The UI uses the newer `teamRoles` system for position assignments
2. The database stores `teamRoles` inside the `position_preferences` JSON column
3. When data is loaded, `teamService.ts` extracts `teamRoles` from `position_preferences` and puts it in a separate field
4. The lineup generation algorithm still checks `player.positionPreferences` instead of `player.teamRoles`

## Solution
Created a conversion utility that:
1. Converts `teamRoles` to `positionPreferences` format for backward compatibility
2. Maps role names: `go-to` → `preferred`, `capable`/`fill-in` → `secondary`, `avoid` → `avoid`
3. Applied this conversion before all lineup generation calls

## Files Changed

### Created:
- `/src/lib/teamRolesToPreferences.ts` - Conversion utility

### Modified:
- `/src/pages/BatchLineupGeneration.tsx` - Added conversion before `generateMultiGameSeries`
- `/src/pages/ViewLineup.tsx` - Added conversion before all `generateCompleteLineupAsync` calls

## Implementation Details

The conversion happens just before lineup generation:
```typescript
// Enhance players with positionPreferences from teamRoles for backward compatibility
const enhancedPlayers = enhancePlayersWithPositionPreferences(attendingPlayers);

// Then use enhancedPlayers for generation
const seriesResult = await generateMultiGameSeries(enhancedPlayers, ...);
```

## Testing
To test the fix:
1. Run the test script: `node test-competitive-mode-fix.mjs`
2. Check that players with teamRoles now generate lineups successfully
3. Verify position assignments are respected in both competitive and recreational modes

## Future Improvements
Consider updating the lineup generation algorithm to directly use `teamRoles` instead of requiring this conversion step. This would involve updating `utils-enhanced.ts` and related files to check `player.teamRoles` instead of `player.positionPreferences`.
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkTeamDeletionError() {
  console.log('\n🔍 Checking team deletion issue...\n');

  try {
    // 1. First, let's find all users to see what's in the database
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, email, is_protected')
      .order('created_at', { ascending: false })
      .limit(20);

    if (profilesError) {
      console.log('❌ Error fetching profiles:', profilesError);
      return;
    }

    console.log('📋 Recent profiles:');
    profiles?.forEach(p => {
      console.log(`   ${p.email} - Protected: ${p.is_protected || false} - ID: ${p.id}`);
    });

    // 2. Find teams that exist
    const { data: teams, error: teamsError } = await supabase
      .from('teams')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(20);

    if (teamsError) {
      console.log('\n❌ Error fetching teams:', teamsError);
      return;
    }

    console.log('\n📋 Recent teams:');
    if (teams && teams.length > 0) {
      for (const t of teams) {
        // Get user info for each team
        const { data: profile } = await supabase
          .from('profiles')
          .select('email, is_protected')
          .eq('id', t.user_id)
          .single();
          
        console.log(`\n   Team: ${t.name}`);
        console.log(`   ID: ${t.id}`);
        console.log(`   User: ${profile?.email || 'Unknown'} (${t.user_id})`);
        console.log(`   Protected: ${profile?.is_protected || false}`);
        console.log(`   Created: ${new Date(t.created_at).toLocaleDateString()}`);
      }
    }

    // 3. Look for any team that might belong to "heatherandnoah"
    const possibleEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    console.log('\n🔍 Looking for teams with similar emails...');
    
    for (const email of possibleEmails) {
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('*')
        .eq('email', email)
        .single();
        
      if (!userError && userData) {
        console.log(`\n✅ Found user: ${email}`);
        console.log(`   ID: ${userData.id}`);
        console.log(`   Protected: ${userData.is_protected || false}`);
        
        // Get their teams
        const { data: userTeams, error: teamError } = await supabase
          .from('teams')
          .select('*')
          .eq('user_id', userData.id);
          
        if (userTeams && userTeams.length > 0) {
          console.log(`   Teams: ${userTeams.length}`);
          userTeams.forEach(t => {
            console.log(`     - ${t.name} (${t.id})`);
          });
        }
      }
    }

    // 4. Test deletion on a non-protected test team
    console.log('\n🧪 Testing team deletion mechanics...');
    
    // Find a test team (not from protected accounts)
    const { data: allTeams, error: allTeamsError } = await supabase
      .from('teams')
      .select('*')
      .limit(10);
      
    let testTeam = null;
    let testError = allTeamsError;
    
    if (allTeams && !allTeamsError) {
      for (const team of allTeams) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('email, is_protected')
          .eq('id', team.user_id)
          .single();
          
        if (profile && !profile.is_protected) {
          testTeam = { ...team, profiles: profile };
          break;
        }
      }
    }
      
    if (testTeam && !testError) {
      console.log(`\n   Found test team: ${testTeam.name} (${testTeam.id})`);
      console.log(`   Owner: ${testTeam.profiles.email}`);
      
      // Try to delete it
      console.log('   Attempting deletion...');
      const { error: deleteError } = await supabase
        .from('teams')
        .delete()
        .eq('id', testTeam.id);
        
      if (deleteError) {
        console.log('   ❌ Delete failed:', deleteError.message);
        console.log('   Error code:', deleteError.code);
        console.log('   Error details:', deleteError.details);
      } else {
        console.log('   ✅ Delete succeeded! (This was just a test)');
        
        // Check if it's really gone
        const { data: checkTeam, error: checkError } = await supabase
          .from('teams')
          .select('id, name')
          .eq('id', testTeam.id)
          .single();
          
        if (checkError?.code === 'PGRST116') {
          console.log('   ✅ Confirmed: Team is deleted from database');
        } else if (checkTeam) {
          console.log('   ⚠️  Team still exists in database!');
        }
      }
    } else {
      console.log('   ❌ No test team found');
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

checkTeamDeletionError();
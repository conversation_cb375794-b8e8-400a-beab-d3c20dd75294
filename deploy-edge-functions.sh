#!/bin/bash

# Make sure Supabase CLI is installed
if ! command -v supabase &> /dev/null
then
    echo "Supabase CLI is not installed. Please install it first."
    echo "https://supabase.com/docs/guides/cli"
    exit 1
fi

# Extract project ref from .env or .env.local
if [ -f .env ]; then
    PROJECT_REF=$(grep VITE_SUPABASE_URL .env | cut -d'=' -f2 | sed 's/https:\/\///' | cut -d'.' -f1)
elif [ -f .env.local ]; then
    PROJECT_REF=$(grep VITE_SUPABASE_URL .env.local | cut -d'=' -f2 | sed 's/https:\/\///' | cut -d'.' -f1)
else
    echo "Error: No .env or .env.local file found"
    echo "Please create one with VITE_SUPABASE_URL=https://YOUR_PROJECT_REF.supabase.co"
    exit 1
fi

if [ -z "$PROJECT_REF" ]; then
    echo "Error: Could not extract project reference from VITE_SUPABASE_URL"
    exit 1
fi

echo "Using project reference: $PROJECT_REF"

# Deploy all edge functions
echo "Deploying edge functions..."

# List of functions to deploy
functions=("admin-create-user" "create-payment" "send-contact-email" "stripe-webhook" "verify-payment")

for func in "${functions[@]}"
do
    echo "Deploying $func..."
    supabase functions deploy "$func" --project-ref "$PROJECT_REF"
    if [ $? -ne 0 ]; then
        echo "Failed to deploy $func"
        exit 1
    fi
done

echo "All edge functions deployed successfully!"

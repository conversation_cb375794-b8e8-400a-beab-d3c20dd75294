# Debug Console Messages to Check

When you run batch generation, please look for these specific messages in the browser console (F12):

## 🔍 **Critical Messages to Find:**

### 1. Cross-Game Data Initialization
Look for messages like:
```
🔄 Cross-game data for PlayerName: 5 field, 2 bench, 1 pitching innings from 1 previous games
```
- If you DON'T see these messages, cross-game data isn't being passed
- If you DO see them, check if the numbers match what you expect from previous games

### 2. Fairness Boost Messages
Look for messages like:
```
⚖️ FAIRNESS BOOST: PlayerName underplayed (45.5% vs expected 75%)
```
- These should appear for players who are behind in playing time
- If you don't see these, the fairness logic isn't running

### 3. Stats Map for Rotation
Look for:
```
📊 Stats map for rotation: [object showing player stats]
```
- Check if the stats show cross-game totals or if they're starting from 0

### 4. Error Messages
Any JavaScript errors that might be preventing the algorithm from working

## 🚨 **What This Will Tell Us:**

**If you see cross-game messages:** The data is being passed correctly
**If you see fairness boost messages:** The algorithm is trying to balance
**If stats show 0 for all players in Game 2:** The cross-game data isn't reaching the algorithm
**If you see errors:** Something is broken in the algorithm

## 📝 **Please Share:**
1. Any messages that start with `🔄 Cross-game data`
2. Any messages that start with `⚖️ FAIRNESS BOOST`
3. Any JavaScript errors (red text)
4. The `📊 Stats map for rotation` object for Game 2

This will help me identify exactly where the cross-game tracking is breaking down.
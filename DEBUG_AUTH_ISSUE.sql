-- Debug auth issues - Run these queries to understand what's happening

-- 1. Check if the current user is authenticated
SELECT auth.uid() as current_user_id, auth.role() as current_role;

-- 2. Check if user exists in auth.users
SELECT id, email, created_at, last_sign_in_at 
FROM auth.users 
WHERE email = 'YOUR_EMAIL_HERE'
LIMIT 1;

-- 3. Check if user has a profile
SELECT * FROM profiles WHERE id = auth.uid()::text;

-- 4. Check if user has a subscription
SELECT * FROM subscriptions WHERE user_id = auth.uid();

-- 5. Check existing teams for the user
SELECT id, name, created_at 
FROM teams 
WHERE user_id = auth.uid();

-- 6. Test if you can insert a team (this will fail if <PERSON><PERSON> blocks it)
-- Don't actually run this, just see the error
/*
INSERT INTO teams (name, user_id) 
VALUES ('Test Team', auth.uid())
RETURNING *;
*/

-- 7. Check all active RLS policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename IN ('profiles', 'subscriptions', 'teams', 'players', 'lineups')
ORDER BY tablename, policyname;
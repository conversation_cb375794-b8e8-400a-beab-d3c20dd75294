import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  console.error('Make sure VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function applyFixes() {
  console.log('🔧 Applying session timeout fixes...\n');

  try {
    // 1. Check for duplicate subscriptions
    console.log('1️⃣ Checking for duplicate subscriptions...');
    const { data: duplicates, error: dupError } = await supabase
      .from('subscriptions')
      .select('user_id')
      .order('user_id');
    
    if (dupError) {
      console.error('Error checking duplicates:', dupError);
    } else {
      // Count duplicates manually
      const userCounts = {};
      duplicates.forEach(row => {
        userCounts[row.user_id] = (userCounts[row.user_id] || 0) + 1;
      });
      const duplicateUsers = Object.entries(userCounts).filter(([_, count]) => count > 1);
      
      if (duplicateUsers.length > 0) {
        console.log(`Found ${duplicateUsers.length} users with duplicate subscriptions`);
        
        // Clean up duplicates
        for (const [userId, count] of duplicateUsers) {
          console.log(`Cleaning up ${count - 1} duplicate(s) for user ${userId}`);
          
          // Get all subscriptions for this user
          const { data: userSubs } = await supabase
            .from('subscriptions')
            .select('*')
            .eq('user_id', userId)
            .order('is_paid', { ascending: false })
            .order('created_at', { ascending: false });
          
          // Keep the first one (best), delete the rest
          if (userSubs && userSubs.length > 1) {
            for (let i = 1; i < userSubs.length; i++) {
              await supabase
                .from('subscriptions')
                .delete()
                .eq('id', userSubs[i].id);
            }
          }
        }
      } else {
        console.log('✅ No duplicate subscriptions found');
      }
    }

    // 2. Fix missing tier and team_limit values
    console.log('\n2️⃣ Fixing missing tier and team_limit values...');
    const { data: needsFix, error: fixError } = await supabase
      .from('subscriptions')
      .select('id, is_paid, tier, team_limit')
      .or('tier.is.null,team_limit.is.null');
    
    if (!fixError && needsFix && needsFix.length > 0) {
      console.log(`Found ${needsFix.length} subscriptions needing fixes`);
      
      for (const sub of needsFix) {
        const updates = {};
        if (!sub.tier) {
          updates.tier = sub.is_paid ? 'starter' : 'free';
        }
        if (sub.team_limit === null) {
          updates.team_limit = sub.is_paid ? 1 : 0;
        }
        
        await supabase
          .from('subscriptions')
          .update(updates)
          .eq('id', sub.id);
      }
      console.log('✅ Fixed all missing values');
    } else {
      console.log('✅ All subscriptions have proper tier and team_limit values');
    }

    // 3. Verify RLS is enabled
    console.log('\n3️⃣ Verifying RLS policies...');
    // We can't directly check RLS from here, but we can test access
    
    // 4. Test subscription access
    console.log('\n4️⃣ Testing subscription access...');
    const { count, error: countError } = await supabase
      .from('subscriptions')
      .select('*', { count: 'exact', head: true });
    
    if (countError) {
      console.error('❌ Error accessing subscriptions:', countError);
    } else {
      console.log(`✅ Successfully accessed subscriptions table (${count} total records)`);
    }

    // 5. Show summary
    console.log('\n📊 Subscription Summary:');
    const { data: summary } = await supabase
      .from('subscriptions')
      .select('is_paid, tier, team_limit');
    
    if (summary) {
      const paid = summary.filter(s => s.is_paid).length;
      const free = summary.filter(s => !s.is_paid).length;
      const tiers = {};
      summary.forEach(s => {
        tiers[s.tier || 'unknown'] = (tiers[s.tier || 'unknown'] || 0) + 1;
      });
      
      console.log(`Total subscriptions: ${summary.length}`);
      console.log(`Paid: ${paid}`);
      console.log(`Free: ${free}`);
      console.log('Tiers:', tiers);
    }

    console.log('\n✅ All fixes applied successfully!');

  } catch (error) {
    console.error('\n❌ Error applying fixes:', error);
    process.exit(1);
  }
}

// Run the fixes
applyFixes();
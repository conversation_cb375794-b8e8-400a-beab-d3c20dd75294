-- Fix Session Timeout Issues - Comprehensive Solution
-- This script addresses subscription RLS policies and ensures proper data structure

-- 1. First, check and fix any duplicate subscriptions
WITH duplicates AS (
  SELECT 
    user_id,
    id,
    is_paid,
    created_at,
    ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY is_paid DESC, created_at DESC) as rn
  FROM subscriptions
)
DELETE FROM subscriptions
WHERE id IN (
  SELECT id FROM duplicates WHERE rn > 1
);

-- 2. Ensure unique constraint on user_id
ALTER TABLE subscriptions
DROP CONSTRAINT IF EXISTS unique_user_id;

ALTER TABLE subscriptions
ADD CONSTRAINT unique_user_id UNIQUE (user_id);

-- 3. Add missing columns if they don't exist
DO $$ 
BEGIN
  -- Add tier column if missing
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'subscriptions' AND column_name = 'tier') THEN
    ALTER TABLE subscriptions ADD COLUMN tier text DEFAULT 'starter';
  END IF;
  
  -- Add team_limit column if missing
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'subscriptions' AND column_name = 'team_limit') THEN
    ALTER TABLE subscriptions ADD COLUMN team_limit integer DEFAULT 1;
  END IF;
  
  -- Add expires_at column if missing
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'subscriptions' AND column_name = 'expires_at') THEN
    ALTER TABLE subscriptions ADD COLUMN expires_at timestamp with time zone;
  END IF;
END $$;

-- 4. Fix any subscriptions with missing tier/team_limit data
UPDATE subscriptions
SET 
  tier = CASE 
    WHEN is_paid = true AND tier IS NULL THEN 'starter'
    WHEN is_paid = false THEN 'free'
    ELSE tier
  END,
  team_limit = CASE
    WHEN is_paid = true AND team_limit IS NULL THEN 1
    WHEN is_paid = false THEN 0
    ELSE team_limit
  END,
  updated_at = NOW()
WHERE tier IS NULL OR team_limit IS NULL;

-- 5. Enable RLS on subscriptions table if not already enabled
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- 6. Drop all existing policies to start fresh
DROP POLICY IF EXISTS "Users can view their own subscription" ON subscriptions;
DROP POLICY IF EXISTS "Users can update their own subscription" ON subscriptions;
DROP POLICY IF EXISTS "Users can insert their own subscription" ON subscriptions;
DROP POLICY IF EXISTS "Users can delete their own subscription" ON subscriptions;
DROP POLICY IF EXISTS "Service role has full access to subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Admins can view all subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Admins can update all subscriptions" ON subscriptions;

-- 7. Create comprehensive RLS policies
-- Users can view their own subscription
CREATE POLICY "Users can view their own subscription"
ON subscriptions FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

-- Users can update their own subscription (but not is_paid field)
CREATE POLICY "Users can update their own subscription"
ON subscriptions FOR UPDATE
TO authenticated
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- Users can insert their own subscription
CREATE POLICY "Users can insert their own subscription"
ON subscriptions FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

-- Service role bypass for edge functions
CREATE POLICY "Service role has full access to subscriptions"
ON subscriptions FOR ALL
TO service_role
USING (true)
WITH CHECK (true);

-- 8. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_is_paid ON subscriptions(is_paid);
CREATE INDEX IF NOT EXISTS idx_subscriptions_tier ON subscriptions(tier);

-- 9. Create a function to safely check payment status
CREATE OR REPLACE FUNCTION check_user_payment_status(p_user_id uuid)
RETURNS boolean AS $$
DECLARE
  v_is_paid boolean;
BEGIN
  -- Quick check with timeout protection
  SELECT is_paid INTO v_is_paid
  FROM subscriptions
  WHERE user_id = p_user_id
  LIMIT 1;
  
  RETURN COALESCE(v_is_paid, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. Grant execute permission on the function
GRANT EXECUTE ON FUNCTION check_user_payment_status TO authenticated;

-- 11. Verify the setup
SELECT 
  'Subscription Table Check' as check_type,
  COUNT(*) as total_subscriptions,
  SUM(CASE WHEN is_paid = true THEN 1 ELSE 0 END) as paid_subscriptions,
  SUM(CASE WHEN tier IS NOT NULL THEN 1 ELSE 0 END) as subscriptions_with_tier
FROM subscriptions;

-- Check RLS policies
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd
FROM pg_policies
WHERE tablename = 'subscriptions'
ORDER BY policyname;
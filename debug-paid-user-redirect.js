import { supabase } from "./src/integrations/supabase/node-client.js";

async function debugPaidUserRedirect(email) {
  console.log(`\n🔍 Debugging payment redirect issue for: ${email}\n`);

  try {
    // 1. Check profile
    console.log('1. Checking user profile...');
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('email', email)
      .single();

    if (profileError || !profile) {
      console.log('❌ User profile not found!');
      return;
    }

    console.log('✅ Profile found:');
    console.log(`   - ID: ${profile.id}`);
    console.log(`   - Email: ${profile.email}`);
    console.log(`   - Is Admin: ${profile.is_admin}`);

    // 2. Check ALL subscriptions for this user
    console.log('\n2. Checking ALL subscriptions...');
    const { data: allSubs, error: allSubsError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', profile.id);

    if (allSubsError) {
      console.log('❌ Error fetching subscriptions:', allSubsError.message);
      return;
    }

    console.log(`Found ${allSubs?.length || 0} total subscription(s)`);
    allSubs?.forEach((sub, i) => {
      console.log(`\n   Subscription ${i + 1}:`);
      console.log(`   - ID: ${sub.id}`);
      console.log(`   - Is Paid: ${sub.is_paid} ${sub.is_paid ? '✅' : '❌'}`);
      console.log(`   - Tier: ${sub.tier || '❌ MISSING'}`);
      console.log(`   - Team Limit: ${sub.team_limit || '❌ MISSING'}`);
      console.log(`   - Expires: ${sub.expires_at || 'Never'}`);
      console.log(`   - Created: ${sub.created_at}`);
    });

    // 3. Test the EXACT query used by the app
    console.log('\n3. Testing the EXACT payment check query...');
    const { data: paymentCheck, error: paymentError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', profile.id)
      .eq('is_paid', true);

    if (paymentError) {
      console.log('❌ Payment query failed:', paymentError.message);
    } else if (!paymentCheck || paymentCheck.length === 0) {
      console.log('❌ No paid subscriptions found by the app query!');
      console.log('   This is why the user is being redirected.');
    } else {
      console.log(`✅ Found ${paymentCheck.length} paid subscription(s)`);
      
      // Check if any are expired
      const activeSubscriptions = paymentCheck.filter(sub => {
        if (!sub.expires_at) return true;
        return new Date(sub.expires_at) > new Date();
      });

      if (activeSubscriptions.length === 0) {
        console.log('❌ All paid subscriptions are expired!');
      } else {
        console.log(`✅ ${activeSubscriptions.length} active paid subscription(s)`);
      }
    }

    // 4. Diagnose the issue
    console.log('\n4. DIAGNOSIS:');
    
    if (!allSubs || allSubs.length === 0) {
      console.log('❌ PROBLEM: No subscription record exists');
      console.log('💡 SOLUTION: Create a subscription record with is_paid=true');
    } else {
      const paidSubs = allSubs.filter(s => s.is_paid);
      if (paidSubs.length === 0) {
        console.log('❌ PROBLEM: Subscription exists but is_paid=false');
        console.log('💡 SOLUTION: Update subscription to set is_paid=true');
      } else {
        const validSubs = paidSubs.filter(s => s.tier && s.team_limit);
        if (validSubs.length === 0) {
          console.log('❌ PROBLEM: Paid subscription missing tier or team_limit');
          console.log('💡 SOLUTION: Update subscription with tier and team_limit');
        } else {
          console.log('✅ Subscription looks valid');
          console.log('💡 Try having the user:');
          console.log('   1. Sign out completely');
          console.log('   2. Clear browser cache/cookies');
          console.log('   3. Sign back in');
        }
      }
    }

    // 5. Check auth session
    console.log('\n5. Testing auth session...');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('⚠️  No active auth session in this script');
      console.log('   (This is normal - users should check their browser session)');
    } else {
      console.log('✅ Auth session exists');
      console.log(`   User ID: ${user.id}`);
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

const email = process.argv[2];
if (!email) {
  console.log('Usage: node debug-paid-user-redirect.js <email>');
  process.exit(1);
}

debugPaidUserRedirect(email);
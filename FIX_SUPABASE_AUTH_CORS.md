# Fix Supabase Auth CORS Error

## Problem
Getting CORS error when trying to login from `dugoutboss.com`:
```
Access to fetch at 'https://mhuuptkgohuztjrovpxz.supabase.co/auth/v1/token?grant_type=password' 
from origin 'https://dugoutboss.com' has been blocked by CORS policy
```

## Solution

### 1. Add Site URL in Supabase Dashboard

1. Go to [Supabase Dashboard](https://app.supabase.com)
2. Select your project
3. Navigate to **Authentication** → **URL Configuration**
4. Update the following fields:

   **Site URL:**
   ```
   https://dugoutboss.com
   ```

   **Redirect URLs (add all of these):**
   ```
   https://dugoutboss.com
   https://dugoutboss.com/*
   https://www.dugoutboss.com
   https://www.dugoutboss.com/*
   http://localhost:5173
   http://localhost:5173/*
   ```

### 2. Verify Email Templates (if using email auth)

1. In Supabase Dashboard, go to **Authentication** → **<PERSON>ail Templates**
2. Ensure all email templates use `{{ .SiteURL }}` for links
3. This ensures emails link back to your production domain

### 3. Update Environment Variables (if needed)

If your app uses a redirect URL after auth, ensure it's set correctly:

```javascript
// In your auth code
const { data, error } = await supabase.auth.signInWithPassword({
  email,
  password,
  options: {
    redirectTo: 'https://dugoutboss.com/dashboard' // or wherever you want
  }
});
```

### 4. Clear Browser Cache

After making these changes:
1. Clear browser cache and cookies for dugoutboss.com
2. Try logging in again

### 5. Additional Checks

If the issue persists:

1. **Check Supabase project region** - Ensure you're using the correct URL
2. **Verify API keys** - Make sure production is using the correct anon key
3. **Check for typos** in the Supabase URL in your environment variables

## Testing

After applying the fixes:

1. Test login from https://dugoutboss.com
2. Test login from https://www.dugoutboss.com (if using www)
3. Test password reset flow
4. Test any OAuth providers (if configured)

## Note on Edge Functions

The Edge Functions CORS configuration in `supabase/functions/_shared/cors.ts` is separate from Auth CORS. 
- Auth CORS is configured in the Supabase Dashboard
- Edge Functions CORS is configured in code

Both need to be properly set up for a fully functional production app.
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '.env.local') });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  console.log('Please ensure VITE_SUPABASE_URL and SUPABASE_SERVICE_KEY are set in .env.local');
  process.exit(1);
}

// Create Supabase client with service key for admin access
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyBattingOrdersFix() {
  try {
    console.log('🔧 Applying batting_orders RLS fix...');
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, 'fix-batting-orders-rls.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Execute the SQL
    const { data, error } = await supabase.rpc('exec_sql', {
      sql_query: sql
    });
    
    if (error) {
      // If exec_sql doesn't exist, try direct query
      console.log('⚠️  exec_sql function not found, trying alternative method...');
      
      // Split SQL into individual statements
      const statements = sql
        .split(';')
        .map(s => s.trim())
        .filter(s => s.length > 0 && !s.startsWith('--'));
      
      console.log(`📝 Executing ${statements.length} SQL statements...`);
      
      for (const statement of statements) {
        if (statement.toLowerCase().includes('select')) {
          // Skip SELECT statements in this context
          continue;
        }
        
        console.log(`Executing: ${statement.substring(0, 50)}...`);
        
        // For DDL statements, we need to use raw SQL through the REST API
        const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'apikey': supabaseServiceKey,
            'Authorization': `Bearer ${supabaseServiceKey}`
          },
          body: JSON.stringify({ sql_query: statement })
        });
        
        if (!response.ok) {
          const errorText = await response.text();
          console.error(`❌ Failed to execute statement: ${errorText}`);
        }
      }
    }
    
    console.log('✅ Batting orders RLS fix applied successfully!');
    
    // Test the fix by checking current policies
    console.log('\n📋 Current batting_orders policies:');
    
    const { data: policies, error: policiesError } = await supabase
      .from('pg_policies')
      .select('*')
      .eq('tablename', 'batting_orders');
    
    if (policies && policies.length > 0) {
      policies.forEach(policy => {
        console.log(`  - ${policy.policyname}: ${policy.cmd}`);
      });
    } else {
      console.log('  No policies found or unable to query pg_policies');
    }
    
    console.log('\n✅ Fix applied! The batting_orders RLS error should be resolved.');
    console.log('🔄 You may need to refresh your app for changes to take effect.');
    
  } catch (error) {
    console.error('❌ Error applying fix:', error);
    process.exit(1);
  }
}

// Run the fix
applyBattingOrdersFix();
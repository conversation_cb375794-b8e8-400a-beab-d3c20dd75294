import { createServiceClient } from "./src/integrations/supabase/node-client.js";

// Use service role key if available
const supabase = createServiceClient();

async function createMissingProfile(email, tier = 'starter') {
  console.log(`\n🔧 Creating profile and subscription for: ${email}\n`);

  try {
    // 1. Find the user in auth.users
    console.log('1. Finding user in auth system...');
    
    if (!supabaseServiceKey) {
      console.log('⚠️  No service role key available');
      console.log('   Please add SUPABASE_SERVICE_ROLE_KEY to .env.local');
      console.log('   You can find it in your Supabase dashboard under Settings > API');
      return;
    }

    const { data: authData, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.log('❌ Failed to access auth users:', authError.message);
      return;
    }

    const authUser = authData.users.find(u => u.email === email);
    
    if (!authUser) {
      console.log('❌ User not found in auth system!');
      console.log('   The user needs to sign up first.');
      return;
    }

    console.log('✅ Found auth user:');
    console.log(`   - ID: ${authUser.id}`);
    console.log(`   - Email: ${authUser.email}`);
    console.log(`   - Created: ${new Date(authUser.created_at).toLocaleDateString()}`);

    // 2. Check if profile already exists
    console.log('\n2. Checking for existing profile...');
    const { data: existingProfile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authUser.id)
      .single();

    if (existingProfile) {
      console.log('✅ Profile already exists');
    } else {
      // 3. Create profile
      console.log('\n3. Creating profile...');
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: authUser.id,
          email: authUser.email,
          full_name: authUser.user_metadata?.full_name || '',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (profileError) {
        console.log('❌ Failed to create profile:', profileError.message);
        return;
      }

      console.log('✅ Profile created successfully');
    }

    // 4. Create subscription
    console.log('\n4. Creating paid subscription...');
    
    // Determine tier details
    let teamLimit = 1;
    let amount = 2000;
    
    switch (tier) {
      case 'starter':
        teamLimit = 1;
        amount = 2000;
        break;
      case 'coach':
        teamLimit = 5;
        amount = 3000;
        break;
      case 'club':
        teamLimit = 999;
        amount = 50000;
        break;
    }

    // Check for existing subscription
    const { data: existingSub } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', authUser.id)
      .single();

    if (existingSub) {
      // Update existing
      const { error: updateError } = await supabase
        .from('subscriptions')
        .update({
          is_paid: true,
          tier: tier,
          team_limit: teamLimit,
          amount: amount,
          currency: 'usd',
          payment_date: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', existingSub.id);

      if (updateError) {
        console.log('❌ Failed to update subscription:', updateError.message);
      } else {
        console.log('✅ Subscription updated to paid status');
      }
    } else {
      // Create new
      const { error: subError } = await supabase
        .from('subscriptions')
        .insert({
          user_id: authUser.id,
          is_paid: true,
          tier: tier,
          team_limit: teamLimit,
          amount: amount,
          currency: 'usd',
          payment_date: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (subError) {
        console.log('❌ Failed to create subscription:', subError.message);
      } else {
        console.log('✅ Subscription created successfully');
      }
    }

    // 5. Verify everything
    console.log('\n5. Verifying setup...');
    const { data: finalProfile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authUser.id)
      .single();

    const { data: finalSub } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', authUser.id)
      .eq('is_paid', true)
      .single();

    if (finalProfile && finalSub) {
      console.log('\n✅ SUCCESS! User is now fully set up:');
      console.log('   - Profile exists');
      console.log(`   - Subscription: ${finalSub.tier} (${finalSub.team_limit} teams)`);
      console.log('   - Payment status: Paid');
      console.log('\n📝 Next steps:');
      console.log('   1. Have the user sign out');
      console.log('   2. Clear browser cache');
      console.log('   3. Sign back in');
      console.log('   4. They should now access the dashboard!');
    } else {
      console.log('\n❌ Something went wrong - please check the errors above');
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

const email = process.argv[2];
const tier = process.argv[3] || 'coach';

if (!email) {
  console.log('Usage: node create-missing-profile.js <email> [tier]');
  console.log('\nExample: node create-missing-profile.js <EMAIL> coach');
  process.exit(1);
}

createMissingProfile(email, tier);
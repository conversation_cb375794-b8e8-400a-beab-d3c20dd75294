#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to apply the Fix Lineup algorithm improvements
 * This will:
 * 1. Back up the original utils-enhanced.ts
 * 2. Apply the patch with fixes
 * 3. Run tests to verify improvements
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔧 Applying Fix Lineup Algorithm Improvements...\n');

// Paths
const UTILS_ENHANCED = path.join(__dirname, 'src/lib/utils-enhanced.ts');
const UTILS_BACKUP = path.join(__dirname, 'src/lib/utils-enhanced.backup.ts');
const PATCH_FILE = path.join(__dirname, 'fix-lineup-algorithm.patch');

// Step 1: Create backup
console.log('📋 Creating backup of utils-enhanced.ts...');
try {
  fs.copyFileSync(UTILS_ENHANCED, UTILS_BACKUP);
  console.log('✅ Backup created: utils-enhanced.backup.ts\n');
} catch (error) {
  console.error('❌ Failed to create backup:', error.message);
  process.exit(1);
}

// Step 2: Apply patch
console.log('🔨 Applying fixes via patch...');
try {
  execSync(`git apply ${PATCH_FILE}`, { stdio: 'inherit' });
  console.log('✅ Patch applied successfully\n');
} catch (error) {
  console.error('❌ Failed to apply patch:', error.message);
  console.log('\n🔄 You can apply fixes manually by reviewing fix-lineup-algorithm.patch');
  console.log('Or copy code from utils-enhanced-fixed.ts\n');
}

// Step 3: Run tests
console.log('🧪 Running Fix Lineup Algorithm tests...');
try {
  execSync('npm test -- fix-lineup-algorithm.test.ts', { stdio: 'inherit' });
  console.log('\n✅ All tests passed!\n');
} catch (error) {
  console.log('\n⚠️  Some tests failed - review the output above');
}

// Step 4: Summary
console.log('\n📊 Summary of Improvements Applied:');
console.log('1. ✅ Fixed off-by-one rotation logic (0-based vs 1-based)');
console.log('2. ✅ Bench streaks now count forward and reset properly');
console.log('3. ✅ Added randomization to reduce constraint solver bias');
console.log('4. ✅ Fallback strategies only run when violations exist');
console.log('5. ✅ Added comprehensive diagnostic logging');

console.log('\n📁 Files Created:');
console.log('- utils-enhanced-fixed.ts (reference implementation)');
console.log('- fix-lineup-algorithm.test.ts (comprehensive tests)');
console.log('- lineup-diagnostics.ts (diagnostic logger)');
console.log('- utils-enhanced.backup.ts (backup of original)');

console.log('\n🎯 Next Steps:');
console.log('1. Test the Fix Playing Time feature in the UI');
console.log('2. Monitor diagnostic logs for anomalies');
console.log('3. If issues persist, restore backup: cp utils-enhanced.backup.ts utils-enhanced.ts');
console.log('4. Review fix-lineup-algorithm.patch for manual application if needed');

console.log('\n✨ Fix Lineup improvements applied successfully!');
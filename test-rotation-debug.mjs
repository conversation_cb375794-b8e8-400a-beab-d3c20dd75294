import { generateCompleteLineup } from './src/lib/utils-enhanced.js';

// Simple test to debug rotation issues
const testRotation = () => {
  const players = [
    { id: '1', name: '<PERSON>', teamRoles: {} },
    { id: '2', name: '<PERSON><PERSON>', teamRoles: {} },
    { id: '3', name: '<PERSON>', teamRoles: {} },
    { id: '4', name: '<PERSON><PERSON><PERSON>', teamRoles: {} },
    { id: '5', name: '<PERSON>', teamRoles: {} },
    { id: '6', name: '<PERSON>', teamRoles: {} },
    { id: '7', name: '<PERSON>', teamRoles: {} },
    { id: '8', name: 'Player8', teamRoles: {} },
    { id: '9', name: 'Player9', teamRoles: {} },
    { id: '10', name: 'Player10', teamRoles: {} },
    { id: '11', name: 'Player11', teamRoles: {} },
    { id: '12', name: 'Player12', teamRoles: {} }
  ];

  console.log('Testing rotation with 12 players, 10 innings, rotateLineupEvery=1');
  
  const innings = generateCompleteLineup(players, 10, {
    rotateLineupEvery: 1,
    rotatePitcherEvery: 2,
    equalPlayingTime: true,
    limitBenchTime: true,
    maxConsecutiveBenchInnings: 2
  });

  // Track playing time
  const playingTime = {};
  players.forEach(p => playingTime[p.name] = 0);

  console.log('\n=== INNING BY INNING BREAKDOWN ===');
  innings.forEach((inning, idx) => {
    console.log(`\nInning ${idx + 1}:`);
    const fieldPlayers = [];
    Object.entries(inning.positions).forEach(([pos, playerName]) => {
      if (pos !== 'bench' && playerName) {
        playingTime[playerName]++;
        fieldPlayers.push(`${pos}: ${playerName}`);
      }
    });
    console.log('Field:', fieldPlayers.join(', '));
    console.log('Bench:', inning.positions.bench?.join(', ') || 'None');
    
    // Show running totals after each inning
    const totals = Object.entries(playingTime)
      .sort(([,a], [,b]) => b - a)
      .map(([name, time]) => `${name}:${time}`);
    console.log('Running totals:', totals.join(', '));
  });

  console.log('\n=== FINAL RESULTS ===');
  const sorted = Object.entries(playingTime).sort(([,a], [,b]) => b - a);
  sorted.forEach(([name, time]) => {
    console.log(`${name}: ${time} innings`);
  });

  const max = Math.max(...Object.values(playingTime));
  const min = Math.min(...Object.values(playingTime));
  console.log(`\nRange: ${max - min} innings (${max} max, ${min} min)`);
  console.log(`Ideal per player: ${(10 * 9 / 12).toFixed(1)} innings`);
};

testRotation();
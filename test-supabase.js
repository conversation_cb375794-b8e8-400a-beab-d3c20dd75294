// Simple script to test Supabase connection
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { readFileSync } from 'fs';

// Initialize dotenv
dotenv.config();

// Get directory path for the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function testSupabaseConnection() {
  console.log('Testing Supabase connection...');
  
  // Read environment variables from .env file directly if needed
  let supabaseUrl = process.env.VITE_SUPABASE_URL;
  let supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;
  
  // If env vars aren't loaded through dotenv, try to read the file directly
  if (!supabaseUrl || !supabaseAnonKey) {
    try {
      const envFile = readFileSync('.env', 'utf8');
      const envVars = envFile.split('\n').reduce((acc, line) => {
        const [key, value] = line.split('=');
        if (key && value) {
          acc[key.trim()] = value.trim();
        }
        return acc;
      }, {});
      
      supabaseUrl = envVars.VITE_SUPABASE_URL;
      supabaseAnonKey = envVars.VITE_SUPABASE_ANON_KEY;
    } catch (err) {
      console.error('Error reading .env file:', err);
    }
  }
  
  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('Missing Supabase environment variables. Check your .env file.');
    return;
  }
  
  console.log(`Supabase URL: ${supabaseUrl}`);
  console.log(`Supabase Anon Key: ***${supabaseAnonKey.slice(-6)}`);
  
  const supabase = createClient(supabaseUrl, supabaseAnonKey);
  
  try {
    // Test the connection with a simple health check
    const { data, error } = await supabase.from('teams').select('count').limit(0);

    if (error && error.code !== 'PGRST116') {
      console.error('❌ Supabase connection error:', error);
      return;
    }

    console.log('✅ Successfully connected to Supabase!');
    
    // Try to get a list of tables
    try {
      // First try to get some data from the teams table
      const { data: teamsData, error: teamsError } = await supabase
        .from('teams')
        .select('*')
        .limit(5);
        
      if (teamsError) {
        console.log('Error fetching teams:', teamsError);
      } else {
        console.log('Teams data sample:', teamsData);
      }
    } catch (tableErr) {
      console.log('Error getting data, but connection is working:', tableErr);
    }
    
  } catch (err) {
    console.error('Error checking Supabase connection:', err);
  }
}

testSupabaseConnection();
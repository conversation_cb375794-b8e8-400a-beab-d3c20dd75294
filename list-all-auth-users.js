import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing environment variables');
  console.log('You need SUPABASE_SERVICE_ROLE_KEY in your .env.local file');
  console.log('Get it from: Dashboard > Settings > API > service_role (secret)');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function listAllAuthUsers() {
  console.log('\nFetching all users from auth.users table...');
  console.log('=' + '='.repeat(60));

  try {
    // Get all auth users
    const { data: authData, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.error('Error fetching auth users:', authError);
      return;
    }

    console.log(`\nFound ${authData.users.length} users in auth system:\n`);

    // Get all profiles for comparison
    const { data: profiles, error: profileError } = await supabase
      .from('profiles')
      .select('id, email, full_name');

    const profileMap = new Map();
    if (profiles) {
      profiles.forEach(p => profileMap.set(p.id, p));
    }

    // Protected accounts
    const PROTECTED_EMAILS = ['<EMAIL>', '<EMAIL>'];
    
    // List each user
    authData.users.forEach((user, index) => {
      const profile = profileMap.get(user.id);
      const hasProfile = !!profile;
      const isProtected = PROTECTED_EMAILS.includes(user.email?.toLowerCase()) || profile?.is_protected;
      
      console.log(`${index + 1}. ${user.email} ${isProtected ? '🔒 PROTECTED' : ''}`);
      console.log(`   ID: ${user.id}`);
      console.log(`   Created: ${new Date(user.created_at).toLocaleDateString()}`);
      console.log(`   Last Sign In: ${user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleDateString() : 'Never'}`);
      console.log(`   Has Profile: ${hasProfile ? 'Yes' : 'No (ORPHANED!)'}`);
      if (profile) {
        console.log(`   Name: ${profile.full_name || 'No name'}`);
      }
      console.log('');
    });

    // Find orphaned users (in auth but not in profiles)
    const orphanedUsers = authData.users.filter(u => !profileMap.has(u.id));
    
    if (orphanedUsers.length > 0) {
      console.log('\n⚠️  ORPHANED USERS (in auth but not in profiles):');
      console.log('These users cannot sign in and are blocking email reuse!\n');
      
      orphanedUsers.forEach(user => {
        console.log(`- ${user.email} (ID: ${user.id})`);
      });

      console.log('\nTo delete these orphaned users, run:');
      console.log('node delete-orphaned-user.js <email>');
    } else {
      console.log('✓ No orphaned users found.');
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

// Add service role key instructions
console.log('\nIf you get an error, make sure you have SUPABASE_SERVICE_ROLE_KEY in .env.local');
console.log('Get it from: https://supabase.com/dashboard/project/mhuuptkgohuztjrovpxz/settings/api');
console.log('Look for "service_role" under Project API keys (click Reveal)\n');

listAllAuthUsers();
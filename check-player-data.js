import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function checkPlayerData() {
  try {
    // Replace with a real player ID from your console logs
    const { data, error } = await supabase
      .from('players')
      .select('id, name, position_preferences')
      .limit(5);

    if (error) {
      console.error('Error:', error);
      return;
    }

    console.log('Player data from database:');
    data.forEach(player => {
      console.log(`\nPlayer: ${player.name} (${player.id})`);
      console.log('position_preferences:', JSON.stringify(player.position_preferences, null, 2));
    });
  } catch (error) {
    console.error('Error:', error);
  }
}

checkPlayerData();
EOF < /dev/null
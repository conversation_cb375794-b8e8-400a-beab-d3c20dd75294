import { supabase } from "./src/integrations/supabase/node-client.js";

async function checkPlayerData() {
  try {
    // Replace with a real player ID from your console logs
    const { data, error } = await supabase
      .from('players')
      .select('id, name, position_preferences')
      .limit(5);

    if (error) {
      console.error('Error:', error);
      return;
    }

    console.log('Player data from database:');
    data.forEach(player => {
      console.log(`\nPlayer: ${player.name} (${player.id})`);
      console.log('position_preferences:', JSON.stringify(player.position_preferences, null, 2));
    });
  } catch (error) {
    console.error('Error:', error);
  }
}

checkPlayerData();
EOF < /dev/null
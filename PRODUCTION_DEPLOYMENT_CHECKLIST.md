# Production Deployment Checklist for dugoutboss.com

## ✅ Completed Steps
- [x] Switched Stripe to live mode
- [x] Updated CORS configuration for dugoutboss.com
- [x] Created .env.local with production configuration
- [x] Set Stripe secrets in Supabase Edge Functions

## 📋 Remaining Steps

### 1. Update .env.local with Live Key
Edit `.env.local` and replace the placeholder with your actual live publishable key:
```
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_YOUR_ACTUAL_KEY_HERE
```

### 2. Deploy Edge Functions
If Docker Desktop is running:
```bash
./deploy-edge-functions.sh
```

If not, you can deploy through Supabase Dashboard:
- Go to Edge Functions section
- Deploy each function manually

### 3. Update Deployment Platform (Netlify/Vercel)

#### For Netlify:
1. Go to app.netlify.com → Your Site → Site settings → Environment variables
2. Add/Update:
   - `VITE_STRIPE_PUBLISHABLE_KEY` = your pk_live_... key
   - `VITE_SUPABASE_URL` = https://mhuuptkgohuztjrovpxz.supabase.co
   - `VITE_SUPABASE_ANON_KEY` = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

#### For Vercel:
1. Go to vercel.com → Your Project → Settings → Environment Variables
2. Add the same variables as above

### 4. Deploy Frontend
```bash
npm run build
# Then deploy through your platform
```

### 5. Verify Stripe Webhook
In Stripe Dashboard → Webhooks:
- Endpoint URL should be: `https://mhuuptkgohuztjrovpxz.supabase.co/functions/v1/stripe-webhook`
- Events: `checkout.session.completed`, `customer.subscription.updated`
- Status should show as "Active"

### 6. Test with Real Payment
1. Visit https://dugoutboss.com/pricing
2. Click "Get Started" on any plan
3. Use a real credit card (start with $5/month plan for testing)
4. Verify:
   - Payment succeeds
   - Redirects to success page
   - User account is upgraded to paid status

### 7. Monitor Logs
- **Stripe Dashboard**: Check webhook logs for successful deliveries
- **Supabase Dashboard**: Check Edge Function logs for any errors
- **Browser Console**: Ensure no CORS errors

## 🚨 Important Reminders
- Never commit `.env.local` to git
- Keep your Stripe secret keys secure
- Test with small amounts first
- Have a rollback plan ready

## 📞 Support Resources
- Stripe Support: dashboard.stripe.com → Help
- Supabase Support: supabase.com → Support
- Your app support: <EMAIL>
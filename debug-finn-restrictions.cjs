const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function checkFinnRestrictions() {
  try {
    // Find Finn in the database
    const { data: players, error } = await supabase
      .from('players')
      .select('*')
      .ilike('name', '%finn%');

    if (error) {
      console.error('Error fetching players:', error);
      return;
    }

    console.log('\n=== FINN PLAYER DATA ===');
    players.forEach(player => {
      console.log('\nPlayer:', player.name);
      console.log('ID:', player.id);
      console.log('Team ID:', player.team_id);
      console.log('\nOld restriction columns:');
      console.log('  pitcher_restriction:', player.pitcher_restriction);
      console.log('  catcher_restriction:', player.catcher_restriction);
      console.log('  first_base_restriction:', player.first_base_restriction);
      console.log('  other_restriction:', player.other_restriction);
      console.log('\nNew position_preferences JSON:');
      console.log(JSON.stringify(player.position_preferences, null, 2));
    });

    // Also check what the teamService would return
    console.log('\n=== MAPPED TEAM ROLES ===');
    players.forEach(player => {
      const preferences = player.position_preferences || {};
      let teamRoles = preferences.teamRoles || {};
      
      // Map old database restriction columns to teamRoles if teamRoles is empty
      if (Object.keys(teamRoles).length === 0) {
        if (player.pitcher_restriction) {
          teamRoles.pitcher = 'avoid';
        }
        if (player.catcher_restriction) {
          teamRoles.catcher = 'avoid';
        }
        if (player.first_base_restriction) {
          teamRoles.firstBase = 'avoid';
        }
        
        if (player.other_restriction) {
          const restriction = player.other_restriction.toLowerCase();
          if (restriction.includes('3b') || restriction.includes('third')) {
            teamRoles.thirdBase = 'avoid';
          }
          // Add other mappings as needed
        }
      }
      
      console.log(`\n${player.name} mapped teamRoles:`, teamRoles);
    });

  } catch (error) {
    console.error('Error:', error);
  }
}

checkFinnRestrictions();
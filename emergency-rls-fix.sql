-- EMERGENCY RLS FIX - RUN THIS IMMEDIATELY
-- The RLS policies are completely broken and blocking all operations

-- 1. Temporarily disable <PERSON><PERSON> to fix the immediate issue
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions DISABLE ROW LEVEL SECURITY;
ALTER TABLE teams DISABLE ROW LEVEL SECURITY;

-- 2. Fix the user that's having issues
DO $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get user ID
    SELECT id INTO v_user_id
    FROM auth.users
    WHERE email = '<EMAIL>';
    
    IF v_user_id IS NOT NULL THEN
        -- Create profile
        INSERT INTO profiles (id, email, created_at, updated_at)
        VALUES (v_user_id, '<EMAIL>', NOW(), NOW())
        ON CONFLICT (id) DO UPDATE
        SET email = EXCLUDED.email, updated_at = NOW();
        
        -- Create subscription
        INSERT INTO subscriptions (
            user_id, is_paid, tier, team_limit, 
            currency, amount, payment_date, 
            created_at, updated_at
        )
        VALUES (
            v_user_id, true, 'club', 999,
            'usd', 0, NOW(),
            NOW(), NOW()
        )
        ON CONFLICT (user_id) DO UPDATE
        SET 
            is_paid = true,
            tier = 'club',
            team_limit = 999,
            updated_at = NOW();
            
        RAISE NOTICE 'User fixed successfully';
    END IF;
END $$;

-- 3. Drop ALL existing policies
DO $$
DECLARE
    pol RECORD;
BEGIN
    FOR pol IN 
        SELECT policyname, tablename 
        FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename IN ('profiles', 'subscriptions', 'teams')
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON %I', pol.policyname, pol.tablename);
    END LOOP;
END $$;

-- 4. Re-enable RLS with SIMPLE, WORKING policies
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;

-- 5. Create SIMPLE policies that actually work

-- Profiles: Users can do everything with their own profile
CREATE POLICY "Enable all for users on profiles" ON profiles
    FOR ALL USING (auth.uid() = id) WITH CHECK (auth.uid() = id);

-- Subscriptions: Users can do everything with their own subscription
CREATE POLICY "Enable all for users on subscriptions" ON subscriptions
    FOR ALL USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

-- Teams: Users can do everything with their own teams
CREATE POLICY "Enable all for users on teams" ON teams
    FOR ALL USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

-- 6. Create a WORKING trigger for new users
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
SECURITY DEFINER SET search_path = public
LANGUAGE plpgsql
AS $$
BEGIN
    INSERT INTO public.profiles (id, email, created_at, updated_at)
    VALUES (new.id, new.email, now(), now())
    ON CONFLICT (id) DO NOTHING;
    
    INSERT INTO public.subscriptions (
        user_id, is_paid, tier, team_limit, 
        created_at, updated_at
    )
    VALUES (
        new.id, false, 'starter', 1,
        now(), now()
    )
    ON CONFLICT (user_id) DO NOTHING;
    
    RETURN new;
END;
$$;

-- Recreate trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 7. Verify everything is working
SELECT 
    tablename,
    policyname,
    cmd
FROM pg_policies
WHERE schemaname = 'public'
AND tablename IN ('profiles', 'subscriptions', 'teams')
ORDER BY tablename;

-- 8. Check the user is fixed
SELECT 
    u.email,
    p.id IS NOT NULL as has_profile,
    s.id IS NOT NULL as has_subscription,
    s.is_paid,
    s.tier,
    s.team_limit
FROM auth.users u
LEFT JOIN profiles p ON p.id = u.id
LEFT JOIN subscriptions s ON s.user_id = u.id
WHERE u.email = '<EMAIL>';

-- IMPORTANT: After running this, users should be able to:
-- 1. Sign up and automatically get profile/subscription
-- 2. Admin-created users can log in
-- 3. All users can create teams
--- Fix Session Persistence Issue ---

The issue is that Supabase's auth state change listener fires with a null session during the brief moment when the page loads, before it can restore the session from storage. This causes the app to think the user is logged out.

Solution:
1. Don't immediately clear auth state when session is null
2. Wait for Supabase to attempt session restoration
3. Use a combination of checks to determine if this is a real logout vs page refresh

Key changes needed:

1. In the auth state change handler, check if this is a page refresh before clearing state
2. Add a grace period for session restoration on refresh
3. Ensure initial session is properly handled

The fix has been applied to:
- Line 208-224: Don't clear state immediately on refresh
- Need to also fix the initial session handling to prevent race conditions
#!/usr/bin/env node

/**
 * This script applies the subscription tiers migration using the Supabase service role key.
 * The service role key has full database access and can execute SQL directly.
 * 
 * SECURITY WARNING: Never commit the service role key to version control!
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

let supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl) {
  console.error('❌ Missing VITE_SUPABASE_URL in environment variables');
  process.exit(1);
}

async function promptForServiceKey() {
  if (supabaseServiceKey) {
    return supabaseServiceKey;
  }

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    console.log('\n🔐 SUPABASE_SERVICE_ROLE_KEY not found in environment variables.');
    console.log('You can find your service role key in:');
    console.log('1. Supabase Dashboard → Settings → API');
    console.log('2. Look for "service_role" under "Project API keys"');
    console.log('\n⚠️  WARNING: This key has full database access. Handle with care!');
    
    rl.question('\nPlease enter your Supabase service role key: ', (key) => {
      rl.close();
      resolve(key.trim());
    });
  });
}

async function applySubscriptionTiersMigration() {
  console.log('🚀 Applying subscription tiers migration with service role key...');
  console.log(`📍 Supabase URL: ${supabaseUrl}`);
  
  // Get the service role key
  supabaseServiceKey = await promptForServiceKey();
  
  if (!supabaseServiceKey) {
    console.error('❌ Service role key is required to run migrations');
    process.exit(1);
  }
  
  console.log(`🔑 Using Service Role Key: ...${supabaseServiceKey.slice(-10)}`);
  
  // Create Supabase client with service role key
  const supabase = createServiceClient();
  
  try {
    // Read the migration file
    const migrationPath = path.join(__dirname, 'supabase/migrations/add_subscription_tiers.sql');
    const migrationSql = fs.readFileSync(migrationPath, 'utf8');
    
    // Split the migration into individual statements
    // This is a simple split - for production use, consider a proper SQL parser
    const statements = migrationSql
      .split(/;\s*$/m)
      .filter(stmt => stmt.trim().length > 0)
      .map(stmt => stmt.trim() + ';');
    
    console.log(`\n📝 Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      const preview = statement.substring(0, 100).replace(/\n/g, ' ');
      
      console.log(`\n[${i + 1}/${statements.length}] Executing: ${preview}...`);
      
      // For service role key, we can use the direct SQL execution
      const { data, error } = await supabase.rpc('execute_sql', { 
        query: statement 
      }).catch(async (err) => {
        // If execute_sql doesn't exist, try alternative approach
        // This uses the fact that service role bypasses RLS
        if (err.message?.includes('function') && err.message?.includes('does not exist')) {
          // For DDL statements, we need to use a different approach
          // Since Supabase doesn't expose direct SQL execution via API,
          // we'll check if the changes already exist
          return { data: null, error: { message: 'Direct SQL execution not available via API' } };
        }
        return { data: null, error: err };
      });
      
      if (error) {
        if (error.message.includes('already exists')) {
          console.log('⚠️  Already exists (skipping)');
        } else if (error.message.includes('Direct SQL execution not available')) {
          console.log('⚠️  Cannot execute DDL via API - manual execution required');
          console.log('\n📋 MANUAL EXECUTION REQUIRED:');
          console.log('The Supabase client API does not support direct DDL execution.');
          console.log('Please run the migration manually in the Supabase SQL Editor.');
          console.log('\nAlternatively, you can use the Supabase CLI:');
          console.log('1. Install Supabase CLI: npm install -g supabase');
          console.log('2. Login: supabase login');
          console.log('3. Link project: supabase link --project-ref ' + supabaseUrl.match(/https:\/\/([^.]+)/)[1]);
          console.log('4. Run migration: supabase db push');
          process.exit(1);
        } else {
          console.error('❌ Error:', error.message);
          // Continue with other statements
        }
      } else {
        console.log('✅ Success');
      }
    }
    
    // Verify the migration was applied
    console.log('\n🔍 Verifying migration...');
    
    const { data: subscriptions, error: verifyError } = await supabase
      .from('subscriptions')
      .select('tier, team_limit, subscription_period')
      .limit(5);
    
    if (!verifyError) {
      console.log('✅ Migration verified successfully!');
      console.log('   Subscription tiers are now available');
      
      if (subscriptions && subscriptions.length > 0) {
        console.log('\n📊 Sample data:');
        console.table(subscriptions);
      }
    } else {
      console.log('⚠️  Could not verify migration:', verifyError.message);
    }
    
    // Test the check_team_limit function
    console.log('\n🧪 Testing check_team_limit function...');
    const testUserId = '00000000-0000-0000-0000-000000000000';
    const { data: limitCheck, error: limitError } = await supabase
      .rpc('check_team_limit', { p_user_id: testUserId });
    
    if (!limitError) {
      console.log('✅ check_team_limit function is working');
      if (limitCheck) {
        console.log('   Function returned:', limitCheck);
      }
    } else {
      console.log('⚠️  check_team_limit function test failed:', limitError.message);
    }
    
    console.log('\n🎉 Migration process completed!');
    console.log('   Note: Some DDL statements may require manual execution in the Supabase SQL Editor.');
    
  } catch (error) {
    console.error('❌ Error during migration:', error);
    process.exit(1);
  }
}

// Add note about storing service role key
console.log('📌 TIP: To avoid entering the service role key each time, add it to your .env.local file:');
console.log('   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here');
console.log('   ⚠️  Never commit this key to version control!\n');

// Run the migration
applySubscriptionTiersMigration();
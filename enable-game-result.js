#!/usr/bin/env node

/**
 * This script enables the game_result column feature after the database migration has been applied.
 * Run this after executing the SQL migration to add the game_result column.
 * 
 * Usage: node enable-game-result.js
 */

console.log('🎮 Enabling game result feature...\n');

console.log('To enable game result tracking in your browser, run this in the browser console:');
console.log('\nlocalStorage.setItem("game_result_migration_applied", "true");\n');

console.log('Or add this to your Supabase SQL Editor after applying the migration:');
console.log(`
-- Add game_result column to lineups table
ALTER TABLE lineups ADD COLUMN IF NOT EXISTS game_result TEXT CHECK (game_result IN ('win', 'loss'));

-- Add index for performance
CREATE INDEX IF NOT EXISTS idx_lineups_game_result ON lineups(game_result);
`);

console.log('\n✅ After running the migration and setting localStorage, the Win/Loss toggle will be enabled!');
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing VITE_SUPABASE_URL or VITE_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function debugWelcomeEmail() {
  console.log('🔍 Welcome Email Debugging Tool\n');
  
  // Step 1: Check if functions are deployed
  console.log('1️⃣ Checking deployed functions...');
  try {
    // Test if the functions are accessible
    const functionsUrl = `${supabaseUrl}/functions/v1`;
    console.log(`   Functions URL: ${functionsUrl}`);
    console.log(`   ✅ send-welcome-email: ${functionsUrl}/send-welcome-email`);
    console.log(`   ✅ admin-create-user: ${functionsUrl}/admin-create-user`);
  } catch (error) {
    console.error('   ❌ Error checking functions:', error.message);
  }

  // Step 2: Test authentication
  console.log('\n2️⃣ Testing authentication...');
  const testEmail = process.env.TEST_ADMIN_EMAIL || '<EMAIL>';
  const testPassword = process.env.TEST_ADMIN_PASSWORD || 'your-admin-password';
  
  if (testEmail === '<EMAIL>') {
    console.log('   ⚠️  Please set TEST_ADMIN_EMAIL and TEST_ADMIN_PASSWORD in .env.local');
    console.log('   Example:');
    console.log('   TEST_ADMIN_EMAIL=<EMAIL>');
    console.log('   TEST_ADMIN_PASSWORD=your-password');
    return;
  }

  try {
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: testEmail,
      password: testPassword
    });

    if (authError) {
      console.error('   ❌ Authentication failed:', authError.message);
      return;
    }

    console.log('   ✅ Authenticated successfully');
    console.log(`   User ID: ${authData.user.id}`);

    // Check if user is admin
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', authData.user.id)
      .single();

    if (profileError) {
      console.error('   ❌ Error checking admin status:', profileError.message);
      return;
    }

    console.log(`   Admin status: ${profile.is_admin ? '✅ Yes' : '❌ No'}`);
    
    if (!profile.is_admin) {
      console.error('   ❌ User is not an admin. Cannot proceed with tests.');
      return;
    }

    // Step 3: Test the welcome email function directly
    console.log('\n3️⃣ Testing send-welcome-email function directly...');
    
    const testData = {
      email: '<EMAIL>',
      name: 'Test User',
      temporaryPassword: 'TempPass123!'
    };

    console.log('   Sending test email to:', testData.email);
    
    try {
      const { data, error } = await supabase.functions.invoke('send-welcome-email', {
        body: testData
      });

      if (error) {
        console.error('   ❌ Function error:', error);
        console.log('\n   Possible issues:');
        console.log('   - RESEND_API_KEY not set in Edge Functions secrets');
        console.log('   - Function not deployed properly');
        console.log('   - Permission issues');
      } else {
        console.log('   ✅ Function called successfully!');
        console.log('   Response:', JSON.stringify(data, null, 2));
      }
    } catch (funcError) {
      console.error('   ❌ Exception calling function:', funcError.message);
    }

    // Step 4: Check recent admin audit logs
    console.log('\n4️⃣ Checking recent admin audit logs...');
    
    const { data: logs, error: logsError } = await supabase
      .from('admin_audit_logs')
      .select('*')
      .eq('action', 'create_user')
      .order('created_at', { ascending: false })
      .limit(5);

    if (logsError) {
      console.error('   ❌ Error fetching logs:', logsError.message);
    } else {
      console.log(`   Found ${logs.length} recent user creation logs`);
      logs.forEach((log, index) => {
        console.log(`   ${index + 1}. ${new Date(log.created_at).toLocaleString()} - ${log.details.email}`);
      });
    }

    // Step 5: Test creating a user through admin-create-user
    console.log('\n5️⃣ Testing user creation with email...');
    console.log('   ⚠️  This will create a real user. Press Ctrl+C to cancel, or wait 5 seconds to continue...');
    
    await new Promise(resolve => setTimeout(resolve, 5000));

    const timestamp = Date.now();
    const newUserData = {
      email: `test-welcome-${timestamp}@example.com`,
      password: 'TempPassword123!',
      fullName: 'Test Welcome User',
      role: 'user',
      isPaid: false
    };

    console.log('   Creating user:', newUserData.email);

    try {
      const { data: createData, error: createError } = await supabase.functions.invoke('admin-create-user', {
        body: newUserData
      });

      if (createError) {
        console.error('   ❌ User creation error:', createError);
      } else {
        console.log('   ✅ User created successfully!');
        console.log('   Response:', JSON.stringify(createData, null, 2));
        
        if (createData.success) {
          console.log('\n   Check the following:');
          console.log('   1. Supabase Dashboard → Edge Functions → send-welcome-email → Logs');
          console.log('   2. Email inbox for:', newUserData.email);
          console.log('   3. Spam folder');
          
          // Cleanup - delete the test user
          console.log('\n   Cleaning up test user...');
          try {
            const { error: deleteError } = await supabase.functions.invoke('admin-delete-user', {
              body: { userId: createData.data.user.id }
            });
            
            if (deleteError) {
              console.error('   ❌ Failed to delete test user:', deleteError.message);
            } else {
              console.log('   ✅ Test user deleted');
            }
          } catch (e) {
            console.error('   ❌ Exception deleting test user:', e.message);
          }
        }
      }
    } catch (createException) {
      console.error('   ❌ Exception creating user:', createException.message);
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  } finally {
    await supabase.auth.signOut();
  }
}

// Additional debugging info
console.log('📋 Debugging Checklist:\n');
console.log('1. ✅ Check RESEND_API_KEY is set:');
console.log('   - Go to Supabase Dashboard → Edge Functions → Secrets');
console.log('   - Look for RESEND_API_KEY');
console.log('   - Get free API key from https://resend.com\n');

console.log('2. ✅ Check Edge Function Logs:');
console.log('   - Go to Supabase Dashboard → Edge Functions');
console.log('   - Click on "send-welcome-email"');
console.log('   - Click on "Logs" tab');
console.log('   - Look for any errors\n');

console.log('3. ✅ Test with cURL:');
console.log('   First get your access token from browser DevTools:');
console.log('   - Open your app and sign in as admin');
console.log('   - Open DevTools → Application → Local Storage');
console.log('   - Find sb-<project-ref>-auth-token');
console.log('   - Copy the access_token value\n');

console.log('   Then run this command:');
console.log(`   curl -X POST '${supabaseUrl}/functions/v1/send-welcome-email' \\
     -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \\
     -H 'Content-Type: application/json' \\
     -d '{"email":"<EMAIL>","name":"Test User","temporaryPassword":"Test123!"}'`);

console.log('\n4. ✅ Common Issues:');
console.log('   - RESEND_API_KEY not set or invalid');
console.log('   - User not admin');
console.log('   - Function not deployed');
console.log('   - CORS issues (shouldn\'t happen with edge functions)');
console.log('   - Email in spam folder');
console.log('   - Resend free tier limits (100/day)');

console.log('\n' + '='.repeat(50) + '\n');

// Run the debugging
debugWelcomeEmail().then(() => {
  console.log('\n✅ Debugging complete!');
}).catch(error => {
  console.error('\n❌ Debugging failed:', error);
});
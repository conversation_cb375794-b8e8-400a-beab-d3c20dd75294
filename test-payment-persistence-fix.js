#!/usr/bin/env node

/**
 * Test script to verify payment persistence fix
 * This tests the localStorage-based payment status caching
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY
);

async function testPaymentPersistence() {
  console.log('🧪 Testing Payment Persistence Fix...\n');
  
  try {
    // Test 1: Check a known paid user
    const testEmail = process.argv[2];
    if (!testEmail) {
      console.error('❌ Please provide a test email as argument');
      console.log('Usage: node test-payment-persistence-fix.js <EMAIL>');
      process.exit(1);
    }
    
    console.log(`📧 Testing with user: ${testEmail}`);
    
    // Get user by email
    const { data: users, error: userError } = await supabase
      .from('profiles')
      .select('id')
      .eq('email', testEmail)
      .limit(1);
    
    if (userError || !users || users.length === 0) {
      console.error('❌ User not found:', userError?.message || 'No user with that email');
      process.exit(1);
    }
    
    const userId = users[0].id;
    console.log(`✅ Found user ID: ${userId}`);
    
    // Check subscription status
    const { data: subscription, error: subError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();
    
    if (subError) {
      console.error('❌ Error checking subscription:', subError);
      process.exit(1);
    }
    
    if (!subscription) {
      console.log('⚠️  No subscription found for user');
      console.log('Creating test subscription...');
      
      const { data: newSub, error: createError } = await supabase
        .from('subscriptions')
        .insert({
          user_id: userId,
          is_paid: true,
          tier: 'club',
          team_limit: 999,
          amount: 4900,
          currency: 'usd',
          payment_date: new Date().toISOString()
        })
        .select()
        .single();
      
      if (createError) {
        console.error('❌ Error creating subscription:', createError);
        process.exit(1);
      }
      
      console.log('✅ Created test subscription');
    } else {
      console.log('✅ Found subscription:', {
        is_paid: subscription.is_paid,
        tier: subscription.tier,
        team_limit: subscription.team_limit
      });
      
      if (!subscription.is_paid) {
        console.log('⚠️  User is not marked as paid, updating...');
        
        const { error: updateError } = await supabase
          .from('subscriptions')
          .update({ is_paid: true })
          .eq('user_id', userId);
        
        if (updateError) {
          console.error('❌ Error updating subscription:', updateError);
          process.exit(1);
        }
        
        console.log('✅ Updated subscription to paid');
      }
    }
    
    // Test localStorage simulation
    console.log('\n📦 Testing localStorage persistence...');
    
    const paymentStatus = {
      userId: userId,
      isPaid: true,
      paymentInfo: { test: true },
      tier: 'club',
      teamLimit: 999,
      timestamp: Date.now()
    };
    
    console.log('✅ Payment status object:', JSON.stringify(paymentStatus, null, 2));
    
    // Test cache duration
    const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours
    const oldTimestamp = Date.now() - CACHE_DURATION - 1000; // Expired
    const expiredStatus = { ...paymentStatus, timestamp: oldTimestamp };
    
    console.log('\n🕐 Testing cache expiration...');
    console.log(`Current time: ${new Date().toISOString()}`);
    console.log(`Expired time: ${new Date(oldTimestamp).toISOString()}`);
    console.log(`Cache valid: ${Date.now() - paymentStatus.timestamp < CACHE_DURATION}`);
    console.log(`Expired valid: ${Date.now() - oldTimestamp < CACHE_DURATION}`);
    
    console.log('\n✅ Payment persistence fix should now:');
    console.log('1. Store payment status in localStorage when verified');
    console.log('2. Restore payment status from localStorage on page refresh');
    console.log('3. Trust localStorage during network errors');
    console.log('4. Only clear payment cache on explicit logout');
    console.log('5. Respect 24-hour cache duration');
    
    console.log('\n📱 To test in browser:');
    console.log('1. Log in as this user');
    console.log('2. Check localStorage for "dugout_boss_payment_status"');
    console.log('3. Refresh the page - should maintain paid status');
    console.log('4. Simulate network error - should still show as paid');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
testPaymentPersistence();
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createServiceClient();

async function verifyUserIsolation(email) {
  try {
    console.log(`\n🔍 Verifying data isolation for user: ${email}\n`);

    // Get user ID
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserByEmail(email);
    if (authError || !authUser) {
      console.error('❌ User not found:', authError?.message);
      return;
    }

    const userId = authUser.user.id;
    console.log(`✅ User ID: ${userId}`);

    // Check what data this user can see
    console.log('\n📊 Checking data visibility:\n');

    // Teams
    const { data: teams, error: teamsError } = await supabase
      .from('teams')
      .select('*')
      .eq('user_id', userId);
    
    console.log(`Teams owned by user: ${teams?.length || 0}`);
    if (teams?.length > 0) {
      teams.forEach(team => {
        console.log(`  - ${team.name} (ID: ${team.id})`);
      });
    }

    // Check if user can see other teams (they shouldn't)
    const { data: allTeams } = await supabase
      .from('teams')
      .select('*');
    
    const otherTeams = allTeams?.filter(t => t.user_id !== userId) || [];
    if (otherTeams.length > 0) {
      console.error(`\n❌ PROBLEM: User can see ${otherTeams.length} teams from other users!`);
      console.log('This indicates RLS policies are not working correctly.');
    } else {
      console.log('\n✅ Good: User cannot see teams from other users');
    }

    // Check profile
    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    console.log(`\nProfile exists: ${profile ? 'Yes' : 'No'}`);
    if (profile) {
      console.log(`  - Name: ${profile.full_name || 'Not set'}`);
      console.log(`  - Email: ${profile.email}`);
    }

    // Check subscription
    const { data: subscription } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    console.log(`\nSubscription exists: ${subscription ? 'Yes' : 'No'}`);
    if (subscription) {
      console.log(`  - Status: ${subscription.status}`);
      console.log(`  - Tier: ${subscription.tier}`);
      console.log(`  - Paid: ${subscription.paid}`);
    }

    // Check if user is admin
    const { data: adminRecord } = await supabase
      .from('admin_users')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    console.log(`\nIs admin: ${adminRecord?.is_active ? 'Yes' : 'No'}`);

    // Summary
    console.log('\n📋 Summary:');
    if (otherTeams.length === 0) {
      console.log('✅ User data is properly isolated');
      console.log('✅ User can only see their own data');
    } else {
      console.log('❌ User data is NOT properly isolated');
      console.log('❌ User can see data from other users');
      console.log('\n🔧 To fix this, run the following SQL in Supabase:');
      console.log('   psql fix-rls-isolation.sql');
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

// Get email from command line
const email = process.argv[2];
if (!email) {
  console.log('Usage: node verify-user-isolation.js <email>');
  process.exit(1);
}

verifyUserIsolation(email);
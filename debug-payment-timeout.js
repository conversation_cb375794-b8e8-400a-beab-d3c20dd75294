#!/usr/bin/env node

/**
 * Debug script to test payment status check timeouts
 * 
 * This script tests the subscription check query directly
 * to help diagnose timeout issues during sign-in
 */

import { createServiceClient } from "./src/integrations/supabase/node-client.js";

// Load environment variables

const supabase = createServiceClient();

async function testPaymentCheck(userEmail) {
  console.log(`\n=== Testing payment check for: ${userEmail} ===\n`);
  
  try {
    // Get user by email
    console.log('1. Finding user...');
    const startTime = Date.now();
    
    const { data: users, error: userError } = await supabase
      .from('profiles')
      .select('id, email')
      .eq('email', userEmail)
      .limit(1);
    
    const userLookupTime = Date.now() - startTime;
    console.log(`   User lookup completed in ${userLookupTime}ms`);
    
    if (userError || !users || users.length === 0) {
      console.error('   Error finding user:', userError || 'User not found');
      return;
    }
    
    const userId = users[0].id;
    console.log(`   Found user ID: ${userId}`);
    
    // Test subscription query
    console.log('\n2. Checking subscriptions...');
    const subStartTime = Date.now();
    
    const { data: subscriptions, error: subError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('is_paid', true)
      .order('created_at', { ascending: false });
    
    const subQueryTime = Date.now() - subStartTime;
    console.log(`   Subscription query completed in ${subQueryTime}ms`);
    
    if (subError) {
      console.error('   Subscription query error:', subError);
    } else {
      console.log(`   Found ${subscriptions?.length || 0} paid subscriptions`);
      if (subscriptions && subscriptions.length > 0) {
        console.log('   Latest subscription:', {
          id: subscriptions[0].id,
          tier: subscriptions[0].tier,
          created_at: subscriptions[0].created_at,
          expires_at: subscriptions[0].expires_at
        });
      }
    }
    
    // Test edge function
    console.log('\n3. Testing edge function...');
    const edgeStartTime = Date.now();
    
    try {
      const { data: edgeResult, error: edgeError } = await supabase.functions.invoke('verify-payment', {
        body: { user_id: userId, email: userEmail }
      });
      
      const edgeTime = Date.now() - edgeStartTime;
      console.log(`   Edge function completed in ${edgeTime}ms`);
      
      if (edgeError) {
        console.error('   Edge function error:', edgeError);
      } else {
        console.log('   Edge function result:', edgeResult);
      }
    } catch (edgeErr) {
      const edgeTime = Date.now() - edgeStartTime;
      console.error(`   Edge function failed after ${edgeTime}ms:`, edgeErr.message);
    }
    
    // Summary
    const totalTime = Date.now() - startTime;
    console.log(`\n=== Total time: ${totalTime}ms ===`);
    console.log('Breakdown:');
    console.log(`- User lookup: ${userLookupTime}ms`);
    console.log(`- Subscription query: ${subQueryTime}ms`);
    console.log(`- Edge function: ${Date.now() - edgeStartTime}ms`);
    
  } catch (error) {
    console.error('\nUnexpected error:', error);
  }
}

// Test specific user or default test user
const testEmail = process.argv[2] || '<EMAIL>';

console.log('Payment Status Check Timeout Debugger');
console.log('====================================');
console.log(`Supabase URL: ${supabaseUrl}`);
console.log(`Testing user: ${testEmail}`);

testPaymentCheck(testEmail).then(() => {
  console.log('\nDebug complete.');
  process.exit(0);
}).catch(err => {
  console.error('\nDebug failed:', err);
  process.exit(1);
});
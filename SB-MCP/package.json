{"name": "supabase-mcp", "version": "1.5.0", "description": "MCP server for Supabase CRUD operations", "type": "module", "exports": {"./*": {"import": "./dist/esm/*", "require": "./dist/cjs/*"}}, "typesVersions": {"*": {"*": ["./dist/esm/*"]}}, "bin": {"supabase-mcp": "dist/esm/index.js", "supabase-mcp-claude": "dist/esm/claude-entry.js"}, "scripts": {"start": "node dist/esm/index.js", "start:claude": "node dist/esm/claude-entry.js", "dev": "tsx watch --clear-screen=false src/index.ts", "dev:claude": "tsx watch --clear-screen=false src/claude-entry.ts", "build": "npm run build:esm && npm run build:cjs", "build:esm": "tsc -p tsconfig.prod.json && echo '{\"type\": \"module\"}' > dist/esm/package.json", "build:cjs": "tsc -p tsconfig.cjs.json && echo '{\"type\": \"commonjs\"}' > dist/cjs/package.json", "prepack": "npm run build", "lint": "eslint . --ext .ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["supabase", "mcp", "model-context-protocol", "claude", "anthropic"], "author": "", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.7.0", "@supabase/supabase-js": "^2.39.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "zod": "^3.22.4"}, "devDependencies": {"@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.10.4", "eslint": "^8.56.0", "nodemon": "^3.0.2", "tsx": "^4.16.5", "ts-node": "^10.9.1", "typescript": "^5.3.3"}, "files": ["dist/", "README.md", "LICENSE"], "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/Cappahccino/SB-MCP"}, "bugs": {"url": "https://github.com/Cappahccino/SB-MCP/issues"}, "homepage": "https://github.com/Cappahccino/SB-MCP#readme"}
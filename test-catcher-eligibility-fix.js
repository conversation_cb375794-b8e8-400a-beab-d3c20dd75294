// Test script to verify catcher eligibility fix
import { canPlayPosition } from './src/lib/utils-enhanced.js';

// Mock player with only teamRoles (no positionPreferences)
const playerWithTeamRoles = {
  id: '1',
  name: '<PERSON>',
  positionPreferences: {}, // Empty preferences
  teamRoles: {
    pitcher: false,
    catcher: 'go-to', // Should be able to play catcher
    firstBase: 'capable',
    secondBase: false,
    thirdBase: false,
    shortstop: false,
    leftField: false,
    centerField: false,
    rightField: false
  }
};

// Mock player with only positionPreferences (no teamRoles)
const playerWithPreferences = {
  id: '2',
  name: '<PERSON>',
  positionPreferences: {
    catcher: { level: 'primary' } // Should be able to play catcher
  },
  teamRoles: {} // Empty roles
};

// Mock player with both
const playerWithBoth = {
  id: '3',
  name: '<PERSON>',
  positionPreferences: {
    firstBase: { level: 'primary' }
  },
  teamRoles: {
    catcher: 'capable' // Should also be able to play catcher
  }
};

// Mock player with avoid in teamRoles
const playerWithAvoid = {
  id: '4',
  name: '<PERSON>',
  positionPreferences: {},
  teamRoles: {
    catcher: 'avoid' // Should NOT be able to play catcher
  }
};

console.log('🧪 Testing catcher eligibility fix...\n');

// Test 1: Player with only teamRoles in competitive mode
console.log('Test 1: Player with only teamRoles (competitive mode)');
console.log(`Player: ${playerWithTeamRoles.name}, Position: catcher, TeamRole: ${playerWithTeamRoles.teamRoles.catcher}`);
const result1 = canPlayPosition(playerWithTeamRoles, 'catcher', true, true);
console.log(`Result: ${result1} (Expected: true)`);
console.log(result1 ? '✅ PASS\n' : '❌ FAIL\n');

// Test 2: Player with only positionPreferences in competitive mode
console.log('Test 2: Player with only positionPreferences (competitive mode)');
console.log(`Player: ${playerWithPreferences.name}, Position: catcher, Preference: primary`);
const result2 = canPlayPosition(playerWithPreferences, 'catcher', true, true);
console.log(`Result: ${result2} (Expected: true)`);
console.log(result2 ? '✅ PASS\n' : '❌ FAIL\n');

// Test 3: Player with both systems in competitive mode
console.log('Test 3: Player with both systems (competitive mode)');
console.log(`Player: ${playerWithBoth.name}, Position: catcher, TeamRole: ${playerWithBoth.teamRoles.catcher}`);
const result3 = canPlayPosition(playerWithBoth, 'catcher', true, true);
console.log(`Result: ${result3} (Expected: true)`);
console.log(result3 ? '✅ PASS\n' : '❌ FAIL\n');

// Test 4: Player with avoid in teamRoles
console.log('Test 4: Player with avoid in teamRoles (competitive mode)');
console.log(`Player: ${playerWithAvoid.name}, Position: catcher, TeamRole: ${playerWithAvoid.teamRoles.catcher}`);
const result4 = canPlayPosition(playerWithAvoid, 'catcher', true, true);
console.log(`Result: ${result4} (Expected: false)`);
console.log(!result4 ? '✅ PASS\n' : '❌ FAIL\n');

// Test 5: Same player in fair play mode (should still respect avoid)
console.log('Test 5: Player with avoid in teamRoles (fair play mode)');
console.log(`Player: ${playerWithAvoid.name}, Position: catcher, TeamRole: ${playerWithAvoid.teamRoles.catcher}`);
const result5 = canPlayPosition(playerWithAvoid, 'catcher', true, false);
console.log(`Result: ${result5} (Expected: false)`);
console.log(!result5 ? '✅ PASS\n' : '❌ FAIL\n');

console.log('\n🎯 Summary:');
console.log('The fix ensures that canPlayPosition checks BOTH positionPreferences AND teamRoles');
console.log('This allows the rotation algorithm to properly validate positions during competitive mode');
console.log('when players are using the new teamRoles system instead of positionPreferences.');
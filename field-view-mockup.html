<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Field View Mockup - <PERSON>'s U15 Selects</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1024px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #1a365d;
            margin: 0 0 10px 0;
            font-size: 24px;
            font-weight: 700;
        }
        
        .inning-nav {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
        }
        
        .nav-btn {
            background: #2b6cb0;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            min-width: 60px;
            touch-action: manipulation;
        }
        
        .nav-btn:hover {
            background: #2c5aa0;
        }
        
        .nav-btn:disabled {
            background: #a0aec0;
            cursor: not-allowed;
        }
        
        .inning-display {
            font-size: 28px;
            font-weight: 700;
            color: #1a365d;
            min-width: 150px;
            text-align: center;
        }
        
        .field-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
            position: relative;
            margin: 20px 0;
        }
        
        .baseball-field {
            width: 100%;
            max-width: 800px;
            height: 600px;
            margin: 0 auto;
            position: relative;
        }
        
        .field-svg {
            width: 100%;
            height: 100%;
        }
        
        .player-circle {
            fill: #48bb78;
            stroke: #2d7748;
            stroke-width: 3;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .player-circle:hover {
            fill: #38a169;
            stroke-width: 4;
        }
        
        .player-text {
            fill: white;
            font-weight: 700;
            font-size: 14px;
            text-anchor: middle;
            dominant-baseline: middle;
            pointer-events: none;
        }
        
        .position-label {
            fill: #4a5568;
            font-size: 12px;
            font-weight: 600;
            text-anchor: middle;
            dominant-baseline: middle;
        }
        
        .bench-area {
            background: #f7fafc;
            border: 2px dashed #cbd5e0;
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
            text-align: center;
        }
        
        .bench-title {
            font-size: 18px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 15px;
        }
        
        .bench-players {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
        }
        
        .bench-player {
            background: #edf2f7;
            border: 2px solid #cbd5e0;
            color: #2d3748;
            padding: 8px 16px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 14px;
        }
        
        .grass {
            fill: #48bb78;
            opacity: 0.3;
        }
        
        .dirt {
            fill: #d69e2e;
            opacity: 0.4;
        }
        
        .base {
            fill: white;
            stroke: #2d3748;
            stroke-width: 2;
        }
        
        .pitcher-mound {
            fill: #d69e2e;
            opacity: 0.6;
            stroke: #b7791f;
            stroke-width: 1;
        }
        
        @media (max-width: 768px) {
            .baseball-field {
                height: 500px;
            }
            
            .player-text {
                font-size: 12px;
            }
            
            .inning-display {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏟️ Noah's U15 Selects - Field View</h1>
            <p style="color: #4a5568; margin: 0; font-size: 16px;">Tournament Game • Spring 2025</p>
        </div>
        
        <div class="inning-nav">
            <button class="nav-btn" onclick="changeInning(-1)">◀</button>
            <div class="inning-display">Inning 3</div>
            <button class="nav-btn" onclick="changeInning(1)">▶</button>
        </div>
        
        <div class="field-container">
            <div class="baseball-field">
                <svg class="field-svg" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
                    <!-- Field Background -->
                    <!-- Outfield Grass -->
                    <path d="M 400 500 Q 150 400 50 200 Q 200 50 400 100 Q 600 50 750 200 Q 650 400 400 500 Z" class="grass" />
                    
                    <!-- Infield Dirt -->
                    <path d="M 400 420 L 200 320 Q 250 250 400 280 Q 550 250 600 320 L 400 420 Z" class="dirt" />
                    
                    <!-- Pitcher's Mound -->
                    <circle cx="400" cy="350" r="25" class="pitcher-mound" />
                    
                    <!-- Bases -->
                    <rect x="395" y="495" width="10" height="10" class="base" transform="rotate(45 400 500)" />
                    <rect x="195" y="315" width="10" height="10" class="base" transform="rotate(45 200 320)" />
                    <rect x="395" y="275" width="10" height="10" class="base" transform="rotate(45 400 280)" />
                    <rect x="595" y="315" width="10" height="10" class="base" transform="rotate(45 600 320)" />
                    
                    <!-- Field Lines -->
                    <line x1="400" y1="500" x2="200" y2="320" stroke="#ffffff" stroke-width="3" opacity="0.8" />
                    <line x1="400" y1="500" x2="600" y2="320" stroke="#ffffff" stroke-width="3" opacity="0.8" />
                    
                    <!-- Position Labels -->
                    <text x="400" y="330" class="position-label">P</text>
                    <text x="400" y="520" class="position-label">C</text>
                    <text x="160" y="340" class="position-label">1B</text>
                    <text x="320" y="240" class="position-label">2B</text>
                    <text x="280" y="280" class="position-label">SS</text>
                    <text x="640" y="340" class="position-label">3B</text>
                    <text x="120" y="180" class="position-label">LF</text>
                    <text x="400" y="120" class="position-label">CF</text>
                    <text x="680" y="180" class="position-label">RF</text>
                    
                    <!-- Players -->
                    <!-- Pitcher -->
                    <circle cx="400" cy="350" r="35" class="player-circle" />
                    <text x="400" y="350" class="player-text">Avalon</text>
                    
                    <!-- Catcher -->
                    <circle cx="400" cy="480" r="35" class="player-circle" />
                    <text x="400" y="480" class="player-text">Grace</text>
                    
                    <!-- First Base -->
                    <circle cx="160" cy="320" r="35" class="player-circle" />
                    <text x="160" y="320" class="player-text">Presley</text>
                    
                    <!-- Second Base -->
                    <circle cx="320" cy="260" r="35" class="player-circle" />
                    <text x="320" y="260" class="player-text">Avery</text>
                    
                    <!-- Shortstop -->
                    <circle cx="280" cy="300" r="35" class="player-circle" />
                    <text x="280" y="300" class="player-text">Mikayla</text>
                    
                    <!-- Third Base -->
                    <circle cx="640" cy="320" r="35" class="player-circle" />
                    <text x="640" y="320" class="player-text">Vienna</text>
                    
                    <!-- Left Field -->
                    <circle cx="120" cy="200" r="35" class="player-circle" />
                    <text x="120" y="200" class="player-text">Finn</text>
                    
                    <!-- Center Field -->
                    <circle cx="400" cy="140" r="35" class="player-circle" />
                    <text x="400" y="140" class="player-text">Kenzie</text>
                    
                    <!-- Right Field -->
                    <circle cx="680" cy="200" r="35" class="player-circle" />
                    <text x="680" y="200" class="player-text">Morgan</text>
                </svg>
            </div>
            
            <div class="bench-area">
                <div class="bench-title">🪑 Bench Players</div>
                <div class="bench-players">
                    <div class="bench-player">Bella</div>
                    <div class="bench-player">Charlotte</div>
                    <div class="bench-player">Elle</div>
                    <div class="bench-player">Evelynn</div>
                    <div class="bench-player">Kaitlyn</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let currentInning = 3;
        
        function changeInning(direction) {
            currentInning += direction;
            if (currentInning < 1) currentInning = 1;
            if (currentInning > 7) currentInning = 7;
            
            document.querySelector('.inning-display').textContent = `Inning ${currentInning}`;
            
            // In a real implementation, this would update the player positions
            // For now, just show the navigation works
            console.log(`Switched to inning ${currentInning}`);
        }
        
        // Add touch feedback for iPad
        document.querySelectorAll('.player-circle').forEach(circle => {
            circle.addEventListener('touchstart', function() {
                this.style.fill = '#38a169';
            });
            
            circle.addEventListener('touchend', function() {
                this.style.fill = '#48bb78';
            });
        });
    </script>
</body>
</html>
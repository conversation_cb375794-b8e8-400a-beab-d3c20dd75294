/**
 * Test script to demonstrate the position eligibility fix
 * 
 * This shows that players can ONLY play positions in their position_preferences
 */

import { canPlayPosition, buildPositionMatrix } from './src/lib/strict-position-eligibility.ts';

// Test case from the user's example
const testPlayers = [
  {
    id: '1',
    name: '<PERSON>',
    positionPreferences: {
      leftField: 'preferred',
      rightField: 'neutral',
      secondBase: 'preferred',
      centerField: 'preferred'
      // NOTICE: No catcher entry means she CANNOT catch
    },
    pitcher_restriction: false,
    catcher_restriction: false,  // This means she CAN learn to catch, but hasn't yet
    first_base_restriction: false,
    isStarPlayer: true
  },
  {
    id: '2',
    name: '<PERSON>',
    positionPreferences: {
      catcher: 'preferred',
      firstBase: 'secondary'
    }
  }
];

console.log('========================================');
console.log('POSITION ELIGIBILITY FIX DEMONSTRATION');
console.log('========================================\n');

// Test Avery's eligibility
console.log('Testing Avery:');
console.log('- Can play catcher?', canPlayPosition(testPlayers[0], 'catcher'));
console.log('- Can play left field?', canPlayPosition(testPlayers[0], 'leftField'));
console.log('- Can play second base?', canPlayPosition(testPlayers[0], 'secondBase'));

console.log('\nTesting Avalon:');
console.log('- Can play catcher?', canPlayPosition(testPlayers[1], 'catcher'));
console.log('- Can play left field?', canPlayPosition(testPlayers[1], 'leftField'));

// Build position matrix
console.log('\n========================================');
console.log('POSITION MATRIX');
console.log('========================================');
const matrix = buildPositionMatrix(testPlayers);

console.log('\nWho can play each position:');
Object.entries(matrix).forEach(([position, players]) => {
  console.log(`${position}: ${players.map(p => p.player.name).join(', ') || 'NO ONE'}`);
});

console.log('\n========================================');
console.log('CRITICAL RULE SUMMARY');
console.log('========================================');
console.log('1. If a position is NOT in position_preferences, the player CANNOT play there');
console.log('2. If position_preferences[position] = "avoid", only use in absolute emergency');
console.log('3. Restrictions (pitcher_restriction, etc) are medical/safety - NEVER violate');
console.log('4. Competitive mode does NOT override position eligibility');

console.log('\n✅ ALGORITHM FIXED: Players can now ONLY play positions in their position_preferences!');
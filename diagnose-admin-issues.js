import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function diagnoseIssues() {
  console.log('🔍 Detailed Database Diagnosis\n');
  console.log('Supabase URL:', supabaseUrl);
  console.log('\n' + '='.repeat(60) + '\n');

  // Test 1: Check profiles table structure
  console.log('1. Testing profiles table...');
  try {
    // Try basic select
    const { data: basicSelect, error: basicError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);
    
    if (basicError) {
      console.log('❌ Basic select failed:', basicError.message);
      console.log('   Error code:', basicError.code);
      console.log('   Error details:', JSON.stringify(basicError, null, 2));
    } else {
      console.log('✅ Basic select works');
      console.log('   Columns:', basicSelect.length > 0 ? Object.keys(basicSelect[0]) : 'No data');
    }

    // Try selecting specific columns
    const { data: columnSelect, error: columnError } = await supabase
      .from('profiles')
      .select('id, email, is_admin')
      .limit(1);
    
    if (columnError) {
      console.log('❌ Column select failed:', columnError.message);
      
      // Try without email
      const { data: noEmail, error: noEmailError } = await supabase
        .from('profiles')
        .select('id, full_name')
        .limit(1);
      
      if (!noEmailError) {
        console.log('⚠️  Can select without email column - email column likely missing');
      }
    } else {
      console.log('✅ Can select id, email, is_admin columns');
    }
  } catch (err) {
    console.log('❌ Profiles table test failed:', err.message);
  }

  console.log('\n' + '-'.repeat(60) + '\n');

  // Test 2: Check subscriptions table
  console.log('2. Testing subscriptions table...');
  try {
    const { data, error } = await supabase
      .from('subscriptions')
      .select('*')
      .limit(1);
    
    if (error) {
      console.log('❌ Subscriptions select failed:', error.message);
      console.log('   Error code:', error.code);
    } else {
      console.log('✅ Subscriptions table accessible');
      console.log('   Columns:', data.length > 0 ? Object.keys(data[0]) : 'No data');
    }

    // Test join query
    const { data: joinData, error: joinError } = await supabase
      .from('subscriptions')
      .select(`
        id,
        user_id,
        profiles!inner(email)
      `)
      .limit(1);
    
    if (joinError) {
      console.log('❌ Join with profiles failed:', joinError.message);
    } else {
      console.log('✅ Can join with profiles table');
    }
  } catch (err) {
    console.log('❌ Subscriptions table test failed:', err.message);
  }

  console.log('\n' + '-'.repeat(60) + '\n');

  // Test 3: Check current user and permissions
  console.log('3. Checking authentication and permissions...');
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.log('❌ Not authenticated:', error.message);
    } else if (user) {
      console.log('✅ Authenticated as:', user.email);
      console.log('   User ID:', user.id);
      
      // Check if user has admin profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();
      
      if (profileError) {
        console.log('❌ Cannot fetch own profile:', profileError.message);
      } else {
        console.log('✅ Profile data:', JSON.stringify(profile, null, 2));
      }
    } else {
      console.log('⚠️  No user session');
    }
  } catch (err) {
    console.log('❌ Auth check failed:', err.message);
  }

  console.log('\n' + '-'.repeat(60) + '\n');

  // Test 4: Test the exact queries from admin pages
  console.log('4. Testing exact admin page queries...');
  
  // Test AdminDashboard query
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('id, full_name, email, created_at')
      .order('created_at', { ascending: false })
      .limit(5);
    
    if (error) {
      console.log('❌ AdminDashboard profiles query failed:', error.message);
      console.log('   Full error:', JSON.stringify(error, null, 2));
    } else {
      console.log('✅ AdminDashboard profiles query works');
    }
  } catch (err) {
    console.log('❌ AdminDashboard test failed:', err.message);
  }

  // Test AdminUsers query
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('id, full_name, email, role, created_at, is_admin');
    
    if (error) {
      console.log('❌ AdminUsers query failed:', error.message);
      console.log('   This might mean missing columns: email, role, or is_admin');
    } else {
      console.log('✅ AdminUsers query works');
    }
  } catch (err) {
    console.log('❌ AdminUsers test failed:', err.message);
  }

  console.log('\n' + '='.repeat(60) + '\n');
  console.log('📋 RECOMMENDED ACTIONS:\n');
  console.log('Based on the errors above, run this SQL in your Supabase SQL Editor:\n');
  
  // Generate SQL based on errors
  console.log(`
-- First, check what columns exist in profiles
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'profiles' 
ORDER BY ordinal_position;

-- If email column is missing:
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS email TEXT;

-- If is_admin column is missing:
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS is_admin BOOLEAN DEFAULT FALSE;

-- Update email from auth.users
UPDATE public.profiles p
SET email = u.email
FROM auth.users u
WHERE p.id = u.id AND p.email IS NULL;

-- Set your user as admin (replace YOUR_EMAIL with your actual email)
UPDATE public.profiles
SET is_admin = TRUE
WHERE email = 'YOUR_EMAIL';

-- Grant admin policies
CREATE POLICY IF NOT EXISTS "Admins can view all profiles"
  ON public.profiles FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = auth.uid() AND is_admin = TRUE
    )
  );
  `);
}

diagnoseIssues();
<!DOCTYPE html>
<html>
<head>
    <title>Auth State Checker</title>
</head>
<body>
    <h1>Supabase Auth State Checker</h1>
    <div id="output" style="white-space: pre-wrap; font-family: monospace;"></div>
    
    <script type="module">
        const output = document.getElementById('output');
        
        function log(msg) {
            output.textContent += msg + '\n';
            console.log(msg);
        }
        
        // Check localStorage
        log('=== localStorage Auth Keys ===');
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key.includes('supabase') || key.includes('auth')) {
                const value = localStorage.getItem(key);
                log(`${key}: ${value ? value.substring(0, 50) + '...' : 'null'}`);
            }
        }
        
        log('\n=== sessionStorage Auth Keys ===');
        for (let i = 0; i < sessionStorage.length; i++) {
            const key = sessionStorage.key(i);
            if (key.includes('auth') || key.includes('session')) {
                const value = sessionStorage.getItem(key);
                log(`${key}: ${value ? value.substring(0, 50) + '...' : 'null'}`);
            }
        }
        
        // Try to parse Supabase auth token
        const authKey = Object.keys(localStorage).find(k => k.includes('supabase.auth.token'));
        if (authKey) {
            try {
                const authData = JSON.parse(localStorage.getItem(authKey));
                log('\n=== Supabase Auth Token ===');
                log('Current user: ' + (authData?.currentSession?.user?.email || 'none'));
                log('Has access token: ' + !!authData?.currentSession?.access_token);
                log('Has refresh token: ' + !!authData?.currentSession?.refresh_token);
                
                if (authData?.currentSession?.expires_at) {
                    const expiresAt = new Date(authData.currentSession.expires_at * 1000);
                    const now = new Date();
                    log('Expires at: ' + expiresAt.toISOString());
                    log('Current time: ' + now.toISOString());
                    log('Expired: ' + (expiresAt < now));
                }
            } catch (e) {
                log('Error parsing auth token: ' + e.message);
            }
        }
        
        log('\n=== Recommendations ===');
        log('1. If session is expired, user needs to log in again');
        log('2. If no auth token found, user needs to log in');
        log('3. Clear all storage and log in fresh: localStorage.clear(); sessionStorage.clear();');
    </script>
</body>
</html>
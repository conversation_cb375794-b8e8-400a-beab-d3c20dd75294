// Test script to introspect the current rotation logic
// Using a realistic 9-player scenario:
// players = ['<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>']
// restrictions = { Alice: ['Catcher'], <PERSON>: [], <PERSON>: [], <PERSON>: [], etc. }
// prefs = { rotateEvery: 1, benchLimit: 1, allowPitcherRotation: true }
// innings = 3

// Mock the Player type structure based on the utils.ts file
const players = [
  {
    id: '1',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: false,
      catcher: true,  // <PERSON> is restricted from Catcher
      firstBase: false,
      other: null
    }
  },
  {
    id: '2',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    }
  },
  {
    id: '3',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    }
  },
  {
    id: '4',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    }
  },
  {
    id: '5',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    }
  },
  {
    id: '6',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    }
  },
  {
    id: '7',
    name: 'Grace',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    }
  },
  {
    id: '8',
    name: 'Henry',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    }
  },
  {
    id: '9',
    name: 'Ivy',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    }
  }
];

// Mock the rotation rules based on the prefs
const rules = {
  limitBenchTime: true,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  respectPositionLockouts: true,
  equalPlayingTime: true,
  rotateLineupEvery: 1,  // rotateEvery from prefs
  rotatePitcherEvery: 2
};

// Import the functions we need to test (we'll need to adapt this for Node.js)
// For now, let's copy the key functions we need

function canPlayPosition(player, position) {
  if (!player || !player.positionRestrictions) {
    return true;
  }

  if (position === "Pitcher" && player.positionRestrictions.pitcher) return false;
  if (position === "Catcher" && player.positionRestrictions.catcher) return false;
  if (position === "First Base" && player.positionRestrictions.firstBase) return false;

  if (player.positionRestrictions.other) {
    if (player.positionRestrictions.other === position) return false;

    if (player.positionRestrictions.other === "Middle Infield" &&
        (position === "Shortstop" || position === "Second Base")) {
      return false;
    }

    if (player.positionRestrictions.other === "3B/MI/SS/2B" &&
        (position === "Third Base" || position === "Shortstop" ||
         position === "Second Base" || position === "Middle Infield")) {
      return false;
    }
  }

  return true;
}

function createInitialInningLineup(availablePlayers) {
  if (availablePlayers.length < 9) {
    throw new Error("Not enough players to create a lineup. Need at least 9 players.");
  }

  // Since we only have 4 players, we'll need to modify this for testing
  // Let's create a simplified version that works with 4 players
  let remaining = [...availablePlayers];
  const positions = {
    leftField: "",
    centerField: "",
    rightField: "",
    thirdBase: "",
    shortstop: "",
    secondBase: "",
    firstBase: "",
    catcher: "",
    pitcher: ""
  };

  // For testing with only 4 players, let's assign them to key positions
  // and leave others empty

  // Find pitcher (someone who can pitch)
  const pitcherIdx = remaining.findIndex(p => !p.positionRestrictions.pitcher);
  if (pitcherIdx !== -1) {
    positions.pitcher = remaining[pitcherIdx].name;
    remaining.splice(pitcherIdx, 1);
  } else {
    positions.pitcher = remaining[0].name;
    remaining.splice(0, 1);
  }

  // Find catcher (someone who can catch)
  const catcherIdx = remaining.findIndex(p => !p.positionRestrictions.catcher);
  if (catcherIdx !== -1) {
    positions.catcher = remaining[catcherIdx].name;
    remaining.splice(catcherIdx, 1);
  } else {
    positions.catcher = remaining[0].name;
    remaining.splice(0, 1);
  }

  // Assign remaining players to other positions
  if (remaining.length > 0) {
    positions.firstBase = remaining[0].name;
    remaining.splice(0, 1);
  }

  if (remaining.length > 0) {
    positions.secondBase = remaining[0].name;
    remaining.splice(0, 1);
  }

  return {
    inning: 1,
    positions: {
      ...positions,
      bench: remaining.map(p => p.name)
    }
  };
}

function getPositionDisplayName(positionId) {
  switch (positionId) {
    case "pitcher": return "Pitcher";
    case "catcher": return "Catcher";
    case "firstBase": return "First Base";
    case "secondBase": return "Second Base";
    case "thirdBase": return "Third Base";
    case "shortstop": return "Shortstop";
    case "leftField": return "Left Field";
    case "centerField": return "Center Field";
    case "rightField": return "Right Field";
    default: return positionId;
  }
}

// Simplified rotation function for testing
function rotatePlayersForNextInning(previousInning, availablePlayers, rules) {
  console.log(`🚀 Starting rotation for inning ${previousInning.inning + 1}`);
  console.log(`📋 Rotation rules:`, rules);
  console.log(`👥 Available players:`, availablePlayers.map(p => p.name));
  console.log(`📍 Previous inning positions:`, previousInning.positions);

  const rotateLineupEvery = rules.rotateLineupEvery || 1;
  const currentInningNumber = previousInning.inning + 1;
  const shouldRotateThisInning = (currentInningNumber - 1) % rotateLineupEvery === 0;

  console.log(`🔄 Rotation check for inning ${currentInningNumber}`);
  console.log(`📋 Rotation frequency: every ${rotateLineupEvery} innings`);
  console.log(`✅ Will rotate: ${shouldRotateThisInning}`);

  return aiPoweredRotationAlgorithm(previousInning, availablePlayers, rules, {}, shouldRotateThisInning);
}

// Simplified AI rotation algorithm for testing
function aiPoweredRotationAlgorithm(previousInning, availablePlayers, rules, playerPositionHistory, shouldRotateThisInning = true) {
  console.log(`🚀 SUPER SMART ROTATION - Inning ${previousInning.inning + 1}`);
  console.log(`🔄 Should rotate this inning: ${shouldRotateThisInning}`);

  if (!shouldRotateThisInning) {
    console.log(`⏸️ No rotation this inning - copying previous lineup`);
    return {
      inning: previousInning.inning + 1,
      positions: { ...previousInning.positions }
    };
  }

  const allPlayerNames = availablePlayers.map(p => p.name).filter(Boolean);
  const playerMap = new Map();
  availablePlayers.forEach(player => {
    if (player?.name) playerMap.set(player.name, player);
  });

  const canPlayerPlayPosition = (playerName, positionName) => {
    const player = playerMap.get(playerName);
    if (!player || !rules.respectPositionLockouts) return true;
    return canPlayPosition(player, positionName);
  };

  const currentPositions = previousInning.positions;
  const currentBench = currentPositions.bench.filter(Boolean);

  console.log(`📍 Previous positions:`, currentPositions);
  console.log(`🪑 Current bench: [${currentBench.join(', ')}]`);

  const newPositions = {
    pitcher: '',
    catcher: '',
    firstBase: '',
    secondBase: '',
    shortstop: '',
    thirdBase: '',
    leftField: '',
    centerField: '',
    rightField: '',
    bench: []
  };

  const assignedPlayers = new Set();
  const allFieldPositionKeys = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
  const currentFieldPlayers = allFieldPositionKeys
    .map(pos => currentPositions[pos])
    .filter(Boolean);

  console.log(`⚾ Current field players: [${currentFieldPlayers.join(', ')}]`);

  // Handle Pitcher Rotation
  const currentInningNumber = previousInning.inning + 1;
  const shouldRotatePitcher = rules.allowPitcherRotation &&
    rules.rotatePitcherEvery &&
    rules.rotatePitcherEvery > 0 &&
    currentInningNumber > 1 &&
    ((currentInningNumber - 1) % rules.rotatePitcherEvery === 0);

  console.log(`⚾ PITCHER ROTATION CHECK:`);
  console.log(`   - Current inning: ${currentInningNumber}`);
  console.log(`   - Should rotate: ${shouldRotatePitcher}`);

  if (shouldRotatePitcher) {
    const benchPitchers = currentBench.filter(player => canPlayerPlayPosition(player, 'Pitcher'));
    console.log(`   - Available bench pitchers: [${benchPitchers.join(', ')}]`);

    if (benchPitchers.length > 0) {
      newPositions.pitcher = benchPitchers[0];
      assignedPlayers.add(benchPitchers[0]);
      console.log(`⚾ ✅ NEW PITCHER: ${benchPitchers[0]} (from bench)`);
    } else {
      newPositions.pitcher = currentPositions.pitcher;
      assignedPlayers.add(currentPositions.pitcher);
      console.log(`⚾ ⚠️ KEEPING PITCHER: ${currentPositions.pitcher} (no suitable bench replacement)`);
    }
  } else {
    newPositions.pitcher = currentPositions.pitcher;
    assignedPlayers.add(currentPositions.pitcher);
    console.log(`⚾ ➡️ KEEPING PITCHER: ${currentPositions.pitcher} (not rotation inning)`);
  }

  // Rotate remaining players
  const unassignedPlayers = allPlayerNames.filter(player => !assignedPlayers.has(player));
  const nonPitcherFieldPositions = ['catcher', 'firstBase', 'secondBase', 'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];

  console.log(`👥 Unassigned players: [${unassignedPlayers.join(', ')}]`);

  let playersToAssign = [...unassignedPlayers];

  // Apply rotation offset
  const rotationOffset = (currentInningNumber - 1) % playersToAssign.length;
  if (rotationOffset > 0 && playersToAssign.length > 1) {
    const rotatedPlayers = [
      ...playersToAssign.slice(rotationOffset),
      ...playersToAssign.slice(0, rotationOffset)
    ];
    playersToAssign = rotatedPlayers;
    console.log(`🔄 Applied rotation offset ${rotationOffset}: [${playersToAssign.join(', ')}]`);
  }

  // Assign players to positions
  for (let i = 0; i < Math.min(nonPitcherFieldPositions.length, playersToAssign.length); i++) {
    const positionKey = nonPitcherFieldPositions[i];
    const positionName = getPositionDisplayName(positionKey);
    const player = playersToAssign[i];

    newPositions[positionKey] = player;
    assignedPlayers.add(player);

    const wasOnBench = currentBench.includes(player);
    const previousPosition = wasOnBench ? 'BENCH' :
      Object.entries(currentPositions).find(([, v]) => v === player)?.[0] || 'UNKNOWN';

    if (canPlayerPlayPosition(player, positionName)) {
      console.log(`✅ ASSIGNED: ${player} (${previousPosition}) -> ${positionName}`);
    } else {
      console.log(`⚠️ ASSIGNED (restricted): ${player} (${previousPosition}) -> ${positionName} (has position restriction)`);
    }
  }

  // Put remaining players on bench
  const remainingPlayers = allPlayerNames.filter(player => !assignedPlayers.has(player));
  newPositions.bench = remainingPlayers;

  console.log(`🪑 NEW BENCH: [${remainingPlayers.join(', ')}]`);
  console.log(`✅ ROTATION COMPLETE!`);

  return {
    inning: previousInning.inning + 1,
    positions: newPositions
  };
}

// Run the test
console.log("=".repeat(60));
console.log("TESTING ROTATION LOGIC WITH PROVIDED INPUTS");
console.log("=".repeat(60));
console.log("Players:", players.map(p => p.name));
console.log("Restrictions: Alice cannot play Catcher");
console.log("Rules:", rules);
console.log("Testing 3 innings");
console.log("=".repeat(60));

try {
  // Create initial lineup
  console.log("\n📋 CREATING INITIAL LINEUP");
  const initialLineup = createInitialInningLineup(players);
  console.log("Initial lineup:", initialLineup);

  let currentLineup = initialLineup;
  const allLineups = [currentLineup];

  // Generate subsequent innings
  for (let i = 2; i <= 3; i++) {
    console.log(`\n📋 GENERATING INNING ${i}`);
    console.log("-".repeat(40));
    currentLineup = rotatePlayersForNextInning(currentLineup, players, rules);
    allLineups.push(currentLineup);
    console.log(`Inning ${i} lineup:`, currentLineup);
  }

  // Summary
  console.log("\n" + "=".repeat(60));
  console.log("FINAL SUMMARY");
  console.log("=".repeat(60));
  allLineups.forEach((lineup, index) => {
    console.log(`\nInning ${lineup.inning}:`);
    console.log(`  Pitcher: ${lineup.positions.pitcher}`);
    console.log(`  Catcher: ${lineup.positions.catcher}`);
    console.log(`  First Base: ${lineup.positions.firstBase}`);
    console.log(`  Second Base: ${lineup.positions.secondBase}`);
    console.log(`  Bench: [${lineup.positions.bench.join(', ')}]`);

    // Check for restriction violations
    if (lineup.positions.catcher === 'Alice') {
      console.log(`  ⚠️ RESTRICTION VIOLATION: Alice is playing Catcher but is restricted from this position!`);
    }
  });

} catch (error) {
  console.error("Error during testing:", error);
}

# Rotation Rules Update Fix

## Issue
When updating team rotation rules, users were getting:
```
Error: User ID is required to create rotation rules
```

## Root Cause
The `updateRotationRules` function in `teamService.ts` was calling `createRotationRules` without passing the user ID when no existing rules were found.

## Fix Applied

### 1. Code Fix (teamService.ts:1077-1110)
- Added user authentication check at the start of `updateRotationRules`
- Pass the user ID to `createRotationRules` when creating new rules
- Proper error handling for unauthenticated users

### 2. RLS Policies
Created `fix-rotation-rules-rls.sql` to ensure proper Row Level Security on the rotation_rules table.

## Steps to Complete Fix

1. **Apply the RLS fix** in Supabase:
   ```sql
   -- Run fix-rotation-rules-rls.sql
   ```

2. **Test the fix**:
   - Go to Rotation Rules page
   - Change any setting (e.g., rotate lineup every 2 innings)
   - Save the changes
   - Should work without errors

## What Changed

### Before:
```typescript
return await createRotationRules(teamId, rules); // Missing user ID
```

### After:
```typescript
const { data: { user } } = await supabase.auth.getUser();
if (!user) {
  throw new Error('User not authenticated');
}
return await createRotationRules(teamId, rules, user.id); // Now includes user ID
```

This ensures that when rotation rules don't exist yet, they can be created with the proper user ID.
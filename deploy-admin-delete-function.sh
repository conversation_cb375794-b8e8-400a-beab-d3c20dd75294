#!/bin/bash

# Deploy the admin-delete-user Edge Function

echo "Deploying admin-delete-user Edge Function..."

# Deploy the function
npx supabase functions deploy admin-delete-user

echo "Edge Function deployed successfully!"
echo ""
echo "To test the function:"
echo "1. Make sure you're logged in as an admin user"
echo "2. Try deleting a user from the Admin Users page"
echo "3. The user should be fully deleted including auth records"
echo ""
echo "Note: Make sure your Supabase project has the SUPABASE_SERVICE_ROLE_KEY"
echo "environment variable set in the Edge Functions dashboard."
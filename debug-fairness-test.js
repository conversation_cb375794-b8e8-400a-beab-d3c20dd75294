import { generateCompleteLineup } from './src/lib/utils-enhanced.ts';

// Test the enhanced fairness logic
const players = Array.from({ length: 12 }, (_, i) => ({
  id: `${i + 1}`,
  name: `Player${i + 1}`,
  teamRoles: {}
}));

console.log('🧪 TESTING ENHANCED FAIRNESS LOGIC');
console.log(`Players: ${players.length}, Innings: 7`);

const innings = generateCompleteLineup(players, 7, {
  rotateLineupEvery: 1,
  rotatePitcherEvery: 2,
  equalPlayingTime: true,  // ENABLE FAIRNESS MODE
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 2
});

// Calculate playing time
const playingTime = {};
players.forEach(p => playingTime[p.name] = 0);

innings.forEach(inning => {
  Object.entries(inning.positions).forEach(([pos, playerName]) => {
    if (pos !== 'bench' && playerName && typeof playerName === 'string') {
      playingTime[playerName]++;
    }
  });
});

const times = Object.values(playingTime);
const maxTime = Math.max(...times);
const minTime = Math.min(...times);
const range = maxTime - minTime;

console.log('\n📊 RESULTS:');
console.log('Playing time distribution:', playingTime);
console.log(`Range: ${minTime}-${maxTime} (${range} innings difference)`);
console.log(`Fair? ${range <= 1 ? 'YES ✅' : 'NO ❌'}`);

// Test mathematical expectation: 12 players * 7 innings = 84 field positions
// 84 / 12 = 7 innings average per player 
// But only 9 field positions per inning, so 63 total field spots
// 63 / 12 = 5.25 average per player
// So we expect: 9 players get 5 innings, 3 players get 6 innings
const expectedMin = 5;
const expectedMax = 6;
const expectedRange = 1;

console.log(`\nExpected: ${expectedMin}-${expectedMax} (range ${expectedRange})`);
console.log(`Actual: ${minTime}-${maxTime} (range ${range})`);
console.log(`Test passed: ${range <= expectedRange && minTime >= expectedMin && maxTime <= expectedMax ? 'YES ✅' : 'NO ❌'}`);
# Complete Deployment Guide for Three-Tier System

## ✅ What You've Done
- Created the three products in Stripe

## 🚀 What You Need to Do Next

### 1. **Get Your Price IDs from Stripe**
The product IDs you provided (prod_SPguf5iN5Ri7Yc, etc.) are not what we need. We need the **price IDs**:

1. Go to your Stripe Dashboard
2. Click on each product
3. Look for the **Pricing** section
4. Copy the price ID that starts with `price_` (not `prod_`)
5. You should have three price IDs like: `price_1ABC123...`

### 2. **Update the Code with Your Price IDs**

Once you have the price IDs, I'll update this file in your code:

```javascript
// In src/pages/Pricing.tsx (lines 15-19)
const PRICE_IDS = {
  starter: 'price_xxx', // Replace with your Starter price ID
  coach: 'price_yyy',   // Replace with your Coach price ID  
  club: 'price_zzz'     // Replace with your Club price ID
};
```

### 3. **Run the Database Migration**

I've created the migration file, but it needs to be applied to your Supabase database:

**Option A - Through Supabase Dashboard (Easier):**
1. Go to your Supabase project dashboard
2. Click on "SQL Editor" in the left sidebar
3. Click "New query"
4. Copy and paste the ENTIRE contents of this file:
   `/Users/<USER>/Apps/diamond-lineup-guru/supabase/migrations/add_subscription_tiers.sql`
5. Click "Run" to execute the migration
6. You should see "Success" message

**Option B - Using Supabase CLI:**
```bash
# If you have Supabase CLI installed
supabase db push
```

### 4. **Deploy the Updated Edge Functions**

The edge functions need to be redeployed with the new subscription logic:

```bash
# Make sure Docker Desktop is running, then:
./deploy-edge-functions.sh
```

If Docker isn't working, deploy through Supabase Dashboard:
1. Go to Edge Functions section
2. Deploy each function manually:
   - create-payment
   - stripe-webhook

### 5. **Update Your Stripe Webhook**

Make sure your webhook is subscribed to these additional events:
1. Go to Stripe Dashboard → Developers → Webhooks
2. Click on your webhook endpoint
3. Click "Update endpoint"
4. Make sure these events are selected:
   - `checkout.session.completed` ✓
   - `customer.subscription.created` (ADD THIS)
   - `customer.subscription.updated` (ADD THIS)
   - `customer.subscription.deleted` (ADD THIS)

### 6. **Deploy Frontend to Cloudflare**

Push the changes to trigger deployment:
```bash
git add -A
git commit -m "Implement three-tier subscription system"
git push
```

### 7. **Test Everything**

1. **Test Subscription Creation:**
   - Go to dugoutboss.com/pricing
   - Try purchasing each tier
   - Verify team limits work

2. **Test Team Limits:**
   - Create teams up to the limit
   - Try exceeding limit - should see error

3. **Check Database:**
   - Run this in Supabase SQL Editor to verify subscriptions:
   ```sql
   SELECT * FROM subscriptions 
   WHERE user_id = 'YOUR_USER_ID'
   ORDER BY created_at DESC;
   ```

## 📋 Quick Checklist

- [ ] Get price IDs from Stripe (not product IDs)
- [ ] Update Pricing.tsx with price IDs
- [ ] Run database migration in Supabase
- [ ] Deploy edge functions
- [ ] Update webhook events in Stripe
- [ ] Push code to GitHub
- [ ] Test subscription flow
- [ ] Verify team limits work

## 🆘 Troubleshooting

**"Column does not exist" errors:**
- The database migration hasn't been run yet
- Go to Supabase SQL Editor and run the migration

**"Invalid price ID" errors:**
- Make sure you're using price IDs (price_xxx) not product IDs (prod_xxx)
- Check that the price IDs are from your live mode, not test mode

**Team limits not working:**
- Check that the migration created the `check_team_limit` function
- Verify the trigger is active on the teams table

## Next Steps

Once you provide the price IDs, I'll update the code and we can complete the deployment!
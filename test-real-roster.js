// Test script for realistic baseball roster (9 positions, 12 players)
const players = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
];

const restrictions = { 
  <PERSON>: ['Catcher'], 
  <PERSON>: [], 
  <PERSON>: ['Pitcher'], 
  <PERSON>: [], 
  <PERSON>: ['First Base'],
  <PERSON>: [],
  <PERSON>: [],
  <PERSON>: [],
  <PERSON>: ['Catcher', 'Pitcher'],
  <PERSON>: [],
  <PERSON>: [],
  <PERSON>: []
};

const prefs = { 
  rotateEvery: 1, 
  benchLimit: 2, 
  allowPitcherRotation: true 
};

const innings = 6;

console.log("=".repeat(80));
console.log("REAL ROSTER TEST - 12 Players, 9 Positions, 6 Innings");
console.log("=".repeat(80));
console.log("Players:", players);
console.log("Restrictions:", restrictions);
console.log("Prefs:", prefs);
console.log("Innings:", innings);
console.log("=".repeat(80));

// Define full baseball positions
const positions = [
  'Pitcher', 'Catcher', 'First Base', 'Second Base', 'Shortstop',
  'Third Base', 'Left Field', 'Center Field', 'Right Field'
];

/**
 * Enhanced generateLineup for real baseball rosters
 */
function generateLineup(players, prefs, restrictions, innings) {
  // Initialize enhanced tracking maps
  let inningsPlayed = {};
  let benchStreak = {};
  let lastPositionTimestamp = {};
  let inningsPitched = {};
  
  // Initialize all players
  players.forEach(player => {
    inningsPlayed[player] = 0;
    benchStreak[player] = 0;
    inningsPitched[player] = 0;
    lastPositionTimestamp[player] = {};
    positions.forEach(pos => {
      lastPositionTimestamp[player][pos] = 0;
    });
  });
  
  console.log("\n📊 INITIAL STATE:");
  console.log("  Total players:", players.length);
  console.log("  Field positions:", positions.length);
  console.log("  Players per inning on bench:", players.length - positions.length);
  
  const lineup = { innings: [] };
  
  // Backtracking helper function
  function canAssignPosition(player, position, assignedThisInning, currentInning) {
    if (assignedThisInning.has(player)) return false;
    
    const playerRestrictions = restrictions[player] || [];
    if (playerRestrictions.includes(position)) return false;
    
    // Bench streak limit only applies if we have excess players
    if (players.length > positions.length && benchStreak[player] >= prefs.benchLimit) {
      return false;
    }
    
    return true;
  }
  
  // Generate each inning
  for (let inning = 1; inning <= innings; inning++) {
    console.log(`\n${"=".repeat(60)}`);
    console.log(`🏟️  GENERATING INNING ${inning}`);
    console.log(`${"=".repeat(60)}`);
    
    const inningLineup = {
      inning: inning,
      positions: {},
      bench: []
    };
    
    const assignedThisInning = new Set();
    
    // Step 1: Handle bench assignments if we have more players than positions
    if (players.length > positions.length) {
      const playersToBeach = players.length - positions.length;
      
      // Sort players by inningsPlayed (desc) then benchStreak (asc) to find who should be benched
      const benchCandidates = [...players].sort((a, b) => {
        const aPlayed = inningsPlayed[a];
        const bPlayed = inningsPlayed[b];
        if (aPlayed !== bPlayed) {
          return bPlayed - aPlayed; // Descending - most played first
        }
        // If tied on playing time, bench those with LOWER bench streak (haven't been benched recently)
        return benchStreak[a] - benchStreak[b]; // Ascending - least benched first
      });
      
      console.log(`\n🪑 BENCH ASSIGNMENT (need to bench ${playersToBeach} players):`);
      console.log("   Bench candidates sorted by inningsPlayed (desc), benchStreak (asc):");
      benchCandidates.forEach((player, index) => {
        const marker = index < playersToBeach ? "🪑" : "⚾";
        console.log(`   ${marker} ${player}: played=${inningsPlayed[player]}, benchStreak=${benchStreak[player]}`);
      });
      
      for (let i = 0; i < playersToBeach; i++) {
        const playerToBench = benchCandidates[i];
        inningLineup.bench.push(playerToBench);
        assignedThisInning.add(playerToBench);
        benchStreak[playerToBench]++;
        console.log(`   📉 BENCHED: ${playerToBench} (benchStreak now ${benchStreak[playerToBench]})`);
      }
    }
    
    // Step 2: Assign field positions
    for (let posIndex = 0; posIndex < positions.length; posIndex++) {
      const position = positions[posIndex];
      
      console.log(`\n🎯 Inning ${inning}, Position ${position}`);
      console.log("   " + "-".repeat(40));
      
      // Get eligible players for this position
      let eligiblePlayers = players.filter(player => 
        canAssignPosition(player, position, assignedThisInning, inning)
      );
      
      console.log(`   📋 eligiblePlayers: [${eligiblePlayers.join(', ')}]`);
      
      // Special handling for Pitcher position
      if (position === 'Pitcher') {
        eligiblePlayers.sort((a, b) => {
          const aPitched = inningsPitched[a];
          const bPitched = inningsPitched[b];
          if (aPitched !== bPitched) {
            return aPitched - bPitched; // Ascending - fewest innings pitched first
          }
          const aLastPitched = lastPositionTimestamp[a][position];
          const bLastPitched = lastPositionTimestamp[b][position];
          return aLastPitched - bLastPitched; // Ascending - longest since last pitch first
        });
        
        console.log(`   ⚾ Pitcher candidates:`);
        eligiblePlayers.slice(0, 3).forEach(player => {
          console.log(`      ${player}: pitched=${inningsPitched[player]}, lastPitched=inning${lastPositionTimestamp[player][position] || 'never'}`);
        });
      } else {
        // Regular position sorting
        eligiblePlayers.sort((a, b) => {
          const aPlayed = inningsPlayed[a];
          const bPlayed = inningsPlayed[b];
          if (aPlayed !== bPlayed) {
            return aPlayed - bPlayed; // Ascending - least played first
          }
          const aLastPlayed = lastPositionTimestamp[a][position];
          const bLastPlayed = lastPositionTimestamp[b][position];
          return aLastPlayed - bLastPlayed; // Ascending - least recently at this position first
        });
        
        console.log(`   📊 Top candidates:`);
        eligiblePlayers.slice(0, 3).forEach(player => {
          console.log(`      ${player}: played=${inningsPlayed[player]}, lastAt${position}=inning${lastPositionTimestamp[player][position] || 'never'}`);
        });
      }
      
      // If no eligible players, use backtracking fallback
      if (eligiblePlayers.length === 0) {
        eligiblePlayers = players.filter(player => !assignedThisInning.has(player));
        console.log(`   ⚠️  BACKTRACK: Using [${eligiblePlayers.join(', ')}]`);
      }
      
      // Assign the first (best) player
      const selectedPlayer = eligiblePlayers[0];
      console.log(`   ✅ SELECTED: ${selectedPlayer}`);
      
      // Make the assignment
      inningLineup.positions[position] = selectedPlayer;
      assignedThisInning.add(selectedPlayer);
      
      // Update tracking maps
      inningsPlayed[selectedPlayer]++;
      benchStreak[selectedPlayer] = 0; // Reset bench streak since they're playing
      lastPositionTimestamp[selectedPlayer][position] = inning;
      
      if (position === 'Pitcher') {
        inningsPitched[selectedPlayer]++;
      }
    }
    
    console.log(`\n📊 END OF INNING ${inning} STATE:`);
    console.log(`   Bench: [${inningLineup.bench.join(', ')}]`);
    console.log(`   Playing time distribution:`, 
      Object.entries(inningsPlayed)
        .sort(([,a], [,b]) => b - a)
        .map(([player, innings]) => `${player}:${innings}`)
        .join(', ')
    );
    
    lineup.innings.push(inningLineup);
  }
  
  return lineup;
}

// Run the test
const result = generateLineup(players, prefs, restrictions, innings);

console.log(`\n${"=".repeat(80)}`);
console.log("📋 FINAL LINEUP SUMMARY:");
console.log(`${"=".repeat(80)}`);

result.innings.forEach((inning) => {
  console.log(`\nInning ${inning.inning}:`);
  console.log(`  Pitcher: ${inning.positions.Pitcher}`);
  console.log(`  Catcher: ${inning.positions.Catcher}`);
  console.log(`  Infield: ${inning.positions['First Base']}, ${inning.positions['Second Base']}, ${inning.positions.Shortstop}, ${inning.positions['Third Base']}`);
  console.log(`  Outfield: ${inning.positions['Left Field']}, ${inning.positions['Center Field']}, ${inning.positions['Right Field']}`);
  if (inning.bench.length > 0) {
    console.log(`  Bench: [${inning.bench.join(', ')}]`);
  }
});

// Check for restriction violations
console.log(`\n${"=".repeat(80)}`);
console.log("🚨 RESTRICTION VIOLATIONS CHECK:");
console.log(`${"=".repeat(80)}`);
let violationFound = false;
result.innings.forEach((inning) => {
  Object.entries(inning.positions).forEach(([position, player]) => {
    const playerRestrictions = restrictions[player] || [];
    if (playerRestrictions.includes(position)) {
      console.log(`❌ VIOLATION: ${player} is playing ${position} but is restricted!`);
      violationFound = true;
    }
  });
});

if (!violationFound) {
  console.log("✅ No restriction violations found!");
}

// Final statistics
console.log(`\n${"=".repeat(80)}`);
console.log("📊 FINAL PLAYING TIME ANALYSIS:");
console.log(`${"=".repeat(80)}`);

const finalStats = {};
players.forEach(player => {
  let fieldInnings = 0;
  let benchInnings = 0;
  
  result.innings.forEach(inning => {
    if (Object.values(inning.positions).includes(player)) {
      fieldInnings++;
    } else if (inning.bench.includes(player)) {
      benchInnings++;
    }
  });
  
  finalStats[player] = { field: fieldInnings, bench: benchInnings, total: fieldInnings + benchInnings };
});

Object.entries(finalStats)
  .sort(([,a], [,b]) => b.field - a.field)
  .forEach(([player, stats]) => {
    console.log(`${player}: ${stats.field} field, ${stats.bench} bench (${stats.total} total)`);
  });

/**
 * Debug the exact catcher calculation logic that determines limited catchers
 */

console.log('🔍 CATCHER CALCULATION DEBUG');

// Simulate the exact scenario from user's demo
const catcherCount = 3; // Vienna, <PERSON><PERSON><PERSON>, Grace
const numberOfGames = 3;
const inningsPerGame = 6;
const rotatePitcherEvery = 2; // Default value

console.log('\n📊 INPUT PARAMETERS:');
console.log(`Catcher count: ${catcherCount}`);
console.log(`Number of games: ${numberOfGames}`);
console.log(`Innings per game: ${inningsPerGame}`);
console.log(`Rotate pitcher every: ${rotatePitcherEvery} innings`);

// Calculation from multi-game-orchestrator.ts line 246-247
const totalCatcherSlots = numberOfGames * Math.ceil(inningsPerGame / rotatePitcherEvery);
const shouldBeLenientMultiGame = catcherCount < totalCatcherSlots / 2;

console.log('\n🎮 MULTI-GAME CALCULATION:');
console.log(`Total catcher slots: ${numberOfGames} games * ceil(${inningsPerGame}/${rotatePitcherEvery}) = ${totalCatcherSlots}`);
console.log(`Threshold for limited: ${totalCatcherSlots}/2 = ${totalCatcherSlots/2}`);
console.log(`Should be lenient (multi-game): ${catcherCount} < ${totalCatcherSlots/2} = ${shouldBeLenientMultiGame}`);

// Calculation from single-game-lineup-strict.ts line 43-44
const estimatedCatcherSlots = Math.ceil(inningsPerGame / rotatePitcherEvery);
const shouldBeLenientSingleGame = catcherCount < estimatedCatcherSlots;

console.log('\n🏟️ SINGLE-GAME CALCULATION:');
console.log(`Estimated catcher slots: ceil(${inningsPerGame}/${rotatePitcherEvery}) = ${estimatedCatcherSlots}`);
console.log(`Should be lenient (single-game): ${catcherCount} < ${estimatedCatcherSlots} = ${shouldBeLenientSingleGame}`);

console.log('\n🎯 ANALYSIS:');
if (shouldBeLenientMultiGame && !shouldBeLenientSingleGame) {
  console.log('❌ MISMATCH: Multi-game logic says "limited catchers" but single-game logic says "sufficient catchers"');
  console.log('This could cause inconsistent behavior between single and multi-game generation');
} else if (!shouldBeLenientMultiGame && shouldBeLenientSingleGame) {
  console.log('❌ MISMATCH: Single-game logic says "limited catchers" but multi-game logic says "sufficient catchers"');
} else if (shouldBeLenientMultiGame && shouldBeLenientSingleGame) {
  console.log('⚠️ BOTH: Both logics agree that catchers are limited');
  console.log('This means catcher rotation will be reduced, potentially keeping same catcher for multiple innings');
} else {
  console.log('✅ BOTH: Both logics agree that catchers are sufficient for normal rotation');
}

// Simulate the effect on Vienna specifically
console.log('\n🎯 VIENNA IMPACT ANALYSIS:');

if (shouldBeLenientSingleGame) {
  console.log('With "limited catchers" logic enabled:');
  console.log('1. Catcher rotation will be suppressed when all catchers have played');
  console.log('2. This means once Mikayla, Grace, and Vienna have each caught once,');
  console.log('   the system will prefer to keep the current catcher rather than rotate');
  console.log('3. If Mikayla (go-to catcher) catches first, she may stay there');
  console.log('4. Vienna may only get a chance early in the game before rotation is suppressed');
} else {
  console.log('With normal catcher rotation:');
  console.log('1. Catchers should rotate every 2 innings');
  console.log('2. Vienna should get opportunities to catch');
  console.log('3. Priority should be: Mikayla (go-to) > Vienna (capable) > Grace (capable)');
}

// Simulate the rotation override logic
console.log('\n🔄 ROTATION OVERRIDE SIMULATION:');
console.log('Assume innings 1-2: Mikayla catches');
console.log('Assume innings 3-4: Grace catches');
console.log('Inning 5 rotation check:');

const catchersUsedSoFar = 2; // Mikayla and Grace
const shouldRotateCatcherInning5 = true; // Normal rotation schedule

if (shouldBeLenientSingleGame && shouldRotateCatcherInning5) {
  if (catchersUsedSoFar >= catcherCount) {
    console.log('❌ Rotation BLOCKED: All catchers have played, keeping current catcher');
  } else {
    console.log('✅ Rotation ALLOWED: Not all catchers used yet, can rotate to Vienna');
  }
} else {
  console.log('✅ Normal rotation logic applies');
}

console.log('\n💡 HYPOTHESIS:');
console.log('Vienna may not be getting catcher opportunities because:');
console.log('1. The catcher rotation is working correctly in early innings');
console.log('2. Mikayla gets first priority (go-to catcher)');
console.log('3. Grace gets second priority (alphabetical or random among capable)');
console.log('4. By the time Vienna could rotate in, rotation might be suppressed');
console.log('');
console.log('OR alternatively:');
console.log('5. Vienna is being assigned to pitch instead of being available for catcher rotation');
console.log('6. The pitcher planning takes precedence over catcher rotation');

console.log('\n🔧 DEBUGGING STEPS:');
console.log('1. Check if Vienna appears in early innings as catcher (innings 1-2)');
console.log('2. Look for "Overriding catcher rotation" messages in console');
console.log('3. Check if Mikayla dominates catcher position due to go-to priority');
console.log('4. Verify if Vienna\'s pitcher assignments conflict with her catcher opportunities');
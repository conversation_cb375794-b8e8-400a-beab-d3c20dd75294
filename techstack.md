# Dugout Boss - Technology Stack

## Overview

Dugout Boss is a production-ready MVP built using modern web technologies with a focus on performance, scalability, and developer experience. This document outlines the current technology stack powering the application.

## Frontend Technologies

### Core Framework
- **React 18.3.1**: Modern JavaScript library with hooks, concurrent features, and functional components
- **TypeScript 5.6.2**: Strongly-typed JavaScript providing excellent developer experience and runtime safety
- **Vite 5.4.2**: Lightning-fast build tool and development server with HMR

### UI Components and Styling
- **shadcn/ui**: Production-ready component library built with Radix UI and Tailwind CSS
- **Tailwind CSS 3.4.1**: Utility-first CSS framework for rapid, responsive design development
- **Radix UI**: Unstyled, accessible components providing solid accessibility foundations
- **Lucide React 0.344.0**: Beautiful, customizable icon library with 1000+ icons

### State Management
- **React Context API**: Global state management for authentication and team data
- **React Hook Form 7.53.0**: Performant form handling with built-in validation

### Routing
- **React Router DOM 6.26.2**: Declarative routing for single-page application navigation

### PDF Generation
- **jsPDF 2.5.1**: Client-side PDF generation for professional lineup sheets

### Utility Libraries
- **clsx 2.1.1**: Utility for constructing className strings conditionally
- **React Hot Toast 2.4.1**: Elegant toast notifications for user feedback

## Backend Technologies

### Database and Backend Services
- **Supabase**: Complete backend-as-a-service providing:
  - **PostgreSQL 15**: Robust relational database with ACID compliance
  - **Real-time subscriptions**: Live data updates across clients
  - **Row Level Security**: Database-level security policies
  - **Edge Functions**: Serverless Deno runtime for custom logic
  - **Storage**: File upload and management capabilities

### Authentication
- **Supabase Auth**: Enterprise-grade authentication system with:
  - Email/password authentication with verification
  - JWT token management with automatic refresh
  - Secure session handling and persistence
  - Password reset functionality
  - Demo mode support

### API
- **Supabase REST API**: Auto-generated, type-safe REST API for all database operations
- **Supabase Client 2.45.4**: Official JavaScript client with TypeScript support

### Payment Processing
- **Stripe**: Industry-leading payment platform providing:
  - Subscription management and recurring billing
  - PCI-compliant payment processing
  - Webhook integration for real-time payment events
  - Test and production environment support

## Development Tools

### Code Quality
- **ESLint 9.9.1**: Comprehensive JavaScript/TypeScript linting with custom rules
- **TypeScript Compiler**: Strict type checking and compilation
- **Prettier**: Automated code formatting for consistency

### Build and Package Management
- **Vite**: Optimized production builds with tree-shaking and code splitting
- **npm 10.x**: Package management and dependency resolution
- **@vitejs/plugin-react**: React support for Vite

### Version Control
- **Git**: Distributed version control system
- **GitHub**: Repository hosting with CI/CD integration

## Production Infrastructure

### Current Deployment Status
- **Status**: Production-ready MVP
- **Database**: Supabase managed PostgreSQL in production
- **Frontend**: Ready for deployment to Vercel, Netlify, or similar platforms
- **CDN**: Global content delivery through hosting provider

### Recommended Hosting
- **Frontend**: Vercel or Netlify for optimal performance and CI/CD integration
- **Backend**: Supabase managed infrastructure (already deployed)
- **Domain**: Custom domain configuration supported
- **SSL**: Automatic HTTPS through hosting providers

## Architecture

### Frontend Architecture
Production-ready component-based architecture featuring:
- **Modular Components**: Reusable UI components with clear separation of concerns
- **Context Providers**: Centralized state management (AuthContext, TeamContext)
- **Service Layer**: Abstracted API calls through dedicated service modules
- **Custom Hooks**: Encapsulated business logic and reusable functionality
- **Type Safety**: Comprehensive TypeScript coverage

### Backend Architecture
Robust Supabase-powered backend with:
- **PostgreSQL Database**: Normalized schema with proper relationships
- **Row Level Security**: Database-level security policies for data protection
- **JWT Authentication**: Secure, stateless authentication system
- **RESTful API**: Auto-generated API with type safety
- **Real-time Features**: Live data updates and subscriptions

### Data Flow
1. User interacts with React frontend components
2. Frontend makes type-safe API calls through Supabase client
3. Supabase enforces Row Level Security policies
4. Database returns filtered, authorized data
5. React Context updates global state
6. UI re-renders with new data

## Database Schema

Production database with normalized relational schema:

### Core Tables
```sql
-- Team Management
teams (id, name, user_id, created_at, updated_at)
players (id, team_id, name, position_restrictions, created_at)
rotation_rules (id, team_id, equal_playing_time, max_consecutive_bench)

-- Lineup Management
lineups (id, team_id, name, game_date, created_at)
lineup_innings (id, lineup_id, inning, player_id, position)
batting_orders (id, lineup_id, player_id, batting_position)

-- User Management
subscriptions (user_id, is_paid, payment_date, stripe_customer_id)
profiles (id, email, is_admin, created_at)
```

### Security Implementation
- **Row Level Security**: All tables protected with RLS policies
- **User Isolation**: Data scoped to authenticated users
- **Admin Access**: Separate policies for administrative functions

See `backend.md` for complete schema documentation.

## Environment Setup

### Production Environment Variables
```bash
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_...
```

### Development Requirements
- **Node.js 18+**: Modern JavaScript runtime
- **npm 10+**: Package management
- **Git**: Version control
- **Modern Browser**: Chrome, Firefox, Safari, or Edge

## Getting Started

### Quick Start
```bash
# Clone the repository
git clone https://github.com/canadian12345/diamond-lineup-guru.git

# Navigate to project directory
cd diamond-lineup-guru

# Install dependencies
npm install

# Start development server
npm run dev
```

### Available Scripts
```bash
npm run dev          # Start development server
npm run build        # Create production build
npm run preview      # Preview production build
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript compiler
```

## Production Deployment

### Build Process
```bash
# Create optimized production build
npm run build

# Preview production build locally
npm run preview
```

### Deployment Checklist
- ✅ Environment variables configured
- ✅ Supabase project deployed and configured
- ✅ Stripe webhooks configured
- ✅ Domain and SSL certificates set up
- ✅ Performance optimization verified

## Performance Optimizations

### Frontend Performance
- **Code Splitting**: Route-based lazy loading reduces initial bundle size
- **Tree Shaking**: Unused code eliminated in production builds
- **Asset Optimization**: Images and static assets optimized
- **Caching**: Strategic browser caching for static assets

### Database Performance
- **Optimized Queries**: Efficient query patterns with proper indexing
- **Connection Pooling**: Managed by Supabase infrastructure
- **Real-time Subscriptions**: Efficient live data updates

## Security Implementation

### Authentication Security
- **JWT Tokens**: Secure, stateless authentication
- **Password Security**: Bcrypt hashing through Supabase
- **Email Verification**: Required for account activation
- **Session Management**: Automatic token refresh and secure logout

### Data Security
- **Row Level Security**: Database-level access control
- **HTTPS Everywhere**: All communications encrypted
- **Input Validation**: Multi-layer validation (client, server, database)
- **SQL Injection Prevention**: Parameterized queries through Supabase

### Payment Security
- **PCI Compliance**: Stripe handles all payment data
- **Webhook Security**: Signed webhook verification
- **No Stored Payment Data**: All payment info handled by Stripe

## Scalability Architecture

### Frontend Scalability
- **Static Site Generation**: Fast loading and CDN distribution
- **Client-side Caching**: Reduced server requests
- **Progressive Loading**: Components loaded as needed

### Backend Scalability
- **Managed Infrastructure**: Supabase handles scaling automatically
- **Connection Pooling**: Efficient database connection management
- **Edge Functions**: Serverless scaling for custom logic

## Quality Assurance

### Code Quality
- **TypeScript Strict Mode**: Maximum type safety
- **ESLint Configuration**: Consistent code standards
- **Component Testing**: Manual testing with comprehensive coverage
- **Cross-browser Testing**: Verified on all major browsers

### Performance Monitoring
- **Lighthouse Scores**: Optimized for performance, accessibility, SEO
- **Bundle Analysis**: Monitored bundle size and optimization
- **Database Performance**: Query optimization and monitoring

This technology stack provides a robust, scalable foundation for the Baseball Lineup Guru application, successfully serving youth baseball and softball coaches with reliable lineup management capabilities.

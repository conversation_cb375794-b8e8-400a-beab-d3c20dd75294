#!/usr/bin/env node

import { supabase } from "./src/integrations/supabase/node-client.js";

// Load environment variables

async function testGameResultUpdate() {
  console.log('🔍 Testing game_result column and update functionality...\n');

  try {
    // 1. Check if game_result column exists
    console.log('1. Checking if game_result column exists...');
    const { data: columnCheck, error: columnError } = await supabase
      .from('lineups')
      .select('id, name, game_result')
      .limit(1);
    
    if (columnError) {
      if (columnError.message.includes('column lineups.game_result does not exist')) {
        console.error('❌ game_result column does not exist!');
        console.log('\nTo fix this, run the following SQL in Supabase:');
        console.log(`
ALTER TABLE lineups 
ADD COLUMN IF NOT EXISTS game_result TEXT CHECK (game_result IN ('win', 'loss', null));

CREATE INDEX IF NOT EXISTS idx_lineups_game_result ON lineups(game_result);
        `);
        return;
      } else {
        console.error('Error checking column:', columnError);
        return;
      }
    }
    
    console.log('✅ game_result column exists\n');

    // 2. Get a sample lineup to test with
    console.log('2. Getting a sample lineup to test with...');
    const { data: lineups, error: fetchError } = await supabase
      .from('lineups')
      .select('id, name, game_result, user_id')
      .limit(5);
    
    if (fetchError) {
      console.error('Error fetching lineups:', fetchError);
      return;
    }
    
    if (!lineups || lineups.length === 0) {
      console.log('No lineups found to test with');
      return;
    }
    
    console.log(`Found ${lineups.length} lineups:`);
    lineups.forEach(l => {
      console.log(`  - ${l.name} (ID: ${l.id}, Result: ${l.game_result || 'not set'})`);
    });
    console.log('');

    // 3. Test updating game_result
    const testLineup = lineups[0];
    console.log(`3. Testing update on lineup: "${testLineup.name}" (ID: ${testLineup.id})`);
    
    // First, check RLS policies
    console.log('   Checking if we can update this lineup...');
    
    // Try to update with a test value
    const newResult = testLineup.game_result === 'win' ? 'loss' : 'win';
    console.log(`   Attempting to set game_result to: ${newResult}`);
    
    const { data: updateData, error: updateError } = await supabase
      .from('lineups')
      .update({ game_result: newResult })
      .eq('id', testLineup.id)
      .select();
    
    if (updateError) {
      console.error('❌ Update failed:', updateError);
      console.log('\nPossible issues:');
      console.log('1. RLS policies may be blocking the update');
      console.log('2. You may need to authenticate as the lineup owner');
      console.log('3. The column constraint might be rejecting the value');
      return;
    }
    
    if (!updateData || updateData.length === 0) {
      console.log('❌ Update returned no data - this might indicate an RLS policy issue');
      return;
    }
    
    console.log('✅ Update successful!');
    console.log(`   New game_result: ${updateData[0].game_result}`);
    
    // 4. Verify the update
    console.log('\n4. Verifying the update...');
    const { data: verifyData, error: verifyError } = await supabase
      .from('lineups')
      .select('id, name, game_result')
      .eq('id', testLineup.id)
      .single();
    
    if (verifyError) {
      console.error('Error verifying update:', verifyError);
      return;
    }
    
    console.log(`✅ Verified: game_result is now "${verifyData.game_result}"`);
    
    // 5. Test setting to null
    console.log('\n5. Testing setting game_result to null...');
    const { data: nullData, error: nullError } = await supabase
      .from('lineups')
      .update({ game_result: null })
      .eq('id', testLineup.id)
      .select();
    
    if (nullError) {
      console.error('❌ Failed to set to null:', nullError);
    } else {
      console.log('✅ Successfully set game_result to null');
    }
    
    console.log('\n✅ All tests passed! The game_result column is working correctly.');
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the test
testGameResultUpdate();
-- Check data isolation <NAME_EMAIL> seeing <EMAIL> data

-- 1. Check both users exist and get their IDs
SELECT 
    'User Check' as check_type,
    u.id,
    u.email,
    u.created_at,
    p.id IS NOT NULL as has_profile,
    p.full_name,
    p.email as profile_email
FROM auth.users u
LEFT JOIN public.profiles p ON p.user_id = u.id
WHERE u.email IN ('<EMAIL>', '<EMAIL>')
ORDER BY u.email;

-- 2. Check teams for both users
SELECT 
    'Teams Check' as check_type,
    t.id as team_id,
    t.name as team_name,
    t.user_id,
    u.email as owner_email,
    t.created_at
FROM public.teams t
JOIN auth.users u ON u.id = t.user_id
WHERE u.email IN ('<EMAIL>', '<EMAIL>')
ORDER BY u.email, t.created_at;

-- 3. Check if RLS is enabled on critical tables
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables
WHERE schemaname = 'public'
AND tablename IN ('profiles', 'teams', 'players', 'lineups', 'subscriptions')
ORDER BY tablename;

-- 4. Check RLS policies on teams table
SELECT 
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies
WHERE schemaname = 'public'
AND tablename = 'teams'
ORDER BY policyname;

-- 5. Test what each user should see when querying teams
-- This simulates what the app would see for each user

-- For <EMAIL>
DO $$
DECLARE
    v_user_id uuid;
    v_team_count integer;
BEGIN
    -- Get user <NAME_EMAIL>
    SELECT id INTO v_user_id FROM auth.users WHERE email = '<EMAIL>';
    
    IF v_user_id IS NOT NULL THEN
        -- Count teams this user owns
        SELECT COUNT(*) INTO v_team_count 
        FROM public.teams 
        WHERE user_id = v_user_id;
        
        RAISE NOTICE 'User <EMAIL> (%) owns % teams', v_user_id, v_team_count;
        
        -- List the teams
        FOR r IN SELECT id, name FROM public.teams WHERE user_id = v_user_id LOOP
            RAISE NOTICE '  - Team: % (ID: %)', r.name, r.id;
        END LOOP;
    ELSE
        RAISE NOTICE 'User <EMAIL> not found';
    END IF;
END $$;

-- For <EMAIL>
DO $$
DECLARE
    v_user_id uuid;
    v_team_count integer;
BEGIN
    -- Get user <NAME_EMAIL>
    SELECT id INTO v_user_id FROM auth.users WHERE email = '<EMAIL>';
    
    IF v_user_id IS NOT NULL THEN
        -- Count teams this user owns
        SELECT COUNT(*) INTO v_team_count 
        FROM public.teams 
        WHERE user_id = v_user_id;
        
        RAISE NOTICE 'User <EMAIL> (%) owns % teams', v_user_id, v_team_count;
        
        -- List the teams
        FOR r IN SELECT id, name FROM public.teams WHERE user_id = v_user_id LOOP
            RAISE NOTICE '  - Team: % (ID: %)', r.name, r.id;
        END LOOP;
    ELSE
        RAISE NOTICE 'User <EMAIL> not found';
    END IF;
END $$;

-- 6. Check if there's any cross-reference or shared data
SELECT 
    'Cross Reference Check' as check_type,
    t1.name as team_name,
    u1.email as owner_email,
    COUNT(DISTINCT p.id) as player_count,
    COUNT(DISTINCT l.id) as lineup_count
FROM public.teams t1
JOIN auth.users u1 ON u1.id = t1.user_id
LEFT JOIN public.players p ON p.team_id = t1.id
LEFT JOIN public.lineups l ON l.team_id = t1.id
WHERE u1.email IN ('<EMAIL>', '<EMAIL>')
GROUP BY t1.id, t1.name, u1.email
ORDER BY u1.email;

-- 7. Check if profiles have correct user_id references
SELECT 
    'Profile Integrity Check' as check_type,
    p.user_id,
    p.email as profile_email,
    u.email as auth_email,
    CASE 
        WHEN p.user_id = u.id THEN 'MATCH'
        ELSE 'MISMATCH - DATA BREACH!'
    END as integrity_status
FROM public.profiles p
JOIN auth.users u ON u.id = p.user_id
WHERE u.email IN ('<EMAIL>', '<EMAIL>');

-- 8. CRITICAL: Check if RLS is actually working by testing a direct query
-- This simulates what happens when the app queries with a specific user context
SELECT 
    'RLS Test' as check_type,
    'If RLS is working, each user should only see their own teams' as note;

-- Show what policies would allow
SELECT 
    'Policy Analysis' as check_type,
    tablename,
    policyname,
    cmd as operation,
    CASE 
        WHEN qual LIKE '%auth.uid()%' THEN 'Uses auth.uid() - GOOD'
        WHEN qual LIKE '%user_id%' THEN 'Uses user_id - CHECK IMPLEMENTATION'
        ELSE 'OTHER - INVESTIGATE'
    END as policy_type,
    qual as policy_condition
FROM pg_policies
WHERE schemaname = 'public'
AND tablename IN ('teams', 'profiles', 'players', 'lineups')
ORDER BY tablename, policyname;
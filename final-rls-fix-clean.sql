-- Final comprehensive RLS fix to ensure <PERSON> can see his data when logged in as admin

-- First, lets see whats actually happening
DO $$
DECLARE
  noah_user_id UUID;
BEGIN
  -- Get <PERSON>'s user ID
  SELECT id INTO noah_user_id FROM auth.users WHERE email = '<EMAIL>';
  RAISE NOTICE 'Noah user ID: %', noah_user_id;
END $$;

-- Create a helper function to check if current user is admin
CREATE OR REPLACE FUNCTION public.is_admin_user()
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT auth.email() IN ('<EMAIL>', '<EMAIL>');
$$;

-- Now lets fix ALL the policies using a consistent pattern
-- The key insight: admins should bypass ALL restrictions

-- TEAMS TABLE
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "teams_select" ON public.teams;
DROP POLICY IF EXISTS "teams_insert" ON public.teams;
DROP POLICY IF EXISTS "teams_update" ON public.teams;
DROP POLICY IF EXISTS "teams_delete" ON public.teams;

CREATE POLICY "Enable read access for users" ON public.teams
  FOR SELECT USING (
    auth.uid() = user_id OR public.is_admin_user()
  );

CREATE POLICY "Enable insert for users" ON public.teams
  FOR INSERT WITH CHECK (
    auth.uid() = user_id OR public.is_admin_user()
  );

CREATE POLICY "Enable update for users" ON public.teams
  FOR UPDATE USING (
    auth.uid() = user_id OR public.is_admin_user()
  );

CREATE POLICY "Enable delete for users" ON public.teams
  FOR DELETE USING (
    auth.uid() = user_id OR public.is_admin_user()
  );

-- PLAYERS TABLE
ALTER TABLE public.players ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "players_select" ON public.players;
DROP POLICY IF EXISTS "players_insert" ON public.players;
DROP POLICY IF EXISTS "players_update" ON public.players;
DROP POLICY IF EXISTS "players_delete" ON public.players;

CREATE POLICY "Enable read access for users" ON public.players
  FOR SELECT USING (
    auth.uid() = user_id OR public.is_admin_user()
  );

CREATE POLICY "Enable insert for users" ON public.players
  FOR INSERT WITH CHECK (
    auth.uid() = user_id OR public.is_admin_user()
  );

CREATE POLICY "Enable update for users" ON public.players
  FOR UPDATE USING (
    auth.uid() = user_id OR public.is_admin_user()
  );

CREATE POLICY "Enable delete for users" ON public.players
  FOR DELETE USING (
    auth.uid() = user_id OR public.is_admin_user()
  );

-- LINEUPS TABLE
ALTER TABLE public.lineups ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "lineups_select_policy" ON public.lineups;
DROP POLICY IF EXISTS "lineups_insert_policy" ON public.lineups;
DROP POLICY IF EXISTS "lineups_update_policy" ON public.lineups;
DROP POLICY IF EXISTS "lineups_delete_policy" ON public.lineups;

CREATE POLICY "Enable read access for users" ON public.lineups
  FOR SELECT USING (
    auth.uid() = user_id OR public.is_admin_user()
  );

CREATE POLICY "Enable insert for users" ON public.lineups
  FOR INSERT WITH CHECK (
    auth.uid() = user_id OR public.is_admin_user()
  );

CREATE POLICY "Enable update for users" ON public.lineups
  FOR UPDATE USING (
    auth.uid() = user_id OR public.is_admin_user()
  );

CREATE POLICY "Enable delete for users" ON public.lineups
  FOR DELETE USING (
    auth.uid() = user_id OR public.is_admin_user()
  );

-- LINEUP_INNINGS TABLE
ALTER TABLE public.lineup_innings ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "lineup_innings_select_policy" ON public.lineup_innings;
DROP POLICY IF EXISTS "lineup_innings_insert_policy" ON public.lineup_innings;
DROP POLICY IF EXISTS "lineup_innings_update_policy" ON public.lineup_innings;
DROP POLICY IF EXISTS "lineup_innings_delete_policy" ON public.lineup_innings;

CREATE POLICY "Enable read access for users" ON public.lineup_innings
  FOR SELECT USING (
    auth.uid() = user_id OR public.is_admin_user()
  );

CREATE POLICY "Enable insert for users" ON public.lineup_innings
  FOR INSERT WITH CHECK (
    auth.uid() = user_id OR public.is_admin_user()
  );

CREATE POLICY "Enable update for users" ON public.lineup_innings
  FOR UPDATE USING (
    auth.uid() = user_id OR public.is_admin_user()
  );

CREATE POLICY "Enable delete for users" ON public.lineup_innings
  FOR DELETE USING (
    auth.uid() = user_id OR public.is_admin_user()
  );

-- ROTATION_RULES TABLE
ALTER TABLE public.rotation_rules ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "rotation_rules_select_policy" ON public.rotation_rules;
DROP POLICY IF EXISTS "rotation_rules_insert_policy" ON public.rotation_rules;
DROP POLICY IF EXISTS "rotation_rules_update_policy" ON public.rotation_rules;
DROP POLICY IF EXISTS "rotation_rules_delete_policy" ON public.rotation_rules;

CREATE POLICY "Enable read access for users" ON public.rotation_rules
  FOR SELECT USING (
    auth.uid() = user_id OR public.is_admin_user()
  );

CREATE POLICY "Enable insert for users" ON public.rotation_rules
  FOR INSERT WITH CHECK (
    auth.uid() = user_id OR public.is_admin_user()
  );

CREATE POLICY "Enable update for users" ON public.rotation_rules
  FOR UPDATE USING (
    auth.uid() = user_id OR public.is_admin_user()
  );

CREATE POLICY "Enable delete for users" ON public.rotation_rules
  FOR DELETE USING (
    auth.uid() = user_id OR public.is_admin_user()
  );

-- LINEUP_ATTENDANCE TABLE
ALTER TABLE public.lineup_attendance ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "lineup_attendance_select_policy" ON public.lineup_attendance;
DROP POLICY IF EXISTS "lineup_attendance_insert_policy" ON public.lineup_attendance;
DROP POLICY IF EXISTS "lineup_attendance_update_policy" ON public.lineup_attendance;
DROP POLICY IF EXISTS "lineup_attendance_delete_policy" ON public.lineup_attendance;

CREATE POLICY "Enable read access for users" ON public.lineup_attendance
  FOR SELECT USING (
    auth.uid() = user_id OR public.is_admin_user()
  );

CREATE POLICY "Enable insert for users" ON public.lineup_attendance
  FOR INSERT WITH CHECK (
    auth.uid() = user_id OR public.is_admin_user()
  );

CREATE POLICY "Enable update for users" ON public.lineup_attendance
  FOR UPDATE USING (
    auth.uid() = user_id OR public.is_admin_user()
  );

CREATE POLICY "Enable delete for users" ON public.lineup_attendance
  FOR DELETE USING (
    auth.uid() = user_id OR public.is_admin_user()
  );

-- Test the function
SELECT public.is_admin_user() as is_admin;

-- Verify Noah can see teams
SELECT COUNT(*) as noah_teams 
FROM public.teams 
WHERE user_id = (SELECT id FROM auth.users WHERE email = '<EMAIL>');
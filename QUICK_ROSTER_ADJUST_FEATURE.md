# Quick Roster Adjust Feature

## Overview
A last-minute roster adjustment feature that allows coaches to quickly update player attendance and regenerate lineups right before game time. This solves the common scenario where players drop out or show up unexpectedly.

## Feature Location
- **Page**: ViewLineup (`/src/pages/ViewLineup.tsx`)
- **UI**: Blue info box with "Last minute change?" section at the top of the lineup view
- **Button**: "🔄 Quick Roster Adjust"

## How It Works

### User Flow
1. Coach opens an existing lineup
2. Sees "Last minute change?" section with explanatory text
3. Clicks "Quick Roster Adjust" button
4. Dialog shows ALL team players with checkboxes
5. Coach can:
   - Uncheck players who can't make it (shown with red background)
   - Check players who just arrived (shown with green background)
6. Click "Apply Changes & Regenerate"
7. System updates the lineup intelligently

### Visual Indicators
- **Red background + "(dropping)"**: Players being removed from the game
- **Green background + "(adding)"**: Players being added to the game
- **Gray background**: No change in attendance status

### Algorithm Behavior
1. **Preserves existing assignments**: Players who are still attending keep their current positions
2. **Smart position filling**: When players drop, empty positions are filled by:
   - First: Players eligible for that position (respecting position preferences)
   - Second: Any available player if no eligible player exists
3. **Complete regeneration**: All innings are regenerated to maintain fair playing time
4. **Minimum player validation**: Requires at least 8 players to create a valid lineup

## Technical Implementation

### Key Components
```typescript
// State management
const [showQuickEditDialog, setShowQuickEditDialog] = useState(false);
const [quickEditAttendance, setQuickEditAttendance] = useState<{[playerId: string]: boolean}>({});
const [originalAttendance, setOriginalAttendance] = useState<{[playerId: string]: boolean}>({});
const [isUpdating, setIsUpdating] = useState(false);
```

### Data Flow
1. **Initialization**: Attendance state populated from current lineup (all team players shown)
2. **Tracking**: Uses player IDs for attendance, player names for positions
3. **Update Process**:
   - Collects checked player IDs
   - Rebuilds first inning keeping attending players in position
   - Fills empty spots with available players
   - Uses `improvedRotateForNextInning` for subsequent innings
   - Saves to database with proper attendance tracking

### Key Functions
- `handleApplyQuickChanges()`: Main function that processes attendance changes
- Preserves rotation settings from original lineup
- Respects position lockouts and preferences
- Ensures all 9 field positions are filled

## Important Notes

### State Synchronization
- Dialog doesn't re-initialize attendance while open (prevents losing checkbox state)
- No immediate lineup reload after update (prevents state conflicts)
- Uses loading state to prevent double-clicks

### Database Considerations
- Lineups use player **names** in positions
- Attendance tracking uses player **IDs** (UUIDs)
- This dual system maintains compatibility with existing lineup system

### Fixed Issues
1. **Double-click problem**: Fixed by preventing re-initialization during edits
2. **React hooks error**: Removed conditional `useMemo` call
3. **X-Frame-Options warning**: Removed invalid meta tag from index.html
4. **Random player selection**: Fixed assignment logic to only use selected players

## Usage Example
```
Coach has a game with 12 players scheduled. 
- 5 minutes before game: 2 players call in sick
- 1 player shows up unexpectedly

Coach:
1. Opens lineup
2. Clicks "Quick Roster Adjust"
3. Unchecks the 2 sick players
4. Checks the unexpected player
5. Clicks "Apply Changes & Regenerate"

Result: New lineup with 11 players, positions filled intelligently, fair playing time maintained
```

## Future Enhancements
- Quick position swap within current inning only
- Bulk operations (check/uncheck all)
- Reason tracking for attendance changes
- Notification system for roster changes
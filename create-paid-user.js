import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing environment variables. Please check your .env.local file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function createPaidUser(email, password, fullName) {
  console.log(`\nCreating paid user: ${email}\n`);

  try {
    // 1. Sign up the user
    console.log('1. Creating user account...');
    const { data: userData, error: signUpError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName
        }
      }
    });

    if (signUpError) {
      if (signUpError.message.includes('already registered')) {
        console.error('❌ User already exists. If you want to make an existing user paid, use the update-user-subscription.js script instead.');
      } else {
        console.error('❌ Failed to create user:', signUpError.message);
      }
      return;
    }

    const userId = userData.user?.id;
    if (!userId) {
      console.error('❌ Failed to get user ID');
      return;
    }

    console.log('✅ User created with ID:', userId);

    // 2. Wait a moment for auth to propagate
    console.log('\n2. Waiting for auth to propagate...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 3. Create profile
    console.log('\n3. Creating user profile...');
    const { error: profileError } = await supabase
      .from('profiles')
      .upsert({
        id: userId,
        full_name: fullName,
        email: email,
        role: 'User',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (profileError) {
      console.warn('⚠️  Profile creation had issues:', profileError.message);
    } else {
      console.log('✅ Profile created successfully');
    }

    // 4. Create subscription for paid access
    console.log('\n4. Creating paid subscription...');
    const { error: subscriptionError } = await supabase
      .from('subscriptions')
      .insert({
        user_id: userId,
        is_paid: true,
        amount: 4900, // $49.00
        currency: 'usd',
        payment_date: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        tier: 'starter',
        team_limit: 1
      });

    if (subscriptionError) {
      console.error('❌ Failed to create subscription:', subscriptionError.message);
    } else {
      console.log('✅ Paid subscription created successfully');
    }

    console.log('\n🎉 Success! User created with full paid access.');
    console.log('\nUser details:');
    console.log(`- Email: ${email}`);
    console.log(`- Name: ${fullName}`);
    console.log(`- User ID: ${userId}`);
    console.log(`- Status: Paid subscriber`);
    console.log('\nThe user will receive a confirmation email to verify their account.');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Get command line arguments
const args = process.argv.slice(2);

if (args.length < 3) {
  console.log('Usage: node create-paid-user.js <email> <password> <full_name>');
  console.log('Example: node create-paid-user.js "<EMAIL>" "password123" "John Doe"');
  process.exit(1);
}

const [email, password, ...nameParts] = args;
const fullName = nameParts.join(' ');

// Validate password
if (password.length < 6) {
  console.error('❌ Password must be at least 6 characters long');
  process.exit(1);
}

// Create the user
createPaidUser(email, password, fullName);
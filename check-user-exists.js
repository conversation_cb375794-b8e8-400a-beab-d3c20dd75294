import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing environment variables');
  console.log('Make sure you have VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in your .env.local file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function checkUser(email) {
  console.log(`\nChecking for user with email: ${email}`);
  console.log('=' + '='.repeat(50));

  try {
    // Check in auth.users (requires service role)
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.error('Error fetching auth users:', authError);
      return;
    }

    const authUser = authUsers.users.find(u => u.email === email);
    
    if (authUser) {
      console.log('\n✓ Found in auth.users:');
      console.log('  ID:', authUser.id);
      console.log('  Email:', authUser.email);
      console.log('  Created:', authUser.created_at);
      console.log('  Last Sign In:', authUser.last_sign_in_at);
      console.log('  Email Confirmed:', authUser.email_confirmed_at ? 'Yes' : 'No');
    } else {
      console.log('\n✗ NOT found in auth.users');
    }

    // Check in profiles table
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('email', email)
      .single();

    if (profile && !profileError) {
      console.log('\n✓ Found in profiles:');
      console.log('  ID:', profile.id);
      console.log('  Email:', profile.email);
      console.log('  Name:', profile.full_name);
      console.log('  Created:', profile.created_at);
    } else {
      console.log('\n✗ NOT found in profiles');
    }

    // If user exists in auth but not profiles, offer to delete
    if (authUser && !profile) {
      console.log('\n⚠️  User exists in auth but not in profiles!');
      console.log('This is likely why you cannot recreate the user.');
      
      const readline = await import('readline');
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });

      rl.question('\nDo you want to delete this user from auth? (yes/no): ', async (answer) => {
        if (answer.toLowerCase() === 'yes') {
          console.log('\nDeleting user from auth...');
          const { error: deleteError } = await supabase.auth.admin.deleteUser(authUser.id);
          
          if (deleteError) {
            console.error('Error deleting user:', deleteError);
          } else {
            console.log('✓ User deleted successfully! You can now recreate them.');
          }
        } else {
          console.log('User not deleted.');
        }
        rl.close();
        process.exit(0);
      });
    } else {
      process.exit(0);
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

// Get email from command line argument
const email = process.argv[2];

if (!email) {
  console.log('Usage: node check-user-exists.js <email>');
  console.log('Example: node check-user-exists.js <EMAIL>');
  process.exit(1);
}

checkUser(email);
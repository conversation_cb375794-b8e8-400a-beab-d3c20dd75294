import { createClient } from '@supabase/supabase-js';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env' });

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables');
  console.error('VITE_SUPABASE_URL:', supabaseUrl ? 'Set' : 'Not set');
  console.error('SUPABASE_SERVICE_ROLE_KEY or VITE_SUPABASE_ANON_KEY:', supabaseServiceKey ? 'Set' : 'Not set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigration() {
  console.log('🚀 Applying admin columns and tables migration...\n');

  try {
    // Read the migration file
    const migrationPath = path.join(__dirname, 'supabase/migrations/20250129100000_add_admin_columns_and_tables.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Split the migration into individual statements
    const statements = migrationSQL
      .split(';')
      .map(s => s.trim())
      .filter(s => s.length > 0 && !s.startsWith('--'));

    console.log(`Found ${statements.length} SQL statements to execute\n`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i] + ';';
      console.log(`Executing statement ${i + 1}/${statements.length}...`);
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        
        if (error) {
          // Try direct execution as fallback
          console.log('Direct RPC failed, trying alternative method...');
          // For now, we'll skip and log the error
          console.error(`Error in statement ${i + 1}:`, error.message);
          console.log('Statement:', statement.substring(0, 100) + '...');
        } else {
          console.log(`✅ Statement ${i + 1} executed successfully`);
        }
      } catch (err) {
        console.error(`Error in statement ${i + 1}:`, err.message);
        console.log('Statement:', statement.substring(0, 100) + '...');
      }
    }

    console.log('\n✅ Migration completed!');
    
    // Verify the changes
    console.log('\n📊 Verifying migration...\n');
    
    // Check profiles table
    const { data: profileColumns, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .limit(0);
    
    if (!profileError) {
      console.log('✅ Profiles table accessible');
    } else {
      console.error('❌ Profiles table error:', profileError.message);
    }
    
    // Check subscriptions table
    const { data: subColumns, error: subError } = await supabase
      .from('subscriptions')
      .select('*')
      .limit(0);
    
    if (!subError) {
      console.log('✅ Subscriptions table accessible');
    } else {
      console.error('❌ Subscriptions table error:', subError.message);
    }
    
    // Check admin_audit_logs table
    const { data: auditColumns, error: auditError } = await supabase
      .from('admin_audit_logs')
      .select('*')
      .limit(0);
    
    if (!auditError) {
      console.log('✅ Admin audit logs table accessible');
    } else {
      console.error('❌ Admin audit logs table error:', auditError.message);
    }
    
    console.log('\n🎉 Migration process completed!');
    console.log('\n⚠️  IMPORTANT: If you see errors above, you may need to:');
    console.log('1. Run the migration SQL directly in the Supabase SQL editor');
    console.log('2. Or use the Supabase CLI with proper service role credentials');
    console.log('\nMigration file location: supabase/migrations/20250129100000_add_admin_columns_and_tables.sql');
    
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

runMigration();
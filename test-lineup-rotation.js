#!/usr/bin/env node

/**
 * Comprehensive Unit Test Suite for Baseball Lineup Generation and Rotation
 *
 * This test suite validates the lineup generation and rotation algorithms
 * without requiring manual UI interaction. It uses <PERSON>'s actual player
 * data to test realistic scenarios with competitive mode settings.
 */

// Import Node.js modules for TypeScript compilation
import { execSync } from 'child_process';
import { readFileSync, writeFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Compile and import the TypeScript utilities
let utils;
try {
  // Try to import the compiled utilities
  const utilsPath = join(__dirname, 'src/lib/utils-enhanced.js');
  utils = await import(utilsPath);
} catch (error) {
  console.log('📦 Compiling TypeScript utilities...');

  // Create a simple wrapper to compile and export the functions we need
  const wrapperCode = `
import {
  generateCompleteLineup,
  generateOptimalLineup,
  generateLineupFromFirstInning,
  createInitialInningLineup,
  rotatePlayersForNextInning,
  validateLineup,
  validateRotation,
  LineupRotator,
  ConstraintSolver,
  PlayerEligibilityCache,
  SeededRandom,
  LineupGenerationError,
  InsufficientPlayersError,
  PositionConstraintError
} from './src/lib/utils-enhanced.ts';

export {
  generateCompleteLineup,
  generateOptimalLineup,
  generateLineupFromFirstInning,
  createInitialInningLineup,
  rotatePlayersForNextInning,
  validateLineup,
  validateRotation,
  LineupRotator,
  ConstraintSolver,
  PlayerEligibilityCache,
  SeededRandom,
  LineupGenerationError,
  InsufficientPlayersError,
  PositionConstraintError
};
`;

  writeFileSync('test-utils-wrapper.ts', wrapperCode);

  try {
    // Compile with tsx
    execSync('npx tsx test-utils-wrapper.ts', { stdio: 'inherit' });
    utils = await import('./test-utils-wrapper.js');
  } catch (compileError) {
    console.error('❌ Failed to compile utilities:', compileError.message);
    // Fall back to mock implementations
    utils = createMockUtils();
  }
}

function createMockUtils() {
  return {
    generateCompleteLineup: () => { throw new Error('Mock implementation - compilation failed'); },
    generateOptimalLineup: () => { throw new Error('Mock implementation - compilation failed'); },
    validateLineup: () => ({ valid: true, errors: [] }),
    validateRotation: () => ({ valid: true, violations: [] }),
    LineupRotator: class { rotateLineup() { throw new Error('Mock implementation'); } },
    ConstraintSolver: class { findValidAssignment() { return new Map(); } },
    PlayerEligibilityCache: class { getEligiblePositions() { return new Set(); } },
    SeededRandom: class { next() { return Math.random(); } },
    LineupGenerationError: Error,
    InsufficientPlayersError: Error,
    PositionConstraintError: Error
  };
}

class ConstraintSolver {
  constructor(players, eligibilityCache) {
    this.players = players;
    this.eligibilityCache = eligibilityCache;
  }

  findValidAssignment(positions, options) {
    // This will call the actual implementation
    throw new Error('Not implemented - need to import from utils-enhanced.ts');
  }
}

class PlayerEligibilityCache {
  constructor(players) {
    this.players = players;
  }
}

class LineupGenerationError extends Error {
  constructor(message, code, context) {
    super(message);
    this.code = code;
    this.context = context;
  }
}

// Mock functions that we'll implement
function generateCompleteLineup(players, numberOfInnings, rules) {
  throw new Error('Not implemented - need to import from utils-enhanced.ts');
}

function validateLineup(inning, players, rules) {
  // Basic validation
  const requiredPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
  const errors = [];

  for (const pos of requiredPositions) {
    if (!inning.positions[pos]) {
      errors.push(`Position ${pos} not assigned`);
    }
  }

  return { valid: errors.length === 0, errors };
}

function validateRotation(previousInnings, currentInning, rules, players) {
  // Basic rotation validation
  return { valid: true, violations: [] };
}

// Mock Noah's actual player data
const NOAH_HOUSE_LEAGUE_PLAYERS = [
  { id: '1', name: 'Avalon', positionRestrictions: {} },
  { id: '2', name: 'Avery', positionRestrictions: {} },
  { id: '3', name: 'Bella', positionRestrictions: {} },
  { id: '4', name: 'Bernie', positionRestrictions: {} },
  { id: '5', name: 'Charlotte', positionRestrictions: {} },
  { id: '6', name: 'Elle B', positionRestrictions: {} },
  { id: '7', name: 'Elle Bizzare', positionRestrictions: {} },
  { id: '8', name: 'Everly P', positionRestrictions: {} },
  { id: '9', name: 'Eveylynn', positionRestrictions: {} },
  { id: '10', name: 'Finn', positionRestrictions: {} },
  { id: '11', name: 'Grace', positionRestrictions: {} },
  { id: '12', name: 'Henley', positionRestrictions: {} }
];

// Noah's actual U15 Selects team data from database (14 players with complex restrictions)
const NOAH_SELECTS_PLAYERS = [
  {
    id: '1',
    name: 'Avalon',
    positionRestrictions: { pitcher: true, catcher: false, firstBase: false, other: null },
    positionRatings: { catcher: 5, pitcher: 3, firstBase: 4, shortstop: 4 },
    isStarPlayer: true
  },
  {
    id: '2',
    name: 'Avery',
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null },
    positionRatings: {},
    isStarPlayer: false
  },
  {
    id: '3',
    name: 'Bella',
    positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: 'Middle Infield' },
    positionRatings: { pitcher: 4, thirdBase: 4 },
    isStarPlayer: false
  },
  {
    id: '4',
    name: 'Charlotte',
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null },
    positionRatings: { shortstop: 2, thirdBase: 3, secondBase: 2 },
    isStarPlayer: false
  },
  {
    id: '5',
    name: 'Elle',
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null },
    positionRatings: {},
    isStarPlayer: false
  },
  {
    id: '6',
    name: 'Evelynn',
    positionRestrictions: { pitcher: true, catcher: false, firstBase: true, other: '3B/MI/SS/2B' },
    positionRatings: {},
    isStarPlayer: false
  },
  {
    id: '7',
    name: 'Finn',
    positionRestrictions: { pitcher: true, catcher: false, firstBase: true, other: 'Shortstop' },
    positionRatings: {},
    isStarPlayer: false
  },
  {
    id: '8',
    name: 'Grace',
    positionRestrictions: { pitcher: false, catcher: true, firstBase: true, other: null },
    positionRatings: { pitcher: 4, secondBase: 5 },
    isStarPlayer: true
  },
  {
    id: '9',
    name: 'Kaitlyn',
    positionRestrictions: { pitcher: false, catcher: true, firstBase: true, other: 'Middle Infield' },
    positionRatings: { pitcher: 2 },
    isStarPlayer: false
  },
  {
    id: '10',
    name: 'Kenzie',
    positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: null },
    positionRatings: { pitcher: 5, firstBase: 4 },
    isStarPlayer: true
  },
  {
    id: '11',
    name: 'Mikayla',
    positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: '3B/MI/SS/2B' },
    positionRatings: { pitcher: 3, firstBase: 4 },
    isStarPlayer: false
  },
  {
    id: '12',
    name: 'Morgan',
    positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: null },
    positionRatings: { pitcher: 4, firstBase: 3, shortstop: 3, secondBase: 3 },
    isStarPlayer: false
  },
  {
    id: '13',
    name: 'Presley',
    positionRestrictions: { pitcher: false, catcher: true, firstBase: true, other: null },
    positionRatings: { pitcher: 5, shortstop: 5 },
    isStarPlayer: true
  },
  {
    id: '14',
    name: 'Vienna',
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null },
    positionRatings: { thirdBase: 5 },
    isStarPlayer: true
  }
];

// Test configuration
const DEFAULT_RULES = {
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 2,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  respectPositionLockouts: true,
  equalPlayingTime: true,
  rotateLineupEvery: 1,
  rotatePitcherEvery: 2
};

// Competitive mode rules for realistic testing
const COMPETITIVE_RULES = {
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 2,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  respectPositionLockouts: true,
  equalPlayingTime: false, // Competitive mode - skill matters
  rotateLineupEvery: 2,
  rotatePitcherEvery: 3,
  competitiveMode: true,
  starPlayerRotationDelay: 2, // Star players stay in longer
  _randomSeed: 12345 // Fixed seed for reproducible results
};

class LineupTestSuite {
  constructor() {
    this.testResults = [];
    this.totalTests = 0;
    this.passedTests = 0;
    this.failedTests = 0;
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString().substr(11, 8);
    const prefix = {
      'info': '📋',
      'success': '✅',
      'error': '❌',
      'warning': '⚠️',
      'debug': '🔍'
    }[type] || '📋';
    
    console.log(`[${timestamp}] ${prefix} ${message}`);
  }

  assert(condition, message, details = null) {
    this.totalTests++;
    
    if (condition) {
      this.passedTests++;
      this.log(`PASS: ${message}`, 'success');
      this.testResults.push({ status: 'PASS', message, details });
    } else {
      this.failedTests++;
      this.log(`FAIL: ${message}`, 'error');
      if (details) {
        this.log(`Details: ${JSON.stringify(details, null, 2)}`, 'debug');
      }
      this.testResults.push({ status: 'FAIL', message, details });
    }
  }

  async runTest(testName, testFunction) {
    this.log(`\n🧪 Running test: ${testName}`, 'info');
    try {
      await testFunction();
      this.log(`✅ Test completed: ${testName}`, 'success');
    } catch (error) {
      this.log(`❌ Test failed: ${testName} - ${error.message}`, 'error');
      this.log(`Stack: ${error.stack}`, 'debug');
      this.assert(false, `Test ${testName} threw exception: ${error.message}`, { error: error.message, stack: error.stack });
    }
  }

  // Test 1: Basic Rotation Plan Creation
  async testBasicRotationPlan() {
    const players = NOAH_HOUSE_LEAGUE_PLAYERS.slice(0, 9); // Use exactly 9 players
    const eligibilityCache = new PlayerEligibilityCache(players);
    const rotator = new LineupRotator(DEFAULT_RULES, eligibilityCache, { next: () => Math.random() });

    // Create a simple first inning
    const firstInning = {
      inning: 1,
      positions: {
        pitcher: 'Avalon',
        catcher: 'Avery',
        firstBase: 'Bella',
        secondBase: 'Bernie',
        shortstop: 'Charlotte',
        thirdBase: 'Elle B',
        leftField: 'Elle Bizzare',
        centerField: 'Everly P',
        rightField: 'Eveylynn',
        bench: []
      }
    };

    // Test rotation to second inning
    const secondInning = rotator.rotateLineup(firstInning, players, 2);
    
    this.assert(secondInning !== null, 'Rotation should produce a valid second inning');
    this.assert(secondInning.inning === 2, 'Second inning should have correct inning number');
    
    // Validate all positions are filled
    const requiredPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
    for (const pos of requiredPositions) {
      this.assert(secondInning.positions[pos], `Position ${pos} should be filled`, { positions: secondInning.positions });
    }

    // Validate no duplicate players
    const assignedPlayers = new Set();
    for (const pos of requiredPositions) {
      const player = secondInning.positions[pos];
      this.assert(!assignedPlayers.has(player), `Player ${player} should not be assigned to multiple positions`);
      assignedPlayers.add(player);
    }
  }

  // Test 2: Constraint Solver Validation
  async testConstraintSolver() {
    const players = NOAH_SELECTS_PLAYERS.slice(0, 9); // Use players with restrictions
    const eligibilityCache = new PlayerEligibilityCache(players);
    const solver = new ConstraintSolver(players, eligibilityCache);

    const positions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
    
    const assignments = solver.findValidAssignment(positions, { respectPositionLockouts: true });
    
    this.assert(assignments !== null, 'Constraint solver should find valid assignments');
    this.assert(assignments.size === 9, 'Should assign all 9 positions', { assignmentCount: assignments.size });

    // Validate assignments respect restrictions
    for (const [position, playerName] of assignments) {
      const player = players.find(p => p.name === playerName);
      this.assert(player !== null, `Assigned player ${playerName} should exist`);
      
      // Check position restrictions
      if (player.positionRestrictions) {
        if (position === 'pitcher') {
          this.assert(!player.positionRestrictions.pitcher, `Player ${playerName} should not be restricted from pitcher`, { player: player.positionRestrictions });
        }
        if (position === 'catcher') {
          this.assert(!player.positionRestrictions.catcher, `Player ${playerName} should not be restricted from catcher`, { player: player.positionRestrictions });
        }
        if (position === 'firstBase') {
          this.assert(!player.positionRestrictions.firstBase, `Player ${playerName} should not be restricted from first base`, { player: player.positionRestrictions });
        }
      }
    }
  }

  // Test 3: Complete Lineup Generation
  async testCompleteLineupGeneration() {
    const players = NOAH_HOUSE_LEAGUE_PLAYERS.slice(0, 10); // 10 players, 1 on bench
    const numberOfInnings = 5;

    const lineup = generateCompleteLineup(players, numberOfInnings, DEFAULT_RULES);
    
    this.assert(lineup.length === numberOfInnings, `Should generate ${numberOfInnings} innings`, { actualInnings: lineup.length });
    
    // Validate each inning
    for (let i = 0; i < lineup.length; i++) {
      const inning = lineup[i];
      this.assert(inning.inning === i + 1, `Inning ${i + 1} should have correct inning number`);
      
      const validation = validateLineup(inning, players, DEFAULT_RULES);
      this.assert(validation.valid, `Inning ${i + 1} should be valid`, { errors: validation.errors });
    }
  }

  // Test 4: Edge Case - Minimum Players (8)
  async testMinimumPlayers() {
    const players = NOAH_HOUSE_LEAGUE_PLAYERS.slice(0, 8); // Minimum 8 players
    
    try {
      const lineup = generateCompleteLineup(players, 3, DEFAULT_RULES);
      this.assert(lineup.length === 3, 'Should generate lineup with minimum players');
      
      // Each inning should have 8 field players and 0 bench
      for (const inning of lineup) {
        this.assert(inning.positions.bench.length === 0, 'Should have no bench players with 8 total players');
      }
    } catch (error) {
      this.assert(false, `Should handle 8 players gracefully`, { error: error.message });
    }
  }

  // Test 5: Edge Case - Many Players (12)
  async testManyPlayers() {
    const players = NOAH_HOUSE_LEAGUE_PLAYERS; // All 12 players
    
    const lineup = generateCompleteLineup(players, 4, DEFAULT_RULES);
    this.assert(lineup.length === 4, 'Should generate lineup with many players');
    
    // Each inning should have 9 field players and 3 bench
    for (const inning of lineup) {
      this.assert(inning.positions.bench.length === 3, 'Should have 3 bench players with 12 total players');
    }
  }

  // Test 6: Rotation Validation
  async testRotationValidation() {
    const players = NOAH_HOUSE_LEAGUE_PLAYERS.slice(0, 9);
    const lineup = generateCompleteLineup(players, 6, DEFAULT_RULES);
    
    // Validate rotation rules for each inning
    for (let i = 1; i < lineup.length; i++) {
      const previousInnings = lineup.slice(0, i);
      const currentInning = lineup[i];
      
      const validation = validateRotation(previousInnings, currentInning, DEFAULT_RULES, players);
      this.assert(validation.valid, `Rotation validation should pass for inning ${i + 1}`, { violations: validation.violations });
    }
  }

  // Test 7: Error Handling - Invalid Input
  async testErrorHandling() {
    const players = NOAH_HOUSE_LEAGUE_PLAYERS.slice(0, 5); // Too few players
    
    try {
      generateCompleteLineup(players, 3, DEFAULT_RULES);
      this.assert(false, 'Should throw error with insufficient players');
    } catch (error) {
      this.assert(error instanceof LineupGenerationError, 'Should throw LineupGenerationError');
      this.assert(error.code === 'INSUFFICIENT_PLAYERS', 'Should have correct error code');
    }
  }

  // Test 8: Specific Bug - Rotation Plan Validation
  async testRotationPlanValidation() {
    const players = NOAH_HOUSE_LEAGUE_PLAYERS.slice(0, 9);
    const eligibilityCache = new PlayerEligibilityCache(players);
    const rotator = new LineupRotator(DEFAULT_RULES, eligibilityCache, { next: () => Math.random() });

    // Create first inning manually
    const firstInning = {
      inning: 1,
      positions: {
        pitcher: 'Avalon',
        catcher: 'Avery',
        firstBase: 'Bella',
        secondBase: 'Bernie',
        shortstop: 'Charlotte',
        thirdBase: 'Elle B',
        leftField: 'Elle Bizzare',
        centerField: 'Everly P',
        rightField: 'Eveylynn',
        bench: []
      }
    };

    // Test multiple rotations to catch validation failures
    let currentInning = firstInning;
    for (let i = 2; i <= 5; i++) {
      try {
        currentInning = rotator.rotateLineup(currentInning, players, i);
        this.assert(currentInning !== null, `Rotation to inning ${i} should succeed`);

        // Validate the rotation plan internally
        const validation = validateLineup(currentInning, players, DEFAULT_RULES);
        this.assert(validation.valid, `Inning ${i} should pass validation`, { errors: validation.errors });

      } catch (error) {
        this.assert(false, `Rotation to inning ${i} should not throw error`, { error: error.message, inning: i });
      }
    }
  }

  // Test 9: Noah's Selects Team - Competitive Mode with Complex Restrictions
  async testNoahSelectsCompetitiveMode() {
    const players = NOAH_SELECTS_PLAYERS; // All 14 players with complex restrictions
    this.log(`Testing with ${players.length} players from Noah's U15 Selects team`, 'info');

    try {
      const lineup = generateCompleteLineup(players, 6, COMPETITIVE_RULES);
      this.assert(lineup.length === 6, 'Should generate 6 innings for competitive game');

      // Validate each inning respects position restrictions
      for (let i = 0; i < lineup.length; i++) {
        const inning = lineup[i];
        this.assert(inning.inning === i + 1, `Inning ${i + 1} should have correct inning number`);

        // Check position restrictions are respected
        const requiredPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
        for (const position of requiredPositions) {
          const playerName = inning.positions[position];
          const player = players.find(p => p.name === playerName);

          this.assert(player !== null, `Player ${playerName} should exist in roster`);

          // Check specific restrictions
          if (position === 'pitcher' && player.positionRestrictions.pitcher) {
            this.assert(false, `Player ${playerName} is restricted from pitcher but assigned to it`, { inning: i + 1, position, restrictions: player.positionRestrictions });
          }
          if (position === 'catcher' && player.positionRestrictions.catcher) {
            this.assert(false, `Player ${playerName} is restricted from catcher but assigned to it`, { inning: i + 1, position, restrictions: player.positionRestrictions });
          }
          if (position === 'firstBase' && player.positionRestrictions.firstBase) {
            this.assert(false, `Player ${playerName} is restricted from first base but assigned to it`, { inning: i + 1, position, restrictions: player.positionRestrictions });
          }
        }

        // Validate bench size (5 players on bench with 14 total)
        this.assert(inning.positions.bench.length === 5, `Should have 5 bench players with 14 total players`, { actualBench: inning.positions.bench.length });
      }

      this.log('✅ Noah\'s Selects team competitive mode test passed', 'success');
    } catch (error) {
      this.assert(false, `Noah's Selects competitive mode should work`, { error: error.message, stack: error.stack });
    }
  }

  // Test 10: Star Player Rotation Delays
  async testStarPlayerRotationDelays() {
    const players = NOAH_SELECTS_PLAYERS.filter(p => p.isStarPlayer); // Only star players
    const nonStarPlayers = NOAH_SELECTS_PLAYERS.filter(p => !p.isStarPlayer).slice(0, 9 - players.length); // Fill to 9 total
    const testPlayers = [...players, ...nonStarPlayers];

    this.log(`Testing star player rotation with ${players.length} star players and ${nonStarPlayers.length} regular players`, 'info');

    try {
      const lineup = generateCompleteLineup(testPlayers, 5, COMPETITIVE_RULES);

      // Track star player field time
      const starPlayerFieldTime = new Map();
      players.forEach(p => starPlayerFieldTime.set(p.name, 0));

      for (const inning of lineup) {
        const requiredPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
        for (const position of requiredPositions) {
          const playerName = inning.positions[position];
          if (starPlayerFieldTime.has(playerName)) {
            starPlayerFieldTime.set(playerName, starPlayerFieldTime.get(playerName) + 1);
          }
        }
      }

      // Star players should have more field time than regular players
      const avgStarFieldTime = Array.from(starPlayerFieldTime.values()).reduce((a, b) => a + b, 0) / starPlayerFieldTime.size;
      this.log(`Average star player field time: ${avgStarFieldTime.toFixed(1)} innings`, 'info');

      // In competitive mode, star players should play more
      this.assert(avgStarFieldTime >= 3, 'Star players should average at least 3 innings of field time', { avgStarFieldTime });

    } catch (error) {
      this.assert(false, `Star player rotation test should work`, { error: error.message });
    }
  }

  // Test 11: Complex Position Restrictions Edge Cases
  async testComplexPositionRestrictions() {
    // Test with players who have multiple restrictions
    const restrictedPlayers = NOAH_SELECTS_PLAYERS.filter(p =>
      p.positionRestrictions.pitcher || p.positionRestrictions.catcher || p.positionRestrictions.firstBase || p.positionRestrictions.other
    ).slice(0, 9);

    this.log(`Testing with ${restrictedPlayers.length} players having complex position restrictions`, 'info');

    try {
      const lineup = generateCompleteLineup(restrictedPlayers, 4, COMPETITIVE_RULES);

      // Verify all innings are valid despite complex restrictions
      for (const inning of lineup) {
        const validation = validateLineup(inning, restrictedPlayers, COMPETITIVE_RULES);
        this.assert(validation.valid, `Inning ${inning.inning} should be valid despite complex restrictions`, { errors: validation.errors });
      }

      this.log('✅ Complex position restrictions test passed', 'success');
    } catch (error) {
      this.assert(false, `Complex position restrictions should be handled`, { error: error.message });
    }
  }

  // Test 12: Long Game Rotation Sustainability (Edge Case)
  async testLongGameRotation() {
    const players = NOAH_SELECTS_PLAYERS.slice(0, 12); // 12 players for manageable bench

    this.log(`Testing long game (8 innings) with ${players.length} players`, 'info');

    try {
      const lineup = generateCompleteLineup(players, 8, COMPETITIVE_RULES);

      // Validate rotation sustainability over long game
      for (let i = 1; i < lineup.length; i++) {
        const previousInnings = lineup.slice(0, i);
        const currentInning = lineup[i];

        const validation = validateRotation(previousInnings, currentInning, COMPETITIVE_RULES, players);
        if (!validation.valid) {
          this.log(`⚠️ Rotation violations in inning ${currentInning.inning}: ${validation.violations.join(', ')}`, 'warning');
        }
      }

      this.log('✅ Long game rotation sustainability test completed', 'success');
    } catch (error) {
      this.assert(false, `Long game rotation should be sustainable`, { error: error.message });
    }
  }

  // Summary and Results
  printSummary() {
    this.log('\n' + '='.repeat(60), 'info');
    this.log('TEST SUITE SUMMARY', 'info');
    this.log('='.repeat(60), 'info');
    this.log(`Total Tests: ${this.totalTests}`, 'info');
    this.log(`Passed: ${this.passedTests}`, 'success');
    this.log(`Failed: ${this.failedTests}`, this.failedTests > 0 ? 'error' : 'success');
    this.log(`Success Rate: ${((this.passedTests / this.totalTests) * 100).toFixed(1)}%`, 'info');
    
    if (this.failedTests > 0) {
      this.log('\nFAILED TESTS:', 'error');
      this.testResults.filter(r => r.status === 'FAIL').forEach(result => {
        this.log(`  - ${result.message}`, 'error');
      });
    }
    
    return this.failedTests === 0;
  }

  // Main test runner
  async runAllTests() {
    this.log('🚀 Starting Baseball Lineup Generation Test Suite', 'info');
    this.log('Using Noah\'s actual U15 Selects team data for realistic testing', 'info');
    this.log('Testing with competitive mode, position restrictions, and star players', 'info');

    // Basic functionality tests
    await this.runTest('Basic Rotation Plan Creation', () => this.testBasicRotationPlan());
    await this.runTest('Constraint Solver Validation', () => this.testConstraintSolver());
    await this.runTest('Complete Lineup Generation', () => this.testCompleteLineupGeneration());
    await this.runTest('Minimum Players (8)', () => this.testMinimumPlayers());
    await this.runTest('Many Players (12)', () => this.testManyPlayers());
    await this.runTest('Rotation Validation', () => this.testRotationValidation());
    await this.runTest('Error Handling', () => this.testErrorHandling());
    await this.runTest('Rotation Plan Validation Bug', () => this.testRotationPlanValidation());

    // Advanced realistic tests with Noah's data
    await this.runTest('Noah\'s Selects - Competitive Mode', () => this.testNoahSelectsCompetitiveMode());
    await this.runTest('Star Player Rotation Delays', () => this.testStarPlayerRotationDelays());
    await this.runTest('Complex Position Restrictions', () => this.testComplexPositionRestrictions());
    await this.runTest('Long Game Rotation Sustainability', () => this.testLongGameRotation());

    return this.printSummary();
  }
}

// Run the tests
if (import.meta.url === `file://${process.argv[1]}`) {
  const testSuite = new LineupTestSuite();
  testSuite.runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Test suite failed to run:', error);
    process.exit(1);
  });
}

export { LineupTestSuite };

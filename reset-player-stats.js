#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to reset player statistics by clearing the player_position_history table
 * This will reset all playing time and position distribution data
 * 
 * Usage: node reset-player-stats.js
 * 
 * Options:
 *   --team-id <id>    Reset stats for a specific team only
 *   --user-id <id>    Reset stats for a specific user only
 *   --dry-run         Show what would be deleted without actually deleting
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set');
  console.error('   Make sure you have a .env file with these values');
  process.exit(1);
}

// Create Supabase client with service role key for admin access
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  teamId: null,
  userId: null,
  dryRun: false
};

for (let i = 0; i < args.length; i++) {
  switch (args[i]) {
    case '--team-id':
      options.teamId = args[++i];
      break;
    case '--user-id':
      options.userId = args[++i];
      break;
    case '--dry-run':
      options.dryRun = true;
      break;
    case '--help':
      console.log(`
Usage: node reset-player-stats.js [options]

Options:
  --team-id <id>    Reset stats for a specific team only
  --user-id <id>    Reset stats for a specific user only
  --dry-run         Show what would be deleted without actually deleting
  --help            Show this help message

Examples:
  # Reset all player stats (use with caution!)
  node reset-player-stats.js

  # Reset stats for a specific team
  node reset-player-stats.js --team-id "123e4567-e89b-12d3-a456-426614174000"

  # Reset stats for a specific user's teams
  node reset-player-stats.js --user-id "123e4567-e89b-12d3-a456-426614174000"

  # Preview what would be deleted
  node reset-player-stats.js --dry-run
`);
      process.exit(0);
  }
}

async function resetPlayerStats() {
  try {
    console.log('🔄 Starting player stats reset...');

    // Build query
    let query = supabase.from('player_position_history').select('*', { count: 'exact' });

    if (options.teamId) {
      // First get all players for this team
      const { data: players, error: playersError } = await supabase
        .from('players')
        .select('id')
        .eq('team_id', options.teamId);

      if (playersError) {
        console.error('❌ Error fetching team players:', playersError);
        return;
      }

      const playerIds = players.map(p => p.id);
      query = query.in('player_id', playerIds);
      console.log(`📌 Filtering by team ${options.teamId} (${playerIds.length} players)`);
    }

    if (options.userId) {
      query = query.eq('user_id', options.userId);
      console.log(`📌 Filtering by user ${options.userId}`);
    }

    // Get count of records to be deleted
    const { count, error: countError } = await query;
    
    if (countError) {
      console.error('❌ Error counting records:', countError);
      return;
    }

    console.log(`📊 Found ${count} position history records to delete`);

    if (count === 0) {
      console.log('✅ No records to delete');
      return;
    }

    if (options.dryRun) {
      console.log('🔍 DRY RUN - No records were actually deleted');
      
      // Show sample of what would be deleted
      const { data: sample, error: sampleError } = await query.limit(5);
      if (!sampleError && sample.length > 0) {
        console.log('\nSample records that would be deleted:');
        sample.forEach(record => {
          console.log(`  - Player ${record.player_id}, Position: ${record.position}, Inning: ${record.inning_number}, Date: ${record.game_date}`);
        });
        if (count > 5) {
          console.log(`  ... and ${count - 5} more records`);
        }
      }
      return;
    }

    // Confirm deletion
    console.log('\n⚠️  WARNING: This will permanently delete player statistics!');
    console.log('Press CTRL+C to cancel, or wait 5 seconds to continue...');
    
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Perform deletion
    console.log('\n🗑️  Deleting records...');
    
    // Build delete query
    let deleteQuery = supabase.from('player_position_history').delete();

    if (options.teamId) {
      const { data: players } = await supabase
        .from('players')
        .select('id')
        .eq('team_id', options.teamId);
      
      const playerIds = players.map(p => p.id);
      deleteQuery = deleteQuery.in('player_id', playerIds);
    }

    if (options.userId) {
      deleteQuery = deleteQuery.eq('user_id', options.userId);
    }

    // Need to add a condition to delete all if no filters
    if (!options.teamId && !options.userId) {
      deleteQuery = deleteQuery.neq('id', '00000000-0000-0000-0000-000000000000'); // This will match all records
    }

    const { error: deleteError } = await deleteQuery;

    if (deleteError) {
      console.error('❌ Error deleting records:', deleteError);
      return;
    }

    console.log(`✅ Successfully deleted ${count} position history records`);
    console.log('📊 Player statistics have been reset');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the reset
resetPlayerStats();
-- QUICK FIX: Disable RLS to allow users to sign up

-- 1. Drop ALL policies to start fresh
DO $$
DECLARE
  rec record;
BEGIN
  -- Drop all policies on profiles
  FOR rec IN SELECT policyname FROM pg_policies WHERE schemaname = 'public' AND tablename = 'profiles'
  LOOP
    EXECUTE format('DROP POLICY IF EXISTS %I ON public.profiles', rec.policyname);
  END LOOP;
  
  -- Drop all policies on subscriptions
  FOR rec IN SELECT policyname FROM pg_policies WHERE schemaname = 'public' AND tablename = 'subscriptions'
  LOOP
    EXECUTE format('DROP POLICY IF EXISTS %I ON public.subscriptions', rec.policyname);
  END LOOP;
  
  -- Drop all policies on teams
  FOR rec IN SELECT policyname FROM pg_policies WHERE schemaname = 'public' AND tablename = 'teams'
  LOOP
    EXECUTE format('DROP POLICY IF EXISTS %I ON public.teams', rec.policyname);
  END LOOP;
END $$;

-- 2. DISABLE RLS ON ALL TABLES
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.teams DISABLE ROW LEVEL SECURITY;

-- 3. Grant all permissions
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- 4. Verify RLS is disabled
SELECT 
  tablename,
  CASE 
    WHEN relrowsecurity THEN 'ENABLED' 
    ELSE 'DISABLED' 
  END as rls_status
FROM pg_tables 
JOIN pg_class ON pg_tables.tablename = pg_class.relname
WHERE schemaname = 'public' 
  AND tablename IN ('profiles', 'subscriptions', 'teams');

-- 5. Message
DO $$
BEGIN
  RAISE NOTICE 'RLS has been DISABLED on profiles, subscriptions, and teams tables';
  RAISE NOTICE 'Users should now be able to sign up without any restrictions';
  RAISE NOTICE 'Test with a regular user account to confirm';
END $$;
# Catcher Rotation Fix Summary

## Problem
The algorithm was too strict about catcher rotations in multi-game series. With limited catchers (e.g., 3 catchers for 4 innings across a double-header), the algorithm would fail with "Cannot assign position catcher" errors instead of allowing catchers to play consecutive innings.

## Solution
Modified the strict lineup generation algorithm to be smarter about catcher rotations:

### 1. Added Catcher Analysis (single-game-lineup-strict.ts)
```typescript
// Analyze catcher availability for smart rotation
const catcherCount = positionAnalysis.positionCounts.get('catcher') || 0;
const catcherRotationFrequency = rules.rotatePitcherEvery || 3;
const estimatedCatcherSlots = Math.ceil(totalInnings / catcherRotationFrequency);
const shouldBeLenientWithCatchers = catcherCount < estimatedCatcherSlots;
```

### 2. Dynamic Rotation Override
When limited catchers are detected, the algorithm now:
- Tracks which catchers have already played
- Allows catchers to play consecutive innings once all available catchers have been used
- Prevents forced rotation that would cause assignment failures

### 3. Separate Catcher Rotation Flag
Added `shouldRotateCatcher` parameter that:
- Uses pitcher rotation frequency (typically every 2-3 innings)
- Can be overridden when catcher availability is limited
- Keeps the same catcher when rotation isn't needed

### 4. Improved Error Messages
Updated error messages in multi-game-orchestrator.ts to provide better guidance:
- Suggests checking player roles (Primary, Capable, Emergency)
- Explains that limited catchers will play consecutive innings
- Recommends adjusting rotation settings if needed

## Example Scenario
**Double-header with 3 catchers:**
- Game 1 (7 innings): Catcher 1 plays innings 1-3, Catcher 2 plays 4-7
- Game 2 (7 innings): Catcher 3 plays innings 1-3, Catcher 1 plays 4-7

Total distribution: Each catcher plays 4-5 innings, no errors!

## Files Modified
1. `/src/lib/single-game-lineup-strict.ts` - Main algorithm improvements
2. `/src/lib/multi-game-orchestrator.ts` - Better error messages and warnings
3. Created `/test-catcher-rotation-fix.js` - Test script demonstrating the fix

## Key Benefits
- Prevents "Cannot assign position" errors with limited catchers
- Maintains fair playing time distribution
- Works seamlessly with existing rotation rules
- Provides clear feedback when roster adjustments might help
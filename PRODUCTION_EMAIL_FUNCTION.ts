// PRODUCTION VERSION - USE YOUR VERIFIED DOMAIN

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.7'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get auth header
    const authHeader = req.headers.get('authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? ''
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    
    const supabaseClient = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })

    // Verify user is admin
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid authentication' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check admin status
    const { data: profile } = await supabaseClient
      .from('profiles')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized - admin access required' }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get request data
    const { email, name, temporaryPassword } = await req.json()

    if (!email || !name || !temporaryPassword) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check for Resend API key
    const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')
    if (!RESEND_API_KEY) {
      console.error('RESEND_API_KEY is not set')
      return new Response(
        JSON.stringify({ error: 'Email service not configured' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Build URLs
    const origin = req.headers.get('origin') || 'https://dugoutboss.com'
    const loginUrl = `${origin}/signin`

    // Create email HTML
    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Dugout Boss</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f5f5f5;">
    <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 40px;">
        <div style="text-align: center; padding-bottom: 30px;">
            <h1 style="color: #1e40af; margin: 0;">Dugout Boss</h1>
            <p style="color: #64748b; margin: 10px 0 0 0;">Smart Lineup Management for Coaches</p>
        </div>
        
        <h2 style="color: #1e293b; margin-bottom: 20px;">Welcome ${name}!</h2>
        
        <p style="color: #475569; line-height: 1.6; margin-bottom: 30px;">
            Your Dugout Boss account has been created. You're now ready to start creating fair and optimized lineups for your team.
        </p>
        
        <div style="background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 30px; margin-bottom: 30px;">
            <h3 style="margin: 0 0 20px 0; color: #1e293b;">Your Login Credentials</h3>
            
            <p style="margin: 0 0 15px 0; color: #475569;">
                <strong>Email:</strong><br>
                ${email}
            </p>
            
            <p style="margin: 0 0 20px 0; color: #475569;">
                <strong>Temporary Password:</strong><br>
                <code style="background-color: #fef3c7; color: #92400e; padding: 4px 8px; border-radius: 4px;">${temporaryPassword}</code>
            </p>
            
            <p style="margin: 0 0 20px 0; color: #dc2626; font-weight: bold;">
                ⚠️ Please change your password after your first login
            </p>
            
            <a href="${loginUrl}" style="display: inline-block; background-color: #1e40af; color: #ffffff; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;">
                Login to Dugout Boss
            </a>
        </div>
        
        <h3 style="color: #1e293b; margin-bottom: 20px;">Getting Started</h3>
        
        <ol style="color: #475569; line-height: 1.8; padding-left: 20px;">
            <li>Set up your team roster - Add players and their position preferences</li>
            <li>Configure rotation rules - Set playing time goals and bench limits</li>
            <li>Generate your first lineup - Let our AI create fair, optimized lineups instantly</li>
        </ol>
        
        <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 30px 0;">
        
        <p style="color: #64748b; font-size: 14px; text-align: center; margin: 0;">
            Need help? Email <NAME_EMAIL><br>
            © ${new Date().getFullYear()} Dugout Boss. All rights reserved.
        </p>
    </div>
</body>
</html>`

    // Create plain text version
    const textContent = `Welcome to Dugout Boss!

Hi ${name},

Your Dugout Boss account has been created. You're now ready to start creating fair and optimized lineups for your team.

YOUR LOGIN CREDENTIALS
=====================
Email: ${email}
Temporary Password: ${temporaryPassword}

⚠️ IMPORTANT: Please change your password after your first login.

Login here: ${loginUrl}

GETTING STARTED
===============
1. Set up your team roster - Add players and their position preferences
2. Configure rotation rules - Set playing time goals and bench limits
3. Generate your first lineup - Let our AI create fair, optimized lineups instantly

Need help? Email <NAME_EMAIL>

Best regards,
The Dugout Boss Team

© ${new Date().getFullYear()} Dugout Boss. All rights reserved.`

    // ===== IMPORTANT: UPDATE THESE VALUES =====
    // Option 1: Use environment variables (recommended)
    const FROM_EMAIL = Deno.env.get('FROM_EMAIL') || '<EMAIL>'
    const FROM_NAME = Deno.env.get('FROM_NAME') || 'Dugout Boss'
    
    // Option 2: Hardcode your verified domain email
    // const FROM_EMAIL = '<EMAIL>'  // Replace with your verified domain
    // const FROM_NAME = 'Dugout Boss'
    
    console.log(`Sending email from: ${FROM_NAME} <${FROM_EMAIL}>`)

    // Send email via Resend
    try {
      const resendResponse = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${RESEND_API_KEY}`,
        },
        body: JSON.stringify({
          from: `${FROM_NAME} <${FROM_EMAIL}>`,  // THIS IS THE KEY CHANGE
          to: [email],
          subject: 'Welcome to Dugout Boss - Your Account Has Been Created',
          html: htmlContent,
          text: textContent,
        }),
      })

      const responseText = await resendResponse.text()
      console.log('Resend response:', resendResponse.status, responseText)

      if (!resendResponse.ok) {
        console.error('Resend API error:', responseText)
        return new Response(
          JSON.stringify({ 
            error: 'Failed to send email',
            details: responseText
          }),
          { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }

      const result = JSON.parse(responseText)
      console.log('Welcome email sent successfully:', result.id)

      return new Response(
        JSON.stringify({ 
          success: true, 
          messageId: result.id
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
      
    } catch (fetchError) {
      console.error('Error calling Resend:', fetchError)
      return new Response(
        JSON.stringify({ 
          error: 'Failed to call Resend API',
          details: fetchError.message
        }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
    
  } catch (error) {
    console.error('Error in send-welcome-email function:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
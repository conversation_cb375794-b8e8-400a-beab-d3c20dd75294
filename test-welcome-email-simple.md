# Simple Welcome Email Testing Guide

## Quick Test Steps

### 1. Deploy the Functions
```bash
./deploy-welcome-email.sh
```

### 2. Set Your RESEND_API_KEY in Supabase
1. Go to your Supabase project dashboard
2. Click on "Edge Functions" in the left sidebar
3. Click on "Secrets" tab
4. Add a new secret:
   - Name: `RESEND_API_KEY`
   - Value: Your Resend API key (get one free at https://resend.com)

### 3. Test Through the Admin Panel
1. Log into your app as an admin
2. Go to the Admin Dashboard
3. Navigate to "Users" section
4. Click "Add User" button
5. Fill in:
   - Email: Use a real email you can check
   - Password: Any password (they'll need to change it)
   - Full Name: Test User
   - Role: User
6. Click "Create User"
7. Check the email inbox - you should receive a welcome email!

### 4. What the Email Looks Like
The welcome email includes:
- Professional Dugout Boss branding
- User's name and email
- Temporary password (highlighted)
- Login button
- Getting started instructions
- Support contact info

### 5. Troubleshooting

**Email not received?**
1. Check spam folder
2. Check Supabase logs:
   ```bash
   supabase functions logs send-welcome-email
   ```
3. Verify RESEND_API_KEY is set correctly

**Getting errors?**
Check the browser console and Supabase logs for error messages.

## The Email Template

The email template is built into the edge function at:
`/supabase/functions/send-welcome-email/index.ts`

Look for the `generateWelcomeEmailHtml` function to see/modify the email design.
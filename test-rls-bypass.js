import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase credentials in .env.local');
  process.exit(1);
}

async function testRLS(email) {
  console.log(`\n🔍 Testing RLS for: ${email}\n`);

  try {
    // Test with anon key (respects RLS)
    console.log('1. Testing with anon key (RLS enabled)...');
    const anonClient = createClient(supabaseUrl, supabaseAnonKey);
    
    const { data: profile } = await anonClient
      .from('profiles')
      .select('id, email')
      .eq('email', email)
      .single();

    if (!profile) {
      console.log('❌ User not found');
      return;
    }

    console.log(`✅ Found user: ${profile.id}`);

    // Check subscription with RLS
    const { data: subWithRLS, error: rlsError } = await anonClient
      .from('subscriptions')
      .select('*')
      .eq('user_id', profile.id);

    if (rlsError) {
      console.log('❌ RLS query failed:', rlsError.message);
    } else {
      console.log(`✅ RLS query succeeded: Found ${subWithRLS?.length || 0} subscriptions`);
    }

    // Test with service key (bypasses RLS) if available
    if (supabaseServiceKey) {
      console.log('\n2. Testing with service key (RLS bypassed)...');
      const serviceClient = createClient(supabaseUrl, supabaseServiceKey);
      
      const { data: subNoRLS, error: serviceError } = await serviceClient
        .from('subscriptions')
        .select('*')
        .eq('user_id', profile.id);

      if (serviceError) {
        console.log('❌ Service query failed:', serviceError.message);
      } else {
        console.log(`✅ Service query succeeded: Found ${subNoRLS?.length || 0} subscriptions`);
        
        if (subNoRLS && subNoRLS.length > 0) {
          subNoRLS.forEach((sub, i) => {
            console.log(`\n   Subscription ${i + 1}:`);
            console.log(`   - ID: ${sub.id}`);
            console.log(`   - Is Paid: ${sub.is_paid}`);
            console.log(`   - Tier: ${sub.tier}`);
            console.log(`   - Team Limit: ${sub.team_limit}`);
          });
        }
      }

      // Compare results
      if (subWithRLS?.length !== subNoRLS?.length) {
        console.log('\n⚠️  RLS is blocking access to some subscriptions!');
        console.log(`   With RLS: ${subWithRLS?.length || 0} subscriptions`);
        console.log(`   Without RLS: ${subNoRLS?.length || 0} subscriptions`);
      }
    } else {
      console.log('\n⚠️  No service key found - cannot test RLS bypass');
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Get email from command line
const email = process.argv[2];
if (!email) {
  console.log('Usage: node test-rls-bypass.js <email>');
  process.exit(1);
}

testRLS(email);
-- CRITICAL: Fix authentication and security issues
-- 1. Fix admin-created users being redirected to pricing
-- 2. Fix users seeing other users' teams (SECURITY BREACH)

\echo '\n=== CRITICAL SECURITY FIX - Authentication Issues ===\n'

-- First, check for the security breach - users accessing wrong teams
\echo '\n=== Checking for Data Isolation Breaches ===\n'
WITH team_access_audit AS (
    SELECT 
        t.id as team_id,
        t.user_id as team_owner,
        t.name as team_name,
        p.id as accessing_user,
        p.email as accessing_email,
        CASE WHEN t.user_id != p.id THEN 'BREACH' ELSE 'OK' END as access_status
    FROM teams t
    CROSS JOIN profiles p
    WHERE EXISTS (
        SELECT 1 FROM lineups l 
        WHERE l.team_id = t.id 
        AND l.created_at > NOW() - INTERVAL '7 days'
    )
)
SELECT * FROM team_access_audit WHERE access_status = 'BREACH' LIMIT 10;

-- Check incomplete subscriptions that are causing the redirect issue
\echo '\n=== Checking Incomplete Paid Subscriptions ===\n'
SELECT 
    s.user_id,
    p.email,
    s.is_paid,
    s.tier,
    s.team_limit,
    s.created_at,
    s.updated_at
FROM subscriptions s
JOIN profiles p ON p.id = s.user_id
WHERE s.is_paid = true 
AND (s.tier IS NULL OR s.team_limit IS NULL)
ORDER BY s.created_at DESC;

-- Fix 1: Update all incomplete paid subscriptions
\echo '\n=== Fixing Incomplete Paid Subscriptions ===\n'
UPDATE subscriptions
SET 
    tier = COALESCE(tier, 'pro'),
    team_limit = COALESCE(team_limit, 10),
    updated_at = NOW()
WHERE is_paid = true 
AND (tier IS NULL OR team_limit IS NULL);

-- Fix 2: Drop ALL existing RLS policies and recreate with unique names
\echo '\n=== Rebuilding RLS Policies with Strict Security ===\n'

-- Drop all existing policies to avoid conflicts
DO $$ 
DECLARE
    pol RECORD;
BEGIN
    -- Drop all policies on critical tables
    FOR pol IN 
        SELECT policyname, tablename 
        FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename IN ('teams', 'players', 'lineups', 'subscriptions', 'profiles')
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON %I', pol.policyname, pol.tablename);
    END LOOP;
END $$;

-- Enable RLS on all tables
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE players ENABLE ROW LEVEL SECURITY;
ALTER TABLE lineups ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create strict RLS policies with unique names
DO $$ 
BEGIN
    -- Teams: STRICT user isolation
    CREATE POLICY "teams_select_own_v2" ON teams
        FOR SELECT USING (auth.uid() = user_id);
    
    CREATE POLICY "teams_insert_own_v2" ON teams
        FOR INSERT WITH CHECK (auth.uid() = user_id);
    
    CREATE POLICY "teams_update_own_v2" ON teams
        FOR UPDATE USING (auth.uid() = user_id);
    
    CREATE POLICY "teams_delete_own_v2" ON teams
        FOR DELETE USING (auth.uid() = user_id);

    -- Players: Can only access players from own teams
    CREATE POLICY "players_select_own_teams_v2" ON players
        FOR SELECT USING (
            EXISTS (
                SELECT 1 FROM teams t 
                WHERE t.id = players.team_id 
                AND t.user_id = auth.uid()
            )
        );
    
    CREATE POLICY "players_insert_own_teams_v2" ON players
        FOR INSERT WITH CHECK (
            EXISTS (
                SELECT 1 FROM teams t 
                WHERE t.id = players.team_id 
                AND t.user_id = auth.uid()
            )
        );
    
    CREATE POLICY "players_update_own_teams_v2" ON players
        FOR UPDATE USING (
            EXISTS (
                SELECT 1 FROM teams t 
                WHERE t.id = players.team_id 
                AND t.user_id = auth.uid()
            )
        );
    
    CREATE POLICY "players_delete_own_teams_v2" ON players
        FOR DELETE USING (
            EXISTS (
                SELECT 1 FROM teams t 
                WHERE t.id = players.team_id 
                AND t.user_id = auth.uid()
            )
        );

    -- Lineups: Can only access lineups from own teams
    CREATE POLICY "lineups_select_own_teams_v2" ON lineups
        FOR SELECT USING (
            EXISTS (
                SELECT 1 FROM teams t 
                WHERE t.id = lineups.team_id 
                AND t.user_id = auth.uid()
            )
        );
    
    CREATE POLICY "lineups_insert_own_teams_v2" ON lineups
        FOR INSERT WITH CHECK (
            EXISTS (
                SELECT 1 FROM teams t 
                WHERE t.id = lineups.team_id 
                AND t.user_id = auth.uid()
            )
        );
    
    CREATE POLICY "lineups_update_own_teams_v2" ON lineups
        FOR UPDATE USING (
            EXISTS (
                SELECT 1 FROM teams t 
                WHERE t.id = lineups.team_id 
                AND t.user_id = auth.uid()
            )
        );
    
    CREATE POLICY "lineups_delete_own_teams_v2" ON lineups
        FOR DELETE USING (
            EXISTS (
                SELECT 1 FROM teams t 
                WHERE t.id = lineups.team_id 
                AND t.user_id = auth.uid()
            )
        );

    -- Subscriptions: STRICT user isolation
    CREATE POLICY "subscriptions_select_own_v2" ON subscriptions
        FOR SELECT USING (auth.uid() = user_id);
    
    CREATE POLICY "subscriptions_insert_own_v2" ON subscriptions
        FOR INSERT WITH CHECK (auth.uid() = user_id);
    
    CREATE POLICY "subscriptions_update_own_v2" ON subscriptions
        FOR UPDATE USING (auth.uid() = user_id);

    -- Profiles: Can view and update own profile only
    CREATE POLICY "profiles_select_own_v2" ON profiles
        FOR SELECT USING (auth.uid() = id);
    
    CREATE POLICY "profiles_insert_own_v2" ON profiles
        FOR INSERT WITH CHECK (auth.uid() = id);
    
    CREATE POLICY "profiles_update_own_v2" ON profiles
        FOR UPDATE USING (auth.uid() = id);

    RAISE NOTICE 'Strict RLS policies created successfully';
END $$;

-- Create indexes for performance and security
\echo '\n=== Creating Security Indexes ===\n'
CREATE INDEX IF NOT EXISTS idx_teams_user_id ON teams(user_id);
CREATE INDEX IF NOT EXISTS idx_players_team_id ON players(team_id);
CREATE INDEX IF NOT EXISTS idx_lineups_team_id ON lineups(team_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);

-- Verify the fix
\echo '\n=== Verification: Fixed Subscriptions ===\n'
SELECT 
    COUNT(*) as total_paid_users,
    COUNT(CASE WHEN tier IS NOT NULL AND team_limit IS NOT NULL THEN 1 END) as complete_subscriptions,
    COUNT(CASE WHEN tier IS NULL OR team_limit IS NULL THEN 1 END) as incomplete_subscriptions
FROM subscriptions
WHERE is_paid = true;

-- Verify RLS policies
\echo '\n=== Verification: New RLS Policies ===\n'
SELECT 
    tablename,
    policyname,
    cmd,
    roles
FROM pg_policies
WHERE schemaname = 'public' 
AND tablename IN ('teams', 'players', 'lineups', 'subscriptions', 'profiles')
ORDER BY tablename, policyname;

-- Create a function to audit team access
\echo '\n=== Creating Security Audit Function ===\n'
CREATE OR REPLACE FUNCTION audit_team_access(check_user_id UUID)
RETURNS TABLE (
    team_id UUID,
    team_name TEXT,
    team_owner UUID,
    can_access BOOLEAN,
    reason TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.id,
        t.name,
        t.user_id,
        t.user_id = check_user_id,
        CASE 
            WHEN t.user_id = check_user_id THEN 'Owner'
            ELSE 'No Access - Different Owner'
        END
    FROM teams t
    ORDER BY t.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

\echo '\n=== CRITICAL FIXES COMPLETE ===\n'
\echo 'Actions taken:'
\echo '1. Fixed all incomplete paid subscriptions'
\echo '2. Rebuilt RLS policies with strict user isolation'
\echo '3. Created security indexes for performance'
\echo '4. Created audit function for team access verification'
\echo '\nNOTE: Users should sign out and back in for changes to take effect'
#!/bin/bash
# Edge Functions Deployment Script
# Run this after Docker Desktop is started

PROJECT_REF="mhuuptkgohuztjrovpxz"

echo "Deploying Edge Functions to Supabase..."

functions=("create-payment" "stripe-webhook" "send-contact-email" "verify-payment" "admin-create-user")

for func in "${functions[@]}"
do
    echo "Deploying $func..."
    supabase functions deploy "$func" --project-ref "$PROJECT_REF"
    if [ $? -eq 0 ]; then
        echo "✓ $func deployed successfully"
    else
        echo "✗ Failed to deploy $func"
    fi
done

echo "All functions deployed!"

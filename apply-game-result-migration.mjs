#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials in environment variables');
  console.error('Please ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function applyGameResultMigration() {
  console.log('🚀 Applying game_result column migration...\n');
  
  try {
    // First check if the column already exists
    console.log('🔍 Checking if game_result column already exists...');
    const { data: testData, error: testError } = await supabase
      .from('lineups')
      .select('id, name, game_result')
      .limit(1);
    
    if (testError && testError.message.includes('column lineups.game_result does not exist')) {
      console.log('❌ Column does not exist, proceeding with migration...\n');
      
      // Since we can't execute arbitrary SQL through the API, we need to use the Supabase dashboard
      console.log('📝 Please run the following SQL in your Supabase dashboard:\n');
      console.log(`-- Add game_result column to lineups table for win-loss tracking
ALTER TABLE lineups 
ADD COLUMN IF NOT EXISTS game_result TEXT CHECK (game_result IN ('win', 'loss', null));

-- Add comment to document the column
COMMENT ON COLUMN lineups.game_result IS 'Game outcome: win, loss, or null for not set';

-- Add index for performance when querying by game result
CREATE INDEX IF NOT EXISTS idx_lineups_game_result ON lineups(game_result);`);
      
      console.log('\n📋 To apply this migration:');
      console.log('1. Go to your Supabase dashboard: https://app.supabase.com');
      console.log('2. Navigate to SQL Editor');
      console.log('3. Paste and run the SQL above');
      console.log('4. After running, execute this script again to verify\n');
      
      console.log('💡 After the migration is applied, the app will automatically detect it.');
      return;
    } else if (testError) {
      console.error('❌ Unexpected error:', testError);
      return;
    }
    
    console.log('✅ game_result column already exists!');
    
    // Update some sample data to test
    console.log('\n🧪 Testing by querying existing lineups...');
    const { data: lineups, error: queryError } = await supabase
      .from('lineups')
      .select('id, name, game_result')
      .limit(5);
    
    if (queryError) {
      console.error('❌ Error querying lineups:', queryError);
      return;
    }
    
    console.log(`\n📊 Found ${lineups.length} lineups:`);
    lineups.forEach(lineup => {
      const result = lineup.game_result || 'not set';
      console.log(`   - ${lineup.name}: ${result}`);
    });
    
    console.log('\n🎉 Migration verified! Game result tracking is now enabled.');
    console.log('💡 The Win/Loss toggle will now appear and persist data correctly.');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Run the migration check
applyGameResultMigration();
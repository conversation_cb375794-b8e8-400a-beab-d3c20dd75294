-- Check subscription table performance and indexes
-- This script helps diagnose slow subscription queries

-- Check table size and row count
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size,
    n_live_tup AS row_count
FROM pg_stat_user_tables
WHERE tablename = 'subscriptions';

-- Check existing indexes on subscriptions table
SELECT 
    indexname,
    indexdef
FROM pg_indexes
WHERE tablename = 'subscriptions'
ORDER BY indexname;

-- Check for missing indexes that could help with payment checks
-- This query is used frequently in AuthContext
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * 
FROM subscriptions
WHERE user_id = '00000000-0000-0000-0000-000000000000'::uuid
AND is_paid = true
ORDER BY created_at DESC;

-- Check for slow queries on subscriptions table
SELECT 
    query,
    calls,
    total_exec_time,
    mean_exec_time,
    stddev_exec_time,
    max_exec_time
FROM pg_stat_statements
WHERE query LIKE '%subscriptions%'
AND mean_exec_time > 100  -- queries taking more than 100ms on average
ORDER BY mean_exec_time DESC
LIMIT 10;

-- Check RLS policies on subscriptions table
SELECT 
    policyname,
    cmd,
    qual,
    with_check
FROM pg_policies
WHERE tablename = 'subscriptions';

-- Suggest creating composite index if not exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname = 'idx_subscriptions_user_paid_created'
    ) THEN
        RAISE NOTICE 'Consider creating this index for better performance:';
        RAISE NOTICE 'CREATE INDEX idx_subscriptions_user_paid_created ON subscriptions(user_id, is_paid, created_at DESC);';
    END IF;
END $$;
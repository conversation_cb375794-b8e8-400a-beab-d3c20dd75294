# Email Setup Guide for Cloudflare Hosting

This guide will help you set up email functionality for Dugout Boss when hosting with Cloudflare.

## Overview

Your app has two types of email functionality:
1. **Password Reset Emails** - Already working via Supabase Auth
2. **Contact Form Emails** - Need to be configured

## Step 1: Set Up Cloudflare Email Routing (Receive Emails)

1. Log in to your Cloudflare dashboard
2. Select your domain (dugoutboss.com)
3. Go to **Email** → **Email Routing**
4. Click **Get started**
5. Add destination addresses:
   - Add your personal email as the destination
6. Create routing rules:
   - `<EMAIL>` → your personal email
   - Any other addresses you want to receive mail at

This allows you to receive emails at your domain without paying for email hosting.

## Step 2: Set Up Resend (Send Emails)

1. Sign up at [resend.com](https://resend.com)
   - Free tier: 100 emails/day, 3,000 emails/month
   - No credit card required

2. Add your domain:
   - Click **Domains** → **Add Domain**
   - Enter `dugoutboss.com`
   - Add the DNS records shown to your Cloudflare DNS:
     - SPF record
     - DKIM records
   - Wait for verification (usually instant with Cloudflare)

3. Get your API key:
   - Go to **API Keys**
   - Create a new API key
   - Copy the key (starts with `re_`)

## Step 3: Configure Supabase Edge Function

1. Add the Resend API key to Supabase:
   ```bash
   # Set the secret in Supabase
   supabase secrets set RESEND_API_KEY=re_YOUR_API_KEY_HERE
   ```

2. Deploy the edge function:
   ```bash
   supabase functions deploy send-contact-email
   ```

## Step 4: Update Email Addresses

If you want to use a different "from" address, update the edge function:
- Change `<EMAIL>` to your preferred sender address
- Make sure the domain is verified in Resend

## Alternative Options

### Option 2: Cloudflare Email Workers
- Use Cloudflare Workers to send emails directly
- More complex but keeps everything in Cloudflare
- Requires MailChannels integration

### Option 3: SendGrid
- More expensive but very reliable
- Better for high volume
- Similar setup process to Resend

## Testing

1. Test the contact form on your site
2. Check Resend dashboard for sent emails
3. Verify you receive <NAME_EMAIL>

## Troubleshooting

### Email not sending
- Check Supabase logs: `supabase functions logs send-contact-email`
- Verify RESEND_API_KEY is set: `supabase secrets list`
- Check Resend dashboard for API activity

### DNS issues
- Make sure all Resend DNS records are added to Cloudflare
- Disable Cloudflare proxy (orange cloud) for email-related records
- Wait 10-15 minutes for DNS propagation

### Rate limits
- Free Resend tier: 100 emails/day
- Consider upgrading if you need more
- Implement rate limiting in your app to prevent abuse

## Production Checklist

- [ ] Cloudflare Email Routing configured
- [ ] Resend account created and domain verified
- [ ] RESEND_API_KEY added to Supabase secrets
- [ ] Edge function deployed
- [ ] Contact form tested and working
- [ ] Email addresses updated in code
- [ ] SPF/DKIM records added to DNS
- [ ] Consider email templates for better formatting

## Security Notes

- Never commit API keys to git
- Use environment variables/secrets for all keys
- Consider adding CAPTCHA to contact form to prevent spam
- Monitor Resend usage to detect abuse
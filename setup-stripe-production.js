#!/usr/bin/env node

import { execSync } from 'child_process';
import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join } from 'path';
import { createInterface } from 'readline';
import { promisify } from 'util';

const rl = createInterface({
  input: process.stdin,
  output: process.stdout,
});

const question = promisify(rl.question).bind(rl);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, type = 'info') {
  const prefix = {
    info: `${colors.blue}ℹ${colors.reset}`,
    success: `${colors.green}✓${colors.reset}`,
    warning: `${colors.yellow}⚠${colors.reset}`,
    error: `${colors.red}✗${colors.reset}`,
    step: `${colors.magenta}▶${colors.reset}`,
  }[type] || '';
  
  console.log(`${prefix} ${message}`);
}

function header(title) {
  console.log('\n' + colors.bright + colors.cyan + '═'.repeat(60) + colors.reset);
  console.log(colors.bright + colors.cyan + `  ${title}` + colors.reset);
  console.log(colors.bright + colors.cyan + '═'.repeat(60) + colors.reset + '\n');
}

async function checkPrerequisites() {
  header('Checking Prerequisites');
  
  let allGood = true;
  
  // Check for required files
  const requiredFiles = [
    'supabase/functions/_shared/cors.ts',
    'deploy-edge-functions.sh',
    '.env.local',
  ];
  
  for (const file of requiredFiles) {
    if (existsSync(file)) {
      log(`Found ${file}`, 'success');
    } else {
      log(`Missing ${file}`, 'error');
      allGood = false;
    }
  }
  
  // Check for test keys in code
  log('\nScanning for test Stripe keys in codebase...', 'info');
  try {
    const testKeyPatterns = ['pk_test_', 'sk_test_', 'whsec_test_'];
    let foundTestKeys = false;
    
    // Use grep to find test keys
    for (const pattern of testKeyPatterns) {
      try {
        execSync(`grep -r "${pattern}" src/ 2>/dev/null`, { encoding: 'utf-8' });
        log(`Found test key pattern: ${pattern}`, 'warning');
        foundTestKeys = true;
      } catch (e) {
        // No matches found (grep returns non-zero when no matches)
      }
    }
    
    if (!foundTestKeys) {
      log('No test keys found in source code', 'success');
    } else {
      allGood = false;
    }
  } catch (error) {
    log('Error scanning for test keys', 'warning');
  }
  
  return allGood;
}

async function updateCORSConfiguration() {
  header('Updating CORS Configuration');
  
  const corsFile = 'supabase/functions/_shared/cors.ts';
  
  try {
    let content = readFileSync(corsFile, 'utf-8');
    
    // Check current allowed origins
    const originMatch = content.match(/allowedOrigins\s*=\s*\[([\s\S]*?)\]/);
    if (originMatch) {
      log('Current allowed origins:', 'info');
      console.log(originMatch[1].trim());
    }
    
    const productionDomain = await question('\nEnter your production domain (e.g., dugoutboss.com): ');
    
    // Create production CORS configuration
    const productionOrigins = [
      `'https://${productionDomain}'`,
      `'https://www.${productionDomain}'`,
    ];
    
    // Replace the allowedOrigins array
    content = content.replace(
      /allowedOrigins\s*=\s*\[[\s\S]*?\]/,
      `allowedOrigins = [\n  ${productionOrigins.join(',\n  ')}\n]`
    );
    
    // Show the changes
    log('\nUpdated CORS configuration:', 'info');
    console.log(content.match(/allowedOrigins\s*=\s*\[([\s\S]*?)\]/)[1].trim());
    
    const confirm = await question('\nSave these changes? (yes/no): ');
    if (confirm.toLowerCase() === 'yes') {
      writeFileSync(corsFile, content);
      log('CORS configuration updated', 'success');
    } else {
      log('CORS update cancelled', 'warning');
    }
  } catch (error) {
    log(`Error updating CORS: ${error.message}`, 'error');
  }
}

async function validateEnvironmentVariables() {
  header('Environment Variable Validation');
  
  const requiredVars = {
    'VITE_STRIPE_PUBLISHABLE_KEY': {
      pattern: /^pk_live_/,
      description: 'Stripe live publishable key',
    },
    'VITE_SUPABASE_URL': {
      pattern: /^https:\/\/.+\.supabase\.co$/,
      description: 'Supabase project URL',
    },
    'VITE_SUPABASE_ANON_KEY': {
      pattern: /^[A-Za-z0-9+/=.]+$/,
      description: 'Supabase anonymous key',
    },
  };
  
  log('Checking .env.local file...', 'info');
  
  try {
    const envContent = readFileSync('.env.local', 'utf-8');
    const envVars = {};
    
    // Parse env file
    envContent.split('\n').forEach(line => {
      if (line && !line.startsWith('#')) {
        const [key, ...valueParts] = line.split('=');
        if (key) {
          envVars[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
    
    let allValid = true;
    
    for (const [varName, config] of Object.entries(requiredVars)) {
      const value = envVars[varName];
      
      if (!value) {
        log(`Missing: ${varName} - ${config.description}`, 'error');
        allValid = false;
      } else if (!config.pattern.test(value)) {
        log(`Invalid: ${varName} - Should match pattern ${config.pattern}`, 'error');
        allValid = false;
      } else {
        log(`Valid: ${varName}`, 'success');
      }
    }
    
    if (!allValid) {
      log('\nPlease update your .env.local file with the correct values', 'warning');
    }
    
    return allValid;
  } catch (error) {
    log('Could not read .env.local file', 'error');
    return false;
  }
}

async function generateManualSteps() {
  header('Manual Steps Required');
  
  const steps = [
    {
      title: 'Stripe Dashboard Configuration',
      tasks: [
        'Go to dashboard.stripe.com',
        'Toggle from "Test mode" to "Live mode"',
        'Navigate to Developers > API keys',
        'Copy your live publishable key (pk_live_...)',
        'Copy your live secret key (sk_live_...)',
      ],
    },
    {
      title: 'Webhook Configuration',
      tasks: [
        'Go to Developers > Webhooks',
        'Click "Add endpoint"',
        'Enter your endpoint URL: https://YOUR_DOMAIN/stripe-webhook',
        'Select events: checkout.session.completed, customer.subscription.updated',
        'Copy the signing secret (whsec_...)',
      ],
    },
    {
      title: 'Supabase Configuration',
      tasks: [
        'Go to your Supabase project settings',
        'Navigate to Edge Functions > Secrets',
        'Add STRIPE_SECRET_KEY with your live secret key',
        'Add STRIPE_WEBHOOK_SECRET with your webhook signing secret',
      ],
    },
    {
      title: 'Frontend Deployment Platform',
      tasks: [
        'Update VITE_STRIPE_PUBLISHABLE_KEY in your deployment platform',
        'Trigger a new deployment to apply changes',
      ],
    },
  ];
  
  for (const section of steps) {
    console.log(`\n${colors.bright}${section.title}:${colors.reset}`);
    section.tasks.forEach((task, index) => {
      console.log(`  ${index + 1}. ${task}`);
    });
  }
  
  console.log('\n' + colors.yellow + 'Keep this checklist handy as you complete each step!' + colors.reset);
}

async function deployEdgeFunctions() {
  header('Edge Functions Deployment');
  
  const deploy = await question('Deploy edge functions now? (yes/no): ');
  
  if (deploy.toLowerCase() === 'yes') {
    log('Deploying edge functions...', 'info');
    
    try {
      // Make the script executable
      execSync('chmod +x deploy-edge-functions.sh');
      
      // Run the deployment script
      execSync('./deploy-edge-functions.sh', { stdio: 'inherit' });
      
      log('Edge functions deployed successfully', 'success');
    } catch (error) {
      log('Edge function deployment failed', 'error');
      console.error(error.message);
    }
  } else {
    log('Skipping edge function deployment', 'info');
    log('Run ./deploy-edge-functions.sh when ready', 'info');
  }
}

async function generateTestingChecklist() {
  header('Post-Deployment Testing Checklist');
  
  const checklist = `
## Testing Checklist

### 1. Basic Payment Flow
- [ ] Navigate to pricing page
- [ ] Click "Get Started" on a plan
- [ ] Complete checkout with real card
- [ ] Verify redirect to success page
- [ ] Check subscription status in database

### 2. Webhook Verification
- [ ] Check Supabase logs for webhook execution
- [ ] Verify subscription record created
- [ ] Confirm user upgraded to paid status

### 3. Cross-Origin Testing
- [ ] Test from production domain
- [ ] Verify no CORS errors in console
- [ ] Check network tab for failed requests

### 4. Error Handling
- [ ] Test with invalid card
- [ ] Test canceling payment
- [ ] Verify proper error messages

### 5. Database Verification
\`\`\`sql
-- Run in Supabase SQL editor
SELECT u.email, s.status, s.price_id, s.current_period_end
FROM auth.users u
JOIN subscriptions s ON u.id = s.user_id
WHERE s.status = 'active'
ORDER BY s.created_at DESC
LIMIT 10;
\`\`\`
`;
  
  const saveChecklist = await question('Save testing checklist to file? (yes/no): ');
  
  if (saveChecklist.toLowerCase() === 'yes') {
    writeFileSync('STRIPE_TESTING_CHECKLIST.md', checklist);
    log('Testing checklist saved to STRIPE_TESTING_CHECKLIST.md', 'success');
  }
  
  console.log(checklist);
}

async function main() {
  console.clear();
  console.log(colors.bright + colors.magenta);
  console.log('╔═══════════════════════════════════════════════════════════╗');
  console.log('║           Stripe Production Setup Assistant               ║');
  console.log('║                   for Dugout Boss                         ║');
  console.log('╚═══════════════════════════════════════════════════════════╝');
  console.log(colors.reset);
  
  try {
    // Step 1: Check prerequisites
    const prereqsPassed = await checkPrerequisites();
    if (!prereqsPassed) {
      log('\nPlease fix the issues above before proceeding', 'error');
      const proceed = await question('Continue anyway? (yes/no): ');
      if (proceed.toLowerCase() !== 'yes') {
        process.exit(1);
      }
    }
    
    // Step 2: Update CORS
    await updateCORSConfiguration();
    
    // Step 3: Validate environment variables
    await validateEnvironmentVariables();
    
    // Step 4: Generate manual steps
    await generateManualSteps();
    
    // Step 5: Deploy edge functions (optional)
    await deployEdgeFunctions();
    
    // Step 6: Generate testing checklist
    await generateTestingChecklist();
    
    // Final summary
    header('Setup Complete!');
    log('Automated steps completed', 'success');
    log('Please complete the manual steps listed above', 'warning');
    log('Use the testing checklist to verify everything works', 'info');
    
  } catch (error) {
    log(`Setup failed: ${error.message}`, 'error');
  } finally {
    rl.close();
  }
}

// Run the setup
main();
# URGENT: Supabase Project Change Detected

## Issue
The Supabase project has been changed in `.env`:
- **OLD**: `https://mqonlcvgzlhztrpubesd.supabase.co`
- **NEW**: `https://mhuuptkgohuztjrovpxz.supabase.co`

This is causing authentication to fail with CORS errors because `dugoutboss.com` is not configured in the new project's authentication settings.

## Solution Options

### Option 1: Configure the New Supabase Project (Recommended if intentional)

1. **Go to the NEW Supabase Dashboard**: https://app.supabase.com
2. Select the project with ID `mhuuptkgohuztjrovpxz`
3. Navigate to **Authentication** → **URL Configuration**
4. Add these settings:

   **Site URL:**
   ```
   https://dugoutboss.com
   ```

   **Redirect URLs:**
   ```
   https://dugoutboss.com
   https://dugoutboss.com/*
   https://www.dugoutboss.com
   https://www.dugoutboss.com/*
   http://localhost:5173
   http://localhost:5173/*
   ```

5. Save the changes

### Option 2: Revert to Original Project (If this was accidental)

If this project change was accidental, revert the `.env` file:

```env
VITE_SUPABASE_URL=https://mqonlcvgzlhztrpubesd.supabase.co
VITE_SUPABASE_ANON_KEY=[original anon key]
```

## Important Considerations

1. **Data Migration**: If switching projects intentionally, ensure all data has been migrated
2. **Edge Functions**: Any deployed edge functions need to be redeployed to the new project
3. **Database Schema**: Ensure the new project has all required tables and migrations
4. **RLS Policies**: Verify Row Level Security policies are configured
5. **Stripe Webhooks**: Update Stripe webhook endpoint if using the new project

## Verification Steps

After fixing:
1. Clear browser cache and cookies
2. Try logging in from https://dugoutboss.com
3. Check browser console for any remaining errors
4. Verify all features work (team creation, lineup generation, etc.)

## Production Deployment

If using the new project in production:
1. Update any CI/CD environment variables
2. Update Cloudflare/Netlify environment variables
3. Redeploy the application
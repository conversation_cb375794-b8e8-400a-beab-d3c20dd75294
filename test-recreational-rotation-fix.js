#!/usr/bin/env node

/**
 * Test to verify that primary players rotate in recreational mode
 */

const { generateSingleGameLineupStrict } = require('./src/lib/single-game-lineup-strict.ts');

// Create test players with some marked as primary
const testPlayers = [
  {
    id: 'p1',
    name: '<PERSON>',
    teamRoles: {
      pitcher: 'go-to',     // Primary pitcher
      firstBase: 'capable'
    }
  },
  {
    id: 'p2',
    name: '<PERSON>',
    teamRoles: {
      catcher: 'go-to',     // Primary catcher
      thirdBase: 'capable'
    }
  },
  {
    id: 'p3',
    name: '<PERSON>',
    teamRoles: {
      shortstop: 'go-to',   // Primary shortstop
      secondBase: 'capable'
    }
  },
  {
    id: 'p4',
    name: '<PERSON>',
    teamRoles: {
      firstBase: 'capable',
      leftField: 'capable'
    }
  },
  {
    id: 'p5',
    name: '<PERSON>',
    teamRoles: {
      secondBase: 'capable',
      centerField: 'capable'
    }
  },
  {
    id: 'p6',
    name: '<PERSON>',
    teamRoles: {
      thirdBase: 'capable',
      rightField: 'capable'
    }
  },
  {
    id: 'p7',
    name: '<PERSON>',
    teamRoles: {
      leftField: 'capable',
      pitcher: 'capable'
    }
  },
  {
    id: 'p8',
    name: '<PERSON>',
    teamRoles: {
      centerField: 'capable',
      catcher: 'capable'
    }
  },
  {
    id: 'p9',
    name: '<PERSON>',
    teamRoles: {
      rightField: 'capable',
      shortstop: 'capable'
    }
  },
  {
    id: 'p10',
    name: 'Jack',
    teamRoles: {
      pitcher: 'capable',
      firstBase: 'capable',
      outfield: 'capable'
    }
  }
];

// Test recreational mode (should rotate primary players)
console.log('🏃 Testing RECREATIONAL mode rotation (primary players should rotate):\n');
const recreationalRules = {
  competitiveMode: false,
  rotateLineupEvery: 1,      // Rotate every inning
  rotatePitcherEvery: 2,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  respectPositionLockouts: true
};

try {
  const recreationalLineup = generateSingleGameLineupStrict(testPlayers, 6, recreationalRules);
  
  // Track where primary players play
  const primaryPlayerPositions = {
    'Alice': [],    // Primary pitcher
    'Bob': [],      // Primary catcher
    'Charlie': []   // Primary shortstop
  };
  
  recreationalLineup.forEach((inning, idx) => {
    console.log(`Inning ${inning.inning}:`);
    Object.entries(inning.positions).forEach(([pos, playerName]) => {
      if (pos !== 'bench' && primaryPlayerPositions[playerName] !== undefined) {
        primaryPlayerPositions[playerName].push(pos);
        console.log(`  ${playerName} (PRIMARY) → ${pos}`);
      }
    });
  });
  
  console.log('\n📊 Primary player rotation summary:');
  Object.entries(primaryPlayerPositions).forEach(([player, positions]) => {
    const uniquePositions = [...new Set(positions)];
    console.log(`  ${player}: played ${uniquePositions.length} different positions - ${uniquePositions.join(', ')}`);
    if (uniquePositions.length === 1) {
      console.log(`    ⚠️  WARNING: Player stayed in same position all game!`);
    }
  });
  
} catch (error) {
  console.error('❌ Recreational mode test failed:', error.message);
}

// Test competitive mode (primary players should stay in position)
console.log('\n\n🏆 Testing COMPETITIVE mode (primary players should stay in positions):\n');
const competitiveRules = {
  competitiveMode: true,
  rotateLineupEvery: 3,      // Less frequent rotation
  rotatePitcherEvery: 3,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  respectPositionLockouts: true
};

try {
  const competitiveLineup = generateSingleGameLineupStrict(testPlayers, 6, competitiveRules);
  
  // Track where primary players play
  const primaryPlayerPositions = {
    'Alice': [],    // Primary pitcher
    'Bob': [],      // Primary catcher
    'Charlie': []   // Primary shortstop
  };
  
  competitiveLineup.forEach((inning, idx) => {
    console.log(`Inning ${inning.inning}:`);
    Object.entries(inning.positions).forEach(([pos, playerName]) => {
      if (pos !== 'bench' && primaryPlayerPositions[playerName] !== undefined) {
        primaryPlayerPositions[playerName].push(pos);
        console.log(`  ${playerName} (PRIMARY) → ${pos}`);
      }
    });
  });
  
  console.log('\n📊 Primary player position summary:');
  Object.entries(primaryPlayerPositions).forEach(([player, positions]) => {
    const uniquePositions = [...new Set(positions)];
    console.log(`  ${player}: played ${uniquePositions.length} different positions - ${uniquePositions.join(', ')}`);
  });
  
} catch (error) {
  console.error('❌ Competitive mode test failed:', error.message);
}
-- FIX: Player Insert RLS Policy Issue
-- The current policy checks auth.uid() = user_id during INSERT, but user_id doesn't exist yet
-- This causes all player inserts to fail for non-admin users

BEGIN;

-- Drop the problematic insert policy
DROP POLICY IF EXISTS "players_insert_policy" ON players;

-- Create a corrected insert policy that properly checks team ownership
CREATE POLICY "players_insert_policy" ON players
    FOR INSERT WITH CHECK (
        -- Check if the user owns the team they're adding a player to
        EXISTS (
            SELECT 1 FROM teams t
            WHERE t.id = team_id  -- Use the team_id from the INSERT
            AND t.user_id = auth.uid()
        )
        OR
        -- Admin users can insert any player
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

-- Also fix the select policy to be consistent
DROP POLICY IF EXISTS "players_select_policy" ON players;

CREATE POLICY "players_select_policy" ON players
    FOR SELECT USING (
        -- Users can see players from their own teams
        EXISTS (
            SELECT 1 FROM teams t
            WHERE t.id = players.team_id
            AND t.user_id = auth.uid()
        )
        OR
        -- Admin users can see all players
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

-- Fix update policy too
DROP POLICY IF EXISTS "players_update_policy" ON players;

CREATE POLICY "players_update_policy" ON players
    FOR UPDATE USING (
        -- Users can update players from their own teams
        EXISTS (
            SELECT 1 FROM teams t
            WHERE t.id = players.team_id
            AND t.user_id = auth.uid()
        )
        OR
        -- Admin users can update any player
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

-- Fix delete policy
DROP POLICY IF EXISTS "players_delete_policy" ON players;

CREATE POLICY "players_delete_policy" ON players
    FOR DELETE USING (
        -- Users can delete players from their own teams
        EXISTS (
            SELECT 1 FROM teams t
            WHERE t.id = players.team_id
            AND t.user_id = auth.uid()
        )
        OR
        -- Admin users can delete any player
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

-- Test the fix: Check if a user can insert a player
-- This should return true for users who own teams
DO $$
DECLARE
    test_user_id UUID;
    test_team_id UUID;
    can_insert BOOLEAN;
BEGIN
    -- Get a test user and their team
    SELECT u.id, t.id INTO test_user_id, test_team_id
    FROM auth.users u
    JOIN teams t ON t.user_id = u.id
    WHERE u.email NOT IN ('<EMAIL>', '<EMAIL>')
    LIMIT 1;

    IF test_user_id IS NOT NULL AND test_team_id IS NOT NULL THEN
        -- Check if this user could insert a player
        SELECT EXISTS (
            SELECT 1 FROM teams t
            WHERE t.id = test_team_id
            AND t.user_id = test_user_id
        ) INTO can_insert;

        RAISE NOTICE 'Test user can insert players: %', can_insert;
    ELSE
        RAISE NOTICE 'No test user with teams found';
    END IF;
END $$;

COMMIT;
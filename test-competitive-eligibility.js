// Test file to examine canPlayPosition behavior in competitive mode
// when players have no teamRoles defined

// Import the canPlayPosition function from utils-enhanced.ts
// Since this is a standalone test, we'll simulate the function behavior

// Simulate the canPlayPosition function based on the source code
function canPlayPosition(player, position, respectLockouts = true, competitiveMode = false) {
  if (!player) {
    return true;
  }

  // Map position display names to teamRole keys
  const positionToRoleKey = {
    'Pitcher': 'pitcher',
    'Catcher': 'catcher',
    'First Base': 'firstBase',
    'Second Base': 'secondBase',
    'Shortstop': 'shortstop',
    'Third Base': 'thirdBase',
    'Left Field': 'leftField',
    'Center Field': 'centerField',
    'Right Field': 'rightField'
  };

  const roleKey = positionToRoleKey[position];
  
  // Check if player has the Utility Player designation
  if (player.teamRoles?.isUtilityPlayer) {
    const utilityPositions = ['firstBase', 'secondBase', 'thirdBase', 'shortstop', 'leftField', 'centerField', 'rightField'];
    
    if (roleKey === 'pitcher' || roleKey === 'catcher') {
      const role = player.teamRoles[roleKey];
      if (role && role !== 'avoid' && role !== 'never' && role !== 'unset') {
        console.log(`✅ Utility player ${player.name} can play ${position} (explicitly set as ${role})`);
        return true;
      }
      console.log(`🚫 Utility player ${player.name} cannot play ${position} (not explicitly assigned)`);
      return false;
    }
    
    if (roleKey && utilityPositions.includes(roleKey)) {
      const role = player.teamRoles[roleKey];
      if (role === 'avoid' || role === 'never') {
        console.log(`🚫 Utility player ${player.name} cannot play ${position} (explicitly marked Never)`);
        return false;
      }
      console.log(`✅ Utility player ${player.name} can play ${position} (utility field position)`);
      return true;
    }
  }

  // Check if player has the Outfield Specialist designation
  if (player.teamRoles?.isOutfieldSpecialist) {
    const outfieldPositions = ['leftField', 'centerField', 'rightField'];
    if (roleKey && outfieldPositions.includes(roleKey)) {
      const role = player.teamRoles[roleKey];
      if (role === 'avoid' || role === 'never') {
        console.log(`🚫 Outfield specialist ${player.name} cannot play ${position} (explicitly marked Never)`);
        return false;
      }
      console.log(`✅ Outfield specialist ${player.name} can play ${position} (outfield position)`);
      return true;
    }
  }

  // Standard role checking
  if (player.teamRoles) {
    const role = player.teamRoles[roleKey];
    
    if (roleKey && role === 'avoid') {
      console.log(`🚫 HARD RESTRICTION: ${player.name} cannot play ${position} (role: avoid/Never)`);
      return false;
    }
    
    if (role && role !== 'unset') {
      console.log(`✅ ${player.name} can play ${position} (role: ${role})`);
      return true;
    }
  }

  // CRITICAL: Handle competitive vs recreational mode differently
  if (!roleKey) {
    console.warn(`⚠️ No role key mapping for position ${position}`);
    return !competitiveMode;
  }
  
  // If we reach here, the position is not explicitly assigned
  if (competitiveMode) {
    // COMPETITIVE MODE: Unassigned positions are NOT allowed
    console.log(`🚫 COMPETITIVE: ${player.name} cannot play ${position} (not assigned)`);
    return false;
  } else {
    // RECREATIONAL MODE: Unassigned positions ARE allowed
    console.log(`✅ RECREATIONAL: ${player.name} can play ${position} (unmarked position - available for rotation)`);
    return true;
  }
}

// Test scenarios
console.log("=== Testing canPlayPosition in Competitive Mode ===\n");

// Test 1: Player with NO teamRoles at all
console.log("Test 1: Player with NO teamRoles property");
const player1 = {
  name: "John Doe",
  // No teamRoles property defined
};

console.log("Competitive mode:");
console.log("- First Base:", canPlayPosition(player1, "First Base", true, true));
console.log("- Pitcher:", canPlayPosition(player1, "Pitcher", true, true));
console.log("- Center Field:", canPlayPosition(player1, "Center Field", true, true));

console.log("\nRecreational mode:");
console.log("- First Base:", canPlayPosition(player1, "First Base", true, false));
console.log("- Pitcher:", canPlayPosition(player1, "Pitcher", true, false));
console.log("- Center Field:", canPlayPosition(player1, "Center Field", true, false));

// Test 2: Player with empty teamRoles object
console.log("\n\nTest 2: Player with EMPTY teamRoles object");
const player2 = {
  name: "Jane Smith",
  teamRoles: {} // Empty object, no positions assigned
};

console.log("Competitive mode:");
console.log("- First Base:", canPlayPosition(player2, "First Base", true, true));
console.log("- Pitcher:", canPlayPosition(player2, "Pitcher", true, true));
console.log("- Center Field:", canPlayPosition(player2, "Center Field", true, true));

console.log("\nRecreational mode:");
console.log("- First Base:", canPlayPosition(player2, "First Base", true, false));
console.log("- Pitcher:", canPlayPosition(player2, "Pitcher", true, false));
console.log("- Center Field:", canPlayPosition(player2, "Center Field", true, false));

// Test 3: Player with some positions assigned but not others
console.log("\n\nTest 3: Player with PARTIAL teamRoles assignments");
const player3 = {
  name: "Mike Johnson",
  teamRoles: {
    firstBase: 'primary',
    pitcher: 'avoid', // Never
    // Other positions not assigned
  }
};

console.log("Competitive mode:");
console.log("- First Base (assigned as primary):", canPlayPosition(player3, "First Base", true, true));
console.log("- Pitcher (marked as avoid/never):", canPlayPosition(player3, "Pitcher", true, true));
console.log("- Center Field (not assigned):", canPlayPosition(player3, "Center Field", true, true));

console.log("\nRecreational mode:");
console.log("- First Base (assigned as primary):", canPlayPosition(player3, "First Base", true, false));
console.log("- Pitcher (marked as avoid/never):", canPlayPosition(player3, "Pitcher", true, false));
console.log("- Center Field (not assigned):", canPlayPosition(player3, "Center Field", true, false));

// Test 4: Player marked as utility player but no other roles
console.log("\n\nTest 4: Player marked as UTILITY but no specific roles");
const player4 = {
  name: "Sarah Wilson",
  teamRoles: {
    isUtilityPlayer: true
    // No specific position assignments
  }
};

console.log("Competitive mode:");
console.log("- First Base (utility covers this):", canPlayPosition(player4, "First Base", true, true));
console.log("- Pitcher (utility doesn't cover):", canPlayPosition(player4, "Pitcher", true, true));
console.log("- Center Field (utility covers this):", canPlayPosition(player4, "Center Field", true, true));

console.log("\nRecreational mode:");
console.log("- First Base (utility covers this):", canPlayPosition(player4, "First Base", true, false));
console.log("- Pitcher (utility doesn't cover):", canPlayPosition(player4, "Pitcher", true, false));
console.log("- Center Field (utility covers this):", canPlayPosition(player4, "Center Field", true, false));

// Summary
console.log("\n\n=== SUMMARY ===");
console.log("In competitive mode:");
console.log("- Players with NO teamRoles cannot play ANY position");
console.log("- Players with EMPTY teamRoles cannot play ANY position");
console.log("- Players can ONLY play positions explicitly assigned to them");
console.log("- Utility players can play field positions (not pitcher/catcher) unless marked 'avoid'");
console.log("\nIn recreational mode:");
console.log("- Players with NO teamRoles CAN play ANY position");
console.log("- Players with EMPTY teamRoles CAN play ANY position");
console.log("- Players can play ANY position unless marked 'avoid' (Never)");
console.log("- More flexible to allow rotation and equal playing time");
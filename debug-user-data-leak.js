const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugUserDataLeak(email) {
  try {
    console.log(`\n🔍 Debugging data leak for user: ${email}\n`);

    // Get user ID
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserByEmail(email);
    if (authError || !authUser) {
      console.error('❌ User not found:', authError?.message);
      return;
    }

    const userId = authUser.user.id;
    console.log(`✅ User ID: ${userId}`);
    console.log(`Email: ${authUser.user.email}`);
    console.log(`Created: ${new Date(authUser.user.created_at).toLocaleString()}`);

    // Check if user has admin privileges
    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    console.log(`\n📋 Profile:`);
    if (profile) {
      console.log(`  - Full Name: ${profile.full_name || 'Not set'}`);
      console.log(`  - Is Admin: ${profile.is_admin || false}`);
      console.log(`  - Created: ${new Date(profile.created_at).toLocaleString()}`);
    } else {
      console.log('  ❌ No profile found');
    }

    // Check admin_users table
    const { data: adminRecord } = await supabase
      .from('admin_users')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (adminRecord) {
      console.log(`\n⚠️  ADMIN RECORD FOUND:`);
      console.log(`  - Is Active: ${adminRecord.is_active}`);
      console.log(`  - Created By: ${adminRecord.created_by}`);
    }

    // Check what teams the user owns
    const { data: userTeams } = await supabase
      .from('teams')
      .select('id, name, created_at')
      .eq('user_id', userId);
    
    console.log(`\n🏟️  User's Teams: ${userTeams?.length || 0}`);
    if (userTeams?.length > 0) {
      userTeams.forEach(team => {
        console.log(`  - ${team.name} (ID: ${team.id})`);
      });
    }

    // Now check what would happen without user filtering (simulating missing WHERE clause)
    console.log(`\n⚠️  Testing unfiltered queries (simulating missing RLS):`);
    
    // Count all teams in database
    const { count: totalTeams } = await supabase
      .from('teams')
      .select('*', { count: 'exact', head: true });
    
    console.log(`  - Total teams in database: ${totalTeams}`);

    // Check if there are any admin-owned teams
    const adminUserIds = [
      '66a2ef09-8270-4d1e-a16d-c19bb96c6da0', // <EMAIL>
      'e6798cac-afcd-4676-842f-23b1c9c5c903'  // admin user
    ];

    const { data: adminTeams } = await supabase
      .from('teams')
      .select('id, name, user_id')
      .in('user_id', adminUserIds);
    
    if (adminTeams?.length > 0) {
      console.log(`  - Admin teams found: ${adminTeams.length}`);
      adminTeams.forEach(team => {
        console.log(`    • ${team.name} (Owner: ${team.user_id})`);
      });
    }

    // Check for demo teams
    const demoUserIds = [
      '212e5e06-9dd0-4d56-89d2-69915b205b53', // baseball demo
      'f15ba189-c70f-4513-9451-6f95568e784a'  // softball demo
    ];

    const { data: demoTeams } = await supabase
      .from('teams')
      .select('id, name, user_id')
      .in('user_id', demoUserIds);
    
    if (demoTeams?.length > 0) {
      console.log(`  - Demo teams found: ${demoTeams.length}`);
      demoTeams.forEach(team => {
        console.log(`    • ${team.name} (Demo User: ${team.user_id})`);
      });
    }

    // Diagnosis
    console.log(`\n📊 Diagnosis:`);
    if (adminRecord?.is_active || profile?.is_admin) {
      console.log('✅ User IS an admin - they should see all data');
    } else if (totalTeams > (userTeams?.length || 0)) {
      console.log('❌ SECURITY ISSUE: RLS is disabled!');
      console.log(`   User can potentially see ${totalTeams - (userTeams?.length || 0)} teams that don't belong to them`);
      console.log('\n🔧 Without RLS, the application must:');
      console.log('   1. Always filter queries by user_id');
      console.log('   2. Never allow direct table access');
      console.log('   3. Validate user ownership on every operation');
    } else {
      console.log('✅ User data appears properly isolated');
    }

    // Check for common issues
    console.log(`\n🐛 Common Issues to Check:`);
    console.log('1. Is the user accessing /admin/* URLs directly?');
    console.log('2. Is there a localStorage issue with demo_mode or user IDs?');
    console.log('3. Is the AuthContext mixing up user sessions?');
    console.log('4. Are queries missing .eq("user_id", userId) filters?');

  } catch (error) {
    console.error('Error:', error);
  }
}

// Get email from command line
const email = process.argv[2];
if (!email) {
  console.log('Usage: node debug-user-data-leak.js <email>');
  process.exit(1);
}

debugUserDataLeak(email);
-- TEMPORARY BYPASS - Mark all existing users as paid
-- This is a temporary fix while we resolve the database issues

-- Update all existing subscriptions to paid status
UPDATE public.subscriptions
SET 
  is_paid = true,
  tier = COALESCE(tier, 'starter'),
  team_limit = COALESCE(team_limit, 1),
  amount = COALESCE(amount, 2000),
  currency = 'usd',
  payment_date = COALESCE(payment_date, NOW()),
  updated_at = NOW()
WHERE is_paid = false OR is_paid IS NULL;

-- Create subscriptions for users who don't have one
INSERT INTO public.subscriptions (
  user_id,
  is_paid,
  tier,
  team_limit,
  amount,
  currency,
  payment_date,
  created_at,
  updated_at
)
SELECT 
  p.id,
  true,
  'starter',
  1,
  2000,
  'usd',
  NOW(),
  NOW(),
  NOW()
FROM profiles p
LEFT JOIN subscriptions s ON p.id = s.user_id
WHERE s.id IS NULL;

-- Verify all users now have paid subscriptions
SELECT 
  p.email,
  s.is_paid,
  s.tier,
  s.team_limit,
  CASE 
    WHEN s.id IS NULL THEN 'NO SUBSCRIPTION'
    WHEN s.is_paid = true THEN 'PAID'
    ELSE 'UNPAID'
  END as status
FROM profiles p
LEFT JOIN subscriptions s ON p.id = s.user_id
ORDER BY p.email;
# Roster Page & Rotation Rules Copy

## Team Roster Page

### Current Copy on Roster Page:

#### Team Roles Section
**Current Header:** "Player Grouping System"

**Current Explanation:**
- **Team Roles (New & Recommended):** Think like a coach: "These are my 4 first base players" - group players by position naturally
- **How It Works:**
  - Primary: Your #1 choice (1-2 players max)
  - In the Mix: Your player group for this position
  - Emergency: Backup coverage only
  - Never: Player can't play this position

#### Important Notes Section
- You only need to fill in the players you want to add. Empty rows will be ignored.
- If a player will miss a game, don't remove that player from this roster. Instead, you'll mark them as absent when creating a lineup.
- Include uniform numbers with each player's name if you want them to appear on the lineup card.

### Simplified Version Suggestion:

#### Quick Setup Guide (Collapsible)
**What you're doing:** Tell the app which positions each player can play - it's like filling out your team's depth chart.

**The 4 Levels:**
- 🟢 **Primary** = "They're my go-to" (Your best 1-2 players at this position)
- 🔵 **In the Mix** = "They regularly play here" (Most players should have 2-4 of these)
- 🟡 **Emergency** = "Only if needed" (Can play there in a pinch)
- 🔴 **Never** = "Don't put them here" (App will never assign them here)

**💡 Tip:** Start simple - just mark each player's main position and any positions they can't play. You can always refine later!

---

## Rotation Rules Page

### Current Copy on Rotation Rules Page:

#### Rotation Settings
- **Rotate Lineup Every X Innings:** How often to shuffle players between positions
- **Rotate Pitcher Every X Innings:** Special rotation frequency for pitchers
- **Max Consecutive Bench Innings:** Maximum innings a player can sit before being rotated in
- **Competitive Mode:** When ON, prioritizes optimal positioning over equal playing time

### Simplified Version Suggestion:

#### Rotation Settings - How Your Lineup Changes During Games

**Basic Settings:**
- **Rotate Players Every ___ Innings:** How often players switch positions (Default: 1 = every inning)
- **Pitcher Rotation Every ___ Innings:** How long pitchers stay on the mound (Default: 2)
- **Max Bench Time ___ Innings:** No one sits longer than this (Default: 2)

---

## Competitive Mode Differences

### When Competitive Mode is OFF (Fair Play - Default):
- **Goal:** Everyone gets equal playing time
- **How it works:**
  - All players rotate through positions evenly
  - No one sits too long on the bench
  - Position assignments follow your settings but prioritize fairness
  - Great for recreational leagues and development

**What coaches see:** "Fair Play Mode - Equal playing time for all players"

### When Competitive Mode is ON:
- **Goal:** Put your best players in the best positions to win
- **How it works:**
  - "Primary" players get priority at key positions (P, C, SS)
  - Star players stay at their best positions longer
  - Still ensures minimum playing time for everyone
  - Uses player ratings to optimize the strongest lineup

**What coaches see:** "Competitive Mode - Optimized for winning while ensuring minimum play time"

### Key Differences Summary:

| Setting | Fair Play Mode | Competitive Mode |
|---------|----------------|------------------|
| Position Assignment | Rotates evenly | Primary players get preference |
| Playing Time | Equal for all | Minimum guaranteed, stars play more |
| Bench Time | Strictly limited | Flexible based on skill |
| Best For | Rec leagues, youth development | Tournament play, competitive leagues |

---

## Recommended Simplifications:

1. **Remove Technical Jargon:**
   - Instead of "constraint satisfaction" → "Smart rotation"
   - Instead of "position eligibility matrix" → "Who can play where"
   - Instead of "optimization algorithm" → "Fair lineup creator"

2. **Use Coach-Friendly Language:**
   - "Your lineup" instead of "lineup state"
   - "Switch positions" instead of "rotate through position assignments"
   - "Bench time" instead of "consecutive non-playing innings"

3. **Visual Cues:**
   - Use emojis/icons for the 4 levels
   - Green/blue/yellow/red color coding
   - Simple checkmarks and X marks

4. **Context-Sensitive Help:**
   - Show different tips based on mode
   - Hide advanced options unless needed
   - Progressive disclosure of complexity
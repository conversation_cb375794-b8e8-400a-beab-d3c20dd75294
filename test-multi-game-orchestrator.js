/**
 * Test the new multi-game orchestrator
 */

import { generateMultiGameSeries } from './src/lib/multi-game-orchestrator.ts';

// Create test players with team roles
const testPlayers = [
  { id: '1', name: '<PERSON>', teamRoles: { pitcher: 'primary', catcher: 'never' } },
  { id: '2', name: '<PERSON>', teamRoles: { catcher: 'primary', pitcher: 'emergency' } },
  { id: '3', name: '<PERSON>', teamRoles: { firstBase: 'primary', pitcher: 'in the mix' } },
  { id: '4', name: '<PERSON>', teamRoles: { secondBase: 'primary', shortstop: 'in the mix' } },
  { id: '5', name: '<PERSON>', teamRoles: { shortstop: 'primary', thirdBase: 'in the mix' } },
  { id: '6', name: '<PERSON>', teamRoles: { thirdBase: 'primary', outfield: 'in the mix' } },
  { id: '7', name: '<PERSON>', teamRoles: { leftField: 'primary', centerField: 'in the mix' } },
  { id: '8', name: '<PERSON>', teamRoles: { centerField: 'primary', rightField: 'in the mix' } },
  { id: '9', name: '<PERSON>', teamRoles: { rightField: 'primary', firstBase: 'in the mix' } },
  { id: '10', name: 'Jack', teamRoles: { pitcher: 'in the mix', outfield: 'primary' } },
  { id: '11', name: 'Kate', teamRoles: { catcher: 'in the mix', infield: 'primary' } },
  { id: '12', name: 'Liam', teamRoles: { infield: 'primary', outfield: 'in the mix' } },
  { id: '13', name: 'Mia', teamRoles: { outfield: 'primary', pitcher: 'never' } },
  { id: '14', name: 'Noah', teamRoles: { infield: 'in the mix', catcher: 'emergency' } }
];

// Test rules
const testRules = {
  rotateLineupEvery: 1,
  rotatePitcherEvery: 3,
  competitiveMode: false,
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 2,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  respectPositionLockouts: true,
  equalPlayingTime: true,
  seed: 'test-seed'
};

console.log('🧪 Testing Multi-Game Orchestrator');
console.log('=' .repeat(50));
console.log(`Players: ${testPlayers.length}`);
console.log(`Games: 3`);
console.log(`Innings per game: 7`);
console.log(`Total innings: 21`);
console.log(`Field positions per game: 63 (9 x 7)`);
console.log(`Total field positions: 189 (63 x 3)`);
console.log(`Target innings per player: ${Math.floor(189 / 14)} (13-14 range expected)`);
console.log('');

try {
  // Generate a 3-game series
  const seriesResult = generateMultiGameSeries(
    testPlayers,
    3,  // 3 games
    7,  // 7 innings per game
    testRules
  );

  console.log('\n📊 SERIES RESULTS');
  console.log('=' .repeat(50));
  console.log(`Balance Score: ${seriesResult.balanceScore.toFixed(1)}%`);
  console.log(`Games Generated: ${seriesResult.games.length}`);
  
  // Analyze each game
  seriesResult.games.forEach((game, idx) => {
    console.log(`\nGame ${idx + 1}:`);
    console.log(`  Innings: ${game.lineups.length}`);
    
    // Count unique players who played
    const playersWhoPlayed = new Set();
    game.lineups.forEach(inning => {
      ['pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop', 
       'thirdBase', 'leftField', 'centerField', 'rightField'].forEach(pos => {
        const playerName = inning.positions[pos];
        if (playerName) playersWhoPlayed.add(playerName);
      });
    });
    console.log(`  Unique players who played: ${playersWhoPlayed.size}`);
  });

  // Show final cumulative stats
  console.log('\n📈 CUMULATIVE PLAYER STATS');
  console.log('=' .repeat(50));
  
  const statsList = Array.from(seriesResult.cumulativeStats.entries())
    .map(([playerId, stats]) => {
      const player = testPlayers.find(p => p.id === playerId);
      return {
        name: player?.name || 'Unknown',
        fieldInnings: stats.totalFieldInnings,
        benchInnings: stats.totalBenchInnings,
        percentage: (stats.totalFieldInnings / (stats.totalFieldInnings + stats.totalBenchInnings) * 100).toFixed(1)
      };
    })
    .sort((a, b) => b.fieldInnings - a.fieldInnings);

  statsList.forEach(stats => {
    console.log(`${stats.name}: ${stats.fieldInnings} field, ${stats.benchInnings} bench (${stats.percentage}%)`);
  });

  const fieldInnings = statsList.map(s => s.fieldInnings);
  const min = Math.min(...fieldInnings);
  const max = Math.max(...fieldInnings);
  const range = max - min;

  console.log('\n📊 FAIRNESS ANALYSIS');
  console.log('=' .repeat(50));
  console.log(`Min innings played: ${min}`);
  console.log(`Max innings played: ${max}`);
  console.log(`Range: ${range}`);
  console.log(`Assessment: ${range <= 3 ? '✅ EXCELLENT' : range <= 5 ? '⚠️ GOOD' : '❌ NEEDS IMPROVEMENT'}`);

  // Check for position restriction violations
  console.log('\n🔒 POSITION RESTRICTION CHECK');
  console.log('=' .repeat(50));
  
  let totalViolations = 0;
  seriesResult.games.forEach((game, gameIdx) => {
    game.lineups.forEach((inning, inningIdx) => {
      Object.entries(inning.positions).forEach(([position, playerName]) => {
        if (position !== 'bench' && playerName) {
          const player = testPlayers.find(p => p.name === playerName);
          if (player?.teamRoles[position] === 'never') {
            console.log(`❌ VIOLATION: ${playerName} at ${position} (Game ${gameIdx + 1}, Inning ${inningIdx + 1})`);
            totalViolations++;
          }
        }
      });
    });
  });

  if (totalViolations === 0) {
    console.log('✅ No position restriction violations found!');
  } else {
    console.log(`❌ Total violations: ${totalViolations}`);
  }

  console.log('\n✅ TEST COMPLETE');

} catch (error) {
  console.error('❌ Test failed:', error);
  console.error(error.stack);
}
# Auto-Fill Best Lineup and Rotation Rules Persistence Fixes

## Issues Addressed

### 1. Auto-Fill Best Lineup Not Using Player Ratings
The auto-fill functionality was not properly utilizing player ratings/skill levels when determining the best lineup assignments.

### 2. Rotation Rules (Competitive Mode) Not Persisting
Competitive mode settings were not being saved to or loaded from the database.

## Changes Made

### 1. Fixed Auto-Fill Algorithm (utils-enhanced.ts)

#### Enhanced Competitive Score Calculation
- Modified `calculateCompetitiveScore` to check player eligibility first
- Added randomization to break ties between players with same ratings
- Improved sorting algorithm to properly order players by score

#### Improved Rating Score System
- Changed from linear to non-linear scoring (1=10, 2=30, 3=60, 4=85, 5=110)
- Increased star player bonus for key positions (40 points)
- Added penalty for poor ratings (1-star gets -20 points)
- Added small random factor to vary assignments

#### Added Debug Logging
- Logs player scores and ratings during auto-fill
- Shows which players are being considered for each position
- Helps diagnose assignment issues

### 2. Fixed Rotation Rules Persistence

#### Database Migration
Created `add_competitive_mode_columns.sql` to add missing columns:
- `competitive_mode` (BOOLEAN)
- `competitive_min_playing_time` (INTEGER)
- `key_positions` (TEXT[])
- `star_player_rotation_delay` (INTEGER)

#### Updated teamService.ts
- Added competitive mode fields to `fetchRotationRules`
- Added competitive mode fields to `createRotationRules`
- Added competitive mode fields to `updateRotationRules`
- Added defaults to `getDefaultRotationRules`

## Testing Instructions

### Test Auto-Fill Best Lineup

1. Add players with different ratings:
   ```
   - Player A: 5-star pitcher, 4-star catcher
   - Player B: 1-star pitcher, 3-star catcher
   - Player C: 3-star pitcher, 5-star catcher
   ```

2. Click "Auto-Fill Best Lineup"

3. Verify:
   - Player A is assigned to pitcher (highest pitcher rating)
   - Player C is assigned to catcher (highest catcher rating)
   - Assignments change based on ratings, not always the same players

### Test Rotation Rules Persistence

1. Go to Rotation Rules page
2. Enable Competitive Mode
3. Set custom values:
   - Min Playing Time: 60%
   - Key Positions: Pitcher, Catcher, First Base
   - Star Player Delay: 2 innings
4. Save the rules
5. Navigate away and come back
6. Verify all settings persisted correctly
7. Create a new lineup and verify competitive mode is applied

## Implementation Details

### Scoring Algorithm
The new scoring system works as follows:

1. **Base Rating Score**: 
   - 1 star = 10 points
   - 2 stars = 30 points
   - 3 stars = 60 points
   - 4 stars = 85 points
   - 5 stars = 110 points

2. **Bonuses**:
   - Star player in key position: +40 points
   - Star player in other position: +15 points
   - Elite rating (5 stars): +25 points

3. **Penalties**:
   - Poor rating (1 star): -20 points
   - Ineligible position: -1000 points

4. **Randomization**: +0 to 5 points to break ties

### Example Calculations
- 5-star pitcher who is a star player: 110 + 40 + 25 + (0-5) = 175-180 points
- 3-star pitcher (regular player): 60 + (0-5) = 60-65 points
- 1-star pitcher: 10 - 20 + (0-5) = -10 to -5 points

## Migration Note
Run the database migration before deploying:
```bash
supabase migration up
```

## Future Enhancements
1. Add UI to set player ratings in Team Roster
2. Add visual indicators for player ratings in lineup views
3. Add team-wide competitive settings that apply to all lineups
4. Add analytics to show how well auto-fill is distributing playing time
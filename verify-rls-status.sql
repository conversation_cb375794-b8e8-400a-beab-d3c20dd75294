-- CHECK WHAT'S ACTUALLY HAPPENING WITH RLS

-- 1. Check if RLS is enabled
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables
WHERE schemaname = 'public'
AND tablename IN ('profiles', 'subscriptions', 'teams', 'players', 'lineups')
ORDER BY tablename;

-- 2. Check what policies exist
SELECT 
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies
WHERE schemaname = 'public'
AND tablename IN ('profiles', 'subscriptions', 'teams')
ORDER BY tablename, policyname;

-- 3. Check if the user's records exist
SELECT 
    'User Check' as check_type,
    u.id,
    u.email,
    u.created_at as user_created,
    p.id IS NOT NULL as has_profile,
    s.id IS NOT NULL as has_subscription,
    s.is_paid,
    s.tier,
    s.team_limit,
    t.id IS NOT NULL as has_team
FROM auth.users u
LEFT JOIN public.profiles p ON p.id = u.id
LEFT JOIN public.subscriptions s ON s.user_id = u.id
LEFT JOIN public.teams t ON t.user_id = u.id AND t.name = 'My Team'
WHERE u.email = '<EMAIL>';

-- 4. FORCE DISABLE RLS COMPLETELY (TEMPORARY - FOR TESTING ONLY)
-- This will make the app work immediately but removes security
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.teams DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.players DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.lineups DISABLE ROW LEVEL SECURITY;

-- 5. Verify RLS is disabled
SELECT 
    'RLS Status After Disable' as status,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables
WHERE schemaname = 'public'
AND tablename IN ('profiles', 'subscriptions', 'teams');
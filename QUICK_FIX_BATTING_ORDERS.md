# Quick Fix for Batting Orders RLS Error

## The Issue
You're getting a 403 error: "new row violates row-level security policy for table batting_orders"

This happens because the RLS policies for the batting_orders table are too restrictive or missing.

## Quick Fix

Run this SQL in the Supabase SQL Editor:

```sql
-- Enable R<PERSON> on batting_orders if not already enabled
ALTER TABLE batting_orders ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view own batting_orders" ON batting_orders;
DROP POLICY IF EXISTS "Users can insert own batting_orders" ON batting_orders;
DROP POLICY IF EXISTS "Users can update own batting_orders" ON batting_orders;
DROP POLICY IF EXISTS "Users can delete own batting_orders" ON batting_orders;

-- Create simpler, working policies
-- Allow users to do everything with batting orders for their own lineups
CREATE POLICY "batting_orders_all_policy"
ON batting_orders
FOR ALL
USING (
    lineup_id IN (
        SELECT l.id 
        FROM lineups l 
        JOIN teams t ON t.id = l.team_id 
        WHERE t.user_id = auth.uid()
    )
)
WITH CHECK (
    lineup_id IN (
        SELECT l.id 
        FROM lineups l 
        JOIN teams t ON t.id = l.team_id 
        WHERE t.user_id = auth.uid()
    )
);

-- <PERSON> permissions
GRANT ALL ON batting_orders TO authenticated;
```

## Alternative: Temporary Disable RLS (Not Recommended for Production)

If you need a quick workaround for testing:

```sql
-- TEMPORARY: Disable RLS on batting_orders
ALTER TABLE batting_orders DISABLE ROW LEVEL SECURITY;
```

**Important**: Only use this for testing. Re-enable RLS before going to production:

```sql
-- Re-enable RLS
ALTER TABLE batting_orders ENABLE ROW LEVEL SECURITY;
```

## The Error Explained

The error occurs because:
1. The app is trying to insert a batting order for a lineup
2. The RLS policy is checking if the user owns the lineup
3. The policy might be failing because the `user_id` field check is conflicting with the lineup ownership check

The simplified policy above only checks lineup ownership through the team relationship, which should resolve the issue.
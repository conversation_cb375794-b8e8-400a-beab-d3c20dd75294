-- Check subscription table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'subscriptions'
ORDER BY ordinal_position;

-- Find all paid subscriptions with missing fields
SELECT 
  s.id,
  s.user_id,
  p.email,
  s.is_paid,
  s.tier,
  s.team_limit,
  s.expires_at,
  s.created_at,
  s.updated_at
FROM subscriptions s
LEFT JOIN profiles p ON s.user_id = p.id
WHERE s.is_paid = true
AND (s.tier IS NULL OR s.team_limit IS NULL)
ORDER BY s.created_at DESC;

-- Check specific user's subscription status (replace with actual email)
SELECT 
  p.id as user_id,
  p.email,
  p.is_admin,
  s.id as subscription_id,
  s.is_paid,
  s.tier,
  s.team_limit,
  s.expires_at,
  s.created_at as subscription_created,
  s.updated_at as subscription_updated
FROM profiles p
LEFT JOIN subscriptions s ON p.id = s.user_id
WHERE p.email = '<EMAIL>';  -- Replace with actual user email

-- Count subscription statistics
SELECT 
  COUNT(*) as total_subscriptions,
  COUNT(CASE WHEN is_paid = true THEN 1 END) as paid_subscriptions,
  COUNT(CASE WHEN is_paid = true AND tier IS NULL THEN 1 END) as paid_missing_tier,
  COUNT(CASE WHEN is_paid = true AND team_limit IS NULL THEN 1 END) as paid_missing_team_limit
FROM subscriptions;

-- Find users with multiple subscriptions (potential duplicates)
SELECT 
  p.email,
  COUNT(s.id) as subscription_count,
  array_agg(json_build_object(
    'id', s.id,
    'is_paid', s.is_paid,
    'tier', s.tier,
    'created_at', s.created_at
  ) ORDER BY s.created_at DESC) as subscriptions
FROM profiles p
JOIN subscriptions s ON p.id = s.user_id
GROUP BY p.id, p.email
HAVING COUNT(s.id) > 1
ORDER BY COUNT(s.id) DESC;

-- Fix missing fields for paid subscriptions
UPDATE subscriptions
SET 
  tier = COALESCE(tier, 'starter'),
  team_limit = COALESCE(team_limit, 1),
  updated_at = NOW()
WHERE is_paid = true
AND (tier IS NULL OR team_limit IS NULL);

-- Show recently updated subscriptions
SELECT 
  s.id,
  p.email,
  s.is_paid,
  s.tier,
  s.team_limit,
  s.updated_at
FROM subscriptions s
LEFT JOIN profiles p ON s.user_id = p.id
WHERE s.updated_at > NOW() - INTERVAL '1 hour'
ORDER BY s.updated_at DESC;
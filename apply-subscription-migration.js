#!/usr/bin/env node

import { supabase } from "./src/integrations/supabase/node-client.js";
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

console.log(`${colors.bright}${colors.cyan}
╔═══════════════════════════════════════════════════════════╗
║           Subscription Tiers Migration Tool               ║
╚═══════════════════════════════════════════════════════════╝
${colors.reset}`);

// Get Supabase URL and anon key from environment or .env file
let SUPABASE_URL = process.env.VITE_SUPABASE_URL;
let SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY;

// Try to read from .env file if not in environment
if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  try {
    const envContent = readFileSync('.env', 'utf-8');
    const envVars = {};
    envContent.split('\n').forEach(line => {
      if (line && !line.startsWith('#')) {
        const [key, ...valueParts] = line.split('=');
        if (key) {
          envVars[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
    SUPABASE_URL = SUPABASE_URL || envVars.VITE_SUPABASE_URL;
    SUPABASE_ANON_KEY = SUPABASE_ANON_KEY || envVars.VITE_SUPABASE_ANON_KEY;
  } catch (error) {
    // .env file not found
  }
}

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  console.error(`${colors.red}Error: Missing Supabase credentials${colors.reset}`);
  console.log('\nPlease ensure you have VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY in your .env file');
  process.exit(1);
}

console.log(`${colors.green}✓${colors.reset} Found Supabase credentials`);
console.log(`${colors.blue}URL:${colors.reset} ${SUPABASE_URL}`);

// Read the migration file
const migrationPath = join(__dirname, 'supabase/migrations/add_subscription_tiers.sql');
let migrationSQL;

try {
  migrationSQL = readFileSync(migrationPath, 'utf-8');
  console.log(`${colors.green}✓${colors.reset} Migration file loaded`);
} catch (error) {
  console.error(`${colors.red}Error reading migration file:${colors.reset}`, error.message);
  process.exit(1);
}

console.log(`\n${colors.yellow}Important:${colors.reset} This script can only check the migration status.`);
console.log('To apply the migration, you need to run it in the Supabase SQL Editor.\n');

// Check if migration has already been applied
async function checkMigrationStatus() {
  try {
    // Check if the tier column exists
    const { data, error } = await supabase
      .from('subscriptions')
      .select('tier')
      .limit(1);

    if (error && error.message.includes('column subscriptions.tier does not exist')) {
      console.log(`${colors.yellow}⚠${colors.reset}  Migration has NOT been applied yet`);
      console.log(`\n${colors.bright}Next Steps:${colors.reset}`);
      console.log('1. Go to your Supabase Dashboard');
      console.log('2. Navigate to SQL Editor');
      console.log('3. Create a new query');
      console.log('4. Copy and paste the migration SQL');
      console.log('5. Click "Run"\n');
      
      console.log(`${colors.cyan}Your Supabase SQL Editor URL:${colors.reset}`);
      console.log(`${SUPABASE_URL.replace('.supabase.co', '.supabase.co/project/' + SUPABASE_URL.split('.')[0].split('//')[1])}/sql\n`);
      
      console.log(`${colors.bright}Migration Preview:${colors.reset}`);
      console.log('This migration will:');
      console.log('- Add tier system (starter, coach, club)');
      console.log('- Add team limits per tier');
      console.log('- Grandfather existing users to club tier');
      console.log('- Create team limit enforcement');
      console.log('- Add subscription status view\n');
      
      // Save migration to a file for easy copying
      const outputPath = 'subscription-migration-to-run.sql';
      try {
        const fs = await import('fs');
        fs.writeFileSync(outputPath, migrationSQL);
        console.log(`${colors.green}✓${colors.reset} Migration SQL saved to: ${colors.bright}${outputPath}${colors.reset}`);
        console.log('  You can copy this file\'s contents to run in Supabase SQL Editor');
      } catch (err) {
        console.log('Could not save migration file:', err.message);
      }
      
      return false;
    } else if (!error) {
      console.log(`${colors.green}✓${colors.reset} Migration appears to be already applied!`);
      
      // Check some sample data
      const { data: subs } = await supabase
        .from('subscriptions')
        .select('tier, team_limit, expires_at')
        .limit(3);
        
      if (subs && subs.length > 0) {
        console.log(`\n${colors.bright}Sample subscription data:${colors.reset}`);
        subs.forEach(sub => {
          console.log(`- Tier: ${sub.tier || 'not set'}, Limit: ${sub.team_limit || 'not set'}`);
        });
      }
      
      return true;
    } else {
      console.error(`${colors.red}Error checking migration status:${colors.reset}`, error.message);
      return null;
    }
  } catch (error) {
    console.error(`${colors.red}Error:${colors.reset}`, error.message);
    return null;
  }
}

// Main execution
(async () => {
  const isApplied = await checkMigrationStatus();
  
  if (isApplied === false) {
    console.log(`\n${colors.yellow}Action Required:${colors.reset} Please run the migration in Supabase SQL Editor`);
  } else if (isApplied === true) {
    console.log(`\n${colors.green}All good!${colors.reset} The migration is already applied.`);
    console.log('\nNext steps:');
    console.log('1. Deploy the edge functions');
    console.log('2. Push your code changes');
    console.log('3. Test the subscription flow');
  }
  
  process.exit(0);
})();
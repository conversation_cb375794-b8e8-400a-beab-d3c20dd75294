-- Fix subscriptions that have tier set but is_paid is false
-- This will mark all subscriptions with a tier as paid

UPDATE subscriptions
SET 
    is_paid = true,
    updated_at = NOW()
WHERE 
    tier IS NOT NULL 
    AND tier != ''
    AND is_paid = false;

-- Show the updated records
SELECT 
    s.id,
    p.email,
    s.is_paid,
    s.tier,
    s.team_limit,
    s.amount
FROM subscriptions s
JOIN profiles p ON p.id = s.user_id
WHERE s.updated_at > NOW() - INTERVAL '1 minute';
-- Fix the handle_new_user trigger that's inserting 'free' tier which violates the valid_tier constraint
-- The valid tiers are: 'starter', 'coach', 'club' (NOT 'free')

-- Drop the existing trigger first
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Recreate the function with correct tier values
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Create profile for new user
    INSERT INTO public.profiles (id, email, is_admin, created_at, updated_at)
    VALUES (
        NEW.id,
        NEW.email,
        CASE WHEN NEW.email IN ('<EMAIL>', '<EMAIL>') THEN true ELSE false END,
        NOW(),
        NOW()
    )
    ON CONFLICT (id) DO NOTHING;

    -- Create subscription record for new user
    -- Use 'starter' instead of 'free' for the tier
    INSERT INTO public.subscriptions (user_id, is_paid, tier, team_limit, created_at, updated_at)
    VALUES (
        NEW.id,
        CASE WHEN NEW.email IN ('<EMAIL>', '<EMAIL>') THEN true ELSE false END,
        CASE WHEN NEW.email IN ('<EMAIL>', '<EMAIL>') THEN 'club' ELSE 'starter' END,
        CASE WHEN NEW.email IN ('<EMAIL>', '<EMAIL>') THEN 999 ELSE 0 END,
        NOW(),
        NOW()
    )
    ON CONFLICT (user_id) DO NOTHING;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the trigger
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Also check if there's a handle_new_profile function that might be doing the same thing
DROP TRIGGER IF EXISTS on_profile_created ON public.profiles;
DROP FUNCTION IF EXISTS public.handle_new_profile();

-- Fix any existing subscriptions that have 'free' tier
UPDATE public.subscriptions 
SET tier = 'starter' 
WHERE tier = 'free';

-- Verify the fix
SELECT 
    'Total subscriptions with free tier (should be 0)' as check,
    COUNT(*) as count
FROM subscriptions
WHERE tier = 'free';

-- Show current constraint definition
SELECT 
    conname as constraint_name,
    pg_get_constraintdef(oid) as definition
FROM pg_constraint
WHERE conrelid = 'subscriptions'::regclass
AND conname = 'valid_tier';
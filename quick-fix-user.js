import { createServiceClient } from "./src/integrations/supabase/node-client.js";
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
});

if (!supabaseUrl || !serviceRoleKey) {
  console.error('\n❌ Missing required environment variables\n');
  console.log('1. Go to Supabase Dashboard > Settings > API');
  console.log('2. Copy the "service_role" key (NOT anon key)');
  console.log('3. Add to .env.local:');
  console.log('   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key\n');
  process.exit(1);
}

// Create admin client
const supabase = createServiceClient();

async function quickFix(email) {
  console.log(`\n🔧 Quick fixing user: ${email}\n`);

  try {
    // Get user
    const { data: { users } } = await supabase.auth.admin.listUsers();
    const user = users.find(u => u.email === email);
    
    if (!user) {
      console.error('❌ User not found');
      return;
    }

    console.log('✅ Found user:', user.id);

    // Fix profile
    await supabase.from('profiles').upsert({
      id: user.id,
      email: user.email,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });
    console.log('✅ Profile fixed');

    // Fix subscription
    await supabase.from('subscriptions').upsert({
      user_id: user.id,
      is_paid: true,
      tier: 'club',
      team_limit: 999,
      currency: 'usd',
      amount: 0,
      payment_date: new Date().toISOString(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });
    console.log('✅ Subscription fixed');

    console.log('\n✨ User fixed! Have them sign out and back in.');

  } catch (error) {
    console.error('Error:', error);
  }
}

const email = process.argv[2];
if (!email) {
  console.error('Usage: node quick-fix-user.js <EMAIL>');
  process.exit(1);
}

quickFix(email);
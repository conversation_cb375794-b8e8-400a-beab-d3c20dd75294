#!/usr/bin/env node

import { supabase } from "./src/integrations/supabase/node-client.js";

// Load environment variables

async function checkProfiles() {
  console.log('Checking profiles table...\n');

  const { data: profiles, error } = await supabase
    .from('profiles')
    .select('id, email, full_name, created_at')
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching profiles:', error);
    return;
  }

  console.log(`Found ${profiles.length} profiles:\n`);
  
  profiles.forEach((profile, index) => {
    console.log(`${index + 1}. ${profile.email || 'No email'} - ${profile.full_name || 'No name'}`);
    console.log(`   ID: ${profile.id}`);
    console.log(`   Created: ${new Date(profile.created_at).toLocaleDateString()}\n`);
  });
}

checkProfiles();
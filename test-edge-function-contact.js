// Test the contact form edge function
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://mhuuptkgohuztjrovpxz.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1odXVwdGtnb2h1enRqcm92cHh6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY0NzU0NDEsImV4cCI6MjA2MjA1MTQ0MX0.aGUpltDKIHYJECOuFHeES7VJp7RKlMjArSg7NxFai_k';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testContactForm() {
  console.log('Testing contact form edge function...');
  
  try {
    const testData = {
      name: 'Test User',
      email: '<EMAIL>',
      subject: 'Test Message',
      message: 'This is a test message from the edge function test script.'
    };
    
    console.log('Sending test data:', testData);
    
    const { data, error } = await supabase.functions.invoke('send-contact-email', {
      body: testData
    });
    
    if (error) {
      console.error('Error from edge function:', error);
      
      // Check if it's a 404 (function not found)
      if (error.message.includes('not found')) {
        console.log('\n❌ The edge function is NOT deployed.');
        console.log('You need to deploy it first using:');
        console.log('  supabase functions deploy send-contact-email');
      } else if (error.message.includes('Failed to send a request')) {
        console.log('\n❌ Cannot reach the edge function.');
        console.log('Possible issues:');
        console.log('1. Function is not deployed');
        console.log('2. CORS issue');
        console.log('3. Network problem');
      }
    } else {
      console.log('Response from edge function:', data);
      
      if (data?.success) {
        console.log('\n✅ Edge function is working!');
        if (data.message.includes('received')) {
          console.log('⚠️  Note: The function is working but Resend API key is not configured yet.');
        } else {
          console.log('✅ Email was sent successfully!');
        }
      }
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

testContactForm();
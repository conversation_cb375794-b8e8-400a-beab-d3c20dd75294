-- IMMEDIATE <NAME_EMAIL>

-- Step 1: Check current status
SELECT 
    'CURRENT STATUS' as step,
    u.id,
    u.email,
    p.id IS NOT NULL as has_profile,
    s.id IS NOT NULL as has_subscription
FROM auth.users u
LEFT JOIN profiles p ON p.id = u.id
LEFT JOIN subscriptions s ON s.user_id = u.id
WHERE u.email = '<EMAIL>';

-- Step 2: Create profile
INSERT INTO profiles (id, email, created_at, updated_at)
SELECT id, email, NOW(), NOW()
FROM auth.users
WHERE email = '<EMAIL>'
ON CONFLICT (id) DO NOTHING;

-- Step 3: Create minimal subscription (avoiding tier constraint)
INSERT INTO subscriptions (
    user_id,
    is_paid,
    team_limit,
    currency,
    created_at,
    updated_at
)
SELECT 
    id,
    true,
    10,
    'usd',
    NOW(),
    NOW()
FROM auth.users
WHERE email = '<EMAIL>'
ON CONFLICT (user_id) DO UPDATE
SET 
    is_paid = true,
    team_limit = 10,
    updated_at = NOW();

-- Step 4: Final verification
SELECT 
    'FINAL STATUS' as step,
    p.email,
    s.is_paid,
    s.team_limit,
    s.created_at
FROM profiles p
JOIN subscriptions s ON s.user_id = p.id
WHERE p.email = '<EMAIL>';
#!/usr/bin/env node

/**
 * Test script to demonstrate MODE-SPECIFIC position eligibility
 * 
 * This shows how the algorithm behaves differently in:
 * - Competitive Mode: Players can ONLY play positions they're assigned to (whitelist)
 * - Fair Play Mode: Players can play ANY position EXCEPT those marked "avoid" (blacklist)
 */

// Mock the canPlayPosition function to demonstrate the logic
function canPlayPosition(player, position, respectLockouts = true, competitiveMode = false) {
  // Normalize position name
  const normalizedPosition = position.toLowerCase().replace(/\s+/g, '');
  
  // Check medical restrictions first (always enforced)
  if (normalizedPosition === 'pitcher' && player.pitcher_restriction) return false;
  if (normalizedPosition === 'catcher' && player.catcher_restriction) return false;
  if (normalizedPosition === 'firstbase' && player.first_base_restriction) return false;
  
  // If no position preferences, behavior depends on mode
  if (!player.positionPreferences || Object.keys(player.positionPreferences).length === 0) {
    if (competitiveMode) {
      console.log(`  → ${player.name} has NO position assignments (competitive mode = cannot play anywhere)`);
      return false;
    } else {
      console.log(`  → ${player.name} has NO position restrictions (fair play mode = can play anywhere)`);
      return true;
    }
  }
  
  const pref = player.positionPreferences[normalizedPosition];
  
  // If marked as "avoid", cannot play in either mode
  if (pref === 'avoid') {
    console.log(`  → ${player.name} AVOIDS ${position} (both modes = cannot play)`);
    return false;
  }
  
  // MODE-SPECIFIC LOGIC
  if (competitiveMode) {
    // COMPETITIVE: Must have explicit entry (whitelist approach)
    if (!pref) {
      console.log(`  → ${player.name} is NOT assigned to ${position} (competitive mode = cannot play)`);
      return false;
    } else {
      console.log(`  → ${player.name} IS assigned to ${position} as "${pref}" (competitive mode = CAN play)`);
      return true;
    }
  } else {
    // FAIR PLAY: Can play unless explicitly avoided (blacklist approach)
    if (!pref) {
      console.log(`  → ${player.name} has no preference for ${position} (fair play mode = CAN play)`);
      return true;
    } else {
      console.log(`  → ${player.name} prefers ${position} as "${pref}" (fair play mode = CAN play)`);
      return true;
    }
  }
}

// Test players
const players = [
  {
    name: 'Avery',
    positionPreferences: {
      leftfield: 'preferred',
      rightfield: 'neutral',
      secondbase: 'preferred',
      centerfield: 'preferred'
      // NOTE: No catcher entry
    },
    pitcher_restriction: false,
    catcher_restriction: false,
    first_base_restriction: false
  },
  {
    name: 'Blake',
    positionPreferences: {
      pitcher: 'avoid',  // Explicitly avoids pitcher
      catcher: 'preferred',
      firstbase: 'neutral'
    }
  },
  {
    name: 'Charlie',
    positionPreferences: {}  // No preferences at all
  },
  {
    name: 'Dana',
    positionPreferences: {
      pitcher: 'preferred',
      catcher: 'avoid'  // Explicitly avoids catcher
    },
    catcher_restriction: true  // Also has medical restriction
  }
];

console.log('========================================');
console.log('MODE-SPECIFIC POSITION ELIGIBILITY TEST');
console.log('========================================\n');

// Test each player in both modes
players.forEach(player => {
  console.log(`\nTesting ${player.name}:`);
  console.log('Preferences:', JSON.stringify(player.positionPreferences || {}));
  
  const testPositions = ['pitcher', 'catcher', 'leftfield'];
  
  console.log('\nCOMPETITIVE MODE (whitelist - only assigned positions):');
  testPositions.forEach(pos => {
    const canPlay = canPlayPosition(player, pos, true, true);
    console.log(`  ${pos}: ${canPlay ? '✅ CAN PLAY' : '❌ CANNOT PLAY'}`);
  });
  
  console.log('\nFAIR PLAY MODE (blacklist - all except avoided positions):');
  testPositions.forEach(pos => {
    const canPlay = canPlayPosition(player, pos, true, false);
    console.log(`  ${pos}: ${canPlay ? '✅ CAN PLAY' : '❌ CANNOT PLAY'}`);
  });
  
  console.log('\n' + '-'.repeat(40));
});

console.log('\n========================================');
console.log('KEY TAKEAWAYS:');
console.log('========================================');
console.log('1. Competitive Mode: Players MUST be explicitly assigned to positions');
console.log('2. Fair Play Mode: Players CAN play any position unless marked "avoid"');
console.log('3. Both modes respect "avoid" preferences and medical restrictions');
console.log('4. Players with no preferences: Cannot play anywhere (competitive), Can play anywhere (fair play)');
console.log('\n✅ This ensures competitive teams have proper position assignments');
console.log('✅ While recreational teams have more flexibility for fun and learning');
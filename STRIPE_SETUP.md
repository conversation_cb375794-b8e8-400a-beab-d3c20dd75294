# Stripe Integration Setup Guide

This document explains how to properly set up Stripe integration for the Baseball Lineup Guru application.

## Prerequisites

1. A Stripe account (create one at [stripe.com](https://stripe.com) if you don't have one)
2. A Supabase project with Edge Functions enabled

## Step 1: Set Up Stripe Account

1. Log in to your Stripe Dashboard at [dashboard.stripe.com](https://dashboard.stripe.com)
2. Make sure you're in the correct mode (Test or Live) depending on your deployment stage
3. Navigate to Developers > API keys
4. Note your **Publishable key** and **Secret key**

## Step 2: Create a Stripe Webhook

1. In your Stripe Dashboard, go to Developers > Webhooks
2. Click "Add endpoint"
3. For the endpoint URL, enter your Supabase Edge Function URL:
   ```
   https://<your-supabase-project-id>.supabase.co/functions/v1/stripe-webhook
   ```
4. For events to send, select:
   - `checkout.session.completed`
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
5. Click "Add endpoint"
6. After creating the webhook, you'll see a **Signing secret**. Copy this value.

## Step 3: Configure Supabase Environment Variables

1. In your Supabase Dashboard, go to Settings > API
2. Navigate to the "Edge Functions" tab
3. Add the following environment variables:
   - `STRIPE_SECRET_KEY`: Your Stripe Secret Key
   - `STRIPE_PUBLISHABLE_KEY`: Your Stripe Publishable Key
   - `STRIPE_WEBHOOK_SECRET`: The Signing Secret from your webhook

## Step 4: Deploy the Edge Functions

1. Make sure you have the Supabase CLI installed:
   ```bash
   npm install -g supabase
   ```

2. Log in to Supabase CLI:
   ```bash
   supabase login
   ```

3. Link your project:
   ```bash
   supabase link --project-ref <your-project-id>
   ```

4. Deploy the Edge Functions:
   ```bash
   supabase functions deploy create-payment
   supabase functions deploy verify-payment
   supabase functions deploy stripe-webhook
   ```

## Step 5: Test the Integration

1. In your application, navigate to the Pricing page
2. Click "Get Lifetime Access" to initiate a checkout
3. Use Stripe test card details:
   - Card number: `4242 4242 4242 4242`
   - Expiration: Any future date
   - CVC: Any 3 digits
   - ZIP: Any 5 digits
4. Complete the checkout
5. You should be redirected to the Payment Success page
6. The application should verify your payment status automatically

## Troubleshooting

### Payment Not Being Verified

1. Check the Supabase Edge Function logs for errors
2. Verify that your webhook is receiving events in the Stripe Dashboard
3. Make sure the webhook signing secret is correctly set in your environment variables
4. Check that the `subscriptions` table exists in your database

### Testing Webhook Locally

For local development, you can use the Stripe CLI to forward webhook events:

1. Install the Stripe CLI: [https://stripe.com/docs/stripe-cli](https://stripe.com/docs/stripe-cli)
2. Run:
   ```bash
   stripe listen --forward-to localhost:54321/functions/v1/stripe-webhook
   ```
3. This will provide a webhook signing secret for local testing

## Production Considerations

1. When moving to production, update your Stripe API keys to live mode
2. Create a new webhook endpoint for your production environment
3. Update the environment variables in your production Supabase project
4. Test the complete payment flow in live mode with a real card (using a small amount)

## Additional Resources

- [Stripe API Documentation](https://stripe.com/docs/api)
- [Supabase Edge Functions Documentation](https://supabase.com/docs/guides/functions)
- [Stripe Webhook Documentation](https://stripe.com/docs/webhooks)

# Pre-Launch Test Checklist for Dugout Boss

## 🔐 1. Sign Up Flow
- [ ] Navigate to https://your-domain.com
- [ ] Click "Get Started" or "Sign Up"
- [ ] Enter email: test_coach_[timestamp]@example.com
- [ ] Enter password (min 6 characters)
- [ ] Verify email confirmation sent
- [ ] Check for proper error messages on invalid input
- [ ] Confirm redirect to payment page after signup

## 💳 2. Payment Process (Live Stripe)
- [ ] See pricing options clearly displayed
- [ ] Click "Subscribe" or payment button
- [ ] Stripe Checkout loads properly
- [ ] Test card: 4242 4242 4242 4242 (for testing even in live mode)
- [ ] Complete payment
- [ ] Verify redirect to dashboard after success
- [ ] Check Stripe dashboard for payment record
- [ ] Verify user marked as 'paid' in database

## 🏟️ 3. Team Creation
- [ ] See team creation prompt on first login
- [ ] Enter team name: "Test Eagles [timestamp]"
- [ ] Save team successfully
- [ ] Verify team appears in team selector
- [ ] Try creating second team
- [ ] Switch between teams successfully

## 👥 4. Add 10+ Players
- [ ] Navigate to Team Roster
- [ ] Add players with varied names:
  1. <PERSON> - <PERSON>, 1B, OF
  2. <PERSON>, 3B
  3. <PERSON> - <PERSON>, 2B, OF
  4. <PERSON>, 1B
  5. <PERSON> - All positions
  6. <PERSON> - <PERSON> only
  7. <PERSON> - <PERSON>, 2B
  8. <PERSON> - 3B, <PERSON>, 1B
  9. <PERSON> - <PERSON>, <PERSON> (star player)
  10. <PERSON> - Utility
  11. Mason <PERSON> - OF only
  12. <PERSON> <PERSON> - SS, 2B (star player)
- [ ] Set position preferences for each
- [ ] Mark 2-3 as star players
- [ ] Save all players successfully
- [ ] Verify all show in roster

## 📋 5. Create a Lineup
- [ ] Click "Create Lineup"
- [ ] Enter game details:
  - Name: "vs Thunder - Game 1"
  - Date: Tomorrow
  - Time: 6:00 PM
  - Innings: 6
- [ ] Continue to attendance
- [ ] Mark 2 players absent
- [ ] Continue to lineup generation
- [ ] Verify all 9 positions filled
- [ ] Check rotation is working
- [ ] Save lineup
- [ ] Download PDF
- [ ] Download CSV

## 📅 6. Create Batch Games
- [ ] Navigate to Batch Games
- [ ] Select 3 games
- [ ] Enter series title: "Weekend Tournament"
- [ ] Set different dates/times
- [ ] Configure rotation settings
- [ ] Generate all lineups
- [ ] Verify series created
- [ ] Check each game has proper lineup

## ✏️ 7. View/Edit Lineups
- [ ] Go to dashboard
- [ ] See all created lineups
- [ ] Click into single game
- [ ] Try "Quick Adjust" for attendance
- [ ] Remove a player
- [ ] Add player back
- [ ] Verify lineup regenerates properly
- [ ] View series
- [ ] Navigate between games in series
- [ ] Check balance metrics

## 📱 8. Mobile Experience
Test on actual phone (or browser mobile mode):
- [ ] Bottom navigation visible
- [ ] Can navigate between sections
- [ ] Team roster displays as cards
- [ ] Create lineup flow works
- [ ] Buttons are tappable (not too small)
- [ ] Can scroll without issues
- [ ] Menu drawer opens/closes
- [ ] Lineup table readable
- [ ] PDF download works on mobile

## 🔍 Additional Checks
- [ ] Rotation rules configuration saves
- [ ] Logout and login works
- [ ] Team switching preserves data
- [ ] No console errors in browser
- [ ] Page refreshes maintain state
- [ ] Demo mode properly restricted

## 🚨 Edge Cases to Test
- [ ] Try lineup with exactly 9 players
- [ ] Try lineup with 8 players (should fail)
- [ ] Set all players absent (should warn)
- [ ] Create game with past date
- [ ] Long team/player names
- [ ] Special characters in names
- [ ] Rapid clicking (no double submits)

## 📊 Performance Checks
- [ ] Team roster loads < 3 seconds
- [ ] Lineup generation < 2 seconds
- [ ] Page transitions smooth
- [ ] No lag when typing

## ✅ Final Verification
- [ ] Check Supabase dashboard for data
- [ ] Verify Stripe subscription active
- [ ] Test on 2-3 different devices
- [ ] Clear browser data and test fresh signup

---

## Test Results Log

Date: ___________
Tester: ___________

Issues Found:
1. 
2. 
3. 

Notes:


---

## Quick Issue Fixes

**Payment not working?**
- Check Stripe webhook endpoint
- Verify STRIPE_PUBLISHABLE_KEY is live key
- Check Edge Function logs

**Login issues?**
- Check Supabase Auth settings
- Verify email confirmations enabled/disabled
- Check redirect URLs

**Performance slow?**
- Check Supabase connection pooling
- Verify indexes are applied
- Check for N+1 queries

**Mobile issues?**
- Test on real device, not just browser
- Check iOS Safari specifically
- Verify touch targets 44px minimum
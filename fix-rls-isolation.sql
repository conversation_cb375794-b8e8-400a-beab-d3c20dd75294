-- Fix RLS Data Isolation - Users seeing admin data instead of fresh slate
-- This ensures users only see their own data, not admin or other users' data

-- Enable RLS on all tables (if not already enabled)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE players ENABLE ROW LEVEL SECURITY;
ALTER TABLE lineups ENABLE ROW LEVEL SECURITY;
ALTER TABLE innings ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance ENABLE ROW LEVEL SECURITY;
ALTER TABLE rotation_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_audit_logs ENABLE ROW LEVEL SECURITY;

-- Drop all existing policies to start fresh
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT tablename, policyname FROM pg_policies WHERE schemaname = 'public') LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON %I', r.policyname, r.tablename);
    END LOOP;
END $$;

-- PROFILES table policies
-- Users can only see and manage their own profile
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can delete own profile" ON profiles FOR DELETE USING (auth.uid() = user_id);

-- TEAMS table policies
-- Users can only see and manage their own teams
CREATE POLICY "Users can view own teams" ON teams FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "Users can insert own teams" ON teams FOR INSERT WITH CHECK (user_id = auth.uid());
CREATE POLICY "Users can update own teams" ON teams FOR UPDATE USING (user_id = auth.uid());
CREATE POLICY "Users can delete own teams" ON teams FOR DELETE USING (user_id = auth.uid());

-- PLAYERS table policies
-- Users can only see and manage players in their own teams
CREATE POLICY "Users can view players in own teams" ON players FOR SELECT 
USING (EXISTS (
    SELECT 1 FROM teams WHERE teams.id = players.team_id AND teams.user_id = auth.uid()
));
CREATE POLICY "Users can insert players in own teams" ON players FOR INSERT 
WITH CHECK (EXISTS (
    SELECT 1 FROM teams WHERE teams.id = players.team_id AND teams.user_id = auth.uid()
));
CREATE POLICY "Users can update players in own teams" ON players FOR UPDATE 
USING (EXISTS (
    SELECT 1 FROM teams WHERE teams.id = players.team_id AND teams.user_id = auth.uid()
));
CREATE POLICY "Users can delete players in own teams" ON players FOR DELETE 
USING (EXISTS (
    SELECT 1 FROM teams WHERE teams.id = players.team_id AND teams.user_id = auth.uid()
));

-- LINEUPS table policies
-- Users can only see and manage lineups for their own teams
CREATE POLICY "Users can view lineups for own teams" ON lineups FOR SELECT 
USING (EXISTS (
    SELECT 1 FROM teams WHERE teams.id = lineups.team_id AND teams.user_id = auth.uid()
));
CREATE POLICY "Users can insert lineups for own teams" ON lineups FOR INSERT 
WITH CHECK (EXISTS (
    SELECT 1 FROM teams WHERE teams.id = lineups.team_id AND teams.user_id = auth.uid()
));
CREATE POLICY "Users can update lineups for own teams" ON lineups FOR UPDATE 
USING (EXISTS (
    SELECT 1 FROM teams WHERE teams.id = lineups.team_id AND teams.user_id = auth.uid()
));
CREATE POLICY "Users can delete lineups for own teams" ON lineups FOR DELETE 
USING (EXISTS (
    SELECT 1 FROM teams WHERE teams.id = lineups.team_id AND teams.user_id = auth.uid()
));

-- INNINGS table policies
-- Users can only see and manage innings for lineups in their teams
CREATE POLICY "Users can view innings for own lineups" ON innings FOR SELECT 
USING (EXISTS (
    SELECT 1 FROM lineups 
    JOIN teams ON teams.id = lineups.team_id 
    WHERE lineups.id = innings.lineup_id AND teams.user_id = auth.uid()
));
CREATE POLICY "Users can insert innings for own lineups" ON innings FOR INSERT 
WITH CHECK (EXISTS (
    SELECT 1 FROM lineups 
    JOIN teams ON teams.id = lineups.team_id 
    WHERE lineups.id = innings.lineup_id AND teams.user_id = auth.uid()
));
CREATE POLICY "Users can update innings for own lineups" ON innings FOR UPDATE 
USING (EXISTS (
    SELECT 1 FROM lineups 
    JOIN teams ON teams.id = lineups.team_id 
    WHERE lineups.id = innings.lineup_id AND teams.user_id = auth.uid()
));
CREATE POLICY "Users can delete innings for own lineups" ON innings FOR DELETE 
USING (EXISTS (
    SELECT 1 FROM lineups 
    JOIN teams ON teams.id = lineups.team_id 
    WHERE lineups.id = innings.lineup_id AND teams.user_id = auth.uid()
));

-- ATTENDANCE table policies
-- Users can only see and manage attendance for lineups in their teams
CREATE POLICY "Users can view attendance for own lineups" ON attendance FOR SELECT 
USING (EXISTS (
    SELECT 1 FROM lineups 
    JOIN teams ON teams.id = lineups.team_id 
    WHERE lineups.id = attendance.lineup_id AND teams.user_id = auth.uid()
));
CREATE POLICY "Users can insert attendance for own lineups" ON attendance FOR INSERT 
WITH CHECK (EXISTS (
    SELECT 1 FROM lineups 
    JOIN teams ON teams.id = lineups.team_id 
    WHERE lineups.id = attendance.lineup_id AND teams.user_id = auth.uid()
));
CREATE POLICY "Users can update attendance for own lineups" ON attendance FOR UPDATE 
USING (EXISTS (
    SELECT 1 FROM lineups 
    JOIN teams ON teams.id = lineups.team_id 
    WHERE lineups.id = attendance.lineup_id AND teams.user_id = auth.uid()
));
CREATE POLICY "Users can delete attendance for own lineups" ON attendance FOR DELETE 
USING (EXISTS (
    SELECT 1 FROM lineups 
    JOIN teams ON teams.id = lineups.team_id 
    WHERE lineups.id = attendance.lineup_id AND teams.user_id = auth.uid()
));

-- ROTATION_RULES table policies
-- Users can only see and manage rotation rules for their own teams
CREATE POLICY "Users can view rotation rules for own teams" ON rotation_rules FOR SELECT 
USING (EXISTS (
    SELECT 1 FROM teams WHERE teams.id = rotation_rules.team_id AND teams.user_id = auth.uid()
));
CREATE POLICY "Users can insert rotation rules for own teams" ON rotation_rules FOR INSERT 
WITH CHECK (EXISTS (
    SELECT 1 FROM teams WHERE teams.id = rotation_rules.team_id AND teams.user_id = auth.uid()
));
CREATE POLICY "Users can update rotation rules for own teams" ON rotation_rules FOR UPDATE 
USING (EXISTS (
    SELECT 1 FROM teams WHERE teams.id = rotation_rules.team_id AND teams.user_id = auth.uid()
));
CREATE POLICY "Users can delete rotation rules for own teams" ON rotation_rules FOR DELETE 
USING (EXISTS (
    SELECT 1 FROM teams WHERE teams.id = rotation_rules.team_id AND teams.user_id = auth.uid()
));

-- SUBSCRIPTIONS table policies
-- Users can only see and manage their own subscription
CREATE POLICY "Users can view own subscription" ON subscriptions FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "Users can insert own subscription" ON subscriptions FOR INSERT WITH CHECK (user_id = auth.uid());
CREATE POLICY "Users can update own subscription" ON subscriptions FOR UPDATE USING (user_id = auth.uid());
-- Don't allow users to delete their own subscription records

-- ADMIN POLICIES - Allow admins to see everything
-- Check if user is admin via admin_users table
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM admin_users 
        WHERE user_id = auth.uid() 
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Admin can see all profiles
CREATE POLICY "Admins can view all profiles" ON profiles FOR SELECT USING (is_admin());
CREATE POLICY "Admins can update all profiles" ON profiles FOR UPDATE USING (is_admin());
CREATE POLICY "Admins can delete all profiles" ON profiles FOR DELETE USING (is_admin());

-- Admin can see all teams
CREATE POLICY "Admins can view all teams" ON teams FOR SELECT USING (is_admin());
CREATE POLICY "Admins can update all teams" ON teams FOR UPDATE USING (is_admin());
CREATE POLICY "Admins can delete all teams" ON teams FOR DELETE USING (is_admin());

-- Admin can see all players
CREATE POLICY "Admins can view all players" ON players FOR SELECT USING (is_admin());
CREATE POLICY "Admins can update all players" ON players FOR UPDATE USING (is_admin());
CREATE POLICY "Admins can delete all players" ON players FOR DELETE USING (is_admin());

-- Admin can see all lineups
CREATE POLICY "Admins can view all lineups" ON lineups FOR SELECT USING (is_admin());
CREATE POLICY "Admins can update all lineups" ON lineups FOR UPDATE USING (is_admin());
CREATE POLICY "Admins can delete all lineups" ON lineups FOR DELETE USING (is_admin());

-- Admin can see all subscriptions
CREATE POLICY "Admins can view all subscriptions" ON subscriptions FOR SELECT USING (is_admin());
CREATE POLICY "Admins can update all subscriptions" ON subscriptions FOR UPDATE USING (is_admin());

-- Admin tables policies
CREATE POLICY "Only admins can view admin_users" ON admin_users FOR SELECT USING (is_admin());
CREATE POLICY "Only admins can manage admin_users" ON admin_users FOR ALL USING (is_admin());

CREATE POLICY "Only admins can view admin_audit_logs" ON admin_audit_logs FOR SELECT USING (is_admin());
CREATE POLICY "Only admins can insert admin_audit_logs" ON admin_audit_logs FOR INSERT WITH CHECK (is_admin());

-- Verify the fix
DO $$
BEGIN
    RAISE NOTICE 'RLS Data Isolation Fix Applied Successfully';
    RAISE NOTICE 'Users will now only see their own data';
    RAISE NOTICE 'Admins can still see everything via is_admin() function';
END $$;
import { supabase } from "./src/integrations/supabase/node-client.js";

// Load environment variables

async function checkAndSuggestFixes() {
  console.log('🔍 Checking your database structure...\n');

  const issues = [];
  const sqlFixes = [];

  // Check if we can access profiles table
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);
    
    if (error) {
      if (error.message.includes('column') && error.message.includes('does not exist')) {
        issues.push('❌ Profiles table is missing required columns');
        sqlFixes.push(`
-- Add missing columns to profiles table
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS email TEXT,
ADD COLUMN IF NOT EXISTS is_admin BOOLEAN DEFAULT FALSE;

-- Update profiles with email from auth.users
UPDATE public.profiles p
SET email = u.email
FROM auth.users u
WHERE p.id = u.id AND p.email IS NULL;

-- Set admin users
UPDATE public.profiles
SET is_admin = TRUE
WHERE email IN ('<EMAIL>', '<EMAIL>');
        `);
      } else {
        issues.push(`❌ Profiles table error: ${error.message}`);
      }
    } else {
      console.log('✅ Profiles table exists');
    }
  } catch (err) {
    issues.push('❌ Cannot access profiles table');
  }

  // Check if subscriptions table exists
  try {
    const { data, error } = await supabase
      .from('subscriptions')
      .select('id')
      .limit(1);
    
    if (error) {
      if (error.code === '42P01') { // Table doesn't exist
        issues.push('❌ Subscriptions table does not exist');
        sqlFixes.push(`
-- Create subscriptions table
CREATE TABLE IF NOT EXISTS public.subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  amount INTEGER NOT NULL DEFAULT 0,
  currency TEXT DEFAULT 'usd',
  is_paid BOOLEAN DEFAULT FALSE,
  payment_date TIMESTAMP WITH TIME ZONE,
  stripe_session_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on subscriptions
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for subscriptions
CREATE POLICY "Users can view their own subscription"
  ON public.subscriptions FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own subscription"
  ON public.subscriptions FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own subscription"
  ON public.subscriptions FOR UPDATE
  USING (auth.uid() = user_id);
        `);
      } else {
        issues.push(`❌ Subscriptions table error: ${error.message}`);
      }
    } else {
      console.log('✅ Subscriptions table exists');
    }
  } catch (err) {
    issues.push('❌ Cannot access subscriptions table');
  }

  // Check current user
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (user) {
      console.log(`\n👤 Current user: ${user.email}`);
      
      // Check if user has a profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('email, is_admin')
        .eq('id', user.id)
        .single();
      
      if (profileError) {
        issues.push('❌ Your user profile is missing or incomplete');
        sqlFixes.push(`
-- Create profile for current user
INSERT INTO public.profiles (id, email, full_name, created_at, updated_at)
VALUES ('${user.id}', '${user.email}', '', NOW(), NOW())
ON CONFLICT (id) DO UPDATE
SET email = EXCLUDED.email,
    updated_at = NOW();

-- If you're an admin, also run:
UPDATE public.profiles
SET is_admin = TRUE
WHERE id = '${user.id}';
        `);
      } else {
        console.log(`✅ Profile exists - Admin: ${profile.is_admin ? 'Yes' : 'No'}`);
      }
    }
  } catch (err) {
    console.log('⚠️  Could not check current user');
  }

  // Summary
  console.log('\n' + '='.repeat(60));
  if (issues.length === 0) {
    console.log('\n✅ All checks passed! Your database structure looks good.');
  } else {
    console.log('\n❌ Found issues:\n');
    issues.forEach(issue => console.log(issue));
    
    console.log('\n📋 SQL TO RUN IN SUPABASE SQL EDITOR:\n');
    console.log('-- Copy everything below this line --');
    console.log('-- Run in your Supabase SQL Editor --\n');
    
    sqlFixes.forEach(fix => console.log(fix));
    
    console.log('\n-- End of SQL --');
    console.log('\n📝 Instructions:');
    console.log('1. Go to your Supabase dashboard');
    console.log('2. Navigate to SQL Editor');
    console.log('3. Copy all the SQL above');
    console.log('4. Paste and click "Run"');
    console.log('5. Refresh your admin pages');
  }
}

checkAndSuggestFixes();
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function debugPitcherRoleSave() {
  console.log('\n=== Debugging Pitcher Role Save Issue ===\n');

  // First get teams
  const { data: teams, error: teamError } = await supabase
    .from('teams')
    .select('id, name')
    .limit(5);

  if (teamError) {
    console.error('Error fetching teams:', teamError);
    return;
  }

  console.log('Found teams:', teams?.map(t => `${t.name} (${t.id})`));

  if (!teams || teams.length === 0) {
    console.log('No teams found');
    return;
  }

  // Get players from the first team
  const { data: players, error: fetchError } = await supabase
    .from('players')
    .select('*')
    .eq('team_id', teams[0].id)
    .limit(5);

  if (fetchError) {
    console.error('Error fetching players:', fetchError);
    return;
  }

  if (!players || players.length === 0) {
    console.log('No players found for team:', teams[0].name);
    return;
  }

  // Test with the first player
  const testPlayer = players[0];
  console.log('\n1. Current player data:');
  console.log('Player:', testPlayer.name);
  console.log('ID:', testPlayer.id);
  console.log('Current position_preferences:', JSON.stringify(testPlayer.position_preferences, null, 2));

  // Create test teamRoles with pitcher
  const testTeamRoles = {
    pitcher: 'go-to',
    catcher: 'capable',
    centerField: 'capable'
  };

  console.log('\n2. Attempting to update with teamRoles:');
  console.log(JSON.stringify(testTeamRoles, null, 2));

  // Update the player
  const updateData = {
    position_preferences: {
      ...(testPlayer.position_preferences || {}),
      teamRoles: testTeamRoles
    }
  };

  console.log('\n3. Full update data:');
  console.log(JSON.stringify(updateData, null, 2));

  const { data: updateResult, error: updateError } = await supabase
    .from('players')
    .update(updateData)
    .eq('id', testPlayer.id)
    .select()
    .single();

  if (updateError) {
    console.error('\n4. Update failed:', updateError);
    return;
  }

  console.log('\n4. Update successful, returned data:');
  console.log('Updated position_preferences:', JSON.stringify(updateResult.position_preferences, null, 2));

  // Verify by fetching again
  const { data: verifyData, error: verifyError } = await supabase
    .from('players')
    .select('position_preferences')
    .eq('id', testPlayer.id)
    .single();

  if (verifyError) {
    console.error('\n5. Verification fetch failed:', verifyError);
    return;
  }

  console.log('\n5. Verification - Fresh fetch from database:');
  console.log('position_preferences:', JSON.stringify(verifyData.position_preferences, null, 2));
  
  // Check if pitcher role is present
  const hasPitcherRole = verifyData.position_preferences?.teamRoles?.pitcher;
  console.log('\n6. Pitcher role present?', hasPitcherRole ? 'YES' : 'NO');
  
  if (hasPitcherRole) {
    console.log('Pitcher role value:', verifyData.position_preferences.teamRoles.pitcher);
  }
}

debugPitcherRoleSave().catch(console.error);
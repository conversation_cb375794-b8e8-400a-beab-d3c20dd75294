import { generateCompleteLineup } from './dist/lib/utils-enhanced.js';

// Test with 12 players like in your screenshot
const players = [
  { id: '1', name: '<PERSON>', teamRoles: {} },
  { id: '2', name: '<PERSON><PERSON>', teamRoles: {} },
  { id: '3', name: '<PERSON>', teamRoles: {} },
  { id: '4', name: '<PERSON><PERSON><PERSON>', teamRoles: {} },
  { id: '5', name: '<PERSON>', teamRoles: {} },
  { id: '6', name: '<PERSON>', teamRoles: {} },
  { id: '7', name: '<PERSON>', teamRoles: {} },
  { id: '8', name: 'Player8', teamRoles: {} },
  { id: '9', name: 'Player9', teamRoles: {} },
  { id: '10', name: 'Player10', teamRoles: {} },
  { id: '11', name: 'Player11', teamRoles: {} },
  { id: '12', name: 'Player12', teamRoles: {} }
];

console.log('===========================================');
console.log('TESTING ROTATION WITH rotateLineupEvery = 1');
console.log('===========================================');
console.log('Players: 12');
console.log('Total Innings: 20 (simulating your 4-game series)');
console.log('Expected: ~15 innings per player (20 * 9 / 12)');
console.log('Settings: rotateLineupEvery=1, equalPlayingTime=true');
console.log('===========================================\n');

const innings = generateCompleteLineup(players, 20, {
  rotateLineupEvery: 1,
  rotatePitcherEvery: 2,
  equalPlayingTime: true,
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 2
});

// Track detailed stats
const stats = {};
players.forEach(p => {
  stats[p.name] = {
    fieldInnings: 0,
    benchInnings: 0,
    positions: {},
    consecutiveBench: 0,
    maxConsecutiveBench: 0
  };
});

// Analyze each inning
console.log('INNING-BY-INNING ANALYSIS:');
console.log('==========================\n');

innings.forEach((inning, idx) => {
  console.log(`INNING ${idx + 1}:`);
  
  // Track field positions
  Object.entries(inning.positions).forEach(([pos, playerName]) => {
    if (pos !== 'bench' && playerName) {
      stats[playerName].fieldInnings++;
      stats[playerName].consecutiveBench = 0;
      if (!stats[playerName].positions[pos]) {
        stats[playerName].positions[pos] = 0;
      }
      stats[playerName].positions[pos]++;
    }
  });
  
  // Track bench
  if (inning.positions.bench) {
    inning.positions.bench.forEach(playerName => {
      stats[playerName].benchInnings++;
      stats[playerName].consecutiveBench++;
      stats[playerName].maxConsecutiveBench = Math.max(
        stats[playerName].maxConsecutiveBench,
        stats[playerName].consecutiveBench
      );
    });
  }
  
  // Show rotation changes if any
  if (idx > 0) {
    const prevInning = innings[idx - 1];
    let changes = 0;
    Object.keys(inning.positions).forEach(pos => {
      if (pos !== 'bench' && inning.positions[pos] !== prevInning.positions[pos]) {
        changes++;
      }
    });
    if (changes > 0) {
      console.log(`  → ${changes} position changes from previous inning`);
    }
  }
  
  // Show current balance every 5 innings
  if ((idx + 1) % 5 === 0) {
    const fieldTimes = Object.values(stats).map(s => s.fieldInnings);
    const max = Math.max(...fieldTimes);
    const min = Math.min(...fieldTimes);
    console.log(`  📊 After ${idx + 1} innings - Range: ${max - min} (Max: ${max}, Min: ${min})`);
  }
});

console.log('\n===========================================');
console.log('FINAL RESULTS:');
console.log('===========================================\n');

// Sort by playing time
const sortedPlayers = Object.entries(stats)
  .sort(([,a], [,b]) => b.fieldInnings - a.fieldInnings);

console.log('PLAYING TIME DISTRIBUTION:');
sortedPlayers.forEach(([name, stat]) => {
  const bar = '█'.repeat(Math.floor(stat.fieldInnings / 2));
  console.log(`${name.padEnd(10)} ${stat.fieldInnings} innings ${bar}`);
});

// Calculate balance metrics
const fieldTimes = Object.values(stats).map(s => s.fieldInnings);
const max = Math.max(...fieldTimes);
const min = Math.min(...fieldTimes);
const avg = fieldTimes.reduce((a, b) => a + b) / fieldTimes.length;
const range = max - min;

console.log('\n===========================================');
console.log('BALANCE ANALYSIS:');
console.log('===========================================');
console.log(`Maximum innings played: ${max}`);
console.log(`Minimum innings played: ${min}`);
console.log(`Average innings played: ${avg.toFixed(1)}`);
console.log(`Range: ${range} innings`);
console.log(`Ideal per player: 15.0 innings (20 × 9 ÷ 12)`);

// Check violations
console.log('\n===========================================');
console.log('RULE COMPLIANCE:');
console.log('===========================================');

let benchViolations = 0;
sortedPlayers.forEach(([name, stat]) => {
  if (stat.maxConsecutiveBench > 2) {
    console.log(`❌ ${name} sat ${stat.maxConsecutiveBench} consecutive innings (max allowed: 2)`);
    benchViolations++;
  }
});

if (benchViolations === 0) {
  console.log('✅ No bench time violations - all players rotated properly');
}

// Final verdict
console.log('\n===========================================');
console.log('FINAL VERDICT:');
console.log('===========================================');

if (max <= 17 && range <= 5) {
  console.log('✅ SUCCESS: Excellent balance achieved!');
  console.log('   - No player dominated playing time');
  console.log('   - Fair distribution across all players');
} else if (max <= 18 && range <= 7) {
  console.log('⚠️  ACCEPTABLE: Good balance with minor variations');
  console.log('   - Some players played slightly more');
  console.log('   - Overall distribution is reasonable');
} else {
  console.log('❌ FAILED: Poor balance detected');
  console.log('   - Some players played too much');
  console.log('   - Rotation algorithm needs improvement');
}

// Show who played most/least
console.log('\nMost playing time:', sortedPlayers[0][0], '-', sortedPlayers[0][1].fieldInnings, 'innings');
console.log('Least playing time:', sortedPlayers[sortedPlayers.length - 1][0], '-', sortedPlayers[sortedPlayers.length - 1][1].fieldInnings, 'innings');
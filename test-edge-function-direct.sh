#!/bin/bash

echo "🔍 Direct Edge Function Test for Welcome Email"
echo "============================================="
echo ""

# Check if we have required environment variables
if [ -z "$SUPABASE_URL" ]; then
    echo "❌ SUPABASE_URL is not set"
    echo "Please run: export SUPABASE_URL='your-supabase-url'"
    exit 1
fi

if [ -z "$SUPABASE_ANON_KEY" ]; then
    echo "❌ SUPABASE_ANON_KEY is not set"
    echo "Please run: export SUPABASE_ANON_KEY='your-anon-key'"
    exit 1
fi

if [ -z "$ACCESS_TOKEN" ]; then
    echo "⚠️  ACCESS_TOKEN is not set"
    echo ""
    echo "To get your access token:"
    echo "1. Sign in to your app as an admin"
    echo "2. Open browser DevTools (F12)"
    echo "3. Go to Application → Local Storage"
    echo "4. Find sb-xxxxx-auth-token"
    echo "5. Copy the 'access_token' value"
    echo "6. Run: export ACCESS_TOKEN='your-token'"
    echo ""
    exit 1
fi

echo "✅ Environment variables set"
echo ""
echo "Testing send-welcome-email function..."
echo ""

# Test the function
RESPONSE=$(curl -s -X POST "${SUPABASE_URL}/functions/v1/send-welcome-email" \
  -H "Authorization: Bearer ${ACCESS_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "name": "Direct Test User",
    "temporaryPassword": "DirectTest123!"
  }')

echo "Response:"
echo "$RESPONSE" | jq '.' 2>/dev/null || echo "$RESPONSE"
echo ""

# Check if response contains error
if echo "$RESPONSE" | grep -q "error"; then
    echo "❌ Function returned an error"
    echo ""
    echo "Common issues:"
    echo "1. RESEND_API_KEY not set in Edge Functions secrets"
    echo "2. User is not an admin"
    echo "3. Invalid access token"
    echo ""
    echo "To check Edge Function logs:"
    echo "supabase functions logs send-welcome-email"
else
    echo "✅ Function called successfully!"
    echo ""
    echo "Check:"
    echo "1. Email <NAME_EMAIL>"
    echo "2. Spam folder"
    echo "3. Edge Function logs for details"
fi
import { supabase } from "./src/integrations/supabase/node-client.js";

async function testTeamDeletion() {
  console.log('\n🔍 Testing team deletion directly...\n');

  try {
    // 1. Sign in as test user to get auth context
    const email = process.argv[2];
    const password = process.argv[3];
    
    if (!email || !password) {
      console.log('Usage: node test-team-deletion-direct.js <email> <password>');
      console.log('This will sign in as the user and test team deletion');
      return;
    }

    console.log(`Signing in as: ${email}`);
    
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (authError) {
      console.error('❌ Sign in failed:', authError.message);
      return;
    }

    console.log('✅ Signed in successfully');
    console.log(`User ID: ${authData.user.id}`);

    // 2. Get user's teams
    const { data: teams, error: teamsError } = await supabase
      .from('teams')
      .select('*')
      .eq('user_id', authData.user.id);

    if (teamsError) {
      console.error('❌ Error fetching teams:', teamsError);
      return;
    }

    console.log(`\n📋 Found ${teams?.length || 0} teams:`);
    teams?.forEach(team => {
      console.log(`   - ${team.name} (ID: ${team.id})`);
    });

    if (!teams || teams.length === 0) {
      console.log('\n❌ No teams found for this user');
      return;
    }

    // 3. Try to delete the first team (or a specific one if multiple)
    const teamToDelete = teams[0]; // You can change this to select a different team
    console.log(`\n🗑️  Attempting to delete team: ${teamToDelete.name}`);

    // First, try to delete related data
    console.log('\n   1. Checking for related data...');
    
    // Get lineups
    const { data: lineups, error: lineupsError } = await supabase
      .from('lineups')
      .select('id')
      .eq('team_id', teamToDelete.id);
    
    console.log(`      Found ${lineups?.length || 0} lineups`);

    // Get players
    const { data: players, error: playersError } = await supabase
      .from('players')
      .select('id')
      .eq('team_id', teamToDelete.id);
    
    console.log(`      Found ${players?.length || 0} players`);

    // Get rotation rules
    const { data: rules, error: rulesError } = await supabase
      .from('rotation_rules')
      .select('id')
      .eq('team_id', teamToDelete.id);
    
    console.log(`      Found ${rules?.length || 0} rotation rules`);

    // 4. Now try to delete the team
    console.log('\n   2. Attempting team deletion...');
    const { error: deleteError } = await supabase
      .from('teams')
      .delete()
      .eq('id', teamToDelete.id)
      .eq('user_id', authData.user.id); // Extra safety check

    if (deleteError) {
      console.log('   ❌ Delete failed!');
      console.log('   Error:', deleteError.message);
      console.log('   Code:', deleteError.code);
      console.log('   Details:', deleteError.details);
      console.log('   Hint:', deleteError.hint);
      
      // Check if it's a protected account error
      if (deleteError.message?.includes('protected account')) {
        console.log('\n   ⚠️  This is a PROTECTED ACCOUNT - teams cannot be deleted');
        console.log('   Protected accounts include:');
        console.log('   - <EMAIL>');
        console.log('   - <EMAIL>');
        console.log('   - Any account marked as is_protected in the database');
      }
    } else {
      console.log('   ✅ Delete command executed successfully');
      
      // 5. Verify deletion
      console.log('\n   3. Verifying deletion...');
      const { data: checkTeam, error: checkError } = await supabase
        .from('teams')
        .select('id, name')
        .eq('id', teamToDelete.id)
        .single();
        
      if (checkError?.code === 'PGRST116') { // Not found
        console.log('   ✅ Confirmed: Team has been deleted from database');
      } else if (checkTeam) {
        console.log('   ⚠️  WARNING: Team still exists in database!');
        console.log('   This suggests a client-side state issue');
      }
    }

    // 6. Sign out
    await supabase.auth.signOut();
    console.log('\n✅ Signed out');

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

testTeamDeletion();
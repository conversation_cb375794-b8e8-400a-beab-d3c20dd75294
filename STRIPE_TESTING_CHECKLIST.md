
## Testing Checklist

### 1. Basic Payment Flow
- [ ] Navigate to pricing page
- [ ] Click "Get Started" on a plan
- [ ] Complete checkout with real card
- [ ] Verify redirect to success page
- [ ] Check subscription status in database

### 2. Webhook Verification
- [ ] Check Supabase logs for webhook execution
- [ ] Verify subscription record created
- [ ] Confirm user upgraded to paid status

### 3. Cross-Origin Testing
- [ ] Test from production domain
- [ ] Verify no CORS errors in console
- [ ] Check network tab for failed requests

### 4. Erro<PERSON> Handling
- [ ] Test with invalid card
- [ ] Test canceling payment
- [ ] Verify proper error messages

### 5. Database Verification
```sql
-- Run in Supabase SQL editor
SELECT u.email, s.status, s.price_id, s.current_period_end
FROM auth.users u
JOIN subscriptions s ON u.id = s.user_id
WHERE s.status = 'active'
ORDER BY s.created_at DESC
LIMIT 10;
```

-- COMPREHENSIVE RLS FIX
-- The issue: Users can't create their own profiles/subscriptions/teams

-- 1. Check current RLS policies
SELECT 
    tablename,
    policyname,
    cmd,
    qual,
    with_check
FROM pg_policies
WHERE schemaname = 'public'
AND tablename IN ('profiles', 'subscriptions', 'teams')
ORDER BY tablename, cmd;

-- 2. Drop problematic policies and recreate with proper permissions
DO $$
BEGIN
    -- Drop all existing policies to start fresh
    DROP POLICY IF EXISTS "profiles_select_own_v2" ON profiles;
    DROP POLICY IF EXISTS "profiles_select_own_v3" ON profiles;
    DROP POLICY IF EXISTS "profiles_insert_own_v2" ON profiles;
    DROP POLICY IF EXISTS "profiles_insert_own_v3" ON profiles;
    DROP POLICY IF EXISTS "profiles_update_own_v2" ON profiles;
    DROP POLICY IF EXISTS "profiles_update_own_v3" ON profiles;
    
    DROP POLICY IF EXISTS "subscriptions_select_own_v2" ON subscriptions;
    DROP POLICY IF EXISTS "subscriptions_select_own_v3" ON subscriptions;
    DROP POLICY IF EXISTS "subscriptions_insert_own_v2" ON subscriptions;
    DROP POLICY IF EXISTS "subscriptions_insert_own_v3" ON subscriptions;
    DROP POLICY IF EXISTS "subscriptions_update_own_v2" ON subscriptions;
    DROP POLICY IF EXISTS "subscriptions_update_own_v3" ON subscriptions;
    
    DROP POLICY IF EXISTS "teams_select_own_v2" ON teams;
    DROP POLICY IF EXISTS "teams_select_own_v3" ON teams;
    DROP POLICY IF EXISTS "teams_insert_own_v2" ON teams;
    DROP POLICY IF EXISTS "teams_insert_own_v3" ON teams;
    DROP POLICY IF EXISTS "teams_update_own_v2" ON teams;
    DROP POLICY IF EXISTS "teams_update_own_v3" ON teams;
    DROP POLICY IF EXISTS "teams_delete_own_v2" ON teams;
    DROP POLICY IF EXISTS "teams_delete_own_v3" ON teams;
END $$;

-- 3. Create proper RLS policies that allow users to manage their own data

-- PROFILES policies
CREATE POLICY "profiles_select_own" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "profiles_insert_own" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "profiles_update_own" ON profiles
    FOR UPDATE USING (auth.uid() = id);

-- SUBSCRIPTIONS policies
CREATE POLICY "subscriptions_select_own" ON subscriptions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "subscriptions_insert_own" ON subscriptions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "subscriptions_update_own" ON subscriptions
    FOR UPDATE USING (auth.uid() = user_id);

-- TEAMS policies
CREATE POLICY "teams_select_own" ON teams
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "teams_insert_own" ON teams
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "teams_update_own" ON teams
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "teams_delete_own" ON teams
    FOR DELETE USING (auth.uid() = user_id);

-- 4. Now fix the specific user
DO $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get user ID
    SELECT id INTO v_user_id
    FROM auth.users
    WHERE email = '<EMAIL>';
    
    IF v_user_id IS NOT NULL THEN
        -- Create profile
        INSERT INTO profiles (id, email, created_at, updated_at)
        VALUES (v_user_id, '<EMAIL>', NOW(), NOW())
        ON CONFLICT (id) DO UPDATE
        SET email = EXCLUDED.email, updated_at = NOW();
        
        -- Create subscription with valid tier
        INSERT INTO subscriptions (
            user_id, is_paid, tier, team_limit, 
            currency, amount, payment_date, 
            created_at, updated_at
        )
        VALUES (
            v_user_id, true, 'club', 999,
            'usd', 0, NOW(),
            NOW(), NOW()
        )
        ON CONFLICT (user_id) DO UPDATE
        SET 
            is_paid = true,
            tier = 'club',
            team_limit = 999,
            updated_at = NOW();
            
        RAISE NOTICE 'User fixed successfully';
    END IF;
END $$;

-- 5. Create/update the auth trigger for auto-creating profiles
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
    INSERT INTO public.profiles (id, email, created_at, updated_at)
    VALUES (new.id, new.email, NOW(), NOW())
    ON CONFLICT (id) DO UPDATE
    SET email = EXCLUDED.email, updated_at = NOW();
    
    INSERT INTO public.subscriptions (
        user_id, is_paid, tier, team_limit, 
        created_at, updated_at
    )
    VALUES (
        new.id, false, 'starter', 1,
        NOW(), NOW()
    )
    ON CONFLICT (user_id) DO NOTHING;
    
    RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Ensure trigger exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 6. Verify the fix
SELECT 
    'User Status' as check_type,
    u.email,
    p.id IS NOT NULL as has_profile,
    s.id IS NOT NULL as has_subscription,
    s.is_paid,
    s.tier,
    s.team_limit
FROM auth.users u
LEFT JOIN profiles p ON p.id = u.id
LEFT JOIN subscriptions s ON s.user_id = u.id
WHERE u.email = '<EMAIL>';

-- 7. Verify RLS policies
SELECT 
    'RLS Policies' as check_type,
    tablename,
    COUNT(*) as policy_count,
    STRING_AGG(policyname || ' (' || cmd || ')', ', ') as policies
FROM pg_policies
WHERE schemaname = 'public'
AND tablename IN ('profiles', 'subscriptions', 'teams')
GROUP BY tablename
ORDER BY tablename;
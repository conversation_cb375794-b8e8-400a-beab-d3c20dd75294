-- Create profiles table
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  full_name TEXT,
  role TEXT,
  is_admin BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create assistant_coaches table
CREATE TABLE IF NOT EXISTS public.assistant_coaches (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create admin audit log table for tracking admin actions
CREATE TABLE IF NOT EXISTS public.admin_audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  admin_id UUID REFERENCES auth.users(id) NOT NULL,
  action TEXT NOT NULL,
  entity_type TEXT NOT NULL,
  entity_id TEXT NOT NULL,
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies for profiles
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own profile"
  ON public.profiles FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
  ON public.profiles FOR UPDATE
  USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile"
  ON public.profiles FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Create RLS policies for assistant_coaches
ALTER TABLE public.assistant_coaches ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own assistant coaches"
  ON public.assistant_coaches FOR SELECT
  USING (auth.uid() = owner_id);

CREATE POLICY "Users can insert their own assistant coaches"
  ON public.assistant_coaches FOR INSERT
  WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Users can update their own assistant coaches"
  ON public.assistant_coaches FOR UPDATE
  USING (auth.uid() = owner_id);

CREATE POLICY "Users can delete their own assistant coaches"
  ON public.assistant_coaches FOR DELETE
  USING (auth.uid() = owner_id);

-- Add RLS to admin_audit_logs table
ALTER TABLE public.admin_audit_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for admin access
-- Allow admins to read all profiles
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Allow admins to read all profiles'
    ) THEN
        CREATE POLICY "Allow admins to read all profiles"
        ON public.profiles FOR SELECT
        USING (
            auth.uid() IN (SELECT id FROM public.profiles WHERE is_admin = TRUE)
        );
    END IF;
END
$$;

-- Allow admins to update all profiles
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Allow admins to update all profiles'
    ) THEN
        CREATE POLICY "Allow admins to update all profiles"
        ON public.profiles FOR UPDATE
        USING (
            auth.uid() IN (SELECT id FROM public.profiles WHERE is_admin = TRUE)
        );
    END IF;
END
$$;

-- Allow admins to read all teams
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Allow admins to read all teams'
    ) THEN
        CREATE POLICY "Allow admins to read all teams"
        ON public.teams FOR SELECT
        USING (
            auth.uid() IN (SELECT id FROM public.profiles WHERE is_admin = TRUE)
        );
    END IF;
END
$$;

-- Allow admins to read all players
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Allow admins to read all players'
    ) THEN
        CREATE POLICY "Allow admins to read all players"
        ON public.players FOR SELECT
        USING (
            auth.uid() IN (SELECT id FROM public.profiles WHERE is_admin = TRUE)
        );
    END IF;
END
$$;

-- Allow admins to read all lineups
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Allow admins to read all lineups'
    ) THEN
        CREATE POLICY "Allow admins to read all lineups"
        ON public.lineups FOR SELECT
        USING (
            auth.uid() IN (SELECT id FROM public.profiles WHERE is_admin = TRUE)
        );
    END IF;
END
$$;

-- Allow admins to read all subscriptions
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Allow admins to read all subscriptions'
    ) THEN
        CREATE POLICY "Allow admins to read all subscriptions"
        ON public.subscriptions FOR SELECT
        USING (
            auth.uid() IN (SELECT id FROM public.profiles WHERE is_admin = TRUE)
        );
    END IF;
END
$$;

-- Allow admins to update all subscriptions
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Allow admins to update all subscriptions'
    ) THEN
        CREATE POLICY "Allow admins to update all subscriptions"
        ON public.subscriptions FOR UPDATE
        USING (
            auth.uid() IN (SELECT id FROM public.profiles WHERE is_admin = TRUE)
        );
    END IF;
END
$$;

-- Allow admins to insert subscriptions for any user
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Allow admins to insert subscriptions'
    ) THEN
        CREATE POLICY "Allow admins to insert subscriptions"
        ON public.subscriptions FOR INSERT
        WITH CHECK (
            auth.uid() IN (SELECT id FROM public.profiles WHERE is_admin = TRUE)
        );
    END IF;
END
$$;

-- Only admins can view audit logs
CREATE POLICY "Only admins can view audit logs"
  ON public.admin_audit_logs FOR SELECT
  USING (auth.uid() IN (SELECT id FROM public.profiles WHERE is_admin = TRUE));

-- Only admins can insert audit logs
CREATE POLICY "Only admins can insert audit logs"
  ON public.admin_audit_logs FOR INSERT
  WITH CHECK (auth.uid() IN (SELECT id FROM public.profiles WHERE is_admin = TRUE));

-- Create profiles for existing users
-- This will create a profile for each user in auth.users
INSERT INTO public.profiles (id, full_name, role, is_admin)
SELECT 
  id, 
  COALESCE(raw_user_meta_data->>'full_name', email) as full_name,
  'user' as role,
  CASE WHEN email = '<EMAIL>' THEN TRUE ELSE FALSE END as is_admin
FROM auth.users
WHERE id NOT IN (SELECT id FROM public.profiles);
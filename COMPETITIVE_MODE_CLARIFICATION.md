# Competitive Mode Clarification

## The Problem
There are multiple places where "competitive mode" appears, causing confusion:

### 1. Coaching Style Slider (0-100)
Located in Team Settings, this slider has these ranges:
- **0-20**: Pure Recreational (competitiveMode = false)
  - Everyone plays equally
  - Players can play ANY position (except those marked "Never")
  - Frequent rotation (every inning)
  
- **20-40**: Mostly Recreational (competitiveMode = false)
  - Mostly equal playing time with slight preferences
  - Similar to pure recreational
  
- **40-60**: Balanced (competitiveMode = false)
  - Playing time based on merit with fairness
  - Players favor their assigned positions
  - Moderate rotation (every 2 innings)
  
- **60-80**: Mostly Competitive (competitiveMode = true)
  - Best players at key positions
  - Players ONLY play assigned positions
  - Strategic lineup optimization
  
- **80-100**: Tournament Mode (competitiveMode = true)
  - Win first, minimums only
  - Strictest position assignments
  - Least rotation

### 2. The Actual competitiveMode Boolean
- Set to `true` when slider is 60 or above
- Set to `false` when slider is below 60
- This is what the algorithm actually uses

## The Fix Applied
The batch lineup generation was using potentially stale competitive mode settings from when the batch was created, not the current team settings. We now:

1. Always use the current team's `competitiveMode` setting
2. Log the competitive mode being used for debugging
3. Explicitly pass `competitiveMode` to the lineup generation algorithm

## What This Means For You

### In Competitive Mode (slider >= 60):
- Players can ONLY play positions they are assigned to in Team Roster
- If a player has no assignment for a position, they CANNOT play there
- You MUST have enough players assigned to each position

### In Recreational Mode (slider < 60):
- Players can play ANY position EXCEPT those marked as "Never"
- More flexibility for learning and development
- Position assignments are preferences, not requirements

## How to Fix "Wrong Position" Issues

1. **Check your Coaching Style slider** - Is it set where you want it?
2. **Check Team Roster** - In competitive mode, ensure every player has position assignments
3. **Regenerate lineups** after changing settings - Old lineups won't update automatically
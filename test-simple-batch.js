// Simple test to demonstrate the cross-game tracking concept
console.log('🎯 TESTING CROSS-GAME TRACKING CONCEPT');

const players = Array.from({ length: 12 }, (_, i) => ({
  id: `player${i + 1}`,
  name: `Player${i + 1}`
}));

// Simulate the tracking approach
const playerInningsTracker = {};
players.forEach(player => {
  playerInningsTracker[player.id] = 0;
});

console.log('\n📊 SIMULATING 3-GAME SERIES:');

// Simulate 3 games
for (let gameIndex = 0; gameIndex < 3; gameIndex++) {
  console.log(`\n🎮 GAME ${gameIndex + 1}:`);
  console.log('Before game:', Object.fromEntries(
    Object.entries(playerInningsTracker).filter(([id, count]) => count > 0)
  ));
  
  // Simulate game generation with cross-game awareness
  // Players with fewer innings get priority
  const sortedByInnings = players
    .map(p => ({ ...p, innings: playerInningsTracker[p.id] }))
    .sort((a, b) => a.innings - b.innings);
  
  console.log('Priority order (least innings first):', 
    sortedByInnings.map(p => `${p.name}(${p.innings})`).join(', ')
  );
  
  // Simulate 7 innings * 9 positions = 63 field assignments
  // Give priority to players with fewer innings
  for (let i = 0; i < 63; i++) {
    const playerIndex = i % 12; // Round-robin through all players
    const player = sortedByInnings[playerIndex];
    playerInningsTracker[player.id]++;
  }
  
  console.log('After game:', Object.fromEntries(
    Object.entries(playerInningsTracker)
  ));
}

// Final analysis
console.log('\n🏆 FINAL RESULTS:');
const finalStats = players.map(player => ({
  name: player.name,
  innings: playerInningsTracker[player.id]
})).sort((a, b) => b.innings - a.innings);

const minInnings = Math.min(...finalStats.map(p => p.innings));
const maxInnings = Math.max(...finalStats.map(p => p.innings));
const range = maxInnings - minInnings;

console.log('📊 Final distribution:', finalStats);
console.log(`📈 Balance: ${minInnings}-${maxInnings} innings (range: ${range})`);

// Check if this would be a good result
const totalInnings = 3 * 7 * 9; // 3 games * 7 innings * 9 positions = 189
const averagePerPlayer = totalInnings / 12; // 189 / 12 = 15.75
console.log(`🎯 Mathematical expectation: ${averagePerPlayer} innings per player`);
console.log(`✅ Cross-game tracking would fix the issue: ${range <= 2 ? 'YES' : 'NO'} (range ≤ 2 is excellent)`);
import { supabase } from "./src/integrations/supabase/node-client.js";

// Load environment variables

async function testUserCreation() {
  console.log('Testing admin user creation...\n');

  try {
    // First, sign in as admin
    console.log('1. Signing in as admin...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'your-admin-password' // Replace with your actual password
    });

    if (authError) {
      console.error('Failed to sign in as admin:', authError);
      return;
    }

    console.log('✓ Signed in successfully');
    console.log('Session token:', authData.session?.access_token?.substring(0, 20) + '...');

    // Test creating a user via the Edge Function
    console.log('\n2. Testing Edge Function for user creation...');
    const testUser = {
      email: `test-${Date.now()}@example.com`,
      password: 'TestPassword123!',
      fullName: 'Test User',
      role: 'Coach',
      isPaid: true
    };

    const response = await fetch(`${supabaseUrl}/functions/v1/admin-create-user`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authData.session?.access_token}`
      },
      body: JSON.stringify(testUser)
    });

    const result = await response.json();
    console.log('Edge Function Response:', {
      status: response.status,
      statusText: response.statusText,
      body: result
    });

    if (!response.ok) {
      console.error('❌ Edge Function failed');
      
      // Try fallback method
      console.log('\n3. Testing fallback method (direct auth.signUp)...');
      const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
        email: testUser.email,
        password: testUser.password,
        options: {
          data: {
            full_name: testUser.fullName,
            role: testUser.role
          }
        }
      });

      if (signUpError) {
        console.error('❌ Fallback method also failed:', signUpError);
      } else {
        console.log('✓ Fallback method succeeded');
        console.log('User ID:', signUpData.user?.id);
        
        // Create profile and subscription manually
        if (signUpData.user?.id) {
          console.log('\n4. Creating profile...');
          const { error: profileError } = await supabase
            .from('profiles')
            .upsert({
              id: signUpData.user.id,
              full_name: testUser.fullName,
              email: testUser.email,
              role: testUser.role,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (profileError) {
            console.error('❌ Profile creation failed:', profileError);
          } else {
            console.log('✓ Profile created successfully');
          }

          console.log('\n5. Creating subscription...');
          const { error: subError } = await supabase
            .from('subscriptions')
            .insert({
              user_id: signUpData.user.id,
              is_paid: true,
              amount: 4900,
              currency: 'usd',
              payment_date: new Date().toISOString(),
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (subError) {
            console.error('❌ Subscription creation failed:', subError);
          } else {
            console.log('✓ Subscription created successfully');
          }
        }
      }
    } else {
      console.log('✓ Edge Function succeeded');
      console.log('Created user:', result.data?.user);
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the test
testUserCreation();
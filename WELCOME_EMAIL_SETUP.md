# Welcome Email Setup Guide

This guide explains how the welcome email system works for admin-created users in Dugout Boss.

## Overview

When an administrator creates a new user account through the admin panel, the system automatically sends a welcome email to the new user containing:
- Login credentials (email and temporary password)
- Getting started instructions
- Support contact information

## Architecture

### Components

1. **Email Service Utility** (`src/utils/emailService.ts`)
   - Provides a reusable interface for sending emails
   - Contains HTML and plain text email templates
   - Handles email sending through Supabase edge functions

2. **Send Welcome Email Edge Function** (`supabase/functions/send-welcome-email/`)
   - Verifies admin authentication
   - Sends emails via Resend API
   - Returns success/failure status

3. **Admin Create User Edge Function** (`supabase/functions/admin-create-user/`)
   - Creates new user accounts
   - Automatically triggers welcome email after successful user creation
   - Handles failures gracefully (user creation succeeds even if email fails)

4. **Admin UI** (`src/pages/AdminUsers.tsx`)
   - Provides interface for creating users
   - Shows success message mentioning welcome email

## Email Service Provider

The application uses **Resend** as the email service provider:
- Free tier: 100 emails/day, 3,000/month
- Production ready with good deliverability
- Simple API integration

## Configuration

### Environment Variables

The following environment variable must be set in Supabase:

```bash
# In Supabase Dashboard > Edge Functions > Secrets
RESEND_API_KEY=re_xxxxxxxxxxxxx
```

### Email Settings

- **From Address**: `Dugout Boss <<EMAIL>>`
- **Support Email**: `<EMAIL>`
- **Domain**: Emails are sent from the Resend demo domain in development

## Email Template

The welcome email includes:

### HTML Version
- Professional design with Dugout Boss branding
- Login credentials in a highlighted box
- Step-by-step getting started guide
- Support contact information
- Mobile-responsive layout

### Plain Text Version
- Simple text format for email clients that don't support HTML
- All the same information as HTML version
- Properly formatted for readability

## How It Works

1. Admin creates a new user through the admin panel
2. `admin-create-user` edge function:
   - Creates the user account in Supabase Auth
   - Creates user profile
   - Sets up subscription if marked as paid
3. After successful user creation, the function calls `send-welcome-email`
4. `send-welcome-email` edge function:
   - Verifies the request is from an admin
   - Generates personalized email content
   - Sends email via Resend API
5. User receives welcome email with login instructions

## Testing

### Manual Testing

1. Deploy the edge functions:
   ```bash
   supabase functions deploy send-welcome-email
   supabase functions deploy admin-create-user
   ```

2. Set the RESEND_API_KEY in Supabase dashboard

3. Create a test user through the admin panel

4. Check the test email inbox for the welcome email

### Automated Testing

Use the provided test script:
```bash
node test-welcome-email.js
```

Prerequisites:
- Set `ADMIN_EMAIL` and `ADMIN_PASSWORD` in `.env.local`
- Ensure edge functions are deployed
- RESEND_API_KEY must be configured

## Troubleshooting

### Email Not Sending

1. **Check RESEND_API_KEY**
   - Verify it's set in Supabase Edge Functions secrets
   - Ensure it's a valid API key from Resend dashboard

2. **Check Edge Function Logs**
   ```bash
   supabase functions logs send-welcome-email
   ```

3. **Verify Admin Authentication**
   - Ensure the user creating accounts has `is_admin: true` in profiles table

### Email Going to Spam

1. **Production Setup**
   - Configure custom domain in Resend
   - Set up SPF/DKIM records
   - Use a proper from address (not @resend.dev)

2. **Content Issues**
   - Avoid spam trigger words
   - Maintain good text-to-image ratio
   - Include unsubscribe information (for transactional emails)

### Rate Limits

- Resend free tier: 100 emails/day
- Monitor usage in Resend dashboard
- Upgrade to paid plan for higher limits

## Production Checklist

- [ ] Set up custom domain in Resend
- [ ] Configure SPF/DKIM records in DNS
- [ ] Update from address to use custom domain
- [ ] Set production RESEND_API_KEY
- [ ] Test email deliverability
- [ ] Monitor email metrics in Resend dashboard

## Extending the System

### Adding New Email Types

1. Add new method to `emailService.ts`
2. Create new edge function for the email type
3. Add email templates (HTML and plain text)
4. Integrate with relevant user flows

### Customizing Templates

1. Edit template functions in edge function
2. Maintain both HTML and plain text versions
3. Test across different email clients
4. Ensure mobile responsiveness

## Security Considerations

1. **Authentication**: Only admins can trigger welcome emails
2. **Rate Limiting**: Consider implementing rate limits to prevent abuse
3. **Input Validation**: Email addresses are validated before sending
4. **Sensitive Data**: Temporary passwords are only sent once
5. **Audit Trail**: All user creations are logged in admin_audit_logs
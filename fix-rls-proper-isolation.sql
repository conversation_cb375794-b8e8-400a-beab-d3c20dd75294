-- FIX RLS POLICIES FOR PROPER USER DATA ISOLATION
-- This will re-enable <PERSON><PERSON> with proper policies to ensure users only see their own data

-- 1. First, let's check what data each user has
SELECT 
    u.email,
    u.id as user_id,
    COUNT(DISTINCT t.id) as team_count,
    COUNT(DISTINCT p.id) as player_count,
    COUNT(DISTINCT l.id) as lineup_count
FROM auth.users u
LEFT JOIN public.teams t ON t.user_id = u.id
LEFT JOIN public.players p ON p.team_id = t.id
LEFT JOIN public.lineups l ON l.team_id = t.id
WHERE u.email IN ('<EMAIL>', '<EMAIL>')
GROUP BY u.email, u.id
ORDER BY u.email;

-- 2. Drop ALL existing policies to start fresh
DO $$ 
DECLARE 
    r RECORD;
BEGIN
    FOR r IN (SELECT tablename, policyname 
              FROM pg_policies 
              WHERE schemaname = 'public' 
              AND tablename IN ('profiles', 'subscriptions', 'teams', 'players', 'lineups', 
                               'innings', 'lineup_players', 'lineup_attendance', 'rotation_rules',
                               'player_position_history', 'contact_messages', 'admin_audit_logs'))
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON %I', r.policyname, r.tablename);
    END LOOP;
END $$;

-- 3. Enable RLS on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.players ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.lineups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.innings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.lineup_players ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.lineup_attendance ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.rotation_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.player_position_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.contact_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.admin_audit_logs ENABLE ROW LEVEL SECURITY;

-- 4. Create simple, strict policies for each table

-- PROFILES
CREATE POLICY "Users can only see their own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can only update their own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can only insert their own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);
CREATE POLICY "Users can only delete their own profile" ON public.profiles
    FOR DELETE USING (auth.uid() = id);

-- SUBSCRIPTIONS
CREATE POLICY "Users can only see their own subscription" ON public.subscriptions
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can only update their own subscription" ON public.subscriptions
    FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can only insert their own subscription" ON public.subscriptions
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can only delete their own subscription" ON public.subscriptions
    FOR DELETE USING (auth.uid() = user_id);

-- TEAMS
CREATE POLICY "Users can only see their own teams" ON public.teams
    FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can only update their own teams" ON public.teams
    FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can only insert their own teams" ON public.teams
    FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can only delete their own teams" ON public.teams
    FOR DELETE USING (auth.uid() = user_id);

-- PLAYERS (must belong to user's team)
CREATE POLICY "Users can only see players on their teams" ON public.players
    FOR SELECT USING (EXISTS (
        SELECT 1 FROM public.teams 
        WHERE teams.id = players.team_id 
        AND teams.user_id = auth.uid()
    ));
CREATE POLICY "Users can only update players on their teams" ON public.players
    FOR UPDATE USING (EXISTS (
        SELECT 1 FROM public.teams 
        WHERE teams.id = players.team_id 
        AND teams.user_id = auth.uid()
    ));
CREATE POLICY "Users can only insert players on their teams" ON public.players
    FOR INSERT WITH CHECK (EXISTS (
        SELECT 1 FROM public.teams 
        WHERE teams.id = players.team_id 
        AND teams.user_id = auth.uid()
    ));
CREATE POLICY "Users can only delete players on their teams" ON public.players
    FOR DELETE USING (EXISTS (
        SELECT 1 FROM public.teams 
        WHERE teams.id = players.team_id 
        AND teams.user_id = auth.uid()
    ));

-- LINEUPS (must belong to user's team)
CREATE POLICY "Users can only see lineups for their teams" ON public.lineups
    FOR SELECT USING (EXISTS (
        SELECT 1 FROM public.teams 
        WHERE teams.id = lineups.team_id 
        AND teams.user_id = auth.uid()
    ));
CREATE POLICY "Users can only update lineups for their teams" ON public.lineups
    FOR UPDATE USING (EXISTS (
        SELECT 1 FROM public.teams 
        WHERE teams.id = lineups.team_id 
        AND teams.user_id = auth.uid()
    ));
CREATE POLICY "Users can only insert lineups for their teams" ON public.lineups
    FOR INSERT WITH CHECK (EXISTS (
        SELECT 1 FROM public.teams 
        WHERE teams.id = lineups.team_id 
        AND teams.user_id = auth.uid()
    ));
CREATE POLICY "Users can only delete lineups for their teams" ON public.lineups
    FOR DELETE USING (EXISTS (
        SELECT 1 FROM public.teams 
        WHERE teams.id = lineups.team_id 
        AND teams.user_id = auth.uid()
    ));

-- ROTATION_RULES (must belong to user's team)
CREATE POLICY "Users can only see rotation rules for their teams" ON public.rotation_rules
    FOR SELECT USING (EXISTS (
        SELECT 1 FROM public.teams 
        WHERE teams.id = rotation_rules.team_id 
        AND teams.user_id = auth.uid()
    ));
CREATE POLICY "Users can only update rotation rules for their teams" ON public.rotation_rules
    FOR UPDATE USING (EXISTS (
        SELECT 1 FROM public.teams 
        WHERE teams.id = rotation_rules.team_id 
        AND teams.user_id = auth.uid()
    ));
CREATE POLICY "Users can only insert rotation rules for their teams" ON public.rotation_rules
    FOR INSERT WITH CHECK (EXISTS (
        SELECT 1 FROM public.teams 
        WHERE teams.id = rotation_rules.team_id 
        AND teams.user_id = auth.uid()
    ));
CREATE POLICY "Users can only delete rotation rules for their teams" ON public.rotation_rules
    FOR DELETE USING (EXISTS (
        SELECT 1 FROM public.teams 
        WHERE teams.id = rotation_rules.team_id 
        AND teams.user_id = auth.uid()
    ));

-- INNINGS (must belong to user's lineup)
CREATE POLICY "Users can only see innings for their lineups" ON public.innings
    FOR SELECT USING (EXISTS (
        SELECT 1 FROM public.lineups l
        JOIN public.teams t ON t.id = l.team_id
        WHERE l.id = innings.lineup_id 
        AND t.user_id = auth.uid()
    ));
CREATE POLICY "Users can only update innings for their lineups" ON public.innings
    FOR UPDATE USING (EXISTS (
        SELECT 1 FROM public.lineups l
        JOIN public.teams t ON t.id = l.team_id
        WHERE l.id = innings.lineup_id 
        AND t.user_id = auth.uid()
    ));
CREATE POLICY "Users can only insert innings for their lineups" ON public.innings
    FOR INSERT WITH CHECK (EXISTS (
        SELECT 1 FROM public.lineups l
        JOIN public.teams t ON t.id = l.team_id
        WHERE l.id = innings.lineup_id 
        AND t.user_id = auth.uid()
    ));
CREATE POLICY "Users can only delete innings for their lineups" ON public.innings
    FOR DELETE USING (EXISTS (
        SELECT 1 FROM public.lineups l
        JOIN public.teams t ON t.id = l.team_id
        WHERE l.id = innings.lineup_id 
        AND t.user_id = auth.uid()
    ));

-- LINEUP_PLAYERS (must belong to user's lineup)
CREATE POLICY "Users can only see lineup players for their lineups" ON public.lineup_players
    FOR SELECT USING (EXISTS (
        SELECT 1 FROM public.lineups l
        JOIN public.teams t ON t.id = l.team_id
        WHERE l.id = lineup_players.lineup_id 
        AND t.user_id = auth.uid()
    ));
CREATE POLICY "Users can only update lineup players for their lineups" ON public.lineup_players
    FOR UPDATE USING (EXISTS (
        SELECT 1 FROM public.lineups l
        JOIN public.teams t ON t.id = l.team_id
        WHERE l.id = lineup_players.lineup_id 
        AND t.user_id = auth.uid()
    ));
CREATE POLICY "Users can only insert lineup players for their lineups" ON public.lineup_players
    FOR INSERT WITH CHECK (EXISTS (
        SELECT 1 FROM public.lineups l
        JOIN public.teams t ON t.id = l.team_id
        WHERE l.id = lineup_players.lineup_id 
        AND t.user_id = auth.uid()
    ));
CREATE POLICY "Users can only delete lineup players for their lineups" ON public.lineup_players
    FOR DELETE USING (EXISTS (
        SELECT 1 FROM public.lineups l
        JOIN public.teams t ON t.id = l.team_id
        WHERE l.id = lineup_players.lineup_id 
        AND t.user_id = auth.uid()
    ));

-- LINEUP_ATTENDANCE (must belong to user's lineup)
CREATE POLICY "Users can only see lineup attendance for their lineups" ON public.lineup_attendance
    FOR SELECT USING (EXISTS (
        SELECT 1 FROM public.lineups l
        JOIN public.teams t ON t.id = l.team_id
        WHERE l.id = lineup_attendance.lineup_id 
        AND t.user_id = auth.uid()
    ));
CREATE POLICY "Users can only update lineup attendance for their lineups" ON public.lineup_attendance
    FOR UPDATE USING (EXISTS (
        SELECT 1 FROM public.lineups l
        JOIN public.teams t ON t.id = l.team_id
        WHERE l.id = lineup_attendance.lineup_id 
        AND t.user_id = auth.uid()
    ));
CREATE POLICY "Users can only insert lineup attendance for their lineups" ON public.lineup_attendance
    FOR INSERT WITH CHECK (EXISTS (
        SELECT 1 FROM public.lineups l
        JOIN public.teams t ON t.id = l.team_id
        WHERE l.id = lineup_attendance.lineup_id 
        AND t.user_id = auth.uid()
    ));
CREATE POLICY "Users can only delete lineup attendance for their lineups" ON public.lineup_attendance
    FOR DELETE USING (EXISTS (
        SELECT 1 FROM public.lineups l
        JOIN public.teams t ON t.id = l.team_id
        WHERE l.id = lineup_attendance.lineup_id 
        AND t.user_id = auth.uid()
    ));

-- PLAYER_POSITION_HISTORY (must belong to user's player)
CREATE POLICY "Users can only see position history for their players" ON public.player_position_history
    FOR SELECT USING (EXISTS (
        SELECT 1 FROM public.players p
        JOIN public.teams t ON t.id = p.team_id
        WHERE p.id = player_position_history.player_id 
        AND t.user_id = auth.uid()
    ));
CREATE POLICY "Users can only update position history for their players" ON public.player_position_history
    FOR UPDATE USING (EXISTS (
        SELECT 1 FROM public.players p
        JOIN public.teams t ON t.id = p.team_id
        WHERE p.id = player_position_history.player_id 
        AND t.user_id = auth.uid()
    ));
CREATE POLICY "Users can only insert position history for their players" ON public.player_position_history
    FOR INSERT WITH CHECK (EXISTS (
        SELECT 1 FROM public.players p
        JOIN public.teams t ON t.id = p.team_id
        WHERE p.id = player_position_history.player_id 
        AND t.user_id = auth.uid()
    ));
CREATE POLICY "Users can only delete position history for their players" ON public.player_position_history
    FOR DELETE USING (EXISTS (
        SELECT 1 FROM public.players p
        JOIN public.teams t ON t.id = p.team_id
        WHERE p.id = player_position_history.player_id 
        AND t.user_id = auth.uid()
    ));

-- CONTACT_MESSAGES (users can only see/manage their own messages)
CREATE POLICY "Users can only see their own contact messages" ON public.contact_messages
    FOR SELECT USING (auth.uid() = user_id OR user_id IS NULL);
CREATE POLICY "Users can insert contact messages" ON public.contact_messages
    FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

-- ADMIN_AUDIT_LOGS (only admins can see)
CREATE POLICY "Only admins can see audit logs" ON public.admin_audit_logs
    FOR SELECT USING (EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.is_admin = true
    ));
CREATE POLICY "Only admins can insert audit logs" ON public.admin_audit_logs
    FOR INSERT WITH CHECK (EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.is_admin = true
    ));

-- 5. Verify the policies are in place
SELECT 
    tablename,
    policyname,
    cmd,
    permissive
FROM pg_policies
WHERE schemaname = 'public'
AND tablename IN ('profiles', 'subscriptions', 'teams', 'players', 'lineups')
ORDER BY tablename, policyname;

-- 6. Test that users can only see their own data
-- This should show different results for each user
SELECT 
    'Final Check' as check_type,
    u.email,
    COUNT(DISTINCT t.id) as team_count,
    COUNT(DISTINCT p.id) as player_count
FROM auth.users u
LEFT JOIN public.teams t ON t.user_id = u.id
LEFT JOIN public.players p ON p.team_id = t.id
WHERE u.email IN ('<EMAIL>', '<EMAIL>')
GROUP BY u.email
ORDER BY u.email;
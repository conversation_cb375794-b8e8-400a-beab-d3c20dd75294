-- Fix for auth flow issues - users getting 401 errors

-- 1. Create super permissive policies that only check if user is authenticated
-- This will help diagnose if the issue is with auth.uid() matching

-- Profiles table - allow any authenticated user to do anything with profiles
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "profiles_all_authenticated" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_permissive" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update" ON public.profiles;
DROP POLICY IF EXISTS "profiles_delete" ON public.profiles;

-- Create a single permissive policy
CREATE POLICY "profiles_authenticated_users" ON public.profiles
  FOR ALL 
  USING (auth.role() = 'authenticated')
  WITH CHECK (auth.role() = 'authenticated');

-- Subscriptions table
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "subscriptions_all_authenticated" ON public.subscriptions;
DROP POLICY IF EXISTS "subscriptions_insert_permissive" ON public.subscriptions;
DROP POLICY IF EXISTS "subscriptions_select_own" ON public.subscriptions;

CREATE POLICY "subscriptions_authenticated_users" ON public.subscriptions
  FOR ALL 
  USING (auth.role() = 'authenticated')
  WITH CHECK (auth.role() = 'authenticated');

-- Teams table
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "teams_all_authenticated" ON public.teams;
DROP POLICY IF EXISTS "teams_insert_permissive" ON public.teams;
DROP POLICY IF EXISTS "teams_select" ON public.teams;
DROP POLICY IF EXISTS "teams_insert" ON public.teams;
DROP POLICY IF EXISTS "teams_update" ON public.teams;
DROP POLICY IF EXISTS "teams_delete" ON public.teams;

CREATE POLICY "teams_authenticated_users" ON public.teams
  FOR ALL 
  USING (auth.role() = 'authenticated')
  WITH CHECK (auth.role() = 'authenticated');

-- 2. Also create specific INSERT policies as a fallback
CREATE POLICY "profiles_insert_self" ON public.profiles
  FOR INSERT 
  WITH CHECK (true);  -- Temporarily allow all inserts

CREATE POLICY "subscriptions_insert_self" ON public.subscriptions
  FOR INSERT 
  WITH CHECK (true);  -- Temporarily allow all inserts

CREATE POLICY "teams_insert_self" ON public.teams
  FOR INSERT 
  WITH CHECK (true);  -- Temporarily allow all inserts

-- 3. Grant all permissions
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- 4. Create or update the auth trigger to auto-create profiles
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email)
  )
  ON CONFLICT (id) DO NOTHING;
  
  RETURN NEW;
END;
$$;

-- Recreate the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 5. Check current auth settings
SELECT 
  current_setting('request.jwt.claims', true)::json->>'role' as jwt_role,
  auth.role() as auth_role,
  auth.uid() as auth_uid,
  auth.email() as auth_email;

-- 6. Verify the policies
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename IN ('profiles', 'subscriptions', 'teams')
ORDER BY tablename, policyname;
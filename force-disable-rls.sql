-- FORCE DISABLE RLS - Nuclear option

-- 1. First verify current RLS status
SELECT 
  n.nspname as schema,
  c.relname as table_name,
  c.relrowsecurity as rls_enabled,
  c.relforcerowsecurity as rls_forced
FROM pg_class c
JOIN pg_namespace n ON n.oid = c.relnamespace
WHERE n.nspname = 'public' 
  AND c.relname IN ('profiles', 'subscriptions', 'teams');

-- 2. Force disable R<PERSON> with CASCADE to remove all policies
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.profiles FORCE ROW LEVEL SECURITY;
ALTER TABLE public.profiles NO FORCE ROW LEVEL SECURITY;
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

ALTER TABLE public.subscriptions DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions FORCE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions NO FORCE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions DISABLE ROW LEVEL SECURITY;

ALTER TABLE public.teams DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.teams FORCE ROW LEVEL SECURITY;
ALTER TABLE public.teams NO FORCE ROW LEVEL SECURITY;
ALTER TABLE public.teams DISABLE ROW LEVEL SECURITY;

-- 3. Drop all policies (even though RLS is disabled)
DO $$
DECLARE
  rec record;
BEGIN
  -- List all policies before dropping
  FOR rec IN 
    SELECT schemaname, tablename, policyname 
    FROM pg_policies 
    WHERE schemaname = 'public' 
      AND tablename IN ('profiles', 'subscriptions', 'teams')
  LOOP
    RAISE NOTICE 'Dropping policy: %.%.%', rec.schemaname, rec.tablename, rec.policyname;
    EXECUTE format('DROP POLICY IF EXISTS %I ON %I.%I', rec.policyname, rec.schemaname, rec.tablename);
  END LOOP;
END $$;

-- 4. Verify RLS is disabled
SELECT 
  n.nspname as schema,
  c.relname as table_name,
  CASE 
    WHEN c.relrowsecurity THEN 'ENABLED - STILL ON!' 
    ELSE 'DISABLED - Good' 
  END as rls_status,
  CASE 
    WHEN c.relforcerowsecurity THEN 'FORCED' 
    ELSE 'NOT FORCED' 
  END as force_status
FROM pg_class c
JOIN pg_namespace n ON n.oid = c.relnamespace
WHERE n.nspname = 'public' 
  AND c.relname IN ('profiles', 'subscriptions', 'teams');

-- 5. Count remaining policies (should be 0)
SELECT COUNT(*) as remaining_policies
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename IN ('profiles', 'subscriptions', 'teams');

-- 6. Grant all permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- 7. Create a simple test function
CREATE OR REPLACE FUNCTION public.test_insert_access()
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  test_id uuid := gen_random_uuid();
BEGIN
  -- Try to insert into profiles
  INSERT INTO public.profiles (id, email, full_name) 
  VALUES (test_id::text, '<EMAIL>', 'Test User')
  ON CONFLICT (id) DO NOTHING;
  
  -- Try to delete the test
  DELETE FROM public.profiles WHERE id = test_id::text;
  
  RETURN 'SUCCESS - Insert and delete worked without RLS issues';
EXCEPTION
  WHEN OTHERS THEN
    RETURN 'FAILED - ' || SQLERRM;
END;
$$;

GRANT EXECUTE ON FUNCTION public.test_insert_access() TO authenticated;

-- 8. Final check
SELECT public.test_insert_access() as test_result;
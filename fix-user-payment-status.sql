-- Fix payment status for a specific user
-- Replace '<EMAIL>' with the actual email

-- First, find the user
WITH user_info AS (
    SELECT id, email, full_name 
    FROM profiles 
    WHERE email = '<EMAIL>'  -- CHANGE THIS EMAIL
)
-- Update their subscription
UPDATE subscriptions s
SET 
    is_paid = true,
    tier = 'starter',  -- or 'coach' or 'club'
    team_limit = 1,    -- 1 for starter, 5 for coach, 999 for club
    amount = 2000,     -- 2000 for starter ($20), 3000 for coach, 50000 for club
    payment_date = COALESCE(payment_date, NOW()),
    updated_at = NOW()
FROM user_info u
WHERE s.user_id = u.id
RETURNING s.*, u.email;

-- If no subscription exists, this will create one:
/*
INSERT INTO subscriptions (
    user_id,
    is_paid,
    tier,
    team_limit,
    amount,
    currency,
    payment_date,
    created_at,
    updated_at
)
SELECT 
    id as user_id,
    true as is_paid,
    'starter' as tier,
    1 as team_limit,
    2000 as amount,
    'usd' as currency,
    NOW() as payment_date,
    NOW() as created_at,
    NOW() as updated_at
FROM profiles
WHERE email = '<EMAIL>'  -- CHANGE THIS EMAIL
ON CONFLICT (user_id) DO UPDATE
SET 
    is_paid = true,
    tier = 'starter',
    team_limit = 1,
    amount = 2000,
    payment_date = NOW(),
    updated_at = NOW();
*/
# Fix Localhost:3000 Redirect Issue

## The Problem
Your Supabase authentication is being redirected to `localhost:3000` instead of the actual Supabase URL. This is likely due to the MCP (Model Context Protocol) server setup interfering with your application.

## Immediate Solutions

### 1. Clear All Browser Data
1. Open Chrome/Browser
2. Go to Settings → Privacy and Security → Clear browsing data
3. Select "All time" for time range
4. Check:
   - Cookies and other site data
   - Cached images and files
   - Hosted app data
5. Click "Clear data"

### 2. Check Service Workers
1. Open Chrome DevTools (F12)
2. Go to Application tab
3. Click on "Service Workers" on the left
4. If you see any service workers for dugoutboss.com or localhost:
   - Click "Unregister" for each one
5. Also check "Storage" → "Clear site data"

### 3. Disable Browser Extensions
1. Temporarily disable ALL browser extensions
2. Especially any development or proxy extensions
3. Try logging in again

### 4. Stop MCP Server (if running)
```bash
# Check if MCP server is running
ps aux | grep mcp-server

# Kill any MCP server processes
pkill -f mcp-server
```

### 5. Check Your Hosts File
```bash
# Check if there are any redirects in hosts file
cat /etc/hosts | grep -E "(supabase|dugoutboss)"
```

### 6. Try a Different Browser or Incognito Mode
1. Open an incognito/private window
2. Go to https://dugoutboss.com
3. Try logging in

## Long-term Fix

### Isolate MCP Server from Your App
The MCP server should not interfere with your web application. Consider:

1. **Run MCP on a different port** (edit `SB-MCP/.env`):
   ```env
   MCP_SERVER_PORT=8080  # Change from 3000
   ```

2. **Ensure MCP is only for Claude Desktop**:
   - The MCP server is meant for Claude Desktop to interact with Supabase
   - It should not be involved in your web application at all

3. **Check for proxy settings**:
   - Make sure no proxy is redirecting Supabase requests
   - Check browser proxy settings
   - Check system proxy settings

## Verification

After trying the above:
1. Open browser console (F12)
2. Go to Network tab
3. Try to login
4. Look for the authentication request
5. It should go to `https://mhuuptkgohuztjrovpxz.supabase.co/auth/v1/token`
6. NOT to `localhost:3000`

## If Still Not Working

1. **Check CloudFlare Settings**:
   - Login to CloudFlare dashboard
   - Check if there are any page rules or redirects
   - Check if there are any workers intercepting requests

2. **Inspect the Network Request**:
   - In browser DevTools, look at the failed request
   - Check the "Request Headers"
   - Look for any unusual headers or redirects

3. **Test Direct API Call**:
   ```javascript
   // Run this in browser console
   fetch('https://mhuuptkgohuztjrovpxz.supabase.co/auth/v1/health')
     .then(r => r.json())
     .then(console.log)
     .catch(console.error);
   ```

   This should return a health check response, not redirect to localhost.
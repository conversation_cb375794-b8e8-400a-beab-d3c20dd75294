# CORS Configuration Guide for Production

## Overview

We've implemented a centralized CORS configuration system for all Edge Functions to ensure proper security in production while maintaining flexibility for development.

## What We Changed

1. **Created a shared CORS module** (`supabase/functions/_shared/cors.ts`)
   - Centralizes allowed origins in one place
   - Provides consistent CORS headers across all functions
   - Makes it easy to update allowed domains

2. **Updated all Edge Functions** to use the shared CORS configuration:
   - `create-payment/index.ts`
   - `stripe-webhook/index.ts`
   - `verify-payment/index.ts`

## Configuration Steps for Production

### 1. Update Allowed Origins

Edit `supabase/functions/_shared/cors.ts` and update the `ALLOWED_ORIGINS` array:

```typescript
const ALLOWED_ORIGINS = [
  // Production domains
  'https://dugoutboss.com',
  'https://www.dugoutboss.com',
  // Add any other production domains here
  
  // Development domains (REMOVE THESE IN PRODUCTION)
  // 'http://localhost:5173',
  // 'http://localhost:3000',
];
```

### 2. Deploy Edge Functions

Deploy all functions with the updated CORS configuration:

```bash
# Deploy all functions at once
supabase functions deploy create-payment
supabase functions deploy stripe-webhook
supabase functions deploy verify-payment

# Or use the deployment script
./deploy-edge-functions.sh
```

### 3. Test CORS Configuration

After deployment, test that CORS is working correctly:

1. **From your production domain**: Should work normally
2. **From unauthorized domain**: Should receive CORS error
3. **Using curl**: Test without origin header

```bash
# Test from production domain
curl -H "Origin: https://dugoutboss.com" \
     -H "Content-Type: application/json" \
     -X OPTIONS \
     https://[your-project].supabase.co/functions/v1/create-payment

# Should return proper CORS headers
```

## Security Benefits

1. **Prevents unauthorized domains** from making requests to your payment endpoints
2. **Protects against CSRF attacks** by validating the origin
3. **Maintains flexibility** with centralized configuration
4. **Easy to update** when adding new domains or subdomains

## Development vs Production

### Development Mode
- Keep localhost origins in the allowed list
- Useful for local testing with Supabase CLI

### Production Mode
- Remove all localhost and development origins
- Only include your actual production domains
- Consider using environment variables for domain configuration

## Troubleshooting

### Common Issues

1. **"Invalid origin" error**
   - Check that your domain is in the `ALLOWED_ORIGINS` array
   - Ensure you're using the exact domain (with https://)
   - Check for trailing slashes

2. **CORS preflight failures**
   - Verify the Edge Function is deployed
   - Check that OPTIONS requests are handled
   - Confirm headers include all required fields

3. **Webhook issues**
   - Stripe webhooks don't send Origin headers
   - CORS doesn't apply to webhook endpoints
   - Focus on signature verification instead

## Best Practices

1. **Always use HTTPS** in production
2. **Be specific with domains** - avoid wildcards
3. **Test thoroughly** after any CORS changes
4. **Monitor failed requests** that might indicate attacks
5. **Keep development and production configs separate**

## Next Steps

1. Remove development domains before going live
2. Add any additional production domains (staging, etc.)
3. Consider implementing domain allowlisting via environment variables
4. Set up monitoring for CORS violations
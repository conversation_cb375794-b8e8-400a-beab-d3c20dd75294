// Test script to verify authentication fixes
// Run this in the browser console to test various scenarios

const testAuthFixes = {
  // Test 1: Check session persistence
  checkSessionPersistence: () => {
    console.log('=== Testing Session Persistence ===');
    const sessionFlag = sessionStorage.getItem('auth_session_initialized');
    console.log('Session initialized flag:', sessionFlag);
    console.log('If this is "true" after refresh, session persistence is working');
    console.log('Try refreshing the page and running this test again');
  },

  // Test 2: Check localStorage clearing
  checkLocalStorageClearing: () => {
    console.log('=== Testing localStorage Clearing ===');
    console.log('Current localStorage keys:');
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.includes('team') || key.includes('player') || key.includes('lineup'))) {
        console.log('-', key, '=', localStorage.getItem(key));
      }
    }
    console.log('These should be cleared when switching users');
  },

  // Test 3: Check global clearTeamContext function
  checkClearTeamContext: () => {
    console.log('=== Testing clearTeamContext Function ===');
    if (window.clearTeamContext) {
      console.log('✓ clearTeamContext is available globally');
      console.log('Type: ', typeof window.clearTeamContext);
    } else {
      console.log('✗ clearTeamContext is NOT available globally');
    }
  },

  // Test 4: Simulate user switch
  simulateUserSwitch: () => {
    console.log('=== Simulating User Switch ===');
    console.log('Current team data in localStorage:');
    console.log('- current_team_id:', localStorage.getItem('current_team_id'));
    
    if (window.clearTeamContext) {
      console.log('Calling clearTeamContext()...');
      window.clearTeamContext();
      console.log('Team context cleared');
      
      console.log('After clearing:');
      console.log('- current_team_id:', localStorage.getItem('current_team_id'));
    }
  },

  // Run all tests
  runAll: function() {
    this.checkSessionPersistence();
    console.log('');
    this.checkLocalStorageClearing();
    console.log('');
    this.checkClearTeamContext();
    console.log('');
    this.simulateUserSwitch();
  }
};

// Instructions
console.log('Auth Fix Test Suite Loaded');
console.log('Run testAuthFixes.runAll() to run all tests');
console.log('Or run individual tests:');
console.log('- testAuthFixes.checkSessionPersistence()');
console.log('- testAuthFixes.checkLocalStorageClearing()');
console.log('- testAuthFixes.checkClearTeamContext()');
console.log('- testAuthFixes.simulateUserSwitch()');

// Make it globally available
window.testAuthFixes = testAuthFixes;
import { supabase } from "./src/integrations/supabase/node-client.js";

async function checkSubscriptionsSchema() {
  console.log('Checking subscriptions table schema...\n');

  try {
    // Get a sample subscription to see all columns
    const { data, error } = await supabase
      .from('subscriptions')
      .select('*')
      .limit(1);

    if (error) {
      console.error('Error fetching subscriptions:', error);
      return;
    }

    if (data && data.length > 0) {
      console.log('Available columns in subscriptions table:');
      console.log('=====================================');
      const columns = Object.keys(data[0]);
      columns.forEach(col => {
        console.log(`- ${col}: ${typeof data[0][col]} (value: ${data[0][col]})`);
      });
    } else {
      console.log('No subscriptions found in the table');
      
      // Try to insert a dummy record to see the schema
      console.log('\nAttempting to get schema information...');
      const { error: insertError } = await supabase
        .from('subscriptions')
        .insert({
          user_id: '00000000-0000-0000-0000-000000000000' // This will fail but show us the schema
        });
      
      if (insertError) {
        console.log('\nSchema information from error:', insertError.message);
      }
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

checkSubscriptionsSchema();
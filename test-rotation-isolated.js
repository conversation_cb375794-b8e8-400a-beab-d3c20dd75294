#!/usr/bin/env node

/**
 * Isolated test to reproduce the "Invalid rotation plan" error
 * without dependencies on the full application environment
 */

// Mock environment variables to avoid Supabase import errors
process.env.VITE_SUPABASE_URL = 'https://mock.supabase.co';
process.env.VITE_SUPABASE_ANON_KEY = 'mock-key';

// <PERSON>'s actual U15 Selects team data
const NOAH_SELECTS_PLAYERS = [
  { 
    id: '1', 
    name: '<PERSON>', 
    positionRestrictions: { pitcher: true, catcher: false, firstBase: false, other: null },
    positionRatings: { catcher: 5, pitcher: 3, firstBase: 4, shortstop: 4 },
    isStarPlayer: true
  },
  { 
    id: '2', 
    name: '<PERSON>', 
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null },
    positionRatings: {},
    isStarPlayer: false
  },
  { 
    id: '3', 
    name: '<PERSON>', 
    positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: 'Middle Infield' },
    positionRatings: { pitcher: 4, thirdBase: 4 },
    isStarPlayer: false
  },
  { 
    id: '4', 
    name: '<PERSON>', 
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null },
    positionRatings: { shortstop: 2, thirdBase: 3, secondBase: 2 },
    isStarPlayer: false
  },
  { 
    id: '5', 
    name: 'Elle', 
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null },
    positionRatings: {},
    isStarPlayer: false
  },
  { 
    id: '6', 
    name: 'Evelynn', 
    positionRestrictions: { pitcher: true, catcher: false, firstBase: true, other: '3B/MI/SS/2B' },
    positionRatings: {},
    isStarPlayer: false
  },
  { 
    id: '7', 
    name: 'Finn', 
    positionRestrictions: { pitcher: true, catcher: false, firstBase: true, other: 'Shortstop' },
    positionRatings: {},
    isStarPlayer: false
  },
  { 
    id: '8', 
    name: 'Grace', 
    positionRestrictions: { pitcher: false, catcher: true, firstBase: true, other: null },
    positionRatings: { pitcher: 4, secondBase: 5 },
    isStarPlayer: true
  },
  { 
    id: '9', 
    name: 'Kaitlyn', 
    positionRestrictions: { pitcher: false, catcher: true, firstBase: true, other: 'Middle Infield' },
    positionRatings: { pitcher: 2 },
    isStarPlayer: false
  },
  { 
    id: '10', 
    name: 'Kenzie', 
    positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: null },
    positionRatings: { pitcher: 5, firstBase: 4 },
    isStarPlayer: true
  },
  { 
    id: '11', 
    name: 'Mikayla', 
    positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: '3B/MI/SS/2B' },
    positionRatings: { pitcher: 3, firstBase: 4 },
    isStarPlayer: false
  },
  { 
    id: '12', 
    name: 'Morgan', 
    positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: null },
    positionRatings: { pitcher: 4, firstBase: 3, shortstop: 3, secondBase: 3 },
    isStarPlayer: false
  },
  { 
    id: '13', 
    name: 'Presley', 
    positionRestrictions: { pitcher: false, catcher: true, firstBase: true, other: null },
    positionRatings: { pitcher: 5, shortstop: 5 },
    isStarPlayer: true
  },
  { 
    id: '14', 
    name: 'Vienna', 
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null },
    positionRatings: { thirdBase: 5 },
    isStarPlayer: true
  }
];

// Competitive mode rules
const COMPETITIVE_RULES = {
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 2,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  respectPositionLockouts: true,
  equalPlayingTime: false,
  rotateLineupEvery: 2,
  rotatePitcherEvery: 3,
  competitiveMode: true,
  starPlayerRotationDelay: 2,
  _randomSeed: 12345
};

async function testRotationError() {
  console.log('🔍 TESTING ROTATION ERROR WITH NOAH\'S SELECTS TEAM');
  console.log('=' .repeat(60));
  console.log(`📊 Players: ${NOAH_SELECTS_PLAYERS.length}`);
  console.log(`⭐ Star players: ${NOAH_SELECTS_PLAYERS.filter(p => p.isStarPlayer).length}`);
  console.log(`🚫 Restricted players: ${NOAH_SELECTS_PLAYERS.filter(p => 
    p.positionRestrictions.pitcher || p.positionRestrictions.catcher || p.positionRestrictions.firstBase || p.positionRestrictions.other
  ).length}`);
  
  try {
    // Import the utilities after setting environment variables
    const utils = await import('./src/lib/utils-enhanced.ts');
    
    console.log('\n🚀 GENERATING COMPLETE LINEUP...');
    const lineup = utils.generateCompleteLineup(NOAH_SELECTS_PLAYERS, 6, COMPETITIVE_RULES);
    
    console.log('✅ SUCCESS: Generated lineup without errors!');
    console.log(`📋 Generated ${lineup.length} innings`);
    
    // Validate each inning
    for (const inning of lineup) {
      console.log(`\n📍 Inning ${inning.inning}:`);
      console.log(`  Pitcher: ${inning.positions.pitcher}`);
      console.log(`  Catcher: ${inning.positions.catcher}`);
      console.log(`  Bench: ${inning.positions.bench.join(', ')}`);
      
      const validation = utils.validateLineup(inning, NOAH_SELECTS_PLAYERS, COMPETITIVE_RULES);
      if (!validation.valid) {
        console.log(`  ❌ Validation errors: ${validation.errors.join(', ')}`);
      }
    }
    
  } catch (error) {
    console.log('\n💥 ERROR CAUGHT:');
    console.log(`❌ Type: ${error.constructor.name}`);
    console.log(`❌ Code: ${error.code || 'N/A'}`);
    console.log(`❌ Message: ${error.message}`);
    
    if (error.constraints) {
      console.log(`❌ Constraints: ${JSON.stringify(error.constraints, null, 2)}`);
    }
    
    console.log('\n🔍 STACK TRACE:');
    console.log(error.stack);
    
    // Analyze the error
    if (error.message.includes('Invalid rotation plan')) {
      console.log('\n🎯 FOUND THE "Invalid rotation plan" ERROR!');
      console.log('This error occurs in LineupRotator.validatePlan() method');
      console.log('Likely causes:');
      console.log('1. Missing position assignments in rotation plan');
      console.log('2. Duplicate player assignments');
      console.log('3. Position restriction violations');
      console.log('4. Constraint solver failure with complex restrictions');
    }
    
    return false;
  }
  
  return true;
}

// Run multiple test scenarios
async function runComprehensiveTests() {
  console.log('🧪 RUNNING COMPREHENSIVE ROTATION TESTS');
  console.log('=' .repeat(60));
  
  const testScenarios = [
    {
      name: 'Full Team (14 players)',
      players: NOAH_SELECTS_PLAYERS,
      innings: 6
    },
    {
      name: 'Reduced Team (12 players)',
      players: NOAH_SELECTS_PLAYERS.slice(0, 12),
      innings: 5
    },
    {
      name: 'Minimum Team (9 players)',
      players: NOAH_SELECTS_PLAYERS.slice(0, 9),
      innings: 4
    },
    {
      name: 'Long Game (8 innings)',
      players: NOAH_SELECTS_PLAYERS.slice(0, 11),
      innings: 8
    }
  ];
  
  for (const scenario of testScenarios) {
    console.log(`\n🎯 Testing: ${scenario.name}`);
    console.log(`   Players: ${scenario.players.length}, Innings: ${scenario.innings}`);
    
    try {
      const utils = await import('./src/lib/utils-enhanced.ts');
      const lineup = utils.generateCompleteLineup(scenario.players, scenario.innings, COMPETITIVE_RULES);
      console.log(`   ✅ SUCCESS: Generated ${lineup.length} innings`);
    } catch (error) {
      console.log(`   ❌ FAILED: ${error.message}`);
      if (error.message.includes('Invalid rotation plan')) {
        console.log(`   🎯 FOUND ERROR in scenario: ${scenario.name}`);
        return { scenario, error };
      }
    }
  }
  
  return null;
}

// Run the tests
runComprehensiveTests().then(result => {
  if (result) {
    console.log('\n🎯 ERROR IDENTIFIED:');
    console.log(`Scenario: ${result.scenario.name}`);
    console.log(`Error: ${result.error.message}`);
  } else {
    console.log('\n✅ All tests passed - no rotation errors found');
  }
}).catch(error => {
  console.error('Test script failed:', error);
  process.exit(1);
});

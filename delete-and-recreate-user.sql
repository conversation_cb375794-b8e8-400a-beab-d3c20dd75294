-- COMPLETE USER DELETION AND RECREATION
-- This will completely remove the user and let them sign up fresh

-- 1. Get the user ID
DO $$
DECLARE
    v_user_id UUID;
BEGIN
    SELECT id INTO v_user_id 
    FROM auth.users 
    WHERE email = '<EMAIL>';
    
    IF v_user_id IS NOT NULL THEN
        -- Delete from all tables (cascade should handle most)
        DELETE FROM teams WHERE user_id = v_user_id;
        DELETE FROM subscriptions WHERE user_id = v_user_id;
        DELETE FROM profiles WHERE id = v_user_id;
        
        -- This is the key part - delete from auth.users
        -- Note: This requires service role permissions
        DELETE FROM auth.users WHERE id = v_user_id;
        
        RAISE NOTICE 'User completely deleted. They can now sign up fresh.';
    ELSE
        RAISE NOTICE 'User not found';
    END IF;
END $$;
#!/usr/bin/env node

// Test script to verify forced rotation for players with excessive bench time
// This test simulates the getBenchStreaks function behavior

// Mock player data
const mockPlayers = [
  { id: '1', name: 'Player1', positionRestrictions: {} },
  { id: '2', name: 'Player2', positionRestrictions: {} },
  { id: '3', name: 'Player3', positionRestrictions: {} },
  { id: '4', name: 'Player4', positionRestrictions: {} },
  { id: '5', name: 'Player5', positionRestrictions: {} },
  { id: '6', name: 'Player6', positionRestrictions: {} },
  { id: '7', name: 'Player7', positionRestrictions: {} },
  { id: '8', name: 'Player8', positionRestrictions: {} },
  { id: '9', name: 'Player9', positionRestrictions: {} },
  { id: '10', name: 'Player10', positionRestrictions: {} },
  { id: '11', name: '<PERSON>11', positionRestrictions: {} },
  { id: '12', name: 'Player12', positionRestrictions: {} }
];

// Test rules with forced rotation enabled
const testRules = {
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 2,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  respectPositionLockouts: false, // Disable for simpler testing
  equalPlayingTime: true,
  rotateLineupEvery: 1,
  rotatePitcherEvery: 3,
  _randomSeed: 12345 // Fixed seed for reproducible results
};

// Simulate the getBenchStreaks function
function getBenchStreaks(allInnings, upToInning) {
  const streaks = new Map();

  if (allInnings.length === 0 || upToInning <= 0) {
    return streaks;
  }

  // Initialize all players to 0
  const firstInning = allInnings[0];
  if (firstInning) {
    const allPlayers = new Set();
    Object.values(firstInning.positions).forEach(player => {
      if (typeof player === 'string' && player) {
        allPlayers.add(player);
      } else if (Array.isArray(player)) {
        player.forEach(p => allPlayers.add(p));
      }
    });
    allPlayers.forEach(player => streaks.set(player, 0));
  }

  // Count consecutive bench innings going backwards from upToInning
  for (let i = Math.min(upToInning - 1, allInnings.length - 1); i >= 0; i--) {
    const inning = allInnings[i];
    if (!inning) break;

    // First, reset streak for players who played in the field
    Object.entries(inning.positions).forEach(([position, player]) => {
      if (position !== 'bench' && typeof player === 'string' && player) {
        streaks.set(player, 0);
      }
    });

    // Then increment streak for bench players
    inning.positions.bench.forEach(player => {
      if (player) {
        const currentStreak = streaks.get(player) || 0;
        streaks.set(player, currentStreak + 1);
      }
    });
  }

  return streaks;
}

function testForcedRotation() {
  console.log('🧪 Testing getBenchStreaks Function');
  console.log('=' .repeat(60));

  try {
    // Create a mock lineup scenario where some players stay on bench too long
    const mockLineup = [
      {
        inning: 1,
        positions: {
          pitcher: 'Player1',
          catcher: 'Player2',
          firstBase: 'Player3',
          secondBase: 'Player4',
          shortstop: 'Player5',
          thirdBase: 'Player6',
          leftField: 'Player7',
          centerField: 'Player8',
          rightField: 'Player9',
          bench: ['Player10', 'Player11', 'Player12']
        }
      },
      {
        inning: 2,
        positions: {
          pitcher: 'Player1',
          catcher: 'Player2',
          firstBase: 'Player3',
          secondBase: 'Player4',
          shortstop: 'Player5',
          thirdBase: 'Player6',
          leftField: 'Player7',
          centerField: 'Player8',
          rightField: 'Player9',
          bench: ['Player10', 'Player11', 'Player12'] // Same players on bench
        }
      },
      {
        inning: 3,
        positions: {
          pitcher: 'Player1',
          catcher: 'Player2',
          firstBase: 'Player3',
          secondBase: 'Player4',
          shortstop: 'Player5',
          thirdBase: 'Player6',
          leftField: 'Player7',
          centerField: 'Player8',
          rightField: 'Player9',
          bench: ['Player10', 'Player11', 'Player12'] // Still same players - should trigger forced rotation
        }
      }
    ];

    console.log('\n📊 TESTING BENCH STREAK CALCULATION:');
    console.log('=' .repeat(40));

    // Test the getBenchStreaks function for each inning
    for (let inning = 1; inning <= 3; inning++) {
      console.log(`\nInning ${inning}:`);
      console.log(`  Bench: [${mockLineup[inning-1].positions.bench.join(', ')}]`);

      const benchStreaks = getBenchStreaks(mockLineup, inning);
      console.log('  Bench streaks:');

      mockPlayers.forEach(player => {
        const streak = benchStreaks.get(player.name) || 0;
        const status = streak > testRules.maxConsecutiveBenchInnings ? '🚨 NEEDS ROTATION' : '✅ OK';
        console.log(`    ${player.name}: ${streak} ${status}`);
      });
    }

    // Test the final state after inning 3
    const finalBenchStreaks = getBenchStreaks(mockLineup, 3);

    console.log('\nPlayers needing forced rotation after inning 3:');
    const playersNeedingRotation = [];
    mockPlayers.forEach(player => {
      const streak = finalBenchStreaks.get(player.name) || 0;
      if (streak > testRules.maxConsecutiveBenchInnings) {
        playersNeedingRotation.push(player.name);
        console.log(`  🚨 ${player.name}: ${streak} consecutive bench innings`);
      }
    });

    console.log('\n🎯 TEST RESULTS:');
    console.log('=' .repeat(20));

    if (playersNeedingRotation.length > 0) {
      console.log(`✅ SUCCESS: getBenchStreaks correctly identified ${playersNeedingRotation.length} players needing forced rotation:`);
      playersNeedingRotation.forEach(player => {
        console.log(`  - ${player}`);
      });
      console.log('\n🎉 The forced rotation detection is working correctly!');
      console.log('💡 These players should be prioritized for field positions in the next inning.');
    } else {
      console.log('❌ UNEXPECTED: No players detected as needing forced rotation.');
      console.log('🔧 This might indicate an issue with the test scenario.');
    }

    // Test the validation function
    console.log('\n🔍 TESTING VALIDATION FUNCTION:');
    console.log('=' .repeat(40));

    testValidationFunction();

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    console.error(error.stack);
  }
}

function testValidationFunction() {
  // Test pitcher rotation validation
  const testRulesWithPitcherRotation = {
    ...testRules,
    rotatePitcherEvery: 2,
    allowPitcherRotation: true
  };

  // Create a scenario where pitcher should have rotated but didn't
  const previousInnings = [
    {
      inning: 1,
      positions: {
        pitcher: 'Player1',
        catcher: 'Player2',
        firstBase: 'Player3',
        secondBase: 'Player4',
        shortstop: 'Player5',
        thirdBase: 'Player6',
        leftField: 'Player7',
        centerField: 'Player8',
        rightField: 'Player9',
        bench: ['Player10', 'Player11', 'Player12']
      }
    },
    {
      inning: 2,
      positions: {
        pitcher: 'Player1', // Same pitcher
        catcher: 'Player2',
        firstBase: 'Player3',
        secondBase: 'Player4',
        shortstop: 'Player5',
        thirdBase: 'Player6',
        leftField: 'Player7',
        centerField: 'Player8',
        rightField: 'Player9',
        bench: ['Player10', 'Player11', 'Player12']
      }
    }
  ];

  const currentInning = {
    inning: 3,
    positions: {
      pitcher: 'Player1', // Should have rotated but didn't
      catcher: 'Player2',
      firstBase: 'Player3',
      secondBase: 'Player4',
      shortstop: 'Player5',
      thirdBase: 'Player6',
      leftField: 'Player7',
      centerField: 'Player8',
      rightField: 'Player9',
      bench: ['Player10', 'Player11', 'Player12'] // Excessive bench time
    }
  };

  // Mock validation function (since we can't import the TypeScript)
  function mockValidateRotation(previousInnings, currentInning, rules, players) {
    const violations = [];
    const inningNumber = currentInning.inning;

    // Check pitcher rotation
    if (rules.rotatePitcherEvery && rules.allowPitcherRotation && previousInnings.length > 0) {
      const shouldHaveRotated = inningNumber > 1 &&
        (inningNumber % rules.rotatePitcherEvery === 1 && inningNumber > 1);
      const previousPitcher = previousInnings[previousInnings.length - 1]?.positions.pitcher;
      const currentPitcher = currentInning.positions.pitcher;
      const didRotate = currentPitcher !== previousPitcher;

      if (shouldHaveRotated && !didRotate) {
        violations.push(`Pitcher should have rotated at inning ${inningNumber} (every ${rules.rotatePitcherEvery} innings)`);
      }
    }

    // Check bench streaks
    const allInnings = [...previousInnings, currentInning];
    const benchStreaks = getBenchStreaks(allInnings, inningNumber);
    const maxAllowedStreak = rules.maxConsecutiveBenchInnings || 2;

    benchStreaks.forEach((streak, player) => {
      if (streak > maxAllowedStreak) {
        violations.push(`${player} on bench for ${streak} consecutive innings (max allowed: ${maxAllowedStreak})`);
      }
    });

    return { valid: violations.length === 0, violations };
  }

  const validation = mockValidateRotation(previousInnings, currentInning, testRulesWithPitcherRotation, mockPlayers);

  console.log('Validation result:', validation);

  if (validation.violations.length > 0) {
    console.log('✅ SUCCESS: Validation correctly detected violations:');
    validation.violations.forEach(violation => {
      console.log(`  - ${violation}`);
    });
  } else {
    console.log('❌ FAILURE: Validation should have detected violations but didn\'t');
  }
}

// Run the test
testForcedRotation();

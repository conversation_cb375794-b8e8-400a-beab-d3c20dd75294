-- Fix for AdminDashboard and other pages that need profile relationships

-- 1. Ensure profiles table has all required columns
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS role TEXT DEFAULT 'user',
ADD COLUMN IF NOT EXISTS is_paid BOOLEAN DEFAULT FALSE;

-- 2. Create foreign key relationship for subscriptions join
-- First check if the constraint exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'subscriptions_user_id_fkey'
    ) THEN
        ALTER TABLE public.subscriptions 
        ADD CONSTRAINT subscriptions_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
    END IF;
END $$;

-- 3. Add missing columns to subscriptions if they don't exist
ALTER TABLE public.subscriptions
ADD COLUMN IF NOT EXISTS stripe_payment_intent_id TEXT,
ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT,
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- 4. Create a view to simplify the profile-subscription join
CREATE OR REPLACE VIEW public.subscription_profiles AS
SELECT 
    s.*,
    p.email as user_email,
    p.full_name as user_full_name
FROM public.subscriptions s
LEFT JOIN public.profiles p ON s.user_id = p.id;

-- Grant access to the view
GRANT SELECT ON public.subscription_profiles TO anon, authenticated;

-- 5. Fix admin_audit_logs structure
ALTER TABLE public.admin_audit_logs
ADD COLUMN IF NOT EXISTS target_type TEXT,
ADD COLUMN IF NOT EXISTS target_id UUID,
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}';

-- Update columns if they exist with old names
DO $$
BEGIN
    -- Rename entity_type to target_type if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'admin_audit_logs' 
        AND column_name = 'entity_type'
    ) THEN
        ALTER TABLE public.admin_audit_logs 
        RENAME COLUMN entity_type TO target_type;
    END IF;
    
    -- Rename entity_id to target_id if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'admin_audit_logs' 
        AND column_name = 'entity_id'
    ) THEN
        ALTER TABLE public.admin_audit_logs 
        DROP COLUMN entity_id;
        ALTER TABLE public.admin_audit_logs 
        ADD COLUMN target_id UUID;
    END IF;
    
    -- Rename details to metadata if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'admin_audit_logs' 
        AND column_name = 'details'
    ) THEN
        ALTER TABLE public.admin_audit_logs 
        RENAME COLUMN details TO metadata;
    END IF;
END $$;

-- 6. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_role ON public.profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_is_paid ON public.profiles(is_paid);
CREATE INDEX IF NOT EXISTS idx_subscriptions_created_at ON public.subscriptions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON public.subscriptions(status);

-- 7. Insert some test data if tables are empty (optional)
-- This helps verify the admin pages are working

-- Check if we need test data
DO $$
DECLARE
    profile_count INTEGER;
    sub_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO profile_count FROM public.profiles;
    SELECT COUNT(*) INTO sub_count FROM public.subscriptions;
    
    -- Only add test data if tables are nearly empty
    IF profile_count < 5 THEN
        -- The actual user data should come from auth.users
        -- Just update any existing profiles with more complete data
        UPDATE public.profiles
        SET 
            role = CASE 
                WHEN is_admin = TRUE THEN 'admin'
                ELSE 'user'
            END,
            is_paid = FALSE
        WHERE role IS NULL;
    END IF;
END $$;

-- 8. Verify the structure
SELECT 
    'Profiles' as table_name,
    COUNT(*) as row_count,
    COUNT(CASE WHEN email IS NOT NULL THEN 1 END) as with_email,
    COUNT(CASE WHEN is_admin = TRUE THEN 1 END) as admin_count
FROM public.profiles

UNION ALL

SELECT 
    'Subscriptions' as table_name,
    COUNT(*) as row_count,
    COUNT(CASE WHEN is_paid = TRUE THEN 1 END) as paid_count,
    0 as admin_count
FROM public.subscriptions

UNION ALL

SELECT 
    'Teams' as table_name,
    COUNT(*) as row_count,
    0 as with_email,
    0 as admin_count
FROM public.teams;
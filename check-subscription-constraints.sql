-- Check for duplicate subscriptions
SELECT 
  user_id,
  COUNT(*) as subscription_count,
  array_agg(id) as subscription_ids,
  array_agg(is_paid) as paid_status
FROM subscriptions
GROUP BY user_id
HAVING COUNT(*) > 1;

-- Check constraints on subscriptions table
SELECT 
  conname as constraint_name,
  contype as constraint_type,
  pg_get_constraintdef(oid) as definition
FROM pg_constraint
WHERE conrelid = 'subscriptions'::regclass;

-- If there are duplicates, clean them up
WITH duplicates AS (
  SELECT 
    user_id,
    id,
    is_paid,
    created_at,
    ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY is_paid DESC, created_at DESC) as rn
  FROM subscriptions
)
DELETE FROM subscriptions
WHERE id IN (
  SELECT id FROM duplicates WHERE rn > 1
);

-- Ensure the unique constraint exists
ALTER TABLE subscriptions
DROP CONSTRAINT IF EXISTS unique_user_id;

ALTER TABLE subscriptions
ADD CONSTRAINT unique_user_id UNIQUE (user_id);

-- Verify no more duplicates
SELECT 
  'Duplicate subscriptions after cleanup' as check_type,
  COUNT(*) as count
FROM (
  SELECT user_id
  FROM subscriptions
  GROUP BY user_id
  HAVING COUNT(*) > 1
) as dups;
# Performance Optimization Guide for Dugout Boss

## 🎯 Key Performance Metrics
- **Initial Load**: < 1 second
- **Data Fetch**: < 2 seconds
- **User Interactions**: < 100ms feedback
- **Page Transitions**: < 300ms

## 🚀 Implemented Optimizations

### 1. **Parallel Data Fetching** ✅
- Reduced 70-85 sequential queries to 6 parallel queries
- Load time improved from 20s to 2-3s
- See: `teamServiceOptimized.ts`

## 📋 Areas to Optimize Next

### 1. **Lineup Generation** (Currently slow)
```typescript
// Current: Sequential player evaluation
for (const player of players) {
  evaluateForPosition(player, position);
}

// Better: Parallel evaluation
const evaluations = await Promise.all(
  players.map(player => evaluateForPosition(player, position))
);
```

### 2. **Batch Operations** (CreateLineup, BatchLineupGeneration)
```typescript
// Instead of creating lineups one by one
for (const game of games) {
  await createLineup(game);
}

// Create all at once
const lineupPromises = games.map(game => createLineup(game));
await Promise.all(lineupPromises);
```

### 3. **Image Loading** (Team logos, player avatars)
```typescript
// Add lazy loading
<img loading="lazy" src={playerAvatar} />

// Or use Intersection Observer
const LazyImage = ({ src, alt }) => {
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef();
  
  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting) {
        setIsInView(true);
        observer.disconnect();
      }
    });
    
    if (imgRef.current) {
      observer.observe(imgRef.current);
    }
  }, []);
  
  return (
    <div ref={imgRef}>
      {isInView && <img src={src} alt={alt} />}
    </div>
  );
};
```

### 4. **Roster Updates** (TeamRoster page)
```typescript
// Current: Update entire team on each player change
await updateTeam(entireTeam);

// Better: Update only the changed player
await updatePlayer(changedPlayer);
```

### 5. **Search & Filters**
```typescript
// Add debouncing
const debouncedSearch = useMemo(
  () => debounce((searchTerm) => {
    performSearch(searchTerm);
  }, 300),
  []
);
```

## 🛠️ Implementation Priorities

### High Priority
1. **Add caching to all data fetches** 
   - Use `useTeamsCache` hook
   - Cache lineup data for 5 minutes
   - Invalidate on mutations

2. **Lazy load lineup details**
   - Only fetch innings/attendance when lineup is opened
   - Prefetch on hover for instant feel

3. **Optimize lineup generation algorithm**
   - Use Web Workers for heavy computation
   - Cache position evaluations

### Medium Priority
1. **Loading skeletons everywhere**
   - Replace spinners with content-shaped skeletons
   - Show partial data as it loads

2. **Optimistic UI updates**
   - Update UI immediately, sync with server in background
   - Rollback on error with clear messaging

3. **Virtual scrolling for long lists**
   - Use react-window for player lists > 50 items
   - Lazy render only visible rows

### Low Priority
1. **Service Worker for offline support**
2. **Image optimization and CDN**
3. **Bundle splitting by route**

## 📊 Measuring Performance

### Add Performance Monitoring
```typescript
// utils/performance.ts
export const measurePerformance = (name: string) => {
  const start = performance.now();
  
  return {
    end: () => {
      const duration = performance.now() - start;
      console.log(`⚡ ${name}: ${duration.toFixed(2)}ms`);
      
      // Send to analytics
      if (window.gtag) {
        window.gtag('event', 'timing_complete', {
          name,
          value: Math.round(duration)
        });
      }
    }
  };
};

// Usage
const perf = measurePerformance('lineup_generation');
await generateLineup();
perf.end(); // Logs: ⚡ lineup_generation: 234.56ms
```

## 🎯 Quick Wins

1. **Remove console.logs in production**
```typescript
if (process.env.NODE_ENV === 'production') {
  console.log = () => {};
}
```

2. **Compress localStorage data**
```typescript
import LZString from 'lz-string';

const saveToStorage = (key: string, data: any) => {
  const compressed = LZString.compress(JSON.stringify(data));
  localStorage.setItem(key, compressed);
};

const loadFromStorage = (key: string) => {
  const compressed = localStorage.getItem(key);
  if (!compressed) return null;
  return JSON.parse(LZString.decompress(compressed));
};
```

3. **Preload critical data**
```typescript
// In App.tsx
useEffect(() => {
  // Preload data user will likely need
  if (user) {
    prefetchTeams(user.id);
    prefetchPlayers(currentTeamId);
  }
}, [user, currentTeamId]);
```

## 🔧 Database Optimizations

1. **Add database indexes** (already done)
2. **Create materialized views for complex queries**
3. **Use database functions for heavy computations**

## 📱 Mobile Performance

1. **Reduce JavaScript bundle size**
   - Tree shake unused code
   - Lazy load heavy components

2. **Optimize for 3G connections**
   - Show essential content first
   - Progressive enhancement

3. **Reduce re-renders**
   - Use React.memo strategically
   - Split context providers

## 🎉 Expected Results

With all optimizations:
- **Initial load**: < 1s (from 3-5s)
- **Team switch**: < 500ms (from 2-3s)
- **Lineup generation**: < 2s (from 5-10s)
- **Search/filter**: Instant (< 100ms)
- **Smooth 60fps** scrolling and animations
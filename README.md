# Dugout Boss

A comprehensive web application for youth baseball and softball coaches to create fair, balanced lineups with automated player rotation and position management.

## Project Overview

**Repository**: https://github.com/canadian12345/diamond-lineup-guru
**Live Demo**: Available via demo mode on the application
**Status**: Active Development (MVP Phase)
**Version**: 1.0.0-beta

## Features

### ✅ Core Functionality (Working)
- **Team Management**: Create and manage multiple teams with player rosters
- **Player Management**: Add players with position restrictions and preferences
- **Lineup Creation**: Create game lineups with attendance tracking
- **Position Assignment**: Manual first inning setup with automated rotation
- **Quick Roster Adjust**: Last-minute attendance changes with smart lineup regeneration
- **Batting Orders**: Create and manage batting orders for lineups
- **Export Options**: PDF and CSV export for game day use
- **Demo Mode**: Fully functional demo with pre-populated data
- **User Authentication**: Secure login with Supabase Auth
- **Payment Integration**: Stripe-based subscription system

### 🚧 In Development
- **Admin Dashboard**: User and subscription management
- **Mobile Optimization**: Enhanced mobile experience
- **Advanced Rotation Rules**: More sophisticated rotation algorithms
- **Assistant Coach Access**: Multi-user team management

## Technology Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for build tooling
- **Tailwind CSS** for styling
- **shadcn/ui** component library
- **React Router** for navigation
- **React Hook Form** for form handling

### Backend
- **Supabase** for database and authentication
- **PostgreSQL** database with Row Level Security
- **Stripe** for payment processing
- **Edge Functions** for serverless logic

### Development Tools
- **ESLint** and **TypeScript** for code quality
- **Git** and **GitHub** for version control
- **npm** for package management

## Getting Started

### Prerequisites
- Node.js 16+ and npm
- Git

### Installation

```bash
# Clone the repository
git clone https://github.com/canadian12345/diamond-lineup-guru.git

# Navigate to project directory
cd diamond-lineup-guru

# Install dependencies
npm install

# Start development server
npm run dev
```

### Environment Setup

Create a `.env.local` file with:
```env
VITE_SUPABASE_URL=your-supabase-url
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
VITE_STRIPE_PUBLISHABLE_KEY=your-stripe-key
```

## Usage

### Demo Mode
Try the application without registration:
1. Visit the homepage
2. Click "Try Demo Mode"
3. Explore all features with pre-populated data

### Full Access
1. Sign up for an account
2. Subscribe for full access
3. Create your team and start managing lineups

## Documentation

- **[Technical Stack](./techstack.md)** - Detailed technology overview
- **[Backend Documentation](./backend.md)** - Database schema and API details
- **[Frontend Documentation](./frontend.md)** - Component architecture and UI details
- **[User Flow](./flow.md)** - Application user journeys
- **[Requirements](./requirements.md)** - Functional and technical requirements
- **[Product Requirements](./prd.md)** - Product vision and features
- **[Project Status](./status.md)** - Current development status

## Contributing

This is a private project. For development access, contact the repository owner.

## License

Private - All rights reserved

## Support

For support or questions, contact: <EMAIL>

import { createServiceClient } from "./src/integrations/supabase/node-client.js";
import { readFileSync } from 'fs';

const supabase = createServiceClient();

async function applyFix() {
  console.log('Applying profiles recursion fix...\n');

  try {
    // First, let's check current policies
    const { data: policies, error: policiesError } = await supabase
      .rpc('get_policies_for_table', { table_name: 'profiles' });
    
    console.log('Current profiles policies:', policies);

    // Read and execute the SQL file
    const sqlContent = readFileSync('./fix-profiles-recursion.sql', 'utf8');
    
    // Split SQL into individual statements
    const statements = sqlContent
      .split(';')
      .map(s => s.trim())
      .filter(s => s.length > 0 && !s.startsWith('--'));

    for (const statement of statements) {
      if (statement.includes('SELECT') && !statement.includes('CREATE')) {
        // Run SELECT statements to see results
        console.log('\nExecuting:', statement.substring(0, 50) + '...');
        const { data, error } = await supabase.rpc('execute_sql', { sql: statement });
        if (error) {
          console.error('Error:', error);
        } else {
          console.log('Result:', data);
        }
      }
    }

    console.log('\n✅ Fix applied! The infinite recursion in profiles table should be resolved.');
    console.log('\nPlease try refreshing your browser and see if teams load now.');
    
  } catch (error) {
    console.error('Error applying fix:', error);
  }
}

applyFix();
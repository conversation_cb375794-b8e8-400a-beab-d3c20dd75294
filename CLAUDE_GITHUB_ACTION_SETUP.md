# Setting Up Claude Code GitHub Action with OAuth

## Prerequisites
- GitHub repository with admin access
- Claude Max subscription
- <PERSON> Code CLI installed and logged in

## Step 1: Find Your Claude Credentials

### On macOS:
1. Open **Keychain Access** app
2. Search for "claude" in the search bar
3. Look for an entry like "claude.ai" or "claude"
4. Double-click the entry and click "Show Password"
5. You should find JSON data with access_token, refresh_token, and expires_at

### Alternative method:
1. Check for credentials file:
   ```bash
   cat ~/.claude/.credentials.json
   ```

2. If not there, try:
   ```bash
   cat ~/Library/Application\ Support/Claude/.credentials.json
   ```

3. Or search for it:
   ```bash
   find ~ -name ".credentials.json" -path "*/claude/*" 2>/dev/null
   ```

## Step 2: Add Secrets to GitHub Repository

1. Go to your repository on GitHub
2. Navigate to **Settings** → **Secrets and variables** → **Actions**
3. Click **New repository secret**
4. Add these three secrets:

   - **CLAUDE_ACCESS_TOKEN**: The access_token value from credentials
   - **CLAUDE_REFRESH_TOKEN**: The refresh_token value from credentials  
   - **CLAUDE_EXPIRES_AT**: The expires_at value from credentials

## Step 3: GitHub Workflow Setup

The workflow file has been created at `.github/workflows/claude-pr-review.yaml`

This workflow will:
- Trigger on pull request creation or updates
- Use the forked Claude Code action with OAuth authentication
- Review your PRs with Claude's assistance

## Step 4: Test the Setup

1. Create a new branch:
   ```bash
   git checkout -b test-claude-action
   ```

2. Make a small change:
   ```bash
   echo "# Test Claude Action" >> test-claude.md
   git add test-claude.md
   git commit -m "Test Claude GitHub Action"
   ```

3. Push and create a PR:
   ```bash
   git push origin test-claude-action
   ```

4. Go to GitHub and create a pull request
5. Watch the Actions tab to see Claude review your PR

## Troubleshooting

### If credentials aren't found:
1. Make sure you're logged into Claude Code:
   ```bash
   claude --login
   ```

2. After logging in, the credentials should be stored

### If the action fails:
1. Check the Actions logs for error messages
2. Verify all three secrets are properly set in GitHub
3. Ensure the tokens haven't expired
4. Try refreshing your Claude login

## Security Notes
- Never commit credentials to your repository
- GitHub secrets are encrypted and safe to use
- Rotate tokens periodically for security
- Consider using GitHub environments for additional security
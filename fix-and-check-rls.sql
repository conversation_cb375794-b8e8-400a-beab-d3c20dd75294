-- First, fix the existing subscriptions
UPDATE subscriptions
SET 
    is_paid = true,
    updated_at = NOW()
WHERE 
    tier IS NOT NULL 
    AND tier != ''
    AND is_paid = false
RETURNING user_id, is_paid, tier, team_limit;

-- Check RLS policies on subscriptions table
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies
WHERE schemaname = 'public' 
AND tablename = 'subscriptions'
ORDER BY policyname;
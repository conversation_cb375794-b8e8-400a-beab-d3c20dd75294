-- Check and Apply RLS Policies for Subscriptions Table

-- 1. First, check if <PERSON><PERSON> is enabled on the subscriptions table
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables
WHERE schemaname = 'public' 
AND tablename = 'subscriptions';

-- 2. Check existing RLS policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies
WHERE schemaname = 'public' 
AND tablename = 'subscriptions'
ORDER BY policyname;

-- 3. Fix any existing subscriptions with tier but is_paid = false
UPDATE subscriptions
SET 
    is_paid = true,
    updated_at = NOW()
WHERE 
    tier IS NOT NULL 
    AND tier != ''
    AND is_paid = false
RETURNING user_id, is_paid, tier, team_limit;

-- 4. Drop existing policies if they exist (to recreate cleanly)
DROP POLICY IF EXISTS "Users can view their own subscriptions" ON public.subscriptions;
DROP POLICY IF EXISTS "Users can insert their own subscriptions" ON public.subscriptions;
DROP POLICY IF EXISTS "Users can update their own subscriptions" ON public.subscriptions;
DROP POLICY IF EXISTS "Users can delete their own subscriptions" ON public.subscriptions;

-- 5. Ensure RLS is enabled
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;

-- 6. Create all necessary RLS policies
-- SELECT policy
CREATE POLICY "Users can view their own subscriptions" ON public.subscriptions
    FOR SELECT 
    USING (auth.uid() = user_id);

-- INSERT policy
CREATE POLICY "Users can insert their own subscriptions" ON public.subscriptions
    FOR INSERT 
    WITH CHECK (auth.uid() = user_id);

-- UPDATE policy  
CREATE POLICY "Users can update their own subscriptions" ON public.subscriptions
    FOR UPDATE 
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- DELETE policy
CREATE POLICY "Users can delete their own subscriptions" ON public.subscriptions
    FOR DELETE 
    USING (auth.uid() = user_id);

-- 7. Add admin policies for service role
-- Admin SELECT policy
CREATE POLICY "Service role can view all subscriptions" ON public.subscriptions
    FOR SELECT
    USING (auth.jwt() ->> 'role' = 'service_role');

-- Admin INSERT policy  
CREATE POLICY "Service role can insert any subscription" ON public.subscriptions
    FOR INSERT
    WITH CHECK (auth.jwt() ->> 'role' = 'service_role');

-- Admin UPDATE policy
CREATE POLICY "Service role can update any subscription" ON public.subscriptions
    FOR UPDATE
    USING (auth.jwt() ->> 'role' = 'service_role')
    WITH CHECK (auth.jwt() ->> 'role' = 'service_role');

-- Admin DELETE policy
CREATE POLICY "Service role can delete any subscription" ON public.subscriptions
    FOR DELETE
    USING (auth.jwt() ->> 'role' = 'service_role');

-- 8. Add comment for documentation
COMMENT ON TABLE public.subscriptions IS 
'Subscription records for user payments. All operations (SELECT/INSERT/UPDATE/DELETE) are restricted by RLS to ensure users can only access their own subscription data. Service role has full access for admin operations.';

-- 9. Verify the policies were created
SELECT 
    'Final Policy Check' as status,
    schemaname,
    tablename,
    policyname,
    cmd,
    permissive,
    roles
FROM pg_policies
WHERE schemaname = 'public' 
AND tablename = 'subscriptions'
ORDER BY policyname;

-- 10. Check subscription data
SELECT 
    'Subscription Data Check' as status,
    user_id,
    tier,
    is_paid,
    team_limit,
    created_at,
    updated_at
FROM subscriptions
ORDER BY created_at DESC
LIMIT 10;
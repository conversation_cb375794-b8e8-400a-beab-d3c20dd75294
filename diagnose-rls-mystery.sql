-- DIAGNOSE: Why are we still getting RLS errors when <PERSON><PERSON> is supposedly disabled?

-- 1. Check the ACTUAL RLS status directly from system tables
SELECT 
  schemaname,
  tablename,
  tablename::regclass::oid as table_oid,
  (SELECT relrowsecurity FROM pg_class WHERE oid = tablename::regclass::oid) as rls_enabled,
  (SELECT relforcerowsecurity FROM pg_class WHERE oid = tablename::regclass::oid) as rls_forced
FROM pg_tables
WHERE schemaname = 'public' 
  AND tablename IN ('profiles', 'subscriptions', 'teams');

-- 2. Check if there are ANY policies (even if <PERSON><PERSON> is "disabled")
SELECT 
  schemaname,
  tablename,
  COUNT(*) as policy_count,
  string_agg(policyname, ', ') as policy_names
FROM pg_policies
WHERE schemaname = 'public' 
  AND tablename IN ('profiles', 'subscriptions', 'teams')
GROUP BY schemaname, tablename;

-- 3. Check for any triggers that might be interfering
SELECT 
  trigger_schema,
  trigger_name,
  event_object_table,
  action_statement
FROM information_schema.triggers
WHERE event_object_schema = 'public'
  AND event_object_table IN ('profiles', 'subscriptions', 'teams');

-- 4. Check for any special permissions or grants
SELECT 
  grantee,
  table_schema,
  table_name,
  privilege_type
FROM information_schema.table_privileges
WHERE table_schema = 'public'
  AND table_name IN ('profiles', 'subscriptions', 'teams')
  AND grantee IN ('authenticated', 'anon')
ORDER BY table_name, grantee, privilege_type;

-- 5. Check if there are any column-level security policies
SELECT 
  table_name,
  column_name,
  privilege_type,
  grantee
FROM information_schema.column_privileges
WHERE table_schema = 'public'
  AND table_name IN ('profiles', 'subscriptions', 'teams')
  AND grantee IN ('authenticated', 'anon');

-- 6. Force a direct insert test as superuser
DO $$
DECLARE
  test_user_id uuid := gen_random_uuid();
  result text;
BEGIN
  -- Try to insert without any RLS context
  BEGIN
    INSERT INTO public.profiles (id, email, full_name) 
    VALUES (test_user_id::text, '<EMAIL>', 'Direct Test');
    
    DELETE FROM public.profiles WHERE id = test_user_id::text;
    
    RAISE NOTICE 'SUCCESS: Direct insert/delete worked';
  EXCEPTION
    WHEN OTHERS THEN
      RAISE NOTICE 'FAILED: Direct insert failed with: %', SQLERRM;
  END;
END $$;

-- 7. Check for any database-level settings
SELECT 
  name,
  setting,
  source
FROM pg_settings
WHERE name LIKE '%row%security%'
   OR name LIKE '%rls%';

-- 8. Check the exact error by attempting an insert with detailed context
DO $$
DECLARE
  current_user_info text;
BEGIN
  -- Get current execution context
  SELECT format('Current user: %s, Session user: %s, Auth.uid(): %s, Auth.role(): %s',
    current_user,
    session_user,
    COALESCE(auth.uid()::text, 'NULL'),
    COALESCE(auth.role(), 'NULL')
  ) INTO current_user_info;
  
  RAISE NOTICE '%', current_user_info;
END $$;
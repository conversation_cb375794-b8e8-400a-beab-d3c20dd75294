<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Game Result Feature</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            max-width: 800px;
            margin: 40px auto;
            padding: 20px;
            line-height: 1.6;
        }
        .status-box {
            background: #f0f0f0;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .enabled { background: #d4edda; }
        .disabled { background: #f8d7da; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .danger {
            background: #dc3545;
        }
        .danger:hover {
            background: #c82333;
        }
        code {
            background: #f5f5f5;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🎮 Debug Game Result Feature</h1>
    
    <div id="status" class="status-box">
        Checking status...
    </div>

    <h2>Quick Fix</h2>
    <p>Click this button to enable the game result feature:</p>
    <button onclick="enableFeature()">Enable Game Result Feature</button>
    <button class="danger" onclick="disableFeature()">Disable Feature</button>

    <h2>What's Happening?</h2>
    <p>The Win/Loss toggle feature checks for two things:</p>
    <ol>
        <li>Whether the <code>game_result</code> column exists in the database</li>
        <li>Whether a localStorage flag is set: <code>game_result_migration_applied</code></li>
    </ol>

    <h2>Manual Steps to Fix</h2>
    <ol>
        <li><strong>Run the database migration</strong> in Supabase SQL Editor:
            <pre>-- Add game_result column to lineups table
ALTER TABLE lineups 
ADD COLUMN IF NOT EXISTS game_result TEXT CHECK (game_result IN ('win', 'loss', null));

-- Add index for performance
CREATE INDEX IF NOT EXISTS idx_lineups_game_result ON lineups(game_result);</pre>
        </li>
        
        <li><strong>Enable the feature in your browser</strong> by running this in the console:
            <pre>localStorage.setItem('game_result_migration_applied', 'true');</pre>
        </li>
        
        <li><strong>Clear the cache</strong> (optional) if the feature still doesn't work:
            <button onclick="clearCache()">Clear Feature Cache</button>
        </li>
        
        <li><strong>Refresh the page</strong> to see the changes take effect.</li>
    </ol>

    <h2>Debug Information</h2>
    <div id="debug-info"></div>

    <script>
        function checkStatus() {
            const migrationApplied = localStorage.getItem('game_result_migration_applied');
            const statusDiv = document.getElementById('status');
            const debugDiv = document.getElementById('debug-info');
            
            if (migrationApplied === 'true') {
                statusDiv.className = 'status-box enabled';
                statusDiv.innerHTML = '✅ Game Result feature is ENABLED';
            } else {
                statusDiv.className = 'status-box disabled';
                statusDiv.innerHTML = '❌ Game Result feature is DISABLED';
            }
            
            // Show all relevant localStorage items
            const relevantKeys = [
                'game_result_migration_applied',
                'demo_mode',
                'supabase.auth.token'
            ];
            
            let debugHtml = '<h3>localStorage values:</h3><ul>';
            relevantKeys.forEach(key => {
                const value = localStorage.getItem(key);
                if (value) {
                    debugHtml += `<li><code>${key}</code>: ${value.substring(0, 50)}${value.length > 50 ? '...' : ''}</li>`;
                }
            });
            debugHtml += '</ul>';
            
            debugDiv.innerHTML = debugHtml;
        }

        function enableFeature() {
            localStorage.setItem('game_result_migration_applied', 'true');
            checkStatus();
            alert('✅ Game Result feature enabled!\n\nPlease refresh the Dugout Boss app to see the Win/Loss toggle.');
        }

        function disableFeature() {
            localStorage.removeItem('game_result_migration_applied');
            checkStatus();
            alert('Feature disabled. Refresh the app to apply changes.');
        }

        function clearCache() {
            // Clear any cached values in sessionStorage too
            sessionStorage.clear();
            
            // Also try to clear the module-level cache by reloading
            if (confirm('This will clear all caches and reload the page. Continue?')) {
                localStorage.setItem('game_result_migration_applied', 'true');
                location.reload(true);
            }
        }

        // Check status on load
        checkStatus();
    </script>
</body>
</html>
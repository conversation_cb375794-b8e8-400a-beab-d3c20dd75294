-- CRITICAL FIX: Users seeing wrong data (noahfleming2007@gmail.<NAME_EMAIL> data)
-- This is a critical security issue that needs immediate resolution

-- First, let's check the current situation
DO $$
DECLARE
    v_user1_id uuid;
    v_user2_id uuid;
    v_user1_teams integer;
    v_user2_teams integer;
BEGIN
    -- Get user IDs
    SELECT id INTO v_user1_id FROM auth.users WHERE email = '<EMAIL>';
    SELECT id INTO v_user2_id FROM auth.users WHERE email = '<EMAIL>';
    
    -- Count teams for each user
    SELECT COUNT(*) INTO v_user1_teams FROM public.teams WHERE user_id = v_user1_id;
    SELECT COUNT(*) INTO v_user2_teams FROM public.teams WHERE user_id = v_user2_id;
    
    RAISE NOTICE 'User <EMAIL> (ID: %) has % teams', v_user1_id, v_user1_teams;
    RAISE NOTICE 'User <EMAIL> (ID: %) has % teams', v_user2_id, v_user2_teams;
END $$;

-- Step 1: Ensure RLS is enabled on ALL tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.players ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.lineups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.lineup_innings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.lineup_attendance ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.batting_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.rotation_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;

-- Step 2: Drop ALL existing policies to start fresh
DO $$
DECLARE
    pol record;
BEGIN
    FOR pol IN 
        SELECT schemaname, tablename, policyname 
        FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename IN ('profiles', 'teams', 'players', 'lineups', 'lineup_innings', 
                         'lineup_attendance', 'batting_orders', 'rotation_rules', 'subscriptions')
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON %I.%I', pol.policyname, pol.schemaname, pol.tablename);
    END LOOP;
END $$;

-- Step 3: Create STRICT isolation policies

-- PROFILES - Users can ONLY see/manage their own profile
CREATE POLICY "profiles_select_own" ON public.profiles FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "profiles_insert_own" ON public.profiles FOR INSERT WITH CHECK (user_id = auth.uid());
CREATE POLICY "profiles_update_own" ON public.profiles FOR UPDATE USING (user_id = auth.uid());
CREATE POLICY "profiles_delete_own" ON public.profiles FOR DELETE USING (user_id = auth.uid());

-- TEAMS - Users can ONLY see/manage their own teams
CREATE POLICY "teams_select_own" ON public.teams FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "teams_insert_own" ON public.teams FOR INSERT WITH CHECK (user_id = auth.uid());
CREATE POLICY "teams_update_own" ON public.teams FOR UPDATE USING (user_id = auth.uid());
CREATE POLICY "teams_delete_own" ON public.teams FOR DELETE USING (user_id = auth.uid());

-- PLAYERS - Users can ONLY see/manage players in their own teams
CREATE POLICY "players_select_own" ON public.players FOR SELECT 
    USING (team_id IN (SELECT id FROM public.teams WHERE user_id = auth.uid()));
CREATE POLICY "players_insert_own" ON public.players FOR INSERT 
    WITH CHECK (team_id IN (SELECT id FROM public.teams WHERE user_id = auth.uid()));
CREATE POLICY "players_update_own" ON public.players FOR UPDATE 
    USING (team_id IN (SELECT id FROM public.teams WHERE user_id = auth.uid()));
CREATE POLICY "players_delete_own" ON public.players FOR DELETE 
    USING (team_id IN (SELECT id FROM public.teams WHERE user_id = auth.uid()));

-- LINEUPS - Users can ONLY see/manage lineups for their own teams
CREATE POLICY "lineups_select_own" ON public.lineups FOR SELECT 
    USING (team_id IN (SELECT id FROM public.teams WHERE user_id = auth.uid()));
CREATE POLICY "lineups_insert_own" ON public.lineups FOR INSERT 
    WITH CHECK (team_id IN (SELECT id FROM public.teams WHERE user_id = auth.uid()));
CREATE POLICY "lineups_update_own" ON public.lineups FOR UPDATE 
    USING (team_id IN (SELECT id FROM public.teams WHERE user_id = auth.uid()));
CREATE POLICY "lineups_delete_own" ON public.lineups FOR DELETE 
    USING (team_id IN (SELECT id FROM public.teams WHERE user_id = auth.uid()));

-- LINEUP_INNINGS - Users can ONLY see/manage innings for their own lineups
CREATE POLICY "innings_select_own" ON public.lineup_innings FOR SELECT 
    USING (lineup_id IN (
        SELECT l.id FROM public.lineups l 
        JOIN public.teams t ON t.id = l.team_id 
        WHERE t.user_id = auth.uid()
    ));
CREATE POLICY "innings_insert_own" ON public.lineup_innings FOR INSERT 
    WITH CHECK (lineup_id IN (
        SELECT l.id FROM public.lineups l 
        JOIN public.teams t ON t.id = l.team_id 
        WHERE t.user_id = auth.uid()
    ));
CREATE POLICY "innings_update_own" ON public.lineup_innings FOR UPDATE 
    USING (lineup_id IN (
        SELECT l.id FROM public.lineups l 
        JOIN public.teams t ON t.id = l.team_id 
        WHERE t.user_id = auth.uid()
    ));
CREATE POLICY "innings_delete_own" ON public.lineup_innings FOR DELETE 
    USING (lineup_id IN (
        SELECT l.id FROM public.lineups l 
        JOIN public.teams t ON t.id = l.team_id 
        WHERE t.user_id = auth.uid()
    ));

-- LINEUP_ATTENDANCE - Users can ONLY see/manage attendance for their own lineups
CREATE POLICY "attendance_select_own" ON public.lineup_attendance FOR SELECT 
    USING (lineup_id IN (
        SELECT l.id FROM public.lineups l 
        JOIN public.teams t ON t.id = l.team_id 
        WHERE t.user_id = auth.uid()
    ));
CREATE POLICY "attendance_insert_own" ON public.lineup_attendance FOR INSERT 
    WITH CHECK (lineup_id IN (
        SELECT l.id FROM public.lineups l 
        JOIN public.teams t ON t.id = l.team_id 
        WHERE t.user_id = auth.uid()
    ));
CREATE POLICY "attendance_update_own" ON public.lineup_attendance FOR UPDATE 
    USING (lineup_id IN (
        SELECT l.id FROM public.lineups l 
        JOIN public.teams t ON t.id = l.team_id 
        WHERE t.user_id = auth.uid()
    ));
CREATE POLICY "attendance_delete_own" ON public.lineup_attendance FOR DELETE 
    USING (lineup_id IN (
        SELECT l.id FROM public.lineups l 
        JOIN public.teams t ON t.id = l.team_id 
        WHERE t.user_id = auth.uid()
    ));

-- BATTING_ORDERS - Users can ONLY see/manage batting orders for their own lineups
CREATE POLICY "batting_select_own" ON public.batting_orders FOR SELECT 
    USING (lineup_id IN (
        SELECT l.id FROM public.lineups l 
        JOIN public.teams t ON t.id = l.team_id 
        WHERE t.user_id = auth.uid()
    ));
CREATE POLICY "batting_insert_own" ON public.batting_orders FOR INSERT 
    WITH CHECK (lineup_id IN (
        SELECT l.id FROM public.lineups l 
        JOIN public.teams t ON t.id = l.team_id 
        WHERE t.user_id = auth.uid()
    ));
CREATE POLICY "batting_update_own" ON public.batting_orders FOR UPDATE 
    USING (lineup_id IN (
        SELECT l.id FROM public.lineups l 
        JOIN public.teams t ON t.id = l.team_id 
        WHERE t.user_id = auth.uid()
    ));
CREATE POLICY "batting_delete_own" ON public.batting_orders FOR DELETE 
    USING (lineup_id IN (
        SELECT l.id FROM public.lineups l 
        JOIN public.teams t ON t.id = l.team_id 
        WHERE t.user_id = auth.uid()
    ));

-- ROTATION_RULES - Users can ONLY see/manage rotation rules for their own teams
CREATE POLICY "rotation_select_own" ON public.rotation_rules FOR SELECT 
    USING (team_id IN (SELECT id FROM public.teams WHERE user_id = auth.uid()));
CREATE POLICY "rotation_insert_own" ON public.rotation_rules FOR INSERT 
    WITH CHECK (team_id IN (SELECT id FROM public.teams WHERE user_id = auth.uid()));
CREATE POLICY "rotation_update_own" ON public.rotation_rules FOR UPDATE 
    USING (team_id IN (SELECT id FROM public.teams WHERE user_id = auth.uid()));
CREATE POLICY "rotation_delete_own" ON public.rotation_rules FOR DELETE 
    USING (team_id IN (SELECT id FROM public.teams WHERE user_id = auth.uid()));

-- SUBSCRIPTIONS - Users can ONLY see/manage their own subscription
CREATE POLICY "subscriptions_select_own" ON public.subscriptions FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "subscriptions_insert_own" ON public.subscriptions FOR INSERT WITH CHECK (user_id = auth.uid());
CREATE POLICY "subscriptions_update_own" ON public.subscriptions FOR UPDATE USING (user_id = auth.uid());

-- Step 4: Add admin overrides (if needed)
-- Create function to check if user is admin
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.admin_users 
        WHERE user_id = auth.uid() 
        AND is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add admin policies for teams (example - add for other tables if needed)
CREATE POLICY "teams_admin_all" ON public.teams FOR ALL USING (is_admin());
CREATE POLICY "players_admin_all" ON public.players FOR ALL USING (is_admin());
CREATE POLICY "lineups_admin_all" ON public.lineups FOR ALL USING (is_admin());

-- Step 5: Verify the fix
DO $$
DECLARE
    v_user1_id uuid;
    v_user2_id uuid;
    v_test_result text;
BEGIN
    -- Get user IDs
    SELECT id INTO v_user1_id FROM auth.users WHERE email = '<EMAIL>';
    SELECT id INTO v_user2_id FROM auth.users WHERE email = '<EMAIL>';
    
    -- Test what each user would see
    RAISE NOTICE '';
    RAISE NOTICE '=== TESTING DATA ISOLATION ===';
    
    -- Test 1: Can user1 see user2's teams?
    PERFORM 1 FROM public.teams WHERE user_id = v_user2_id;
    IF FOUND THEN
        RAISE WARNING 'CRITICAL: RLS may not be working - user data visible across accounts!';
    ELSE
        RAISE NOTICE 'GOOD: Users cannot see each others teams directly';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE 'Data isolation policies have been applied.';
    RAISE NOTICE 'Each user will now ONLY see their own data.';
    RAISE NOTICE '';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Clear all browser localStorage/sessionStorage';
    RAISE NOTICE '2. Sign out and sign back in';
    RAISE NOTICE '3. Test that each user only sees their own data';
    
END $$;

-- Step 6: Create fresh profile/subscription for noahfleming2007 if missing
DO $$
DECLARE
    v_user_id uuid;
    v_has_profile boolean;
    v_has_subscription boolean;
BEGIN
    -- Get user ID
    SELECT id INTO v_user_id FROM auth.users WHERE email = '<EMAIL>';
    
    IF v_user_id IS NOT NULL THEN
        -- Check if profile exists
        SELECT EXISTS(SELECT 1 FROM public.profiles WHERE user_id = v_user_id) INTO v_has_profile;
        
        IF NOT v_has_profile THEN
            INSERT INTO public.profiles (user_id, email, full_name, created_at, updated_at)
            VALUES (v_user_id, '<EMAIL>', 'Noah Fleming', NOW(), NOW());
            RAISE NOTICE 'Created <NAME_EMAIL>';
        END IF;
        
        -- Check if subscription exists
        SELECT EXISTS(SELECT 1 FROM public.subscriptions WHERE user_id = v_user_id) INTO v_has_subscription;
        
        IF NOT v_has_subscription THEN
            INSERT INTO public.subscriptions (user_id, status, tier, paid, team_limit, created_at, updated_at)
            VALUES (v_user_id, 'active', 'free', false, 0, NOW(), NOW());
            RAISE NOTICE 'Created <NAME_EMAIL>';
        END IF;
    END IF;
END $$;
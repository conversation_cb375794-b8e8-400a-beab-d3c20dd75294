# Apply Database Migration for Series Metadata

## Issue
The batch game creation is failing because the database doesn't have the series metadata columns yet.

## Quick Fix
To resolve the "column does not exist" errors:

### Steps:
1. Go to your **Supabase Dashboard**
2. Navigate to **SQL Editor** 
3. **Copy ONLY the SQL below** (not the markdown):

```sql
ALTER TABLE lineups 
ADD COLUMN IF NOT EXISTS series_title TEXT,
ADD COLUMN IF NOT EXISTS series_id TEXT,
ADD COLUMN IF NOT EXISTS game_number INTEGER,
ADD COLUMN IF NOT EXISTS total_games_in_series INTEGER;

CREATE INDEX IF NOT EXISTS idx_lineups_series_id ON lineups(series_id);
CREATE INDEX IF NOT EXISTS idx_lineups_series_title ON lineups(series_title);

COMMENT ON COLUMN lineups.series_title IS 'Title of the series this game belongs to (e.g., "Weekend Tournament")';
COMMENT ON COLUMN lineups.series_id IS 'Unique identifier for grouping games in a series';
COMMENT ON COLUMN lineups.game_number IS 'The sequence number of this game within the series (e.g., 1, 2, 3)';
COMMENT ON COLUMN lineups.total_games_in_series IS 'Total number of games in this series';
```

4. **Click "Run"**
5. **Refresh your app** and try creating batch games again

## What This Fixes
- ✅ Series title will be saved and displayed on dashboard
- ✅ "View Series" navigation will work properly  
- ✅ Series grouping will use actual series IDs instead of creation time
- ✅ No more "column does not exist" errors
- ✅ Better database performance with indexes

## Alternative: Use the SQL file
You can also copy the contents of `SERIES_METADATA_MIGRATION.sql` file which contains the same SQL.
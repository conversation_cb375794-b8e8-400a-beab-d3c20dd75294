import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing environment variables. Please check your .env.local file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function debugUserSubscription(email) {
  console.log(`\nDebugging subscription for: ${email}\n`);

  try {
    // 1. Find the user
    console.log('1. Looking up user in profiles...');
    const { data: profiles, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('email', email);

    if (profileError) {
      console.error('Error fetching profile:', profileError);
      return;
    }

    if (!profiles || profiles.length === 0) {
      console.error('❌ No profile found for this email');
      return;
    }

    const profile = profiles[0];
    console.log('✅ Found profile:', {
      id: profile.id,
      email: profile.email,
      full_name: profile.full_name,
      is_admin: profile.is_admin
    });

    // 2. Check subscriptions
    console.log('\n2. Checking subscriptions...');
    const { data: subscriptions, error: subError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', profile.id);

    if (subError) {
      console.error('Error fetching subscriptions:', subError);
      return;
    }

    if (!subscriptions || subscriptions.length === 0) {
      console.log('❌ No subscriptions found for this user');
      console.log('\n💡 Creating a paid subscription for this user...');
      
      // Create a subscription
      const { data: newSub, error: createError } = await supabase
        .from('subscriptions')
        .insert({
          user_id: profile.id,
          is_paid: true,
          amount: 4900,
          currency: 'usd',
          payment_date: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          tier: 'starter',
          team_limit: 1
        })
        .select()
        .single();

      if (createError) {
        console.error('❌ Failed to create subscription:', createError);
      } else {
        console.log('✅ Created subscription:', newSub);
      }
      return;
    }

    console.log(`✅ Found ${subscriptions.length} subscription(s):`);
    subscriptions.forEach((sub, index) => {
      console.log(`\nSubscription ${index + 1}:`, {
        id: sub.id,
        user_id: sub.user_id,
        is_paid: sub.is_paid,
        amount: sub.amount,
        currency: sub.currency,
        tier: sub.tier,
        team_limit: sub.team_limit,
        payment_date: sub.payment_date,
        created_at: sub.created_at,
        expires_at: sub.expires_at
      });
    });

    // 3. Check if any are paid
    const paidSubs = subscriptions.filter(s => s.is_paid === true);
    if (paidSubs.length === 0) {
      console.log('\n❌ No PAID subscriptions found');
      
      // Update the first subscription to be paid
      if (subscriptions.length > 0) {
        console.log('\n💡 Updating first subscription to be paid...');
        const { error: updateError } = await supabase
          .from('subscriptions')
          .update({
            is_paid: true,
            amount: 4900,
            currency: 'usd',
            payment_date: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            tier: 'starter',
            team_limit: 1
          })
          .eq('id', subscriptions[0].id);

        if (updateError) {
          console.error('❌ Failed to update subscription:', updateError);
        } else {
          console.log('✅ Updated subscription to paid status');
        }
      }
    } else {
      console.log(`\n✅ Found ${paidSubs.length} PAID subscription(s)`);
      
      // Check for missing fields
      const needsUpdate = paidSubs.some(sub => !sub.tier || !sub.team_limit);
      if (needsUpdate) {
        console.log('\n⚠️  Some paid subscriptions are missing tier/team_limit fields');
        console.log('💡 Updating subscriptions with missing fields...');
        
        for (const sub of paidSubs) {
          if (!sub.tier || !sub.team_limit) {
            const { error: updateError } = await supabase
              .from('subscriptions')
              .update({
                tier: sub.tier || 'starter',
                team_limit: sub.team_limit || 1,
                updated_at: new Date().toISOString()
              })
              .eq('id', sub.id);

            if (updateError) {
              console.error(`❌ Failed to update subscription ${sub.id}:`, updateError);
            } else {
              console.log(`✅ Updated subscription ${sub.id} with tier/team_limit`);
            }
          }
        }
      }
    }

    // 4. Test the query that checkPaymentStatus uses
    console.log('\n3. Testing the exact query from checkPaymentStatus...');
    const { data: paymentCheck, error: paymentError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', profile.id)
      .eq('is_paid', true);

    if (paymentError) {
      console.error('❌ Payment check query failed:', paymentError);
    } else {
      console.log(`✅ Payment check query returned ${paymentCheck?.length || 0} results`);
      if (paymentCheck && paymentCheck.length > 0) {
        console.log('This user SHOULD be recognized as paid');
      } else {
        console.log('This user will NOT be recognized as paid');
      }
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Get email from command line
const email = process.argv[2];

if (!email) {
  console.log('Usage: node debug-user-subscription.js <email>');
  console.log('Example: node debug-user-subscription.js "<EMAIL>"');
  process.exit(1);
}

debugUserSubscription(email);
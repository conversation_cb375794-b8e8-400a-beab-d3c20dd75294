-- Comprehensive fix for user signup flow RLS policies

-- 1. Fix profiles table to allow new users to create their profile
DROP POLICY IF EXISTS "profiles_select" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update" ON public.profiles;
DROP POLICY IF EXISTS "profiles_delete" ON public.profiles;

-- More permissive profiles policies
CREATE POLICY "profiles_select" ON public.profiles
  FOR SELECT USING (true);  -- Anyone can read profiles

CREATE POLICY "profiles_insert" ON public.profiles
  FOR INSERT WITH CHECK (
    -- Allow users to create their own profile
    auth.uid()::text = id
  );

CREATE POLICY "profiles_update" ON public.profiles
  FOR UPDATE USING (
    -- Users can update their own profile
    auth.uid()::text = id
    OR auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

CREATE POLICY "profiles_delete" ON public.profiles
  FOR DELETE USING (
    -- Only admins can delete profiles
    auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

-- 2. Fix subscriptions table
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;

-- Drop all existing policies
DO $$
DECLARE
  policy record;
BEGIN
  FOR policy IN 
    SELECT policyname 
    FROM pg_policies 
    WHERE schemaname = 'public' AND tablename = 'subscriptions'
  LOOP
    EXECUTE format('DROP POLICY IF EXISTS %I ON public.subscriptions', policy.policyname);
  END LOOP;
END $$;

-- Create new policies
CREATE POLICY "subscriptions_select" ON public.subscriptions
  FOR SELECT USING (
    auth.uid() = user_id
    OR auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

CREATE POLICY "subscriptions_insert" ON public.subscriptions
  FOR INSERT WITH CHECK (
    -- Users can create their own subscription
    auth.uid() = user_id
  );

CREATE POLICY "subscriptions_update" ON public.subscriptions
  FOR UPDATE USING (
    auth.uid() = user_id
    OR auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

CREATE POLICY "subscriptions_delete" ON public.subscriptions
  FOR DELETE USING (
    auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

-- 3. Ensure teams policies are correct
DROP POLICY IF EXISTS "teams_select" ON public.teams;
DROP POLICY IF EXISTS "teams_insert" ON public.teams;
DROP POLICY IF EXISTS "teams_update" ON public.teams;
DROP POLICY IF EXISTS "teams_delete" ON public.teams;

CREATE POLICY "teams_select" ON public.teams
  FOR SELECT USING (
    auth.uid() = user_id 
    OR auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

CREATE POLICY "teams_insert" ON public.teams
  FOR INSERT WITH CHECK (
    -- Users can only create teams for themselves
    auth.uid() = user_id
  );

CREATE POLICY "teams_update" ON public.teams
  FOR UPDATE USING (
    auth.uid() = user_id 
    OR auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

CREATE POLICY "teams_delete" ON public.teams
  FOR DELETE USING (
    auth.uid() = user_id 
    OR auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

-- 4. Create a function to help with profile creation during signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Create profile for new user
  INSERT INTO public.profiles (id, email, full_name, role, is_admin)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
    CASE 
      WHEN NEW.email IN ('<EMAIL>', '<EMAIL>') THEN 'admin'
      ELSE 'user'
    END,
    NEW.email IN ('<EMAIL>', '<EMAIL>')
  )
  ON CONFLICT (id) DO UPDATE
  SET 
    email = EXCLUDED.email,
    updated_at = NOW();

  -- Create default subscription for new user
  INSERT INTO public.subscriptions (user_id, is_paid, tier, team_limit)
  VALUES (
    NEW.id,
    false,
    'starter',
    0
  )
  ON CONFLICT (user_id) DO NOTHING;

  RETURN NEW;
END;
$$;

-- 5. Create trigger for automatic profile creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 6. Grant necessary permissions
GRANT ALL ON public.profiles TO authenticated;
GRANT ALL ON public.subscriptions TO authenticated;
GRANT ALL ON public.teams TO authenticated;
GRANT EXECUTE ON FUNCTION public.handle_new_user() TO postgres;

-- 7. Fix any existing users without profiles
INSERT INTO public.profiles (id, email, full_name, role, is_admin)
SELECT 
  id,
  email,
  COALESCE(raw_user_meta_data->>'full_name', email),
  CASE 
    WHEN email IN ('<EMAIL>', '<EMAIL>') THEN 'admin'
    ELSE 'user'
  END,
  email IN ('<EMAIL>', '<EMAIL>')
FROM auth.users
WHERE id NOT IN (SELECT id::uuid FROM public.profiles)
ON CONFLICT (id) DO NOTHING;

-- 8. Fix any existing users without subscriptions
INSERT INTO public.subscriptions (user_id, is_paid, tier, team_limit)
SELECT 
  id,
  false,
  'starter',
  0
FROM auth.users
WHERE id NOT IN (SELECT user_id FROM public.subscriptions)
ON CONFLICT (user_id) DO NOTHING;

-- Final verification
SELECT 
  'Total users' as metric,
  COUNT(*) as count
FROM auth.users
UNION ALL
SELECT 
  'Users with profiles' as metric,
  COUNT(*) as count
FROM public.profiles
UNION ALL
SELECT 
  'Users with subscriptions' as metric,
  COUNT(*) as count
FROM public.subscriptions;
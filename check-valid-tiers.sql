-- Check what the valid_tier constraint allows
SELECT 
    conname as constraint_name,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint
WHERE conrelid = 'subscriptions'::regclass
AND contype = 'c'
AND conname LIKE '%tier%';

-- Check column information
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public' 
AND table_name = 'subscriptions'
AND column_name IN ('tier', 'subscription_tier');

-- Check what tiers exist in the database
SELECT DISTINCT tier, subscription_tier, COUNT(*) as count
FROM subscriptions
GROUP BY tier, subscription_tier
ORDER BY count DESC;

-- Look at a sample of existing subscriptions
SELECT 
    user_id,
    is_paid,
    tier,
    subscription_tier,
    team_limit,
    created_at
FROM subscriptions
WHERE is_paid = true
LIMIT 5;
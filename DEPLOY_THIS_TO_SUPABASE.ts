// COPY THIS ENTIRE FILE TO YOUR SUPABASE EDGE FUNCTION: send-welcome-email

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.7'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Basic validation
    const authHeader = req.headers.get('authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Parse request body first to check what we're receiving
    let body;
    try {
      const text = await req.text();
      console.log('Raw request body:', text);
      body = JSON.parse(text);
    } catch (e) {
      console.error('Failed to parse request body:', e);
      return new Response(
        JSON.stringify({ error: 'Invalid JSON in request body' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const { email, name, temporaryPassword } = body;
    console.log('Parsed data:', { email, name, hasPassword: !!temporaryPassword });

    if (!email || !name || !temporaryPassword) {
      return new Response(
        JSON.stringify({ 
          error: 'Missing required fields',
          received: { email: !!email, name: !!name, temporaryPassword: !!temporaryPassword }
        }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check environment variables
    const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')
    if (!RESEND_API_KEY) {
      console.error('RESEND_API_KEY environment variable is not set');
      return new Response(
        JSON.stringify({ 
          error: 'Email service not configured',
          details: 'RESEND_API_KEY is missing from Edge Function secrets'
        }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Skip admin check for now to isolate the issue
    console.log('Skipping admin check for debugging...');

    // Simple email content
    const origin = req.headers.get('origin') || 'https://dugoutboss.com'
    const loginUrl = `${origin}/signin`

    const htmlContent = `
<html>
<body>
<h1>Welcome to Dugout Boss!</h1>
<p>Hi ${name},</p>
<p>Your account has been created with the following credentials:</p>
<p><strong>Email:</strong> ${email}<br>
<strong>Password:</strong> ${temporaryPassword}</p>
<p>Please <a href="${loginUrl}">login here</a> and change your password.</p>
</body>
</html>`

    const textContent = `Welcome to Dugout Boss!

Hi ${name},

Your account has been created with the following credentials:
Email: ${email}
Password: ${temporaryPassword}

Please login at ${loginUrl} and change your password.

Best regards,
The Dugout Boss Team`

    // Try to send email
    console.log('Attempting to send email via Resend...');
    
    try {
      const resendResponse = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${RESEND_API_KEY}`,
        },
        body: JSON.stringify({
          from: 'Dugout Boss <<EMAIL>>',
          to: [email],
          subject: 'Welcome to Dugout Boss',
          html: htmlContent,
          text: textContent,
        }),
      })

      const responseText = await resendResponse.text()
      console.log('Resend response:', resendResponse.status, responseText)

      if (!resendResponse.ok) {
        return new Response(
          JSON.stringify({ 
            error: 'Resend API error',
            status: resendResponse.status,
            details: responseText
          }),
          { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }

      const result = JSON.parse(responseText)
      return new Response(
        JSON.stringify({ 
          success: true, 
          messageId: result.id,
          debug: 'Email sent successfully'
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )

    } catch (fetchError) {
      console.error('Error calling Resend:', fetchError)
      return new Response(
        JSON.stringify({ 
          error: 'Failed to call Resend API',
          details: fetchError.message
        }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

  } catch (error) {
    console.error('Unexpected error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error.message,
        stack: error.stack
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
#!/usr/bin/env tsx

/**
 * Comprehensive Lineup Generation Test Suite
 * 
 * This test directly imports and tests the lineup generation algorithm
 * using tsx to handle TypeScript compilation.
 */

// Mock environment variables
process.env.VITE_SUPABASE_URL = 'http://localhost:54321';
process.env.VITE_SUPABASE_ANON_KEY = 'test-key';

// Mock import.meta.env for the supabase client
(globalThis as any).import = {
  meta: {
    env: {
      VITE_SUPABASE_URL: 'http://localhost:54321',
      VITE_SUPABASE_ANON_KEY: 'test-key'
    }
  }
};

import {
  generateCompleteLineup,
  validateLineup,
  LineupRules
} from './src/lib/utils-enhanced';
import { Player, InningLineup } from './src/contexts/TeamContext';

// Test data - Noah's U15 Selects team with realistic position restrictions and ratings
const NOAH_SELECTS_PLAYERS: Player[] = [
  {
    id: '1', name: '<PERSON>', jerseyNumber: 1, attendance: 'present',
    positionRestrictions: ['Pitcher'],
    positionRatings: { pitcher: 5, firstBase: 3, leftField: 2 },
    starPositions: ['pitcher']
  },
  {
    id: '2', name: '<PERSON> <PERSON>', jerseyNumber: 2, attendance: 'present',
    positionRestrictions: ['Catcher'],
    positionRatings: { catcher: 5, thirdBase: 4, rightField: 3 },
    starPositions: ['catcher']
  },
  {
    id: '3', name: 'Charlie Kim', jerseyNumber: 3, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { shortstop: 5, secondBase: 4, centerField: 3 },
    starPositions: ['shortstop']
  },
  {
    id: '4', name: 'David Chen', jerseyNumber: 4, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { firstBase: 4, thirdBase: 3, leftField: 3 },
    starPositions: []
  },
  {
    id: '5', name: 'Emma Wilson', jerseyNumber: 5, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { secondBase: 4, shortstop: 3, rightField: 3 },
    starPositions: []
  },
  {
    id: '6', name: 'Frank Miller', jerseyNumber: 6, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { thirdBase: 4, firstBase: 3, centerField: 2 },
    starPositions: []
  },
  {
    id: '7', name: 'Grace Lee', jerseyNumber: 7, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { leftField: 4, centerField: 4, rightField: 3 },
    starPositions: []
  },
  {
    id: '8', name: 'Henry Davis', jerseyNumber: 8, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { centerField: 5, leftField: 4, rightField: 4 },
    starPositions: ['centerField']
  },
  {
    id: '9', name: 'Ivy Johnson', jerseyNumber: 9, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { rightField: 4, centerField: 3, firstBase: 2 },
    starPositions: []
  },
  {
    id: '10', name: 'Jack Brown', jerseyNumber: 10, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { pitcher: 3, firstBase: 4, leftField: 3 },
    starPositions: []
  },
  {
    id: '11', name: 'Kelly White', jerseyNumber: 11, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { catcher: 3, thirdBase: 4, secondBase: 3 },
    starPositions: []
  },
  {
    id: '12', name: 'Liam Garcia', jerseyNumber: 12, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { shortstop: 3, secondBase: 4, centerField: 3 },
    starPositions: []
  },
  {
    id: '13', name: 'Maya Patel', jerseyNumber: 13, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { leftField: 3, rightField: 3, firstBase: 2 },
    starPositions: []
  },
  {
    id: '14', name: 'Noah Fleming', jerseyNumber: 14, attendance: 'present',
    positionRestrictions: [],
    positionRatings: { pitcher: 2, catcher: 2, firstBase: 3 },
    starPositions: []
  }
];

// House league players (no restrictions, equal ratings)
const HOUSE_LEAGUE_PLAYERS: Player[] = Array.from({ length: 12 }, (_, i) => ({
  id: `hl${i + 1}`,
  name: `Player ${i + 1}`,
  jerseyNumber: i + 1,
  attendance: 'present' as const,
  positionRestrictions: [],
  positionRatings: {},
  starPositions: []
}));

// Default competitive rules
const COMPETITIVE_RULES: LineupRules = {
  respectPositionLockouts: true,
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 2,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  equalPlayingTime: false,
  rotateLineupEvery: 2,
  rotatePitcherEvery: 3,
  competitiveMode: true,
  competitiveMinPlayingTime: 0.4,
  keyPositions: ['pitcher', 'catcher', 'shortstop'],
  starPlayerRotationDelay: 1,
  _randomSeed: 12345
};

// Default house league rules
const HOUSE_LEAGUE_RULES: LineupRules = {
  respectPositionLockouts: true,
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 2,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  equalPlayingTime: true,
  rotateLineupEvery: 1,
  rotatePitcherEvery: 2,
  competitiveMode: false,
  _randomSeed: 12345
};

class LineupTestSuite {
  private passedTests = 0;
  private failedTests = 0;
  private testResults: Array<{ name: string; status: string; error?: string }> = [];

  assert(condition: boolean, message: string, details: any = {}) {
    if (!condition) {
      throw new Error(`Assertion failed: ${message}. Details: ${JSON.stringify(details)}`);
    }
  }

  async runTest(testName: string, testFunction: () => void | Promise<void>) {
    console.log(`\n🧪 Running test: ${testName}`);
    try {
      await testFunction();
      console.log(`✅ PASS: ${testName}`);
      this.passedTests++;
      this.testResults.push({ name: testName, status: 'PASS' });
    } catch (error) {
      console.log(`❌ FAIL: ${testName}`);
      console.log(`🔍 Error: ${error.message}`);
      this.failedTests++;
      this.testResults.push({ name: testName, status: 'FAIL', error: error.message });
    }
  }

  // Test 1: Basic functionality with minimum players
  async testMinimumPlayers() {
    const players = HOUSE_LEAGUE_PLAYERS.slice(0, 8);
    const innings = 5;
    
    const lineup = generateCompleteLineup(players, innings, HOUSE_LEAGUE_RULES);
    
    this.assert(lineup.length === innings, `Should generate ${innings} innings`, { actualInnings: lineup.length });
    this.assert(lineup[0].inning === 1, 'First inning should be numbered 1');
    this.assert(lineup[innings - 1].inning === innings, `Last inning should be numbered ${innings}`);
    
    // Validate each inning has correct number of field positions
    lineup.forEach((inning, index) => {
      const fieldPositions = Object.keys(inning.positions).filter(pos => pos !== 'bench');
      this.assert(fieldPositions.length === 8, `Inning ${index + 1} should have 8 field positions with 8 players`, { actualPositions: fieldPositions.length });
    });
  }

  // Test 2: Handle 12 players with bench rotation
  async testManyPlayers() {
    const players = HOUSE_LEAGUE_PLAYERS.slice(0, 12);
    const innings = 6;
    
    const lineup = generateCompleteLineup(players, innings, HOUSE_LEAGUE_RULES);
    
    this.assert(lineup.length === innings, `Should generate ${innings} innings`);
    
    // Each inning should have 9 field positions + 3 bench
    lineup.forEach((inning, index) => {
      const fieldPositions = Object.keys(inning.positions).filter(pos => pos !== 'bench');
      const benchPlayers = inning.positions.bench || [];
      this.assert(fieldPositions.length === 9, `Inning ${index + 1} should have 9 field positions`);
      this.assert(benchPlayers.length === 3, `Inning ${index + 1} should have 3 bench players`);
    });
  }

  // Test 3: Competitive mode with star players
  async testCompetitiveMode() {
    const lineup = generateCompleteLineup(NOAH_SELECTS_PLAYERS, 7, COMPETITIVE_RULES);
    
    // Check that star players are in their star positions in early innings
    const firstInning = lineup[0];
    
    // Alex Thompson (star pitcher) should be pitching
    this.assert(firstInning.positions.pitcher === 'Alex Thompson', 'Star pitcher should be pitching in first inning');
    
    // Ben Rodriguez (star catcher) should be catching
    this.assert(firstInning.positions.catcher === 'Ben Rodriguez', 'Star catcher should be catching in first inning');
    
    // Charlie Kim (star shortstop) should be at shortstop
    this.assert(firstInning.positions.shortstop === 'Charlie Kim', 'Star shortstop should be at shortstop in first inning');
  }

  // Test 4: Position restrictions
  async testPositionRestrictions() {
    const lineup = generateCompleteLineup(NOAH_SELECTS_PLAYERS, 5, COMPETITIVE_RULES);
    
    lineup.forEach((inning, index) => {
      // Alex Thompson should only be at pitcher (restricted)
      const alexPosition = Object.entries(inning.positions).find(([pos, player]) => 
        player === 'Alex Thompson' && pos !== 'bench'
      );
      if (alexPosition) {
        this.assert(alexPosition[0] === 'pitcher', `Alex Thompson should only play pitcher, found at ${alexPosition[0]} in inning ${index + 1}`);
      }
      
      // Ben Rodriguez should only be at catcher (restricted)
      const benPosition = Object.entries(inning.positions).find(([pos, player]) => 
        player === 'Ben Rodriguez' && pos !== 'bench'
      );
      if (benPosition) {
        this.assert(benPosition[0] === 'catcher', `Ben Rodriguez should only play catcher, found at ${benPosition[0]} in inning ${index + 1}`);
      }
    });
  }

  // Test 5: Equal playing time in house league
  async testEqualPlayingTime() {
    const players = HOUSE_LEAGUE_PLAYERS.slice(0, 10);
    const innings = 8;
    const lineup = generateCompleteLineup(players, innings, HOUSE_LEAGUE_RULES);
    
    // Count field innings for each player
    const playingTime = new Map<string, number>();
    players.forEach(player => playingTime.set(player.name, 0));
    
    lineup.forEach(inning => {
      Object.entries(inning.positions).forEach(([pos, player]) => {
        if (pos !== 'bench' && typeof player === 'string') {
          playingTime.set(player, (playingTime.get(player) || 0) + 1);
        }
      });
    });
    
    const playingTimes = Array.from(playingTime.values());
    const minTime = Math.min(...playingTimes);
    const maxTime = Math.max(...playingTimes);
    
    // In equal playing time mode, difference should be minimal (≤ 1 inning)
    this.assert(maxTime - minTime <= 1, `Playing time should be equal (max difference 1), but found difference of ${maxTime - minTime}`, { playingTimes: Object.fromEntries(playingTime) });
  }

  async runAllTests() {
    console.log('🚀 COMPREHENSIVE LINEUP GENERATION TEST SUITE');
    console.log('='.repeat(60));
    
    // Run all tests
    await this.runTest('Minimum Players (8)', () => this.testMinimumPlayers());
    await this.runTest('Many Players (12)', () => this.testManyPlayers());
    await this.runTest('Competitive Mode Star Players', () => this.testCompetitiveMode());
    await this.runTest('Position Restrictions', () => this.testPositionRestrictions());
    await this.runTest('Equal Playing Time', () => this.testEqualPlayingTime());
    
    // Print summary
    console.log('\n' + '='.repeat(60));
    console.log('📋 TEST SUITE SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total Tests: ${this.passedTests + this.failedTests}`);
    console.log(`✅ Passed: ${this.passedTests}`);
    console.log(`❌ Failed: ${this.failedTests}`);
    console.log(`📊 Success Rate: ${((this.passedTests / (this.passedTests + this.failedTests)) * 100).toFixed(1)}%`);
    
    if (this.failedTests > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.testResults.filter(r => r.status === 'FAIL').forEach(result => {
        console.log(`❌   - ${result.name}: ${result.error}`);
      });
    }
    
    return this.failedTests === 0;
  }
}

// Run the tests
async function main() {
  const testSuite = new LineupTestSuite();
  const success = await testSuite.runAllTests();
  process.exit(success ? 0 : 1);
}

main().catch(error => {
  console.error('Test suite failed to run:', error);
  process.exit(1);
});

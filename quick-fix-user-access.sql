-- Quick fix to allow users to sign up and create data

-- 1. Temporarily make profiles table more permissive
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Drop the restrictive insert policy
DROP POLICY IF EXISTS "profiles_insert" ON public.profiles;

-- Create a more permissive insert policy
CREATE POLICY "profiles_insert_permissive" ON public.profiles
  FOR INSERT WITH CHECK (
    -- Allow any authenticated user to create a profile with their own ID
    auth.uid() IS NOT NULL AND auth.uid()::text = id
  );

-- 2. Fix subscriptions table
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;

-- Drop restrictive policies
DROP POLICY IF EXISTS "Enable insert for users own subscription" ON public.subscriptions;
DROP POLICY IF EXISTS "subscriptions_insert" ON public.subscriptions;

-- Create permissive insert policy
CREATE POLICY "subscriptions_insert_permissive" ON public.subscriptions
  FOR INSERT WITH CHECK (
    -- Allow authenticated users to create their own subscription
    auth.uid() IS NOT NULL AND auth.uid() = user_id
  );

-- 3. Ensure select policies exist
CREATE POLICY IF NOT EXISTS "subscriptions_select_own" ON public.subscriptions
  FOR SELECT USING (
    auth.uid() = user_id
    OR auth.email() IN ('<EMAIL>', '<EMAIL>')
  );

-- 4. Fix teams insert policy
DROP POLICY IF EXISTS "teams_insert" ON public.teams;

CREATE POLICY "teams_insert_permissive" ON public.teams
  FOR INSERT WITH CHECK (
    -- Allow authenticated users to create their own teams
    auth.uid() IS NOT NULL AND auth.uid() = user_id
  );

-- 5. Grant permissions
GRANT ALL ON public.profiles TO authenticated;
GRANT ALL ON public.subscriptions TO authenticated;
GRANT ALL ON public.teams TO authenticated;

-- Check current policies
SELECT 
  tablename,
  policyname,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE schemaname = 'public' 
  AND tablename IN ('profiles', 'subscriptions', 'teams')
  AND cmd = 'INSERT'
ORDER BY tablename, policyname;
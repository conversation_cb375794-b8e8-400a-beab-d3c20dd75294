# How to Sign In as Admin

The admin pages are failing because you need to be authenticated with Supabase, not just the local storage auth.

## Steps to Access Admin Pages:

1. **Go to the Sign In page**: http://localhost:5173/signin
   - Or click "Sign In" in the header

2. **Sign in with your admin email**:
   - Email: <EMAIL> (or whatever email you set as admin in the migration)
   - Password: Your Supabase account password

3. **After signing in**, navigate to: http://localhost:5173/admin

## If You Don't Have a Password Set:

1. Go to: http://localhost:5173/signup
2. Create an <NAME_EMAIL>
3. This will create your Supabase auth account
4. Then sign in normally

## Alternative: Reset Password

1. Go to: http://localhost:5173/signin
2. Click "Forgot Password?"
3. Enter: <EMAIL>
4. Check your email for the reset link
5. Set a new password
6. Sign in with the new password

## Verify Admin Access:

After signing in, the admin pages should work because:
- You'll have a proper Supabase auth session
- The migration set is_admin = <NAME_EMAIL>
- The RLS policies allow admins to see all data

## Admin Pages Available:

- Dashboard: http://localhost:5173/admin
- Users: http://localhost:5173/admin/users
- Teams: http://localhost:5173/admin/teams
- Billing: http://localhost:5173/admin/billing
- Audit Logs: http://localhost:5173/admin/audit-logs
- Settings: http://localhost:5173/admin/settings
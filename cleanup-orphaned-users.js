#!/usr/bin/env node

import { createServiceClient } from "./src/integrations/supabase/node-client.js";

// Load environment variables

// Create Supabase client with service role key for admin access
const supabase = createServiceClient();

async function cleanupOrphanedUsers() {
  console.log('🔍 Finding orphaned auth users (users without profiles)...\n');

  try {
    // Get all auth users
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.error('Error fetching auth users:', authError);
      return;
    }

    console.log(`Found ${authUsers.users.length} total auth users`);

    // Get all profiles
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, email, full_name');

    if (profilesError) {
      console.error('Error fetching profiles:', profilesError);
      return;
    }

    console.log(`Found ${profiles.length} profiles in database\n`);

    // Create a set of profile user IDs for quick lookup
    const profileUserIds = new Set(profiles.map(p => p.id));

    // Protected accounts that should NEVER be deleted
    const PROTECTED_EMAILS = ['<EMAIL>', '<EMAIL>'];
    
    // Find orphaned users (auth users without profiles), excluding protected accounts
    const orphanedUsers = authUsers.users.filter(user => 
      !profileUserIds.has(user.id) && 
      !PROTECTED_EMAILS.includes(user.email?.toLowerCase() || '')
    );

    if (orphanedUsers.length === 0) {
      console.log('✅ No orphaned users found! Your database is clean.');
      return;
    }

    console.log(`⚠️  Found ${orphanedUsers.length} orphaned auth users:\n`);

    // Display orphaned users
    orphanedUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.email || 'No email'} (ID: ${user.id})`);
      console.log(`   Created: ${new Date(user.created_at).toLocaleDateString()}`);
      console.log(`   Last sign in: ${user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleDateString() : 'Never'}\n`);
    });

    // Ask for confirmation
    console.log('❓ Do you want to delete these orphaned auth users?');
    console.log('⚠️  This will permanently remove them from the auth system.');
    console.log('\nTo delete, run this script with the --delete flag:');
    console.log('node cleanup-orphaned-users.js --delete\n');

    // If --delete flag is provided, delete the users
    if (process.argv.includes('--delete')) {
      console.log('🗑️  Deleting orphaned users...\n');

      for (const user of orphanedUsers) {
        // Double-check protection before deletion
        if (PROTECTED_EMAILS.includes(user.email?.toLowerCase() || '')) {
          console.log(`⚠️  SKIPPING PROTECTED ACCOUNT: ${user.email}`);
          continue;
        }
        
        try {
          const { error } = await supabase.auth.admin.deleteUser(user.id);
          if (error) {
            console.error(`❌ Failed to delete ${user.email}: ${error.message}`);
          } else {
            console.log(`✅ Deleted ${user.email}`);
          }
        } catch (error) {
          console.error(`❌ Error deleting ${user.email}:`, error);
        }
      }

      console.log('\n✨ Cleanup complete!');
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the cleanup
cleanupOrphanedUsers();
# Email Deliverability Guide - Getting to the Inbox

## Quick Fixes (Implement Now)

### 1. **DNS Records Setup**
Ensure all these are properly configured in your domain's DNS:

- **SPF Record**: `v=spf1 include:amazonses.com ~all`
- **DKIM**: Should be automatically set by Resend
- **DMARC**: `v=DMARC1; p=none; rua=mailto:<EMAIL>`

Check at: https://app.resend.com/domains → Your domain → DNS Records

### 2. **Email Content Improvements**
Update your email to avoid spam triggers:

```typescript
// Add these headers to your email
headers: {
  'List-Unsubscribe': '<mailto:<EMAIL>>',
  'X-Entity-Ref-ID': messageId,
  'Precedence': 'bulk'
}

// Update subject line - avoid spam words
subject: 'Your Dugout Boss Account is Ready'  // Instead of "Account Has Been Created"

// Add to email footer
<p style="font-size: 12px; color: #999; text-align: center;">
  This is a transactional email sent because an account was created for you.
  If you didn't expect this email, <NAME_EMAIL>
</p>
```

### 3. **Warm Up Your Domain**
- Start by sending emails to yourself and team members
- Have them mark as "Not Spam" and move to inbox
- Gradually increase volume

### 4. **Use a Subdomain for Transactional Emails**
Consider using `mail.dugoutboss.com` or `notify.dugoutboss.com` for automated emails

## Code Updates for Better Deliverability

```typescript
// Updated email sending code with better headers
body: JSON.stringify({
  from: 'Dugout Boss <<EMAIL>>',  // Use 'hello' instead of 'support'
  to: [email],
  subject: 'Your Dugout Boss Account is Ready',
  html: htmlContent,
  text: textContent,
  headers: {
    'List-Unsubscribe': '<mailto:<EMAIL>>',
    'X-Priority': '3',  // Normal priority
    'X-Mailer': 'Dugout Boss Mailer'
  },
  tags: [
    { name: 'category', value: 'welcome' },
    { name: 'type', value: 'transactional' }
  ]
}),
```

## Email Content Best Practices

### Avoid Spam Triggers:
- ❌ "Free", "Click here", "Act now", excessive exclamation marks
- ❌ ALL CAPS text
- ❌ Too many links
- ❌ Poor HTML structure
- ✅ Balanced text-to-image ratio
- ✅ Include both HTML and plain text versions
- ✅ Personalized content (you're already doing this!)

### Improve Trust Signals:
1. Add physical address in footer
2. Include clear "why you're receiving this" text
3. Add support contact information
4. Use consistent "from" name and email

## Long-term Solutions

### 1. **Email Authentication Score**
Check your score at:
- https://www.mail-tester.com/
- Send a test email to the address they provide
- Fix any issues they identify

### 2. **Dedicated IP (For High Volume)**
- Consider when sending 100k+ emails/month
- Provides full control over reputation

### 3. **Monitor Metrics**
In Resend dashboard, watch:
- Bounce rate (keep < 5%)
- Complaint rate (keep < 0.1%)
- Open rates (aim for > 20%)

### 4. **Implement Feedback Loop**
- Set up complaint feedback with major ISPs
- Remove complainers immediately

## Immediate Action Items

1. **Update DNS Records** - Check all are green in Resend
2. **Test with Mail-Tester** - Aim for 8+ score
3. **Update Email Content** - Remove spam triggers
4. **Ask Recipients** - Have them mark as "Not Spam"
5. **Consider Warm-up Service** - Services like Warmup Inbox

## Testing Checklist

- [ ] All DNS records verified in Resend
- [ ] Email passes spam tests (score 8+)
- [ ] Test in Gmail, Outlook, Yahoo
- [ ] Recipients marking as "Not Spam"
- [ ] No spam trigger words in content
- [ ] Proper unsubscribe mechanism
- [ ] Physical address in footer

Remember: Building email reputation takes time. Start with small volumes to trusted recipients and gradually increase.
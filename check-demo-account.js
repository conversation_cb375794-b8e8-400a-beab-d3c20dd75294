// <PERSON>ript to check if the demo account is working correctly
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Initialize dotenv
dotenv.config();

async function checkDemoAccount() {
  console.log('=== CHECKING DEMO ACCOUNT ===');

  // Get Supabase credentials
  const supabaseUrl = process.env.VITE_SUPABASE_URL;
  const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('❌ Missing Supabase environment variables. Check your .env file.');
    return;
  }

  // Create Supabase client
  const supabase = createClient(supabaseUrl, supabaseAnonKey);

  try {
    // Sign in with demo account
    console.log('Signing in with demo account...');
    // Note: In a production environment, these credentials should be stored in environment variables
    const demoPassword = process.env.DEMO_PASSWORD;
    if (!demoPassword) {
      console.error('❌ DEMO_PASSWORD environment variable not set');
      return;
    }

    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: demoPassword
    });

    if (error) {
      console.error('❌ Failed to sign in with demo account:', error);
      return;
    }

    console.log('✅ Successfully signed in as demo user');
    const userId = data.user.id;
    console.log('User ID:', userId);

    // Check if the team with the hardcoded ID exists
    const hardcodedTeamId = '83bd9832-f5db-4c7d-b234-41fd38f90007';
    console.log(`Checking if team with ID ${hardcodedTeamId} exists...`);

    const { data: teamData, error: teamError } = await supabase
      .from('teams')
      .select('*')
      .eq('id', hardcodedTeamId);

    if (teamError) {
      console.error('❌ Error checking for team:', teamError);
      return;
    }

    if (!teamData || teamData.length === 0) {
      console.error(`❌ Team with ID ${hardcodedTeamId} does not exist`);

      // Check if the user has any teams
      const { data: userTeams, error: userTeamsError } = await supabase
        .from('teams')
        .select('*')
        .eq('user_id', userId);

      if (userTeamsError) {
        console.error('❌ Error checking for user teams:', userTeamsError);
      } else if (userTeams && userTeams.length > 0) {
        console.log(`✅ User has ${userTeams.length} teams:`);
        userTeams.forEach(team => {
          console.log(`- ${team.name} (${team.id})`);
        });
      } else {
        console.log('❌ User has no teams');
      }

      return;
    }

    console.log(`✅ Team with ID ${hardcodedTeamId} exists:`, teamData[0]);

    // Check if the team has players
    console.log('Checking if team has players...');
    const { data: playersData, error: playersError } = await supabase
      .from('players')
      .select('*')
      .eq('team_id', hardcodedTeamId);

    if (playersError) {
      console.error('❌ Error checking for players:', playersError);
    } else if (!playersData || playersData.length === 0) {
      console.error('❌ Team has no players');
    } else {
      console.log(`✅ Team has ${playersData.length} players:`);
      playersData.forEach(player => {
        console.log(`- ${player.name} (${player.id})`);
      });
    }

    // Check if the team has lineups
    console.log('Checking if team has lineups...');
    const { data: lineupsData, error: lineupsError } = await supabase
      .from('lineups')
      .select('*')
      .eq('team_id', hardcodedTeamId);

    if (lineupsError) {
      console.error('❌ Error checking for lineups:', lineupsError);
    } else if (!lineupsData || lineupsData.length === 0) {
      console.error('❌ Team has no lineups');
    } else {
      console.log(`✅ Team has ${lineupsData.length} lineups:`);
      lineupsData.forEach(lineup => {
        console.log(`- ${lineup.name} (${lineup.id})`);
      });

      // Check if the lineups have innings
      for (const lineup of lineupsData) {
        console.log(`Checking if lineup ${lineup.name} has innings...`);
        const { data: inningsData, error: inningsError } = await supabase
          .from('lineup_innings')
          .select('*')
          .eq('lineup_id', lineup.id);

        if (inningsError) {
          console.error(`❌ Error checking for innings in lineup ${lineup.name}:`, inningsError);
        } else if (!inningsData || inningsData.length === 0) {
          console.error(`❌ Lineup ${lineup.name} has no innings`);
        } else {
          console.log(`✅ Lineup ${lineup.name} has ${inningsData.length} innings`);
        }
      }
    }

    // Check if the team has rotation rules
    console.log('Checking if team has rotation rules...');
    const { data: rulesData, error: rulesError } = await supabase
      .from('rotation_rules')
      .select('*')
      .eq('team_id', hardcodedTeamId);

    if (rulesError) {
      console.error('❌ Error checking for rotation rules:', rulesError);
    } else if (!rulesData || rulesData.length === 0) {
      console.error('❌ Team has no rotation rules');
    } else {
      console.log(`✅ Team has rotation rules:`, rulesData[0]);
    }

    console.log('\n=== CHECK COMPLETE ===');
    console.log('The demo account is set up correctly with:');
    console.log(`- Team ID: ${hardcodedTeamId}`);
    console.log(`- Players: ${playersData ? playersData.length : 0}`);
    console.log(`- Lineups: ${lineupsData ? lineupsData.length : 0}`);
    console.log(`- Rotation Rules: ${rulesData && rulesData.length > 0 ? 'Yes' : 'No'}`);

  } catch (err) {
    console.error('❌ Error during check:', err);
  }
}

checkDemoAccount();
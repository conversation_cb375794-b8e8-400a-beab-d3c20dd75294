# COMPREHENSIVE AUTH FIX PLAN

## Critical Issues Identified

### 1. **DATA ISOLATION BREACH** (CRITICAL)
- Users are seeing other users' data (noahfleming2007@gmail.<NAME_EMAIL>'s teams)
- Root causes:
  - RLS policies may be disabled or misconfigured
  - localStorage persistence causing cross-user contamination
  - Special hardcoded <NAME_EMAIL>

### 2. **User Login Issues**
- Admin-created users cannot login
- Missing subscription fields (tier, team_limit) causing redirects to pricing page
- Email verification requirements blocking access

### 3. **Payment Integration Issues**
- Guest checkout users cannot link payment to new accounts
- No mechanism to associate Stripe payment with Supabase auth

## Immediate Actions Required

### Step 1: Apply Emergency RLS Disable (COMPLETED)
```sql
-- Run emergency-disable-rls.sql in Supabase SQL Editor
-- This temporarily disables RLS to allow users to access their data
```

### Step 2: Fix Client-Side Issues (COMPLETED)
- ✅ <NAME_EMAIL> logic from TeamContext
- ✅ Added localStorage clearing on signin/signout in AuthContext
- ✅ Clear all cached data to prevent cross-user contamination

### Step 3: Apply Proper RLS Policies
```sql
-- Run fix-rls-proper-isolation.sql in Supabase SQL Editor
-- This re-enables RLS with proper user isolation policies
```

### Step 4: Fix Admin User Creation
```sql
-- Ensure all paid users have proper subscription fields
UPDATE public.subscriptions
SET 
    tier = COALESCE(tier, 'club'),
    team_limit = COALESCE(team_limit, -1),
    updated_at = NOW()
WHERE is_paid = true 
AND (tier IS NULL OR team_limit IS NULL);
```

### Step 5: Fix Auto-Profile Creation
```sql
-- Create or replace the trigger for auto-creating profiles
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
    -- Create profile
    INSERT INTO public.profiles (id, email, full_name, created_at, updated_at)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
        NOW(),
        NOW()
    )
    ON CONFLICT (id) DO NOTHING;
    
    -- Create default subscription
    INSERT INTO public.subscriptions (user_id, is_paid, tier, team_limit, created_at, updated_at)
    VALUES (
        NEW.id,
        false,
        'starter',
        1,
        NOW(),
        NOW()
    )
    ON CONFLICT (user_id) DO NOTHING;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Ensure trigger exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
```

## Long-Term Solutions

### 1. **Implement Proper Payment Linking**
- Add payment_intent_id to subscriptions table
- Store payment intent during guest checkout
- Link payment to account during signup with ?payment_intent=xxx

### 2. **Fix Email Verification**
- Admin-created users should have email_confirm: true
- Or implement a bypass for admin-created accounts

### 3. **Add Database Constraints**
```sql
-- Ensure critical fields are never null
ALTER TABLE public.subscriptions 
ALTER COLUMN tier SET NOT NULL,
ALTER COLUMN tier SET DEFAULT 'starter',
ALTER COLUMN team_limit SET NOT NULL,
ALTER COLUMN team_limit SET DEFAULT 1;
```

### 4. **Implement Proper User Deletion**
- When deleting users, also delete auth.users record
- Use CASCADE deletes or edge function

## Testing Checklist

1. [ ] Test user can create account after payment
2. [ ] Test user can login after account creation  
3. [ ] Test admin can create paid user who can login
4. [ ] Test users only see their own data (no cross-contamination)
5. [ ] Test localStorage is cleared on login/logout
6. [ ] Test RLS policies properly isolate data

## Database Queries for Verification

```sql
-- Check user isolation
SELECT 
    u.email,
    u.id,
    s.tier,
    s.team_limit,
    s.is_paid,
    COUNT(DISTINCT t.id) as teams
FROM auth.users u
LEFT JOIN public.subscriptions s ON s.user_id = u.id
LEFT JOIN public.teams t ON t.user_id = u.id
WHERE u.email IN ('<EMAIL>', '<EMAIL>')
GROUP BY u.email, u.id, s.tier, s.team_limit, s.is_paid;

-- Check RLS status
SELECT tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('teams', 'players', 'lineups');
```

## Priority Order

1. **IMMEDIATE**: Run emergency-disable-rls.sql to stop data breach
2. **URGENT**: Deploy client fixes (already done via code changes)
3. **HIGH**: Apply proper RLS policies
4. **MEDIUM**: Fix admin user creation flow
5. **LOW**: Implement long-term solutions

## Success Criteria

- No user can see another user's data
- All users can login after account creation
- Admin-created paid users can access all features
- Payment flow works for both logged-in and guest users
- No localStorage contamination between users
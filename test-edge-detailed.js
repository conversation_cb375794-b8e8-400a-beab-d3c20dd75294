// Test with more details
import { supabase } from "./src/integrations/supabase/node-client.js";

const supabaseUrl = 'https://mhuuptkgohuztjrovpxz.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1odXVwdGtnb2h1enRqcm92cHh6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY0NzU0NDEsImV4cCI6MjA2MjA1MTQ0MX0.aGUpltDKIHYJECOuFHeES7VJp7RKlMjArSg7NxFai_k';

async function testContactForm() {
  console.log('Testing contact form edge function...\n');
  
  try {
    const testData = {
      name: 'Test User',
      email: '<EMAIL>',
      subject: 'Test Message',
      message: 'This is a test message.'
    };
    
    const { data, error } = await supabase.functions.invoke('send-contact-email', {
      body: testData
    });
    
    if (error) {
      console.error('Error:', error.message);
      
      // Try to get the response body for more details
      if (error.context && error.context.body) {
        const reader = error.context.body.getReader();
        const decoder = new TextDecoder();
        let body = '';
        
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          body += decoder.decode(value);
        }
        
        console.log('\nError details:', body);
      }
    } else {
      console.log('Success! Response:', data);
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

testContactForm();
#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}Setting up email functionality for Dugout Boss${NC}"
echo ""

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo -e "${RED}Supabase CLI is not installed.${NC}"
    echo "Installing via Homebrew..."
    brew install supabase/tap/supabase
fi

# Get project ref from .env
PROJECT_REF="mhuuptkgohuztjrovpxz"
echo -e "${GREEN}✓ Found Supabase project: ${PROJECT_REF}${NC}"

# Link to project
echo -e "${BLUE}Linking to Supabase project...${NC}"
supabase link --project-ref $PROJECT_REF

# Ask for Resend API key
echo ""
echo -e "${BLUE}Enter your Resend API key (starts with 're_')${NC}"
echo "You can find this at: https://resend.com/api-keys"
read -p "Resend API Key: " RESEND_KEY

if [ -z "$RESEND_KEY" ]; then
    echo -e "${RED}No API key provided. Exiting.${NC}"
    exit 1
fi

# Set the secret
echo -e "${BLUE}Setting Resend API key as secret...${NC}"
supabase secrets set RESEND_API_KEY=$RESEND_KEY

# Deploy the function
echo -e "${BLUE}Deploying send-contact-email function...${NC}"
supabase functions deploy send-contact-email

echo ""
echo -e "${GREEN}✅ Email function deployed successfully!${NC}"
echo ""
echo "Next steps:"
echo "1. Test the contact form on your website"
echo "2. Check emails <NAME_EMAIL>"
echo "3. Monitor logs: supabase functions logs send-contact-email --tail"
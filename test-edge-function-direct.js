#!/usr/bin/env node

import fetch from 'node-fetch';

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

console.log(`${colors.bright}${colors.cyan}
╔═══════════════════════════════════════════════════════════╗
║           Testing Edge Function Directly                  ║
╚═══════════════════════════════════════════════════════════╝
${colors.reset}`);

const EDGE_FUNCTION_URL = 'https://mhuuptkgohuztjrovpxz.supabase.co/functions/v1/create-payment';

// Test payload
const testPayload = {
  priceId: 'price_1RUrWQAg0PgXx1Ii6pWKg42U', // Starter plan
  tier: 'starter',
  successUrl: 'https://dugoutboss.com/payment-success',
  cancelUrl: 'https://dugoutboss.com/payment-canceled'
};

async function testEdgeFunction() {
  console.log(`\n${colors.blue}Testing create-payment function...${colors.reset}`);
  console.log(`URL: ${EDGE_FUNCTION_URL}`);
  console.log(`Payload:`, testPayload);
  
  try {
    const response = await fetch(EDGE_FUNCTION_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'https://dugoutboss.com',
        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1odXVwdGtnb2h1enRqcm92cHh6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY0NzU0NDEsImV4cCI6MjA2MjA1MTQ0MX0.aGUpltDKIHYJECOuFHeES7VJp7RKlMjArSg7NxFai_k'
      },
      body: JSON.stringify(testPayload)
    });
    
    console.log(`\nResponse status: ${response.status} ${response.statusText}`);
    
    const responseText = await response.text();
    console.log(`\nResponse body:`);
    
    try {
      const responseJson = JSON.parse(responseText);
      console.log(JSON.stringify(responseJson, null, 2));
      
      if (response.status === 500) {
        console.log(`\n${colors.red}Error Details:${colors.reset}`);
        if (responseJson.error) {
          console.log(`Error message: ${responseJson.error}`);
        }
        
        console.log(`\n${colors.yellow}Common causes of 500 errors:${colors.reset}`);
        console.log(`1. Invalid or missing Stripe secret key`);
        console.log(`2. Invalid price ID`);
        console.log(`3. Missing SUPABASE_SERVICE_ROLE_KEY`);
        console.log(`4. Database connection issues`);
      } else if (response.status === 200) {
        console.log(`\n${colors.green}Success! The edge function is working.${colors.reset}`);
        if (responseJson.url) {
          console.log(`Stripe checkout URL: ${responseJson.url}`);
        }
      }
    } catch (e) {
      console.log(responseText);
    }
    
  } catch (error) {
    console.log(`\n${colors.red}Request failed:${colors.reset}`, error.message);
  }
}

// Run the test
testEdgeFunction();
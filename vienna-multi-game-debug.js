/**
 * Debug script to trace Vienna's catcher assignment in multi-game scenarios
 * Focus on pitcher planning vs catcher assignment conflicts
 */

console.log('🔍 VIENNA MULTI-GAME CATCHER DEBUG');

// Simulate Vienna and other demo players
const players = [
  {
    id: 'vienna-id',
    name: 'Vienna',
    teamRoles: {
      pitcher: 'capable',
      catcher: 'capable', 
      firstBase: 'capable'
    }
  },
  {
    id: 'mikayla-id',
    name: '<PERSON><PERSON><PERSON>',
    teamRoles: {
      catcher: 'go-to',
      shortstop: 'capable'
    }
  },
  {
    id: 'grace-id',
    name: '<PERSON>',
    teamRoles: {
      catcher: 'capable',
      rightField: 'capable'
    }
  },
  {
    id: 'emma-id',
    name: '<PERSON>',
    teamRoles: {
      pitcher: 'go-to',
      leftField: 'capable'
    }
  },
  {
    id: 'sophia-id',
    name: '<PERSON>',
    teamRoles: {
      pitcher: 'capable',
      centerField: 'capable'
    }
  }
];

console.log('\n📋 PLAYER ANALYSIS:');
players.forEach(player => {
  const pitcherRole = player.teamRoles?.pitcher || 'not set';
  const catcherRole = player.teamRoles?.catcher || 'not set';
  console.log(`${player.name}: pitcher=${pitcherR<PERSON>}, catcher=${catcherRole}`);
});

// Simulate pitcher planning logic from multi-game-orchestrator.ts
function simulatePitcherPlanning(players, numberOfGames, inningsPerGame, rotatePitcherEvery) {
  console.log('\n⚾ SIMULATING PITCHER PLANNING:');
  
  // Identify all pitchers
  const allPitchers = players.filter(p => 
    p.teamRoles?.pitcher && 
    p.teamRoles.pitcher !== 'never' &&
    p.teamRoles.pitcher !== 'avoid'
  );
  
  console.log(`Found ${allPitchers.length} pitchers:`, allPitchers.map(p => 
    `${p.name} (${p.teamRoles.pitcher})`
  ));
  
  // Categorize by pitcher strategy (simulate missing pitcherStrategy)
  const starters = allPitchers.filter(p => p.pitcherStrategy?.role === 'starter');
  const closers = allPitchers.filter(p => p.pitcherStrategy?.role === 'closer');
  const relievers = allPitchers.filter(p => p.pitcherStrategy?.role === 'reliever');
  const anyRole = allPitchers.filter(p => !p.pitcherStrategy?.role || p.pitcherStrategy.role === 'any');
  
  console.log(`Categorized: ${starters.length} starters, ${closers.length} closers, ${relievers.length} relievers, ${anyRole.length} any/unset`);
  
  const gamePlans = [];
  const pitcherUsage = new Map();
  allPitchers.forEach(p => pitcherUsage.set(p.id, 0));
  
  for (let gameNum = 1; gameNum <= numberOfGames; gameNum++) {
    const pitchingSlots = Math.ceil(inningsPerGame / rotatePitcherEvery);
    const gamePitchers = [];
    
    // Calculate which innings each slot covers
    const getInningsForSlot = (slotIndex) => {
      const start = slotIndex * rotatePitcherEvery + 1;
      const end = Math.min(start + rotatePitcherEvery - 1, inningsPerGame);
      return Array.from({ length: end - start + 1 }, (_, i) => start + i);
    };
    
    // Slot 1: Prefer starters (but since we have no explicit starters, use any)
    if (pitchingSlots >= 1) {
      const availableStarters = starters.length > 0 ? starters : anyRole.length > 0 ? anyRole : allPitchers;
      const starter = getLeastUsedPitcher(availableStarters, pitcherUsage, gameNum);
      if (starter) {
        gamePitchers.push({
          player: starter,
          targetInnings: getInningsForSlot(0),
          role: 'starter'
        });
        pitcherUsage.set(starter.id, pitcherUsage.get(starter.id) + 1);
      }
    }
    
    // Fill remaining slots
    while (gamePitchers.length < pitchingSlots && allPitchers.length > gamePitchers.length) {
      const slotIndex = gamePitchers.length;
      const remainingPitchers = allPitchers.filter(p => 
        !gamePitchers.some(gp => gp.player.id === p.id)
      );
      
      const pitcher = getLeastUsedPitcher(remainingPitchers, pitcherUsage, gameNum);
      if (pitcher) {
        gamePitchers.push({
          player: pitcher,
          targetInnings: getInningsForSlot(slotIndex),
          role: 'any'
        });
        pitcherUsage.set(pitcher.id, pitcherUsage.get(pitcher.id) + 1);
      } else {
        break;
      }
    }
    
    gamePlans.push({
      gameNumber: gameNum,
      plannedPitchers: gamePitchers
    });
    
    console.log(`Game ${gameNum} pitcher plan (${gamePitchers.length}/${pitchingSlots} slots):`, 
      gamePitchers.map(p => `${p.player.name} (${p.role}) innings ${p.targetInnings.join(',')}`)
    );
  }
  
  return gamePlans;
}

function getLeastUsedPitcher(pitchers, usage, gameNum) {
  if (pitchers.length === 0) return null;
  
  const sorted = [...pitchers].sort((a, b) => {
    const aUsage = usage.get(a.id) || 0;
    const bUsage = usage.get(b.id) || 0;
    return aUsage - bUsage;
  });
  
  const minUsage = usage.get(sorted[0].id) || 0;
  const leastUsed = sorted.filter(p => (usage.get(p.id) || 0) === minUsage);
  
  return leastUsed[(gameNum - 1) % leastUsed.length];
}

// Simulate catcher availability analysis
function simulateCatcherAnalysis(players, numberOfGames, inningsPerGame, rotatePitcherEvery) {
  console.log('\n🥎 SIMULATING CATCHER ANALYSIS:');
  
  // Count available catchers
  const allCatchers = players.filter(p => 
    p.teamRoles?.catcher && 
    p.teamRoles.catcher !== 'never' &&
    p.teamRoles.catcher !== 'avoid'
  );
  
  console.log(`Found ${allCatchers.length} catchers:`, allCatchers.map(p => 
    `${p.name} (${p.teamRoles.catcher})`
  ));
  
  // Calculate catcher slots needed
  const catcherSlotsPerGame = Math.ceil(inningsPerGame / rotatePitcherEvery);
  const totalCatcherSlots = numberOfGames * catcherSlotsPerGame;
  
  console.log(`Catcher rotation frequency: every ${rotatePitcherEvery} innings`);
  console.log(`Catcher slots per game: ${catcherSlotsPerGame}`);
  console.log(`Total catcher slots needed: ${totalCatcherSlots}`);
  console.log(`Catcher utilization: ${(totalCatcherSlots / allCatchers.length).toFixed(1)} slots per catcher`);
  
  const shouldBeLenientWithCatchers = allCatchers.length < totalCatcherSlots / 2;
  console.log(`Should be lenient with catchers: ${shouldBeLenientWithCatchers}`);
  
  return { allCatchers, shouldBeLenientWithCatchers, catcherSlotsPerGame };
}

// Run simulations
const numberOfGames = 3;
const inningsPerGame = 6;
const rotatePitcherEvery = 2;

const pitcherPlans = simulatePitcherPlanning(players, numberOfGames, inningsPerGame, rotatePitcherEvery);
const catcherAnalysis = simulateCatcherAnalysis(players, numberOfGames, inningsPerGame, rotatePitcherEvery);

console.log('\n🎯 CONFLICT ANALYSIS:');
console.log('Looking for innings where Vienna is scheduled to pitch but could catch...');

// Check each game for potential conflicts
pitcherPlans.forEach(gamePlan => {
  const gameNum = gamePlan.gameNumber;
  console.log(`\nGame ${gameNum}:`);
  
  // Check if Vienna is pitching
  const viennaPitching = gamePlan.plannedPitchers.find(pp => pp.player.name === 'Vienna');
  
  if (viennaPitching) {
    console.log(`  Vienna pitching: innings ${viennaPitching.targetInnings.join(', ')}`);
    
    // Check which innings Vienna could catch instead
    const allInnings = Array.from({ length: inningsPerGame }, (_, i) => i + 1);
    const viennaPitchingInnings = new Set(viennaPitching.targetInnings);
    const viennaCouldCatch = allInnings.filter(inning => !viennaPitchingInnings.has(inning));
    
    console.log(`  Vienna could catch: innings ${viennaCouldCatch.join(', ')}`);
    
    // Check catcher rotation schedule
    const catcherRotationInnings = [];
    for (let inning = 1; inning <= inningsPerGame; inning++) {
      const shouldRotateCatcher = inning > 1 && ((inning - 1) % rotatePitcherEvery) === 0;
      if (shouldRotateCatcher) {
        catcherRotationInnings.push(inning);
      }
    }
    
    console.log(`  Catcher rotation scheduled: innings ${catcherRotationInnings.join(', ')}`);
    
    // Find opportunities where Vienna could rotate in as catcher
    const opportunities = viennaCouldCatch.filter(inning => 
      catcherRotationInnings.includes(inning)
    );
    
    if (opportunities.length > 0) {
      console.log(`  🎯 OPPORTUNITY: Vienna could rotate to catcher in innings ${opportunities.join(', ')}`);
    } else {
      console.log(`  ❌ NO OPPORTUNITY: Vienna pitching conflicts with catcher rotation`);
    }
  } else {
    console.log(`  Vienna not pitching - should be available for catcher rotation`);
  }
});

console.log('\n💡 POTENTIAL ISSUES:');
console.log('1. Vienna may be prioritized for pitching over catching');
console.log('2. Catcher rotation may be disabled due to "limited catchers" logic');
console.log('3. Priority adjustments from previous games may affect assignment');
console.log('4. Star player logic may keep Vienna in pitching role');

console.log('\n🔍 DEBUGGING STEPS:');
console.log('1. Check console logs for "Vienna" during batch generation');
console.log('2. Look for "shouldRotateCatcher = false" messages'); 
console.log('3. Search for "Using planned pitcher: Vienna" messages');
console.log('4. Check if "Limited catchers detected" appears in logs');
console.log('5. Look for Vienna in position assignment logs for catcher role');
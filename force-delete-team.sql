-- Option 4: Force delete a specific team (replace with your team ID)
-- First disable the trigger
ALTER TABLE teams DISABLE TRIGGER protect_teams_trigger;

-- Delete the team (replace with your actual team ID)
DELETE FROM teams WHERE id = '0eee967c-0389-4899-8cbd-1960a004adc0';

-- Re-enable the trigger
ALTER TABLE teams ENABLE TRIGGER protect_teams_trigger;

-- Verify the team is gone
SELECT id, name FROM teams WHERE id = '0eee967c-0389-4899-8cbd-1960a004adc0';
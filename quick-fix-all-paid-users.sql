-- Ensure all paid subscriptions have required fields
UPDATE subscriptions
SET 
  tier = COALESCE(tier, 'starter'),
  team_limit = COALESCE(team_limit, 1),
  updated_at = NOW()
WHERE is_paid = true
AND (tier IS NULL OR team_limit IS NULL);

-- Show all paid users for verification
SELECT 
  p.email,
  p.id as user_id,
  s.is_paid,
  s.tier,
  s.team_limit,
  s.updated_at
FROM profiles p
JOIN subscriptions s ON p.id = s.user_id
WHERE s.is_paid = true
ORDER BY p.email;
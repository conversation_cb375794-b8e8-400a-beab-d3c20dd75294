#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials in environment variables');
  console.error('Please ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function applyGameResultMigration() {
  console.log('🚀 Applying game_result column migration...');
  
  try {
    // Read the migration file
    const migrationPath = path.join(__dirname, 'supabase/migrations/add_game_result_column.sql');
    const migrationSql = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('📝 Migration SQL:');
    console.log(migrationSql);
    
    // Execute the migration
    const { data, error } = await supabase.rpc('exec_sql', { sql: migrationSql });
    
    if (error) {
      console.error('❌ Migration failed:', error);
      
      // Try alternative approach - check if column already exists
      console.log('🔍 Checking if game_result column already exists...');
      
      const { data: columnCheck, error: columnError } = await supabase
        .from('lineups')
        .select('game_result')
        .limit(1);
      
      if (columnError) {
        if (columnError.message.includes('column "game_result" does not exist')) {
          console.log('❌ Column does not exist and migration failed');
          console.log('🔧 Manual intervention required:');
          console.log('   1. Run the following SQL in your Supabase dashboard:');
          console.log('   2. ALTER TABLE lineups ADD COLUMN IF NOT EXISTS game_result TEXT CHECK (game_result IN (\'win\', \'loss\', null));');
          process.exit(1);
        } else {
          console.error('❌ Unexpected error checking column:', columnError);
          process.exit(1);
        }
      } else {
        console.log('✅ Column already exists! Migration not needed.');
      }
    } else {
      console.log('✅ Migration applied successfully!');
    }
    
    // Test the column by running a simple query
    console.log('🧪 Testing game_result column...');
    const { data: testData, error: testError } = await supabase
      .from('lineups')
      .select('id, game_result')
      .limit(1);
    
    if (testError) {
      console.error('❌ Test query failed:', testError);
      process.exit(1);
    }
    
    console.log('✅ game_result column is working correctly!');
    console.log('🎉 Migration completed successfully! Game result toggle should now work.');
    
  } catch (error) {
    console.error('❌ Error applying migration:', error);
    process.exit(1);
  }
}

// Run the migration
applyGameResultMigration();
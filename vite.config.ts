import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    // Add stability improvements for dev server
    hmr: {
      overlay: false, // Disable error overlay that can cause crashes
      timeout: 120000, // Increase HMR timeout to 2 minutes
    },
    watch: {
      // Use polling for better stability on macOS
      usePolling: true,
      interval: 1000, // Poll every second
      // Ignore node_modules and large directories
      ignored: ['**/node_modules/**', '**/dist/**', '**/.git/**'],
    },
    // Increase memory limit for better stability
    fs: {
      // Allow serving files from these directories
      allow: ['..'],
    },
  },
  plugins: [
    react({
      // Disable Fast Refresh if it's causing issues
      fastRefresh: mode === 'development',
    }),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  // Optimize dependency pre-bundling
  optimizeDeps: {
    // Include dependencies that cause issues when not pre-bundled
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@supabase/supabase-js',
      'lucide-react',
    ],
    // Exclude problematic dependencies from optimization
    exclude: ['lovable-tagger'],
    // Force dependency optimization on startup
    force: false,
  },
  // Clear screen on rebuild to avoid terminal overflow
  clearScreen: false,
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunk for large dependencies
          vendor: ['react', 'react-dom'],
          // UI chunk for component libraries
          ui: ['@radix-ui/react-alert-dialog', '@radix-ui/react-dialog', '@radix-ui/react-select'],
          // PDF chunk for PDF generation libraries (dynamically imported)
          pdf: ['jspdf', 'jspdf-autotable'],
          // Charts chunk for chart libraries (dynamically imported)
          charts: ['recharts'],
        },
      },
    },
    // Increase chunk size warning limit for better optimization
    chunkSizeWarningLimit: 1000,
  },
}));

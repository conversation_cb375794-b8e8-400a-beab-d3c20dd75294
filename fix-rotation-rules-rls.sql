-- Fix RLS policies for rotation_rules table
-- Users should be able to manage rotation rules for their own teams

BEGIN;

-- Check if R<PERSON> is enabled on rotation_rules
SELECT 
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables
WHERE schemaname = 'public' 
AND tablename = 'rotation_rules';

-- Drop any existing policies
DROP POLICY IF EXISTS "Users can view their own rotation rules" ON rotation_rules;
DROP POLICY IF EXISTS "Users can insert their own rotation rules" ON rotation_rules;
DROP POLICY IF EXISTS "Users can update their own rotation rules" ON rotation_rules;
DROP POLICY IF EXISTS "Users can delete their own rotation rules" ON rotation_rules;

-- Enable RLS if not already enabled
ALTER TABLE rotation_rules ENABLE ROW LEVEL SECURITY;

-- Create proper policies
CREATE POLICY "rotation_rules_select_policy" ON rotation_rules
    FOR SELECT USING (
        -- Users can see rotation rules for their teams
        EXISTS (
            SELECT 1 FROM teams t
            WHERE t.id = rotation_rules.team_id
            AND t.user_id = auth.uid()
        )
        OR
        -- Admin users can see all rotation rules
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

CREATE POLICY "rotation_rules_insert_policy" ON rotation_rules
    FOR INSERT WITH CHECK (
        -- Users can insert rotation rules for their teams
        EXISTS (
            SELECT 1 FROM teams t
            WHERE t.id = team_id
            AND t.user_id = auth.uid()
        )
        OR
        -- Admin users can insert any rotation rules
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

CREATE POLICY "rotation_rules_update_policy" ON rotation_rules
    FOR UPDATE USING (
        -- Users can update rotation rules for their teams
        EXISTS (
            SELECT 1 FROM teams t
            WHERE t.id = rotation_rules.team_id
            AND t.user_id = auth.uid()
        )
        OR
        -- Admin users can update any rotation rules
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

CREATE POLICY "rotation_rules_delete_policy" ON rotation_rules
    FOR DELETE USING (
        -- Users can delete rotation rules for their teams
        EXISTS (
            SELECT 1 FROM teams t
            WHERE t.id = rotation_rules.team_id
            AND t.user_id = auth.uid()
        )
        OR
        -- Admin users can delete any rotation rules
        auth.email() IN ('<EMAIL>', '<EMAIL>')
    );

-- Verify policies
SELECT 
    policyname,
    cmd
FROM pg_policies
WHERE schemaname = 'public' 
AND tablename = 'rotation_rules'
ORDER BY cmd, policyname;

COMMIT;
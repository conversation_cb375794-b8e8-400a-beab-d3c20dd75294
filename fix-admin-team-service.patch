--- a/src/services/teamService.ts
+++ b/src/services/teamService.ts
@@ -46,11 +46,31 @@
 // Team CRUD operations
 export const fetchTeams = async (userId: string): Promise<Team[]> => {
   try {
-    const { data: teamsData, error } = await supabase
-      .from('teams')
-      .select('*')
-      .eq('user_id', userId);
+    // Check if the current user is an admin
+    const { data: { user } } = await supabase.auth.getUser();
+    const isAdmin = user?.email && ['<EMAIL>', '<EMAIL>'].includes(user.email);
+    
+    let teamsData;
+    let error;
+
+    if (isAdmin) {
+      // Admin: fetch ALL teams
+      console.log('Admin user detected, fetching all teams');
+      const result = await supabase
+        .from('teams')
+        .select('*')
+        .order('created_at', { ascending: false });
+      
+      teamsData = result.data;
+      error = result.error;
+    } else {
+      // Regular user: fetch only their teams
+      const result = await supabase
+        .from('teams')
+        .select('*')
+        .eq('user_id', userId);
+      teamsData = result.data;
+      error = result.error;
+    }
 
     if (error) {
       console.error('Error fetching teams:', error);

--- a/src/services/teamServiceOptimized.ts
+++ b/src/services/teamServiceOptimized.ts
@@ -6,13 +6,35 @@
 export const fetchTeamsOptimized = async (userId: string): Promise<Team[]> => {
   try {
     console.time('fetchTeamsOptimized');
+
+    // Check if the current user is an admin
+    const { data: { user } } = await supabase.auth.getUser();
+    const isAdmin = user?.email && ['<EMAIL>', '<EMAIL>'].includes(user.email);
     
-    // 1. Fetch all teams for the user
-    const { data: teamsData, error: teamsError } = await supabase
-      .from('teams')
-      .select('*')
-      .eq('user_id', userId);
+    let teamsData;
+    let teamsError;
+
+    if (isAdmin) {
+      // Admin: fetch ALL teams
+      console.log('Admin user detected in optimized service, fetching all teams');
+      const result = await supabase
+        .from('teams')
+        .select('*')
+        .order('created_at', { ascending: false })
+        .limit(100); // Reasonable limit for admin view
+      
+      teamsData = result.data;
+      teamsError = result.error;
+    } else {
+      // Regular user: fetch only their teams
+      const result = await supabase
+        .from('teams')
+        .select('*')
+        .eq('user_id', userId);
+      
+      teamsData = result.data;
+      teamsError = result.error;
+    }
 
     if (teamsError) throw teamsError;
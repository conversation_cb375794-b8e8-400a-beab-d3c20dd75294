-- Run this in Supabase SQL Editor to fix profile and subscription RLS policies
-- This will allow users to create their own profiles and subscriptions

-- First, let's check the column types
SELECT 
    table_name,
    column_name,
    data_type 
FROM information_schema.columns 
WHERE table_name IN ('profiles', 'subscriptions') 
    AND column_name IN ('id', 'user_id')
ORDER BY table_name, column_name;

-- 1. Enable RLS on both tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- 2. Drop any existing policies that might conflict
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON profiles;
DROP POLICY IF EXISTS "Enable select for authenticated users" ON profiles;
DROP POLICY IF EXISTS "Enable update for authenticated users" ON profiles;

DROP POLICY IF EXISTS "Users can insert own subscription" ON subscriptions;
DROP POLICY IF EXISTS "Users can update own subscription" ON subscriptions;
DROP POLICY IF EXISTS "Users can view own subscription" ON subscriptions;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON subscriptions;
DROP POLICY IF EXISTS "Enable select for authenticated users" ON subscriptions;
DROP POLICY IF EXISTS "Enable update for authenticated users" ON subscriptions;

-- 3. Create new permissive policies for profiles
-- Note: If profiles.id is text, no casting needed. If it's uuid, we cast auth.uid() to uuid
CREATE POLICY "Users can insert own profile" ON profiles
    FOR INSERT 
    TO authenticated
    WITH CHECK (
        CASE 
            WHEN pg_typeof(id) = 'text'::regtype THEN auth.uid()::text = id
            ELSE auth.uid() = id::uuid
        END
    );

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE 
    TO authenticated
    USING (
        CASE 
            WHEN pg_typeof(id) = 'text'::regtype THEN auth.uid()::text = id
            ELSE auth.uid() = id::uuid
        END
    )
    WITH CHECK (
        CASE 
            WHEN pg_typeof(id) = 'text'::regtype THEN auth.uid()::text = id
            ELSE auth.uid() = id::uuid
        END
    );

CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT 
    TO authenticated
    USING (
        CASE 
            WHEN pg_typeof(id) = 'text'::regtype THEN auth.uid()::text = id
            ELSE auth.uid() = id::uuid
        END
    );

-- 4. Create new permissive policies for subscriptions  
CREATE POLICY "Users can insert own subscription" ON subscriptions
    FOR INSERT 
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own subscription" ON subscriptions
    FOR UPDATE 
    TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view own subscription" ON subscriptions
    FOR SELECT 
    TO authenticated
    USING (auth.uid() = user_id);

-- 5. Alternative approach if the CASE statements don't work
-- Uncomment and run these instead if the above policies fail:

/*
-- For profiles table (assuming id is text type based on the error)
CREATE POLICY "Users can insert own profile simple" ON profiles
    FOR INSERT 
    TO authenticated
    WITH CHECK (auth.uid()::text = id);

CREATE POLICY "Users can update own profile simple" ON profiles
    FOR UPDATE 
    TO authenticated
    USING (auth.uid()::text = id)
    WITH CHECK (auth.uid()::text = id);

CREATE POLICY "Users can view own profile simple" ON profiles
    FOR SELECT 
    TO authenticated
    USING (auth.uid()::text = id);
*/

-- 6. Verify policies were created
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename IN ('profiles', 'subscriptions')
ORDER BY tablename, policyname;
# Dugout Boss - Backend Documentation

## Overview

The Dugout Boss application uses Supabase as its backend service, providing authentication, database, and serverless functions. This document outlines the backend architecture, database schema, and API endpoints.

## Supabase Configuration

- **Project URL**: https://mhuuptkgohuztjrovpxz.supabase.co
- **Region**: us-east-2
- **Authentication**: Email/password authentication with JWT tokens
- **Storage**: Not currently used, but available for future expansion
- **Edge Functions**: Used for payment processing and verification

## Database Schema

The application uses the following database tables:

### Teams Table
```sql
CREATE TABLE public.teams (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);
```

### Players Table
```sql
CREATE TABLE public.players (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
  pitcher_restriction BOOLEAN DEFAULT false,
  catcher_restriction BOOLEAN DEFAULT false,
  first_base_restriction BOOLEAN DEFAULT false,
  other_restriction TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);
```

### Lineups Table
```sql
CREATE TABLE public.lineups (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  game_date DATE NOT NULL,
  team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);
```

### Lineup Innings Table
```sql
CREATE TABLE public.lineup_innings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  lineup_id UUID REFERENCES public.lineups(id) ON DELETE CASCADE,
  inning_number INTEGER NOT NULL,
  positions JSONB NOT NULL, -- Store the positions as JSON data
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  UNIQUE(lineup_id, inning_number)
);
```

### Lineup Attendance Table
```sql
CREATE TABLE public.lineup_attendance (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  lineup_id UUID REFERENCES public.lineups(id) ON DELETE CASCADE,
  player_id UUID REFERENCES public.players(id) ON DELETE CASCADE,
  is_present BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  UNIQUE(lineup_id, player_id)
);
```

### Batting Orders Table
```sql
CREATE TABLE public.batting_orders (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  lineup_id UUID REFERENCES public.lineups(id) ON DELETE CASCADE,
  player_order JSONB NOT NULL, -- Array of player IDs in batting order
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);
```

### Rotation Rules Table
```sql
CREATE TABLE public.rotation_rules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
  rotation_method TEXT NOT NULL DEFAULT 'standard',
  equal_playing_time BOOLEAN DEFAULT true,
  rotate_players BOOLEAN DEFAULT true,
  respect_position_lockouts BOOLEAN DEFAULT true,
  allow_pitcher_rotation BOOLEAN DEFAULT false,
  allow_catcher_rotation BOOLEAN DEFAULT true,
  prioritize_outfield_rotation BOOLEAN DEFAULT true,
  limit_bench_time BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);
```

### Subscriptions Table
```sql
CREATE TABLE public.subscriptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  stripe_session_id TEXT,
  is_paid BOOLEAN DEFAULT false,
  amount INTEGER,
  currency TEXT DEFAULT 'usd',
  payment_date TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
```

### Profiles Table
```sql
CREATE TABLE public.profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  full_name TEXT,
  role TEXT,
  is_admin BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Admin Audit Logs Table
```sql
CREATE TABLE public.admin_audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  admin_id UUID REFERENCES auth.users(id) NOT NULL,
  action TEXT NOT NULL,
  entity_type TEXT NOT NULL,
  entity_id TEXT NOT NULL,
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Row Level Security (RLS)

All tables have Row Level Security enabled to ensure users can only access their own data. Admin users have additional policies that allow them to access all data.

Example policies:
```sql
-- For regular users
CREATE POLICY "Users can view their own teams" ON public.teams
  FOR SELECT USING (auth.uid() = user_id);

-- For admin users
CREATE POLICY "Allow admins to read all teams" ON public.teams
  FOR SELECT USING (auth.uid() IN (SELECT id FROM public.profiles WHERE is_admin = TRUE));
```

## Edge Functions

### verify-payment
Verifies if a user has a valid payment record in the database or in Stripe.

### create-payment
Creates a Stripe checkout session for a user to purchase access to the application.

### stripe-webhook
Handles Stripe webhook events to update the subscription status in the database.

## API Endpoints

The application uses Supabase's auto-generated REST API for most database operations. Custom endpoints are implemented using Edge Functions.

## Authentication Flow

1. User signs up or signs in using email/password
2. Supabase Auth generates a JWT token
3. The application verifies the user's payment status
4. If paid, the user is directed to the dashboard
5. If not paid, the user is directed to the pricing page

## Demo Mode

Demo mode creates a temporary user with pre-populated data and marks them as paid to allow full access to the application features.

## Migrations

Database migrations are managed through SQL files in the `supabase/migrations` directory and can be applied manually or through the application's migration tool.

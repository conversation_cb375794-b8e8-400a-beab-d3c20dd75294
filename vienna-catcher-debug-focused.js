/**
 * Focused debug script to trace Vienna's catcher eligibility issue
 * and confirm the canPlayPosition bug
 */

console.log('🔍 FOCUSED VIENNA CATCHER DEBUG');

// Simulate Vienna's actual data structure
const vienna = {
  id: 'vienna-id',
  name: 'Vienna',
  teamRoles: {
    pitcher: 'capable',
    catcher: 'capable',
    firstBase: 'capable'
  }
};

console.log('\n📋 Vienna\'s team roles:', vienna.teamRoles);

// Reproduce the BUGGY canPlayPosition function from utils-enhanced.ts lines 2472-2490
function canPlayPositionBuggy(player, position) {
  // Map position display names to teamRole keys
  const positionToRoleKey = {
    'Pitcher': 'pitcher',
    'Catcher': 'catcher',
    'First Base': 'firstBase',
    'Second Base': 'secondBase',
    'Shortstop': 'shortstop',
    'Third Base': 'thirdBase',
    'Left Field': 'leftField',
    'Center Field': 'centerField',
    'Right Field': 'rightField'
  };

  const roleKey = positionToRoleKey[position];
  
  // Standard role checking
  if (player.teamRoles) {
    const role = player.teamRoles[roleKey];
    
    // ALWAYS respect 'avoid' (which means "Never" in the UI)
    if (roleKey && role === 'avoid') {
      console.log(`🚫 HARD RESTRICTION: ${player.name} cannot play ${position} (role: avoid/Never)`);
      return false;
    }
    
    if (roleKey && role === 'never') {
      console.log(`🚫 HARD RESTRICTION: ${player.name} cannot play ${position} (role: never)`);
      return false;
    }
    
    // If player has an explicit positive role, they can play
    if (role && role !== 'unset') {
      console.log(`✅ ${player.name} can play ${position} (role: ${role})`);
      return true;
    }
  }

  // BUGGY LOGIC: If a player has ANY position assignments, they can ONLY play those positions
  if (player.teamRoles) {
    const assignedPositions = Object.entries(player.teamRoles)
      .filter(([key, value]) => 
        key !== 'isUtilityPlayer' && 
        key !== 'isOutfieldSpecialist' && 
        value && 
        value !== 'unset' && 
        value !== 'avoid' && 
        value !== 'never'
      );
    
    // BUG IS HERE: This always returns false without checking if position is assigned!
    if (assignedPositions.length > 0) {
      console.log(`🚫 ${player.name} cannot play ${position} (not in their assigned positions)`);
      console.log(`   Assigned positions:`, assignedPositions.map(([key, value]) => `${key}:${value}`));
      return false; // ← THIS IS THE BUG!
    }
  }

  console.log(`🚫 ${player.name} cannot play ${position} (no positions assigned)`);
  return false;
}

// Test the buggy function with Vienna
console.log('\n🐛 TESTING BUGGY canPlayPosition FUNCTION:');
console.log('Vienna\'s catcher role:', vienna.teamRoles.catcher);
console.log('Can Vienna play Catcher?', canPlayPositionBuggy(vienna, 'Catcher'));
console.log('Can Vienna play Pitcher?', canPlayPositionBuggy(vienna, 'Pitcher'));

// Show what the CORRECT logic should be
function canPlayPositionFixed(player, position) {
  const positionToRoleKey = {
    'Pitcher': 'pitcher',
    'Catcher': 'catcher',
    'First Base': 'firstBase',
    'Second Base': 'secondBase',
    'Shortstop': 'shortstop',
    'Third Base': 'thirdBase',
    'Left Field': 'leftField',
    'Center Field': 'centerField',
    'Right Field': 'rightField'
  };

  const roleKey = positionToRoleKey[position];
  
  if (player.teamRoles) {
    const role = player.teamRoles[roleKey];
    
    // ALWAYS respect 'avoid' (which means "Never" in the UI)
    if (roleKey && role === 'avoid') {
      console.log(`🚫 HARD RESTRICTION: ${player.name} cannot play ${position} (role: avoid/Never)`);
      return false;
    }
    
    if (roleKey && role === 'never') {
      console.log(`🚫 HARD RESTRICTION: ${player.name} cannot play ${position} (role: never)`);
      return false;
    }
    
    // If player has an explicit positive role, they can play
    if (role && role !== 'unset') {
      console.log(`✅ ${player.name} can play ${position} (role: ${role})`);
      return true;
    }
  }

  // FIXED LOGIC: If a player has ANY position assignments, check if this position is one of them
  if (player.teamRoles) {
    const assignedPositions = Object.entries(player.teamRoles)
      .filter(([key, value]) => 
        key !== 'isUtilityPlayer' && 
        key !== 'isOutfieldSpecialist' && 
        value && 
        value !== 'unset' && 
        value !== 'avoid' && 
        value !== 'never'
      );
    
    // FIXED: Check if the requested position is actually in their assigned positions
    if (assignedPositions.length > 0) {
      const hasThisPosition = assignedPositions.some(([key]) => key === roleKey);
      if (hasThisPosition) {
        console.log(`✅ ${player.name} can play ${position} (in their assigned positions)`);
        return true;
      } else {
        console.log(`🚫 ${player.name} cannot play ${position} (not in their assigned positions)`);
        console.log(`   Assigned positions:`, assignedPositions.map(([key, value]) => `${key}:${value}`));
        return false;
      }
    }
  }

  console.log(`🚫 ${player.name} cannot play ${position} (no positions assigned)`);
  return false;
}

console.log('\n✅ TESTING FIXED canPlayPosition FUNCTION:');
console.log('Can Vienna play Catcher?', canPlayPositionFixed(vienna, 'Catcher'));
console.log('Can Vienna play Pitcher?', canPlayPositionFixed(vienna, 'Pitcher'));
console.log('Can Vienna play Shortstop?', canPlayPositionFixed(vienna, 'Shortstop'));

console.log('\n🎯 ROOT CAUSE IDENTIFIED:');
console.log('The canPlayPosition function in utils-enhanced.ts has a critical bug');
console.log('on lines 2485-2489. It checks if a player has any position assignments');
console.log('but then immediately returns false without checking if the requested');
console.log('position is one of their assigned positions.');
console.log('');
console.log('This means Vienna (and ALL players with position assignments)');
console.log('are being marked as ineligible for ALL positions, including ones');
console.log('they are explicitly capable of playing!');

console.log('\n🔧 SOLUTION:');
console.log('Replace lines 2485-2489 in utils-enhanced.ts with:');
console.log(`
    // FIXED: Check if the requested position is actually in their assigned positions
    if (assignedPositions.length > 0) {
      const hasThisPosition = assignedPositions.some(([key]) => key === roleKey);
      if (hasThisPosition) {
        console.log(\`✅ \${player.name} can play \${position} (in their assigned positions)\`);
        return true;
      } else {
        console.log(\`🚫 \${player.name} cannot play \${position} (not in their assigned positions)\`);
        return false;
      }
    }
`);

console.log('\n💥 IMPACT:');
console.log('This bug affects:');
console.log('1. PlayerEligibilityCache - marks all positioned players as ineligible');
console.log('2. ConstraintSolver - cannot find valid assignments');
console.log('3. Single-game and multi-game lineup generation');
console.log('4. Position rotation logic');
console.log('');
console.log('This explains why Vienna never gets assigned to catcher - the');
console.log('system thinks she\'s not eligible for ANY position!');
-- Ensure all required columns exist for admin pages

-- 1. Check and add missing columns to profiles
ALTER TABLE public.profiles
ADD COLUMN IF NOT EXISTS email TEXT,
ADD COLUMN IF NOT EXISTS full_name TEXT,
ADD COLUMN IF NOT EXISTS role TEXT DEFAULT 'user',
ADD COLUMN IF NOT EXISTS is_admin BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS is_paid BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- 2. Ensure subscriptions has all columns
ALTER TABLE public.subscriptions
ADD COLUMN IF NOT EXISTS amount INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS currency TEXT DEFAULT 'usd',
ADD COLUMN IF NOT EXISTS is_paid BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS payment_date TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS stripe_session_id TEXT,
ADD COLUMN IF NOT EXISTS stripe_payment_intent_id TEXT,
ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT,
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- 3. Update email in profiles from auth.users if missing
UPDATE public.profiles p
SET email = u.email
FROM auth.users u
WHERE p.id = u.id 
AND (p.email IS NULL OR p.email = '');

-- 4. Set admin flags for specific users
UPDATE public.profiles
SET is_admin = TRUE
WHERE email IN ('<EMAIL>', '<EMAIL>');

-- 5. Verify structure
SELECT 
    'Checking profiles columns:' as status,
    column_name,
    data_type
FROM information_schema.columns
WHERE table_name = 'profiles'
ORDER BY ordinal_position;

-- 6. Check for any missing data
SELECT 
    'Users without email:' as issue,
    COUNT(*) as count
FROM public.profiles
WHERE email IS NULL OR email = ''

UNION ALL

SELECT 
    'Admin users:' as issue,
    COUNT(*) as count
FROM public.profiles
WHERE is_admin = TRUE;
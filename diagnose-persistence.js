// Diagnostic script for persistence issues
console.log('🔍 PERSISTENCE DIAGNOSTIC TOOL');
console.log('==============================');

// 1. Check localStorage before and after actions
function checkLocalStorage() {
  console.log('\n📦 Current localStorage:');
  console.log('Total keys:', localStorage.length);
  
  const keys = {};
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    keys[key] = localStorage.getItem(key);
  }
  
  // Group by type
  const authKeys = [];
  const teamKeys = [];
  const otherKeys = [];
  
  Object.keys(keys).forEach(key => {
    if (key.startsWith('sb-') || key.startsWith('supabase.auth.')) {
      authKeys.push(key);
    } else if (key.includes('team') || key === 'default_team_id' || key === 'current_team_id') {
      teamKeys.push(key);
    } else {
      otherKeys.push(key);
    }
  });
  
  console.log('\n🔐 Auth Keys:', authKeys);
  console.log('🏆 Team Keys:', teamKeys);
  console.log('📋 Other Keys:', otherKeys);
  
  return keys;
}

// 2. Monitor localStorage changes
function monitorLocalStorage() {
  console.log('\n👁️ Starting localStorage monitor...');
  console.log('(This will log any changes to localStorage)');
  
  // Store original localStorage.setItem and removeItem
  const originalSetItem = localStorage.setItem;
  const originalRemoveItem = localStorage.removeItem;
  const originalClear = localStorage.clear;
  
  // Override setItem
  localStorage.setItem = function(key, value) {
    console.log(`📝 SET: ${key} = ${value?.substring(0, 50)}...`);
    console.trace('Call stack:');
    return originalSetItem.apply(this, arguments);
  };
  
  // Override removeItem
  localStorage.removeItem = function(key) {
    console.log(`🗑️ REMOVE: ${key}`);
    console.trace('Call stack:');
    return originalRemoveItem.apply(this, arguments);
  };
  
  // Override clear
  localStorage.clear = function() {
    console.log('💥 CLEAR ALL!');
    console.trace('Call stack:');
    return originalClear.apply(this);
  };
  
  console.log('Monitor installed. All localStorage operations will be logged.');
}

// 3. Test specific scenarios
function testScenarios() {
  console.log('\n🧪 Testing common scenarios...');
  
  // Test 1: Check auth state
  console.log('\n1️⃣ Checking auth state:');
  const hasAuthToken = Array.from({length: localStorage.length}, (_, i) => localStorage.key(i))
    .some(key => key && key.startsWith('sb-'));
  console.log('Has auth token:', hasAuthToken);
  console.log('Session initialized:', sessionStorage.getItem('auth_session_initialized'));
  
  // Test 2: Check team persistence
  console.log('\n2️⃣ Checking team persistence:');
  console.log('Default team:', localStorage.getItem('default_team_id'));
  console.log('Current team:', localStorage.getItem('current_team_id'));
  console.log('Teams cache exists:', !!localStorage.getItem('teams_cache'));
  
  // Test 3: Check for problematic patterns
  console.log('\n3️⃣ Checking for issues:');
  const keys = Object.keys(localStorage);
  const hasLegacyKeys = keys.some(k => k.includes('auth_') || k.includes('demo_'));
  console.log('Has legacy auth keys:', hasLegacyKeys);
  
  if (hasLegacyKeys) {
    console.log('Legacy keys found:', keys.filter(k => k.includes('auth_') || k.includes('demo_')));
  }
}

// 4. Check React contexts (run in console after page loads)
function checkReactContexts() {
  console.log('\n⚛️ To check React contexts, run this in console after page loads:');
  console.log(`
// Check if auth context is preserving tokens
if (window.clearAppLocalStorage) {
  console.log('clearAppLocalStorage function exists');
  // You can test it by calling: window.clearAppLocalStorage()
}

// Check team context
if (window.currentTeamId) {
  console.log('Current team from context:', window.currentTeamId);
}
  `);
}

// Run diagnostics
console.log('\n🚀 Running diagnostics...\n');
checkLocalStorage();
testScenarios();
monitorLocalStorage();
checkReactContexts();

console.log('\n✅ Diagnostic setup complete!');
console.log('💡 Now try your actions (refresh, login, logout) and watch the console for changes.');

// Export for console use
window.diagnosePersistence = {
  checkLocalStorage,
  monitorLocalStorage,
  testScenarios,
  checkReactContexts
};

console.log('\n📌 You can also run these commands:');
console.log('- diagnosePersistence.checkLocalStorage()');
console.log('- diagnosePersistence.testScenarios()');
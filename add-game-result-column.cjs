#!/usr/bin/env node

// Simple script to add the game_result column using Supabase client
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function addGameResultColumn() {
  console.log('🔧 Adding game_result column to lineups table...');
  
  try {
    // Try to execute the SQL directly through a test insert/update
    // This is a workaround since we can't execute DDL directly through the client
    
    console.log('📋 The game_result column needs to be added manually.');
    console.log('🔧 Please run this SQL in your Supabase SQL Editor:');
    console.log('');
    console.log('-- Add game_result column to lineups table');
    console.log("ALTER TABLE lineups ADD COLUMN IF NOT EXISTS game_result TEXT CHECK (game_result IN ('win', 'loss'));");
    console.log('');
    console.log('-- Add index for performance');
    console.log('CREATE INDEX IF NOT EXISTS idx_lineups_game_result ON lineups(game_result);');
    console.log('');
    console.log('📝 After running this SQL, the game result toggle should work correctly.');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

addGameResultColumn();
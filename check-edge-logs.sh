#!/bin/bash

echo "📋 Checking Edge Function Logs"
echo "=============================="
echo ""

# Function to check logs with proper authentication
check_logs() {
    local function_name=$1
    echo "🔍 Checking logs for: $function_name"
    echo "-----------------------------------"
    
    # Try to get logs using Supabase CLI
    if command -v supabase &> /dev/null; then
        echo "Using Supabase CLI..."
        # Note: This requires being logged in to Supabase CLI
        echo "If this fails, make sure you're logged in with: supabase login"
        echo ""
        
        # Get project ID from supabase directory
        if [ -f "supabase/.temp/project-ref" ]; then
            PROJECT_REF=$(cat supabase/.temp/project-ref)
            echo "Project ref: $PROJECT_REF"
        fi
        
        # This would work if we had proper CLI auth setup
        echo "Run this command manually:"
        echo "supabase functions logs $function_name --project-ref $PROJECT_REF"
    else
        echo "❌ Supabase CLI not found"
    fi
    
    echo ""
}

# Check both functions
check_logs "send-welcome-email"
check_logs "admin-create-user"

echo "Alternative: Check logs in Supabase Dashboard"
echo "============================================="
echo ""
echo "1. Go to https://app.supabase.com"
echo "2. Select your project"
echo "3. Navigate to Edge Functions"
echo "4. Click on 'send-welcome-email'"
echo "5. Click on the 'Logs' tab"
echo "6. Look for:"
echo "   - 'RESEND_API_KEY is not set' errors"
echo "   - 'Resend API error' messages"
echo "   - 'Welcome email sent successfully' messages"
echo ""
echo "Common log messages to look for:"
echo "- 'Missing authorization header' - Not authenticated"
echo "- 'Unauthorized - admin access required' - User not admin"
echo "- 'Email service not configured' - RESEND_API_KEY missing"
echo "- 'Failed to send email' - Resend API error"
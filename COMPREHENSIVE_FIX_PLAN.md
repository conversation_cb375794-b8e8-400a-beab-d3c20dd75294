# Comprehensive Fix Plan for Critical Issues

## Issues to Fix

### 1. **Rotation Rules Update Failure**
- Error: "User not authenticated" when calling `updateRotationRules`
- Root cause: `supabase.auth.getUser()` is returning null/undefined

### 2. **Player Save Failure on Roster**
- Error: "JSON object requested, multiple (or no) rows returned" (406 Not Acceptable)
- Root cause: Player update query not finding the player record or RLS blocking update

### 3. **Session Persistence Still Failing**
- Users still being logged out on refresh
- Root cause: Auth state management not properly handling session restoration

## Detailed Fix Plan

### Fix 1: Rotation Rules Authentication Issue

**Problem**: The `getUser()` call in `updateRotationRules` is failing
**Solution**: Pass user context from TeamContext instead of fetching it

```typescript
// In teamService.ts - updateRotationRules
export const updateRotationRules = async (teamId: string, rules: RotationRules, userId?: string): Promise<RotationRules> => {
  try {
    // Use provided userId or get from session
    let currentUserId = userId;
    
    if (!currentUserId) {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        // Try getSession as fallback
        const { data: { session } } = await supabase.auth.getSession();
        currentUserId = session?.user?.id;
      } else {
        currentUserId = user.id;
      }
    }
    
    if (!currentUserId) {
      throw new Error('User not authenticated');
    }
    
    // ... rest of function
```

### Fix 2: Player Update Query Issue

**Problem**: Player update returning 0 rows (RLS or query issue)
**Solution**: Fix the update query and check RLS policies

```typescript
// In teamService.ts - updatePlayer
export const updatePlayer = async (player: Player): Promise<Player> => {
  try {
    // First verify the player exists and user has access
    const { data: existingPlayer, error: checkError } = await supabase
      .from('players')
      .select('id, team_id')
      .eq('id', player.id)
      .single();
      
    if (checkError || !existingPlayer) {
      throw new Error('Player not found or access denied');
    }
    
    // Update with proper error handling
    const { data, error } = await supabase
      .from('players')
      .update({
        name: player.name,
        position_preferences: {
          ...player.positionPreferences || {},
          teamRoles: player.teamRoles || {},
          pitcherStrategy: player.pitcherStrategy || null,
          isStarPlayer: player.isStarPlayer || false
        }
      })
      .eq('id', player.id)
      .select()
      .single();
```

### Fix 3: Complete Session Persistence Rewrite

**Problem**: Multiple issues with session restoration timing
**Solution**: Implement a robust session manager

```typescript
// New approach for AuthContext
const AuthProvider = ({ children }) => {
  const [authState, setAuthState] = useState({
    user: null,
    session: null,
    isLoading: true,
    isInitialized: false
  });

  // Single source of truth for auth state
  useEffect(() => {
    let mounted = true;

    const initializeAuth = async () => {
      try {
        // 1. Check for existing session
        const { data: { session } } = await supabase.auth.getSession();
        
        if (session && mounted) {
          setAuthState({
            user: session.user,
            session,
            isLoading: false,
            isInitialized: true
          });
        } else if (mounted) {
          // No session found
          const wasAuthenticated = localStorage.getItem('was_authenticated') === 'true';
          
          if (wasAuthenticated) {
            // Wait for potential session restoration
            setTimeout(() => {
              if (mounted && !authState.session) {
                localStorage.removeItem('was_authenticated');
                setAuthState(prev => ({ ...prev, isLoading: false }));
              }
            }, 2000);
          } else {
            setAuthState(prev => ({ ...prev, isLoading: false }));
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        if (mounted) {
          setAuthState(prev => ({ ...prev, isLoading: false }));
        }
      }
    };

    // 2. Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        if (!mounted) return;

        console.log('Auth event:', event);

        switch (event) {
          case 'SIGNED_IN':
            localStorage.setItem('was_authenticated', 'true');
            setAuthState({
              user: session?.user || null,
              session,
              isLoading: false,
              isInitialized: true
            });
            break;
            
          case 'SIGNED_OUT':
            localStorage.removeItem('was_authenticated');
            clearAllUserData();
            setAuthState({
              user: null,
              session: null,
              isLoading: false,
              isInitialized: true
            });
            break;
            
          case 'TOKEN_REFRESHED':
            setAuthState(prev => ({
              ...prev,
              session,
              user: session?.user || prev.user
            }));
            break;
            
          case 'USER_UPDATED':
            setAuthState(prev => ({
              ...prev,
              user: session?.user || prev.user
            }));
            break;
        }
      }
    );

    initializeAuth();

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []);

  // Rest of provider...
};
```

## Implementation Steps

### Step 1: Fix Rotation Rules (Immediate)
1. Update `teamService.ts` to handle auth better
2. Pass user ID from TeamContext when available
3. Add fallback to getSession if getUser fails

### Step 2: Fix Player Updates (Immediate)
1. Add existence check before update
2. Improve error messages
3. Check and fix player RLS policies if needed

### Step 3: Rewrite Auth State Management (Major)
1. Simplify auth state to single source of truth
2. Remove duplicate session checks
3. Use localStorage for persistence hint only
4. Handle all auth events properly

### Step 4: Testing Protocol
1. Test rotation rules update
2. Test player name/preference changes
3. Test page refresh 10 times in a row
4. Test with different browsers
5. Test with slow network conditions

## Quick Fixes to Apply Now

### 1. For Rotation Rules
```typescript
// In TeamContext.tsx - updateRotationRules
const updateRotationRules = async (rules: RotationRules) => {
  // ... existing code ...
  
  if (!user) return; // Skip Supabase in demo mode
  
  try {
    // Pass user ID explicitly
    await teamService.updateRotationRules(currentTeam.id, rules, user.id);
```

### 2. For Player Updates
```sql
-- Check if player policies are too restrictive
SELECT * FROM pg_policies 
WHERE tablename = 'players' 
AND policyname LIKE '%update%';
```

### 3. For Session Persistence
- Implement session check on mount
- Don't clear state on INITIAL_SESSION with no session
- Add retry logic for session restoration

## Success Metrics
- [ ] Rotation rules save without auth errors
- [ ] Players update without 406 errors
- [ ] Users stay logged in after 10 consecutive refreshes
- [ ] No data leakage between users
- [ ] All operations work for both regular and admin users
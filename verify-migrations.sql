-- Verification queries to check if migrations were applied correctly
-- Run these in Supabase SQL Editor to verify the fixes

-- 1. Check if RLS policies were created for subscriptions table
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM pg_policies 
WHERE tablename = 'subscriptions'
ORDER BY cmd, policyname;

-- 2. Check if profiles exist for all users in auth.users
SELECT 
  'Users with profiles' as check_type,
  COUNT(*) as count
FROM auth.users u
INNER JOIN public.profiles p ON u.id = p.id
UNION ALL
SELECT 
  'Users missing profiles' as check_type,
  COUNT(*) as count
FROM auth.users u
LEFT JOIN public.profiles p ON u.id = p.id
WHERE p.id IS NULL;

-- 3. Check <EMAIL> admin setup
SELECT 
  u.email,
  p.is_admin,
  p.role,
  s.is_paid,
  s.stripe_session_id
FROM auth.users u
LEFT JOIN public.profiles p ON u.id = p.id
LEFT JOIN public.subscriptions s ON u.id = s.user_id
WHERE u.email = '<EMAIL>';

-- 4. Check subscription records
SELECT 
  'Total subscription records' as check_type,
  COUNT(*) as count
FROM public.subscriptions
UNION ALL
SELECT 
  'Paid subscription records' as check_type,
  COUNT(*) as count
FROM public.subscriptions
WHERE is_paid = true;

-- 5. Check for any users still missing profiles (should be 0)
SELECT 
  u.id,
  u.email,
  u.created_at
FROM auth.users u
LEFT JOIN public.profiles p ON u.id = p.id
WHERE p.id IS NULL
LIMIT 5;
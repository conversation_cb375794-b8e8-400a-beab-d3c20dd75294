#!/usr/bin/env tsx

/**
 * Test the actual utils-enhanced.ts algorithm with correct data format
 */

// Mock environment variables
process.env.VITE_SUPABASE_URL = 'http://localhost:54321';
process.env.VITE_SUPABASE_ANON_KEY = 'test-key';

// Mock import.meta.env
(globalThis as any).import = {
  meta: {
    env: {
      VITE_SUPABASE_URL: 'http://localhost:54321',
      VITE_SUPABASE_ANON_KEY: 'test-key'
    }
  }
};

// Mock supabase before importing utils-enhanced
const mockSupabase = {
  auth: {
    getUser: () => Promise.resolve({ data: { user: null }, error: null }),
  },
  from: () => ({
    select: () => ({
      eq: () => ({
        single: () => Promise.resolve({ data: null, error: null })
      })
    }),
  })
};

// Mock the supabase module
const Module = require('module');
const originalRequire = Module.prototype.require;
Module.prototype.require = function(id: string) {
  if (id.includes('supabaseClient')) {
    return { supabase: mockSupabase };
  }
  return originalRequire.apply(this, arguments);
};

// Import the actual functions
import { generateCompleteLineup, validateLineup, LineupRules } from './src/lib/utils-enhanced';
import { Player } from './src/contexts/TeamContext';

// Test data using the correct Player interface format
const NOAH_SELECTS_PLAYERS: Player[] = [
  {
    id: '1', 
    name: 'Alex Thompson',
    positionRestrictions: { pitcher: false, catcher: true, firstBase: true, other: null }, // Can ONLY play pitcher
    positionRatings: { pitcher: 5, firstBase: 3, leftField: 2 },
    isStarPlayer: true
  },
  {
    id: '2', 
    name: 'Ben Rodriguez',
    positionRestrictions: { pitcher: true, catcher: false, firstBase: true, other: null }, // Can ONLY play catcher
    positionRatings: { catcher: 5, thirdBase: 4, rightField: 3 },
    isStarPlayer: true
  },
  {
    id: '3', 
    name: 'Charlie Kim',
    positionRestrictions: { pitcher: false, catcher: false, firstBase: false, other: null }, // No restrictions
    positionRatings: { shortstop: 5, secondBase: 4, centerField: 3 },
    isStarPlayer: true
  },
  {
    id: '4', 
    name: 'David Chen',
    positionRestrictions: { pitcher: false, catcher: false, firstBase: false, other: null },
    positionRatings: { firstBase: 4, thirdBase: 3, leftField: 3 }
  },
  {
    id: '5', 
    name: 'Emma Wilson',
    positionRestrictions: { pitcher: false, catcher: false, firstBase: false, other: null },
    positionRatings: { secondBase: 4, shortstop: 3, rightField: 3 }
  },
  {
    id: '6', 
    name: 'Frank Miller',
    positionRestrictions: { pitcher: false, catcher: false, firstBase: false, other: null },
    positionRatings: { thirdBase: 4, firstBase: 3, centerField: 2 }
  },
  {
    id: '7', 
    name: 'Grace Lee',
    positionRestrictions: { pitcher: false, catcher: false, firstBase: false, other: null },
    positionRatings: { leftField: 4, centerField: 4, rightField: 3 }
  },
  {
    id: '8', 
    name: 'Henry Davis',
    positionRestrictions: { pitcher: false, catcher: false, firstBase: false, other: null },
    positionRatings: { centerField: 5, leftField: 4, rightField: 4 },
    isStarPlayer: true
  },
  {
    id: '9', 
    name: 'Ivy Johnson',
    positionRestrictions: { pitcher: false, catcher: false, firstBase: false, other: null },
    positionRatings: { rightField: 4, centerField: 3, firstBase: 2 }
  }
];

// House league players
const HOUSE_LEAGUE_PLAYERS: Player[] = Array.from({ length: 12 }, (_, i) => ({
  id: `hl${i + 1}`,
  name: `Player ${i + 1}`,
  positionRestrictions: { pitcher: false, catcher: false, firstBase: false, other: null }, // No restrictions
  positionRatings: {}
}));

// Rules
const COMPETITIVE_RULES: LineupRules = {
  respectPositionLockouts: true,
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 2,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  equalPlayingTime: false,
  rotateLineupEvery: 2,
  rotatePitcherEvery: 3,
  competitiveMode: true,
  competitiveMinPlayingTime: 0.4,
  keyPositions: ['pitcher', 'catcher', 'shortstop'],
  starPlayerRotationDelay: 1,
  _randomSeed: 12345
};

const HOUSE_LEAGUE_RULES: LineupRules = {
  respectPositionLockouts: true,
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 2,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  equalPlayingTime: true,
  rotateLineupEvery: 1,
  rotatePitcherEvery: 2,
  competitiveMode: false,
  _randomSeed: 12345
};

async function testRealAlgorithm() {
  console.log('🚀 TESTING REAL ALGORITHM FROM utils-enhanced.ts');
  console.log('='.repeat(60));
  
  try {
    // Test 1: Basic functionality with 8 players
    console.log('\n🧪 Test 1: Basic functionality with 8 players');
    const players8 = HOUSE_LEAGUE_PLAYERS.slice(0, 8);
    const lineup8 = generateCompleteLineup(players8, 5, HOUSE_LEAGUE_RULES);
    console.log(`✅ Generated ${lineup8.length} innings for 8 players`);
    
    // Test 2: 12 players with bench
    console.log('\n🧪 Test 2: 12 players with bench rotation');
    const players12 = HOUSE_LEAGUE_PLAYERS.slice(0, 12);
    const lineup12 = generateCompleteLineup(players12, 6, HOUSE_LEAGUE_RULES);
    console.log(`✅ Generated ${lineup12.length} innings for 12 players`);
    
    // Test 3: Competitive mode with Noah's team
    console.log('\n🧪 Test 3: Competitive mode with Noah\'s Selects team');
    const lineupCompetitive = generateCompleteLineup(NOAH_SELECTS_PLAYERS, 7, COMPETITIVE_RULES);
    console.log(`✅ Generated ${lineupCompetitive.length} innings for competitive team`);
    
    // Verify star players are in correct positions
    const firstInning = lineupCompetitive[0];
    console.log('🔍 First inning positions:');
    console.log(`  Pitcher: ${firstInning.positions.pitcher}`);
    console.log(`  Catcher: ${firstInning.positions.catcher}`);
    console.log(`  Shortstop: ${firstInning.positions.shortstop}`);
    
    // Test 4: Position restrictions validation
    console.log('\n🧪 Test 4: Position restrictions validation');
    let restrictionsRespected = true;
    lineupCompetitive.forEach((inning, index) => {
      // Check Alex Thompson (restricted to pitcher only)
      const alexPosition = Object.entries(inning.positions).find(([pos, player]) => 
        player === 'Alex Thompson' && pos !== 'bench'
      );
      if (alexPosition && alexPosition[0] !== 'pitcher') {
        console.log(`❌ Alex Thompson found at ${alexPosition[0]} in inning ${index + 1} (should only be pitcher)`);
        restrictionsRespected = false;
      }
      
      // Check Ben Rodriguez (restricted to catcher only)
      const benPosition = Object.entries(inning.positions).find(([pos, player]) => 
        player === 'Ben Rodriguez' && pos !== 'bench'
      );
      if (benPosition && benPosition[0] !== 'catcher') {
        console.log(`❌ Ben Rodriguez found at ${benPosition[0]} in inning ${index + 1} (should only be catcher)`);
        restrictionsRespected = false;
      }
    });
    
    if (restrictionsRespected) {
      console.log('✅ All position restrictions respected');
    }
    
    // Test 5: Lineup validation
    console.log('\n🧪 Test 5: Lineup validation');
    let allValid = true;
    lineupCompetitive.forEach((inning, index) => {
      const validation = validateLineup(inning, NOAH_SELECTS_PLAYERS, COMPETITIVE_RULES);
      if (!validation.valid) {
        console.log(`❌ Inning ${index + 1} validation failed:`, validation.errors);
        allValid = false;
      }
    });
    
    if (allValid) {
      console.log('✅ All lineups passed validation');
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('🎉 ALL TESTS PASSED! Real algorithm is working correctly.');
    console.log('='.repeat(60));
    
    return true;
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    return false;
  }
}

// Run the test
testRealAlgorithm().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('Test suite failed to run:', error);
  process.exit(1);
});

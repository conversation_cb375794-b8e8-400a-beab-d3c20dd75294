-- Check RLS policies on teams table
SELECT 
  policyname,
  cmd,
  qual,
  with_check
FROM pg_policies
WHERE tablename = 'teams'
ORDER BY cmd, policyname;

-- Check if there are any foreign key constraints preventing deletion
SELECT
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    rc.delete_rule
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
    JOIN information_schema.referential_constraints AS rc
      ON tc.constraint_name = rc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
  AND ccu.table_name = 'teams';

-- Check triggers on teams table
SELECT 
  tgname as trigger_name,
  proname as function_name,
  tgtype,
  tgenabled
FROM pg_trigger t
JOIN pg_proc p ON t.tgfoid = p.oid
WHERE tgrelid = 'teams'::regclass
AND tgname NOT LIKE 'RI_ConstraintTrigger%';

-- Test deletion for a specific user (replace with actual email)
-- First, see what teams exist
SELECT 
  t.id,
  t.name,
  t.user_id,
  p.email,
  COUNT(pl.id) as player_count,
  COUNT(l.id) as lineup_count
FROM teams t
JOIN profiles p ON t.user_id = p.id
LEFT JOIN players pl ON pl.team_id = t.id
LEFT JOIN lineups l ON l.team_id = t.id
WHERE p.email = 'YOUR_EMAIL_HERE' -- Replace with your test account email
GROUP BY t.id, t.name, t.user_id, p.email;
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function debugTeamDeletion(email) {
  console.log(`\n🔍 Debugging team deletion for: ${email}\n`);

  try {
    // 1. Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('email', email)
      .single();

    if (profileError || !profile) {
      console.log('❌ User profile not found!');
      return;
    }

    console.log('✅ User found:', profile.id);

    // 2. Get user's teams
    const { data: teams, error: teamsError } = await supabase
      .from('teams')
      .select(`
        *,
        players(count),
        lineups(count)
      `)
      .eq('user_id', profile.id);

    if (teamsError) {
      console.log('❌ Error fetching teams:', teamsError);
      return;
    }

    console.log(`\n📋 Found ${teams?.length || 0} teams:`);
    teams?.forEach(team => {
      console.log(`\n   Team: ${team.name}`);
      console.log(`   ID: ${team.id}`);
      console.log(`   Players: ${team.players?.[0]?.count || 0}`);
      console.log(`   Lineups: ${team.lineups?.[0]?.count || 0}`);
    });

    if (teams && teams.length > 0) {
      const testTeam = teams[0];
      console.log(`\n🧪 Testing deletion of team: ${testTeam.name}`);

      // 3. Try to delete related data first
      console.log('\n   1. Deleting innings...');
      const { error: inningsError } = await supabase
        .from('innings')
        .delete()
        .in('lineup_id', 
          await supabase
            .from('lineups')
            .select('id')
            .eq('team_id', testTeam.id)
            .then(res => res.data?.map(l => l.id) || [])
        );

      if (inningsError) {
        console.log('   ❌ Error deleting innings:', inningsError.message);
      } else {
        console.log('   ✅ Innings deleted');
      }

      console.log('\n   2. Deleting lineups...');
      const { error: lineupsError } = await supabase
        .from('lineups')
        .delete()
        .eq('team_id', testTeam.id);

      if (lineupsError) {
        console.log('   ❌ Error deleting lineups:', lineupsError.message);
      } else {
        console.log('   ✅ Lineups deleted');
      }

      console.log('\n   3. Deleting players...');
      const { error: playersError } = await supabase
        .from('players')
        .delete()
        .eq('team_id', testTeam.id);

      if (playersError) {
        console.log('   ❌ Error deleting players:', playersError.message);
      } else {
        console.log('   ✅ Players deleted');
      }

      console.log('\n   4. Deleting rotation rules...');
      const { error: rulesError } = await supabase
        .from('rotation_rules')
        .delete()
        .eq('team_id', testTeam.id);

      if (rulesError) {
        console.log('   ❌ Error deleting rotation rules:', rulesError.message);
      } else {
        console.log('   ✅ Rotation rules deleted');
      }

      console.log('\n   5. Finally, deleting the team...');
      const { error: teamError } = await supabase
        .from('teams')
        .delete()
        .eq('id', testTeam.id);

      if (teamError) {
        console.log('   ❌ Error deleting team:', teamError);
        console.log('   Full error:', JSON.stringify(teamError, null, 2));
      } else {
        console.log('   ✅ Team deleted successfully!');
      }

      // 4. Verify deletion
      const { data: remainingTeams, error: verifyError } = await supabase
        .from('teams')
        .select('id, name')
        .eq('user_id', profile.id);

      console.log(`\n📊 Remaining teams: ${remainingTeams?.length || 0}`);
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

const email = process.argv[2];
if (!email) {
  console.log('Usage: node debug-team-deletion.js <email>');
  process.exit(1);
}

debugTeamDeletion(email);
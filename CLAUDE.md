# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Diamond Lineup Guru (Dugout Boss) is a baseball/softball team management application that helps coaches create fair lineups with intelligent rotation algorithms. The app supports both recreational (equal playing time) and competitive (optimal positioning) modes.

## Development Commands

```bash
# Development
npm run dev              # Start development server on http://localhost:5173

# Building
npm run build            # Production build (cleans dist first)
npm run build:dev        # Development build
npm run build:prod       # Production build
npm run preview          # Preview production build

# Testing
npm run test             # Run tests with Vitest
npm run test:ui          # Run tests with Vitest UI

# Code Quality
npm run lint             # Run ESLint
```

## Repository Etiquette

- Branch naming: use `feature/<ticket>` for new features and `hotfix/<issue>` for bug fixes.
- Pull Request guidelines: reference the related issue, include screenshots for UI changes, and ensure all CI checks pass before marking ready for review.
- Commit messages: use imperative mood, for example `feat(auth): enable session persistence`.

## Allowed Tools

Claude Code is permitted to use the following tools:
- `Edit` for file modifications.
- `Bash` for Git operations (e.g., `git commit`, `git diff`).
- `Python` for running or generating scripts.
- `Permissions` to inspect or update allowed tool configurations.

Run `/permissions` in Claude Code to adjust tool permissions on demand.

## Key Architecture Components

### State Management
- **TeamContext** (`src/contexts/TeamContext.tsx`): Central state for team data, players, rotation rules
- **AuthContext** (`src/contexts/AuthContext.tsx`): Authentication state and user management
- Uses React Context API, not Redux or other state libraries

### Major Features

#### Quick Roster Adjust (ViewLineup.tsx)
- Last-minute attendance changes with smart lineup regeneration
- Shows ALL team players with visual indicators for drops/adds
- Preserves existing position assignments when possible
- Fills empty spots intelligently using position preferences
- See `QUICK_ROSTER_ADJUST_FEATURE.md` for full details

### Rotation Algorithm System
The rotation system is split between two main implementations:

1. **utils-enhanced.ts**: Contains the main rotation logic
   - `ConstraintSolver`: Handles position assignments respecting all rules
   - `LineupRotator`: Manages player rotations between innings
   - `generateLineup()`: Main entry point for lineup generation
   - Supports competitive mode with `findCompetitiveAssignment()`

2. **improvedRotation.ts**: Alternative rotation implementation
   - Simpler, more aggressive rotation strategy
   - Used as fallback when main algorithm has issues

### Player Management Systems

Two parallel systems exist for managing player position preferences:

1. **Team Roles** (New/Recommended): `TeamRoleManager.tsx`
   - Natural grouping: "These are my 4 first base players"
   - Roles: Primary, In the Mix, Emergency, Never
   - Stored in `player.teamRoles`

2. **Position Preferences** (Legacy): `PositionPreferenceManager.tsx`
   - Abstract preference levels and rankings
   - Stored in `player.positionPreferences`

### Database Schema
- Supabase/PostgreSQL with Row Level Security
- Key tables: teams, players, lineups, rotation_rules
- Player data includes: name, position restrictions, preferences, ratings
- See `supabase/migrations/schema.sql` for full schema

### Component Organization
- Pages in `src/pages/` - Full page components
- Reusable components in `src/components/`
- UI primitives in `src/components/ui/` (shadcn/ui)
- Utility functions in `src/lib/`

## Critical Implementation Details

### Rotation Rules
The rotation algorithm respects multiple constraints:
- `rotateLineupEvery`: How often to rotate players (every X innings)
- `rotatePitcherEvery`: Separate pitcher rotation frequency
- `maxConsecutiveBenchInnings`: Max innings a player can sit (default: 2)
- `competitiveMode`: When true, prioritizes optimal positioning
- `keyPositions`: Positions considered critical (default: pitcher, catcher, shortstop)

### Position Handling
- 9 field positions + bench
- Position keys: pitcher, catcher, firstBase, secondBase, thirdBase, shortstop, leftField, centerField, rightField
- Display names mapped in `POSITION_CONFIG` in utils-enhanced.ts

### Competitive Mode Scoring
When `competitiveMode` is enabled:
1. Players get scores based on position ratings (1-5 scale)
2. Star players get bonuses for key positions
3. Algorithm uses `findCompetitiveAssignment()` instead of standard assignment
4. Minimum playing time still enforced via `competitiveMinPlayingTime`

### Common Issues & Solutions

1. **Missing Position Assignments**: The rotation algorithm has fallback logic to ensure all 9 positions are filled
2. **Bench Streak Violations**: Force rotation when players exceed `maxConsecutiveBenchInnings`
3. **Position Eligibility**: Check `canPlayPosition()` which respects lockouts and restrictions

## Testing Approach

- Unit tests in `src/__tests__/` using Vitest
- Key test files:
  - `lineup-generation.test.ts`: Core algorithm tests
  - `rotation.test.ts`: Rotation logic tests
- Test utilities in various `test-*.js` files in root

## Environment Variables

Required in `.env.local`:
- `VITE_SUPABASE_URL`
- `VITE_SUPABASE_ANON_KEY`
- `VITE_STRIPE_PUBLISHABLE_KEY`

## Deployment Notes

- Frontend deployed to Netlify/Vercel
- Supabase handles backend/database
- Edge functions in `supabase/functions/`
- Stripe webhook endpoint required for payments

---

## Authentication & Session Persistence

**Session Persistence Debugging**

Add a helper in `authUtils.ts` to inspect stored Supabase tokens:
```ts
export function debugAuthStorage() {
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key?.startsWith("sb-")) {
      console.log(`${key} →`, localStorage.getItem(key));
    }
  }
}
```

**Prompt for Claude Code:**

```
Hey Claude, before editing any code, output a detailed plan for verifying JWT session persistence:
1. Confirm `supabaseClient.ts` is configured with `persistSession: true`, `autoRefreshToken: true`, and `storage: localStorage`.
2. Search the codebase for any calls to `localStorage.clear()` or similar that could remove `sb-` tokens.
3. Locate where `signInWithPassword` runs and where we set or clear tokens in AuthContext and SignIn flows.
4. Outline how to implement and test `debugAuthStorage()` and a small Node script (`verify-session-persistence.js`) to validate persistence across reloads.
```

**Usage Instructions:**
- After adding `debugAuthStorage()`, open your app in the browser, run `debugAuthStorage()` pre- and post-refresh, and confirm the same `sb-…` tokens persist.

## Plan Approval & Execution

- **Always produce a detailed plan before making code edits.** 
  When prompted to apply changes, first output a "### Plan" section with clear steps.
- **Wait for explicit user confirmation** (e.g., "LGTM", "Proceed") before executing any code modifications.
- Use prompts like:
  ```
  think deeply: outline the plan
  ```
  or
  ```
  please output a detailed plan and await approval
  ```
  to ensure planning mode is activated.

## Tool Call Resilience

- **Handle diff or replace failures**: If a tool call fails, try alternative diff scopes up to 5 times.
- **Fallback file approach**: When all retries fail, create a temporary file `{original}_fixed.{ext}`, apply edits there, then replace the original file.
- **Error logging**: On any tool error, log the failure context and continue to the next approach.

## Tuning & Iteration

- Continually refine `CLAUDE.md` as the codebase evolves.
- Tag new tips or edge cases with `#` and commit updates to keep the guide current.
-- CRITICAL FIX: Allow users to create their own profile and subscription records
-- The current RLS policies are blocking INSERT operations

-- First, check current policies
SELECT 
    tablename,
    policyname,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('profiles', 'subscriptions')
ORDER BY tablename, cmd;

-- Drop existing INSERT policies that might be blocking
DROP POLICY IF EXISTS "profiles_insert_own_v2" ON profiles;
DROP POLICY IF EXISTS "subscriptions_insert_own_v2" ON subscriptions;

-- Create proper INSERT policies that allow users to create their own records
CREATE POLICY "profiles_insert_own_v3" ON profiles
    FOR INSERT 
    WITH CHECK (auth.uid() = id);

CREATE POLICY "subscriptions_insert_own_v3" ON subscriptions
    FOR INSERT 
    WITH CHECK (auth.uid() = user_id);

-- Also ensure UPDATE policies exist
DROP POLICY IF EXISTS "profiles_update_own_v2" ON profiles;
CREATE POLICY "profiles_update_own_v3" ON profiles
    FOR UPDATE 
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

DROP POLICY IF EXISTS "subscriptions_update_own_v2" ON subscriptions;
CREATE POLICY "subscriptions_update_own_v3" ON subscriptions
    FOR UPDATE 
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Verify the new policies
SELECT 
    tablename,
    policyname,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('profiles', 'subscriptions')
ORDER BY tablename, cmd;

-- Also check if there's a trigger preventing profile creation
SELECT 
    trigger_name,
    event_manipulation,
    event_object_table,
    action_statement
FROM information_schema.triggers
WHERE event_object_schema = 'public'
AND event_object_table IN ('profiles', 'subscriptions');

-- CRITICAL: Ensure auth trigger exists to auto-create profiles
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
    INSERT INTO public.profiles (id, email, created_at, updated_at)
    VALUES (
        new.id, 
        new.email,
        NOW(),
        NOW()
    )
    ON CONFLICT (id) DO UPDATE
    SET 
        email = EXCLUDED.email,
        updated_at = NOW();
    
    -- Also create a starter subscription
    INSERT INTO public.subscriptions (user_id, is_paid, tier, team_limit, created_at, updated_at)
    VALUES (
        new.id,
        false,
        'starter',
        1,
        NOW(),
        NOW()
    )
    ON CONFLICT (user_id) DO NOTHING;
    
    RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Create trigger for new user signups
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Fix the specific user who is having issues
DO $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Find the user
    SELECT id INTO v_user_id
    FROM auth.users
    WHERE email = '<EMAIL>'
    LIMIT 1;
    
    IF v_user_id IS NOT NULL THEN
        -- Create profile if missing
        INSERT INTO public.profiles (id, email, created_at, updated_at)
        VALUES (v_user_id, '<EMAIL>', NOW(), NOW())
        ON CONFLICT (id) DO NOTHING;
        
        -- Create subscription if missing
        INSERT INTO public.subscriptions (user_id, is_paid, tier, team_limit, created_at, updated_at)
        VALUES (v_user_id, true, 'pro', 10, NOW(), NOW())
        ON CONFLICT (user_id) 
        DO UPDATE SET 
            is_paid = true,
            tier = 'pro',
            team_limit = 10,
            updated_at = NOW();
            
        RAISE NOTICE '<NAME_EMAIL>';
    ELSE
        RAISE NOTICE 'User <EMAIL> not found in auth.users';
    END IF;
END $$;

-- Summary
SELECT 'RLS INSERT policies fixed. Users can now create their own profiles and subscriptions.' as status;
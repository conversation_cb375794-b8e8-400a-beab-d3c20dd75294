const { createServiceClient } = require('./src/integrations/supabase/node-client.cjs');
const fs = require('fs').promises;
const path = require('path');

// Create service client with service role key
const supabase = createServiceClient();

async function applyMigration() {
  try {
    console.log('Reading migration file...');
    const migrationPath = path.join(__dirname, 'supabase', 'migrations', 'fix_lineup_innings_admin_policies.sql');
    const migrationSQL = await fs.readFile(migrationPath, 'utf8');
    
    console.log('Applying lineup_innings RLS fix migration...');
    
    // Split by semicolons but be careful about statements that might contain semicolons in strings
    const statements = migrationSQL
      .split(/;\s*$|;\s*\n/m)
      .filter(stmt => stmt.trim().length > 0)
      .map(stmt => stmt.trim() + ';');
    
    for (const statement of statements) {
      if (statement.trim().length > 0 && !statement.trim().startsWith('--')) {
        console.log('\nExecuting:', statement.substring(0, 100) + '...');
        
        const { error } = await supabase.rpc('execute_sql', {
          sql: statement
        }).single();
        
        if (error) {
          // Try direct execution as fallback
          const { error: directError, data } = await supabase
            .from('_sql')
            .insert({ query: statement })
            .select();
            
          if (directError) {
            console.error('Error executing statement:', directError);
            console.error('Statement:', statement);
            
            // Continue with other statements even if one fails
            continue;
          }
        }
        
        console.log('✓ Success');
      }
    }
    
    console.log('\n✅ Migration applied successfully!');
    
    // Verify the policies were created
    console.log('\nVerifying policies...');
    const { data: policies, error: verifyError } = await supabase
      .rpc('execute_sql', {
        sql: `
          SELECT schemaname, tablename, policyname 
          FROM pg_policies 
          WHERE tablename IN ('lineup_innings', 'lineups', 'lineup_attendance')
          ORDER BY tablename, policyname;
        `
      });
      
    if (!verifyError && policies) {
      console.log('\nCurrent policies:');
      console.log(policies);
    }
    
  } catch (error) {
    console.error('Error applying migration:', error);
    process.exit(1);
  }
}

// Alternative approach using direct SQL execution
async function applyMigrationDirect() {
  try {
    const { execSync } = require('child_process');
    
    console.log('Applying migration using direct database connection...');
    
    // Get the database URL from Supabase
    const dbUrl = process.env.DATABASE_URL || process.env.SUPABASE_DB_URL;
    
    if (!dbUrl) {
      console.error('No database URL found. Please set DATABASE_URL or SUPABASE_DB_URL in your .env.local file');
      console.log('\nYou can find your database URL in your Supabase project settings:');
      console.log('1. Go to https://app.supabase.com/project/YOUR_PROJECT_ID/settings/database');
      console.log('2. Copy the "Connection string" (URI format)');
      console.log('3. Add it to your .env.local file as DATABASE_URL=your_connection_string');
      return;
    }
    
    const migrationPath = path.join(__dirname, 'supabase', 'migrations', 'fix_lineup_innings_admin_policies.sql');
    
    // Use psql to apply the migration
    execSync(`psql "${dbUrl}" -f "${migrationPath}"`, { stdio: 'inherit' });
    
    console.log('\n✅ Migration applied successfully!');
    
  } catch (error) {
    console.error('Direct migration failed:', error.message);
    console.log('\nTrying RPC method...');
    await applyMigration();
  }
}

// Run the migration
applyMigrationDirect().catch(error => {
  console.error('Failed to apply migration:', error);
  process.exit(1);
});
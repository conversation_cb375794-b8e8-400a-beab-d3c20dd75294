import { createServiceClient } from "./src/integrations/supabase/node-client.js";

// Create service clients
const supabaseAdmin = createServiceClient();
const supabase = createServiceClient();

async function testUserCreation() {
  const testEmail = 'testuser' + Date.now() + '@example.com';
  const testPassword = 'TestPassword123!';
  
  console.log('Testing user creation with email:', testEmail);
  
  try {
    // 1. Create user via auth
    console.log('\n1. Creating user via auth...');
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword
    });
    
    if (authError) {
      console.error('Auth signup error:', authError);
      return;
    }
    
    console.log('User created successfully:', authData.user?.id);
    
    // 2. Sign in as the user
    console.log('\n2. Signing in as user...');
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: testEmail,
      password: testPassword
    });
    
    if (signInError) {
      console.error('Sign in error:', signInError);
      return;
    }
    
    console.log('Signed in successfully');
    
    // 3. Try to create profile with admin client (bypass RLS)
    console.log('\n3. Creating profile with admin client...');
    const { data: profileData, error: profileError } = await supabaseAdmin
      .from('profiles')
      .insert({
        id: signInData.user.id,
        email: testEmail,
        full_name: 'Test User'
      });
      
    if (profileError) {
      console.error('Profile creation error (admin):', profileError);
    } else {
      console.log('Profile created successfully with admin client');
    }
    
    // 4. Try to create subscription with admin client
    console.log('\n4. Creating subscription with admin client...');
    const { data: subData, error: subError } = await supabaseAdmin
      .from('subscriptions')
      .insert({
        user_id: signInData.user.id,
        is_paid: false,
        tier: 'starter',
        team_limit: 0
      });
      
    if (subError) {
      console.error('Subscription creation error (admin):', subError);
    } else {
      console.log('Subscription created successfully with admin client');
    }
    
    // 5. Try to create team with admin client
    console.log('\n5. Creating team with admin client...');
    const { data: teamData, error: teamError } = await supabaseAdmin
      .from('teams')
      .insert({
        name: 'Test Team',
        user_id: signInData.user.id
      });
      
    if (teamError) {
      console.error('Team creation error (admin):', teamError);
    } else {
      console.log('Team created successfully with admin client');
    }
    
    // 6. Now try with regular client to see if it works
    console.log('\n6. Testing with regular client...');
    const { data: regularProfile, error: regularError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', signInData.user.id)
      .single();
      
    if (regularError) {
      console.error('Regular client select error:', regularError);
    } else {
      console.log('Regular client can read profile:', regularProfile);
    }
    
    console.log('\n✅ Test complete. Check if the user can now log in via the UI.');
    
  } catch (error) {
    console.error('Unexpected error:', error);
  } finally {
    // Sign out
    await supabase.auth.signOut();
  }
}

// Run the test
testUserCreation();
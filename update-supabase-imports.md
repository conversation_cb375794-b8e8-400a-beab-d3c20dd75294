# Supabase Import Update Guide

## Overview
We're centralizing all Supabase client creation to avoid duplicate instances and ensure consistent configuration.

## New Import Patterns

### For ES Modules (.js, .mjs files)

#### Regular client (using anon key):
```javascript
import { supabase } from './src/integrations/supabase/node-client.js';
```

#### Service client (using service role key):
```javascript
import { createServiceClient } from './src/integrations/supabase/node-client.js';
const supabase = createServiceClient();
```

### For CommonJS (.cjs files)

#### Regular client:
```javascript
const { supabase } = require('./src/integrations/supabase/node-client.cjs');
```

#### Service client:
```javascript
const { createServiceClient } = require('./src/integrations/supabase/node-client.cjs');
const supabase = createServiceClient();
```

## Files to Update

### ES Module Files (.js) - Regular Client
These files use the anon key and should import the regular supabase client:
- check-user-subscription.js
- check-demo-account.js
- test-supabase.js
- debug-payment-status.js
- check-profiles.js
- verify-admin-setup.js

### ES Module Files (.js) - Service Client
These files use the service role key and need createServiceClient:
- apply-admin-migration.js
- create-paid-user.js
- admin-create-paid-user.js
- apply-subscription-migration.js

### CommonJS Files (.cjs) - Service Client
- fix-user-auth-immediate.cjs
- force-fix-user.cjs
- add-game-result-column.cjs

## Migration Steps

1. Remove these imports:
   - `import { createClient } from '@supabase/supabase-js'`
   - `const { createClient } = require('@supabase/supabase-js')`
   - `import dotenv from 'dotenv'` (if only used for Supabase)
   - `require('dotenv').config()` (if only used for Supabase)

2. Remove these lines:
   - Environment variable declarations for Supabase
   - createClient calls
   - Error checking for missing env vars

3. Add the appropriate import from the guide above

4. If the file uses special auth configuration, check if it needs the service client
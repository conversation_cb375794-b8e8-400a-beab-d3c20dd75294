// Test script for session persistence debugging
// Run this in browser console

const debugSessionPersistence = {
  // Check current auth state
  checkAuthState: async () => {
    console.log('=== Current Auth State ===');
    const { data: { session } } = await window.supabase.auth.getSession();
    console.log('Session exists:', !!session);
    if (session) {
      console.log('User email:', session.user.email);
      console.log('Session expires at:', new Date(session.expires_at * 1000));
    }
    console.log('SessionStorage auth flag:', sessionStorage.getItem('auth_session_initialized'));
    return session;
  },

  // Monitor auth state changes
  monitorAuthChanges: () => {
    console.log('=== Monitoring Auth State Changes ===');
    const { data } = window.supabase.auth.onAuthStateChange((event, session) => {
      console.log(`Auth Event: ${event}`);
      console.log('Session:', session ? `User: ${session.user.email}` : 'No session');
      console.log('Timestamp:', new Date().toISOString());
    });
    console.log('Monitor started. Refresh the page to see auth events.');
    return data;
  },

  // Check Supabase client
  checkSupabaseClient: () => {
    console.log('=== Supabase Client Check ===');
    console.log('Supabase client exists:', !!window.supabase);
    console.log('Auth namespace exists:', !!window.supabase?.auth);
    console.log('Storage key:', window.supabase?.storageKey || 'Not accessible');
  },

  // Force session refresh
  forceRefreshSession: async () => {
    console.log('=== Forcing Session Refresh ===');
    try {
      const { data, error } = await window.supabase.auth.refreshSession();
      if (error) {
        console.error('Refresh failed:', error);
      } else {
        console.log('Session refreshed successfully');
        console.log('New session:', data.session ? `User: ${data.session.user.email}` : 'No session');
      }
    } catch (e) {
      console.error('Error refreshing:', e);
    }
  },

  // Check localStorage for auth tokens
  checkLocalStorage: () => {
    console.log('=== LocalStorage Auth Tokens ===');
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.includes('supabase')) {
        console.log(key, ':', localStorage.getItem(key).substring(0, 50) + '...');
      }
    }
  }
};

// Make available globally
window.debugSession = debugSessionPersistence;

console.log('Session Debug Tools Loaded');
console.log('Available commands:');
console.log('- debugSession.checkAuthState()');
console.log('- debugSession.monitorAuthChanges()');
console.log('- debugSession.checkSupabaseClient()');
console.log('- debugSession.forceRefreshSession()');
console.log('- debugSession.checkLocalStorage()');
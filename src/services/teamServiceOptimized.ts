import { supabase } from "@/supabaseClient";
import { Team, Player, Lineup, RotationRules, InningLineup } from "@/contexts/TeamContext";
import { getDefaultRotationRules } from "./teamService";

// Optimized version that loads data in parallel
export const fetchTeamsOptimized = async (userId: string): Promise<Team[]> => {
  try {
    console.time('fetchTeamsOptimized');
    
    // 1. Fetch all teams for the user
    const { data: teamsData, error: teamsError } = await supabase
      .from('teams')
      .select('*')
      .eq('user_id', userId);

    if (teamsError) throw teamsError;
    
    if (!teamsData || teamsData.length === 0) {
      console.timeEnd('fetchTeamsOptimized');
      return [];
    }

    console.log(`Loading ${teamsData.length} teams...`);

    // 2. Fetch ALL related data in parallel
    const teamIds = teamsData.map(t => t.id);
    
    const [
      playersResult,
      lineupsResult,
      rotationRulesResult,
      inningsResult,
      attendanceResult,
      battingOrdersResult
    ] = await Promise.all([
      // Get all players for all teams
      supabase
        .from('players')
        .select('*')
        .in('team_id', teamIds),
      
      // Get all lineups for all teams
      supabase
        .from('lineups')
        .select('*')
        .in('team_id', teamIds),
      
      // Get all rotation rules
      supabase
        .from('rotation_rules')
        .select('*')
        .in('team_id', teamIds),
      
      // Get ALL innings for ALL lineups at once
      supabase
        .from('lineup_innings')
        .select('*')
        .in('lineup_id', 
          await supabase
            .from('lineups')
            .select('id')
            .in('team_id', teamIds)
            .then(res => res.data?.map(l => l.id) || [])
        )
        .order('lineup_id', { ascending: true })
        .order('inning_number', { ascending: true }),
      
      // Get ALL attendance records at once
      supabase
        .from('lineup_attendance')
        .select('*')
        .in('lineup_id',
          await supabase
            .from('lineups')
            .select('id')
            .in('team_id', teamIds)
            .then(res => res.data?.map(l => l.id) || [])
        ),
      
      // Get ALL batting orders at once
      supabase
        .from('batting_orders')
        .select('*')
        .in('lineup_id',
          await supabase
            .from('lineups')
            .select('id')
            .in('team_id', teamIds)
            .then(res => res.data?.map(l => l.id) || [])
        )
    ]);

    // 3. Process errors
    if (playersResult.error) console.error('Error fetching players:', playersResult.error);
    if (lineupsResult.error) console.error('Error fetching lineups:', lineupsResult.error);
    if (inningsResult.error) console.error('Error fetching innings:', inningsResult.error);
    if (attendanceResult.error) console.error('Error fetching attendance:', attendanceResult.error);

    // 4. Create lookup maps for O(1) access
    const playersByTeam = new Map<string, Player[]>();
    const lineupsByTeam = new Map<string, any[]>();
    const rulesByTeam = new Map<string, any>();
    const inningsByLineup = new Map<string, any[]>();
    const attendanceByLineup = new Map<string, any[]>();
    const battingOrderByLineup = new Map<string, any>();

    // Group players by team
    playersResult.data?.forEach(player => {
      const teamPlayers = playersByTeam.get(player.team_id) || [];
      teamPlayers.push(player);
      playersByTeam.set(player.team_id, teamPlayers);
    });

    // Group lineups by team
    lineupsResult.data?.forEach(lineup => {
      const teamLineups = lineupsByTeam.get(lineup.team_id) || [];
      teamLineups.push(lineup);
      lineupsByTeam.set(lineup.team_id, teamLineups);
    });

    // Group rotation rules by team
    rotationRulesResult.data?.forEach(rule => {
      rulesByTeam.set(rule.team_id, rule);
    });

    // Group innings by lineup
    inningsResult.data?.forEach(inning => {
      const lineupInnings = inningsByLineup.get(inning.lineup_id) || [];
      lineupInnings.push(inning);
      inningsByLineup.set(inning.lineup_id, lineupInnings);
    });

    // Group attendance by lineup
    attendanceResult.data?.forEach(att => {
      const lineupAttendance = attendanceByLineup.get(att.lineup_id) || [];
      lineupAttendance.push(att);
      attendanceByLineup.set(att.lineup_id, lineupAttendance);
    });

    // Group batting orders by lineup
    battingOrdersResult.data?.forEach(order => {
      battingOrderByLineup.set(order.lineup_id, order);
    });

    // 5. Assemble teams with all their data
    const teams: Team[] = teamsData.map(teamData => {
      // Get players for this team
      const teamPlayers = (playersByTeam.get(teamData.id) || []).map(player => {
        const preferences = player.position_preferences || {};
        const teamRoles = preferences.teamRoles || {};
        const pitcherStrategy = preferences.pitcherStrategy || undefined;
        const isStarPlayer = preferences.isStarPlayer || false;
        const { teamRoles: _ignore1, pitcherStrategy: _ignore2, isStarPlayer: _ignore3, ...legacyPreferences } = preferences;
        
        return {
          id: player.id,
          name: player.name,
          positionPreferences: legacyPreferences,
          teamRoles: teamRoles,
          pitcherStrategy: pitcherStrategy,
          isStarPlayer: isStarPlayer
        };
      });

      // Get lineups for this team
      const teamLineups = (lineupsByTeam.get(teamData.id) || []).map(lineupData => {
        // Get innings for this lineup
        const innings: InningLineup[] = (inningsByLineup.get(lineupData.id) || [])
          .sort((a, b) => a.inning_number - b.inning_number)
          .map(inning => ({
            inning: inning.inning_number,
            positions: inning.positions as any
          }));

        // Get attendance for this lineup
        const attendance: {[playerId: string]: boolean} = {};
        (attendanceByLineup.get(lineupData.id) || []).forEach(record => {
          attendance[record.player_id] = record.is_present;
        });

        // Get batting order
        const battingOrderData = battingOrderByLineup.get(lineupData.id);
        const battingOrder = battingOrderData?.player_order || [];

        return {
          id: lineupData.id,
          name: lineupData.name,
          gameDate: lineupData.game_date,
          createdDate: lineupData.created_at,
          attendance,
          innings,
          battingOrder,
          rotationSettings: lineupData.rotation_settings || undefined,
          gameResult: lineupData.game_result || null,
          seriesTitle: lineupData.series_title || undefined,
          seriesId: lineupData.series_id || undefined,
          gameNumber: lineupData.game_number || undefined,
          totalGamesInSeries: lineupData.total_games_in_series || undefined
        };
      });

      // Get rotation rules
      const rotationRule = rulesByTeam.get(teamData.id);
      const rotationRules = rotationRule ? {
        rotationMethod: rotationRule.rotation_method,
        equalPlayingTime: rotationRule.equal_playing_time,
        rotatePlayers: rotationRule.rotate_players,
        respectPositionLockouts: rotationRule.respect_position_lockouts,
        allowPitcherRotation: rotationRule.allow_pitcher_rotation,
        allowCatcherRotation: rotationRule.allow_catcher_rotation,
        prioritizeOutfieldRotation: rotationRule.prioritize_outfield_rotation,
        limitBenchTime: rotationRule.limit_bench_time,
        rotateLineupEvery: rotationRule.rotate_lineup_every || 1,
        rotatePitcherEvery: rotationRule.rotate_pitcher_every || 2,
        competitiveMode: rotationRule.competitive_mode || false,
        competitiveMinPlayingTime: rotationRule.competitive_min_playing_time || 50
      } : getDefaultRotationRules();

      return {
        id: teamData.id,
        name: teamData.name,
        players: teamPlayers,
        lineups: teamLineups,
        rotationRules,
        defaultBattingOrder: teamData.default_batting_order || undefined
      };
    });

    console.timeEnd('fetchTeamsOptimized');
    console.log(`Loaded ${teams.length} teams with ${teams.reduce((sum, t) => sum + t.lineups.length, 0)} total lineups`);
    
    return teams;
  } catch (error) {
    console.error('Error in fetchTeamsOptimized:', error);
    throw error;
  }
};
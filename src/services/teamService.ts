import { supabase } from "@/supabaseClient";
import { Team, Player, Lineup, RotationRules, InningLineup } from "@/contexts/TeamContext";
import { savePlayerPositionHistory } from "@/lib/utils-enhanced";

// Cache for checking column existence
let gameResultColumnExists: boolean | null = null;

// Helper function to check if game_result column exists
const checkGameResultColumn = async (): Promise<boolean> => {
  // If we've already checked, return cached result
  if (gameResultColumnExists !== null) {
    return gameResultColumnExists;
  }

  // Check localStorage first to avoid repeated queries
  const cachedResult = localStorage.getItem('game_result_migration_applied');
  if (cachedResult === 'true') {
    gameResultColumnExists = true;
    return true;
  }

  try {
    // Try to query the column directly with a safer approach
    const { data, error } = await supabase
      .from('lineups')
      .select('id')
      .limit(1);
    
    if (error) {
      console.log('Error checking game_result column, assuming it does not exist');
      gameResultColumnExists = false;
      return false;
    }
    
    // If basic query works, assume column exists (safer for batch operations)
    gameResultColumnExists = true;
    localStorage.setItem('game_result_migration_applied', 'true');
    return true;
  } catch (e) {
    // If we can't determine from database, assume it doesn't exist
    gameResultColumnExists = false;
    return false;
  }
};

// Team CRUD operations
export const fetchTeams = async (userId: string): Promise<Team[]> => {
  try {
    const { data: teamsData, error } = await supabase
      .from('teams')
      .select('*')
      .eq('user_id', userId);

    if (error) {
      console.error('Error fetching teams:', error);
      throw error;
    }

    // For each team, fetch its players and lineups
    const teams: Team[] = [];
    for (const teamData of teamsData) {
      const players = await fetchPlayers(teamData.id);
      const lineups = await fetchLineups(teamData.id);
      const rotationRules = await fetchRotationRules(teamData.id);

      teams.push({
        id: teamData.id,
        name: teamData.name,
        players,
        lineups,
        rotationRules: rotationRules || getDefaultRotationRules(),
        defaultBattingOrder: teamData.default_batting_order || undefined
      });
    }

    return teams;
  } catch (error) {
    console.error('Error in fetchTeams:', error);
    throw error;
  }
};

export const createTeam = async (userId: string, name: string, id?: string): Promise<Team> => {
  try {
    // First check team limits
    const { data: limitCheck, error: limitError } = await supabase
      .rpc('check_team_limit', { p_user_id: userId });
    
    if (limitError) {
      console.error('Error checking team limit:', limitError);
      throw new Error('Unable to check team limit. Please try again.');
    }
    
    const { can_create, current_count, team_limit, tier } = limitCheck[0];
    
    if (!can_create) {
      const errorMessage = tier === 'free' 
        ? 'Please subscribe to create teams.' 
        : `You've reached your team limit (${current_count}/${team_limit}). Please upgrade your plan to create more teams.`;
      throw new Error(errorMessage);
    }
    
    // Check if a team with this name already exists for this user
    const existingTeams = await fetchTeams(userId);
    const existingTeam = existingTeams.find(team => team.name === name);

    if (existingTeam) {
      console.log(`Team with name "${name}" already exists for user, returning existing team`);
      return existingTeam;
    }

    const teamData = { name, user_id: userId };
    if (id) {
      teamData['id'] = id;
    }

    const { data, error } = await supabase
      .from('teams')
      .insert([teamData])
      .select()
      .single();

    if (error) {
      // Check if this is a unique constraint violation
      if (error.code === '23505' && error.message.includes('unique_team_name_per_user')) {
        console.log('Team name already exists, fetching existing team');
        const teams = await fetchTeams(userId);
        const existingTeam = teams.find(team => team.name === name);
        if (existingTeam) {
          return existingTeam;
        }
      }
      console.error('Error creating team:', error);
      throw error;
    }

    // Create default rotation rules for the team
    const rotationRules = getDefaultRotationRules();
    await createRotationRules(data.id, rotationRules, userId);

    return {
      id: data.id,
      name: data.name,
      players: [],
      lineups: [],
      rotationRules
    };
  } catch (error) {
    console.error('Error in createTeam:', error);
    throw error;
  }
};

export const updateTeam = async (team: Team): Promise<Team> => {
  try {
    const { error } = await supabase
      .from('teams')
      .update({ name: team.name })
      .eq('id', team.id);

    if (error) {
      console.error('Error updating team:', error);
      throw error;
    }

    // Update rotation rules
    await updateRotationRules(team.id, team.rotationRules);

    return team;
  } catch (error) {
    console.error('Error in updateTeam:', error);
    throw error;
  }
};

export const deleteTeam = async (teamId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('teams')
      .delete()
      .eq('id', teamId);

    if (error) {
      console.error('Error deleting team:', error);
      throw error;
    }
  } catch (error) {
    console.error('Error in deleteTeam:', error);
    throw error;
  }
};

// Player CRUD operations
export const fetchPlayers = async (teamId: string): Promise<Player[]> => {
  try {
    const { data, error } = await supabase
      .from('players')
      .select('*')
      .eq('team_id', teamId);

    if (error) {
      console.error('Error fetching players:', error);
      throw error;
    }

    console.log('teamService.fetchPlayers: Raw data from database:', data.map(p => ({
      name: p.name,
      position_preferences: p.position_preferences
    })));

    return data.map(player => {
      // All data is stored in position_preferences JSON column
      const preferences = player.position_preferences || {};
      
      // Extract teamRoles, pitcherStrategy, and isStarPlayer from position_preferences
      const teamRoles = preferences.teamRoles || {};
      const pitcherStrategy = preferences.pitcherStrategy || undefined;
      const isStarPlayer = preferences.isStarPlayer || false;
      
      // Extract only actual position preferences (not teamRoles, pitcherStrategy, or isStarPlayer)
      const { teamRoles: _ignore1, pitcherStrategy: _ignore2, isStarPlayer: _ignore3, ...legacyPreferences } = preferences;
      
      console.log(`fetchPlayers: Loading player ${player.name} with teamRoles:`, teamRoles, 'isStarPlayer:', isStarPlayer);

      return {
        id: player.id,
        name: player.name,
        positionPreferences: legacyPreferences,
        teamRoles: teamRoles,
        pitcherStrategy: pitcherStrategy,
        isStarPlayer: isStarPlayer
      };
    });
  } catch (error) {
    console.error('Error in fetchPlayers:', error);
    throw error;
  }
};

export const createPlayer = async (userId: string, teamId: string, player: Player): Promise<Player> => {
  try {
    const { data, error } = await supabase
      .from('players')
      .insert([{
        name: player.name,
        team_id: teamId,
        user_id: userId,
        position_preferences: {
          ...player.positionPreferences || {},
          teamRoles: player.teamRoles || {},
          pitcherStrategy: player.pitcherStrategy || null,
          isStarPlayer: player.isStarPlayer || false
        }
      }])
      .select()
      .single();

    if (error) {
      console.error('Error creating player:', error);
      throw error;
    }

    return {
      id: data.id,
      name: data.name,
      positionPreferences: (() => {
        const prefs = data.position_preferences || {};
        const { teamRoles, pitcherStrategy, isStarPlayer, ...legacyPrefs } = prefs;
        return legacyPrefs;
      })(),
      teamRoles: data.position_preferences?.teamRoles || {},
      pitcherStrategy: data.position_preferences?.pitcherStrategy || undefined,
      isStarPlayer: data.position_preferences?.isStarPlayer || false
    };
  } catch (error) {
    console.error('Error in createPlayer:', error);
    throw error;
  }
};

export const updatePlayer = async (player: Player): Promise<Player> => {
  try {
    console.log('teamService.updatePlayer: Starting update for player:', {
      id: player.id,
      name: player.name,
      teamRoles: player.teamRoles,
      pitcherStrategy: player.pitcherStrategy
    });

    const updateData = {
      name: player.name,
      position_preferences: {
        ...(player.positionPreferences || {}),
        teamRoles: player.teamRoles || {},
        pitcherStrategy: player.pitcherStrategy || null,
        isStarPlayer: player.isStarPlayer || false
      }
    };

    console.log('teamService.updatePlayer: Update data prepared:', updateData);
    console.log('teamService.updatePlayer: teamRoles pitcher value:', updateData.position_preferences?.teamRoles?.pitcher);

    const { data, error } = await supabase
      .from('players')
      .update(updateData)
      .eq('id', player.id)
      .select()
      .single();
      
    // Add a small delay to ensure database has processed the update
    await new Promise(resolve => setTimeout(resolve, 100));

    if (error) {
      console.error('teamService.updatePlayer: Database error:', error);
      
      // If it's a size error, try to save without position preferences
      if (error.message?.includes('value too long') || error.code === '22001') {
        console.warn('teamService.updatePlayer: Retrying without position_preferences due to size limit');
        
        const minimalUpdateData = {
          name: player.name
        };
        
        const { data: retryData, error: retryError } = await supabase
          .from('players')
          .update(minimalUpdateData)
          .eq('id', player.id)
          .select()
          .single();
          
        if (retryError) {
          console.error('teamService.updatePlayer: Retry also failed:', retryError);
          throw retryError;
        }
        
        // Save the JSON data separately in smaller chunks
        const teamRolesUpdate = {
          position_preferences: {
            teamRoles: player.teamRoles || {}
          }
        };
        
        await supabase
          .from('players')
          .update(teamRolesUpdate)
          .eq('id', player.id);
          
        console.log('teamService.updatePlayer: Update successful after retry');
        return player;
      }
      
      throw error;
    }

    console.log('teamService.updatePlayer: Update successful, returned data:', data);
    console.log('teamService.updatePlayer: Saved teamRoles:', data?.position_preferences?.teamRoles);
    console.log('teamService.updatePlayer: Saved pitcher role specifically:', data?.position_preferences?.teamRoles?.pitcher);
    
    // Return the player with the confirmed saved data
    return {
      ...player,
      teamRoles: data?.position_preferences?.teamRoles || player.teamRoles,
      pitcherStrategy: data?.position_preferences?.pitcherStrategy || player.pitcherStrategy
    };
  } catch (error) {
    console.error('teamService.updatePlayer: Exception caught:', error);
    throw error;
  }
};

export const deletePlayer = async (playerId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('players')
      .delete()
      .eq('id', playerId);

    if (error) {
      console.error('Error deleting player:', error);
      throw error;
    }
  } catch (error) {
    console.error('Error in deletePlayer:', error);
    throw error;
  }
};

// Lineup CRUD operations
export const fetchLineups = async (teamId: string): Promise<Lineup[]> => {
  try {
    // Fetch basic lineup data (include game_result if column exists)
    let selectColumns = 'id, name, team_id, user_id, game_date, rotation_settings, created_at, updated_at';
    
    // Check if game_result column exists
    const hasGameResult = await checkGameResultColumn();
    if (hasGameResult) {
      selectColumns += ', game_result';
    }
    
    // Include series metadata columns
    selectColumns += ', series_title, series_id, game_number, total_games_in_series';
    
    const { data: lineupsData, error: lineupsError } = await supabase
      .from('lineups')
      .select(selectColumns)
      .eq('team_id', teamId);

    if (lineupsError) {
      console.error('Error fetching lineups:', lineupsError);
      throw lineupsError;
    }

    // Create an array to hold the complete lineup objects
    const lineups: Lineup[] = [];

    // For each lineup, fetch its innings, attendance, and batting order
    for (const lineupData of lineupsData) {
      // Fetch innings
      const { data: inningsData, error: inningsError } = await supabase
        .from('lineup_innings')
        .select('*')
        .eq('lineup_id', lineupData.id)
        .order('inning_number', { ascending: true });

      if (inningsError) {
        console.error('Error fetching innings:', inningsError);
        throw inningsError;
      }

      // Convert innings data to the format expected by the app
      const innings: InningLineup[] = inningsData.map(inning => ({
        inning: inning.inning_number,
        positions: inning.positions as any
      }));

      // Fetch attendance
      const { data: attendanceData, error: attendanceError } = await supabase
        .from('lineup_attendance')
        .select('*')
        .eq('lineup_id', lineupData.id);

      if (attendanceError) {
        console.error('Error fetching attendance:', attendanceError);
        throw attendanceError;
      }

      // Convert attendance data to the format expected by the app
      const attendance: {[playerId: string]: boolean} = {};
      attendanceData.forEach(record => {
        attendance[record.player_id] = record.is_present;
      });

      // Fetch batting order (optional - might not exist)
      const { data: battingOrderData, error: battingOrderError } = await supabase
        .from('batting_orders')
        .select('*')
        .eq('lineup_id', lineupData.id)
        .maybeSingle(); // Use maybeSingle() instead of single() to handle missing records

      // Batting order might not exist yet
      let battingOrder: string[] = [];
      if (!battingOrderError && battingOrderData) {
        battingOrder = battingOrderData.player_order as string[] || [];
      } else if (battingOrderError && battingOrderError.code !== 'PGRST116') {
        // Log error only if it's not a "no rows returned" error
        console.warn('Error fetching batting order (non-critical):', battingOrderError);
      }

      // Create the complete lineup object
      const lineupObject: Lineup = {
        id: lineupData.id,
        name: lineupData.name,
        gameDate: lineupData.game_date,
        createdDate: lineupData.created_at,
        attendance,
        innings,
        battingOrder,
        // Include rotation settings if they exist
        rotationSettings: lineupData.rotation_settings || undefined,
        // Include series metadata if present
        seriesTitle: lineupData.series_title || undefined,
        seriesId: lineupData.series_id || undefined,
        gameNumber: lineupData.game_number || undefined,
        totalGamesInSeries: lineupData.total_games_in_series || undefined
      };
      
      // Include game result if column exists and data is present
      if (hasGameResult && 'game_result' in lineupData) {
        lineupObject.gameResult = lineupData.game_result as GameResult;
      }
      
      lineups.push(lineupObject);
    }

    return lineups;
  } catch (error) {
    console.error('Error in fetchLineups:', error);
    throw error;
  }
};

export const createLineup = async (userId: string, teamId: string, lineup: Lineup): Promise<Lineup> => {
  try {
    // First, test the table structure (exclude game_result for now)
    console.log('Testing lineups table structure...');
    const { data: testData, error: testError } = await supabase
      .from('lineups')
      .select('id, name, team_id, user_id, game_date, rotation_settings')
      .limit(1);
    
    if (testError) {
      console.error('Table structure test failed:', testError);
      throw new Error(`Database schema error: ${testError.message}`);
    }
    
    console.log('Table structure test passed. Creating lineup with data:', {
      name: lineup.name,
      team_id: teamId,
      user_id: userId,
      game_date: lineup.gameDate,
      rotation_settings: lineup.rotationSettings
    });

    // First, create the basic lineup record (include game_result if column exists)
    const insertData: any = {
      name: lineup.name,
      team_id: teamId,
      user_id: userId,
      game_date: lineup.gameDate,
      rotation_settings: lineup.rotationSettings || null
    };
    
    // Check if game_result column exists
    const hasGameResult = await checkGameResultColumn();
    if (hasGameResult) {
      insertData.game_result = lineup.gameResult || null;
    }
    
    // Check if series metadata columns exist before trying to use them
    try {
      // Test if series columns exist by doing a simple select
      const { data: testData, error: testError } = await supabase
        .from('lineups')
        .select('series_title, series_id, game_number, total_games_in_series')
        .limit(1);
      
      if (!testError) {
        // Series columns exist, safe to include them
        console.log('Series metadata columns detected, including in insert');
        insertData.series_title = lineup.seriesTitle || null;
        insertData.series_id = lineup.seriesId || null;
        insertData.game_number = lineup.gameNumber || null;
        insertData.total_games_in_series = lineup.totalGamesInSeries || null;
      } else {
        console.log('Series metadata columns not yet available:', testError.message);
      }
    } catch (columnError) {
      console.log('Could not check for series metadata columns, skipping');
    }

    console.log('Inserting lineup data:', insertData);

    const { data: lineupData, error: lineupError } = await supabase
      .from('lineups')
      .insert(insertData)
      .select('*')
      .single();

    if (lineupError) {
      console.error('Error creating lineup - Full error details:', {
        error: lineupError,
        message: lineupError.message,
        details: lineupError.details,
        hint: lineupError.hint,
        code: lineupError.code
      });
      throw lineupError;
    }

    const lineupId = lineupData.id;

    // Insert innings data
    if (lineup.innings && lineup.innings.length > 0) {
      const inningsToInsert = lineup.innings.map(inning => ({
        lineup_id: lineupId,
        inning_number: inning.inning,
        positions: inning.positions,
        user_id: userId
      }));

      const { error: inningsError } = await supabase
        .from('lineup_innings')
        .insert(inningsToInsert);

      if (inningsError) {
        console.error('Error creating innings:', inningsError);
        throw inningsError;
      }

      // Save player position history for each inning
      try {
        // Get all players for this lineup
        const { data: playersData, error: playersError } = await supabase
          .from('players')
          .select('*')
          .eq('team_id', teamId);

        if (playersError) {
          console.error('Error fetching players for position history:', playersError);
        } else {
          // Create player map for position history tracking
          const playerMap = new Map<string, Player>();
          playersData.forEach(player => {
            playerMap.set(player.name, {
              id: player.id,
              name: player.name,
              teamRoles: player.position_preferences?.teamRoles || {},
              positionPreferences: (() => {
                const prefs = player.position_preferences || {};
                const { teamRoles, pitcherStrategy, ...legacyPrefs } = prefs;
                return legacyPrefs;
              })()
            });
          });

          // Set team_id on the lineup for position history tracking
          lineup.team_id = teamId;

          // Save position history for each inning (AFTER lineup is saved to get real ID)
          try {
            const realLineup = { ...lineup, id: lineup.id, team_id: teamId }; // Use real database ID
            for (const inning of lineup.innings) {
              await savePlayerPositionHistory(realLineup, inning, playerMap);
            }
          } catch (historyError) {
            console.warn('Non-critical: Failed to save position history:', historyError);
          }
        }
      } catch (historyError) {
        console.error('Error saving player position history:', historyError);
        // Don't throw here, we still want to continue with the lineup creation
      }
    }

    // Insert attendance data
    if (lineup.attendance) {
      const attendanceToInsert = Object.entries(lineup.attendance).map(([playerId, isPresent]) => ({
        lineup_id: lineupId,
        player_id: playerId,
        is_present: isPresent,
        user_id: userId
      }));

      const { error: attendanceError } = await supabase
        .from('lineup_attendance')
        .insert(attendanceToInsert);

      if (attendanceError) {
        console.error('Error creating attendance:', attendanceError);
        throw attendanceError;
      }
    }

    // Insert batting order if it exists
    if (lineup.battingOrder && lineup.battingOrder.length > 0) {
      const { error: battingOrderError } = await supabase
        .from('batting_orders')
        .insert([{
          lineup_id: lineupId,
          player_order: lineup.battingOrder,
          user_id: userId
        }]);

      if (battingOrderError) {
        console.error('Error creating batting order:', battingOrderError);
        throw battingOrderError;
      }
    }

    // Create the complete lineup object
    const completeLineup = {
      id: lineupId,
      name: lineupData.name,
      gameDate: lineupData.game_date,
      createdDate: lineupData.created_at,
      attendance: lineup.attendance || {},
      innings: lineup.innings || [],
      battingOrder: lineup.battingOrder || [],
      rotationSettings: lineup.rotationSettings || undefined,
      gameResult: lineup.gameResult || null
    };

    // Demo mode now uses the database just like regular mode

    return completeLineup;
  } catch (error) {
    console.error('Error in createLineup:', error);
    throw error;
  }
};

export const updateLineup = async (lineup: Lineup): Promise<Lineup> => {
  try {
    // Demo mode now uses the database just like regular mode

    // First try to refresh the session to ensure we have a valid token
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !sessionData.session) {
      console.error('Session error or missing:', sessionError);
      // Try to refresh the session
      const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
      
      if (refreshError || !refreshData.session) {
        console.error('Failed to refresh session:', refreshError);
        throw new Error('Session expired - please sign in again');
      }
    }

    // Get current user ID once at the beginning and validate
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError) {
      console.error('Error getting user:', userError);
      throw new Error(`Authentication error: ${userError.message}`);
    }

    const userId = userData.user?.id;
    if (!userId) {
      console.error('No user ID found in auth data:', userData);
      throw new Error('User not authenticated - no user ID found');
    }

    console.log('UpdateLineup: Using user ID:', userId);

    // First, verify the lineup exists
    const { data: existingLineup, error: checkError } = await supabase
      .from('lineups')
      .select('id, team_id')
      .eq('id', lineup.id)
      .single();

    if (checkError || !existingLineup) {
      console.error('Lineup not found in database:', lineup.id, checkError);
      throw new Error(`Lineup with ID ${lineup.id} not found in database`);
    }

    // Update the basic lineup record
    const updateData: any = {
      name: lineup.name,
      game_date: lineup.gameDate,
      rotation_settings: lineup.rotationSettings || null
    };
    
    // Check if game_result column exists
    const hasGameResult = await checkGameResultColumn();
    if (hasGameResult) {
      updateData.game_result = lineup.gameResult || null;
    }
    
    // Check if series metadata columns exist before trying to use them
    try {
      const { data: testData, error: testError } = await supabase
        .from('lineups')
        .select('series_title, series_id, game_number, total_games_in_series')
        .limit(1);
      
      if (!testError) {
        // Series columns exist, safe to include them in update
        updateData.series_title = lineup.seriesTitle || null;
        updateData.series_id = lineup.seriesId || null;
        updateData.game_number = lineup.gameNumber || null;
        updateData.total_games_in_series = lineup.totalGamesInSeries || null;
      }
    } catch (columnError) {
      // Series columns don't exist yet, skip them
    }
    
    const { error: lineupError } = await supabase
      .from('lineups')
      .update(updateData)
      .eq('id', lineup.id);

    if (lineupError) {
      console.error('Error updating lineup:', lineupError);
      throw lineupError;
    }

    // Update innings data - first delete existing innings
    const { error: deleteInningsError } = await supabase
      .from('lineup_innings')
      .delete()
      .eq('lineup_id', lineup.id);

    if (deleteInningsError) {
      console.error('Error deleting innings:', deleteInningsError);
      throw deleteInningsError;
    }

    // Then insert new innings data
    if (lineup.innings && lineup.innings.length > 0) {

      const inningsToInsert = lineup.innings.map(inning => ({
        lineup_id: lineup.id,
        inning_number: inning.inning,
        positions: inning.positions,
        user_id: userId
      }));

      console.log(`Inserting ${inningsToInsert.length} innings for lineup ${lineup.id}`);

      const { error: inningsError } = await supabase
        .from('lineup_innings')
        .insert(inningsToInsert);

      if (inningsError) {
        console.error('Error updating innings:', inningsError);
        console.error('Failed innings data:', inningsToInsert);
        // Check if it's a foreign key constraint error
        if (inningsError.message?.includes('foreign key constraint')) {
          throw new Error(`Cannot save innings: lineup ID ${lineup.id} is invalid or has been deleted`);
        }
        throw inningsError;
      }

      // Save player position history for each inning
      try {
        // Get team_id from the lineup record in the database if not present in the lineup object
        let teamId = lineup.team_id;
        if (!teamId) {
          const { data: lineupData, error: lineupError } = await supabase
            .from('lineups')
            .select('team_id')
            .eq('id', lineup.id)
            .single();

          if (lineupError) {
            console.error('Error fetching lineup team_id:', lineupError);
            throw lineupError;
          }

          teamId = lineupData.team_id;
        }

        // Get all players for this lineup
        const { data: playersData, error: playersError } = await supabase
          .from('players')
          .select('*')
          .eq('team_id', teamId);

        if (playersError) {
          console.error('Error fetching players for position history:', playersError);
        } else {
          // Create player map for position history tracking
          const playerMap = new Map<string, Player>();
          playersData.forEach(player => {
            playerMap.set(player.name, {
              id: player.id,
              name: player.name,
              teamRoles: player.position_preferences?.teamRoles || {},
              positionPreferences: (() => {
                const prefs = player.position_preferences || {};
                const { teamRoles, pitcherStrategy, ...legacyPrefs } = prefs;
                return legacyPrefs;
              })()
            });
          });

          // Save position history for each inning (AFTER lineup is saved to get real ID)
          try {
            const realLineup = { ...lineup, id: lineup.id, team_id: teamId }; // Use real database ID
            for (const inning of lineup.innings) {
              await savePlayerPositionHistory(realLineup, inning, playerMap);
            }
          } catch (historyError) {
            console.warn('Non-critical: Failed to save position history:', historyError);
          }
        }
      } catch (historyError) {
        console.error('Error saving player position history:', historyError);
        // Don't throw here, we still want to continue with the lineup update
      }
    }

    // Update attendance data using UPSERT to avoid constraint violations
    if (lineup.attendance) {
      // Use UPSERT to handle existing records gracefully
      const attendanceToUpsert = Object.entries(lineup.attendance).map(([playerId, isPresent]) => ({
        lineup_id: lineup.id,
        player_id: playerId,
        is_present: isPresent,
        user_id: userId
      }));

      const { error: attendanceError } = await supabase
        .from('lineup_attendance')
        .upsert(attendanceToUpsert, {
          onConflict: 'lineup_id,player_id'
        });

      if (attendanceError) {
        console.error('Error updating attendance:', attendanceError);
        throw attendanceError;
      }
    }

    // Update batting order - first check if it exists
    const { data: existingBattingOrder, error: checkBattingOrderError } = await supabase
      .from('batting_orders')
      .select('*')
      .eq('lineup_id', lineup.id)
      .maybeSingle(); // Use maybeSingle() instead of single() to handle missing records

    if (checkBattingOrderError && checkBattingOrderError.code !== 'PGRST116') { // PGRST116 is "not found"
      console.error('Error checking batting order:', checkBattingOrderError);
      throw checkBattingOrderError;
    }

    if (existingBattingOrder) {
      // Update existing batting order
      const { error: battingOrderError } = await supabase
        .from('batting_orders')
        .update({
          player_order: lineup.battingOrder || []
        })
        .eq('id', existingBattingOrder.id);

      if (battingOrderError) {
        console.error('Error updating batting order:', battingOrderError);
        throw battingOrderError;
      }
    } else if (lineup.battingOrder && lineup.battingOrder.length > 0) {
      // Insert new batting order
      const { error: battingOrderError } = await supabase
        .from('batting_orders')
        .insert([{
          lineup_id: lineup.id,
          player_order: lineup.battingOrder,
          user_id: userId
        }]);

      if (battingOrderError) {
        console.error('Error creating batting order:', battingOrderError);
        throw battingOrderError;
      }
    }

    return lineup;
  } catch (error) {
    console.error('Error in updateLineup:', error);
    throw error;
  }
};

export const deleteLineup = async (lineupId: string): Promise<void> => {
  try {
    // Due to cascade delete in the database schema, we only need to delete the lineup record
    // and all related records (innings, attendance, batting order) will be deleted automatically
    const { error } = await supabase
      .from('lineups')
      .delete()
      .eq('id', lineupId);

    if (error) {
      console.error('Error deleting lineup:', error);
      throw error;
    }
  } catch (error) {
    console.error('Error in deleteLineup:', error);
    throw error;
  }
};


// Rotation Rules operations
export const fetchRotationRules = async (teamId: string): Promise<RotationRules | null> => {
  try {
    const { data, error } = await supabase
      .from('rotation_rules')
      .select('*')
      .eq('team_id', teamId)
      .order('created_at', { ascending: false }) // Get the most recent one
      .limit(1); // Only get one record

    if (error) {
      console.error('Error fetching rotation rules:', error);
      // Don't throw the error, just return null to use defaults
      return null;
    }

    // If no data found, return null to use defaults
    if (!data || data.length === 0) {
      return null;
    }

    const rotationRule = data[0]; // Get the first (most recent) record

    return {
      rotationMethod: rotationRule.rotation_method,
      equalPlayingTime: rotationRule.equal_playing_time,
      rotatePlayers: rotationRule.rotate_players,
      respectPositionLockouts: rotationRule.respect_position_lockouts,
      allowPitcherRotation: rotationRule.allow_pitcher_rotation,
      allowCatcherRotation: rotationRule.allow_catcher_rotation,
      prioritizeOutfieldRotation: rotationRule.prioritize_outfield_rotation,
      limitBenchTime: rotationRule.limit_bench_time,
      rotateLineupEvery: rotationRule.rotate_lineup_every || 1,
      rotatePitcherEvery: rotationRule.rotate_pitcher_every || 2,
      // Competitive mode settings
      competitiveMode: rotationRule.competitive_mode || false,
      competitiveMinPlayingTime: rotationRule.competitive_min_playing_time || 50
    };
  } catch (error) {
    console.error('Error in fetchRotationRules:', error);
    // Return null instead of throwing to use default rotation rules
    return null;
  }
};

export const createRotationRules = async (teamId: string, rules: RotationRules, userId?: string): Promise<RotationRules> => {
  try {
    // Get the current user ID if not provided
    const currentUserId = userId || (await supabase.auth.getUser()).data.user?.id;

    if (!currentUserId) {
      throw new Error('User ID is required to create rotation rules');
    }

    const { data, error } = await supabase
      .from('rotation_rules')
      .insert([{
        team_id: teamId,
        user_id: currentUserId,
        rotation_method: rules.rotationMethod,
        equal_playing_time: rules.equalPlayingTime,
        rotate_players: rules.rotatePlayers,
        respect_position_lockouts: rules.respectPositionLockouts,
        allow_pitcher_rotation: rules.allowPitcherRotation,
        allow_catcher_rotation: rules.allowCatcherRotation,
        prioritize_outfield_rotation: rules.prioritizeOutfieldRotation,
        limit_bench_time: rules.limitBenchTime,
        rotate_lineup_every: rules.rotateLineupEvery,
        rotate_pitcher_every: rules.rotatePitcherEvery,
        // Competitive mode settings
        competitive_mode: rules.competitiveMode || false,
        competitive_min_playing_time: rules.competitiveMinPlayingTime || 50
      }])
      .select()
      .single();

    if (error) {
      console.error('Error creating rotation rules:', error);
      throw error;
    }

    return {
      rotationMethod: data.rotation_method,
      equalPlayingTime: data.equal_playing_time,
      rotatePlayers: data.rotate_players,
      respectPositionLockouts: data.respect_position_lockouts,
      allowPitcherRotation: data.allow_pitcher_rotation,
      allowCatcherRotation: data.allow_catcher_rotation,
      prioritizeOutfieldRotation: data.prioritize_outfield_rotation,
      limitBenchTime: data.limit_bench_time,
      rotateLineupEvery: data.rotate_lineup_every || 1,
      rotatePitcherEvery: data.rotate_pitcher_every || 2,
      // Competitive mode settings
      competitiveMode: data.competitive_mode || false,
      competitiveMinPlayingTime: data.competitive_min_playing_time || 50
    };
  } catch (error) {
    console.error('Error in createRotationRules:', error);
    throw error;
  }
};

export const updateRotationRules = async (teamId: string, rules: RotationRules): Promise<RotationRules> => {
  try {
    // First, try to update existing rotation rules
    const { data, error } = await supabase
      .from('rotation_rules')
      .update({
        rotation_method: rules.rotationMethod,
        equal_playing_time: rules.equalPlayingTime,
        rotate_players: rules.rotatePlayers,
        respect_position_lockouts: rules.respectPositionLockouts,
        allow_pitcher_rotation: rules.allowPitcherRotation,
        allow_catcher_rotation: rules.allowCatcherRotation,
        prioritize_outfield_rotation: rules.prioritizeOutfieldRotation,
        limit_bench_time: rules.limitBenchTime,
        rotate_lineup_every: rules.rotateLineupEvery,
        rotate_pitcher_every: rules.rotatePitcherEvery,
        // Competitive mode settings
        competitive_mode: rules.competitiveMode || false,
        competitive_min_playing_time: rules.competitiveMinPlayingTime || 50
      })
      .eq('team_id', teamId)
      .select();

    // If no rows were affected, it means rotation rules don't exist yet, so create them
    if (!error && (!data || data.length === 0)) {
      console.log('No existing rotation rules found, creating new ones for team:', teamId);
      return await createRotationRules(teamId, rules);
    }

    if (error) {
      console.error('Error updating rotation rules:', error);
      throw error;
    }

    return rules;
  } catch (error) {
    console.error('Error in updateRotationRules:', error);
    throw error;
  }
};

// Helper function to get default rotation rules
export const getDefaultRotationRules = (): RotationRules => {
  return {
    rotationMethod: "standard",
    equalPlayingTime: true,
    rotatePlayers: true,
    respectPositionLockouts: true,
    allowPitcherRotation: false,
    allowCatcherRotation: true,
    prioritizeOutfieldRotation: true,
    limitBenchTime: true,
    rotateLineupEvery: 1,
    rotatePitcherEvery: 2,
    // Competitive mode defaults
    competitiveMode: false,
    competitiveMinPlayingTime: 50
  };
};

// Default players for a demo team
const getDefaultPlayers = (): Player[] => {
  return [
    { id: "p1", name: "Mikayla", teamRoles: { catcher: 'go-to', shortstop: 'capable' }, positionPreferences: {} },
    { id: "p2", name: "Finn", teamRoles: { pitcher: 'go-to', firstBase: 'capable' }, positionPreferences: {} },
    { id: "p3", name: "Avalon", teamRoles: { secondBase: 'capable', centerField: 'capable' }, positionPreferences: {} },
    { id: "p4", name: "Grace", teamRoles: { catcher: 'capable', rightField: 'capable' }, positionPreferences: {} },
    { id: "p5", name: "Bella", teamRoles: { thirdBase: 'capable', leftField: 'capable' }, positionPreferences: {} },
    { id: "p6", name: "Kenzie", teamRoles: { shortstop: 'capable', centerField: 'capable' }, positionPreferences: {} },
    { id: "p7", name: "Presley", teamRoles: { firstBase: 'capable', rightField: 'capable' }, positionPreferences: {} },
    { id: "p8", name: "Avery", teamRoles: { secondBase: 'capable', leftField: 'capable' }, positionPreferences: {} },
    { id: "p9", name: "Elle", teamRoles: { pitcher: 'capable', firstBase: 'capable' }, positionPreferences: {} },
    { id: "p10", name: "Vienna", teamRoles: { pitcher: 'capable', catcher: 'capable', firstBase: 'capable' }, positionPreferences: {} },
    { id: "p11", name: "Katelyn", teamRoles: { thirdBase: 'capable', leftField: 'capable' }, positionPreferences: {} },
    { id: "p12", name: "Morgan", teamRoles: { shortstop: 'capable', rightField: 'capable' }, positionPreferences: {} }
  ];
};

// Function to create a complete demo team with players and lineups
export const createDemoTeam = async (userId: string): Promise<Team> => {
  try {
    console.log("Creating demo team for user:", userId);

    // Check if demo team already exists for this user
    const existingTeams = await fetchTeams(userId);
    const existingDemoTeam = existingTeams.find(team => team.name === "Demo Softball Team");

    if (existingDemoTeam) {
      console.log("Demo team already exists for user, returning existing team:", existingDemoTeam.id);
      return existingDemoTeam;
    }

    // 1. Create the team with the hardcoded ID
    const hardcodedTeamId = '83bd9832-f5db-4c7d-b234-41fd38f90007';
    const team = await createTeam(userId, "Demo Softball Team", hardcodedTeamId);
    console.log("Created demo team:", team);

    // 2. Add default players
    const defaultPlayers = getDefaultPlayers();
    const createdPlayers: Player[] = [];

    for (const player of defaultPlayers) {
      try {
        const createdPlayer = await createPlayer(userId, team.id, player);
        createdPlayers.push(createdPlayer);
        console.log(`Added player ${player.name} to demo team`);
      } catch (error) {
        console.error(`Error adding player ${player.name} to demo team:`, error);
      }
    }

    // Update the team with the created players
    team.players = createdPlayers;

    // 3. Create sample lineups
    await createSampleLineups(userId, team);

    // 4. Fetch the complete team with lineups
    const teams = await fetchTeams(userId);
    const demoTeam = teams.find(t => t.id === team.id) || team;

    return demoTeam;
  } catch (error) {
    console.error("Error creating demo team:", error);
    throw error;
  }
};

// Function to initialize demo data directly
export const initializeDemoData = async (userId: string): Promise<void> => {
  try {
    console.log("Initializing demo data for user:", userId);

    // Add a lock mechanism to prevent concurrent initialization
    const lockKey = `demo_init_${userId}`;
    const existingLock = localStorage.getItem(lockKey);

    if (existingLock) {
      const lockTime = parseInt(existingLock);
      const now = Date.now();
      // If lock is less than 30 seconds old, skip initialization
      if (now - lockTime < 30000) {
        console.log("Demo initialization already in progress, skipping");
        return;
      }
    }

    // Set lock
    localStorage.setItem(lockKey, Date.now().toString());

    try {
      // Get the current authenticated user to check if it's a demo user
      const { data: { user } } = await supabase.auth.getUser();
      
      // For the persistent demo user, always check if the demo team exists
      const DEMO_TEAM_ID = '83bd9832-f5db-4c7d-b234-41fd38f90007';
      
      // Check if this is a demo user by email pattern or demo mode flag
      const isDemoUser = user?.email === '<EMAIL>' || 
                        localStorage.getItem('demo_mode') === 'true';

      if (isDemoUser) {
        console.log("This is a demo user - checking for existing demo team");
        console.log("Demo user email:", user?.email);
        console.log("Demo user ID from auth:", user?.id);

        // Check if the demo team exists
        const { data: demoTeam, error } = await supabase
          .from('teams')
          .select('*')
          .eq('id', DEMO_TEAM_ID)
          .single();

        if (error || !demoTeam) {
          console.log("Persistent demo team not found, this should not happen");
          throw new Error("Persistent demo team not found in database");
        }

        console.log("Persistent demo team found - no initialization needed");
        return;
      }

      // For other users, check if they already have a demo team
      const teams = await fetchTeams(userId);
      const existingDemoTeam = teams.find(team => 
        team.name === "Demo Softball Team" || 
        team.name === "Demo Wildcats" ||
        team.id === DEMO_TEAM_ID
      );

      if (existingDemoTeam) {
        console.log("Demo team already exists for user:", existingDemoTeam.id);
        return;
      }

      if (teams.length === 0) {
        // Create a demo team with all data
        console.log("No teams found, creating complete demo team");
        await createDemoTeam(userId);
      } else {
        console.log("User has existing teams, not creating demo team");
      }

      console.log("Demo data initialization complete");
    } finally {
      // Clear lock
      localStorage.removeItem(lockKey);
    }
  } catch (error) {
    console.error("Error initializing demo data:", error);
    // Clear lock on error too
    const lockKey = `demo_init_${userId}`;
    localStorage.removeItem(lockKey);
    throw error;
  }
};

// Helper function to create realistic innings for demo lineups
const createDemoInnings = (players: Player[], numInnings: number = 6): InningLineup[] => {
  const innings: InningLineup[] = [];
  const availablePlayers = players.filter(p => p.name.trim() !== '');

  if (availablePlayers.length < 9) {
    console.warn("Not enough players to create realistic demo innings");
    return [];
  }

  for (let i = 1; i <= numInnings; i++) {
    // Create a realistic rotation for each inning
    const inning: InningLineup = {
      inning: i,
      positions: {
        pitcher: availablePlayers[(i - 1) % availablePlayers.length]?.id || '',
        catcher: availablePlayers[(i + 1) % availablePlayers.length]?.id || '',
        firstBase: availablePlayers[(i + 2) % availablePlayers.length]?.id || '',
        secondBase: availablePlayers[(i + 3) % availablePlayers.length]?.id || '',
        thirdBase: availablePlayers[(i + 4) % availablePlayers.length]?.id || '',
        shortstop: availablePlayers[(i + 5) % availablePlayers.length]?.id || '',
        leftField: availablePlayers[(i + 6) % availablePlayers.length]?.id || '',
        centerField: availablePlayers[(i + 7) % availablePlayers.length]?.id || '',
        rightField: availablePlayers[(i + 8) % availablePlayers.length]?.id || '',
        bench: availablePlayers.slice(9).map(p => p.id) // Remaining players on bench
      }
    };
    innings.push(inning);
  }

  return innings;
};

export const createSampleLineups = async (userId: string, team: Team): Promise<void> => {
  try {
    if (!team.players || team.players.length === 0) {
      console.warn("Cannot create sample lineups: no players in team");
      return;
    }

    // Create attendance data for all players
    const attendance: {[playerId: string]: boolean} = {};
    team.players.forEach(player => {
      attendance[player.id] = true;
    });

    console.log("Creating sample lineups with team players:", team.players);

    // Create realistic innings for the demo lineups
    const demoInnings = createDemoInnings(team.players, 6);

    // Create sample lineups with complete innings
    const sampleLineups = [
      {
        name: "Forest Glade Tournament",
        gameDate: new Date().toISOString().split('T')[0], // Today's date
        attendance,
        innings: demoInnings,
        battingOrder: team.players.slice(0, Math.min(9, team.players.length)).map(p => p.id) // Use player IDs
      },
      {
        name: "Essex Game",
        gameDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Next week
        attendance,
        innings: createDemoInnings(team.players, 6), // Create different rotation
        battingOrder: team.players.slice(0, Math.min(9, team.players.length)).map(p => p.id) // Use player IDs
      },
      {
        name: "Sarnia Championship",
        gameDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Two weeks from now
        attendance,
        innings: createDemoInnings(team.players, 6), // Create different rotation
        battingOrder: team.players.slice(0, Math.min(9, team.players.length)).map(p => p.id) // Use player IDs
      }
    ];

    console.log(`Creating ${sampleLineups.length} sample lineups with complete innings`);

    // Create each lineup in the database
    const createdLineups: Lineup[] = [];

    for (const lineup of sampleLineups) {
      try {
        const lineupToCreate: Lineup = {
          id: "", // Will be assigned by the database
          name: lineup.name,
          gameDate: lineup.gameDate,
          createdDate: new Date().toISOString(),
          attendance: lineup.attendance,
          innings: lineup.innings,
          battingOrder: lineup.battingOrder
        };

        const createdLineup = await createLineup(userId, team.id, lineupToCreate);
        console.log(`Created sample lineup: ${lineup.name}`);

        // Add to our array of created lineups
        createdLineups.push(createdLineup);
      } catch (error) {
        console.error(`Error creating sample lineup ${lineup.name}:`, error);
      }
    }

    // All data is stored in Supabase database
  } catch (error) {
    console.error("Error creating sample lineups:", error);
    throw error;
  }
};

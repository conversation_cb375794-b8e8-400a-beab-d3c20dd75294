import { supabase } from "@/supabaseClient";
import { Lineup, InningLineup } from "@/contexts/TeamContext";

// Lightweight lineup info for list views
export interface LineupSummary {
  id: string;
  name: string;
  gameDate: string;
  createdDate: string;
  playerCount?: number;
  inningCount?: number;
  gameResult?: 'win' | 'loss' | null;
  seriesTitle?: string;
  gameNumber?: number;
}

// Fetch only basic lineup info (for lists/tables)
export const fetchLineupSummaries = async (teamId: string): Promise<LineupSummary[]> => {
  try {
    console.time('fetchLineupSummaries');
    
    const { data: lineups, error } = await supabase
      .from('lineups')
      .select(`
        id,
        name,
        game_date,
        created_at,
        game_result,
        series_title,
        game_number
      `)
      .eq('team_id', teamId)
      .order('game_date', { ascending: false });

    if (error) throw error;

    // Get counts in parallel
    const lineupIds = lineups?.map(l => l.id) || [];
    
    const [inningCounts, attendanceCounts] = await Promise.all([
      // Get inning counts
      supabase
        .from('lineup_innings')
        .select('lineup_id')
        .in('lineup_id', lineupIds),
      
      // Get attendance counts (players present)
      supabase
        .from('lineup_attendance')
        .select('lineup_id, is_present')
        .in('lineup_id', lineupIds)
        .eq('is_present', true)
    ]);

    // Create count maps
    const inningCountMap = new Map<string, number>();
    inningCounts.data?.forEach(inning => {
      const count = inningCountMap.get(inning.lineup_id) || 0;
      inningCountMap.set(inning.lineup_id, count + 1);
    });

    const playerCountMap = new Map<string, number>();
    attendanceCounts.data?.forEach(att => {
      const count = playerCountMap.get(att.lineup_id) || 0;
      playerCountMap.set(att.lineup_id, count + 1);
    });

    const summaries: LineupSummary[] = lineups?.map(lineup => ({
      id: lineup.id,
      name: lineup.name,
      gameDate: lineup.game_date,
      createdDate: lineup.created_at,
      playerCount: playerCountMap.get(lineup.id) || 0,
      inningCount: inningCountMap.get(lineup.id) || 0,
      gameResult: lineup.game_result,
      seriesTitle: lineup.series_title,
      gameNumber: lineup.game_number
    })) || [];

    console.timeEnd('fetchLineupSummaries');
    return summaries;
  } catch (error) {
    console.error('Error fetching lineup summaries:', error);
    throw error;
  }
};

// Fetch full lineup details (only when needed)
export const fetchLineupDetails = async (lineupId: string): Promise<Lineup | null> => {
  try {
    console.time(`fetchLineupDetails-${lineupId}`);
    
    // Fetch all lineup data in parallel
    const [
      lineupResult,
      inningsResult,
      attendanceResult,
      battingOrderResult
    ] = await Promise.all([
      // Basic lineup info
      supabase
        .from('lineups')
        .select('*')
        .eq('id', lineupId)
        .single(),
      
      // Innings
      supabase
        .from('lineup_innings')
        .select('*')
        .eq('lineup_id', lineupId)
        .order('inning_number', { ascending: true }),
      
      // Attendance
      supabase
        .from('lineup_attendance')
        .select('*')
        .eq('lineup_id', lineupId),
      
      // Batting order
      supabase
        .from('batting_orders')
        .select('*')
        .eq('lineup_id', lineupId)
        .maybeSingle()
    ]);

    if (lineupResult.error || !lineupResult.data) {
      console.error('Error fetching lineup:', lineupResult.error);
      return null;
    }

    // Process innings
    const innings: InningLineup[] = inningsResult.data?.map(inning => ({
      inning: inning.inning_number,
      positions: inning.positions as any
    })) || [];

    // Process attendance
    const attendance: {[playerId: string]: boolean} = {};
    attendanceResult.data?.forEach(record => {
      attendance[record.player_id] = record.is_present;
    });

    // Process batting order
    const battingOrder = battingOrderResult.data?.player_order || [];

    const lineup: Lineup = {
      id: lineupResult.data.id,
      name: lineupResult.data.name,
      gameDate: lineupResult.data.game_date,
      createdDate: lineupResult.data.created_at,
      team_id: lineupResult.data.team_id,
      attendance,
      innings,
      battingOrder,
      rotationSettings: lineupResult.data.rotation_settings || undefined,
      gameResult: lineupResult.data.game_result || null,
      seriesTitle: lineupResult.data.series_title || undefined,
      seriesId: lineupResult.data.series_id || undefined,
      gameNumber: lineupResult.data.game_number || undefined,
      totalGamesInSeries: lineupResult.data.total_games_in_series || undefined
    };

    console.timeEnd(`fetchLineupDetails-${lineupId}`);
    return lineup;
  } catch (error) {
    console.error('Error fetching lineup details:', error);
    throw error;
  }
};

// Prefetch lineup details (for hover/focus)
export const prefetchLineupDetails = async (lineupId: string): Promise<void> => {
  // Store in a simple cache
  const cacheKey = `lineup_details_${lineupId}`;
  const cached = sessionStorage.getItem(cacheKey);
  
  if (cached) {
    const parsedCache = JSON.parse(cached);
    const age = Date.now() - parsedCache.timestamp;
    // Cache for 5 minutes
    if (age < 5 * 60 * 1000) {
      return; // Already cached
    }
  }

  try {
    const details = await fetchLineupDetails(lineupId);
    if (details) {
      sessionStorage.setItem(cacheKey, JSON.stringify({
        data: details,
        timestamp: Date.now()
      }));
    }
  } catch (error) {
    console.warn('Prefetch failed:', error);
  }
};

// Get cached lineup details if available
export const getCachedLineupDetails = (lineupId: string): Lineup | null => {
  const cacheKey = `lineup_details_${lineupId}`;
  const cached = sessionStorage.getItem(cacheKey);
  
  if (cached) {
    const parsedCache = JSON.parse(cached);
    const age = Date.now() - parsedCache.timestamp;
    // Cache for 5 minutes
    if (age < 5 * 60 * 1000) {
      return parsedCache.data;
    }
  }
  
  return null;
};
import { User } from '@supabase/supabase-js';

// Mock user data
const MOCK_USER: User = {
  id: 'mock-user-id',
  app_metadata: {},
  user_metadata: {},
  aud: 'authenticated',
  created_at: new Date().toISOString(),
  email: '<EMAIL>',
  role: 'authenticated',
};

// Mock session data
const MOCK_SESSION = {
  access_token: 'mock-access-token',
  refresh_token: 'mock-refresh-token',
  expires_in: 3600,
  user: MOCK_USER,
};

// Store for mock authentication state
let isAuthenticated = false;
let currentUser: User | null = null;
let currentSession: typeof MOCK_SESSION | null = null;

// Event listeners for auth state changes
const listeners: Array<(event: string, session: any) => void> = [];

/**
 * Enable mock authentication mode
 * This bypasses real Supabase authentication for testing
 */
export const enableMockAuth = () => {
  localStorage.setItem('use_mock_auth', 'true');
  
  // Auto-login with mock user
  mockSignIn();
  
  console.log('🔑 Mock authentication enabled');
  return true;
};

/**
 * Check if mock authentication is enabled
 */
export const isMockAuthEnabled = (): boolean => {
  return localStorage.getItem('use_mock_auth') === 'true';
};

/**
 * Disable mock authentication mode
 */
export const disableMockAuth = () => {
  localStorage.removeItem('use_mock_auth');
  mockSignOut();
  console.log('🔑 Mock authentication disabled');
  return true;
};

/**
 * Sign in with mock credentials
 */
export const mockSignIn = () => {
  isAuthenticated = true;
  currentUser = { ...MOCK_USER };
  currentSession = { ...MOCK_SESSION };
  
  // Mark as paid for testing
  localStorage.setItem('mock_user_is_paid', 'true');
  
  // Notify listeners
  listeners.forEach(listener => listener('SIGNED_IN', currentSession));
  
  return {
    data: { user: currentUser, session: currentSession },
    error: null
  };
};

/**
 * Sign out from mock authentication
 */
export const mockSignOut = () => {
  isAuthenticated = false;
  currentUser = null;
  currentSession = null;
  
  // Remove paid status
  localStorage.removeItem('mock_user_is_paid');
  
  // Notify listeners
  listeners.forEach(listener => listener('SIGNED_OUT', null));
  
  return { error: null };
};

/**
 * Get current session for mock auth
 */
export const mockGetSession = () => {
  return {
    data: { session: isAuthenticated ? currentSession : null },
    error: null
  };
};

/**
 * Get current user for mock auth
 */
export const mockGetUser = () => {
  return {
    data: { user: currentUser },
    error: null
  };
};

/**
 * Add auth state change listener
 */
export const mockOnAuthStateChange = (callback: (event: string, session: any) => void) => {
  listeners.push(callback);
  
  // Return mock subscription
  return {
    data: {
      subscription: {
        unsubscribe: () => {
          const index = listeners.indexOf(callback);
          if (index > -1) {
            listeners.splice(index, 1);
          }
        }
      }
    }
  };
};

/**
 * Mock auth interface that mimics Supabase auth
 */
export const mockAuth = {
  signInWithPassword: () => mockSignIn(),
  signUp: () => mockSignIn(),
  signOut: () => mockSignOut(),
  getSession: () => mockGetSession(),
  getUser: () => mockGetUser(),
  onAuthStateChange: mockOnAuthStateChange
};

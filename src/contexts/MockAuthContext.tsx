import { createContext, useState, useEffect, useContext, ReactNode } from "react";
import { User } from "@supabase/supabase-js";
import { toast } from "sonner";

interface MockAuthContextProps {
  user: User | null;
  session: any;
  loading: boolean;
  signUp: (email: string, password: string) => Promise<any>;
  signIn: (email: string, password: string) => Promise<any>;
  signOut: () => Promise<void>;
  isPaid: boolean;
  checkPaymentStatus: () => Promise<boolean>;
  paymentInfo: any | null;
}

const MockAuthContext = createContext<MockAuthContextProps | undefined>(undefined);

export const useMockAuth = () => {
  const context = useContext(MockAuthContext);
  if (context === undefined) {
    throw new Error("useMockAuth must be used within a MockAuthProvider");
  }
  return context;
};

interface MockAuthProviderProps {
  children: ReactNode;
}

// Mock user data
const mockUsers = [
  {
    email: "<EMAIL>",
    password: "demo1234",
    id: "mock-user-1",
    isPaid: true
  },
  {
    email: "<EMAIL>",
    password: "password123",
    id: "mock-user-2",
    isPaid: false
  }
];

export const MockAuthProvider = ({ children }: MockAuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [isPaid, setIsPaid] = useState(false);
  const [paymentInfo, setPaymentInfo] = useState<any>(null);

  useEffect(() => {
    // Check if there's a stored user in localStorage
    const storedUser = localStorage.getItem("mockUser");
    if (storedUser) {
      const parsedUser = JSON.parse(storedUser);
      setUser(parsedUser as User);
      setSession({ user: parsedUser });

      // Check if the user is paid
      const mockUser = mockUsers.find(u => u.email === parsedUser.email);
      if (mockUser) {
        setIsPaid(mockUser.isPaid);
      }
    }

    setLoading(false);
  }, []);

  const signUp = async (email: string, password: string) => {
    // Check if user already exists
    const existingUser = mockUsers.find(u => u.email === email);
    if (existingUser) {
      return { error: { message: "User already exists" } };
    }

    // Create a new mock user
    const newUser = {
      id: `mock-user-${Date.now()}`,
      email,
      isPaid: false
    };

    // Store the user in localStorage
    localStorage.setItem("mockUser", JSON.stringify(newUser));

    // Update state
    setUser(newUser as User);
    setSession({ user: newUser });
    setIsPaid(false);

    toast.success("Account created successfully!");
    return { data: { user: newUser }, error: null };
  };

  const signIn = async (email: string, password: string) => {
    // Find the user in our mock database
    const mockUser = mockUsers.find(u => u.email === email && u.password === password);

    if (!mockUser) {
      return { error: { message: "Invalid login credentials" } };
    }

    // Create a user object that matches Supabase's User type
    const user = {
      id: mockUser.id,
      email: mockUser.email,
    };

    // Store the user in localStorage
    localStorage.setItem("mockUser", JSON.stringify(user));

    // Update state
    setUser(user as User);
    setSession({ user });
    setIsPaid(mockUser.isPaid);

    if (mockUser.isPaid) {
      setPaymentInfo({
        id: "mock-subscription-1",
        created_at: new Date().toISOString(),
        amount: 4900,
        currency: "usd"
      });
    }

    return { data: { user }, error: null };
  };

  const signOut = async () => {
    // Clear the stored user
    localStorage.removeItem("mockUser");

    // Clear any demo-specific localStorage items
    localStorage.removeItem('demo_user_is_paid');
    localStorage.removeItem('original_user_email');

    // Update state
    setUser(null);
    setSession(null);
    setIsPaid(false);
    setPaymentInfo(null);

    return;
  };

  const checkPaymentStatus = async (): Promise<boolean> => {
    if (!user) {
      return false;
    }

    // Find the user in our mock database
    const mockUser = mockUsers.find(u => u.email === user.email);
    if (mockUser) {
      setIsPaid(mockUser.isPaid);

      if (mockUser.isPaid) {
        setPaymentInfo({
          id: "mock-subscription-1",
          created_at: new Date().toISOString(),
          amount: 4900,
          currency: "usd"
        });
      }

      return mockUser.isPaid;
    }

    return false;
  };

  return (
    <MockAuthContext.Provider
      value={{
        user,
        session,
        loading,
        signUp,
        signIn,
        signOut,
        isPaid,
        checkPaymentStatus,
        paymentInfo
      }}
    >
      {children}
    </MockAuthContext.Provider>
  );
};

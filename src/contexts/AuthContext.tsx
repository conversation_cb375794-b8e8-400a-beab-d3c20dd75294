
import { createContext, useState, useEffect, useContext, ReactNode, useCallback, useRef } from "react";
import { User, SupabaseClient } from "@supabase/supabase-js";
import { supabase } from "@/supabaseClient";
import {
  setAdminAccountFlags,
  setDemoAccountFlags,
  clearAllAuthFlags,
  getAccountType
} from "@/utils/authUtils";

interface AuthContextProps {
  user: User | null;
  session: any; // Type from Supabase
  supabase: SupabaseClient;
  loading: boolean;
  signUp: (email: string, password: string) => Promise<any>;
  signIn: (email: string, password: string) => Promise<any>;
  signOut: () => Promise<void>;
  isPaid: boolean;
  checkPaymentStatus: () => Promise<boolean>;
  paymentInfo: any | null;
  subscriptionTier: 'free' | 'starter' | 'coach' | 'club';
  teamLimit: number;
  teamsUsed: number;
}

const AuthContext = createContext<AuthContextProps | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

// Define stable payment info objects for demo and admin users
const DEMO_PAYMENT_INFO = {
  id: "demo-subscription",
  created_at: "2024-01-01T00:00:00.000Z", // Static timestamp
  amount: 4900,
  currency: "usd",
  is_demo: true
};

const ADMIN_PAYMENT_INFO = {
  id: "admin-subscription",
  created_at: "2024-01-01T00:00:00.000Z", // Static timestamp
  amount: 4900,
  currency: "usd",
  is_demo: false // Admin is not a demo for payment purposes
};

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [isPaid, setIsPaid] = useState(false);
  const [paymentInfo, setPaymentInfo] = useState<any>(null);
  const [subscriptionTier, setSubscriptionTier] = useState<'free' | 'starter' | 'coach' | 'club'>('free');
  const [teamLimit, setTeamLimit] = useState(0);
  const [teamsUsed, setTeamsUsed] = useState(0);
  
  // Add a simple cache for payment status to prevent repeated slow queries
  const paymentCacheRef = useRef<{ userId: string | null; timestamp: number; isPaid: boolean } | null>(null);
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  useEffect(() => {
    console.log("AuthContext: Setting up auth state listener");
    let isActive = true; // Flag to prevent state updates after unmount
    
    // Failsafe: Set loading to false after maximum wait time
    const loadingTimeout = setTimeout(() => {
      if (loading && isActive) {
        console.warn("AuthContext: Loading timeout reached, forcing loading to false");
        setLoading(false);
      }
    }, 10000); // 10 second maximum wait

    // Set up the auth state listener
    const { data } = supabase.auth.onAuthStateChange(
      async (event, currentSession) => {
        console.log("AuthContext: Auth state changed:", event);

        if (!isActive) return;

        if (currentSession?.user) {
          console.log("AuthContext: User authenticated:", currentSession.user.email);
          
          // CRITICAL: Clear payment cache when user changes to prevent session confusion
          if (user && user.id !== currentSession.user.id) {
            console.log("AuthContext: User changed, clearing payment cache");
            paymentCacheRef.current = null;
          }

          const accountType = getAccountType(currentSession.user.email);

          switch (accountType) {
            case 'demo':
              console.log("AuthContext: Demo user detected, setting demo mode flag");
              setDemoAccountFlags();
              setIsPaid(true);
              break;
            case 'admin':
              console.log("AuthContext: Admin account detected, setting up admin access");
              setAdminAccountFlags();
              setIsPaid(true);
              // Skip database checks for admin - just set user and continue
              setSession(currentSession);
              setUser(currentSession.user);
              setLoading(false);
              return; // Exit early for admin users
            default:
              console.log("AuthContext: Regular user detected, clearing auth flags");
              clearAllAuthFlags();
              break;
          }

          setSession(currentSession);
          setUser(currentSession.user);

          // Run profile and payment checks sequentially to avoid race conditions
          try {
            // First ensure profile exists
            await ensureUserProfile(currentSession.user);
            
            // Then check payment status with a shorter timeout for initial load
            // Skip payment check if we're in the signup flow
            const isSignupFlow = window.location.pathname.includes('/signup');
            if (!isSignupFlow) {
              // Don't wait for payment check to complete - do it in background
              checkPaymentStatus().catch(error => {
                console.warn('AuthContext: Background payment check failed:', error);
              });
            }
          } catch (error) {
            console.error("AuthContext: Error in auth checks:", error);
            // Don't block auth flow for these errors
          }
        } else {
          console.log("AuthContext: No authenticated user");
          // CRITICAL: Clear all state and cache when no user
          paymentCacheRef.current = null;
          setSession(null);
          setUser(null);
          setIsPaid(false);
          setPaymentInfo(null);
          setSubscriptionTier('free');
          setTeamLimit(0);
          setTeamsUsed(0);
        }

        setLoading(false);
      }
    );

    const subscription = data.subscription;

    // Get the initial session only once
    const initializeAuth = async () => {
      try {
        console.log("AuthContext: Getting initial session");
        
        // Add timeout to getSession to prevent hanging
        const getSessionPromise = supabase.auth.getSession();
        const sessionTimeout = new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Session check timeout')), 8000)
        );
        
        let initialSession = null;
        
        try {
          const { data: { session } } = await Promise.race([
            getSessionPromise,
            sessionTimeout
          ]) as any;
          initialSession = session;
        } catch (sessionError) {
          // Handle timeout gracefully
          if (sessionError.message === 'Session check timeout') {
            console.warn("AuthContext: Session check timed out, attempting direct auth check");
            
            // Try to get the current user directly as a fallback
            try {
              const { data: { user: currentUser } } = await supabase.auth.getUser();
              if (currentUser) {
                console.log("AuthContext: Found user via direct check:", currentUser.email);
                // Manually trigger auth state change
                const mockSession = { user: currentUser, access_token: '', token_type: 'bearer' };
                setSession(mockSession);
                setUser(currentUser);
                
                // Handle special account types
                const accountType = getAccountType(currentUser.email);
                if (accountType === 'demo') {
                  setDemoAccountFlags();
                  setIsPaid(true);
                } else if (accountType === 'admin') {
                  setAdminAccountFlags();
                  setIsPaid(true);
                }
                
                // Skip waiting for payment check on initial load
                setLoading(false);
                return;
              }
            } catch (fallbackError) {
              console.error("AuthContext: Fallback auth check failed:", fallbackError);
            }
          }
        }
        
        if (!isActive) return;

        if (initialSession?.user) {
          console.log("AuthContext: Initial session found for user:", initialSession.user.email);
          // The auth state listener will handle setting up the user state
        } else {
          console.log("AuthContext: No initial session found");

          // Check if we're in demo mode
          const isDemoMode = localStorage.getItem('demo_mode') === 'true';
          if (isDemoMode) {
            console.log("AuthContext: Demo mode detected, setting up demo user");

            const demoUserId = localStorage.getItem('demo_user_id') || '212e5e06-9dd0-4d56-89d2-69915b205b53';
            const demoEmail = localStorage.getItem('demo_user_email') || '<EMAIL>';

            // Create a mock user for demo mode
            setUser({
              id: demoUserId,
              email: demoEmail,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              email_confirmed_at: new Date().toISOString(),
              last_sign_in_at: new Date().toISOString(),
              app_metadata: {},
              user_metadata: {},
              aud: 'authenticated',
              role: 'authenticated'
            });

            // Manually set up demo state as no session exists but demo_mode is on
            console.log("AuthContext: Demo mode (no initial session), setting up demo user state manually");
            setDemoAccountFlags();
            setIsPaid(true);
            setPaymentInfo(DEMO_PAYMENT_INFO);
          }
        }
      } catch (error) {
        console.error("AuthContext: Error initializing auth:", error);
        // Even on error, we should stop loading
        if (error.message === 'Session check timeout') {
          console.warn("AuthContext: Session check timed out, proceeding without session");
        }
      }

      // Always set loading to false after initialization attempt
      if (isActive) {
        setLoading(false);
      }
    };

    initializeAuth();

    // Set up session refresh interval to prevent auth timeouts
    const refreshInterval = setInterval(async () => {
      if (session) {
        console.log("AuthContext: Refreshing session to prevent timeout");
        try {
          const { error } = await supabase.auth.refreshSession();
          if (error) {
            console.error("AuthContext: Failed to refresh session:", error);
            // If refresh fails, the auth state listener will handle it
          } else {
            console.log("AuthContext: Session refreshed successfully");
          }
        } catch (error) {
          console.error("AuthContext: Error refreshing session:", error);
        }
      }
    }, 10 * 60 * 1000); // Refresh every 10 minutes

    return () => {
      isActive = false;
      clearTimeout(loadingTimeout);
      subscription.unsubscribe();
      clearInterval(refreshInterval);
    };
  }, []); // Empty dependency array to run only once

  const signUp = async (email: string, password: string) => {
    try {
      // First attempt normal signup
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        }
      });

      if (error) {
        console.error("Signup error:", error.message);

        // If user already exists but email not confirmed, try to auto-confirm for test users
        if (error.message.includes('already registered') && (import.meta.env.DEV || email.includes('test') || email.includes('demo'))) {
          console.warn("Test user detected. Attempting to bypass email confirmation...");

          // Try to sign in directly - this might work if confirmation is disabled on Supabase
          const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
            email,
            password
          });

          if (!signInError) {
            console.log("Successfully signed in test user without confirmation");
            return { data: signInData, error: null };
          }

          console.error("Could not auto-sign in test user:", signInError);
          throw error;
        }

        throw error;
      }

      return { data, error: null };
    } catch (error) {
      console.error("Error in signUp:", error);
      throw error;
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      // CRITICAL: Clear any existing cached payment data to prevent session confusion
      paymentCacheRef.current = null;
      
      // CRITICAL: Clear ALL localStorage to prevent cross-user data contamination
      const itemsToPreserve = ['supabase.auth.token']; // Preserve auth token temporarily
      const preservedItems: Record<string, string> = {};
      
      // Preserve specific items
      itemsToPreserve.forEach(key => {
        const value = localStorage.getItem(key);
        if (value) preservedItems[key] = value;
      });
      
      // Clear everything
      localStorage.clear();
      
      // Restore preserved items
      Object.entries(preservedItems).forEach(([key, value]) => {
        localStorage.setItem(key, value);
      });
      
      // Clear any existing state before signing in a new user
      setUser(null);
      setSession(null);
      setIsPaid(false);
      setPaymentInfo(null);
      setSubscriptionTier('free');
      setTeamLimit(0);
      setTeamsUsed(0);
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        console.error("Error signing in:", error.message);

        // Special handling for unconfirmed emails for test users or in development
        const isTestUser = import.meta.env.DEV || email.includes('test') || email.includes('demo');
        if (error.message.includes('Email not confirmed') && isTestUser) {
          console.warn("Test user detected. Bypassing email confirmation requirement");

          // Try to sign up again which might help in some cases
          const { error: signUpError } = await supabase.auth.signUp({
            email,
            password,
            options: {
              emailRedirectTo: `${window.location.origin}/auth/callback`,
            }
          });

          if (signUpError && !signUpError.message.includes('already registered')) {
            console.error("Error in signup attempt:", signUpError);
            throw error; // Throw the original error
          }

          // For test users, try a workaround by creating a temporary user
          if (isTestUser) {
            const tempEmail = `temp_${Date.now()}@baseballguru.com`;
            console.log("Creating temporary test user:", tempEmail);

            const { error: tempError } = await supabase.auth.signUp({
              email: tempEmail,
              password,
              options: {
                data: { is_test: true }
              }
            });

            if (!tempError) {
              console.log("Successfully created temporary test user");

              // Try to auto-sign in with the temp user
              const { data: tempSignInData, error: tempSignInError } = await supabase.auth.signInWithPassword({
                email: tempEmail,
                password
              });

              if (!tempSignInError) {
                console.log("Successfully signed in with temporary test user");
                // Store original email to display in UI
                localStorage.setItem('original_user_email', email);
                return tempSignInData;
              }
            }
          }

          // If we get here, try signing in again with original credentials
          const { data: retryData, error: retryError } = await supabase.auth.signInWithPassword({
            email,
            password,
          });

          if (retryError) {
            console.error("Error in retry sign in:", retryError);
            throw error; // Throw the original error
          }

          return retryData;
        }

        throw error;
      }

      return data;
    } catch (error) {
      console.error("Error in signIn:", error);
      throw error;
    }
  };

  const signOut = async () => {
    console.log("AuthContext: Signing out user");

    // Check if we're in demo mode
    const isDemoMode = localStorage.getItem('demo_mode') === 'true';

    // CRITICAL: Clear payment cache to prevent session confusion
    paymentCacheRef.current = null;

    // Sign out from Supabase
    await supabase.auth.signOut();

    // Clear all auth-related state immediately
    setUser(null);
    setSession(null);
    setIsPaid(false);
    setPaymentInfo(null);
    setSubscriptionTier('free');
    setTeamLimit(0);
    setTeamsUsed(0);
    
    // CRITICAL: Clear ALL localStorage to prevent cross-user data contamination
    localStorage.clear();

    // Clear any demo-specific localStorage items
    localStorage.removeItem('demo_user_is_paid');
    localStorage.removeItem('original_user_email');

    // If we were in demo mode, keep the flag but clear other demo data
    if (isDemoMode) {
      console.log("AuthContext: Preserving demo mode flag but clearing demo data");
      localStorage.setItem('demo_mode', 'true');
      localStorage.removeItem('demo_team');
      localStorage.removeItem('demo_lineups');
      localStorage.removeItem('demo_player_ids');
      localStorage.removeItem('demo_data_initialized');
      localStorage.removeItem('current_team_id');
    } else {
      console.log("AuthContext: Clearing all demo mode flags");
      localStorage.removeItem('demo_mode');
    }
  };

  const checkPaymentStatus = useCallback(async (): Promise<boolean> => {
    try {
      if (!user) {
        setIsPaid(false);
        setPaymentInfo(null);
        setSubscriptionTier('free');
        setTeamLimit(0);
        setTeamsUsed(0);
        paymentCacheRef.current = null;
        return false;
      }

      // Check cache first
      if (paymentCacheRef.current &&
          paymentCacheRef.current.userId === user.id &&
          Date.now() - paymentCacheRef.current.timestamp < CACHE_DURATION) {
        console.log("AuthContext: Using cached payment status");
        return paymentCacheRef.current.isPaid;
      }

      // CRITICAL FIX: Improved admin and demo user detection
      const isDemoUser = user.email === '<EMAIL>' || user.email === '<EMAIL>';
      const isAdminUser = user.email === '<EMAIL>' || user.email === '<EMAIL>';

      console.log(`AuthContext: Checking payment status for user ${user.id} (${user.email})`, {
        isDemoUser,
        isAdminUser
      });

      // CRITICAL FIX: Handle demo/admin users first with proper setup
      if (isDemoUser || isAdminUser) {
        console.log("AuthContext: Demo or admin user detected - setting up privileged access");

        setIsPaid(true);
        setSubscriptionTier('club');
        setTeamLimit(999);

        const targetPaymentInfo = isDemoUser ? DEMO_PAYMENT_INFO : ADMIN_PAYMENT_INFO;
        setPaymentInfo(targetPaymentInfo);

        // Fetch actual team count
        try {
          const { data: teams } = await supabase
            .from('teams')
            .select('id')
            .eq('user_id', user.id);
          if (teams) {
            setTeamsUsed(teams.length);
          }
        } catch (teamError) {
          console.warn('AuthContext: Could not fetch team count for admin/demo user:', teamError);
          setTeamsUsed(0);
        }

        // Update cache
        paymentCacheRef.current = { userId: user.id, timestamp: Date.now(), isPaid: true };
        return true;
      }

      // CRITICAL FIX: For regular users, check payment status with improved error handling
      try {
        console.log(`AuthContext: Checking payment status for regular user ${user.id} (${user.email})`);
        const checkStartTime = Date.now();

        // CRITICAL FIX: Simplified payment check without complex timeout logic
        // First, try a simple direct query to subscriptions table
        const { data: subscriptionData, error: subscriptionError } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', user.id)
          .maybeSingle(); // Use maybeSingle to handle no results gracefully

        const checkDuration = Date.now() - checkStartTime;
        console.log(`AuthContext: Payment check completed in ${checkDuration}ms`);

        // CRITICAL FIX: Handle subscription query results properly
        if (subscriptionError) {
          console.error("AuthContext: Error checking subscription:", subscriptionError);

          // Don't immediately fail - try to maintain current status if possible
          if (isPaid) {
            console.warn("AuthContext: Subscription check failed but user was previously paid - maintaining status");
            return isPaid;
          }

          setIsPaid(false);
          setPaymentInfo(null);
          setSubscriptionTier('free');
          setTeamLimit(0);
          return false;
        }

        // CRITICAL FIX: Check if user has a subscription and if it's paid
        if (subscriptionData) {
          console.log("AuthContext: Found subscription record:", {
            id: subscriptionData.id,
            is_paid: subscriptionData.is_paid,
            tier: subscriptionData.tier,
            team_limit: subscriptionData.team_limit
          });

          if (subscriptionData.is_paid) {
            // User has a paid subscription
            setIsPaid(true);
            setPaymentInfo(subscriptionData);
            setSubscriptionTier(subscriptionData.tier || 'club');
            setTeamLimit(subscriptionData.team_limit || 999);

            // Fetch team count
            try {
              const { data: teams } = await supabase
                .from('teams')
                .select('id')
                .eq('user_id', user.id);
              setTeamsUsed(teams?.length || 0);
            } catch (teamError) {
              console.warn('AuthContext: Could not fetch team count:', teamError);
              setTeamsUsed(0);
            }

            // Update cache
            paymentCacheRef.current = { userId: user.id, timestamp: Date.now(), isPaid: true };
            return true;
          } else {
            // User has subscription but it's not paid
            console.log("AuthContext: User has subscription but is_paid is false");
            setIsPaid(false);
            setPaymentInfo(null);
            setSubscriptionTier('free');
            setTeamLimit(0);
            setTeamsUsed(0);

            // Update cache
            paymentCacheRef.current = { userId: user.id, timestamp: Date.now(), isPaid: false };
            return false;
          }
        } else {
          console.log("AuthContext: No paid subscriptions found for user");
          setIsPaid(false);
          setPaymentInfo(null);
          setSubscriptionTier('free');
          setTeamLimit(0);
          setTeamsUsed(0);
          
          // For users who might have made guest payments, try to find and link any orphaned payments
          if (user.email) {
            console.log(`AuthContext: Attempting to find orphaned payments for email: ${user.email}`);
            
            try {
              // Call an edge function to check for orphaned payments with timeout
              const edgeFunctionPromise = supabase.functions.invoke('verify-payment', {
                body: { user_id: user.id, email: user.email }
              });
              
              const edgeFunctionTimeout = new Promise((resolve) => 
                setTimeout(() => resolve({ data: null, error: 'Edge function timeout' }), 5000)
              );
              
              const { data: orphanedCheck } = await Promise.race([
                edgeFunctionPromise,
                edgeFunctionTimeout
              ]) as any;
              
              if (orphanedCheck?.found_payment) {
                console.log("AuthContext: Found and linked orphaned payment, rechecking...");
                // Recheck payment status after potential linking
                const { data: recheckData } = await supabase
                  .from('subscriptions')
                  .select('*')
                  .eq('user_id', user.id)
                  .eq('is_paid', true);
                
                if (recheckData && recheckData.length > 0) {
                  setIsPaid(true);
                  setPaymentInfo(recheckData[0]);
                  return true;
                }
              }
            } catch (linkError) {
              console.log("AuthContext: Could not check for orphaned payments:", linkError);
              // Don't fail the payment check due to edge function errors
            }
          }
          
          setIsPaid(false);
          setPaymentInfo(null);
          // Update cache for unpaid status
          paymentCacheRef.current = { userId: user.id, timestamp: Date.now(), isPaid: false };
          return false;
        }
      } catch (paymentError) {
        console.error("Error checking payment status:", paymentError);
        setIsPaid(false);
        setPaymentInfo(null);
        // Don't update cache on error - allow retry
        return false;
      }
    } catch (error) {
      console.error("Error in checkPaymentStatus:", error);
      return false;
    }
  }, [user, isPaid, paymentInfo, CACHE_DURATION]);

  // Function to ensure user has a profile record - simplified to use upsert
  const ensureUserProfile = async (user: any) => {
    if (!user) return;

    try {
      console.log("AuthContext: Ensuring profile for user:", user.email);
      
      const isAdmin = user.email === '<EMAIL>';
      
      // Add a timeout wrapper to prevent hanging
      const profilePromise = supabase
        .from('profiles')
        .upsert({
          id: user.id,
          email: user.email,
          full_name: user.email,
          role: isAdmin ? 'admin' : 'user',
          is_admin: isAdmin,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'id'
        });
        
      const profileTimeout = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Profile creation timeout')), 5000)
      );
      
      try {
        const { error: profileError } = await Promise.race([
          profilePromise,
          profileTimeout
        ]) as any;
        
        if (profileError) {
          console.error("AuthContext: Error upserting profile:", profileError);
        } else {
          console.log("AuthContext: Profile ensured for user");
        }
      } catch (timeoutError) {
        console.warn("AuthContext: Profile creation timed out, continuing anyway");
      }

      // Also ensure they have a subscription record with timeout
      const subPromise = supabase
        .from('subscriptions')
        .upsert({
          user_id: user.id,
          is_paid: false, // Default to unpaid
          tier: 'starter',
          team_limit: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id',
          ignoreDuplicates: true // Don't update if already exists
        });
        
      const subTimeout = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Subscription creation timeout')), 5000)
      );
      
      try {
        const { error: subError } = await Promise.race([
          subPromise,
          subTimeout
        ]) as any;
        
        if (subError) {
          console.error("AuthContext: Error upserting subscription:", subError);
        } else {
          console.log("AuthContext: Subscription ensured for user");
        }
      } catch (timeoutError) {
        console.warn("AuthContext: Subscription creation timed out, continuing anyway");
      }
      
    } catch (error) {
      console.error("AuthContext: Error in ensureUserProfile:", error);
      // Don't let profile errors block authentication
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        session,
        supabase,
        loading,
        signUp,
        signIn,
        signOut,
        isPaid,
        checkPaymentStatus,
        paymentInfo,
        subscriptionTier,
        teamLimit,
        teamsUsed
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

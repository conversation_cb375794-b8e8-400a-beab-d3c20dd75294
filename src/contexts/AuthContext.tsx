import { createContext, useState, useEffect, useContext, ReactNode, useCallback, useRef } from "react";
import { User, SupabaseClient, Session } from "@supabase/supabase-js";
import { supabase } from "@/integrations/supabase/client";
import {
  setAdminAccountFlags,
  setDemoAccountFlags,
  clearAllAuthFlags,
  getAccountType
} from "@/utils/authUtils";

interface AuthContextProps {
  user: User | null;
  session: Session | null;
  supabase: SupabaseClient;
  loading: boolean;
  signUp: (email: string, password: string) => Promise<any>;
  signIn: (email: string, password: string) => Promise<any>;
  signOut: () => Promise<void>;
  isPaid: boolean;
  checkPaymentStatus: () => Promise<boolean>;
  paymentInfo: any | null;
  subscriptionTier: 'free' | 'starter' | 'coach' | 'club';
  teamLimit: number;
  teamsUsed: number;
  isInitialized: boolean;
}

const AuthContext = createContext<AuthContextProps | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

// Define stable payment info objects for demo and admin users
const DEMO_PAYMENT_INFO = {
  id: "demo-subscription",
  created_at: "2024-01-01T00:00:00.000Z", // Static timestamp
  amount: 4900,
  currency: "usd",
  is_demo: true
};

const ADMIN_PAYMENT_INFO = {
  id: "admin-subscription",
  created_at: "2024-01-01T00:00:00.000Z", // Static timestamp
  amount: 4900,
  currency: "usd",
  is_demo: false // Admin is not a demo for payment purposes
};

export const AuthProvider = ({ children }: AuthProviderProps) => {
  console.log("🔐 AuthContext: Initializing AuthProvider");
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isPaid, setIsPaid] = useState(false);
  const [paymentInfo, setPaymentInfo] = useState<any>(null);
  const [subscriptionTier, setSubscriptionTier] = useState<'free' | 'starter' | 'coach' | 'club'>('free');
  const [teamLimit, setTeamLimit] = useState(0);
  const [teamsUsed, setTeamsUsed] = useState(0);
  
  // Add a simple cache for payment status to prevent repeated slow queries
  const paymentCacheRef = useRef<{ userId: string | null; timestamp: number; isPaid: boolean } | null>(null);
  // Use longer cache duration to prevent unnecessary redirects during temporary issues
  const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours for all users
  
  // Persistent payment status storage key
  const PAYMENT_STATUS_KEY = 'dugout_boss_payment_status';

  // Helper function to clear only app-specific localStorage keys
  const clearAppLocalStorage = () => {
    console.log("🧹 Clearing app-specific localStorage (preserving Supabase tokens and user preferences)");
    
    // List of patterns to preserve
    const preservePatterns = [
      'supabase.auth.',  // Main Supabase session & refresh tokens
      'sb-',             // Alternative Supabase prefix
      'supabase-auth-'   // Another possible Supabase pattern
    ];
    
    // List of specific keys to preserve (user preferences)
    const preserveKeys = [
      'default_team_id',  // User's default team selection
      'current_team_id'   // Currently selected team
    ];
    
    // Get all keys
    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        // Check if this key should be preserved
        const shouldPreserve = 
          preservePatterns.some(pattern => key.startsWith(pattern)) ||
          preserveKeys.includes(key);
          
        if (!shouldPreserve) {
          keysToRemove.push(key);
        }
      }
    }
    
    // Remove only non-preserved keys
    keysToRemove.forEach(key => {
      console.log(`  Removing: ${key}`);
      localStorage.removeItem(key);
    });
    
    // Clear specific sessionStorage items (but not all)
    sessionStorage.removeItem('auth_session_initialized');
    
    console.log(`✅ Cleared ${keysToRemove.length} app keys, preserved Supabase tokens and user preferences`);
  };

  // Initialize auth state
  useEffect(() => {
    console.log("🔐 AuthContext: Initializing auth state in useEffect");
    console.log("🔐 AuthContext: Current supabase client:", supabase);
    console.log("🔐 AuthContext: Supabase URL:", supabase.supabaseUrl);
    let mounted = true;

    let isActive = true;
    let loadingTimeout: NodeJS.Timeout;

    const { data } = supabase.auth.onAuthStateChange(
      async (event, currentSession) => {
        if (!isActive) return;

        if (currentSession && currentSession.user) {
          console.log("AuthContext: Auth state changed - user is signed in", currentSession.user.email);

          // Check if this is actually a different user or just a session refresh
          const previousUserId = sessionStorage.getItem('last_known_user_id');
          const isActuallyDifferentUser = user && user.id !== currentSession.user.id && 
                                         currentSession.user.id !== previousUserId;
          
          if (!user || isActuallyDifferentUser) {
            console.log("AuthContext: User changed or new login", {
              hadUser: !!user,
              oldId: user?.id,
              newId: currentSession.user.id,
              previousUserId,
              isActuallyDifferent: isActuallyDifferentUser
            });
            
            // Only clear payment state if it's actually a different user
            if (isActuallyDifferentUser || !previousUserId) {
              console.log("AuthContext: Different user detected, clearing ALL cached state");
              paymentCacheRef.current = null;
              setIsPaid(false);
              setPaymentInfo(null);
              setSubscriptionTier('free');
              setTeamLimit(0);
              setTeamsUsed(0);

              const keysToRemove: string[] = [];
              for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.includes('team') || key.includes('lineup') || key.includes('player') || 
                           key === 'demo_mode' || key === 'auth_bypass_payment')) {
                  // Skip keys we want to preserve
                  if (key !== 'default_team_id' && key !== 'current_team_id') {
                    keysToRemove.push(key);
                  }
                }
              }
              keysToRemove.forEach(key => localStorage.removeItem(key));

              if ((window as any).clearTeamContext) {
                console.log("AuthContext: Calling clearTeamContext");
                (window as any).clearTeamContext();
              }
            } else {
              console.log("AuthContext: Same user returning, preserving payment state");
              // Check if we have cached payment info for this user
              if (paymentCacheRef.current && paymentCacheRef.current.userId === currentSession.user.id) {
                console.log("AuthContext: Restoring cached payment status:", paymentCacheRef.current.isPaid);
                setIsPaid(paymentCacheRef.current.isPaid);
              } else {
                // Try to restore from localStorage
                try {
                  const storedStatus = localStorage.getItem(PAYMENT_STATUS_KEY);
                  if (storedStatus) {
                    const parsed = JSON.parse(storedStatus);
                    if (parsed.userId === currentSession.user.id && 
                        Date.now() - parsed.timestamp < CACHE_DURATION) {
                      console.log("AuthContext: Restoring payment status from localStorage:", parsed.isPaid);
                      setIsPaid(parsed.isPaid);
                      setPaymentInfo(parsed.paymentInfo);
                      setSubscriptionTier(parsed.tier || 'free');
                      setTeamLimit(parsed.teamLimit || 0);
                      paymentCacheRef.current = {
                        userId: parsed.userId,
                        timestamp: parsed.timestamp,
                        isPaid: parsed.isPaid
                      };
                    }
                  }
                } catch (e) {
                  console.warn("Error restoring payment status:", e);
                }
              }
            }
          }

          const accountType = getAccountType(currentSession.user.email);

          switch (accountType) {
            case 'demo':
              console.log("AuthContext: Demo user detected, setting demo mode flag");
              setDemoAccountFlags();
              setIsPaid(true);
              break;
            case 'admin':
              console.log("AuthContext: Admin account detected, setting up admin access");
              setAdminAccountFlags();
              setIsPaid(true);
              setSession(currentSession);
              setUser(currentSession.user);
              console.log("🔐 DEBUG: Admin user currentSession =", currentSession);
              console.log("🔐 DEBUG: Admin user access_token =", currentSession.access_token);
              console.log("🔐 DEBUG: Admin user expires_at =", currentSession.expires_at);
              console.log("🔐 DEBUG: Supabase client auth headers:", supabase.auth.getSession());
              setLoading(false);
              return;
            default:
              console.log("AuthContext: Regular user detected");
              // Don't clear auth flags for regular users - their session is valid!
              break;
          }

          setSession(currentSession);
          setUser(currentSession.user);
          console.log("🔐 DEBUG: Regular user currentSession =", currentSession);
          console.log("🔐 DEBUG: Regular user access_token =", currentSession.access_token);
          console.log("🔐 DEBUG: Regular user expires_at =", currentSession.expires_at);
          console.log("🔐 DEBUG: Session set in state, verifying supabase client...");
          
          // Verify the session is properly set in supabase client
          supabase.auth.getSession().then(({ data, error }) => {
            console.log("🔐 DEBUG: Supabase client session check:", { 
              hasSession: !!data?.session,
              sessionUser: data?.session?.user?.email,
              error 
            });
          });

          try {
            const isPageRefresh = sessionStorage.getItem('auth_session_initialized') === 'true';
            
            if (!isPageRefresh) {
              sessionStorage.setItem('auth_session_initialized', 'true');
              console.log('AuthContext: Initial login detected, running full auth checks');
              await ensureUserProfile(currentSession.user);

              const isSignupFlow = window.location.pathname.includes('/signup');
              if (!isSignupFlow) {
                await checkPaymentStatus().catch(error => {
                  console.warn('AuthContext: Initial payment check failed:', error);
                });
              }
            } else {
              console.log('AuthContext: Page refresh detected, maintaining session without blocking checks');
              const acctType = getAccountType(currentSession.user.email);

              if (acctType === 'demo') {
                setIsPaid(true);
                setSubscriptionTier('club');
                setTeamLimit(999);
                setPaymentInfo(DEMO_PAYMENT_INFO);
              } else if (acctType === 'admin') {
                setIsPaid(true);
                setSubscriptionTier('club');
                setTeamLimit(999);
                setPaymentInfo(ADMIN_PAYMENT_INFO);
              } else {
                setIsPaid(true);
                setSubscriptionTier('club');
                setTeamLimit(999);

                setTimeout(() => {
                  checkPaymentStatus().catch(error => {
                    console.warn('AuthContext: Background payment check failed:', error);
                  });
                }, 1000);
              }
            }
          } catch (error) {
            console.error("AuthContext: Error in auth checks:", error);
          }
        } else {
          console.log("AuthContext: No authenticated user in auth state change");
          
          const isPageRefresh = sessionStorage.getItem('auth_session_initialized') === 'true';
          
          if (!isPageRefresh || event === 'SIGNED_OUT') {
            // Check if this is a temporary state during session recovery
            const lastKnownUserId = sessionStorage.getItem('last_known_user_id');
            const lastKnownPayment = sessionStorage.getItem('last_known_payment_status');
            
            if (event !== 'SIGNED_OUT' && lastKnownUserId && lastKnownPayment === 'paid') {
              console.log("AuthContext: No user but was previously paid - preserving state during recovery");
              // Don't clear payment state yet - give it time to recover
            } else {
              console.log("AuthContext: Clearing auth state (event:", event, ")");
              paymentCacheRef.current = null;
              setSession(null);
              setUser(null);
              setIsPaid(false);
              setPaymentInfo(null);
              setSubscriptionTier('free');
              setTeamLimit(0);
              setTeamsUsed(0);
            }
          } else {
            console.log("AuthContext: Page refresh detected, not clearing state yet");
          }
        }

        const isWaitingForRestore = !currentSession?.user && 
                                   sessionStorage.getItem('auth_session_initialized') === 'true' &&
                                   event !== 'SIGNED_OUT';
        
        if (!isWaitingForRestore) {
          setLoading(false);
        }
      }
    );

    const initializeAuth = async () => {
      try {
        console.log("🔐 AuthContext: initializeAuth() called");
        console.log("🔐 AuthContext: Checking for existing session...");
        
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error("AuthContext: Error getting session:", error);
          setLoading(false);
          return;
        }
        
        if (session) {
          console.log("🔐 AuthContext: Found initial session for:", session.user.email);
          console.log("🔐 AuthContext: Initial session token:", session.access_token?.substring(0, 20) + "...");
          sessionStorage.setItem('auth_session_initialized', 'true');
        } else {
          console.log("🔐 AuthContext: No initial session found");
          
          const wasAuthenticated = sessionStorage.getItem('auth_session_initialized') === 'true';
          if (wasAuthenticated) {
            console.log("AuthContext: Was authenticated before refresh, checking for stored session...");
            
            // Check if Supabase has auth tokens in localStorage
            const hasSupabaseTokens = Array.from({ length: localStorage.length }, (_, i) => localStorage.key(i))
              .some(key => key && key.startsWith('sb-') && key.includes('auth-token'));
            
            if (hasSupabaseTokens) {
              console.log("AuthContext: Found Supabase tokens, attempting session recovery...");
              // Try to recover the session
              supabase.auth.getSession().then(({ data: { session }, error }) => {
                if (error) {
                  console.error("AuthContext: Error recovering session:", error);
                  sessionStorage.removeItem('auth_session_initialized');
                  setLoading(false);
                } else if (session) {
                  console.log("AuthContext: Session recovered successfully!");
                  // Session will be handled by onAuthStateChange
                } else {
                  console.log("AuthContext: No session found despite tokens, may need re-authentication");
                  sessionStorage.removeItem('auth_session_initialized');
                  setLoading(false);
                }
              });
            } else {
              console.log("AuthContext: No Supabase tokens found, user needs to login");
              sessionStorage.removeItem('auth_session_initialized');
              setLoading(false);
            }
            return;
          }
        }
        
        if (!sessionStorage.getItem('auth_session_initialized') || session) {
          setLoading(false);
        }
      } catch (error) {
        console.error("AuthContext: Error in initializeAuth:", error);
        setLoading(false);
      }
    };

    initializeAuth();

    // Track last refresh time to avoid excessive refreshes
    let lastRefreshTime = Date.now();
    
    // Add window focus listener to refresh session when user returns
    const handleWindowFocus = async () => {
      if (session && Date.now() - lastRefreshTime > 5 * 60 * 1000) { // 5 minutes
        console.log("AuthContext: Window focused, refreshing session");
        try {
          const { data, error } = await supabase.auth.refreshSession();
          if (!error && data?.session) {
            setSession(data.session);
            lastRefreshTime = Date.now();
            console.log("AuthContext: Session refreshed on focus");
          }
        } catch (error) {
          console.error("AuthContext: Error refreshing session on focus:", error);
        }
      }
    };
    
    window.addEventListener('focus', handleWindowFocus);

    const refreshInterval = setInterval(async () => {
      if (session) {
        console.log("AuthContext: Refreshing session to prevent timeout");
        try {
          const { data, error } = await supabase.auth.refreshSession();
          if (error) {
            console.error("AuthContext: Failed to refresh session:", error);
            
            // If refresh token is expired, try to get a new session
            if (error.message?.includes('refresh_token') || error.message?.includes('expired')) {
              console.log("AuthContext: Refresh token expired, checking stored session");
              const { data: { session: storedSession } } = await supabase.auth.getSession();
              if (!storedSession) {
                console.log("AuthContext: No valid session found");
                
                // Check if user was previously paid - if so, don't sign them out
                const wasPaid = isPaid || paymentCacheRef.current?.isPaid || 
                               sessionStorage.getItem('last_known_payment_status') === 'paid';
                
                if (wasPaid) {
                  console.log("AuthContext: User was previously paid - keeping logged in despite session issue");
                  // Set a flag to indicate session needs refresh
                  sessionStorage.setItem('session_needs_refresh', 'true');
                } else {
                  console.log("AuthContext: User is not paid, signing out");
                  await signOut();
                }
              }
            }
          } else {
            console.log("AuthContext: Session refreshed successfully");
            if (data?.session) {
              setSession(data.session);
            }
          }
        } catch (error) {
          console.error("AuthContext: Error refreshing session:", error);
        }
      }
    }, 10 * 60 * 1000);

    return () => {
      isActive = false;
      clearTimeout(loadingTimeout);
      data.subscription.unsubscribe();
      clearInterval(refreshInterval);
      window.removeEventListener('focus', handleWindowFocus);
    };
  }, []); // Empty dependency array to run only once

  const signUp = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        }
      });

      if (error) {
        console.error("Signup error:", error.message);

        if (error.message.includes('already registered') && (import.meta.env.DEV || email.includes('test') || email.includes('demo'))) {
          console.warn("Test user detected. Attempting to bypass email confirmation...");

          const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
            email,
            password
          });

          if (!signInError) {
            console.log("Successfully signed in test user without confirmation");
            return { data: signInData, error: null };
          }

          console.error("Could not auto-sign in test user:", signInError);
          throw error;
        }

        throw error;
      }

      return { data, error: null };
    } catch (error) {
      console.error("Error in signUp:", error);
      throw error;
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      // CRITICAL: Clear any existing cached payment data to prevent session confusion
      paymentCacheRef.current = null;
      
      // Clear only app-specific data, preserve Supabase tokens
      clearAppLocalStorage();
      
      setUser(null);
      setSession(null);
      setIsPaid(false);
      setPaymentInfo(null);
      setSubscriptionTier('free');
      setTeamLimit(0);
      setTeamsUsed(0);
      
      // 1. Call Supabase to sign in
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        console.error("Error signing in:", error.message);

        const isTestUser = import.meta.env.DEV || email.includes('test') || email.includes('demo');
        if (error.message.includes('Email not confirmed') && isTestUser) {
          console.warn("Test user detected. Bypassing email confirmation requirement");

          const { error: signUpError } = await supabase.auth.signUp({
            email,
            password,
            options: {
              emailRedirectTo: `${window.location.origin}/auth/callback`,
            }
          });

          if (signUpError && !signUpError.message.includes('already registered')) {
            console.error("Error in signup attempt:", signUpError);
            throw error;
          }

          if (isTestUser) {
            const tempEmail = `temp_${Date.now()}@baseballguru.com`;
            console.log("Creating temporary test user:", tempEmail);

            const { error: tempError } = await supabase.auth.signUp({
              email: tempEmail,
              password,
              options: {
                data: { is_test: true }
              }
            });

            if (!tempError) {
              console.log("Successfully created temporary test user");

              const { data: tempSignInData, error: tempSignInError } = await supabase.auth.signInWithPassword({
                email: tempEmail,
                password
              });

              if (!tempSignInError) {
                console.log("Successfully signed in with temporary test user");
                localStorage.setItem('original_user_email', email);
                return tempSignInData;
              }
            }
          }

          const { data: retryData, error: retryError } = await supabase.auth.signInWithPassword({
            email,
            password,
          });

          if (retryError) {
            console.error("Error in retry sign in:", retryError);
            throw error;
          }

          return retryData;
        }

        throw error;
      }

      // 2. The session should be automatically set by Supabase after successful sign in
      // We don't need to manually set the token - Supabase handles this
      if (data.session?.access_token) {
        console.log("🔐 AuthContext: Sign in successful, token received");
        console.log("🔐 AuthContext: Token (first 20 chars):", data.session.access_token.substring(0, 20) + "...");
        
        // Verify the session is properly set
        const { data: sessionCheck } = await supabase.auth.getSession();
        console.log("🔐 AuthContext: Session verification after sign in:", {
          hasSession: !!sessionCheck?.session,
          userEmail: sessionCheck?.session?.user?.email,
          tokenMatch: sessionCheck?.session?.access_token === data.session.access_token
        });
      } else {
        console.log("🔐 AuthContext: WARNING - No access token in sign in response!");
      }

      return data;
    } catch (error) {
      console.error("Error in signIn:", error);
      throw error;
    }
  };

  const signOut = async () => {
    console.log("AuthContext: Signing out user");

    const isDemoMode = localStorage.getItem('demo_mode') === 'true';

    paymentCacheRef.current = null;

    // Sign out from Supabase first
    await supabase.auth.signOut();

    setUser(null);
    setSession(null);
    setIsPaid(false);
    setPaymentInfo(null);
    setSubscriptionTier('free');
    setTeamLimit(0);
    setTeamsUsed(0);
    
    // Clear only app-specific data, preserve Supabase tokens 
    // (though signOut should have cleared them)
    clearAppLocalStorage();
    
    // Clear payment status cache
    try {
      localStorage.removeItem(PAYMENT_STATUS_KEY);
    } catch (e) {
      console.warn("Error clearing payment status:", e);
    }

    // Additional cleanup for demo mode
    if (isDemoMode) {
      console.log("AuthContext: Clearing demo data");
      localStorage.removeItem('demo_team');
      localStorage.removeItem('demo_lineups');
      localStorage.removeItem('demo_player_ids');
      localStorage.removeItem('demo_data_initialized');
    }
  };

  const checkPaymentStatus = useCallback(async (): Promise<boolean> => {
    try {
      if (!user) {
        setIsPaid(false);
        setPaymentInfo(null);
        setSubscriptionTier('free');
        setTeamLimit(0);
        setTeamsUsed(0);
        paymentCacheRef.current = null;
        return false;
      }

      if (paymentCacheRef.current &&
          paymentCacheRef.current.userId === user.id &&
          Date.now() - paymentCacheRef.current.timestamp < CACHE_DURATION) {
        console.log("AuthContext: Using cached payment status");
        return paymentCacheRef.current.isPaid;
      }

      const isDemoUser = user.email === '<EMAIL>' || user.email === '<EMAIL>';
      const isAdminUser = user.email === '<EMAIL>' || user.email === '<EMAIL>';

      console.log(`AuthContext: Checking payment status for user ${user.id} (${user.email})`, {
        isDemoUser,
        isAdminUser
      });

      if (isDemoUser || isAdminUser) {
        console.log("AuthContext: Demo or admin user detected - setting up privileged access");

        setIsPaid(true);
        setSubscriptionTier('club');
        setTeamLimit(999);

        const targetPaymentInfo = isDemoUser ? DEMO_PAYMENT_INFO : ADMIN_PAYMENT_INFO;
        setPaymentInfo(targetPaymentInfo);

        try {
          const { data: teams } = await supabase
            .from('teams')
            .select('id')
            .eq('user_id', user.id);
          if (teams) {
            setTeamsUsed(teams.length);
          }
        } catch (teamError) {
          console.warn('AuthContext: Could not fetch team count for admin/demo user:', teamError);
          setTeamsUsed(0);
        }

        paymentCacheRef.current = { userId: user.id, timestamp: Date.now(), isPaid: true };
        return true;
      }

      try {
        console.log(`AuthContext: Checking payment status for regular user ${user.id} (${user.email})`);
        const checkStartTime = Date.now();

        const { data: subscriptionData, error: subscriptionError } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', user.id)
          .maybeSingle();

        const checkDuration = Date.now() - checkStartTime;
        console.log(`AuthContext: Payment check completed in ${checkDuration}ms`);

        if (subscriptionError) {
          console.error("AuthContext: Error checking subscription:", subscriptionError);

          // Try to recover from localStorage first
          try {
            const storedStatus = localStorage.getItem(PAYMENT_STATUS_KEY);
            if (storedStatus) {
              const parsed = JSON.parse(storedStatus);
              if (parsed.userId === user.id && parsed.isPaid) {
                console.warn("AuthContext: Subscription check failed but localStorage shows paid - maintaining status");
                return true;
              }
            }
          } catch (e) {
            console.warn("Error reading stored payment status:", e);
          }

          // If we have a cached payment status, trust it during errors
          if (paymentCacheRef.current && paymentCacheRef.current.isPaid) {
            console.warn("AuthContext: Subscription check failed but cache shows paid - maintaining status");
            return paymentCacheRef.current.isPaid;
          }

          if (isPaid) {
            console.warn("AuthContext: Subscription check failed but user was previously paid - maintaining status");
            return isPaid;
          }

          // Don't immediately set to unpaid on error - could be temporary network issue
          console.warn("AuthContext: Subscription check failed - maintaining current status");
          return isPaid || false;
        }

        if (subscriptionData) {
          console.log("AuthContext: Found subscription record:", {
            id: subscriptionData.id,
            is_paid: subscriptionData.is_paid,
            tier: subscriptionData.tier,
            team_limit: subscriptionData.team_limit
          });

          if (subscriptionData.is_paid) {
            setIsPaid(true);
            setPaymentInfo(subscriptionData);
            setSubscriptionTier(subscriptionData.tier || 'club');
            setTeamLimit(subscriptionData.team_limit || 999);

            try {
              const { data: teams } = await supabase
                .from('teams')
                .select('id')
                .eq('user_id', user.id);
              setTeamsUsed(teams?.length || 0);
            } catch (teamError) {
              console.warn('AuthContext: Could not fetch team count:', teamError);
              setTeamsUsed(0);
            }

            paymentCacheRef.current = { userId: user.id, timestamp: Date.now(), isPaid: true };
            
            // Store in localStorage for persistence
            try {
              localStorage.setItem(PAYMENT_STATUS_KEY, JSON.stringify({
                userId: user.id,
                isPaid: true,
                paymentInfo: subscriptionData,
                tier: subscriptionData.tier || 'club',
                teamLimit: subscriptionData.team_limit || 999,
                timestamp: Date.now()
              }));
            } catch (e) {
              console.warn("Error storing payment status:", e);
            }
            
            return true;
          } else {
            console.log("AuthContext: User has subscription but is_paid is false");
            setIsPaid(false);
            setPaymentInfo(null);
            setSubscriptionTier('free');
            setTeamLimit(0);
            setTeamsUsed(0);

            paymentCacheRef.current = { userId: user.id, timestamp: Date.now(), isPaid: false };
            return false;
          }
        } else {
          console.log("AuthContext: No paid subscriptions found for user");
          setIsPaid(false);
          setPaymentInfo(null);
          setSubscriptionTier('free');
          setTeamLimit(0);
          setTeamsUsed(0);
          
          if (user.email) {
            console.log(`AuthContext: Attempting to find orphaned payments for email: ${user.email}`);
            
            try {
              console.log('🔐 AuthContext: About to call verify-payment edge function');
              console.log('🔐 AuthContext: Edge function auth check:', {
                hasUser: !!user,
                userId: user.id,
                userEmail: user.email
              });
              
              // Check auth before edge function call
              const { data: sessionCheck } = await supabase.auth.getSession();
              console.log('🔐 AuthContext: Session before edge function:', {
                hasSession: !!sessionCheck?.session,
                hasToken: !!sessionCheck?.session?.access_token
              });
              
              const edgeFunctionPromise = supabase.functions.invoke('verify-payment', {
                body: { user_id: user.id, email: user.email }
              });
              
              const edgeFunctionTimeout = new Promise((resolve) => 
                setTimeout(() => resolve({ data: null, error: 'Edge function timeout' }), 5000)
              );
              
              const { data: orphanedCheck } = await Promise.race([
                edgeFunctionPromise,
                edgeFunctionTimeout
              ]) as any;
              
              if (orphanedCheck?.found_payment) {
                console.log("AuthContext: Found and linked orphaned payment, rechecking...");
                const { data: recheckData } = await supabase
                  .from('subscriptions')
                  .select('*')
                  .eq('user_id', user.id)
                  .eq('is_paid', true);
                
                if (recheckData && recheckData.length > 0) {
                  setIsPaid(true);
                  setPaymentInfo(recheckData[0]);
                  return true;
                }
              }
            } catch (linkError) {
              console.log("AuthContext: Could not check for orphaned payments:", linkError);
            }
          }
          
          setIsPaid(false);
          setPaymentInfo(null);
          paymentCacheRef.current = { userId: user.id, timestamp: Date.now(), isPaid: false };
          return false;
        }
      } catch (paymentError) {
        console.error("Error checking payment status:", paymentError);
        // Don't immediately set to false - could be temporary error
        const cachedStatus = paymentCacheRef.current?.isPaid || 
                           sessionStorage.getItem('last_known_payment_status') === 'paid';
        if (cachedStatus) {
          console.warn("Payment check error but cache shows paid - maintaining status");
          return true;
        }
        return isPaid || false;
      }
    } catch (error) {
      console.error("Error in checkPaymentStatus:", error);
      return false;
    }
  }, [user, isPaid, paymentInfo, CACHE_DURATION]);

  // Function to ensure user has a profile record - simplified to use upsert
  const ensureUserProfile = async (user: any) => {
    if (!user) return;
    
    console.log("AuthContext: Ensuring profile for user:", user.email);

    try {
      console.log("AuthContext: Ensuring profile for user:", user.email);
      
      const isAdmin = user.email === '<EMAIL>';
      
      // Add a timeout wrapper to prevent hanging
      const profilePromise = supabase
        .from('profiles')
        .upsert({
          id: user.id.toString(), // Convert UUID to string
          email: user.email,
          full_name: user.email,
          role: isAdmin ? 'admin' : 'user',
          is_admin: isAdmin,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'id'
        });
        
      const profileTimeout = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Profile creation timeout')), 5000)
      );
      
      try {
        const { error: profileError } = await Promise.race([
          profilePromise,
          profileTimeout
        ]) as any;
        
        if (profileError) {
          console.error("AuthContext: Error upserting profile:", profileError);
        } else {
          console.log("AuthContext: Profile ensured for user");
        }
      } catch (timeoutError) {
        console.warn("AuthContext: Profile creation timed out, continuing anyway");
      }

      // Also ensure they have a subscription record with timeout
      const subPromise = supabase
        .from('subscriptions')
        .upsert({
          user_id: user.id,
          is_paid: false, // Default to unpaid
          tier: 'starter',
          team_limit: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id',
          ignoreDuplicates: true // Don't update if already exists
        });
        
      const subTimeout = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Subscription creation timeout')), 5000)
      );
      
      try {
        const { error: subError } = await Promise.race([
          subPromise,
          subTimeout
        ]) as any;
        
        if (subError) {
          console.error("AuthContext: Error upserting subscription:", subError);
        } else {
          console.log("AuthContext: Subscription ensured for user");
        }
      } catch (timeoutError) {
        console.warn("AuthContext: Subscription creation timed out, continuing anyway");
      }
      
    } catch (error) {
      console.error("AuthContext: Error in ensureUserProfile:", error);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        session,
        supabase,
        loading,
        signUp,
        signIn,
        signOut,
        isPaid,
        checkPaymentStatus,
        paymentInfo,
        subscriptionTier,
        teamLimit,
        teamsUsed
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
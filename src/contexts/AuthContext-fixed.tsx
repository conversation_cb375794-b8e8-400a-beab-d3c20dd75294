// Key changes for session persistence fix:

// 1. Add a flag to track if we're initializing from storage
const [isInitializing, setIsInitializing] = useState(true);

// 2. Modify the auth state change handler to not clear state during initialization
const { data } = supabase.auth.onAuthStateChange(
  async (event, currentSession) => {
    console.log("AuthContext: Auth state changed:", event, "Session:", !!currentSession);
    
    // CRITICAL: Handle different events appropriately
    if (event === 'INITIAL_SESSION') {
      // This fires when Supabase is checking for an existing session
      if (currentSession) {
        console.log("AuthContext: Found initial session for:", currentSession.user.email);
        setSession(currentSession);
        setUser(currentSession.user);
        setIsInitializing(false);
      } else if (!isInitializing) {
        // Only clear if we're not in the initial loading phase
        console.log("AuthContext: No session and not initializing, clearing state");
        clearAuthState();
      }
      return;
    }
    
    if (event === 'SIGNED_IN') {
      console.log("AuthContext: User signed in:", currentSession?.user.email);
      if (currentSession) {
        // Clear data if user changed
        if (user && user.id !== currentSession.user.id) {
          clearAllUserData();
        }
        setSession(currentSession);
        setUser(currentSession.user);
        handleAccountType(currentSession.user);
      }
    }
    
    if (event === 'SIGNED_OUT') {
      console.log("AuthContext: User signed out");
      clearAuthState();
      clearAllUserData();
    }
    
    if (event === 'TOKEN_REFRESHED') {
      console.log("AuthContext: Token refreshed");
      if (currentSession) {
        setSession(currentSession);
      }
    }
    
    setLoading(false);
  }
);

// 3. Improve initial session retrieval
const initializeAuth = async () => {
  try {
    console.log("AuthContext: Initializing auth...");
    setIsInitializing(true);
    
    // Get the session with a reasonable timeout
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error("AuthContext: Error getting session:", error);
      setIsInitializing(false);
      setLoading(false);
      return;
    }
    
    if (session) {
      console.log("AuthContext: Found existing session for:", session.user.email);
      setSession(session);
      setUser(session.user);
      handleAccountType(session.user);
      
      // Mark as initialized for future refreshes
      sessionStorage.setItem('auth_initialized', 'true');
    } else {
      console.log("AuthContext: No existing session found");
      
      // Check if this is a page refresh with a previously authenticated session
      const wasAuthenticated = sessionStorage.getItem('auth_initialized') === 'true';
      if (wasAuthenticated) {
        console.log("AuthContext: Was previously authenticated, waiting for session restoration...");
        // Give Supabase more time to restore the session
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Check one more time
        const { data: { session: retrySession } } = await supabase.auth.getSession();
        if (retrySession) {
          console.log("AuthContext: Session restored on retry");
          setSession(retrySession);
          setUser(retrySession.user);
          handleAccountType(retrySession.user);
        } else {
          console.log("AuthContext: Session not restored, user needs to login");
          sessionStorage.removeItem('auth_initialized');
        }
      }
    }
    
    setIsInitializing(false);
    setLoading(false);
  } catch (error) {
    console.error("AuthContext: Error in initialization:", error);
    setIsInitializing(false);
    setLoading(false);
  }
};

// 4. Add helper functions
const clearAuthState = () => {
  setSession(null);
  setUser(null);
  setIsPaid(false);
  setPaymentInfo(null);
  setSubscriptionTier('free');
  setTeamLimit(0);
  setTeamsUsed(0);
  sessionStorage.removeItem('auth_initialized');
};

const clearAllUserData = () => {
  // Clear payment cache
  paymentCacheRef.current = null;
  
  // Clear localStorage items
  const keysToRemove = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && (key.includes('team') || key.includes('lineup') || key.includes('player') || 
               key === 'demo_mode' || key === 'auth_bypass_payment')) {
      keysToRemove.push(key);
    }
  }
  keysToRemove.forEach(key => localStorage.removeItem(key));
  
  // Clear team context
  if (window.clearTeamContext) {
    window.clearTeamContext();
  }
};

import { createContext, useState, useContext, ReactNode, useEffect, useMemo, useCallback } from "react";
import { generateId } from "@/lib/utils-enhanced";
import { useAuth } from "@/contexts/AuthContext";
import * as teamService from "@/services/teamService";
import { getDefaultRotationRules } from "@/services/teamService";
import { fetchTeamsOptimized } from "@/services/teamServiceOptimized";
import { toast } from "sonner";
import { supabase } from "@/supabaseClient";
import { getAccountType } from "@/utils/authUtils";
import { validateDemoModeOperation, showDemoModeWarning } from "@/utils/demoModeValidation";
import { useTeamsCache } from "@/hooks/useTeamsCache";

export type Position = "Pitcher" | "Catcher" | "First Base" | "Second Base" | "Third Base" | "Shortstop" | "Left Field" | "Center Field" | "Right Field" | "3B/MI/SS/2B";

// Simplified player rating system (1-5 scale)
export type PlayerRating = 1 | 2 | 3 | 4 | 5;

export interface PositionRatings {
  pitcher?: PlayerRating;
  catcher?: PlayerRating;
  firstBase?: PlayerRating;
  secondBase?: PlayerRating;
  thirdBase?: PlayerRating;
  shortstop?: PlayerRating;
  leftField?: PlayerRating;
  centerField?: PlayerRating;
  rightField?: PlayerRating;
  leftCenter?: PlayerRating;
  rightCenter?: PlayerRating;
}

// Legacy support for existing preference system (for migration)
export type PositionPreference = "preferred" | "secondary" | "avoid" | "neutral";
export interface PositionPreferenceWithRank {
  level: PositionPreference;
  rank?: number;
}
export interface PositionPreferences {
  pitcher?: PositionPreference | PositionPreferenceWithRank;
  catcher?: PositionPreference | PositionPreferenceWithRank;
  firstBase?: PositionPreference | PositionPreferenceWithRank;
  secondBase?: PositionPreference | PositionPreferenceWithRank;
  thirdBase?: PositionPreference | PositionPreferenceWithRank;
  shortstop?: PositionPreference | PositionPreferenceWithRank;
  leftField?: PositionPreference | PositionPreferenceWithRank;
  centerField?: PositionPreference | PositionPreferenceWithRank;
  rightField?: PositionPreference | PositionPreferenceWithRank;
  leftCenter?: PositionPreference | PositionPreferenceWithRank;
  rightCenter?: PositionPreference | PositionPreferenceWithRank;
}

// New team-dynamics-based preference system
export type PlayerRole = "go-to" | "capable" | "fill-in" | "avoid" | "unset";

export interface TeamRolePreferences {
  // Primary positions (limited player pools)
  pitcher?: PlayerRole;
  catcher?: PlayerRole;

  // Skill-dependent infield positions
  shortstop?: PlayerRole;
  secondBase?: PlayerRole;
  thirdBase?: PlayerRole;

  // Flexible positions
  firstBase?: PlayerRole;
  leftField?: PlayerRole;
  centerField?: PlayerRole;
  rightField?: PlayerRole;

  // Special designations
  isUtilityPlayer?: boolean;  // Can play anywhere competently
  isOutfieldSpecialist?: boolean;  // Prefers outfield positions
}

export interface PitcherStrategy {
  role: 'starter' | 'reliever' | 'closer' | 'any';
  maxInningsPerGame?: number;
  maxInningsPerSeries?: number;
  preferredInnings?: number[]; // Specific innings they prefer (1-indexed)
  restInningsBetweenAppearances?: number;
  priority: number; // 1-5, with 1 being highest priority for their role
}

export interface Player {
  id: string;
  name: string;
  // New simplified rating system
  positionRatings?: PositionRatings;
  isStarPlayer?: boolean; // Direct star player designation for competitive mode

  // Team-dynamics-based preference system (primary system)
  teamRoles?: TeamRolePreferences;

  // Advanced pitcher strategy settings
  pitcherStrategy?: PitcherStrategy;

  // Legacy support (for backward compatibility during migration)
  positionPreferences?: PositionPreferences;
}

export type GameResult = 'win' | 'loss' | null;

export interface Lineup {
  id: string;
  name: string;
  gameDate: string;
  createdDate: string;
  team_id?: string; // Team ID for database operations
  attendance: {[playerId: string]: boolean}; // Track which players are present
  innings: InningLineup[];
  battingOrder: string[];
  // Game-specific rotation settings (stored when lineup is created)
  rotationSettings?: {
    limitBenchTime: boolean;
    rotateLineupEvery: number;
    rotatePitcherEvery: number;
  };
  gameResult?: GameResult; // Win/Loss tracking for completed games
  // Series metadata for batch-created games
  seriesTitle?: string;
  seriesId?: string;
  gameNumber?: number;
  totalGamesInSeries?: number;
}

export interface InningLineup {
  inning: number;
  positions: {
    leftField: string;
    centerField: string;
    rightField: string;
    thirdBase: string;
    shortstop: string;
    secondBase: string;
    firstBase: string;
    catcher: string;
    pitcher: string;
    bench: string[];
    // Optional positions for teams that use them
    leftCenter?: string;
    rightCenter?: string;
  };
}

export interface RotationRules {
  rotationMethod: "standard" | "manual";
  equalPlayingTime: boolean;
  rotatePlayers: boolean;
  respectPositionLockouts: boolean;
  allowPitcherRotation: boolean;
  allowCatcherRotation: boolean;
  prioritizeOutfieldRotation: boolean;
  limitBenchTime: boolean;
  rotateLineupEvery: number; // How often to rotate lineup (1, 2, 3 innings)
  rotatePitcherEvery: number; // How often to rotate pitcher (1, 2, 3 innings)

  // Competitive mode settings - simplified
  competitiveMode?: boolean;
  competitiveMinPlayingTime?: number; // Minimum playing time percentage (30-50%)
}

export interface Team {
  id: string;
  name: string;
  players: Player[];
  lineups: Lineup[];
  rotationRules: RotationRules;
  defaultBattingOrder?: string[];
}

interface TeamContextType {
  teams: Team[];
  currentTeamId: string;
  setCurrentTeamId: (id: string) => void;
  addTeam: (name: string) => Promise<string | null>;
  updateTeam: (team: Team) => Promise<void>;
  removeTeam: (id: string) => Promise<void>;
  currentTeam: Team;
  teamName: string;
  setTeamName: (name: string) => void;
  players: Player[];
  setPlayers: (players: Player[]) => void;
  addPlayer: (player: Player) => Promise<void>;
  updatePlayer: (player: Player) => Promise<void>;
  removePlayer: (id: string) => Promise<void>;
  lineups: Lineup[];
  setLineups: (lineups: Lineup[]) => void;
  addLineup: (lineup: Lineup) => Promise<Lineup>;
  updateLineup: (lineup: Lineup) => Promise<void>;
  removeLineup: (id: string) => Promise<void>;
  getAvailablePlayers: (lineup: Lineup, attendanceData?: {[playerId: string]: boolean}) => Player[];
  rotationRules: RotationRules;
  updateRotationRules: (rules: RotationRules) => Promise<void>;
  cleanupDemoLineups: () => Promise<void>;
  refreshTeamData: () => Promise<void>;
  loading: boolean;
  getDefaultBattingOrder: () => string[] | null;
  setDefaultBattingOrder: (playerIds: string[]) => Promise<void>;
}

export const TeamContext = createContext<TeamContextType | undefined>(undefined);

export const useTeam = () => {
  const context = useContext(TeamContext);
  if (context === undefined) {
    throw new Error('useTeam must be used within a TeamProvider');
  }
  return context;
};

interface TeamProviderProps {
  children: ReactNode;
}

// Default players for a new team
const defaultPlayers: Player[] = [
  { id: "p1", name: "Mikayla", positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: "Shortstop" }, positionPreferences: {} },
  { id: "p2", name: "Finn", positionRestrictions: { pitcher: true, catcher: false, firstBase: true, other: null }, positionPreferences: {} },
  { id: "p3", name: "Avalon", positionRestrictions: { pitcher: false, catcher: false, firstBase: false, other: null }, positionPreferences: {} },
  { id: "p4", name: "Grace", positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: null }, positionPreferences: {} },
  { id: "p5", name: "Bella", positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: null }, positionPreferences: {} },
  { id: "p6", name: "Kenzie", positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: null }, positionPreferences: {} },
  { id: "p7", name: "Presley", positionRestrictions: { pitcher: false, catcher: true, firstBase: true, other: null }, positionPreferences: {} },
  { id: "p8", name: "Avery", positionRestrictions: { pitcher: false, catcher: true, firstBase: true, other: null }, positionPreferences: {} },
  { id: "p9", name: "Elle", positionRestrictions: { pitcher: true, catcher: false, firstBase: false, other: null }, positionPreferences: {} },
  { id: "p10", name: "Vienna", positionRestrictions: { pitcher: true, catcher: false, firstBase: true, other: null }, positionPreferences: {} },
  { id: "p11", name: "Katelyn", positionRestrictions: { pitcher: false, catcher: true, firstBase: true, other: null }, positionPreferences: {} },
  { id: "p12", name: "Morgan", positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: null }, positionPreferences: {} },
  { id: "p13", name: "Evy", positionRestrictions: { pitcher: false, catcher: true, firstBase: true, other: null }, positionPreferences: {} },
  { id: "p14", name: "Charlotte", positionRestrictions: { pitcher: true, catcher: true, firstBase: false, other: null }, positionPreferences: {} }
];

// Default rotation rules for a new team
const defaultRotationRules: RotationRules = {
  rotationMethod: "standard",
  equalPlayingTime: true,
  rotatePlayers: true,
  respectPositionLockouts: true,
  allowPitcherRotation: false,
  allowCatcherRotation: true,
  prioritizeOutfieldRotation: true,
  limitBenchTime: true,
  rotateLineupEvery: 1,
  rotatePitcherEvery: 2,

  // Competitive mode defaults
  competitiveMode: false,
  competitiveMinPlayingTime: 50
};

export const TeamProvider = ({ children }: TeamProviderProps) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  
  // Initialize cache for teams data
  const {
    getCachedData,
    setCachedData,
    invalidateCache,
    isStale,
    isRevalidating,
    setIsRevalidating
  } = useTeamsCache({ ttl: 5 * 60 * 1000 }); // 5 minute cache

  // Initialize with an empty teams array
  const [teams, setTeams] = useState<Team[]>([]);

  // Initialize with empty currentTeamId
  const [currentTeamId, setCurrentTeamIdState] = useState<string>("");

  // Setter for currentTeamId
  const setCurrentTeamId = useCallback((id: string) => {
    setCurrentTeamIdState(prevCurrentTeamId => {
      if (id !== prevCurrentTeamId) {
        console.log("Setting current team ID (prev, new):", prevCurrentTeamId, id);
        // Persist team selection to localStorage
        localStorage.setItem('current_team_id', id);
        console.log("TeamContext: Saved current team ID to localStorage:", id);
        return id;
      }
      return prevCurrentTeamId;
    });
  }, [setCurrentTeamIdState]);

  // Get the current team based on currentTeamId with more robust fallback
  const currentTeam = useMemo(() => {
    const foundTeam = teams.find(team => team.id === currentTeamId);
    if (foundTeam) {
      return foundTeam;
    }
    // If currentTeamId is not (yet) valid, return a default empty team.
    // A separate useEffect will handle setting currentTeamId correctly.
    return {
      id: "",
      name: "",
      players: [],
      lineups: [],
      rotationRules: defaultRotationRules
    };
  }, [teams, currentTeamId]);

  // Effect to manage currentTeamId based on the available teams
  useEffect(() => {
    if (teams.length > 0) {
      const currentTeamIsValid = teams.some(team => team.id === currentTeamId);
      if (!currentTeamIsValid) {
        // Try to restore from localStorage first
        const savedTeamId = localStorage.getItem('current_team_id');
        const savedTeamExists = savedTeamId && teams.some(team => team.id === savedTeamId);
        
        if (savedTeamExists) {
          console.log("TeamContext: Restoring team from localStorage:", savedTeamId);
          setCurrentTeamId(savedTeamId);
        } else {
          // If currentTeamId is not valid (e.g., empty, or not in the list of teams),
          // set it to the ID of the first available team.
          console.log("TeamContext: currentTeamId is invalid or not in teams, setting to first team:", teams[0].id);
          setCurrentTeamId(teams[0].id);
        }
      }
    } else {
      // No teams are available. If currentTeamId is set, clear it.
      if (currentTeamId !== "") {
        console.log("TeamContext: No teams available, clearing currentTeamId.");
        setCurrentTeamId("");
      }
    }
  }, [teams, currentTeamId, setCurrentTeamId]);

  // These are convenience accessors for the current team's properties
  const teamName = currentTeam.name;
  const players = currentTeam.players;
  const lineups = currentTeam.lineups;
  const rotationRules = currentTeam.rotationRules;

  // Load teams from Supabase when user changes
  useEffect(() => {
    let isActive = true; // Flag to prevent state updates after component unmount

    // Add a timeout to log loading status but don't show user-facing messages
    const loadingTimeout = setTimeout(() => {
      console.log("TeamContext: Loading teams is taking longer than expected");
      // Removed the toast notification as it was showing inappropriately
    }, 5000); // 5 second timeout

    // Define the async function to load teams
    const loadTeamsAsync = async () => {
      if (!isActive) return; // Don't proceed if component unmounted

      console.log("TeamContext: Starting to load teams for user:", user?.email);

      // Check if we're in demo mode
      const isDemoMode = localStorage.getItem('demo_mode') === 'true';

      if (!user && !isDemoMode) {
        console.log("TeamContext: No user logged in and not in demo mode, redirecting to login");
        setLoading(false);
        return;
      }

      // Check cache first
      const cachedTeams = getCachedData();
      if (cachedTeams && !isStale) {
        console.log("TeamContext: Using cached teams data");
        setTeams(cachedTeams);
        setLoading(false);
        return;
      }
      
      // If we have stale cached data, show it while revalidating
      if (cachedTeams && isStale && !isRevalidating) {
        console.log("TeamContext: Using stale cache while revalidating");
        setTeams(cachedTeams);
        setLoading(false);
        setIsRevalidating(true);
      } else {
        setLoading(true);
      }

      // Use demo user ID if in demo mode and no user available
      const userId = user?.id || (isDemoMode ? localStorage.getItem('demo_user_id') || 'f15ba189-c70f-4513-9451-6f95568e784a' : null);

      if (!userId) {
        console.log("TeamContext: No user ID available");
        setLoading(false);
        return;
      }

      console.log("TeamContext: Using user ID:", userId, isDemoMode ? "(demo mode)" : "(regular mode)");
      console.log("TeamContext: Demo mode localStorage values:", {
        demo_mode: localStorage.getItem('demo_mode'),
        demo_user_id: localStorage.getItem('demo_user_id'),
        current_team_id: localStorage.getItem('current_team_id')
      });

      // Special handling for demo mode - try to load demo team directly first
      if (isDemoMode && userId === 'f15ba189-c70f-4513-9451-6f95568e784a') {
        console.log("TeamContext: Demo mode detected - attempting direct demo team load");
        try {
          const demoTeams = await fetchTeamsOptimized('f15ba189-c70f-4513-9451-6f95568e784a');
          console.log("TeamContext: Raw demo teams result:", demoTeams);

          if (demoTeams.length > 0) {
            console.log("TeamContext: Successfully loaded demo teams directly:");
            demoTeams.forEach((team, index) => {
              console.log(`  Team ${index + 1}:`, {
                id: team.id,
                name: team.name,
                playerCount: team.players?.length || 0,
                lineupCount: team.lineups?.length || 0,
                lineups: team.lineups?.map(l => ({ id: l.id, name: l.name, inningCount: l.innings?.length || 0 })) || []
              });
            });
            setTeams(demoTeams);
            setCachedData(demoTeams);
            setLoading(false);
            setIsRevalidating(false);
            toast.success(`Demo team loaded with ${demoTeams[0].lineups?.length || 0} lineups`);
            return; // Exit early - we got the demo data
          } else {
            console.log("TeamContext: Direct demo team load returned no teams");
          }
        } catch (directDemoError) {
          console.error("TeamContext: Direct demo team load failed:", directDemoError);
          console.error("TeamContext: Error details:", {
            message: directDemoError.message,
            code: directDemoError.code,
            details: directDemoError.details
          });
        }
      }

      try {
        // Try to fetch teams for the user with retry logic
        let fetchedTeams: Team[] = [];
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
          try {
            console.log(`TeamContext: Attempt ${retryCount + 1} to fetch teams for user ID: ${userId}`);

            // Add extra logging for demo mode
            if (isDemoMode) {
              console.log("TeamContext: Demo mode detected - fetching demo team data");
              console.log("TeamContext: Expected demo team ID: 83bd9832-f5db-4c7d-b234-41fd38f90007");
              console.log("TeamContext: Expected demo user ID: 212e5e06-9dd0-4d56-89d2-69915b205b53");
            }

            // Use optimized version for production
            fetchedTeams = await fetchTeamsOptimized(userId);
            console.log(`TeamContext: Successfully fetched ${fetchedTeams.length} teams on attempt ${retryCount + 1}`);

            // Log details about fetched teams in demo mode
            if (isDemoMode && fetchedTeams.length > 0) {
              console.log("TeamContext: Demo teams fetched:", fetchedTeams.map(t => ({ id: t.id, name: t.name, playerCount: t.players?.length || 0, lineupCount: t.lineups?.length || 0 })));
            }

            break; // Success, exit retry loop
          } catch (fetchError) {
            retryCount++;
            console.error(`TeamContext: Attempt ${retryCount} failed for user ${userId}:`, fetchError);

            // Add specific logging for demo mode failures
            if (isDemoMode) {
              console.error("TeamContext: Demo mode team fetch failed - this should not happen with persistent demo data");
              console.error("TeamContext: Error details:", {
                message: fetchError.message,
                code: fetchError.code,
                details: fetchError.details,
                hint: fetchError.hint
              });
            }

            if (retryCount >= maxRetries) {
              throw fetchError; // Last attempt failed, throw the error
            }

            // Wait before retrying (exponential backoff)
            const delay = 1000 * Math.pow(2, retryCount - 1); // 1s, 2s, 4s
            console.log(`TeamContext: Waiting ${delay}ms before retry ${retryCount + 1}`);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }

        if (fetchedTeams.length === 0) {
          // No teams found - check if this is a demo user
          const isDemoUser = isDemoMode || user?.email?.includes('demo') || user?.email?.includes('baseball_demo');

          if (isDemoUser) {
            console.log("TeamContext: Demo user with no teams - this should not happen with persistent demo");
            console.log("TeamContext: User ID used for query:", userId);
            console.log("TeamContext: Demo mode flag:", isDemoMode);
            console.log("TeamContext: User email:", user?.email);

            // Try to diagnose why the demo team wasn't found
            console.log("TeamContext: Attempting direct database check for demo team...");
            try {
              const { data: directCheck, error: directError } = await supabase
                .from('teams')
                .select('*')
                .eq('id', '83bd9832-f5db-4c7d-b234-41fd38f90007')
                .single();

              if (directError) {
                console.error("TeamContext: Direct demo team check failed:", directError);
              } else if (directCheck) {
                console.log("TeamContext: Demo team exists in database:", directCheck);
                console.log("TeamContext: Demo team user_id:", directCheck.user_id);
                console.log("TeamContext: Query user_id:", userId);
                console.log("TeamContext: User IDs match:", directCheck.user_id === userId);

                // If the demo team exists but user IDs don't match, try to fetch it directly
                if (directCheck.user_id !== userId) {
                  console.log("TeamContext: User ID mismatch - trying to fetch demo team with correct user ID");
                  try {
                    const demoTeams = await fetchTeamsOptimized(directCheck.user_id);
                    if (demoTeams.length > 0) {
                      console.log("TeamContext: Successfully fetched demo team with correct user ID:", demoTeams);
                      setTeams(demoTeams);
                      toast.success("Demo team loaded successfully");
                      return; // Exit early, we found the data
                    }
                  } catch (demoFetchError) {
                    console.error("TeamContext: Failed to fetch demo team with correct user ID:", demoFetchError);
                  }
                }
              }
            } catch (directCheckError) {
              console.error("TeamContext: Error during direct demo team check:", directCheckError);
            }

            // For demo mode, we should always have the persistent demo team
            // If we don't, something went wrong, so use fallback
            console.error("TeamContext: Demo team not found in database, this should not happen!");
            const fallbackTeam = {
              id: "83bd9832-f5db-4c7d-b234-41fd38f90007",
              name: "Demo Wildcats",
              players: defaultPlayers,
              lineups: [],
              rotationRules: defaultRotationRules
            };
            setTeams([fallbackTeam]);
            // setCurrentTeamId("83bd9832-f5db-4c7d-b234-41fd38f90007"); // Managed by useEffect
            // localStorage.setItem('current_team_id', '83bd9832-f5db-4c7d-b234-41fd38f90007'); // Potential loop source
            toast.warning("Using fallback demo team - persistent demo data not found");
          } else {
            // Regular user with no teams - create a basic team
            console.log("TeamContext: Regular user with no teams, creating basic team");
            try {
              const newTeam = await teamService.createTeam(userId, "My Team");
              setTeams([newTeam]);
              // setCurrentTeamId(newTeam.id); // Managed by useEffect
            } catch (error) {
              console.error("TeamContext: Error creating basic team:", error);
              const fallbackTeam = {
                id: "team1",
                name: "My Team",
                players: [],
                lineups: [],
                rotationRules: defaultRotationRules
              };
              setTeams([fallbackTeam]);
              // setCurrentTeamId("team1"); // Managed by useEffect
            }
          }
        } else {
          // Teams found - use them directly
          console.log("TeamContext: Using existing teams:", fetchedTeams);
          // Cache the fetched teams
          setCachedData(fetchedTeams);
          
          // For demo users, ensure we use the Demo Wildcats team
          if (isDemoMode || user?.email?.includes('demo') || user?.email?.includes('baseball_demo')) {
            // Find the Demo Wildcats team
            const demoWildcatsTeam = fetchedTeams.find(team => 
              team.id === '83bd9832-f5db-4c7d-b234-41fd38f90007' || 
              team.name === 'Demo Wildcats'
            );
            
            if (demoWildcatsTeam) {
              console.log("TeamContext: Found Demo Wildcats team for demo user:", demoWildcatsTeam.id);
              // Reorder teams to put Demo Wildcats first
              const reorderedTeams = [demoWildcatsTeam, ...fetchedTeams.filter(t => t.id !== demoWildcatsTeam.id)];
              setTeams(reorderedTeams);
              // Force selection of Demo Wildcats team
              localStorage.setItem('current_team_id', demoWildcatsTeam.id);
            } else {
              console.log("TeamContext: Demo Wildcats team not found in fetched teams");
              setTeams(fetchedTeams);
            }
          } else {
            setTeams(fetchedTeams);
          }
        }
      } catch (error) {
        console.error("TeamContext: Error loading teams:", error);

        // Check if this is a demo user
        const isDemoUser = isDemoMode || user?.email?.includes('demo') || user?.email?.includes('baseball_demo');

        if (isDemoUser) {
          // Don't create fallback demo teams - this was causing duplicates
          console.log("Demo user error - should use database demo team");
          toast.error("Demo mode error. Please try refreshing the page.");
          setTeams([]);
        } else {
          // For regular users, try a recovery approach
          try {
            const { data: teamsData, error: fetchError } = await supabase
              .from('teams')
              .select('*')
              .eq('user_id', user.id);

            if (!fetchError && teamsData && teamsData.length > 0) {
              // We have basic team data
              const basicTeams = teamsData.map(team => ({
                id: team.id,
                name: team.name,
                players: [],
                lineups: [],
                rotationRules: defaultRotationRules
              }));

              setTeams(basicTeams);
              // setCurrentTeamId(basicTeams[0].id); // Managed by useEffect
              toast.warning("Limited team data loaded. Some features may be unavailable.");

              // Try to load full data in the background
              fetchTeamsOptimized(user.id)
                .then(fullTeams => {
                  if (fullTeams.length > 0) {
                    setTeams(fullTeams); // This will trigger the useEffect to set currentTeamId
                    // The logic to preserve currentId if it still exists in fullTeams
                    // is handled by the useEffect for currentTeamId management.
                  }
                })
                .catch(e => console.error("TeamContext: Background fetch failed:", e));
            } else {
              // No teams found or error, use fallback
              toast.error("Failed to load teams");
              const defaultTeam = {
                id: "team1",
                name: "Default Team",
                players: defaultPlayers,
                lineups: [],
                rotationRules: defaultRotationRules
              };

              setTeams([defaultTeam]);
              // setCurrentTeamId("team1"); // Managed by useEffect
            }
          } catch (recoveryError) {
            console.error("TeamContext: Recovery attempt failed:", recoveryError);

            // Use fallback default team
            const defaultTeam = {
              id: "team1",
              name: "Default Team",
              players: defaultPlayers,
              lineups: [],
              rotationRules: defaultRotationRules
            };

            setTeams([defaultTeam]);
            // setCurrentTeamId("team1"); // Managed by useEffect
            toast.error("Failed to load teams. Using default team.");
          }
        }
      } finally {
        // Always set loading to false when done
        setLoading(false);
        setIsRevalidating(false);
      }
    };

    // Execute the async function
    loadTeamsAsync();

    // Clean up the timeout and prevent state updates after unmount
    return () => {
      isActive = false;
      clearTimeout(loadingTimeout);
    };
  }, [user?.id, user?.email]);

  // Team management functions
  const addTeam = async (name: string) => {
    // All users use the database
    invalidateCache(); // Clear cache when adding team

    try {
      // For regular users, create team in Supabase
      const userId = user.id;
      const newTeam = await teamService.createTeam(userId, name);
      setTeams([...teams, newTeam]);
      toast.success(`Team "${name}" created successfully`);
      return newTeam.id;
    } catch (error) {
      console.error("Error creating team:", error);
      toast.error("Failed to create team");
      return null;
    }
  };

  const updateTeam = async (updatedTeam: Team) => {
    console.log("TeamContext: Updating team:", updatedTeam.id, updatedTeam.name);

    try {
      // Create a deep copy to avoid reference issues
      const teamCopy = JSON.parse(JSON.stringify(updatedTeam));

      // Update local state immediately for better UX
      const updatedTeams = teams.map(team =>
        team.id === teamCopy.id ? teamCopy : team
      );

      setTeams(updatedTeams);

      // Make sure currentTeamId is set correctly
      if (currentTeamId === teamCopy.id) {
        // Force a refresh of the current team ID to trigger UI updates
        setCurrentTeamId(teamCopy.id);
      }

      // Demo mode now uses the database just like regular mode

      if (!user) {
        console.log("TeamContext: No user, skipping database update");
        return; // Skip Supabase update in demo mode
      }

      // Use a flag to prevent multiple update attempts
      const updateInProgress = localStorage.getItem(`updating_team_${teamCopy.id}`);
      if (updateInProgress) {
        console.log(`TeamContext: Update already in progress for team ${teamCopy.id}, skipping`);
        return;
      }

      localStorage.setItem(`updating_team_${teamCopy.id}`, 'true');

      // Set a timeout to clear the flag in case of errors
      setTimeout(() => {
        localStorage.removeItem(`updating_team_${teamCopy.id}`);
      }, 10000); // 10 second timeout

      // Update the team normally for all users
      await teamService.updateTeam(updatedTeam);

      // Clear the flag after successful update
      localStorage.removeItem(`updating_team_${updatedTeam.id}`);
    } catch (error) {
      console.error("TeamContext: Error updating team:", error);

      // Only show the error message once
      const errorShown = localStorage.getItem(`error_shown_team_${updatedTeam.id}`);
      if (!errorShown) {
        toast.error("Failed to update team");
        localStorage.setItem(`error_shown_team_${updatedTeam.id}`, 'true');

        // Clear the error flag after 5 seconds to allow future error messages
        setTimeout(() => {
          localStorage.removeItem(`error_shown_team_${updatedTeam.id}`);
        }, 5000);
      }

      // Clear the update flag
      localStorage.removeItem(`updating_team_${updatedTeam.id}`);

      // Revert to previous state on error, but avoid recursive calls
      if (user.id) {
        try {
          console.log("TeamContext: Reverting to previous state after error");
          const previousTeams = await fetchTeamsOptimized(user.id);
          setTeams(previousTeams);
        } catch (revertError) {
          console.error("Error reverting team changes:", revertError);
        }
      }
    }
  };

  const removeTeam = async (id: string) => {
    // Don't allow removing the last team
    if (teams.length <= 1) {
      toast.error("Cannot delete the only team");
      return;
    }
    
    invalidateCache(); // Clear cache when removing team

    // Store the current teams for potential rollback
    const previousTeams = [...teams];
    const teamToDelete = teams.find(team => team.id === id);
    
    if (!teamToDelete) {
      toast.error("Team not found");
      return;
    }

    // If removing the current team, switch to another team
    if (id === currentTeamId) {
      const otherTeam = teams.find(team => team.id !== id);
      if (otherTeam) {
        setCurrentTeamId(otherTeam.id);
      }
    }

    if (!user) return; // Skip Supabase delete in demo mode

    try {
      // First attempt to delete from database
      await teamService.deleteTeam(id);
      
      // Only update local state if database deletion succeeded
      const updatedTeams = teams.filter(team => team.id !== id);
      setTeams(updatedTeams);
      
      toast.success(`Team "${teamToDelete.name}" deleted successfully`);
    } catch (error) {
      console.error("Error deleting team:", error);
      
      // Check if this is a protected account error
      if (error?.message?.includes('protected account')) {
        toast.error("Cannot delete teams for protected accounts");
      } else {
        toast.error("Failed to delete team: " + (error?.message || "Unknown error"));
      }
      
      // Don't revert state - the team wasn't actually deleted from the database
      // so it will still be there on next refresh
    }
  };

  // Update the current team's name
  const setTeamName = async (name: string) => {
    console.log(`TeamContext: Setting team name to: "${name}"`);

    // Update local state immediately
    const updatedTeam = { ...currentTeam, name };

    // Update state directly to avoid recursive calls
    setTeams(teams.map(team => team.id === updatedTeam.id ? updatedTeam : team));

    // All users (including demo users) should save data to the database
    const isDemoMode = !user;
    if (isDemoMode) {
      console.log("TeamContext: No user logged in, cannot update team name");
      return;
    }

    if (!user) return; // Skip database updates if no user

    // Use a flag to prevent multiple update attempts
    const updateInProgress = localStorage.getItem(`updating_team_name_${currentTeam.id}`);
    if (updateInProgress) {
      console.log(`TeamContext: Team name update already in progress for team ${currentTeam.id}, skipping`);
      return;
    }

    localStorage.setItem(`updating_team_name_${currentTeam.id}`, 'true');

    // Special handling for Noah's account
    if (user?.email === '<EMAIL>') {
      console.log(`TeamContext: Setting team name for Noah's account to: "${name}"`);

      try {
        // Direct update to the database
        const { error } = await supabase
          .from('teams')
          .update({
            name,
            updated_at: new Date().toISOString()
          })
          .eq('id', currentTeam.id);

        if (error) {
          console.error("TeamContext: Direct team name update failed:", error);
          throw error;
        } else {
          console.log(`TeamContext: Direct team name update succeeded: "${name}"`);

          // Verify the update was successful
          const { data, error: verifyError } = await supabase
            .from('teams')
            .select('name')
            .eq('id', currentTeam.id)
            .single();

          if (verifyError) {
            console.error("TeamContext: Failed to verify team name update:", verifyError);
          } else {
            console.log(`TeamContext: Verified team name in database: "${data.name}"`);
            if (data.name !== name) {
              console.error(`TeamContext: Team name mismatch! Expected "${name}" but got "${data.name}"`);

              // Try one more time with a different approach
              const { error: finalError } = await supabase
                .from('teams')
                .update({
                  name,
                  updated_at: new Date().toISOString()
                })
                .eq('id', currentTeam.id);

              if (finalError) {
                console.error("TeamContext: Final team name update attempt failed:", finalError);
              } else {
                console.log("TeamContext: Final team name update attempt succeeded");
              }
            }
          }
        }
      } catch (error) {
        console.error("TeamContext: Error in setTeamName:", error);

        // Only show the error message once
        const errorShown = localStorage.getItem(`error_shown_team_name_${currentTeam.id}`);
        if (!errorShown) {
          toast.error("There was an issue updating the team name. Please try again.");
          localStorage.setItem(`error_shown_team_name_${currentTeam.id}`, 'true');

          // Clear the error flag after 5 seconds
          setTimeout(() => {
            localStorage.removeItem(`error_shown_team_name_${currentTeam.id}`);
          }, 5000);
        }
      } finally {
        // Clear the update flag
        localStorage.removeItem(`updating_team_name_${currentTeam.id}`);
      }
    } else {
      // For other users, use the service method directly
      try {
        await teamService.updateTeam(updatedTeam);
        localStorage.removeItem(`updating_team_name_${currentTeam.id}`);
      } catch (error) {
        console.error("TeamContext: Error updating team name:", error);

        // Only show the error message once
        const errorShown = localStorage.getItem(`error_shown_team_name_${currentTeam.id}`);
        if (!errorShown) {
          toast.error("There was an issue updating the team name. Please try again.");
          localStorage.setItem(`error_shown_team_name_${currentTeam.id}`, 'true');

          // Clear the error flag after 5 seconds
          setTimeout(() => {
            localStorage.removeItem(`error_shown_team_name_${currentTeam.id}`);
          }, 5000);
        }

        // Clear the update flag
        localStorage.removeItem(`updating_team_name_${currentTeam.id}`);
      }
    }
  };

  // Player management functions for the current team
  const setPlayers = async (newPlayers: Player[]) => {
    try {
      console.log("TeamContext: Setting players", newPlayers.length, "players for team", currentTeam.id);

      // Check if we're in demo mode
      const isDemoMode = localStorage.getItem('demo_mode') === 'true';

      if (isDemoMode) {
        // Demo mode: just update local state and localStorage
        try {
          const savedDemoTeam = localStorage.getItem('demo_team');
          if (savedDemoTeam) {
            const parsedDemoTeam = JSON.parse(savedDemoTeam);
            parsedDemoTeam.players = newPlayers;
            localStorage.setItem('demo_team', JSON.stringify(parsedDemoTeam));
            console.log("TeamContext: Updated players in localStorage for demo mode");
          }
        } catch (e) {
          console.error("TeamContext: Failed to update players in localStorage:", e);
        }

        // Update local state
        const updatedTeam = { ...currentTeam, players: newPlayers };
        setTeams(teams.map(team => team.id === updatedTeam.id ? updatedTeam : team));

        console.log("TeamContext: Players updated successfully in demo mode");
        return;
      }

      // For regular users, we need to save to the database
      if (!user) {
        console.log("TeamContext: No user available, cannot save players to database");
        throw new Error("No user logged in");
      }

      // Fetch existing players from database
      const existingPlayers = await teamService.fetchPlayers(currentTeam.id);
      const existingPlayerMap = new Map(existingPlayers.map(p => [p.id, p]));
      const newPlayerMap = new Map(newPlayers.map(p => [p.id, p]));
      
      const updatedPlayers: Player[] = [];
      
      // Update existing players (preserve their IDs!)
      for (const player of newPlayers) {
        if (player.id && existingPlayerMap.has(player.id)) {
          // This is an existing player - UPDATE it
          console.log(`Updating existing player: ${player.name} (${player.id})`);
          console.log(`Player teamRoles:`, player.teamRoles);
          const updatedPlayer = await teamService.updatePlayer(player);
          updatedPlayers.push(updatedPlayer);
        } else {
          // This is a new player - CREATE it
          console.log(`Creating new player: ${player.name}`);
          console.log(`New player teamRoles:`, player.teamRoles);
          const createdPlayer = await teamService.createPlayer(user.id, currentTeam.id, player);
          updatedPlayers.push(createdPlayer);
        }
      }
      
      // Delete players that are no longer in the list
      for (const existingPlayer of existingPlayers) {
        if (!newPlayerMap.has(existingPlayer.id)) {
          console.log(`Deleting removed player: ${existingPlayer.name} (${existingPlayer.id})`);
          await teamService.deletePlayer(existingPlayer.id);
        }
      }

      // Update local state with all the updated players
      const updatedTeam = { ...currentTeam, players: updatedPlayers };
      setTeams(teams.map(team => team.id === updatedTeam.id ? updatedTeam : team));

      console.log("TeamContext: Players updated successfully in database");
      
      // Force a refresh to ensure we have the latest data
      setTimeout(async () => {
        try {
          const freshPlayers = await teamService.fetchPlayers(currentTeam.id);
          const refreshedTeam = { ...currentTeam, players: freshPlayers };
          setTeams(teams.map(team => team.id === refreshedTeam.id ? refreshedTeam : team));
          console.log("TeamContext: Refreshed player data from database");
        } catch (error) {
          console.error("TeamContext: Error refreshing player data:", error);
        }
      }, 500);
    } catch (error) {
      console.error("TeamContext: Error setting players:", error);
      toast.error("Failed to update players. Please try again.");
      throw error;
    }
  };

  const addPlayer = async (player: Player) => {
    console.log(`Adding player: ${player.name}`);
    console.log("TeamContext: Demo mode flag:", localStorage.getItem('demo_mode'));

    // Update local state immediately
    const updatedPlayers = [...players, player];
    const updatedTeam = { ...currentTeam, players: updatedPlayers };

    // Update state directly to avoid recursive calls
    setTeams(teams.map(team => team.id === updatedTeam.id ? updatedTeam : team));

    // Check if we're in demo mode
    const isDemoMode = localStorage.getItem('demo_mode') === 'true';
    if (isDemoMode) {
      console.log("TeamContext: Demo mode detected, simulating player save");

      // In demo mode, just simulate the save without actually saving to database
      // This allows users to go through the motions and see how it works
      toast.success(`Player "${player.name}" added successfully (Demo Mode - changes not permanently saved)`);
      return; // Exit early for demo mode
    }

    // For non-demo mode, we need a user to save to Supabase
    if (!user) {
      console.log("TeamContext: No user available for non-demo mode, skipping Supabase save");
      return;
    }

    try {
      await teamService.createPlayer(user.id, currentTeam.id, player);
      toast.success(`Player "${player.name}" added successfully`);
    } catch (error) {
      console.error("Error adding player:", error);
      toast.error("Failed to add player");

      // Revert on error
      const revertedPlayers = players.filter(p => p.id !== player.id);
      const revertedTeam = { ...currentTeam, players: revertedPlayers };
      setTeams(teams.map(team => team.id === revertedTeam.id ? revertedTeam : team));
    }
  };

  const updatePlayer = async (player: Player) => {
    console.log(`Updating player: ${player.name}`);
    console.log("TeamContext: Demo mode flag:", localStorage.getItem('demo_mode'));

    // Update local state immediately
    const updatedPlayers = players.map(p => p.id === player.id ? player : p);
    const updatedTeam = { ...currentTeam, players: updatedPlayers };

    // Update state directly to avoid recursive calls
    setTeams(teams.map(team => team.id === updatedTeam.id ? updatedTeam : team));

    // Check if we're in demo mode
    const isDemoMode = localStorage.getItem('demo_mode') === 'true';
    if (isDemoMode) {
      console.log("TeamContext: Demo mode detected, simulating player update");

      // In demo mode, just simulate the update without actually saving to database
      toast.success(`Player "${player.name}" updated successfully (Demo Mode - changes not permanently saved)`);
      return; // Exit early for demo mode
    }

    // For non-demo mode, we need a user to save to Supabase
    if (!user) {
      console.log("TeamContext: No user available for non-demo mode, skipping Supabase save");
      return;
    }

    try {
      const updatedPlayer = await teamService.updatePlayer(player);
      
      // Refresh player data from database to ensure we have the latest
      setTimeout(async () => {
        try {
          const freshPlayers = await teamService.fetchPlayers(currentTeam.id);
          const refreshedTeam = { ...currentTeam, players: freshPlayers };
          setTeams(teams.map(team => team.id === refreshedTeam.id ? refreshedTeam : team));
          console.log("TeamContext: Refreshed player data after individual update");
        } catch (error) {
          console.error("TeamContext: Error refreshing after update:", error);
        }
      }, 300);
      
      toast.success(`Player "${player.name}" updated successfully`);
    } catch (error) {
      console.error("Error updating player:", error);
      toast.error("Failed to update player");

      // Revert on error
      try {
        const previousPlayers = await teamService.fetchPlayers(currentTeam.id);
        const revertedTeam = { ...currentTeam, players: previousPlayers };
        setTeams(teams.map(team => team.id === revertedTeam.id ? revertedTeam : team));
      } catch (revertError) {
        console.error("Error reverting player changes:", revertError);
      }
    }
  };

  const removePlayer = async (id: string) => {
    console.log(`Removing player with ID: ${id}`);
    console.log("TeamContext: Demo mode flag:", localStorage.getItem('demo_mode'));

    // Update local state immediately
    const updatedPlayers = players.filter(player => player.id !== id);
    const updatedTeam = { ...currentTeam, players: updatedPlayers };

    // Update state directly to avoid recursive calls
    setTeams(teams.map(team => team.id === updatedTeam.id ? updatedTeam : team));

    // Check if we're in demo mode
    const isDemoMode = localStorage.getItem('demo_mode') === 'true';
    if (isDemoMode) {
      console.log("TeamContext: Demo mode detected, simulating player removal");

      // In demo mode, just simulate the removal without actually deleting from database
      toast.success("Player removed successfully (Demo Mode - changes not permanently saved)");
      return; // Exit early for demo mode
    }

    // For non-demo mode, we need a user to save to Supabase
    if (!user) {
      console.log("TeamContext: No user available for non-demo mode, skipping Supabase save");
      return;
    }

    try {
      await teamService.deletePlayer(id);
      toast.success("Player removed successfully");
    } catch (error) {
      console.error("Error removing player:", error);
      toast.error("Failed to remove player");

      // Revert on error
      try {
        const previousPlayers = await teamService.fetchPlayers(currentTeam.id);
        const revertedTeam = { ...currentTeam, players: previousPlayers };
        setTeams(teams.map(team => team.id === revertedTeam.id ? revertedTeam : team));
      } catch (revertError) {
        console.error("Error reverting player deletion:", revertError);
      }
    }
  };

  // Lineup management functions for the current team
  const setLineups = async (newLineups: Lineup[]) => {
    // Update local state immediately
    const updatedTeam = { ...currentTeam, lineups: newLineups };

    // For other users, use the normal updateTeam function
    await updateTeam(updatedTeam);
  };

  const addLineup = async (lineup: Lineup): Promise<Lineup> => {
    console.log("TeamContext: Adding lineup:", lineup.name);
    console.log("TeamContext: Current user:", user);
    console.log("TeamContext: Demo mode flag:", localStorage.getItem('demo_mode'));

    // Validate demo mode operation
    try {
      validateDemoModeOperation('create', {
        allowWrite: true, // Demo mode can create lineups, but they're temporary
        customMessage: 'Demo Mode: Lineup created for demonstration. Changes are temporary.'
      });
      showDemoModeWarning('Demo Mode: This lineup is temporary and will not be permanently saved.');
    } catch (error) {
      toast.error(error.message);
      throw error;
    }

    // Generate a proper ID if one doesn't exist
    if (!lineup.id || lineup.id === "") {
      lineup.id = generateId();
    }

    // No need to save to localStorage as we're using the database
    console.log("TeamContext: Adding lineup with ID:", lineup.id);

    // Update local state immediately
    const updatedLineups = [...lineups, lineup];
    const updatedTeam = { ...currentTeam, lineups: updatedLineups };

    // Update state directly to avoid recursive calls
    setTeams(teams.map(team => team.id === updatedTeam.id ? updatedTeam : team));

    // Use consistent demo mode detection with the rest of the app
    const accountType = getAccountType(user?.email);
    const isDemoMode = accountType === 'demo';

    console.log("TeamContext: Account type:", accountType, "isDemoMode:", isDemoMode);

    if (isDemoMode) {
      console.log("TeamContext: Demo mode detected, simulating lineup creation");

      // In demo mode, allow more than 3 lineups but show a notice
      if (updatedLineups.length > 6) {
        console.log("TeamContext: Demo mode has many lineups, showing notice");

        // Just show a notice but don't delete lineups
        toast.info("Demo mode: You have many lineups! In a real account, you can have unlimited lineups.", {
          duration: 4000
        });
      }

      // In demo mode, just simulate the save without actually saving to database
      toast.success(`Lineup "${lineup.name}" created successfully (Demo Mode - not permanently saved)`);
      return lineup; // Return lineup as-is for demo mode
    }

    // For non-demo mode, we need a user to save to Supabase
    if (!user) {
      console.log("TeamContext: No user available for non-demo mode, skipping Supabase save");
      return lineup;
    }

    // Validate that we have a current team with a valid ID
    if (!currentTeam || !currentTeam.id) {
      console.error("TeamContext: No current team or team ID available, cannot save lineup");
      toast.error("No team selected. Please select a team first.");
      return lineup;
    }

    try {
      // Create lineup in database and get the real ID
      const createdLineup = await teamService.createLineup(user.id, currentTeam.id, lineup);
      
      // Update the lineup with the real database ID
      const lineupWithRealId = { ...lineup, id: createdLineup.id };
      
      // Update local state with the real ID
      const updatedLineupsWithRealId = lineups.map(l => 
        l.id === lineup.id ? lineupWithRealId : l
      );
      const updatedTeamWithRealId = { ...currentTeam, lineups: updatedLineupsWithRealId };
      setTeams(teams.map(team => team.id === updatedTeamWithRealId.id ? updatedTeamWithRealId : team));
      
      toast.success(`Lineup "${lineup.name}" created successfully`);
      
      // Return the lineup with real ID for navigation
      return lineupWithRealId;
    } catch (error: any) {
      console.error("Error creating lineup:", error);
      
      // Provide specific error messages based on error type
      let errorMessage = "Failed to create lineup";
      if (error?.message?.includes("duplicate key")) {
        errorMessage = "A lineup with this name already exists. Please choose a different name.";
      } else if (error?.message?.includes("column") && error?.message?.includes("does not exist")) {
        errorMessage = "Database configuration error. Please contact support.";
      } else if (error?.message?.includes("permission")) {
        errorMessage = "Permission denied. Please check your account status.";
      } else if (error?.message?.includes("network")) {
        errorMessage = "Network error. Please check your connection and try again.";
      } else if (error?.message) {
        errorMessage = `Error: ${error.message}`;
      }
      
      toast.error(errorMessage, {
        duration: 5000,
        description: "Check your connection and try again. Contact support if the problem persists."
      });

      // Revert on error, but don't call updateTeam to avoid recursion
      const revertedLineups = lineups.filter(l => l.id !== lineup.id);
      const revertedTeam = { ...currentTeam, lineups: revertedLineups };
      setTeams(teams.map(team => team.id === revertedTeam.id ? revertedTeam : team));
      
      throw error;
    }
  };

  // Create a debounced version of the lineup update function
  const updateLineupDebounced = (() => {
    const pendingUpdates = new Map<string, NodeJS.Timeout>();

    return (lineup: Lineup): Promise<void> => {
      return new Promise((resolve) => {
        // Cancel any pending update for this lineup
        if (pendingUpdates.has(lineup.id)) {
          clearTimeout(pendingUpdates.get(lineup.id));
          pendingUpdates.delete(lineup.id);
        }

        // For immediate updates from EditInning, check for a special flag
        const isImmediate = sessionStorage.getItem(`lineup_${lineup.id}`) !== null;

        if (isImmediate) {
          console.log("TeamContext: Immediate update requested for lineup:", lineup.id);
          // Execute update immediately
          pendingUpdates.delete(lineup.id);
          _updateLineup(lineup).then(resolve);
        } else {
          // Schedule a new update with debounce
          const timeoutId = setTimeout(() => {
            pendingUpdates.delete(lineup.id);
            _updateLineup(lineup).then(resolve);
          }, 300); // 300ms debounce time

          pendingUpdates.set(lineup.id, timeoutId);
        }
      });
    };
  })();

  // The actual update function (private implementation)
  const _updateLineup = async (lineup: Lineup) => {
    console.log("TeamContext: Updating lineup:", lineup.name);

    // Check if an update is already in progress, but with a timeout check
    const updateInProgress = localStorage.getItem(`updating_lineup_${lineup.id}`);
    const updateTimestamp = localStorage.getItem(`updating_lineup_timestamp_${lineup.id}`);

    if (updateInProgress && updateTimestamp) {
      const timeSinceUpdate = Date.now() - parseInt(updateTimestamp);
      // If update has been in progress for more than 30 seconds, clear it and proceed
      if (timeSinceUpdate > 30000) {
        console.log(`TeamContext: Update flag expired for lineup ${lineup.id}, clearing and proceeding`);
        localStorage.removeItem(`updating_lineup_${lineup.id}`);
        localStorage.removeItem(`updating_lineup_timestamp_${lineup.id}`);
      } else {
        console.log(`TeamContext: Update already in progress for lineup ${lineup.id} (${timeSinceUpdate}ms ago), skipping`);
        return;
      }
    }

    // Set the update flag with timestamp
    localStorage.setItem(`updating_lineup_${lineup.id}`, 'true');
    localStorage.setItem(`updating_lineup_timestamp_${lineup.id}`, Date.now().toString());

    try {
      // Make sure lineup has an ID
      if (!lineup.id || lineup.id === "") {
        lineup.id = generateId();
        console.warn("TeamContext: Lineup had no ID, generated one:", lineup.id);
      }

      // Update local state immediately
      console.log("TeamContext: Updating local state with lineup:", {
        id: lineup.id,
        name: lineup.name,
        inningsCount: lineup.innings?.length,
        firstInningPositions: lineup.innings?.[0]?.positions
      });

      const updatedLineups = lineups.map(l => l.id === lineup.id ? lineup : l);
      const updatedTeam = { ...currentTeam, lineups: updatedLineups };

      // Update local state directly to avoid recursive calls
      setTeams(teams.map(team => team.id === updatedTeam.id ? updatedTeam : team));

      console.log("TeamContext: Local state updated, lineup count:", updatedLineups.length);

      // Demo mode now uses the database just like regular mode
      // Determine account type using the imported getAccountType function
      const accountType = getAccountType(user?.email);
      const isDemoMode = accountType === 'demo';

      if (isDemoMode) {
        console.log("TeamContext: Demo mode detected, simulating lineup update");

        // In demo mode, just simulate the update without actually saving to database
        toast.success(`Lineup "${lineup.name}" updated successfully (Demo Mode - changes not permanently saved)`);

        // Clear the update flag after a short delay
        setTimeout(() => {
          localStorage.removeItem(`updating_lineup_${lineup.id}`);
        }, 500);

        return; // Exit early for demo mode
      }

      if (!user) {
        // Clear the update flag
        localStorage.removeItem(`updating_lineup_${lineup.id}`);
        return; // Skip Supabase in demo mode
      }

      // Validate that we have a current team with a valid ID
      if (!currentTeam || !currentTeam.id) {
        console.error("TeamContext: No current team or team ID available, cannot update lineup");
        localStorage.removeItem(`updating_lineup_${lineup.id}`);
        toast.error("No team selected. Please select a team first.");
        return;
      }

      console.log("TeamContext: About to call teamService.updateLineup with:", {
        id: lineup.id,
        name: lineup.name,
        rotationSettings: lineup.rotationSettings
      });

      await teamService.updateLineup(lineup);
      console.log("TeamContext: Database update completed successfully");

      // Verify the lineup is still in local state after database save
      const verifyLineup = lineups.find(l => l.id === lineup.id);
      if (verifyLineup) {
        console.log("TeamContext: Lineup verified in local state after DB save:", {
          id: verifyLineup.id,
          inningsCount: verifyLineup.innings?.length,
          firstInningPositions: verifyLineup.innings?.[0]?.positions
        });
      } else {
        console.error("TeamContext: Lineup NOT found in local state after DB save!");
      }

      // Clear the flags after successful update
      localStorage.removeItem(`updating_lineup_${lineup.id}`);
      localStorage.removeItem(`updating_lineup_timestamp_${lineup.id}`);

      // Only show success message if this is an immediate update (from EditInning)
      const isImmediate = sessionStorage.getItem(`lineup_${lineup.id}`) !== null;
      if (isImmediate) {
        toast.success(`Lineup "${lineup.name}" saved successfully`);
        // Clear the immediate flag
        sessionStorage.removeItem(`lineup_${lineup.id}`);
      }

      console.log("TeamContext: Lineup update completed successfully");
    } catch (error) {
      console.error("Error updating lineup:", error);
      console.error("Error details:", {
        message: error.message,
        stack: error.stack,
        lineupId: lineup.id,
        lineupName: lineup.name
      });

      // Only show the error message once
      const errorShown = localStorage.getItem(`error_shown_lineup_${lineup.id}`);
      if (!errorShown) {
        // Show more specific error message
        const errorMessage = error.message?.includes('not authenticated')
          ? "Authentication error - please try signing in again"
          : error.message?.includes('network')
          ? "Network error - please check your connection"
          : `Failed to update lineup: ${error.message || 'Unknown error'}`;

        toast.error(errorMessage);
        localStorage.setItem(`error_shown_lineup_${lineup.id}`, 'true');

        // Clear the error flag after 5 seconds to allow future error messages
        setTimeout(() => {
          localStorage.removeItem(`error_shown_lineup_${lineup.id}`);
        }, 5000);
      }

      // Clear the update flags
      localStorage.removeItem(`updating_lineup_${lineup.id}`);
      localStorage.removeItem(`updating_lineup_timestamp_${lineup.id}`);

      // Revert on error, but don't call updateTeam to avoid recursion
      try {
        if (currentTeam && currentTeam.id) {
          const previousLineups = await teamService.fetchLineups(currentTeam.id);
          // Update state directly
          const revertedTeam = { ...currentTeam, lineups: previousLineups };
          setTeams(teams.map(team => team.id === revertedTeam.id ? revertedTeam : team));
        }
      } catch (revertError) {
        console.error("Error reverting lineup changes:", revertError);
      }
    }
  };

  // Public update function that can use immediate or debounced updates
  const updateLineup = (lineup: Lineup): Promise<void> => {
    console.log("TeamContext: updateLineup called for lineup:", lineup.id);

    // Store a timestamp to track how long the update takes
    try {
      localStorage.setItem(`lineup_update_start_${lineup.id}`, Date.now().toString());
    } catch (e) {
      console.error("TeamContext: Failed to store update timestamp:", e);
    }

    // Check if this is a manual save or quick roster update that needs immediate processing
    const isImmediate = sessionStorage.getItem(`immediate_update_${lineup.id}`) !== null;
    
    if (isImmediate) {
      console.log("TeamContext: Immediate update requested, skipping debounce");
      sessionStorage.removeItem(`immediate_update_${lineup.id}`);
      return _updateLineup(lineup).then(() => {
        // Log completion time
        try {
          const startTime = localStorage.getItem(`lineup_update_start_${lineup.id}`);
          if (startTime) {
            const duration = Date.now() - parseInt(startTime);
            console.log(`TeamContext: Immediate lineup update completed in ${duration}ms`);
            localStorage.removeItem(`lineup_update_start_${lineup.id}`);
          }
        } catch (e) {
          console.error("TeamContext: Failed to log update duration:", e);
        }
      });
    }

    return updateLineupDebounced(lineup).then(() => {
      // Log completion time
      try {
        const startTime = localStorage.getItem(`lineup_update_start_${lineup.id}`);
        if (startTime) {
          const duration = Date.now() - parseInt(startTime);
          console.log(`TeamContext: Debounced lineup update completed in ${duration}ms`);
          localStorage.removeItem(`lineup_update_start_${lineup.id}`);
        }
      } catch (e) {
        console.error("TeamContext: Error calculating update duration:", e);
      }
    });
  };

  const removeLineup = async (id: string) => {
    // Update local state immediately
    const updatedLineups = lineups.filter(lineup => lineup.id !== id);
    const updatedTeam = { ...currentTeam, lineups: updatedLineups };

    // Update state directly to avoid recursive calls
    setTeams(teams.map(team => team.id === updatedTeam.id ? updatedTeam : team));

    // Use consistent demo mode detection with the rest of the app
    const accountType = getAccountType(user?.email);
    const isDemoMode = accountType === 'demo';

    if (isDemoMode) {
      console.log("TeamContext: Demo mode detected, simulating lineup deletion");

      // In demo mode, just simulate the deletion without actually deleting from database
      toast.success("Lineup deleted successfully (Demo Mode - changes not permanently saved)");
      return; // Exit early for demo mode
    }

    if (!user) return; // Skip Supabase in demo mode

    try {
      await teamService.deleteLineup(id);
      toast.success("Lineup deleted successfully");
    } catch (error) {
      console.error("Error deleting lineup:", error);
      toast.error("Failed to delete lineup");

      // Revert on error, but don't call updateTeam to avoid recursion
      try {
        const previousLineups = await teamService.fetchLineups(currentTeam.id);
        // Update state directly
        const revertedTeam = { ...currentTeam, lineups: previousLineups };
        setTeams(teams.map(team => team.id === revertedTeam.id ? revertedTeam : team));
      } catch (revertError) {
        console.error("Error reverting lineup deletion:", revertError);
      }
    }
  };

  const getAvailablePlayers = (lineup: Lineup, attendanceData?: {[playerId: string]: boolean}) => {
    console.log("🔍 getAvailablePlayers called with lineup:", lineup?.name, "ID:", lineup?.id);
    console.log("🔍 Current players count:", players.length);

    // Use either the attendance from the lineup or the provided attendance data
    const attendance = attendanceData || lineup?.attendance;

    if (!attendance || Object.keys(attendance).length === 0) {
      console.warn("⚠️ No attendance data found for lineup, returning all players as fallback");
      return players;
    }

    console.log("📋 Attendance data:", attendance);
    console.log("📋 Attendance entries count:", Object.keys(attendance).length);

    // Check if we're in demo mode
    const isDemoMode = localStorage.getItem('demo_mode') === 'true';
    console.log("🎭 Demo mode:", isDemoMode);

    // Filter players based on attendance - this is the core logic
    const availablePlayers = players.filter(player => {
      const isAttending = attendance[player.id];
      console.log(`👤 Player ${player.name} (ID: ${player.id}) attendance: ${isAttending}`);
      return isAttending === true; // Explicitly check for true
    });

    console.log("✅ Available players after filtering:", availablePlayers.map(p => p.name));
    console.log("✅ Available players count:", availablePlayers.length);

    // Only use fallbacks in very specific cases to avoid the bug
    if (availablePlayers.length === 0) {
      console.warn("⚠️ No players marked as attending! This might indicate a data issue.");

      // Check if all attendance values are false (everyone marked absent)
      const allAbsent = Object.values(attendance).every(val => val === false);
      if (allAbsent) {
        console.warn("⚠️ All players marked as absent - this is likely intentional");
        return []; // Return empty array instead of all players
      }

      // Check if attendance data has mismatched player IDs
      const playerIds = players.map(p => p.id);
      const attendanceIds = Object.keys(attendance);
      const hasMatchingIds = attendanceIds.some(id => playerIds.includes(id));

      if (!hasMatchingIds) {
        console.warn("⚠️ No matching player IDs between attendance and current players - possible data mismatch");
        console.warn("⚠️ Player IDs:", playerIds);
        console.warn("⚠️ Attendance IDs:", attendanceIds);

        // In this case, we might need to return all players as a fallback
        // but only if we're in demo mode or there's a clear data issue
        if (isDemoMode) {
          console.warn("⚠️ Demo mode: returning all players due to ID mismatch");
          return players;
        }
      }

      console.warn("⚠️ Returning empty array - no attending players found");
      return [];
    }

    return availablePlayers;
  };

  const updateRotationRules = async (rules: RotationRules) => {
    // Update local state immediately
    const updatedTeam = { ...currentTeam, rotationRules: rules };

    // Update local state directly without calling updateTeam to avoid circular dependency
    const updatedTeams = teams.map(team =>
      team.id === updatedTeam.id ? updatedTeam : team
    );
    setTeams(updatedTeams);

    if (!user) return; // Skip Supabase in demo mode

    try {
      await teamService.updateRotationRules(currentTeam.id, rules);
      toast.success("Rotation rules updated successfully");
    } catch (error) {
      console.error("Error updating rotation rules:", error);
      toast.error("Failed to update rotation rules");

      // Revert on error - update local state directly
      try {
        const previousRules = await teamService.fetchRotationRules(currentTeam.id);
        if (previousRules) {
          const revertedTeam = { ...currentTeam, rotationRules: previousRules };
          const revertedTeams = teams.map(team =>
            team.id === revertedTeam.id ? revertedTeam : team
          );
          setTeams(revertedTeams);
        }
      } catch (revertError) {
        console.error("Error reverting rotation rules:", revertError);
      }
    }
  };

  // Demo cleanup function - removes only user-created data, preserves original demo data
  const cleanupDemoLineups = async () => {
    const isDemoMode = localStorage.getItem('demo_mode') === 'true';
    if (!isDemoMode) return;

    console.log("TeamContext: Cleaning up user-created demo data (preserving original demo data)");

    try {
      // Get the demo team ID
      const demoTeamId = localStorage.getItem('current_team_id') || '83bd9832-f5db-4c7d-b234-41fd38f90007';

      // Get all lineups for the demo team
      const { data: demoLineups, error } = await supabase
        .from('lineups')
        .select('id, name, created_at')
        .eq('team_id', demoTeamId)
        .order('created_at', { ascending: true }); // Oldest first

      if (error) {
        console.error("TeamContext: Error fetching demo lineups for cleanup:", error);
        return;
      }

      // The original demo lineups are the first 3 (oldest)
      // Delete any lineups beyond the first 3 (these are user-created)
      if (demoLineups && demoLineups.length > 3) {
        const lineupsToDelete = demoLineups.slice(3); // Keep first 3, delete the rest

        for (const lineup of lineupsToDelete) {
          console.log("TeamContext: Deleting user-created demo lineup:", lineup.name);
          const { error: deleteError } = await supabase
            .from('lineups')
            .delete()
            .eq('id', lineup.id);

          if (deleteError) {
            console.error("TeamContext: Error deleting user-created lineup:", deleteError);
          }
        }

        console.log(`TeamContext: Cleaned up ${lineupsToDelete.length} user-created lineups`);
      }

      // For players, we need a different approach since we can't easily distinguish original vs user-created
      // For now, let's not auto-delete players to avoid losing the original demo players
      // Users can manually manage the roster in demo mode

      console.log("TeamContext: Demo cleanup complete (original demo data preserved)");

    } catch (error) {
      console.error("TeamContext: Error during demo cleanup:", error);
    }
  };

  // Function to refresh team data (useful for Dashboard when returning from other pages)
  const refreshTeamData = async () => {
    if (!user?.id) {
      console.log("TeamContext: Cannot refresh - no user ID");
      return;
    }

    console.log("TeamContext: Refreshing team data for user:", user.id);
    setLoading(true);

    try {
      const refreshedTeams = await fetchTeamsOptimized(user.id);
      console.log(`TeamContext: Successfully refreshed ${refreshedTeams.length} teams`);
      setTeams(refreshedTeams);
      setCachedData(refreshedTeams); // Update cache with fresh data
    } catch (error) {
      console.error("TeamContext: Error refreshing team data:", error);
      toast.error("Failed to refresh team data");
    } finally {
      setLoading(false);
    }
  };

  // Default batting order functions
  const getDefaultBattingOrder = useCallback((): string[] | null => {
    if (!currentTeam || !currentTeam.defaultBattingOrder) {
      return null;
    }
    return currentTeam.defaultBattingOrder;
  }, [currentTeam]);

  const setDefaultBattingOrder = useCallback(async (playerIds: string[]): Promise<void> => {
    if (!currentTeamId) {
      throw new Error('No team selected');
    }

    console.log('Setting default batting order:', { playerIds, currentTeamId, userEmail: user?.email });

    const accountType = getAccountType(user?.email);
    
    // Validate demo mode - use correct function signature
    try {
      validateDemoModeOperation('write', {
        allowWrite: true, // Demo mode can set batting order locally
        customMessage: 'Demo Mode: Default batting order saved locally (not permanently saved)'
      });
    } catch (error) {
      console.log('Demo mode validation passed with warning:', error.message);
      showDemoModeWarning(error.message);
      // Continue with demo mode operation
    }

    try {
      if (accountType === 'demo') {
        // For demo mode, just update local state
        const updatedTeams = teams.map(team => 
          team.id === currentTeamId 
            ? { ...team, defaultBattingOrder: playerIds }
            : team
        );
        setTeams(updatedTeams);
        console.log('Demo: Default batting order saved locally');
      } else {
        // For real users, save to database
        console.log('Saving to database with user ID:', user?.id);
        console.log('Database update parameters:', {
          table: 'teams',
          updateData: { default_batting_order: playerIds },
          whereClause: { id: currentTeamId, user_id: user?.id }
        });

        const { data, error } = await supabase
          .from('teams')
          .update({ default_batting_order: playerIds })
          .eq('id', currentTeamId)
          .eq('user_id', user?.id)
          .select(); // Add select to get returned data

        if (error) {
          console.error('Database error saving default batting order:', error);
          console.error('Error details:', {
            message: error.message,
            code: error.code,
            hint: error.hint,
            details: error.details
          });
          throw new Error(`Database error: ${error.message}`);
        }

        console.log('Database update result:', data);

        if (!data || data.length === 0) {
          console.warn('No rows were updated. This might indicate a permission or ID mismatch issue.');
          console.warn('Check if team exists and user has permission to update it.');
        }

        // Update local state
        const updatedTeams = teams.map(team => 
          team.id === currentTeamId 
            ? { ...team, defaultBattingOrder: playerIds }
            : team
        );
        setTeams(updatedTeams);
        console.log('Default batting order saved to database successfully');
      }
    } catch (error) {
      console.error('Error setting default batting order:', error);
      console.error('Full error object:', error);
      throw error;
    }
  }, [currentTeamId, teams, setTeams, user]);

  // Demo lineups are now created only once during initial demo user setup in DemoLogin.tsx

  return (
    <TeamContext.Provider
      value={{
        teams,
        currentTeamId,
        setCurrentTeamId,
        addTeam,
        updateTeam,
        removeTeam,
        currentTeam,
        teamName,
        setTeamName,
        players,
        setPlayers,
        addPlayer,
        updatePlayer,
        removePlayer,
        lineups,
        setLineups,
        addLineup,
        updateLineup,
        removeLineup,
        getAvailablePlayers,
        rotationRules,
        updateRotationRules,
        cleanupDemoLineups,
        refreshTeamData,
        loading,
        getDefaultBattingOrder,
        setDefaultBattingOrder
      }}
    >
      {children}
    </TeamContext.Provider>
  );
};

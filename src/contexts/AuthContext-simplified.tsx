  // Simplified version of ensureUserProfile that doesn't timeout
  const ensureUserProfile = async (user: any) => {
    if (!user) return;

    try {
      console.log("AuthContext: Ensuring profile for user:", user.email);
      
      const isAdmin = user.email === '<EMAIL>';
      
      // Use upsert instead of checking first - this is faster and doesn't timeout
      const { error: upsertError } = await supabase
        .from('profiles')
        .upsert({
          id: user.id,
          email: user.email,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          is_admin: isAdmin
        }, {
          onConflict: 'id'
        });

      if (upsertError) {
        console.error("AuthContext: Error upserting profile:", upsertError);
      } else {
        console.log("AuthContext: Profile ensured for user");
      }

      // Also ensure they have a subscription record
      const { error: subError } = await supabase
        .from('subscriptions')
        .upsert({
          user_id: user.id,
          is_paid: false,
          tier: 'starter',
          team_limit: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        });

      if (subError) {
        console.error("AuthContext: Error upserting subscription:", subError);
      }
      
    } catch (error) {
      console.error("AuthContext: Error in ensureUserProfile:", error);
    }
  };
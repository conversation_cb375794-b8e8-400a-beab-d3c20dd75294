// Robust session persistence implementation
// Key improvements:
// 1. Single source of truth for auth state
// 2. Proper handling of all auth events
// 3. No premature state clearing
// 4. Better session restoration logic

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { User, Session } from '@supabase/supabase-js';

interface AuthState {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  isInitialized: boolean;
}

interface AuthContextType extends AuthState {
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  // ... other methods
}

const AuthContext = createContext<AuthContextType | null>(null);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    isLoading: true,
    isInitialized: false
  });

  // Initialize auth
  useEffect(() => {
    let mounted = true;

    const initializeAuth = async () => {
      try {
        console.log('AuthContext: Initializing auth...');
        
        // Get current session
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('AuthContext: Error getting session:', error);
          if (mounted) {
            setAuthState({
              user: null,
              session: null,
              isLoading: false,
              isInitialized: true
            });
          }
          return;
        }
        
        if (session) {
          console.log('AuthContext: Found existing session for:', session.user.email);
          if (mounted) {
            setAuthState({
              user: session.user,
              session,
              isLoading: false,
              isInitialized: true
            });
            // Mark that we have a valid session
            localStorage.setItem('auth_session_exists', 'true');
          }
        } else {
          console.log('AuthContext: No existing session');
          
          // Check if we had a session before (page refresh case)
          const hadSession = localStorage.getItem('auth_session_exists') === 'true';
          
          if (hadSession && mounted) {
            console.log('AuthContext: Had session before, waiting for restoration...');
            
            // Give Supabase time to restore session from storage
            setTimeout(async () => {
              if (!mounted) return;
              
              // Check one more time
              const { data: { session: restoredSession } } = await supabase.auth.getSession();
              
              if (restoredSession) {
                console.log('AuthContext: Session restored after wait');
                setAuthState({
                  user: restoredSession.user,
                  session: restoredSession,
                  isLoading: false,
                  isInitialized: true
                });
              } else {
                console.log('AuthContext: Session not restored, user needs to login');
                localStorage.removeItem('auth_session_exists');
                setAuthState({
                  user: null,
                  session: null,
                  isLoading: false,
                  isInitialized: true
                });
              }
            }, 2000); // Wait 2 seconds for session restoration
          } else {
            // No previous session
            if (mounted) {
              setAuthState({
                user: null,
                session: null,
                isLoading: false,
                isInitialized: true
              });
            }
          }
        }
      } catch (error) {
        console.error('AuthContext: Initialization error:', error);
        if (mounted) {
          setAuthState({
            user: null,
            session: null,
            isLoading: false,
            isInitialized: true
          });
        }
      }
    };

    // Set up auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return;
        
        console.log('AuthContext: Auth event:', event, 'Has session:', !!session);
        
        switch (event) {
          case 'INITIAL_SESSION':
            // Already handled in initializeAuth
            break;
            
          case 'SIGNED_IN':
            console.log('AuthContext: User signed in');
            localStorage.setItem('auth_session_exists', 'true');
            setAuthState({
              user: session!.user,
              session: session!,
              isLoading: false,
              isInitialized: true
            });
            break;
            
          case 'SIGNED_OUT':
            console.log('AuthContext: User signed out');
            localStorage.removeItem('auth_session_exists');
            // Clear all user data
            clearAllUserData();
            setAuthState({
              user: null,
              session: null,
              isLoading: false,
              isInitialized: true
            });
            break;
            
          case 'TOKEN_REFRESHED':
            console.log('AuthContext: Token refreshed');
            if (session) {
              setAuthState(prev => ({
                ...prev,
                session,
                user: session.user
              }));
            }
            break;
            
          case 'USER_UPDATED':
            console.log('AuthContext: User updated');
            if (session) {
              setAuthState(prev => ({
                ...prev,
                user: session.user
              }));
            }
            break;
        }
      }
    );

    // Initialize
    initializeAuth();

    // Cleanup
    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []);

  // Session refresh interval
  useEffect(() => {
    if (!authState.session) return;

    const interval = setInterval(async () => {
      console.log('AuthContext: Refreshing session...');
      const { error } = await supabase.auth.refreshSession();
      if (error) {
        console.error('AuthContext: Failed to refresh session:', error);
      }
    }, 10 * 60 * 1000); // Every 10 minutes

    return () => clearInterval(interval);
  }, [authState.session]);

  const clearAllUserData = () => {
    // Clear localStorage items
    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (
        key.includes('team') || 
        key.includes('lineup') || 
        key.includes('player') ||
        key === 'demo_mode' ||
        key === 'auth_bypass_payment'
      )) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach(key => localStorage.removeItem(key));
    
    // Clear sessionStorage
    sessionStorage.clear();
    
    // Clear team context if available
    if (window.clearTeamContext) {
      window.clearTeamContext();
    }
  };

  const signIn = async (email: string, password: string) => {
    // Implementation...
  };

  const signOut = async () => {
    await supabase.auth.signOut();
  };

  const value: AuthContextType = {
    ...authState,
    signIn,
    signOut,
    // ... other methods
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};
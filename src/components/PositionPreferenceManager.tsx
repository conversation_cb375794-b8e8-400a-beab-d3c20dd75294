import React, { useState } from 'react';
import { Player, PositionPreference, PositionPreferenceWithRank } from '@/contexts/TeamContext';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Star, Circle, X, RotateCcw, Grid3X3, List, HelpCircle, AlertTriangle, Zap, Target, Users, Settings } from 'lucide-react';
import { toast } from 'sonner';

interface PositionPreferenceManagerProps {
  player: Player;
  onPreferenceChange: (playerId: string, position: string, preference: PositionPreference | PositionPreferenceWithRank | null) => void;
  disabled?: boolean;
  compact?: boolean;
}

const POSITION_OPTIONS = [
  { key: 'pitcher', label: 'Pitcher', abbrev: 'P' },
  { key: 'catcher', label: 'Catcher', abbrev: 'C' },
  { key: 'firstBase', label: 'First Base', abbrev: '1B' },
  { key: 'secondBase', label: 'Second Base', abbrev: '2B' },
  { key: 'shortstop', label: 'Shortstop', abbrev: 'SS' },
  { key: 'thirdBase', label: 'Third Base', abbrev: '3B' },
  { key: 'leftField', label: 'Left Field', abbrev: 'LF' },
  { key: 'centerField', label: 'Center Field', abbrev: 'CF' },
  { key: 'rightField', label: 'Right Field', abbrev: 'RF' },
];

const PREFERENCE_LEVELS = [
  { value: 'preferred', label: 'Preferred', icon: Star, color: 'bg-green-100 text-green-800 border-green-200' },
  { value: 'secondary', label: 'Secondary', icon: Circle, color: 'bg-blue-100 text-blue-800 border-blue-200' },
  { value: 'avoid', label: 'Avoid', icon: X, color: 'bg-red-100 text-red-800 border-red-200' },
  { value: 'neutral', label: 'Neutral', icon: RotateCcw, color: 'bg-gray-100 text-gray-800 border-gray-200' },
];

// Quick preset configurations for common player types
const PLAYER_PRESETS = [
  {
    id: 'star-pitcher',
    name: 'Star Pitcher',
    icon: Zap,
    description: 'Preferred pitcher, secondary at 1B/OF',
    preferences: {
      pitcher: { level: 'preferred' as PositionPreference, rank: 1 },
      firstBase: { level: 'secondary' as PositionPreference },
      leftField: { level: 'secondary' as PositionPreference },
      rightField: { level: 'secondary' as PositionPreference }
    }
  },
  {
    id: 'star-catcher',
    name: 'Star Catcher',
    icon: Target,
    description: 'Preferred catcher, secondary at 1B',
    preferences: {
      catcher: { level: 'preferred' as PositionPreference, rank: 1 },
      firstBase: { level: 'secondary' as PositionPreference }
    }
  },
  {
    id: 'utility-player',
    name: 'Utility Player',
    icon: Users,
    description: 'Secondary at multiple infield positions',
    preferences: {
      firstBase: { level: 'secondary' as PositionPreference },
      secondBase: { level: 'secondary' as PositionPreference },
      thirdBase: { level: 'secondary' as PositionPreference },
      shortstop: { level: 'secondary' as PositionPreference }
    }
  },
  {
    id: 'outfield-specialist',
    name: 'Outfield Specialist',
    icon: Circle,
    description: 'Preferred in all outfield positions',
    preferences: {
      leftField: { level: 'preferred' as PositionPreference, rank: 1 },
      centerField: { level: 'preferred' as PositionPreference, rank: 2 },
      rightField: { level: 'preferred' as PositionPreference, rank: 3 }
    }
  }
];

export const PositionPreferenceManager: React.FC<PositionPreferenceManagerProps> = ({
  player,
  onPreferenceChange,
  disabled = false,
  compact = false
}) => {
  const [viewMode, setViewMode] = useState<'simple' | 'matrix'>('simple');
  const [showHelp, setShowHelp] = useState(false);

  // Helper function to normalize preference value
  const normalizePreference = (pref: PositionPreference | PositionPreferenceWithRank | undefined): {
    level: PositionPreference;
    rank?: number;
  } => {
    if (!pref) return { level: 'neutral' };
    if (typeof pref === 'string') return { level: pref };
    return pref;
  };

  // Helper function to get preference badge styling
  const getPreferenceBadge = (preference: PositionPreference | PositionPreferenceWithRank | undefined) => {
    const normalized = normalizePreference(preference);
    const config = PREFERENCE_LEVELS.find(p => p.value === normalized.level);
    if (!config || normalized.level === 'neutral') return null;

    return {
      ...config,
      rank: normalized.rank
    };
  };

  // Handle preference change with validation
  const handlePreferenceChange = (position: string, level: PositionPreference, rank?: number) => {
    if (disabled) return;

    let newPreference: PositionPreference | PositionPreferenceWithRank | null = null;
    
    if (level === 'neutral') {
      newPreference = null; // Remove preference entirely
    } else if (rank && rank > 0) {
      newPreference = { level, rank };
    } else {
      newPreference = level;
    }

    onPreferenceChange(player.id, position, newPreference);
    
    // Provide user feedback
    if (level === 'neutral') {
      toast.success(`Cleared ${position} preference for ${player.name}`);
    } else {
      const rankText = rank ? ` (Priority #${rank})` : '';
      toast.success(`Set ${player.name} ${position} to ${level}${rankText}`);
    }
  };

  // Bulk operations
  const clearAllPreferences = () => {
    POSITION_OPTIONS.forEach(pos => {
      onPreferenceChange(player.id, pos.key, null);
    });
    toast.success(`Cleared all preferences for ${player.name}`);
  };

  const setPreferredPositions = (positions: string[]) => {
    positions.forEach((pos, index) => {
      handlePreferenceChange(pos, 'preferred', index + 1);
    });
  };

  // Apply a preset configuration
  const applyPreset = (presetId: string) => {
    const preset = PLAYER_PRESETS.find(p => p.id === presetId);
    if (!preset) return;

    // Clear all existing preferences first
    clearAllPreferences();

    // Apply preset preferences
    Object.entries(preset.preferences).forEach(([position, pref]) => {
      if ('rank' in pref) {
        handlePreferenceChange(position, pref.level, pref.rank);
      } else {
        handlePreferenceChange(position, pref.level);
      }
    });

    toast.success(`Applied ${preset.name} preset to ${player.name}`);
  };

  // Validation and suggestions
  const getPreferenceAnalysis = () => {
    const preferences = player.positionPreferences || {};
    const preferred = POSITION_OPTIONS.filter(pos => {
      const pref = normalizePreference(preferences[pos.key as keyof typeof preferences]);
      return pref.level === 'preferred';
    });
    const avoided = POSITION_OPTIONS.filter(pos => {
      const pref = normalizePreference(preferences[pos.key as keyof typeof preferences]);
      return pref.level === 'avoid';
    });

    const warnings = [];
    if (preferred.length === 0) {
      warnings.push('No preferred positions set - player may not get optimal assignments');
    }
    if (avoided.length >= 7) {
      warnings.push('Too many avoided positions - may cause lineup generation issues');
    }
    if (preferred.length >= 8) {
      warnings.push('Too many preferred positions - consider using secondary preferences');
    }

    return { preferred, avoided, warnings };
  };

  const analysis = getPreferenceAnalysis();

  // Compact view for roster page - completely redesigned for better UX
  if (compact) {
    const hasPreferences = POSITION_OPTIONS.some(pos => {
      const pref = player.positionPreferences?.[pos.key as keyof typeof player.positionPreferences];
      return pref && pref !== 'neutral';
    });

    return (
      <div className="space-y-3">
        {/* Quick Preset Buttons */}
        <div className="flex flex-wrap gap-1">
          {PLAYER_PRESETS.map(preset => {
            const IconComponent = preset.icon;
            return (
              <Button
                key={preset.id}
                variant="outline"
                size="sm"
                className="h-7 text-xs px-2"
                onClick={() => applyPreset(preset.id)}
                disabled={disabled}
                title={preset.description}
              >
                <IconComponent className="w-3 h-3 mr-1" />
                {preset.name}
              </Button>
            );
          })}
        </div>

        {/* Current Preferences Summary */}
        {hasPreferences ? (
          <div className="flex flex-wrap gap-1">
            {POSITION_OPTIONS.map(pos => {
              const preference = player.positionPreferences?.[pos.key as keyof typeof player.positionPreferences];
              const badge = getPreferenceBadge(preference);
              if (!badge) return null;

              const IconComponent = badge.icon;
              return (
                <Badge key={pos.key} variant="outline" className={`text-xs ${badge.color}`}>
                  <IconComponent className="w-3 h-3 mr-1" />
                  {pos.abbrev}
                  {badge.rank && <span className="ml-1 font-bold">#{badge.rank}</span>}
                </Badge>
              );
            })}
          </div>
        ) : (
          <div className="text-xs text-gray-500 italic">No preferences set</div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-1">
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm" className="h-7 text-xs">
                <Settings className="w-3 h-3 mr-1" />
                Custom Setup
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl">
              <DialogHeader>
                <DialogTitle>Position Preferences - {player.name}</DialogTitle>
              </DialogHeader>
              <PositionPreferenceManager
                player={player}
                onPreferenceChange={onPreferenceChange}
                disabled={disabled}
                compact={false}
              />
            </DialogContent>
          </Dialog>

          {hasPreferences && (
            <Button
              variant="outline"
              size="sm"
              className="h-7 text-xs text-red-600 hover:text-red-700"
              onClick={clearAllPreferences}
              disabled={disabled}
            >
              <RotateCcw className="w-3 h-3 mr-1" />
              Clear
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Position Preferences - {player.name}</CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowHelp(true)}
            >
              <HelpCircle className="w-4 h-4 mr-1" />
              Help
            </Button>
            <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'simple' | 'matrix')}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="simple" className="text-xs">
                  <List className="w-3 h-3 mr-1" />
                  Simple
                </TabsTrigger>
                <TabsTrigger value="matrix" className="text-xs">
                  <Grid3X3 className="w-3 h-3 mr-1" />
                  Matrix
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>

        {/* Analysis and warnings */}
        {analysis.warnings.length > 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
            <div className="flex items-start">
              <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
              <div className="text-sm text-yellow-800">
                <div className="font-medium mb-1">Preference Suggestions:</div>
                <ul className="list-disc list-inside space-y-1">
                  {analysis.warnings.map((warning, index) => (
                    <li key={index}>{warning}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent>
        <Tabs value={viewMode} className="w-full">
          <TabsContent value="simple" className="space-y-4">
            {/* Simple view - position by position */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {POSITION_OPTIONS.map(position => {
                const currentPref = player.positionPreferences?.[position.key as keyof typeof player.positionPreferences];
                const normalized = normalizePreference(currentPref);
                
                return (
                  <div key={position.key} className="border rounded-lg p-3 space-y-2">
                    <div className="font-medium text-sm">{position.label}</div>
                    
                    {/* Current preference display */}
                    <div className="min-h-[24px]">
                      {(() => {
                        const badge = getPreferenceBadge(currentPref);
                        if (badge) {
                          const IconComponent = badge.icon;
                          return (
                            <Badge variant="outline" className={`text-xs ${badge.color}`}>
                              <IconComponent className="w-3 h-3 mr-1" />
                              {badge.label}
                              {badge.rank && <span className="ml-1 font-bold">#{badge.rank}</span>}
                            </Badge>
                          );
                        }
                        return <span className="text-xs text-gray-500">Neutral</span>;
                      })()}
                    </div>

                    {/* Preference controls */}
                    <div className="space-y-2">
                      <Select
                        value={normalized.level}
                        onValueChange={(value) => handlePreferenceChange(position.key, value as PositionPreference)}
                        disabled={disabled}
                      >
                        <SelectTrigger className="h-8 text-xs">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {PREFERENCE_LEVELS.map(level => {
                            const IconComponent = level.icon;
                            return (
                              <SelectItem key={level.value} value={level.value}>
                                <div className="flex items-center">
                                  <IconComponent className="w-3 h-3 mr-2" />
                                  {level.label}
                                </div>
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>

                      {/* Ranking for preferred/secondary */}
                      {(normalized.level === 'preferred' || normalized.level === 'secondary') && (
                        <Select
                          value={normalized.rank?.toString() || ''}
                          onValueChange={(value) => {
                            const rank = value ? parseInt(value) : undefined;
                            handlePreferenceChange(position.key, normalized.level, rank);
                          }}
                          disabled={disabled}
                        >
                          <SelectTrigger className="h-7 text-xs">
                            <SelectValue placeholder="Priority" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="">No Priority</SelectItem>
                            {[1, 2, 3, 4, 5].map(rank => (
                              <SelectItem key={rank} value={rank.toString()}>
                                Priority #{rank}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </TabsContent>

          <TabsContent value="matrix" className="space-y-4">
            {/* Matrix view - all positions in a grid */}
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-200">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-200 p-2 text-left text-sm font-medium">Position</th>
                    <th className="border border-gray-200 p-2 text-center text-sm font-medium">Current</th>
                    <th className="border border-gray-200 p-2 text-center text-sm font-medium">Preference</th>
                    <th className="border border-gray-200 p-2 text-center text-sm font-medium">Priority</th>
                    <th className="border border-gray-200 p-2 text-center text-sm font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {POSITION_OPTIONS.map(position => {
                    const currentPref = player.positionPreferences?.[position.key as keyof typeof player.positionPreferences];
                    const normalized = normalizePreference(currentPref);
                    const badge = getPreferenceBadge(currentPref);
                    
                    return (
                      <tr key={position.key} className="hover:bg-gray-50">
                        <td className="border border-gray-200 p-2 font-medium text-sm">
                          {position.label}
                        </td>
                        <td className="border border-gray-200 p-2 text-center">
                          {badge ? (
                            <Badge variant="outline" className={`text-xs ${badge.color}`}>
                              <badge.icon className="w-3 h-3 mr-1" />
                              {badge.label}
                              {badge.rank && <span className="ml-1 font-bold">#{badge.rank}</span>}
                            </Badge>
                          ) : (
                            <span className="text-xs text-gray-500">Neutral</span>
                          )}
                        </td>
                        <td className="border border-gray-200 p-2">
                          <Select
                            value={normalized.level}
                            onValueChange={(value) => handlePreferenceChange(position.key, value as PositionPreference)}
                            disabled={disabled}
                          >
                            <SelectTrigger className="h-8 text-xs">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {PREFERENCE_LEVELS.map(level => {
                                const IconComponent = level.icon;
                                return (
                                  <SelectItem key={level.value} value={level.value}>
                                    <div className="flex items-center">
                                      <IconComponent className="w-3 h-3 mr-2" />
                                      {level.label}
                                    </div>
                                  </SelectItem>
                                );
                              })}
                            </SelectContent>
                          </Select>
                        </td>
                        <td className="border border-gray-200 p-2">
                          {(normalized.level === 'preferred' || normalized.level === 'secondary') ? (
                            <Select
                              value={normalized.rank?.toString() || ''}
                              onValueChange={(value) => {
                                const rank = value ? parseInt(value) : undefined;
                                handlePreferenceChange(position.key, normalized.level, rank);
                              }}
                              disabled={disabled}
                            >
                              <SelectTrigger className="h-7 text-xs">
                                <SelectValue placeholder="None" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="">No Priority</SelectItem>
                                {[1, 2, 3, 4, 5].map(rank => (
                                  <SelectItem key={rank} value={rank.toString()}>
                                    #{rank}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          ) : (
                            <span className="text-xs text-gray-400">-</span>
                          )}
                        </td>
                        <td className="border border-gray-200 p-2 text-center">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                            onClick={() => handlePreferenceChange(position.key, 'neutral')}
                            disabled={disabled || normalized.level === 'neutral'}
                            title="Clear preference"
                          >
                            <RotateCcw className="w-3 h-3" />
                          </Button>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </TabsContent>
        </Tabs>

        {/* Quick Presets and Bulk Operations */}
        <div className="space-y-3 pt-4 border-t">
          {/* Player Type Presets */}
          <div>
            <h4 className="text-sm font-medium mb-2">Quick Player Presets:</h4>
            <div className="flex flex-wrap gap-2">
              {PLAYER_PRESETS.map(preset => {
                const IconComponent = preset.icon;
                return (
                  <Button
                    key={preset.id}
                    variant="outline"
                    size="sm"
                    onClick={() => applyPreset(preset.id)}
                    disabled={disabled}
                    title={preset.description}
                  >
                    <IconComponent className="w-4 h-4 mr-1" />
                    {preset.name}
                  </Button>
                );
              })}
            </div>
          </div>

          {/* Traditional Bulk Operations */}
          <div>
            <h4 className="text-sm font-medium mb-2">Other Actions:</h4>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={clearAllPreferences}
                disabled={disabled}
              >
                <RotateCcw className="w-4 h-4 mr-1" />
                Clear All Preferences
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setPreferredPositions(['pitcher', 'catcher'])}
                disabled={disabled}
              >
                <Star className="w-4 h-4 mr-1" />
                Set Battery Preferred
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setPreferredPositions(['leftField', 'centerField', 'rightField'])}
                disabled={disabled}
              >
                <Star className="w-4 h-4 mr-1" />
                Set Outfield Preferred
              </Button>
            </div>
          </div>
        </div>
      </CardContent>

      {/* Help Dialog */}
      <Dialog open={showHelp} onOpenChange={setShowHelp}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Position Preferences Help</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">Preference Levels:</h4>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <Star className="w-4 h-4 mr-2 text-green-600" />
                  <strong>Preferred:</strong> Player will be prioritized for these positions
                </li>
                <li className="flex items-center">
                  <Circle className="w-4 h-4 mr-2 text-blue-600" />
                  <strong>Secondary:</strong> Player will be considered after preferred players
                </li>
                <li className="flex items-center">
                  <X className="w-4 h-4 mr-2 text-red-600" />
                  <strong>Avoid:</strong> Player will only be assigned when necessary
                </li>
                <li className="flex items-center">
                  <RotateCcw className="w-4 h-4 mr-2 text-gray-600" />
                  <strong>Neutral:</strong> No preference (default)
                </li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Priority Rankings:</h4>
              <p>Within preferred and secondary levels, you can set priority rankings (1-5) to further refine assignments. Priority #1 is highest.</p>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Quick Presets:</h4>
              <ul className="space-y-2">
                {PLAYER_PRESETS.map(preset => {
                  const IconComponent = preset.icon;
                  return (
                    <li key={preset.id} className="flex items-center">
                      <IconComponent className="w-4 h-4 mr-2 text-blue-600" />
                      <strong>{preset.name}:</strong> <span className="ml-1">{preset.description}</span>
                    </li>
                  );
                })}
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">Best Practices:</h4>
              <ul className="list-disc list-inside space-y-1">
                <li>Use quick presets for common player types to save time</li>
                <li>Set 2-4 preferred positions per player for optimal results</li>
                <li>Use rankings when multiple players prefer the same position</li>
                <li>Avoid setting too many "avoid" preferences (max 3-4 per player)</li>
                <li>Preferences work alongside position restrictions and rotation rules</li>
              </ul>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default PositionPreferenceManager;

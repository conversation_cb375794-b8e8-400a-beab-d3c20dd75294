import { ReactNode } from "react";
import { Link, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { 
  LayoutDashboard, 
  Users, 
  CreditCard, 
  ClipboardList, 
  Settings, 
  LogOut,
  ChevronRight,
  Shield,
  Wrench
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils-enhanced";

interface AdminLayoutProps {
  children: ReactNode;
}

const AdminLayout = ({ children }: AdminLayoutProps) => {
  const { user, signOut } = useAuth();
  const location = useLocation();

  const navItems = [
    {
      name: "Dashboard",
      path: "/admin",
      icon: <LayoutDashboard className="h-5 w-5" />
    },
    {
      name: "Users",
      path: "/admin/users",
      icon: <Users className="h-5 w-5" />
    },
    {
      name: "Billing",
      path: "/admin/billing",
      icon: <CreditCard className="h-5 w-5" />
    },
    {
      name: "Teams",
      path: "/admin/teams",
      icon: <ClipboardList className="h-5 w-5" />
    },
    {
      name: "Audit Logs",
      path: "/admin/audit-logs",
      icon: <Shield className="h-5 w-5" />
    },
    {
      name: "Maintenance",
      path: "/admin/maintenance",
      icon: <Wrench className="h-5 w-5" />
    },
    {
      name: "Settings",
      path: "/admin/settings",
      icon: <Settings className="h-5 w-5" />
    }
  ];

  return (
    <div className="min-h-screen flex flex-col md:flex-row bg-gray-50">
      {/* Sidebar */}
      <div className="w-full md:w-64 bg-baseball-navy text-white flex flex-col">
        {/* Logo */}
        <div className="p-4 border-b border-baseball-navy-light">
          <Link to="/admin" className="flex items-center">
            <span className="text-xl font-bold">Diamond Admin</span>
          </Link>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-1">
          {navItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              className={cn(
                "flex items-center px-3 py-2 rounded-md transition-colors",
                location.pathname === item.path
                  ? "bg-baseball-green text-white"
                  : "text-gray-300 hover:bg-baseball-navy-light hover:text-white"
              )}
            >
              {item.icon}
              <span className="ml-3">{item.name}</span>
            </Link>
          ))}
        </nav>

        {/* User info */}
        <div className="p-4 border-t border-baseball-navy-light">
          <div className="flex items-center mb-4">
            <div className="w-8 h-8 rounded-full bg-baseball-green flex items-center justify-center text-white">
              {user?.email?.charAt(0).toUpperCase() || "U"}
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium">{user?.email}</p>
              <p className="text-xs text-gray-400">Administrator</p>
            </div>
          </div>
          <div className="space-y-2">
            <Link to="/dashboard" className="block">
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full border-gray-700 text-gray-300 hover:bg-baseball-navy-light justify-start"
              >
                <ChevronRight className="h-4 w-4 mr-2 rotate-180" />
                Exit Admin Panel
              </Button>
            </Link>
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full border-gray-700 text-gray-300 hover:bg-baseball-navy-light"
              onClick={() => signOut()}
            >
              <LogOut className="h-4 w-4 mr-2" />
              Sign Out
            </Button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col">
        {/* Breadcrumb */}
        <div className="bg-white border-b p-4">
          <div className="flex items-center text-sm text-gray-500">
            <Link to="/admin" className="hover:text-baseball-green">Admin</Link>
            {location.pathname !== "/admin" && (
              <>
                <ChevronRight className="h-4 w-4 mx-1" />
                <span className="text-gray-900 font-medium">
                  {navItems.find(item => item.path === location.pathname)?.name || "Page"}
                </span>
              </>
            )}
          </div>
        </div>

        {/* Page content */}
        <div className="flex-1 overflow-auto">
          {children}
        </div>
      </div>
    </div>
  );
};

export default AdminLayout;

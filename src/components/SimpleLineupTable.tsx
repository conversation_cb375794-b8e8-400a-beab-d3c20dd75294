import React from "react";
import { InningLineup } from "@/contexts/TeamContext";

interface LineupTableProps {
  lineup: {
    innings: InningLineup[];
    battingOrder?: string[];
  };
  maxInnings?: number;
}

const LineupTable: React.FC<LineupTableProps> = ({ lineup, maxInnings }) => {
  if (!lineup || !lineup.innings || lineup.innings.length === 0) {
    return <div>No lineup data available</div>;
  }

  const innings = maxInnings 
    ? lineup.innings.slice(0, maxInnings)
    : lineup.innings;

  // Base positions grouped by field area
  const positionGroups = [
    {
      name: "Battery",
      positions: [
        { id: "pitcher", label: "Pitcher" },
        { id: "catcher", label: "Catcher" },
      ]
    },
    {
      name: "Infield",
      positions: [
        { id: "firstBase", label: "1st Base" },
        { id: "secondBase", label: "2nd Base" },
        { id: "shortstop", label: "Shortstop" },
        { id: "thirdBase", label: "3rd Base" },
      ]
    },
    {
      name: "Outfield",
      positions: [
        { id: "leftField", label: "Left Field" },
        { id: "centerField", label: "Center Field" },
        { id: "rightField", label: "Right Field" },
      ]
    }
  ];
  

  // Calculate max bench players
  const maxBenchPlayers = Math.max(
    ...innings.map(inning => inning.positions.bench?.length || 0)
  );


  return (
    <div className="overflow-x-auto">
      <table className="min-w-full border-collapse">
        <thead>
          <tr className="bg-gray-100">
            <th className="border border-gray-300 px-3 py-2 text-left text-sm font-semibold w-32">
              Position
            </th>
            {innings.map((inning) => (
              <th
                key={inning.inning}
                className="border border-gray-300 px-3 py-2 text-center text-sm font-semibold min-w-[100px]"
              >
                Inning {inning.inning}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {positionGroups.map((group, groupIndex) => (
            <React.Fragment key={group.name}>
              {groupIndex > 0 && (
                <tr>
                  <td colSpan={innings.length + 1} className="h-2 bg-gray-50"></td>
                </tr>
              )}
              <tr>
                <td 
                  colSpan={innings.length + 1} 
                  className="bg-baseball-navy text-white px-3 py-1 text-xs font-semibold uppercase tracking-wide"
                >
                  {group.name}
                </td>
              </tr>
              {group.positions.map((position) => (
                <tr key={position.id} className="hover:bg-gray-50">
                  <td className="border border-gray-300 px-3 py-2 font-medium text-sm bg-gray-50">
                    {position.label}
                  </td>
                  {innings.map((inning) => {
                    const playerName = inning.positions[position.id as keyof typeof inning.positions] as string || "";
                    return (
                      <td
                        key={`${position.id}-${inning.inning}`}
                        className="border border-gray-300 px-3 py-2 text-sm text-center"
                      >
                        {playerName || "-"}
                      </td>
                    );
                  })}
                </tr>
              ))}
            </React.Fragment>
          ))}
          
          {maxBenchPlayers > 0 && (
            <>
              <tr>
                <td colSpan={innings.length + 1} className="h-2 bg-gray-50"></td>
              </tr>
              <tr>
                <td 
                  colSpan={innings.length + 1} 
                  className="bg-baseball-navy text-white px-3 py-1 text-xs font-semibold uppercase tracking-wide"
                >
                  Bench
                </td>
              </tr>
              {Array.from({ length: maxBenchPlayers }, (_, i) => (
                <tr key={`bench${i}`} className="hover:bg-gray-50">
                  <td className="border border-gray-300 px-3 py-2 font-medium text-sm bg-gray-50">
                    Bench {i + 1}
                  </td>
                  {innings.map((inning) => {
                    const playerName = inning.positions.bench?.[i] || "";
                    return (
                      <td
                        key={`bench${i}-${inning.inning}`}
                        className="border border-gray-300 px-3 py-2 text-sm text-center"
                      >
                        {playerName || "-"}
                      </td>
                    );
                  })}
                </tr>
              ))}
            </>
          )}
        </tbody>
      </table>
    </div>
  );
};

export default LineupTable;
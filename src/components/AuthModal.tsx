
import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { toast } from "sonner";

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultMode?: "login" | "signup";
}

const AuthModal = ({ isOpen, onClose, defaultMode = "login" }: AuthModalProps) => {
  const [mode, setMode] = useState<"login" | "signup">(defaultMode);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const { signIn, signUp } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      if (mode === "login") {
        try {
          const result = await signIn(email, password);
          if (result.error) throw result.error;
          toast.success("Successfully logged in!");
          onClose();
        } catch (error: any) {
          console.error("Login error:", error);

          // Handle specific error messages
          if (error.message?.includes('Email not confirmed')) {
            toast.error("Email not confirmed. Please check your inbox for a verification email.");
          } else if (error.message?.includes('Invalid login credentials')) {
            toast.error("Invalid email or password. Please try again.");
          } else {
            toast.error(error.message || "Authentication failed");
          }
        }
      } else {
        try {
          const result = await signUp(email, password);
          if (result.error) throw result.error;
          toast.success("Registration successful! Please check your email for verification.");
          onClose();
        } catch (error: any) {
          console.error("Signup error:", error);

          if (error.message?.includes('already registered')) {
            toast.error("This email is already registered. Please try logging in instead.");
          } else {
            toast.error(error.message || "Registration failed");
          }
        }
      }
    } catch (error: any) {
      console.error("General auth error:", error);
      toast.error(error.message || "Authentication failed");
    } finally {
      setIsLoading(false);
    }
  };

  const toggleMode = () => {
    setMode(mode === "login" ? "signup" : "login");
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {mode === "login" ? "Sign in to your account" : "Create a new account"}
          </DialogTitle>
          <DialogDescription>
            {mode === "login"
              ? "Enter your email and password to access your dashboard."
              : "Sign up to get started with Baseball Lineup Guru."}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="••••••••"
              required
            />
          </div>

          <Button
            type="submit"
            variant="baseball"
            className="w-full"
            disabled={isLoading}
          >
            {isLoading
              ? "Processing..."
              : mode === "login"
                ? "Sign In"
                : "Create Account"}
          </Button>
        </form>

        <div className="text-center mt-4">
          <button
            type="button"
            onClick={toggleMode}
            className="text-sm text-baseball-navy hover:underline"
          >
            {mode === "login"
              ? "Don't have an account? Sign up"
              : "Already have an account? Sign in"}
          </button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AuthModal;

import React, { useState } from 'react';
import { Player, PlayerRating, PositionRatings } from '@/contexts/TeamContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { 
  Star, 
  HelpCircle, 
  RotateCcw, 
  Grid3X3, 
  List,
  Trophy,
  Target,
  Users,
  TrendingUp,
  BookOpen,
  Settings
} from 'lucide-react';
import { toast } from 'sonner';

interface PlayerRatingManagerProps {
  player: Player;
  onRatingChange: (playerId: string, position: string, rating: PlayerRating | null) => void;
  onStarPlayerChange: (playerId: string, isStarPlayer: boolean) => void;
  disabled?: boolean;
  compact?: boolean;
  competitiveMode?: boolean;
}

// Position groups for better organization
const POSITION_GROUPS = {
  battery: {
    name: 'Pitcher & Catcher Positions',
    positions: [
      { key: 'pitcher', label: 'Pitcher' },
      { key: 'catcher', label: 'Catcher' }
    ]
  },
  infield: {
    name: 'Infield Positions',
    positions: [
      { key: 'firstBase', label: 'First Base' },
      { key: 'secondBase', label: 'Second Base' },
      { key: 'thirdBase', label: 'Third Base' },
      { key: 'shortstop', label: 'Shortstop' }
    ]
  },
  outfield: {
    name: 'Outfield Positions',
    positions: [
      { key: 'leftField', label: 'Left Field' },
      { key: 'centerField', label: 'Center Field' },
      { key: 'rightField', label: 'Right Field' }
    ]
  },
  utility: {
    name: 'Utility Positions',
    positions: [
      { key: 'leftCenter', label: 'Left Center' },
      { key: 'rightCenter', label: 'Right Center' },
      { key: 'middleInfield', label: 'Middle Infield' }
    ]
  }
};

// Flatten positions for backward compatibility
const POSITION_OPTIONS = Object.values(POSITION_GROUPS).flatMap(group => group.positions);

const RATING_LEVELS = [
  { value: 5, label: 'Elite', icon: Trophy, color: 'text-yellow-600 bg-yellow-50 border-yellow-200', description: 'Best' },
  { value: 4, label: 'Strong', icon: Target, color: 'text-green-600 bg-green-50 border-green-200', description: 'Good' },
  { value: 3, label: 'Average', icon: Users, color: 'text-blue-600 bg-blue-50 border-blue-200', description: 'Solid' },
  { value: 2, label: 'Developing', icon: TrendingUp, color: 'text-orange-600 bg-orange-50 border-orange-200', description: 'Learning' },
  { value: 1, label: 'Beginner', icon: BookOpen, color: 'text-purple-600 bg-purple-50 border-purple-200', description: 'New' }
];

export const PlayerRatingManager: React.FC<PlayerRatingManagerProps> = ({
  player,
  onRatingChange,
  onStarPlayerChange,
  disabled = false,
  compact = false,
  competitiveMode = false
}) => {
  const keyPositions = ['pitcher', 'catcher', 'shortstop']; // Fixed key positions
  const [viewMode, setViewMode] = useState<'simple' | 'matrix'>('simple');
  const [showHelp, setShowHelp] = useState(false);

  // Handle rating change
  const handleRatingChange = (position: string, rating: PlayerRating | null) => {
    if (disabled) return;

    onRatingChange(player.id, position, rating);
    
    if (rating === null) {
      toast.success(`Cleared ${position} rating for ${player.name}`);
    } else {
      const ratingInfo = RATING_LEVELS.find(r => r.value === rating);
      toast.success(`Set ${player.name} ${position} to ${ratingInfo?.label} (${rating})`);
    }
  };

  // Handle star player toggle
  const handleStarPlayerChange = (isStarPlayer: boolean) => {
    if (disabled) return;
    
    onStarPlayerChange(player.id, isStarPlayer);
    toast.success(`${player.name} ${isStarPlayer ? 'marked as' : 'removed as'} star player`);
  };

  // Bulk operations
  const clearAllRatings = () => {
    POSITION_OPTIONS.forEach(pos => {
      onRatingChange(player.id, pos.key, null);
    });
    toast.success(`Cleared all ratings for ${player.name}`);
  };

  const setElitePositions = (positions: string[]) => {
    positions.forEach(pos => {
      handleRatingChange(pos, 5);
    });
  };

  // Get rating badge info
  const getRatingBadge = (rating: PlayerRating | undefined) => {
    if (!rating) return null;
    const ratingInfo = RATING_LEVELS.find(r => r.value === rating);
    return ratingInfo;
  };

  // Analysis and suggestions
  const getAnalysis = () => {
    const ratings = player.positionRatings || {};
    const ratedPositions = Object.keys(ratings).length;
    const elitePositions = Object.values(ratings).filter(r => r === 5).length;
    const warnings = [];

    if (ratedPositions === 0) {
      warnings.push('No position ratings set - player may not get optimal assignments in competitive mode');
    }
    if (elitePositions > 3) {
      warnings.push('Consider limiting elite (5) ratings to 2-3 positions for more realistic assignments');
    }
    if (player.isStarPlayer && elitePositions === 0) {
      warnings.push('Star player should have at least one elite (5) rating');
    }

    return { warnings };
  };

  const analysis = getAnalysis();

  if (compact) {
    return (
      <div className="space-y-4">
        {/* Star Player Toggle */}
        <div className="flex items-center justify-center gap-2 p-3 bg-gray-50 rounded-lg">
          <Switch
            checked={player.isStarPlayer || false}
            onCheckedChange={handleStarPlayerChange}
            disabled={disabled}
            id="star-player-compact"
          />
          <Label htmlFor="star-player-compact" className="text-sm font-medium cursor-pointer">
            {player.isStarPlayer ? (
              <span className="text-yellow-600 flex items-center gap-1">
                <Star className="w-4 h-4" />
                Star Player
              </span>
            ) : (
              <span className="text-gray-600">Star Player</span>
            )}
          </Label>
        </div>

        {/* Quick Rating for Key Positions */}
        <div className="space-y-3">
          <div className="text-sm font-medium text-center text-gray-700">
            {competitiveMode && (
              <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-300 mb-2">
                <Trophy className="w-3 h-3 mr-1" />
                COMPETITIVE MODE
              </Badge>
            )}
            <div>Key Position Ratings</div>
          </div>
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
            {keyPositions.slice(0, 6).map(position => {
              const positionInfo = POSITION_OPTIONS.find(p => p.key === position);
              const rating = player.positionRatings?.[position as keyof PositionRatings];
              const isKeyPosition = keyPositions.includes(position);

              return (
                <div 
                  key={position} 
                  className={`p-2 rounded-lg border overflow-hidden ${isKeyPosition ? 'border-yellow-300 bg-yellow-50' : 'border-gray-200 bg-white'}`}
                >
                  <div className={`text-xs font-medium mb-1.5 truncate flex items-center justify-center gap-1 ${isKeyPosition ? 'text-yellow-800' : 'text-gray-700'}`}>
                    <span className="truncate">{positionInfo?.label}</span>
                    {isKeyPosition && competitiveMode && (
                      <Star className="w-3 h-3 text-yellow-600 flex-shrink-0" />
                    )}
                  </div>
                  <div className="flex justify-center gap-0.5">
                    {[1, 2, 3, 4, 5].map(value => (
                      <button
                        key={value}
                        onClick={() => handleRatingChange(position, rating === value ? null : value)}
                        disabled={disabled}
                        className={`w-6 h-6 rounded-full text-[10px] font-bold transition-all flex items-center justify-center ${
                          rating === value
                            ? isKeyPosition && competitiveMode
                              ? 'bg-yellow-500 text-white ring-1 ring-yellow-300'
                              : 'bg-blue-500 text-white ring-1 ring-blue-300'
                            : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                        } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                        title={`Rate ${value}/5${isKeyPosition && competitiveMode ? ' (Key Position)' : ''}`}
                      >
                        {value}
                      </button>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Show if player has other ratings */}
        {player.positionRatings && Object.keys(player.positionRatings).length > 3 && (
          <div className="text-xs text-center text-blue-600">
            + {Object.keys(player.positionRatings).length - 3} more positions rated
          </div>
        )}
        
        {/* Custom Setup Button */}
        <div className="flex justify-center">
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 text-xs">
                <Settings className="w-3 h-3 mr-1" />
                Full Rating Setup
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
              <DialogHeader className="flex-shrink-0">
                <DialogTitle>Player Skills & Ratings - {player.name}</DialogTitle>
              </DialogHeader>
              <div className="overflow-y-auto flex-1 px-1">
                <PlayerRatingManager
                  player={player}
                  onRatingChange={onRatingChange}
                  onStarPlayerChange={onStarPlayerChange}
                  disabled={disabled}
                  compact={false}
                  competitiveMode={competitiveMode}
                />
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <CardTitle className="text-lg">{player.name} - Player Ratings</CardTitle>
            {player.isStarPlayer && (
              <Badge variant="outline" className="text-yellow-600 bg-yellow-50 border-yellow-200">
                <Star className="w-4 h-4 mr-1" />
                Star Player
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowHelp(true)}
            >
              <HelpCircle className="w-4 h-4 mr-1" />
              Help
            </Button>
            <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'simple' | 'matrix')}>
              <TabsList className="grid w-[200px] grid-cols-2">
                <TabsTrigger value="simple" className="text-xs">
                  <List className="w-3 h-3 mr-1" />
                  Simple
                </TabsTrigger>
                <TabsTrigger value="matrix" className="text-xs">
                  <Grid3X3 className="w-3 h-3 mr-1" />
                  Matrix
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>

        {/* Star Player Toggle */}
        <div className="flex items-center space-x-2 pt-2 border-t">
          <Switch
            id="star-player"
            checked={player.isStarPlayer || false}
            onCheckedChange={handleStarPlayerChange}
            disabled={disabled}
          />
          <Label htmlFor="star-player" className="text-sm cursor-pointer flex-1">
            <span className="block">Designate as Star Player</span>
            <span className="text-xs text-gray-500">Gets priority in competitive mode</span>
          </Label>
        </div>

        {/* Analysis and warnings */}
        {analysis.warnings.length > 0 && (
          <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mt-3">
            <div className="flex items-start gap-2">
              <HelpCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-blue-800 flex-1">
                <div className="font-medium mb-1">Suggestions:</div>
                <ul className="list-disc list-inside space-y-1">
                  {analysis.warnings.map((warning, index) => (
                    <li key={index} className="break-words">{warning}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent>
        <Tabs value={viewMode} className="w-full">
          <TabsContent value="simple" className="space-y-6">
            {/* Simple view - grouped by position type */}
            {Object.entries(POSITION_GROUPS).map(([groupKey, group]) => (
              <div key={groupKey} className="space-y-3">
                <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                  {groupKey === 'battery' && <Target className="w-4 h-4 text-red-600" />}
                  {groupKey === 'infield' && <Users className="w-4 h-4 text-blue-600" />}
                  {groupKey === 'outfield' && <Users className="w-4 h-4 text-green-600" />}
                  {groupKey === 'utility' && <Grid3X3 className="w-4 h-4 text-purple-600" />}
                  {group.name}
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                  {group.positions.map(position => {
                const currentRating = player.positionRatings?.[position.key as keyof PositionRatings];
                const badge = getRatingBadge(currentRating);
                
                return (
                  <div key={position.key} className="border rounded-lg p-3 bg-white hover:shadow-sm transition-shadow overflow-hidden">
                    <div className="font-medium text-sm border-b pb-2 truncate">{position.label}</div>
                    
                    {/* Current rating display */}
                    <div className="min-h-[32px] flex items-center justify-center my-2">
                      {badge ? (
                        <Badge variant="outline" className={`${badge.color} text-xs`}>
                          <badge.icon className="w-3 h-3 mr-1 flex-shrink-0" />
                          <span className="truncate">{badge.label}</span>
                        </Badge>
                      ) : (
                        <span className="text-xs text-gray-400 italic">Not rated</span>
                      )}
                    </div>

                    {/* Rating buttons */}
                    <div className="space-y-2">
                      <div className="flex justify-center gap-1">
                        {RATING_LEVELS.map(level => (
                          <button
                            key={level.value}
                            className={`w-7 h-7 rounded-full text-xs font-bold transition-all flex items-center justify-center ${
                              currentRating === level.value 
                                ? 'bg-blue-500 text-white ring-2 ring-blue-300' 
                                : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                            } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                            onClick={() => handleRatingChange(position.key, level.value)}
                            disabled={disabled}
                            title={`${level.label} - ${level.description}`}
                          >
                            {level.value}
                          </button>
                        ))}
                      </div>
                      
                      {/* Clear button */}
                      {currentRating && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="w-full h-8 text-xs"
                          onClick={() => handleRatingChange(position.key, null)}
                          disabled={disabled}
                        >
                          <RotateCcw className="w-3 h-3 mr-1" />
                          Clear Rating
                        </Button>
                      )}
                    </div>
                  </div>
                );
              })}
                </div>
              </div>
            ))}
          </TabsContent>

          <TabsContent value="matrix" className="space-y-4">
            {/* Matrix view - all positions in a table */}
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-200">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-200 p-2 text-left text-sm font-medium">Position</th>
                    <th className="border border-gray-200 p-2 text-center text-sm font-medium">Current Rating</th>
                    <th className="border border-gray-200 p-2 text-center text-sm font-medium">Rating Scale</th>
                    <th className="border border-gray-200 p-2 text-center text-sm font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {POSITION_OPTIONS.map(position => {
                    const currentRating = player.positionRatings?.[position.key as keyof PositionRatings];
                    const badge = getRatingBadge(currentRating);

                    return (
                      <tr key={position.key} className="hover:bg-gray-50">
                        <td className="border border-gray-200 p-2 font-medium text-sm">
                          {position.label}
                        </td>
                        <td className="border border-gray-200 p-2 text-center">
                          {badge ? (
                            <Badge variant="outline" className={`${badge.color} text-xs`}>
                              <badge.icon className="w-3 h-3 mr-1" />
                              {badge.label}
                            </Badge>
                          ) : (
                            <span className="text-xs text-gray-500">-</span>
                          )}
                        </td>
                        <td className="border border-gray-200 p-2">
                          <div className="flex justify-center gap-1">
                            {RATING_LEVELS.map(level => (
                              <Button
                                key={level.value}
                                variant={currentRating === level.value ? "default" : "outline"}
                                size="sm"
                                className={`h-7 w-8 text-xs p-0 ${currentRating === level.value ? '' : level.color}`}
                                onClick={() => handleRatingChange(position.key, level.value)}
                                disabled={disabled}
                                title={`${level.label} - ${level.description}`}
                              >
                                {level.value}
                              </Button>
                            ))}
                          </div>
                        </td>
                        <td className="border border-gray-200 p-2 text-center">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                            onClick={() => handleRatingChange(position.key, null)}
                            disabled={disabled || !currentRating}
                            title="Clear rating"
                          >
                            <RotateCcw className="w-3 h-3" />
                          </Button>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </TabsContent>
        </Tabs>

        {/* Bulk operations */}
        <div className="flex flex-wrap gap-2 pt-4 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={clearAllRatings}
            disabled={disabled}
          >
            <RotateCcw className="w-4 h-4 mr-1" />
            Clear All Ratings
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setElitePositions(['pitcher', 'catcher'])}
            disabled={disabled}
          >
            <Trophy className="w-4 h-4 mr-1" />
            Elite Pitcher & Catcher
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setElitePositions(['leftField', 'centerField', 'rightField'])}
            disabled={disabled}
          >
            <Trophy className="w-4 h-4 mr-1" />
            Elite Outfield
          </Button>
        </div>
      </CardContent>

      {/* Help Dialog */}
      <Dialog open={showHelp} onOpenChange={setShowHelp}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Player Rating System Help</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">Rating Scale (1-5):</h4>
              <ul className="space-y-2">
                {RATING_LEVELS.map(level => (
                  <li key={level.value} className="flex items-center">
                    <level.icon className={`w-4 h-4 mr-2 ${level.color.split(' ')[0]}`} />
                    <strong>{level.value} - {level.label}:</strong>
                    <span className="ml-1">{level.description}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">Star Player Designation:</h4>
              <p>Mark your best players as "Star Players" for competitive mode. Star players get:</p>
              <ul className="list-disc list-inside space-y-1 mt-2">
                <li>Priority placement in key positions (pitcher, catcher, shortstop)</li>
                <li>Extended time in preferred positions before rotation</li>
                <li>Strategic advantages in competitive lineup generation</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">Best Practices:</h4>
              <ul className="list-disc list-inside space-y-1">
                <li>Rate players honestly - this helps create balanced, effective lineups</li>
                <li>Use Elite (5) ratings sparingly - reserve for truly exceptional positions</li>
                <li>Star player designation should reflect overall team impact, not just skill</li>
                <li>Ratings work alongside position restrictions and rotation rules</li>
                <li>No rating (blank) means the player is neutral for that position</li>
              </ul>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default PlayerRatingManager;

import React from 'react';

interface BasicSelectProps {
  options: { value: string; label: string }[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

const BasicSelect: React.FC<BasicSelectProps> = ({
  options,
  value,
  onChange,
  placeholder = "Select an option"
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onChange(e.target.value);
  };

  return (
    <select
      value={value || ""}
      onChange={handleChange}
      className="w-full h-9 rounded-md border border-input bg-white px-3 py-1 text-sm"
    >
      <option value="">{placeholder}</option>
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
};

export default BasicSelect;

import React from 'react';
import { Search, X } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useDebouncedSearch } from '@/hooks/useDebounce';
import { Player } from '@/contexts/TeamContext';
import { cn } from '@/lib/utils-enhanced';

interface PlayerSearchProps {
  players: Player[];
  onResultsChange?: (results: Player[]) => void;
  placeholder?: string;
  className?: string;
}

export function PlayerSearch({ 
  players, 
  onResultsChange,
  placeholder = "Search players...",
  className 
}: PlayerSearchProps) {
  const { query, setQuery, results, isSearching } = useDebouncedSearch(
    players,
    (player, searchQuery) => {
      const lowerQuery = searchQuery.toLowerCase();
      return (
        player.name.toLowerCase().includes(lowerQuery) ||
        // Search by position preferences
        Object.entries(player.teamRoles || {}).some(([position, role]) => 
          role !== 'avoid' && position.toLowerCase().includes(lowerQuery)
        )
      );
    },
    300 // 300ms debounce
  );

  // Notify parent of results change
  React.useEffect(() => {
    onResultsChange?.(results);
  }, [results, onResultsChange]);

  return (
    <div className={cn("relative", className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder={placeholder}
          className="pl-9 pr-9"
        />
        {query && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setQuery('')}
            className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7 p-0"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>
      
      {isSearching && (
        <div className="absolute right-3 top-1/2 -translate-y-1/2">
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-baseball-navy border-t-transparent" />
        </div>
      )}
      
      {query && !isSearching && (
        <div className="absolute top-full left-0 right-0 mt-1 text-sm text-gray-600">
          Found {results.length} player{results.length !== 1 ? 's' : ''}
        </div>
      )}
    </div>
  );
}

// Enhanced player select with search
export function PlayerSelectWithSearch({
  players,
  value,
  onChange,
  placeholder = "Select a player",
  className
}: {
  players: Player[];
  value: string;
  onChange: (playerId: string) => void;
  placeholder?: string;
  className?: string;
}) {
  const [open, setOpen] = React.useState(false);
  const [filteredPlayers, setFilteredPlayers] = React.useState(players);

  return (
    <div className={cn("space-y-2", className)}>
      <PlayerSearch
        players={players}
        onResultsChange={setFilteredPlayers}
        placeholder="Type to search..."
      />
      
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-baseball-navy"
      >
        <option value="">{placeholder}</option>
        {filteredPlayers.map(player => (
          <option key={player.id} value={player.id}>
            {player.name}
          </option>
        ))}
      </select>
    </div>
  );
}
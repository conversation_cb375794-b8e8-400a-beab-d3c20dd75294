import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

interface DemoModeButtonProps {
  size?: "default" | "sm" | "lg" | "icon" | null | undefined;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" | "baseball" | "baseballOutline" | null | undefined;
  className?: string;
}

const DemoModeButton = ({ size = "default", variant = "outline", className = "" }: DemoModeButtonProps) => {
  const navigate = useNavigate();

  const handleDemoClick = async () => {
    // Note: We don't automatically clean up demo data here anymore
    // to preserve the original demo lineups and players.
    // Users can manually reset if needed, or we can add a specific reset button.

    // Clear any existing auth state
    localStorage.clear();
    sessionStorage.clear();

    // Navigate to demo login page which will handle the authentication
    navigate('/demo-login');
  };

  return (
    <Button
      size={size}
      variant={variant}
      onClick={handleDemoClick}
      className={className}
    >
      Try Demo Mode
    </Button>
  );
};

export default DemoModeButton;

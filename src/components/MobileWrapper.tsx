import React from 'react';
import { useLocation } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';
import { BottomNav } from './mobile/BottomNav';
import { useAuth } from '@/contexts/AuthContext';

interface MobileWrapperProps {
  children: React.ReactNode;
}

export const MobileWrapper: React.FC<MobileWrapperProps> = ({
  children
}) => {
  const isMobile = useIsMobile();
  const location = useLocation();
  const { user } = useAuth();
  
  // Don't show mobile UI on auth pages
  const isAuthPage = ['/sign-in', '/signup', '/reset-password', '/'].includes(location.pathname);
  
  // Only hide bottom nav on pages where it would interfere with the user's task
  const hideBottomNav = [
    // Multi-step lineup creation flow
    '/set-lineup-attendance',
    '/set-first-inning', 
    '/batting-order',
    '/basic-set-first-inning',
    '/simple-set-first-inning',
    
    // Editing flows
    '/edit-inning',
    '/batch-lineup-generation',
    
    // Auth pages
    '/sign-in',
    '/signup', 
    '/reset-password',
    '/',
    
    // Admin pages (they have their own navigation)
    '/admin',
  ].includes(location.pathname) || 
  location.pathname.startsWith('/view-lineup/') || // Individual lineup viewing/editing
  location.pathname.startsWith('/edit-inning/') ||
  location.pathname.startsWith('/admin/') ||
  isAuthPage;
  
  
  return (
    <>
      {/* Just render children - pages handle their own layout */}
      {children}
      
      {/* Add padding to pages when bottom nav is shown */}
      {isMobile && !hideBottomNav && user && (
        <div className="h-16" /> 
      )}
      
      {/* Mobile Bottom Navigation - Global */}
      {isMobile && !hideBottomNav && user && (
        <BottomNav />
      )}
    </>
  );
};
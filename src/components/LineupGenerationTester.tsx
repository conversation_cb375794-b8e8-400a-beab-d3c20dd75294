import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { generateLineupFromFirstInning, rotatePlayersForNextInning } from '../lib/utils-enhanced';
import type { Player, InningLineup, LineupRules } from '../lib/utils-enhanced';

// Create mock players for testing
const createMockPlayer = (name: string, id: string, canPitch: boolean = false): Player => ({
  id,
  name,
  team_id: 'test-team',
  attendance: 'present' as const,
  positionRestrictions: canPitch ? [] : ['pitcher'], // Only some can pitch
  positionPreferences: {
    pitcher: canPitch ? 'neutral' : 'avoid',
    catcher: 'neutral',
    firstBase: 'neutral',
    secondBase: 'neutral',
    thirdBase: 'neutral',
    shortstop: 'neutral',
    leftField: 'neutral',
    centerField: 'neutral',
    rightField: 'neutral'
  }
});

export const LineupGenerationTester: React.FC = () => {
  const [testResults, setTestResults] = useState<any>({});
  const [isRunning, setIsRunning] = useState(false);

  const runComparisonTest = () => {
    setIsRunning(true);
    const results: any = {};

    // Create test players - make players 1, 5, 9, 10 able to pitch
    const players = [
      createMockPlayer('Player1', 'p1', true),  // Can pitch
      createMockPlayer('Player2', 'p2', false),
      createMockPlayer('Player3', 'p3', false),
      createMockPlayer('Player4', 'p4', false),
      createMockPlayer('Player5', 'p5', true),  // Can pitch
      createMockPlayer('Player6', 'p6', false),
      createMockPlayer('Player7', 'p7', false),
      createMockPlayer('Player8', 'p8', false),
      createMockPlayer('Player9', 'p9', true),  // Can pitch
      createMockPlayer('Player10', 'p10', true), // Can pitch (bench)
      createMockPlayer('Player11', 'p11', false),
      createMockPlayer('Player12', 'p12', false),
    ];

    // Create first inning
    const firstInning: InningLineup = {
      inning: 1,
      positions: {
        pitcher: 'Player1',
        catcher: 'Player2',
        firstBase: 'Player3',
        secondBase: 'Player4',
        thirdBase: 'Player5',
        shortstop: 'Player6',
        leftField: 'Player7',
        centerField: 'Player8',
        rightField: 'Player9',
        bench: ['Player10', 'Player11', 'Player12']
      }
    };

    // Test configuration
    const rules: LineupRules = {
      rotateLineupEvery: 2,      // Rotate lineup every 2 innings
      rotatePitcherEvery: 1,     // Rotate pitcher every inning
      limitBenchTime: true,
      maxConsecutiveBenchInnings: 2,
      allowPitcherRotation: true,
      allowCatcherRotation: false,
      respectPositionLockouts: true,
      equalPlayingTime: true,
      _randomSeed: 42
    };

    // Test 1: Main lineup generation (generateLineupFromFirstInning)
    try {
      const mainLineup = generateLineupFromFirstInning(firstInning, players, 6, rules);
      
      results.mainGeneration = {
        success: true,
        innings: mainLineup.map((inning, idx) => {
          const pitcherChanged = idx > 0 && inning.positions.pitcher !== mainLineup[idx-1].positions.pitcher;
          const playersRotated = idx > 0 ? 
            Object.keys(inning.positions).filter(pos => {
              if (pos === 'bench') return false;
              const currPlayer = (inning.positions as any)[pos];
              const prevPlayer = (mainLineup[idx-1].positions as any)[pos];
              return currPlayer !== prevPlayer;
            }).length : 0;
          
          return {
            inning: inning.inning,
            pitcher: inning.positions.pitcher,
            pitcherChanged,
            playersRotated,
            bench: inning.positions.bench
          };
        })
      };
    } catch (error) {
      results.mainGeneration = { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }

    // Test 2: ViewLineup "aggressive rotation" approach (simulated)
    try {
      const aggressiveLineup = [firstInning];
      
      for (let i = 1; i < 6; i++) {
        const prevInning = aggressiveLineup[i - 1];
        const newInning: InningLineup = {
          inning: i + 1,
          positions: {
            pitcher: '',
            catcher: '',
            firstBase: '',
            secondBase: '',
            thirdBase: '',
            shortstop: '',
            leftField: '',
            centerField: '',
            rightField: '',
            bench: []
          }
        };
        
        // Simulate aggressive rotation
        const allPlayers = [...Object.values(prevInning.positions).filter(p => typeof p === 'string'), ...prevInning.positions.bench].filter(Boolean) as string[];
        const benchPlayers = prevInning.positions.bench;
        const fieldPlayers = allPlayers.filter(p => !benchPlayers.includes(p));
        
        // Prioritize bench players
        const rotationOrder = [...benchPlayers, ...fieldPlayers];
        const assignedPlayers = new Set<string>();
        
        // Assign positions
        const positions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'thirdBase', 'shortstop', 'leftField', 'centerField', 'rightField'];
        for (const pos of positions) {
          for (const playerName of rotationOrder) {
            if (!assignedPlayers.has(playerName)) {
              const player = players.find(p => p.name === playerName);
              if (pos === 'pitcher' && player?.positionRestrictions?.includes('pitcher')) {
                continue; // Can't play pitcher
              }
              (newInning.positions as any)[pos] = playerName;
              assignedPlayers.add(playerName);
              break;
            }
          }
        }
        
        // Remaining to bench
        newInning.positions.bench = allPlayers.filter(p => !assignedPlayers.has(p));
        aggressiveLineup.push(newInning);
      }
      
      results.aggressiveRotation = {
        success: true,
        innings: aggressiveLineup.map((inning, idx) => {
          const pitcherChanged = idx > 0 && inning.positions.pitcher !== aggressiveLineup[idx-1].positions.pitcher;
          const playersRotated = idx > 0 ? 
            Object.keys(inning.positions).filter(pos => {
              if (pos === 'bench') return false;
              const currPlayer = (inning.positions as any)[pos];
              const prevPlayer = (aggressiveLineup[idx-1].positions as any)[pos];
              return currPlayer !== prevPlayer;
            }).length : 0;
          
          return {
            inning: inning.inning,
            pitcher: inning.positions.pitcher,
            pitcherChanged,
            playersRotated,
            bench: inning.positions.bench
          };
        })
      };
    } catch (error) {
      results.aggressiveRotation = { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }

    setTestResults(results);
    setIsRunning(false);
  };

  const renderApproachResults = (approach: string, data: any) => {
    if (!data.success) {
      return (
        <div className="text-red-500">
          Error: {data.error}
        </div>
      );
    }

    return (
      <div className="space-y-2">
        <table className="w-full text-sm">
          <thead>
            <tr className="border-b">
              <th className="text-left">Inning</th>
              <th className="text-left">Pitcher</th>
              <th className="text-center">Changed?</th>
              <th className="text-center">Rotations</th>
              <th className="text-left">Bench</th>
            </tr>
          </thead>
          <tbody>
            {data.innings.map((inning: any) => (
              <tr key={inning.inning} className="border-b">
                <td>{inning.inning}</td>
                <td>{inning.pitcher}</td>
                <td className="text-center">{inning.pitcherChanged ? '✅' : '-'}</td>
                <td className="text-center">{inning.playersRotated || '-'}</td>
                <td>{inning.bench.join(', ')}</td>
              </tr>
            ))}
          </tbody>
        </table>
        
        <div className="mt-2 p-2 bg-gray-100 rounded">
          <div>Pitcher changes: {data.innings.filter((i: any) => i.pitcherChanged).length}</div>
          <div>Innings with rotation: {data.innings.filter((i: any) => i.playersRotated > 0).length}</div>
        </div>
      </div>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Lineup Generation Comparison Test</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="p-4 bg-blue-50 rounded">
            <p className="font-semibold">Test Configuration:</p>
            <ul className="text-sm mt-2">
              <li>• Rotate lineup every 2 innings</li>
              <li>• Rotate pitcher every inning</li>
              <li>• Players 1, 5, 9, 10 can pitch</li>
              <li>• Respect position restrictions</li>
            </ul>
          </div>

          <Button 
            onClick={runComparisonTest} 
            disabled={isRunning}
            className="w-full"
          >
            {isRunning ? 'Running Tests...' : 'Run Comparison Test'}
          </Button>

          {Object.keys(testResults).length > 0 && (
            <div className="space-y-6">
              <Card className="p-4">
                <h3 className="font-semibold mb-2">Main Generation (rotatePlayersForNextInning)</h3>
                {renderApproachResults('main', testResults.mainGeneration)}
              </Card>

              <Card className="p-4">
                <h3 className="font-semibold mb-2">Aggressive Rotation (ViewLineup approach)</h3>
                {renderApproachResults('aggressive', testResults.aggressiveRotation)}
              </Card>

              <Card className="p-4 bg-green-50">
                <h3 className="font-semibold mb-2">Analysis</h3>
                <div className="text-sm space-y-1">
                  {testResults.mainGeneration?.success && testResults.aggressiveRotation?.success && (
                    <>
                      <p>Main generation pitcher changes: {testResults.mainGeneration.innings.filter((i: any) => i.pitcherChanged).length}</p>
                      <p>Aggressive rotation pitcher changes: {testResults.aggressiveRotation.innings.filter((i: any) => i.pitcherChanged).length}</p>
                      <p className="mt-2 font-semibold">
                        {testResults.aggressiveRotation.innings.filter((i: any) => i.pitcherChanged).length > 
                         testResults.mainGeneration.innings.filter((i: any) => i.pitcherChanged).length
                          ? '✅ Aggressive rotation performs more pitcher changes as configured'
                          : '❌ Main generation should rotate pitcher every inning but does not'}
                      </p>
                    </>
                  )}
                </div>
              </Card>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
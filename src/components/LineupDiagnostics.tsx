import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertCircle, ChevronDown, ChevronUp, Info } from "lucide-react";
import { Player } from "@/lib/utils-enhanced";

interface LineupDiagnosticsProps {
  players: Player[];
  lineup: any; // InningLineup type
  inningNumber: number;
}

const LineupDiagnostics = ({ players, lineup, inningNumber }: LineupDiagnosticsProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Analyze why certain players aren't playing
  const analyzePlayerUsage = () => {
    const analysis: {
      notPlaying: Array<{
        player: Player;
        positions: string[];
        reasons: string[];
      }>;
      playingLess: Array<{
        player: Player;
        fieldInnings: number;
        benchInnings: number;
      }>;
    } = { notPlaying: [], playingLess: [] };

    // Get all players on bench
    const benchPlayers = lineup.positions.bench || [];
    
    benchPlayers.forEach((playerName: string) => {
      const player = players.find(p => p.name === playerName);
      if (!player) return;

      const reasons: string[] = [];
      const eligiblePositions: string[] = [];

      // Check what positions they're marked for
      Object.entries(player.teamRoles || {}).forEach(([position, role]) => {
        if (role === 'primary' || role === 'go-to') {
          eligiblePositions.push(`${position} (Primary)`);
        } else if (role === 'capable' || role === 'in the mix') {
          eligiblePositions.push(`${position} (In the Mix)`);
        } else if (role === 'fill-in' || role === 'emergency') {
          eligiblePositions.push(`${position} (Emergency)`);
        }
      });

      // Determine reasons they're not playing
      if (eligiblePositions.length === 0) {
        reasons.push("No positions assigned");
      } else {
        reasons.push("Lower priority due to playing time balance");
        
        // Check if they're marked for occupied positions
        const occupiedByPrimary = eligiblePositions.some(pos => {
          const posName = pos.split(' ')[0];
          const currentPlayer = lineup.positions[posName];
          if (currentPlayer) {
            const occupant = players.find(p => p.name === currentPlayer);
            return occupant?.teamRoles?.[posName] === 'primary' || 
                   occupant?.teamRoles?.[posName] === 'go-to';
          }
          return false;
        });
        
        if (occupiedByPrimary) {
          reasons.push("Positions occupied by primary players");
        }
      }

      analysis.notPlaying.push({
        player,
        positions: eligiblePositions,
        reasons
      });
    });

    return analysis;
  };

  const analysis = analyzePlayerUsage();

  if (!analysis.notPlaying.length) return null;

  return (
    <Card className="mt-4 border-amber-200 bg-amber-50/50">
      <CardHeader 
        className="pb-3 cursor-pointer hover:bg-amber-50"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <CardTitle className="text-sm flex items-center justify-between">
          <span className="flex items-center gap-2">
            <Info className="w-4 h-4 text-amber-600" />
            Lineup Insights - Inning {inningNumber}
          </span>
          {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
        </CardTitle>
      </CardHeader>
      
      {isExpanded && (
        <CardContent className="space-y-3">
          <div className="text-sm text-amber-800">
            <p className="font-medium mb-2">Players on bench this inning:</p>
            {analysis.notPlaying.map((item, idx) => (
              <div key={idx} className="mb-3 p-2 bg-white rounded border border-amber-200">
                <p className="font-medium text-gray-900">{item.player.name}</p>
                {item.positions.length > 0 && (
                  <p className="text-xs text-gray-600 mt-1">
                    Can play: {item.positions.join(', ')}
                  </p>
                )}
                <p className="text-xs text-amber-700 mt-1">
                  Reason: {item.reasons.join('; ')}
                </p>
              </div>
            ))}
          </div>
          
          <div className="p-2 bg-blue-50 rounded text-xs text-blue-800">
            <p className="font-medium mb-1">Why aren't "In the Mix" players always playing?</p>
            <ul className="space-y-1 ml-4 list-disc">
              <li>The algorithm prioritizes overall playing time balance</li>
              <li>Players who've played less get priority, regardless of role</li>
              <li>"In the Mix" players will rotate in when it's their turn</li>
              <li>To force a player to play more, mark them as "Primary" for that position</li>
            </ul>
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export default LineupDiagnostics;
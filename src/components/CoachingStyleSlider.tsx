import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { 
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { RotationRules } from '@/contexts/TeamContext';

interface CoachingStyleSliderProps {
  currentRules: RotationRules;
  onRulesChange: (rules: RotationRules) => void;
  onStyleChange?: (style: number) => void;
  initialStyle?: number;
  disabled?: boolean;
}

export function CoachingStyleSlider({ 
  currentRules, 
  onRulesChange,
  onStyleChange,
  initialStyle, 
  disabled 
}: CoachingStyleSliderProps) {
  // Calculate initial slider value from initialStyle or current rules
  const getSliderValue = (): number => {
    // If we have an explicit coaching style value, use it
    if (initialStyle !== undefined) {
      return initialStyle;
    }
    
    // Otherwise, infer from current rules
    if (currentRules.competitiveMode) {
      if (currentRules.competitiveMinPlayingTime && currentRules.competitiveMinPlayingTime <= 25) {
        return 90; // Tournament mode
      }
      return 70; // Mostly competitive
    }
    if (currentRules.equalPlayingTime) {
      return 10; // Pure recreational
    }
    return 50; // Balanced
  };

  const [sliderValue, setSliderValue] = React.useState(getSliderValue());
  // Only show advanced settings for coaches who need them (balanced or competitive)
  const [showAdvanced, setShowAdvanced] = React.useState(false);

  const getSliderDescription = (value: number): string => {
    if (value < 20) return "Everyone plays equally, pure development focus";
    if (value < 40) return "Mostly equal playing time with slight preferences";
    if (value < 60) return "Balanced approach - merit-based with fairness";
    if (value < 80) return "Competitive focus with minimum play requirements";
    return "Tournament mode - win first, minimums only";
  };

  const getLineupRulesFromSlider = (sliderValue: number): Partial<RotationRules> => {
    if (sliderValue < 20) {
      // Pure recreational
      return {
        competitiveMode: false,
        equalPlayingTime: true,
        competitiveMinPlayingTime: 50,
        rotateLineupEvery: 1,
        rotatePitcherEvery: 2,
        limitBenchTime: true,
        respectPositionLockouts: true
      };
    } else if (sliderValue < 40) {
      // Mostly recreational
      return {
        competitiveMode: false,
        equalPlayingTime: true,
        competitiveMinPlayingTime: 45,
        rotateLineupEvery: 1,
        rotatePitcherEvery: 2,
        limitBenchTime: true,
        respectPositionLockouts: true
      };
    } else if (sliderValue < 60) {
      // Balanced
      return {
        competitiveMode: false,
        equalPlayingTime: false,
        competitiveMinPlayingTime: 40,
        rotateLineupEvery: 2,
        rotatePitcherEvery: 3,
        limitBenchTime: true,
        respectPositionLockouts: true
      };
    } else if (sliderValue < 80) {
      // Mostly competitive
      return {
        competitiveMode: true,
        equalPlayingTime: false,
        competitiveMinPlayingTime: 35,
        rotateLineupEvery: 2,
        rotatePitcherEvery: 3,
        limitBenchTime: true,
        respectPositionLockouts: true
      };
    } else {
      // Tournament mode
      return {
        competitiveMode: true,
        equalPlayingTime: false,
        competitiveMinPlayingTime: 25,
        rotateLineupEvery: 3,
        rotatePitcherEvery: 4,
        limitBenchTime: true,
        respectPositionLockouts: true
      };
    }
  };

  const handleSliderChange = (value: number[]) => {
    const newValue = value[0];
    setSliderValue(newValue);
    
    // Get new rules based on slider position
    const newRules = getLineupRulesFromSlider(newValue);
    
    // Merge with existing rules to preserve other settings
    onRulesChange({
      ...currentRules,
      ...newRules
    });
    
    // Also save the coaching style value if callback provided
    if (onStyleChange) {
      onStyleChange(newValue);
    }
  };

  const getSliderColor = (value: number) => {
    if (value < 30) return 'bg-blue-500';
    if (value < 70) return 'bg-green-500';
    return 'bg-orange-500';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Coaching Style</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="flex justify-between text-sm font-medium">
            <span className="text-blue-600">Equal Playing Time</span>
            <span className="text-orange-600">Win First</span>
          </div>
          
          <Slider
            value={[sliderValue]}
            onValueChange={handleSliderChange}
            max={100}
            min={0}
            step={10}
            disabled={disabled}
            className="w-full"
          />
          
          <div className="text-center">
            <p className="text-sm text-muted-foreground mt-2">
              {getSliderDescription(sliderValue)}
            </p>
            <div className={cn(
              "mt-3 px-3 py-1 rounded-full inline-block text-xs font-medium",
              sliderValue < 30 && "bg-blue-100 text-blue-700",
              sliderValue >= 30 && sliderValue < 70 && "bg-green-100 text-green-700",
              sliderValue >= 70 && "bg-orange-100 text-orange-700"
            )}>
              {sliderValue < 30 && "Recreational Mode"}
              {sliderValue >= 30 && sliderValue < 70 && "Balanced Mode"}
              {sliderValue >= 70 && "Competitive Mode"}
            </div>
          </div>
        </div>

        {/* What this means section */}
        <div className="bg-gray-50 rounded-lg p-4 space-y-2">
          <h4 className="font-medium text-sm">What this means:</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            {sliderValue < 30 && (
              <>
                <li>• Every player gets equal innings on the field</li>
                <li>• Players can play any position (except those marked "Never")</li>
                <li>• Frequent rotation (every inning)</li>
                <li>• Perfect for development and learning</li>
              </>
            )}
            {sliderValue >= 30 && sliderValue < 70 && (
              <>
                <li>• Playing time based on merit with fairness</li>
                <li>• Players favor their assigned positions</li>
                <li>• Moderate rotation (every 2 innings)</li>
                <li>• Good for house leagues</li>
              </>
            )}
            {sliderValue >= 70 && (
              <>
                <li>• Best players at key positions (P, C, SS)</li>
                <li>• Players ONLY play assigned positions</li>
                <li>• Strategic lineup optimization</li>
                <li>• Ideal for tournaments and travel ball</li>
              </>
            )}
          </ul>
        </div>

        {/* Advanced settings collapsible - only show for non-recreational coaches */}
        {sliderValue >= 30 && (
          <Collapsible open={showAdvanced} onOpenChange={setShowAdvanced}>
            <CollapsibleTrigger className="flex items-center gap-2 text-sm font-medium hover:text-primary transition-colors">
              <ChevronDown className={cn(
                "h-4 w-4 transition-transform",
                showAdvanced && "rotate-180"
              )} />
              Advanced Settings
            </CollapsibleTrigger>
          <CollapsibleContent className="pt-4 space-y-4">
            <div className="grid gap-4">
              <div className="space-y-2">
                <Label htmlFor="rotate-every">Rotate Lineup Every</Label>
                <select
                  id="rotate-every"
                  value={currentRules.rotateLineupEvery}
                  onChange={(e) => onRulesChange({
                    ...currentRules,
                    rotateLineupEvery: parseInt(e.target.value)
                  })}
                  disabled={disabled}
                  className="w-full p-2 border rounded"
                >
                  <option value={1}>Every Inning</option>
                  <option value={2}>Every 2 Innings</option>
                  <option value={3}>Every 3 Innings</option>
                  <option value={4}>Every 4 Innings</option>
                </select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="rotate-pitcher">Rotate Pitcher Every</Label>
                <select
                  id="rotate-pitcher"
                  value={currentRules.rotatePitcherEvery}
                  onChange={(e) => onRulesChange({
                    ...currentRules,
                    rotatePitcherEvery: parseInt(e.target.value)
                  })}
                  disabled={disabled}
                  className="w-full p-2 border rounded"
                >
                  <option value={1}>Every Inning</option>
                  <option value={2}>Every 2 Innings</option>
                  <option value={3}>Every 3 Innings</option>
                  <option value={4}>Every 4 Innings</option>
                  <option value={5}>Every 5 Innings</option>
                  <option value={6}>Every 6 Innings</option>
                </select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="default-innings">Default Game Length</Label>
                <select
                  id="default-innings"
                  value={currentRules.defaultInnings || 7}
                  onChange={(e) => onRulesChange({
                    ...currentRules,
                    defaultInnings: parseInt(e.target.value)
                  })}
                  disabled={disabled}
                  className="w-full p-2 border rounded"
                >
                  <option value={3}>3 Innings</option>
                  <option value={4}>4 Innings</option>
                  <option value={5}>5 Innings</option>
                  <option value={6}>6 Innings</option>
                  <option value={7}>7 Innings</option>
                  <option value={8}>8 Innings</option>
                  <option value={9}>9 Innings</option>
                </select>
              </div>

              {currentRules.competitiveMode && (
                <div className="space-y-2">
                  <Label htmlFor="min-playing">Minimum Playing Time %</Label>
                  <select
                    id="min-playing"
                    value={currentRules.competitiveMinPlayingTime || 30}
                    onChange={(e) => onRulesChange({
                      ...currentRules,
                      competitiveMinPlayingTime: parseInt(e.target.value)
                    })}
                    disabled={disabled}
                    className="w-full p-2 border rounded"
                  >
                    <option value={25}>25% (Tournament)</option>
                    <option value={30}>30%</option>
                    <option value={35}>35%</option>
                    <option value={40}>40%</option>
                    <option value={45}>45%</option>
                    <option value={50}>50% (Half)</option>
                  </select>
                </div>
              )}
            </div>
          </CollapsibleContent>
        </Collapsible>
        )}
      </CardContent>
    </Card>
  );
}

import { Link } from "react-router-dom";

const Footer = () => {
  return (
    <footer className="bg-baseball-navy text-baseball-white py-4 px-6 mt-8">
      <div className="container mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm mb-2 md:mb-0">&copy; {new Date().getFullYear()} DugoutBoss.com</p>
          <div className="flex space-x-4">
            <Link to="/faq" className="text-sm hover:underline">FAQ</Link>
            <Link to="/contact" className="text-sm hover:underline">Contact</Link>
            <Link to="/privacy-policy" className="text-sm hover:underline">Privacy</Link>
            <Link to="/dashboard" className="text-sm hover:underline">Dashboard</Link>
            <Link to="/" className="text-sm hover:underline">Home</Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;

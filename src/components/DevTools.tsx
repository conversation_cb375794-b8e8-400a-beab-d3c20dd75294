import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ChevronUp, ChevronDown, Database, Wrench } from 'lucide-react';

const DevTools = () => {
  const [isOpen, setIsOpen] = useState(false);

  // Only show DevTools in development mode
  if (import.meta.env.PROD) {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 md:right-4 md:left-auto z-40">
      {isOpen && (
        <div className="bg-white rounded-lg shadow-lg p-4 mb-2 border border-gray-200 w-64 max-w-[calc(100vw-2rem)]">
          <h3 className="font-bold text-lg mb-3 text-gray-800">Developer Tools</h3>

          <div className="space-y-2">
            <Button asChild variant="outline" className="w-full justify-start" size="sm">
              <a href="/test-connection" className="flex items-center">
                <Database className="mr-2 h-4 w-4" />
                Test Database Connection
              </a>
            </Button>

            <Button asChild variant="outline" className="w-full justify-start" size="sm">
              <a href="/run-migrations" className="flex items-center">
                <Wrench className="mr-2 h-4 w-4" />
                Database Setup
              </a>
            </Button>
          </div>
        </div>
      )}

      <Button
        onClick={() => setIsOpen(!isOpen)}
        variant="default"
        size="sm"
        className="rounded-full h-12 w-12 flex items-center justify-center bg-baseball-navy hover:bg-baseball-navy/90"
      >
        {isOpen ? (
          <ChevronDown className="h-6 w-6" />
        ) : (
          <ChevronUp className="h-6 w-6" />
        )}
      </Button>
    </div>
  );
};

export default DevTools;

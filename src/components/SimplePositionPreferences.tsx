import React from 'react';
import { Player, PositionPreference, PositionPreferenceWithRank } from '@/contexts/TeamContext';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Star, Circle, X, RotateCcw } from 'lucide-react';
import { toast } from 'sonner';

interface SimplePositionPreferencesProps {
  player: Player;
  onPreferenceChange: (playerId: string, position: string, preference: PositionPreference | PositionPreferenceWithRank | null) => void;
  disabled?: boolean;
}

const POSITION_OPTIONS = [
  { key: 'pitcher', label: 'Pitcher', abbrev: 'P' },
  { key: 'catcher', label: 'Catcher', abbrev: 'C' },
  { key: 'firstBase', label: 'First Base', abbrev: '1B' },
  { key: 'secondBase', label: 'Second Base', abbrev: '2B' },
  { key: 'shortstop', label: 'Shortstop', abbrev: 'SS' },
  { key: 'thirdBase', label: 'Third Base', abbrev: '3B' },
  { key: 'leftField', label: 'Left Field', abbrev: 'LF' },
  { key: 'centerField', label: 'Center Field', abbrev: 'CF' },
  { key: 'rightField', label: 'Right Field', abbrev: 'RF' },
];

const SimplePositionPreferences: React.FC<SimplePositionPreferencesProps> = ({
  player,
  onPreferenceChange,
  disabled = false
}) => {
  // Helper function to get current preference for a position
  const getCurrentPreference = (position: string): PositionPreference => {
    const pref = player.positionPreferences?.[position as keyof typeof player.positionPreferences];
    if (!pref) return 'neutral';
    if (typeof pref === 'string') return pref;
    return pref.level;
  };

  // Handle preference change
  const handlePreferenceChange = (position: string, level: PositionPreference) => {
    if (disabled) return;

    let newPreference: PositionPreference | null = null;
    
    if (level !== 'neutral') {
      newPreference = level;
    }

    onPreferenceChange(player.id, position, newPreference);
    
    // Provide user feedback
    if (level === 'neutral') {
      toast.success(`Cleared ${position} preference for ${player.name}`);
    } else {
      toast.success(`Set ${player.name} ${position} to ${level}`);
    }
  };

  // Get button styling based on preference level
  const getButtonStyle = (position: string, level: PositionPreference) => {
    const currentLevel = getCurrentPreference(position);
    const isActive = currentLevel === level;
    
    const baseClasses = "h-8 text-xs transition-all duration-200";
    
    switch (level) {
      case 'preferred':
        return `${baseClasses} ${isActive 
          ? 'bg-green-500 text-white border-green-600 shadow-md' 
          : 'bg-green-50 text-green-700 border-green-200 hover:bg-green-100'}`;
      case 'secondary':
        return `${baseClasses} ${isActive 
          ? 'bg-blue-500 text-white border-blue-600 shadow-md' 
          : 'bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100'}`;
      case 'avoid':
        return `${baseClasses} ${isActive 
          ? 'bg-red-500 text-white border-red-600 shadow-md' 
          : 'bg-red-50 text-red-700 border-red-200 hover:bg-red-100'}`;
      case 'neutral':
        return `${baseClasses} ${isActive 
          ? 'bg-gray-200 text-gray-700 border-gray-300' 
          : 'bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100'}`;
      default:
        return baseClasses;
    }
  };

  // Get icon for preference level
  const getIcon = (level: PositionPreference) => {
    switch (level) {
      case 'preferred': return Star;
      case 'secondary': return Circle;
      case 'avoid': return X;
      case 'neutral': return RotateCcw;
      default: return Circle;
    }
  };

  return (
    <div className="space-y-4">
      {/* Position Grid */}
      <div className="grid grid-cols-3 gap-3">
        {POSITION_OPTIONS.map(position => {
          const currentLevel = getCurrentPreference(position.key);
          
          return (
            <div key={position.key} className="space-y-2">
              {/* Position Label */}
              <div className="text-center">
                <div className="text-sm font-medium">{position.abbrev}</div>
                <div className="text-xs text-gray-500">{position.label}</div>
              </div>
              
              {/* Preference Buttons */}
              <div className="space-y-1">
                {(['preferred', 'secondary', 'avoid', 'neutral'] as PositionPreference[]).map(level => {
                  const IconComponent = getIcon(level);
                  const isActive = currentLevel === level;
                  
                  return (
                    <Button
                      key={level}
                      variant="outline"
                      size="sm"
                      className={getButtonStyle(position.key, level)}
                      onClick={() => handlePreferenceChange(position.key, level)}
                      disabled={disabled}
                    >
                      <IconComponent className="w-3 h-3 mr-1" />
                      {level === 'preferred' && 'Pref'}
                      {level === 'secondary' && 'Sec'}
                      {level === 'avoid' && 'Avoid'}
                      {level === 'neutral' && 'Clear'}
                    </Button>
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>

      {/* Current Preferences Summary */}
      <div className="pt-3 border-t">
        <div className="text-sm font-medium mb-2">Current Preferences:</div>
        <div className="flex flex-wrap gap-1">
          {POSITION_OPTIONS.map(pos => {
            const level = getCurrentPreference(pos.key);
            if (level === 'neutral') return null;

            const IconComponent = getIcon(level);
            const colorClass = level === 'preferred' ? 'bg-green-100 text-green-800 border-green-200' :
                              level === 'secondary' ? 'bg-blue-100 text-blue-800 border-blue-200' :
                              'bg-red-100 text-red-800 border-red-200';

            return (
              <Badge key={pos.key} variant="outline" className={`text-xs ${colorClass}`}>
                <IconComponent className="w-3 h-3 mr-1" />
                {pos.abbrev}: {level}
              </Badge>
            );
          })}
          {POSITION_OPTIONS.every(pos => getCurrentPreference(pos.key) === 'neutral') && (
            <span className="text-xs text-gray-500 italic">No preferences set</span>
          )}
        </div>
      </div>
    </div>
  );
};

export default SimplePositionPreferences;

import { Navigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { useEffect, useState, ReactNode } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Loader2 } from "lucide-react";

interface AdminRouteProps {
  children: ReactNode;
}

const AdminRoute = ({ children }: AdminRouteProps) => {
  const { user, loading: authLoading } = useAuth();
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null);
  const [checking, setChecking] = useState(true);

  useEffect(() => {
    const checkAdminStatus = async () => {
      if (authLoading) {
        return; // Wait for auth to finish loading
      }
      
      if (!user) {
        setIsAdmin(false);
        setChecking(false);
        return;
      }

      // Check if this is a demo user - demo users are never admin
      const isDemoUser = user.email?.includes('demo') ||
                        user.email?.includes('baseball_demo') ||
                        localStorage.getItem('demo_mode') === 'true';

      if (isDemoUser) {
        setIsAdmin(false);
        setChecking(false);
        return;
      }

      // Special case for admin accounts
      if (user.email?.includes('admin') || user.email === '<EMAIL>') {
        setIsAdmin(true);
        setChecking(false);
        return;
      }

      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('is_admin')
          .eq('id', user.id)
          .maybeSingle(); // Use maybeSingle instead of single to handle missing profiles

        if (error) {
          console.error("Error checking admin status:", error);
          // If profile doesn't exist, check by email
          if (user.email === '<EMAIL>' || user.email === '<EMAIL>') {
            setIsAdmin(true);
          } else {
            setIsAdmin(false);
          }
        } else {
          setIsAdmin(data?.is_admin || false);
        }
      } catch (error) {
        console.error("Error checking admin status:", error);
        setIsAdmin(false);
      } finally {
        setChecking(false);
      }
    };

    checkAdminStatus();
  }, [user, authLoading]);

  if (authLoading || checking || isAdmin === null) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-baseball-green" />
        <span className="ml-2 text-lg">Checking admin privileges...</span>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/sign-in" replace />;
  }
  
  if (!isAdmin) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
};

export default AdminRoute;

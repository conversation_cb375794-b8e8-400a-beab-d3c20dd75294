import React, { lazy, Suspense } from 'react';
import { Loader2 } from 'lucide-react';

// Dynamically import the chart component to reduce initial bundle size
const ChartComponent = lazy(() => import('./chart'));

// Loading component for chart loading state
const ChartLoading = () => (
  <div className="flex aspect-video justify-center items-center">
    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
  </div>
);

// Re-export all chart components with dynamic loading
export const ChartContainer: React.FC<any> = (props) => (
  <Suspense fallback={<ChartLoading />}>
    <ChartComponent.ChartContainer {...props} />
  </Suspense>
);

export const ChartTooltip: React.FC<any> = (props) => (
  <Suspense fallback={null}>
    <ChartComponent.ChartTooltip {...props} />
  </Suspense>
);

export const ChartTooltipContent: React.FC<any> = (props) => (
  <Suspense fallback={null}>
    <ChartComponent.ChartTooltipContent {...props} />
  </Suspense>
);

export const ChartLegend: React.FC<any> = (props) => (
  <Suspense fallback={null}>
    <ChartComponent.ChartLegend {...props} />
  </Suspense>
);

export const ChartLegendContent: React.FC<any> = (props) => (
  <Suspense fallback={null}>
    <ChartComponent.ChartLegendContent {...props} />
  </Suspense>
);

// Re-export utility functions (these don't need dynamic loading)
export { getPayloadConfigFromPayload } from './chart';

// Export types (no dynamic loading needed for types)
export type { ChartConfig } from './chart';
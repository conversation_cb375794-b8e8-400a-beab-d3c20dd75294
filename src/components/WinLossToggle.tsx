import { useState } from "react";
import { GameResult } from "@/contexts/TeamContext";
import { Button } from "@/components/ui/button";
import { CheckCircle2, XCircle, Minus } from "lucide-react";

interface WinLossToggleProps {
  gameResult: GameResult;
  onResultChange: (result: GameResult) => void;
  disabled?: boolean;
  isPastGame?: boolean;
}

const WinLossToggle = ({ gameResult, onResultChange, disabled = false, isPastGame = true }: WinLossToggleProps) => {
  const [isChanging, setIsChanging] = useState(false);

  // Only show for past games
  if (!isPastGame) {
    return <span className="text-xs text-gray-400">Future Game</span>;
  }
  
  // If gameResult is undefined, it means the database column doesn't exist yet
  if (gameResult === undefined) {
    return (
      <div className="flex items-center gap-1 text-xs text-gray-500" title="Game result tracking unavailable - database migration needed">
        <span>-</span>
      </div>
    );
  }

  const handleResultChange = async (newResult: GameResult) => {
    if (disabled || isChanging) return;
    
    setIsChanging(true);
    try {
      await onResultChange(newResult);
    } finally {
      setIsChanging(false);
    }
  };

  return (
    <div className="flex items-center gap-1">
      <Button
        variant={gameResult === 'win' ? "default" : "ghost"}
        size="sm"
        className={`h-6 w-6 p-0 ${
          gameResult === 'win' 
            ? "bg-green-600 hover:bg-green-700 text-white" 
            : "hover:bg-green-50 hover:text-green-700"
        }`}
        onClick={() => handleResultChange(gameResult === 'win' ? null : 'win')}
        disabled={disabled || isChanging}
        title={gameResult === 'win' ? 'Click to clear' : 'Mark as Win'}
      >
        <CheckCircle2 className="h-3 w-3" />
      </Button>
      
      <Button
        variant={gameResult === 'loss' ? "destructive" : "ghost"}
        size="sm"
        className={`h-6 w-6 p-0 ${
          gameResult === 'loss' 
            ? "bg-red-600 hover:bg-red-700" 
            : "hover:bg-red-50 hover:text-red-700"
        }`}
        onClick={() => handleResultChange(gameResult === 'loss' ? null : 'loss')}
        disabled={disabled || isChanging}
        title={gameResult === 'loss' ? 'Click to clear' : 'Mark as Loss'}
      >
        <XCircle className="h-3 w-3" />
      </Button>
      
      {gameResult && (
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
          onClick={() => handleResultChange(null)}
          disabled={disabled || isChanging}
          title="Clear result"
        >
          <Minus className="h-3 w-3" />
        </Button>
      )}
    </div>
  );
};

export default WinLossToggle;
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

interface ReturnToMenuButtonProps {
  className?: string;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" | "baseball" | "baseballOutline";
}

const ReturnToMenuButton = ({ 
  className = "", 
  variant = "link" 
}: ReturnToMenuButtonProps) => {
  const { user } = useAuth();
  const navigate = useNavigate();

  const handleClick = () => {
    // If user is logged in, go to dashboard, otherwise go to homepage
    navigate(user ? "/dashboard" : "/");
  };

  return (
    <Button 
      variant={variant} 
      onClick={handleClick}
      className={className}
    >
      Return to the menu screen
    </Button>
  );
};

export default ReturnToMenuButton;

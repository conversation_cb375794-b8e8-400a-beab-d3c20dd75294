import React, { useState, useRef, TouchEvent } from 'react';
import { cn } from '@/lib/utils';
import { Trash2, Edit, MoreVertical } from 'lucide-react';

interface SwipeAction {
  icon: React.ElementType;
  label: string;
  color: string;
  action: () => void;
}

interface SwipeableCardProps {
  children: React.ReactNode;
  leftActions?: SwipeAction[];
  rightActions?: SwipeAction[];
  onSwipeComplete?: (direction: 'left' | 'right') => void;
  className?: string;
}

export const SwipeableCard: React.FC<SwipeableCardProps> = ({
  children,
  leftActions = [],
  rightActions = [],
  onSwipeComplete,
  className
}) => {
  const [startX, setStartX] = useState(0);
  const [currentX, setCurrentX] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  
  const threshold = 80; // Pixels to trigger action
  const maxSwipe = 120; // Maximum swipe distance

  const handleTouchStart = (e: TouchEvent) => {
    setStartX(e.touches[0].clientX);
    setIsDragging(true);
  };

  const handleTouchMove = (e: TouchEvent) => {
    if (!isDragging) return;
    
    const diff = e.touches[0].clientX - startX;
    const clampedDiff = Math.max(-maxSwipe, Math.min(maxSwipe, diff));
    setCurrentX(clampedDiff);
  };

  const handleTouchEnd = () => {
    if (!isDragging) return;
    
    const shouldTriggerAction = Math.abs(currentX) > threshold;
    
    if (shouldTriggerAction) {
      const direction = currentX > 0 ? 'right' : 'left';
      const actions = direction === 'left' ? rightActions : leftActions;
      
      if (actions.length > 0) {
        // Animate card off screen
        setCurrentX(direction === 'left' ? -window.innerWidth : window.innerWidth);
        
        setTimeout(() => {
          onSwipeComplete?.(direction);
          // Reset position
          setCurrentX(0);
        }, 300);
      } else {
        // Snap back if no actions
        setCurrentX(0);
      }
    } else {
      // Snap back if threshold not reached
      setCurrentX(0);
    }
    
    setIsDragging(false);
  };

  const renderActions = (actions: SwipeAction[], side: 'left' | 'right') => {
    if (actions.length === 0) return null;
    
    const isVisible = (side === 'left' && currentX > 0) || (side === 'right' && currentX < 0);
    const opacity = Math.min(1, Math.abs(currentX) / threshold);
    
    return (
      <div
        className={cn(
          "absolute top-0 bottom-0 flex items-center",
          side === 'left' ? "left-0 pl-4" : "right-0 pr-4",
          isVisible ? "opacity-100" : "opacity-0"
        )}
        style={{ opacity }}
      >
        {actions.map((action, index) => {
          const Icon = action.icon;
          return (
            <button
              key={index}
              onClick={action.action}
              className={cn(
                "touch-target-preferred rounded-full",
                "flex items-center justify-center",
                "transition-transform duration-200",
                "active:scale-90"
              )}
              style={{ backgroundColor: action.color }}
            >
              <Icon size={24} className="text-white" />
            </button>
          );
        })}
      </div>
    );
  };

  return (
    <div className={cn("relative overflow-hidden", className)}>
      {renderActions(leftActions, 'left')}
      {renderActions(rightActions, 'right')}
      
      <div
        ref={cardRef}
        className={cn(
          "relative bg-white dark:bg-gray-800",
          "transition-transform duration-300 ease-out",
          isDragging && "transition-none"
        )}
        style={{ transform: `translateX(${currentX}px)` }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {children}
      </div>
    </div>
  );
};

// Example usage component
export const SwipeablePlayerCard: React.FC<{
  player: { id: string; name: string; position?: string };
  onEdit: () => void;
  onDelete: () => void;
}> = ({ player, onEdit, onDelete }) => {
  const rightActions: SwipeAction[] = [
    {
      icon: Edit,
      label: 'Edit',
      color: '#3B82F6',
      action: onEdit
    },
    {
      icon: Trash2,
      label: 'Delete',
      color: '#EF4444',
      action: onDelete
    }
  ];

  return (
    <SwipeableCard rightActions={rightActions} className="mb-2">
      <div className="mobile-card flex items-center justify-between">
        <div>
          <h3 className="font-semibold text-lg">{player.name}</h3>
          {player.position && (
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {player.position}
            </p>
          )}
        </div>
        <MoreVertical size={20} className="text-gray-400" />
      </div>
    </SwipeableCard>
  );
};
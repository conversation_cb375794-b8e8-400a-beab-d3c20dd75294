import React, { useState, useRef, TouchEvent, ReactNode } from 'react';
import { RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PullToRefreshProps {
  onRefresh: () => Promise<void>;
  children: ReactNode;
  threshold?: number;
  className?: string;
}

export const PullToRefresh: React.FC<PullToRefreshProps> = ({
  onRefresh,
  children,
  threshold = 80,
  className
}) => {
  const [pullDistance, setPullDistance] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [startY, setStartY] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const maxPull = 150;
  const resistance = 2.5; // Higher = more resistance

  const handleTouchStart = (e: TouchEvent) => {
    if (containerRef.current?.scrollTop === 0) {
      setStartY(e.touches[0].clientY);
    }
  };

  const handleTouchMove = (e: TouchEvent) => {
    if (!startY || isRefreshing) return;
    
    const currentY = e.touches[0].clientY;
    const diff = currentY - startY;
    
    if (diff > 0 && containerRef.current?.scrollTop === 0) {
      // Apply resistance as pull distance increases
      const resistedDiff = diff / resistance;
      const clampedDiff = Math.min(resistedDiff, maxPull);
      setPullDistance(clampedDiff);
      
      // Prevent default scrolling
      if (clampedDiff > 10) {
        e.preventDefault();
      }
    }
  };

  const handleTouchEnd = async () => {
    if (pullDistance > threshold && !isRefreshing) {
      setIsRefreshing(true);
      setPullDistance(60); // Hold at loading position
      
      try {
        await onRefresh();
      } catch (error) {
        console.error('Refresh failed:', error);
      } finally {
        setIsRefreshing(false);
        setPullDistance(0);
      }
    } else {
      setPullDistance(0);
    }
    
    setStartY(0);
  };

  const getIconRotation = () => {
    if (isRefreshing) return 'animate-spin';
    const rotation = Math.min(pullDistance * 3, 180);
    return `rotate-${rotation}`;
  };

  const getIconScale = () => {
    const scale = 0.5 + Math.min(pullDistance / threshold, 1) * 0.5;
    return scale;
  };

  return (
    <div 
      ref={containerRef}
      className={cn("relative overflow-auto smooth-scroll", className)}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Pull indicator */}
      <div
        className={cn(
          "absolute top-0 left-0 right-0 flex justify-center",
          "transition-all duration-300 ease-out",
          pullDistance > 0 ? "opacity-100" : "opacity-0"
        )}
        style={{
          height: `${pullDistance}px`,
          transform: `translateY(-${pullDistance}px)`
        }}
      >
        <div className="flex items-center justify-center">
          <RefreshCw
            size={24}
            className={cn(
              "text-baseball-green",
              isRefreshing && "animate-spin"
            )}
            style={{
              transform: `scale(${getIconScale()}) rotate(${pullDistance * 3}deg)`,
              transition: isRefreshing ? 'none' : 'transform 0.2s'
            }}
          />
        </div>
      </div>
      
      {/* Content */}
      <div
        className="transition-transform duration-300 ease-out"
        style={{
          transform: `translateY(${pullDistance}px)`
        }}
      >
        {children}
      </div>
    </div>
  );
};

// Example usage wrapper for lists
export const RefreshableList: React.FC<{
  items: any[];
  onRefresh: () => Promise<void>;
  renderItem: (item: any, index: number) => ReactNode;
  emptyMessage?: string;
  className?: string;
}> = ({ items, onRefresh, renderItem, emptyMessage = "No items", className }) => {
  return (
    <PullToRefresh onRefresh={onRefresh} className={cn("h-full", className)}>
      {items.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-64 text-gray-500">
          <RefreshCw size={48} className="mb-4 opacity-20" />
          <p>{emptyMessage}</p>
          <p className="text-sm mt-2">Pull down to refresh</p>
        </div>
      ) : (
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {items.map(renderItem)}
        </div>
      )}
    </PullToRefresh>
  );
};
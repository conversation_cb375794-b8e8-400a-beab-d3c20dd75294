import React, { useState } from 'react';
import { Link, useNavigate } from "react-router-dom";
import { useTeam } from "@/contexts/TeamContext";
import { useAuth } from "@/contexts/AuthContext";
import { Menu, X, ChevronLeft, LogOut, Home, Users, ClipboardList, Settings } from "lucide-react";
import { cn } from '@/lib/utils';
import { toast } from "sonner";

interface MobileHeaderProps {
  title?: string;
  showBack?: boolean;
  backLink?: string;
  backState?: any;
  onBack?: () => void;
  rightAction?: React.ReactNode;
}

export const MobileHeader: React.FC<MobileHeaderProps> = ({ 
  title, 
  showBack = false, 
  backLink = "/dashboard", 
  backState, 
  onBack,
  rightAction 
}) => {
  const { teamName } = useTeam();
  const { user, signOut } = useAuth();
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const isDemoMode = user?.email?.includes('demo') ||
                     user?.email?.includes('baseball_demo') ||
                     localStorage.getItem('demo_mode') === 'true' ||
                     localStorage.getItem('demo_user_is_paid') === 'true';

  const handleLogout = async () => {
    try {
      await signOut();
      // Clear all demo-specific localStorage items
      localStorage.removeItem('demo_user_is_paid');
      localStorage.removeItem('demo_mode');
      localStorage.removeItem('demo_user_email');
      localStorage.removeItem('create_demo_team');

      toast.success("You have been logged out successfully");
      navigate('/');
    } catch (error) {
      console.error("Error logging out:", error);
      toast.error("Failed to log out. Please try again.");
    }
  };

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      if (backLink === "/" && user) {
        navigate("/dashboard");
      } else {
        navigate(backLink, backState ? { state: backState } : undefined);
      }
    }
  };

  return (
    <>
      {/* Header */}
      <header className="bg-baseball-navy text-white safe-top md:hidden">
        <div className="flex items-center justify-between h-14 px-4">
          {/* Left Action */}
          <div className="flex items-center -ml-2">
            {showBack ? (
              <button
                onClick={handleBack}
                className="touch-target p-2 rounded-full active:bg-white/10"
                aria-label="Go back"
              >
                <ChevronLeft size={24} />
              </button>
            ) : (
              <button
                onClick={() => setIsMenuOpen(true)}
                className="touch-target p-2 rounded-full active:bg-white/10"
                aria-label="Open menu"
              >
                <Menu size={24} />
              </button>
            )}
          </div>

          {/* Title */}
          <div className="flex-1 text-center px-4">
            <h1 className="text-lg font-semibold truncate">
              {title || teamName}
            </h1>
            {isDemoMode && !title && (
              <span className="text-xs text-yellow-400">DEMO</span>
            )}
          </div>

          {/* Right Action */}
          <div className="flex items-center -mr-2">
            {rightAction || <div className="w-10" />}
          </div>
        </div>
      </header>

      {/* Slide-out Menu */}
      <div
        className={cn(
          "fixed inset-0 z-50 md:hidden",
          "transition-opacity duration-300",
          isMenuOpen ? "opacity-100 pointer-events-auto" : "opacity-0 pointer-events-none"
        )}
      >
        {/* Backdrop */}
        <div
          className="absolute inset-0 bg-black/50"
          onClick={() => setIsMenuOpen(false)}
        />

        {/* Menu Panel */}
        <div
          className={cn(
            "absolute left-0 top-0 bottom-0 w-80 max-w-[85vw]",
            "bg-white dark:bg-gray-900",
            "shadow-xl",
            "transition-transform duration-300",
            isMenuOpen ? "translate-x-0" : "-translate-x-full"
          )}
        >
          {/* Menu Header */}
          <div className="bg-baseball-navy text-white p-4 safe-top">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-bold">Dugout Boss</h2>
                {user && (
                  <p className="text-sm text-white/80 mt-1">
                    {user.email}
                    {isDemoMode && (
                      <span className="ml-2 text-xs bg-yellow-500 text-white px-2 py-0.5 rounded-full">
                        DEMO
                      </span>
                    )}
                  </p>
                )}
              </div>
              <button
                onClick={() => setIsMenuOpen(false)}
                className="touch-target p-2 -m-2 rounded-full active:bg-white/10"
              >
                <X size={24} />
              </button>
            </div>
          </div>

          {/* Menu Items */}
          <nav className="flex-1 overflow-y-auto">
            <div className="py-2">
              <Link
                to="/dashboard"
                onClick={() => setIsMenuOpen(false)}
                className="flex items-center gap-4 px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-800 active:bg-gray-200 dark:active:bg-gray-700"
              >
                <Home size={20} />
                <span className="font-medium">Home</span>
              </Link>

              {teamName && (
                <>
                  <div className="border-t border-gray-200 dark:border-gray-700 my-2" />
                  
                  <Link
                    to="/team-roster"
                    onClick={() => setIsMenuOpen(false)}
                    className="flex items-center gap-4 px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-800 active:bg-gray-200 dark:active:bg-gray-700"
                  >
                    <Users size={20} />
                    <span className="font-medium">Team Roster</span>
                  </Link>

                  <Link
                    to="/create-lineup"
                    state={null}
                    onClick={() => setIsMenuOpen(false)}
                    className="flex items-center gap-4 px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-800 active:bg-gray-200 dark:active:bg-gray-700"
                  >
                    <ClipboardList size={20} />
                    <span className="font-medium">Create Lineup</span>
                  </Link>

                  <Link
                    to="/rotation-rules"
                    onClick={() => setIsMenuOpen(false)}
                    className="flex items-center gap-4 px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-800 active:bg-gray-200 dark:active:bg-gray-700"
                  >
                    <Settings size={20} />
                    <span className="font-medium">Rotation Rules</span>
                  </Link>
                </>
              )}
            </div>
          </nav>

          {/* Menu Footer */}
          {user && (
            <div className="border-t border-gray-200 dark:border-gray-700 p-4 safe-bottom">
              <button
                onClick={() => {
                  setIsMenuOpen(false);
                  handleLogout();
                }}
                className="flex items-center gap-4 w-full px-4 py-3 rounded-lg bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 font-medium active:bg-red-100 dark:active:bg-red-900/30"
              >
                <LogOut size={20} />
                <span>Logout</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </>
  );
};
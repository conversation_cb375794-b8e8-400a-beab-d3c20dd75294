import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, Users, Calendar, Trophy, User } from 'lucide-react';
import { cn } from '@/lib/utils';

interface NavItem {
  icon: React.ElementType;
  label: string;
  path: string;
  badge?: number;
}

const navItems: NavItem[] = [
  {
    icon: Home,
    label: 'Home',
    path: '/dashboard'
  },
  {
    icon: Users,
    label: 'Roster',
    path: '/team-roster'
  },
  {
    icon: Calendar,
    label: 'Lineup',
    path: '/create-lineup'
  },
  {
    icon: Trophy,
    label: 'Batch',
    path: '/batch-games'
  },
  {
    icon: User,
    label: 'Profile',
    path: '/user-profile'
  }
];

export const BottomNav: React.FC = () => {
  const location = useLocation();

  return (
    <nav className="fixed bottom-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 md:hidden shadow-[0_-4px_12px_rgba(0,0,0,0.08)]">
      <div className="flex justify-around items-center h-14 px-2" style={{ paddingBottom: 'env(safe-area-inset-bottom, 0px)' }}>
        {navItems.map((item) => {
          const Icon = item.icon;
          const isActive = location.pathname.startsWith(item.path);
          
          return (
            <Link
              key={item.path}
              to={item.path}
              className={cn(
                "flex flex-col items-center justify-center",
                "flex-1 py-2 relative group",
                "transition-all duration-200",
                isActive 
                  ? "text-baseball-green dark:text-green-400" 
                  : "text-gray-500 dark:text-gray-400"
              )}
            >
              <Icon 
                size={22} 
                className={cn(
                  "transition-all duration-200",
                  "group-active:scale-90",
                  isActive && "transform scale-110"
                )}
              />
              <span className={cn(
                "text-[10px] mt-1 font-medium",
                isActive && "text-[11px]"
              )}>{item.label}</span>
              
              {/* Active indicator - cleaner dot style */}
              {isActive && (
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-baseball-green dark:bg-green-400 rounded-full" />
              )}
            </Link>
          );
        })}
      </div>
    </nav>
  );
};
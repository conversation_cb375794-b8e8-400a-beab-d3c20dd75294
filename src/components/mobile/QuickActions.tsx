import React from 'react';
import { cn } from '@/lib/utils';
import { 
  UserPlus, 
  UserMinus, 
  Repeat, 
  FileText, 
  Clock,
  TrendingUp
} from 'lucide-react';

interface QuickAction {
  icon: React.ElementType;
  label: string;
  sublabel?: string;
  onClick: () => void;
  color?: string;
  badge?: string | number;
}

interface QuickActionsProps {
  actions: QuickAction[];
  layout?: 'grid' | 'list' | 'compact';
  className?: string;
}

export const QuickActions: React.FC<QuickActionsProps> = ({
  actions,
  layout = 'grid',
  className
}) => {
  if (layout === 'grid') {
    return (
      <div className={cn(
        "grid grid-cols-2 gap-3 p-4",
        className
      )}>
        {actions.map((action, index) => {
          const Icon = action.icon;
          return (
            <button
              key={index}
              onClick={action.onClick}
              className={cn(
                "relative p-4 rounded-xl",
                "bg-white dark:bg-gray-800",
                "border border-gray-200 dark:border-gray-700",
                "shadow-sm active:shadow-none",
                "transition-all duration-200",
                "active:scale-95",
                "touch-target-preferred"
              )}
            >
              {action.badge && (
                <span className="absolute top-2 right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center">
                  {action.badge}
                </span>
              )}
              
              <div className="flex flex-col items-center gap-2">
                <div
                  className={cn(
                    "w-12 h-12 rounded-full",
                    "flex items-center justify-center",
                    action.color || "bg-baseball-green/10"
                  )}
                >
                  <Icon 
                    size={24} 
                    className={action.color ? "text-white" : "text-baseball-green"}
                  />
                </div>
                <div className="text-center">
                  <p className="font-medium text-sm">{action.label}</p>
                  {action.sublabel && (
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                      {action.sublabel}
                    </p>
                  )}
                </div>
              </div>
            </button>
          );
        })}
      </div>
    );
  }

  if (layout === 'list') {
    return (
      <div className={cn("space-y-2 p-4", className)}>
        {actions.map((action, index) => {
          const Icon = action.icon;
          return (
            <button
              key={index}
              onClick={action.onClick}
              className={cn(
                "w-full flex items-center gap-4 p-4",
                "bg-white dark:bg-gray-800",
                "border border-gray-200 dark:border-gray-700",
                "rounded-lg shadow-sm",
                "transition-all duration-200",
                "active:scale-[0.98] active:shadow-none",
                "touch-target"
              )}
            >
              <div
                className={cn(
                  "w-10 h-10 rounded-full flex-shrink-0",
                  "flex items-center justify-center",
                  action.color || "bg-baseball-green/10"
                )}
              >
                <Icon 
                  size={20} 
                  className={action.color ? "text-white" : "text-baseball-green"}
                />
              </div>
              <div className="flex-1 text-left">
                <p className="font-medium">{action.label}</p>
                {action.sublabel && (
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {action.sublabel}
                  </p>
                )}
              </div>
              {action.badge && (
                <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1">
                  {action.badge}
                </span>
              )}
            </button>
          );
        })}
      </div>
    );
  }

  // Compact layout for inline use
  return (
    <div className={cn("flex gap-2 overflow-x-auto p-2", className)}>
      {actions.map((action, index) => {
        const Icon = action.icon;
        return (
          <button
            key={index}
            onClick={action.onClick}
            className={cn(
              "flex items-center gap-2 px-4 py-2",
              "bg-white dark:bg-gray-800",
              "border border-gray-200 dark:border-gray-700",
              "rounded-full whitespace-nowrap",
              "transition-all duration-200",
              "active:scale-95",
              "touch-target"
            )}
          >
            <Icon size={16} className="text-baseball-green" />
            <span className="text-sm font-medium">{action.label}</span>
            {action.badge && (
              <span className="bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center ml-1">
                {action.badge}
              </span>
            )}
          </button>
        );
      })}
    </div>
  );
};

// Pre-built Game Day Quick Actions
export const GameDayQuickActions: React.FC<{
  onSubstitute: () => void;
  onRotate: () => void;
  onViewLineup: () => void;
  onStats: () => void;
  substitutionsCount?: number;
  currentInning?: number;
}> = ({ 
  onSubstitute, 
  onRotate, 
  onViewLineup, 
  onStats,
  substitutionsCount = 0,
  currentInning = 1
}) => {
  const actions: QuickAction[] = [
    {
      icon: UserPlus,
      label: 'Substitute',
      sublabel: 'Quick player swap',
      onClick: onSubstitute,
      badge: substitutionsCount > 0 ? substitutionsCount : undefined
    },
    {
      icon: Repeat,
      label: 'Rotate',
      sublabel: `Inning ${currentInning}`,
      onClick: onRotate
    },
    {
      icon: FileText,
      label: 'Lineup Card',
      sublabel: 'View/Export',
      onClick: onViewLineup
    },
    {
      icon: TrendingUp,
      label: 'Stats',
      sublabel: 'Game stats',
      onClick: onStats
    }
  ];

  return <QuickActions actions={actions} layout="grid" />;
};
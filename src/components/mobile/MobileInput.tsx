import React, { forwardRef, InputHTMLAttributes, TextareaHTMLAttributes } from 'react';
import { cn } from '@/lib/utils';
import { X, Check } from 'lucide-react';

interface MobileInputProps extends InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  helper?: string;
  error?: string;
  clearable?: boolean;
  onClear?: () => void;
  successIcon?: boolean;
}

export const MobileInput = forwardRef<HTMLInputElement, MobileInputProps>(
  ({ 
    label, 
    helper, 
    error, 
    clearable, 
    onClear,
    successIcon,
    className,
    ...props 
  }, ref) => {
    const hasValue = props.value && String(props.value).length > 0;
    
    return (
      <div className="mb-4">
        {label && (
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {label}
            {props.required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}
        
        <div className="relative">
          <input
            ref={ref}
            className={cn(
              "w-full px-4 py-3",
              "text-base", // Prevents iOS zoom
              "bg-white dark:bg-gray-800",
              "border rounded-lg",
              "transition-all duration-200",
              "focus:outline-none focus:ring-2",
              "touch-target", // Ensures 48px min height
              error 
                ? "border-red-500 focus:ring-red-500" 
                : "border-gray-300 dark:border-gray-600 focus:ring-baseball-green focus:border-baseball-green",
              className
            )}
            {...props}
          />
          
          {/* Clear button */}
          {clearable && hasValue && (
            <button
              type="button"
              onClick={onClear}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 touch-target p-2"
            >
              <X size={20} className="text-gray-400" />
            </button>
          )}
          
          {/* Success icon */}
          {successIcon && hasValue && !error && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <Check size={20} className="text-green-500" />
            </div>
          )}
        </div>
        
        {/* Helper or error text */}
        {(helper || error) && (
          <p className={cn(
            "mt-1 text-sm",
            error ? "text-red-500" : "text-gray-500 dark:text-gray-400"
          )}>
            {error || helper}
          </p>
        )}
      </div>
    );
  }
);

MobileInput.displayName = 'MobileInput';

// Mobile-optimized textarea
interface MobileTextareaProps extends TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  helper?: string;
  error?: string;
}

export const MobileTextarea = forwardRef<HTMLTextAreaElement, MobileTextareaProps>(
  ({ label, helper, error, className, ...props }, ref) => {
    return (
      <div className="mb-4">
        {label && (
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {label}
            {props.required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}
        
        <textarea
          ref={ref}
          className={cn(
            "w-full px-4 py-3",
            "text-base", // Prevents iOS zoom
            "bg-white dark:bg-gray-800",
            "border rounded-lg",
            "transition-all duration-200",
            "focus:outline-none focus:ring-2",
            "min-h-[96px]", // Comfortable touch target
            "resize-y",
            error 
              ? "border-red-500 focus:ring-red-500" 
              : "border-gray-300 dark:border-gray-600 focus:ring-baseball-green focus:border-baseball-green",
            className
          )}
          {...props}
        />
        
        {(helper || error) && (
          <p className={cn(
            "mt-1 text-sm",
            error ? "text-red-500" : "text-gray-500 dark:text-gray-400"
          )}>
            {error || helper}
          </p>
        )}
      </div>
    );
  }
);

MobileTextarea.displayName = 'MobileTextarea';

// Mobile-optimized select
interface MobileSelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string;
  helper?: string;
  error?: string;
  options: Array<{ value: string; label: string }>;
  placeholder?: string;
}

export const MobileSelect = forwardRef<HTMLSelectElement, MobileSelectProps>(
  ({ label, helper, error, options, placeholder, className, ...props }, ref) => {
    return (
      <div className="mb-4">
        {label && (
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {label}
            {props.required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}
        
        <select
          ref={ref}
          className={cn(
            "w-full px-4 py-3",
            "text-base", // Prevents iOS zoom
            "bg-white dark:bg-gray-800",
            "border rounded-lg",
            "transition-all duration-200",
            "focus:outline-none focus:ring-2",
            "touch-target", // Ensures 48px min height
            "appearance-none", // Remove default arrow
            "bg-no-repeat bg-right",
            "pr-10", // Space for custom arrow
            error 
              ? "border-red-500 focus:ring-red-500" 
              : "border-gray-300 dark:border-gray-600 focus:ring-baseball-green focus:border-baseball-green",
            className
          )}
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23666' d='M10.293 3.293L6 7.586 1.707 3.293A1 1 0 00.293 4.707l5 5a1 1 0 001.414 0l5-5a1 1 0 10-1.414-1.414z'/%3E%3C/svg%3E")`,
            backgroundPosition: 'right 1rem center'
          }}
          {...props}
        >
          {placeholder && (
            <option value="" disabled>
              {placeholder}
            </option>
          )}
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        
        {(helper || error) && (
          <p className={cn(
            "mt-1 text-sm",
            error ? "text-red-500" : "text-gray-500 dark:text-gray-400"
          )}>
            {error || helper}
          </p>
        )}
      </div>
    );
  }
);

MobileSelect.displayName = 'MobileSelect';
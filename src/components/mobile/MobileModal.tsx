import React, { useEffect, useState } from 'react';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface MobileModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  type?: 'full' | 'sheet';
  className?: string;
}

export const MobileModal: React.FC<MobileModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  type = 'sheet',
  className
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
      requestAnimationFrame(() => {
        setIsAnimating(true);
      });
    } else {
      setIsAnimating(false);
      setTimeout(() => {
        setIsVisible(false);
      }, 300);
    }
  }, [isOpen]);

  if (!isVisible) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className={cn(
          "fixed inset-0 bg-black/50 z-50",
          "transition-opacity duration-300",
          isAnimating ? "opacity-100" : "opacity-0"
        )}
        onClick={onClose}
      />

      {/* Modal Content */}
      <div
        className={cn(
          "fixed z-50",
          type === 'full' 
            ? "inset-0" 
            : "bottom-0 left-0 right-0 max-h-[90vh]",
          "bg-white dark:bg-gray-900",
          type === 'sheet' && "rounded-t-2xl",
          "transition-transform duration-300 ease-out",
          isAnimating 
            ? "translate-y-0" 
            : type === 'full' 
              ? "translate-y-full" 
              : "translate-y-full",
          className
        )}
      >
        {/* Header */}
        <div className={cn(
          "flex items-center justify-between",
          "p-4 border-b border-gray-200 dark:border-gray-700",
          type === 'full' && "safe-top"
        )}>
          {/* Drag handle for bottom sheet */}
          {type === 'sheet' && (
            <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-gray-300 dark:bg-gray-600 rounded-full" />
          )}
          
          <h2 className="text-lg font-semibold">
            {title || 'Modal'}
          </h2>
          
          <button
            onClick={onClose}
            className="touch-target p-2 -m-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div 
          className={cn(
            "overflow-y-auto",
            type === 'sheet' && "pb-safe-bottom",
            type === 'full' && "flex-1"
          )}
          style={{
            maxHeight: type === 'sheet' ? 'calc(90vh - 4rem)' : undefined
          }}
        >
          {children}
        </div>
      </div>
    </>
  );
};

// Action Sheet variant for quick actions
interface ActionSheetOption {
  label: string;
  onClick: () => void;
  icon?: React.ElementType;
  destructive?: boolean;
}

interface ActionSheetProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  options: ActionSheetOption[];
  cancelLabel?: string;
}

export const ActionSheet: React.FC<ActionSheetProps> = ({
  isOpen,
  onClose,
  title,
  options,
  cancelLabel = 'Cancel'
}) => {
  return (
    <MobileModal
      isOpen={isOpen}
      onClose={onClose}
      type="sheet"
      title={title}
    >
      <div className="py-2">
        {options.map((option, index) => {
          const Icon = option.icon;
          return (
            <button
              key={index}
              onClick={() => {
                option.onClick();
                onClose();
              }}
              className={cn(
                "w-full flex items-center gap-4 px-6 py-4",
                "text-left transition-colors duration-200",
                "active:bg-gray-100 dark:active:bg-gray-800",
                option.destructive && "text-red-500"
              )}
            >
              {Icon && <Icon size={20} />}
              <span className="text-base">{option.label}</span>
            </button>
          );
        })}
        
        <div className="border-t border-gray-200 dark:border-gray-700 mt-2 pt-2">
          <button
            onClick={onClose}
            className="w-full px-6 py-4 text-left text-base font-medium active:bg-gray-100 dark:active:bg-gray-800"
          >
            {cancelLabel}
          </button>
        </div>
      </div>
    </MobileModal>
  );
};
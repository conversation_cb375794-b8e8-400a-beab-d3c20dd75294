import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { Plus, X } from 'lucide-react';

interface FABAction {
  icon: React.ElementType;
  label: string;
  onClick: () => void;
  color?: string;
}

interface FloatingActionButtonProps {
  actions?: FABAction[];
  icon?: React.ElementType;
  onClick?: () => void;
  className?: string;
  position?: 'bottom-right' | 'bottom-left' | 'bottom-center';
}

export const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  actions,
  icon: Icon = Plus,
  onClick,
  className,
  position = 'bottom-right'
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const handleMainClick = () => {
    if (actions && actions.length > 0) {
      setIsExpanded(!isExpanded);
    } else if (onClick) {
      onClick();
    }
  };

  const handleActionClick = (action: FABAction) => {
    action.onClick();
    setIsExpanded(false);
  };

  const positionClasses = {
    'bottom-right': 'bottom-safe-bottom right-6',
    'bottom-left': 'bottom-safe-bottom left-6',
    'bottom-center': 'bottom-safe-bottom left-1/2 transform -translate-x-1/2'
  };

  return (
    <>
      {/* Backdrop */}
      {isExpanded && (
        <div
          className="fixed inset-0 bg-black/20 z-40 md:hidden"
          onClick={() => setIsExpanded(false)}
        />
      )}

      {/* FAB Container */}
      <div
        className={cn(
          "fixed z-50",
          positionClasses[position],
          "md:hidden", // Only show on mobile
          className
        )}
        style={{ paddingBottom: 'calc(1.5rem + env(safe-area-inset-bottom))' }}
      >
        {/* Speed dial actions */}
        {isExpanded && actions && (
          <div className="absolute bottom-full mb-4 right-0 space-y-3">
            {actions.map((action, index) => {
              const ActionIcon = action.icon;
              return (
                <div
                  key={index}
                  className={cn(
                    "flex items-center justify-end gap-3",
                    "animate-in slide-in-from-bottom-2 fade-in duration-200"
                  )}
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  <span className="bg-gray-800 text-white px-3 py-1 rounded-full text-sm whitespace-nowrap">
                    {action.label}
                  </span>
                  <button
                    onClick={() => handleActionClick(action)}
                    className={cn(
                      "w-12 h-12 rounded-full shadow-lg",
                      "flex items-center justify-center",
                      "transition-all duration-200",
                      "active:scale-90",
                      action.color || "bg-white dark:bg-gray-800"
                    )}
                  >
                    <ActionIcon size={20} className={action.color ? "text-white" : "text-gray-700 dark:text-gray-300"} />
                  </button>
                </div>
              );
            })}
          </div>
        )}

        {/* Main FAB */}
        <button
          onClick={handleMainClick}
          className={cn(
            "w-14 h-14 rounded-full shadow-lg",
            "bg-baseball-green text-white",
            "flex items-center justify-center",
            "transition-all duration-300",
            "active:scale-90",
            isExpanded && "rotate-45"
          )}
        >
          {isExpanded ? (
            <X size={24} />
          ) : (
            <Icon size={24} />
          )}
        </button>
      </div>
    </>
  );
};

// Mini FAB variant for secondary actions
export const MiniFAB: React.FC<{
  icon: React.ElementType;
  onClick: () => void;
  label?: string;
  className?: string;
  position?: 'left' | 'right';
}> = ({ icon: Icon, onClick, label, className, position = 'right' }) => {
  const positionClasses = position === 'left' 
    ? 'left-6' 
    : 'right-6';

  return (
    <button
      onClick={onClick}
      className={cn(
        "fixed z-40",
        positionClasses,
        "bottom-24", // Above main FAB
        "w-12 h-12 rounded-full shadow-md",
        "bg-white dark:bg-gray-800",
        "flex items-center justify-center",
        "transition-all duration-200",
        "active:scale-90",
        "md:hidden",
        className
      )}
      style={{ marginBottom: 'env(safe-area-inset-bottom)' }}
      aria-label={label}
    >
      <Icon size={20} className="text-gray-700 dark:text-gray-300" />
    </button>
  );
};
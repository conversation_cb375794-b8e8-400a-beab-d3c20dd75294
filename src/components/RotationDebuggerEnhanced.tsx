import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { LineupRotator, PlayerEligibilityCache, SeededRandom } from '../lib/utils-enhanced';
import type { Player, InningLineup, LineupRules } from '../lib/utils-enhanced';

// Create mock players for testing
const createMockPlayer = (name: string, id: string): Player => ({
  id,
  name,
  team_id: 'test-team',
  attendance: 'present' as const,
  positionRestrictions: [],
  positionPreferences: {
    pitcher: 'neutral',
    catcher: 'neutral',
    firstBase: 'neutral',
    secondBase: 'neutral',
    thirdBase: 'neutral',
    shortstop: 'neutral',
    leftField: 'neutral',
    centerField: 'neutral',
    rightField: 'neutral'
  }
});

export const RotationDebuggerEnhanced: React.FC = () => {
  const [debugOutput, setDebugOutput] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const runDetailedTest = () => {
    setIsRunning(true);
    const output: string[] = [];
    
    // Create test players
    const players = Array.from({ length: 12 }, (_, i) => 
      createMockPlayer(`Player${i + 1}`, `player-${i + 1}`)
    );

    // Create first inning
    const firstInning: InningLineup = {
      inning: 1,
      positions: {
        pitcher: 'Player1',
        catcher: 'Player2',
        firstBase: 'Player3',
        secondBase: 'Player4',
        thirdBase: 'Player5',
        shortstop: 'Player6',
        leftField: 'Player7',
        centerField: 'Player8',
        rightField: 'Player9',
        bench: ['Player10', 'Player11', 'Player12']
      }
    };

    output.push('=== DETAILED ROTATION TEST ===\n');
    output.push('First Inning Setup:');
    output.push('Field: P1, C2, 1B3, 2B4, 3B5, SS6, LF7, CF8, RF9');
    output.push('Bench: P10, P11, P12\n');

    // Test with rotateLineupEvery = 1
    const rules: LineupRules = {
      rotateLineupEvery: 1,
      limitBenchTime: true,
      maxConsecutiveBenchInnings: 2,
      allowPitcherRotation: false,
      allowCatcherRotation: false,
      respectPositionLockouts: false,
      equalPlayingTime: true
    };

    const eligibilityCache = new PlayerEligibilityCache();
    const random = new SeededRandom(42);
    const rotator = new LineupRotator(rules, eligibilityCache, random);
    
    // Capture debug output
    let debugMessages: string[] = [];
    const originalConsoleLog = console.log;
    console.log = (...args: any[]) => {
      const message = args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
      ).join(' ');
      
      if (message.includes('[ROTATION DEBUG')) {
        debugMessages.push(message);
      }
      originalConsoleLog(...args);
    };

    try {
      output.push('Testing Inning 1 -> 2 rotation...\n');
      
      const secondInning = rotator.rotateLineup(firstInning, players, 2);
      
      // Analyze what happened
      const positions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'thirdBase', 
                        'shortstop', 'leftField', 'centerField', 'rightField'];
      
      let changes = 0;
      output.push('Position changes:');
      positions.forEach(pos => {
        const before = (firstInning.positions as any)[pos];
        const after = (secondInning.positions as any)[pos];
        if (before !== after) {
          changes++;
          output.push(`  ${pos}: ${before} → ${after}`);
        }
      });
      
      if (changes === 0) {
        output.push('\n❌ NO CHANGES - Rotation did not occur!');
        
        // Add relevant debug messages
        output.push('\nRelevant debug messages:');
        debugMessages
          .filter(msg => 
            msg.includes('Rotation frequency check') ||
            msg.includes('Forced rotation check') ||
            msg.includes('No rotation needed') ||
            msg.includes('FORCED ROTATION') ||
            msg.includes('Creating rotation plan')
          )
          .forEach(msg => output.push(msg));
      } else {
        output.push(`\n✅ Rotation successful! ${changes} position changes.`);
      }
      
    } catch (error) {
      output.push(`\n❌ ERROR: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      // Restore console.log
      console.log = originalConsoleLog;
    }

    setDebugOutput(output);
    setIsRunning(false);
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Enhanced Rotation Debugger</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <Button 
            onClick={runDetailedTest} 
            disabled={isRunning}
            className="w-full"
          >
            {isRunning ? 'Running...' : 'Run Detailed Debug Test'}
          </Button>

          {debugOutput.length > 0 && (
            <Card className="p-4 bg-gray-50">
              <pre className="text-xs whitespace-pre-wrap font-mono">
                {debugOutput.join('\n')}
              </pre>
            </Card>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
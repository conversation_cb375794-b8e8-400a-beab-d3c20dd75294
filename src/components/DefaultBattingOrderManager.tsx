import React, { useState, useEffect } from 'react';
import { useTeam, Player } from '@/contexts/TeamContext';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { Save, X, RotateCcw, Circle } from 'lucide-react';
import { generateId } from '@/lib/utils-enhanced';

interface DefaultBattingOrderManagerProps {
  isOpen: boolean;
  onClose: () => void;
}


const DefaultBattingOrderManager: React.FC<DefaultBattingOrderManagerProps> = ({
  isOpen,
  onClose,
}) => {
  console.log('🏏 DefaultBattingOrderManager component rendered:', { isOpen });
  
  const { players, teamName, currentTeamId, getDefaultBattingOrder, setDefaultBattingOrder } = useTeam();
  const [battingOrder, setBattingOrder] = useState<string[]>([]);
  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Filter out players with empty names and get player names
  const validPlayers = players.filter(player => player.name.trim() !== '');
  const availablePlayers = validPlayers.map(p => p.name);
  
  console.log('🏏 Component state:', { 
    isOpen, 
    playerCount: players.length, 
    validPlayerCount: validPlayers.length,
    battingOrderLength: battingOrder.length,
    hasChanges,
    currentTeamId 
  });

  useEffect(() => {
    if (isOpen) {
      console.log('🏏 Initializing batting order dialog...');
      console.log('🏏 Available players:', availablePlayers);
      console.log('🏏 Valid players:', validPlayers.map(p => ({ id: p.id, name: p.name })));
      
      const defaultOrder = getDefaultBattingOrder();
      console.log('🏏 Default order from context:', defaultOrder);
      
      // Initialize batting order with empty slots for all available players
      const initialBattingOrder = Array(availablePlayers.length).fill("");
      console.log('🏏 Initial empty batting order:', initialBattingOrder);
      
      if (defaultOrder && defaultOrder.length > 0) {
        // Use existing default order - convert player IDs to names
        defaultOrder.forEach((playerId, index) => {
          const player = validPlayers.find(p => p.id === playerId);
          if (player && index < initialBattingOrder.length) {
            initialBattingOrder[index] = player.name;
            console.log(`🏏 Set position ${index} to ${player.name}`);
          } else {
            console.log(`🏏 Could not set position ${index} for playerId ${playerId}`);
          }
        });
      }
      
      console.log('🏏 Final initial batting order:', initialBattingOrder);
      setBattingOrder(initialBattingOrder);
      setHasChanges(false);
    }
  }, [isOpen, availablePlayers.length]);

  const getSelectOrdinal = (index: number) => {
    if (index === 0) return "1st";
    if (index === 1) return "2nd";
    if (index === 2) return "3rd";
    return `${index + 1}th`;
  };

  const isPlayerUsedElsewhere = (playerName: string, currentIndex: number) => {
    return battingOrder.some((selectedPlayer, index) => 
      index !== currentIndex && selectedPlayer === playerName
    );
  };

  const handlePlayerSelect = (index: number, playerName: string) => {
    console.log('🏏 handlePlayerSelect called:', { index, playerName, currentBattingOrder: battingOrder });
    
    const newBattingOrder = [...battingOrder];
    
    // If this player is used elsewhere, clear that position
    const existingIndex = battingOrder.findIndex((selectedPlayer, i) => 
      i !== index && selectedPlayer === playerName
    );
    
    if (existingIndex !== -1) {
      // Clear the existing position
      newBattingOrder[existingIndex] = "";
      console.log('🏏 Cleared existing position:', existingIndex);
    }
    
    // Set the new player at the current position
    newBattingOrder[index] = playerName;
    console.log('🏏 Updated batting order:', newBattingOrder);
    
    setBattingOrder(newBattingOrder);
    setHasChanges(true);
    console.log('🏏 Set hasChanges to true');
  };

  const resetToAlphabetical = () => {
    const alphabeticalOrder = [...availablePlayers].sort((a, b) => a.localeCompare(b));
    setBattingOrder(alphabeticalOrder);
    setHasChanges(true);
  };


  const handleSave = async () => {
    if (!currentTeamId) {
      toast.error('No team selected');
      return;
    }

    setIsSaving(true);
    try {
      console.log('🏏 DefaultBattingOrderManager: Starting save process');
      console.log('🏏 Current batting order:', battingOrder);
      console.log('🏏 Valid players:', validPlayers.map(p => ({ id: p.id, name: p.name })));
      
      // Convert player names back to IDs for storage, filtering out empty slots and invalid players
      const battingOrderIds = battingOrder
        .filter(playerName => playerName && playerName.trim() !== '') // Remove empty slots
        .map(playerName => {
          const player = validPlayers.find(p => p.name === playerName);
          if (!player) {
            console.warn(`🏏 Player "${playerName}" not found in valid players list`);
            return null;
          }
          return player.id;
        })
        .filter(Boolean) as string[];
      
      console.log('🏏 Filtered batting order IDs to save:', battingOrderIds);
      
      if (battingOrderIds.length === 0) {
        toast.error('No valid players selected for batting order');
        return;
      }
      
      // Save via TeamContext
      await setDefaultBattingOrder(battingOrderIds);
      
      toast.success('Default batting order saved! This will be used when creating new lineups.');
      setHasChanges(false);
      onClose();
    } catch (error) {
      console.error('🏏 Error saving default batting order:', error);
      toast.error(`Failed to save default batting order: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    if (hasChanges) {
      if (confirm('You have unsaved changes. Are you sure you want to cancel?')) {
        onClose();
      }
    } else {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleCancel}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            ⚾ Set Default Batting Order
          </DialogTitle>
          <DialogDescription>
            Arrange your entire roster in your preferred batting order. This will be automatically 
            applied to new lineups based on which players are attending.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Info box */}
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h4 className="font-semibold text-blue-900 mb-2">How it works:</h4>
            <ul className="text-sm text-blue-800 space-y-1 list-disc list-inside">
              <li><strong>Full roster ordering:</strong> Set batting positions for ALL {validPlayers.length} players</li>
              <li><strong>Attendance filtering:</strong> When creating lineups, only attending players are used</li>
              <li><strong>Order preserved:</strong> Relative batting order maintained among present players</li>
              <li><strong>Time saving:</strong> Apply your established order with one click</li>
            </ul>
          </div>

          {/* Example */}
          <div className="p-3 bg-amber-50 rounded-lg border border-amber-200">
            <p className="text-sm text-amber-800">
              <strong>Example:</strong> If your default order is John, Mike, Sarah, Tom and Mike is absent, 
              the batting order becomes: 1. John, 2. Sarah, 3. Tom
            </p>
          </div>

          {/* Controls */}
          <div className="flex justify-between items-center">
            <div>
              <h3 className="font-semibold">Batting Order ({availablePlayers.length} players)</h3>
              <p className="text-xs text-gray-500 mt-1">
                💡 <strong>Tip:</strong> Use the dropdowns to select players for each batting position
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={resetToAlphabetical}
              className="text-gray-600"
            >
              <RotateCcw className="w-4 h-4 mr-1" />
              Reset to A-Z
            </Button>
          </div>

          {/* Batting order interface - matching working pattern from BattingOrder.tsx */}
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {battingOrder.map((selectedPlayer, index) => (
              <div
                key={index}
                className={`grid grid-cols-3 items-center rounded-md overflow-hidden ${
                  index % 2 === 0 ? 'bg-baseball-lightgreen' : 'bg-baseball-lightblue'
                }`}
              >
                <div className="font-bold p-3 flex items-center">
                  <Circle className="mr-2 h-4 w-4 text-baseball-green" />
                  <span>Batting {getSelectOrdinal(index)}:</span>
                </div>
                <div className="col-span-2 p-2">
                  <Select
                    value={selectedPlayer || "none"}
                    onValueChange={(value) => {
                      console.log('🏏 onValueChange triggered:', { index, value, selectedPlayer });
                      handlePlayerSelect(index, value === "none" ? "" : value);
                    }}
                  >
                    <SelectTrigger className="h-9 bg-white border-baseball-green/30">
                      <SelectValue placeholder={`Choose a player to bat ${getSelectOrdinal(index)}`} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">-- Select Player --</SelectItem>
                      {availablePlayers
                        .sort((a, b) => a.localeCompare(b))
                        .map(playerName => (
                        <SelectItem key={playerName} value={playerName}>
                          {isPlayerUsedElsewhere(playerName, index) ? `{${playerName}}` : playerName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            ))}
          </div>

          {validPlayers.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <p>No players found. Add players to your roster first.</p>
            </div>
          )}
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={handleCancel}
          >
            <X className="w-4 h-4 mr-1" />
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={!hasChanges || battingOrder.length === 0 || isSaving}
            className="bg-baseball-green hover:bg-baseball-green/90"
          >
            <Save className="w-4 h-4 mr-1" />
            {isSaving ? 'Saving...' : 'Save Default Order'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DefaultBattingOrderManager;
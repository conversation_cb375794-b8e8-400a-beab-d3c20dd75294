import React, { useState, useEffect } from 'react';
import { useTeam } from '@/contexts/TeamContext';
import { cn } from '@/lib/utils';
import { 
  Play, 
  Pause, 
  RotateCcw, 
  Users, 
  Clock,
  ChevronUp,
  ChevronDown,
  UserPlus,
  UserMinus
} from 'lucide-react';
import { FloatingActionButton } from './mobile/FloatingActionButton';
import { SwipeableCard } from './mobile/SwipeableCard';
import { QuickActions, GameDayQuickActions } from './mobile/QuickActions';
import { toast } from 'sonner';

interface GameDayModeProps {
  lineupId: string;
  onExit: () => void;
}

export const GameDayMode: React.FC<GameDayModeProps> = ({ lineupId, onExit }) => {
  const { players, currentLineup } = useTeam();
  const [currentInning, setCurrentInning] = useState(1);
  const [isGameActive, setIsGameActive] = useState(false);
  const [gameTime, setGameTime] = useState(0);
  const [showSubstitutionPanel, setShowSubstitutionPanel] = useState(false);
  const [selectedPosition, setSelectedPosition] = useState<string | null>(null);
  
  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isGameActive) {
      interval = setInterval(() => {
        setGameTime(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isGameActive]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleInningChange = (direction: 'up' | 'down') => {
    if (direction === 'up') {
      setCurrentInning(prev => prev + 1);
      toast.success(`Advanced to inning ${currentInning + 1}`);
    } else if (currentInning > 1) {
      setCurrentInning(prev => prev - 1);
      toast.success(`Back to inning ${currentInning - 1}`);
    }
  };

  const handleQuickSubstitution = (positionKey: string, newPlayerId: string) => {
    // Implementation would update the lineup
    toast.success('Player substituted successfully');
    setShowSubstitutionPanel(false);
    setSelectedPosition(null);
  };

  // Simplified position display
  const positions = [
    { key: 'pitcher', label: 'P', row: 0, col: 1 },
    { key: 'catcher', label: 'C', row: 3, col: 1 },
    { key: 'firstBase', label: '1B', row: 2, col: 2 },
    { key: 'secondBase', label: '2B', row: 1, col: 2 },
    { key: 'thirdBase', label: '3B', row: 2, col: 0 },
    { key: 'shortstop', label: 'SS', row: 1, col: 0.5 },
    { key: 'leftField', label: 'LF', row: -1, col: 0 },
    { key: 'centerField', label: 'CF', row: -1, col: 1 },
    { key: 'rightField', label: 'RF', row: -1, col: 2 }
  ];

  return (
    <div className="min-h-screen bg-baseball-green/10 dark:bg-gray-900">
      {/* Game Header */}
      <div className="bg-baseball-navy text-white p-4 safe-top">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-xl font-bold">Game Day Mode</h1>
            <p className="text-sm opacity-80">{currentLineup?.name || 'Active Game'}</p>
          </div>
          <button
            onClick={onExit}
            className="touch-target px-4 py-2 bg-white/20 rounded-lg"
          >
            Exit
          </button>
        </div>
        
        {/* Game Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={() => setIsGameActive(!isGameActive)}
              className={cn(
                "touch-target-preferred rounded-full",
                "flex items-center justify-center",
                "transition-all duration-200",
                isGameActive 
                  ? "bg-red-500 text-white" 
                  : "bg-green-500 text-white"
              )}
            >
              {isGameActive ? <Pause size={24} /> : <Play size={24} />}
            </button>
            <div className="text-2xl font-mono">{formatTime(gameTime)}</div>
          </div>
          
          {/* Inning Control */}
          <div className="flex items-center gap-2 bg-white/10 rounded-lg p-2">
            <button
              onClick={() => handleInningChange('down')}
              className="touch-target p-1"
              disabled={currentInning === 1}
            >
              <ChevronDown size={20} />
            </button>
            <div className="px-3">
              <div className="text-xs">Inning</div>
              <div className="text-2xl font-bold">{currentInning}</div>
            </div>
            <button
              onClick={() => handleInningChange('up')}
              className="touch-target p-1"
            >
              <ChevronUp size={20} />
            </button>
          </div>
        </div>
      </div>

      {/* Field View */}
      <div className="p-4">
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-lg">
          <h2 className="font-bold mb-4">Current Positions</h2>
          
          {/* Simplified Field Diagram */}
          <div className="relative h-64 bg-baseball-green/20 rounded-lg">
            {positions.map((pos) => {
              const player = currentLineup?.innings?.[currentInning - 1]?.[pos.key];
              const left = 20 + (pos.col * 30);
              const top = 50 + (pos.row * 20);
              
              return (
                <button
                  key={pos.key}
                  onClick={() => {
                    setSelectedPosition(pos.key);
                    setShowSubstitutionPanel(true);
                  }}
                  className={cn(
                    "absolute transform -translate-x-1/2 -translate-y-1/2",
                    "w-16 h-16 rounded-full",
                    "bg-white dark:bg-gray-700 border-2 border-baseball-navy",
                    "flex flex-col items-center justify-center",
                    "touch-target-preferred",
                    "active:scale-95 transition-transform",
                    "shadow-md"
                  )}
                  style={{ left: `${left}%`, top: `${top}%` }}
                >
                  <span className="font-bold text-xs">{pos.label}</span>
                  <span className="text-xs truncate max-w-[60px]">
                    {player?.name?.split(' ')[0] || 'Empty'}
                  </span>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="px-4">
        <GameDayQuickActions
          onSubstitute={() => setShowSubstitutionPanel(true)}
          onRotate={() => toast.info('Rotation feature coming soon')}
          onViewLineup={() => toast.info('Lineup view coming soon')}
          onStats={() => toast.info('Stats feature coming soon')}
          currentInning={currentInning}
        />
      </div>

      {/* High Contrast Toggle */}
      <div className="fixed top-safe-top right-4 mt-20 z-40">
        <HighContrastToggle showLabel={false} />
      </div>

      {/* Substitution Panel */}
      {showSubstitutionPanel && (
        <div className="fixed inset-x-0 bottom-0 bg-white dark:bg-gray-900 rounded-t-2xl shadow-xl z-50 safe-bottom">
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold">
                Quick Substitution
                {selectedPosition && ` - ${selectedPosition}`}
              </h3>
              <button
                onClick={() => {
                  setShowSubstitutionPanel(false);
                  setSelectedPosition(null);
                }}
                className="touch-target p-2"
              >
                ×
              </button>
            </div>
            
            {/* Available players */}
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {players
                .filter(p => !currentLineup?.innings?.[currentInning - 1]?.[selectedPosition || ''])
                .map(player => (
                  <button
                    key={player.id}
                    onClick={() => handleQuickSubstitution(selectedPosition!, player.id)}
                    className="w-full p-3 bg-gray-100 dark:bg-gray-800 rounded-lg text-left touch-target"
                  >
                    <div className="font-medium">{player.name}</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      Available to play
                    </div>
                  </button>
                ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
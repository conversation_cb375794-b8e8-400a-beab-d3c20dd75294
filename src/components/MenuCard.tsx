
import { ReactNode } from "react";
import { Link } from "react-router-dom";
import { cn } from "@/lib/utils-enhanced";

interface MenuCardProps {
  icon: ReactNode;
  title: string;
  children: ReactNode;
  className?: string;
}

const MenuCard = ({ icon, title, children, className }: MenuCardProps) => {
  return (
    <div className={cn("rounded-lg border bg-card text-card-foreground shadow p-6", className)}>
      <div className="flex items-start gap-4">
        <div className="text-baseball-navy text-3xl">{icon}</div>
        <div>
          <h3 className="font-bold text-lg text-baseball-navy mb-2">{title}</h3>
          <div className="space-y-3">{children}</div>
        </div>
      </div>
    </div>
  );
};

interface MenuLinkProps {
  to: string;
  children: ReactNode;
}

export const MenuLink = ({ to, children }: MenuLinkProps) => {
  return (
    <Link 
      to={to} 
      className="block text-baseball-navy font-medium hover:underline"
    >
      {children}
    </Link>
  );
};

export default MenuCard;

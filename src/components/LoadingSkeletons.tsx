import { Skeleton } from "@/components/ui/skeleton";

export const TeamCardSkeleton = () => (
  <div className="bg-white rounded-lg shadow-md p-6 space-y-4">
    <Skeleton className="h-7 w-32" />
    <div className="space-y-2">
      <div className="flex justify-between">
        <Skeleton className="h-4 w-20" />
        <Skeleton className="h-4 w-16" />
      </div>
      <div className="flex justify-between">
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-4 w-16" />
      </div>
    </div>
    <Skeleton className="h-10 w-full" />
  </div>
);

export const LineupRowSkeleton = () => (
  <div className="flex items-center justify-between p-4 border-b">
    <div className="flex-1 space-y-2">
      <Skeleton className="h-5 w-48" />
      <Skeleton className="h-4 w-32" />
    </div>
    <div className="flex gap-2">
      <Skeleton className="h-8 w-8 rounded" />
      <Skeleton className="h-8 w-8 rounded" />
      <Skeleton className="h-8 w-8 rounded" />
    </div>
  </div>
);

export const PlayerRowSkeleton = () => (
  <div className="flex items-center gap-4 p-4 border-b">
    <Skeleton className="h-10 w-10 rounded-full" />
    <div className="flex-1 space-y-2">
      <Skeleton className="h-5 w-32" />
      <Skeleton className="h-4 w-24" />
    </div>
    <Skeleton className="h-8 w-20" />
  </div>
);

export const PlayerAttendanceSkeleton = () => (
  <div className="flex items-center justify-between p-3 rounded-md border bg-gray-100 border-gray-200">
    <div className="flex items-center gap-3">
      <Skeleton className="h-5 w-5" />
      <Skeleton className="h-5 w-32" />
    </div>
    <Skeleton className="h-5 w-5 rounded" />
  </div>
);

export const AttendancePageSkeleton = () => (
  <div className="space-y-6">
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-2">
        <Skeleton className="h-6 w-6" />
        <Skeleton className="h-6 w-40" />
      </div>
      <Skeleton className="h-10 w-36" />
    </div>
    
    <Skeleton className="h-5 w-96" />
    
    <Skeleton className="h-16 w-full rounded-md" />
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {Array.from({ length: 8 }).map((_, i) => (
        <PlayerAttendanceSkeleton key={i} />
      ))}
    </div>
    
    <div className="flex justify-center mt-8">
      <Skeleton className="h-10 w-48" />
    </div>
  </div>
);

export const DashboardSkeleton = () => (
  <div className="container mx-auto px-4 py-8">
    <div className="mb-8">
      <Skeleton className="h-8 w-48 mb-2" />
      <Skeleton className="h-4 w-64" />
    </div>
    
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      <TeamCardSkeleton />
      <TeamCardSkeleton />
      <TeamCardSkeleton />
    </div>
    
    <div className="mt-8">
      <Skeleton className="h-6 w-32 mb-4" />
      <div className="bg-white rounded-lg shadow">
        <LineupRowSkeleton />
        <LineupRowSkeleton />
        <LineupRowSkeleton />
      </div>
    </div>
  </div>
);
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { generateId } from '@/lib/utils-enhanced';
import { Player } from '@/contexts/TeamContext';
import { VisualPositionAssignment } from './VisualPositionAssignment';
import { 
  Users, 
  Trophy, 
  BalanceScale,
  ChevronRight,
  UserPlus,
  Target
} from 'lucide-react';

interface TeamOnboardingProps {
  onComplete: (teamType: string, players: Player[]) => void;
}

export function TeamOnboarding({ onComplete }: TeamOnboardingProps) {
  const [step, setStep] = useState(1);
  const [teamType, setTeamType] = useState('');
  const [players, setPlayers] = useState<Player[]>([]);
  const [newPlayerName, setNewPlayerName] = useState('');

  const handleAddPlayer = () => {
    if (newPlayerName.trim()) {
      const newPlayer: Player = {
        id: generateId(),
        name: newPlayerName.trim(),
        teamRoles: {}
      };
      setPlayers([...players, newPlayer]);
      setNewPlayerName('');
    }
  };

  const updatePlayer = (updatedPlayer: Player) => {
    setPlayers(players.map(p => p.id === updatedPlayer.id ? updatedPlayer : p));
  };

  const removePlayer = (playerId: string) => {
    setPlayers(players.filter(p => p.id !== playerId));
  };

  const completeOnboarding = () => {
    onComplete(teamType, players);
  };

  const steps = [
    {
      title: "Welcome to Dugout Boss!",
      content: (
        <div className="space-y-4">
          <p className="text-muted-foreground">
            Let's get your team set up in just a few minutes. First, what type of team are you coaching?
          </p>
          
          <div className="grid gap-4">
            <Card 
              className={cn(
                "cursor-pointer transition-all hover:shadow-lg",
                teamType === 'rec' && "ring-2 ring-blue-500"
              )}
              onClick={() => setTeamType('rec')}
            >
              <CardContent className="flex items-start gap-4 p-6">
                <div className="rounded-full bg-blue-100 p-3">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold mb-1">House/Rec League</h3>
                  <p className="text-sm text-muted-foreground">
                    Focus on equal playing time and player development. Perfect for younger players learning the game.
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card 
              className={cn(
                "cursor-pointer transition-all hover:shadow-lg",
                teamType === 'competitive' && "ring-2 ring-orange-500"
              )}
              onClick={() => setTeamType('competitive')}
            >
              <CardContent className="flex items-start gap-4 p-6">
                <div className="rounded-full bg-orange-100 p-3">
                  <Trophy className="h-6 w-6 text-orange-600" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold mb-1">Travel/Select Team</h3>
                  <p className="text-sm text-muted-foreground">
                    Play to win while meeting minimum play rules. Best players at key positions.
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card 
              className={cn(
                "cursor-pointer transition-all hover:shadow-lg",
                teamType === 'both' && "ring-2 ring-green-500"
              )}
              onClick={() => setTeamType('both')}
            >
              <CardContent className="flex items-start gap-4 p-6">
                <div className="rounded-full bg-green-100 p-3">
                  <BalanceScale className="h-6 w-6 text-green-600" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold mb-1">I Coach Both Types</h3>
                  <p className="text-sm text-muted-foreground">
                    Need flexibility for different situations. You can adjust settings game by game.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="flex justify-end pt-4">
            <Button 
              onClick={() => setStep(2)}
              disabled={!teamType}
              size="lg"
            >
              Next
              <ChevronRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      )
    },
    {
      title: "Add Your First Players",
      content: (
        <div className="space-y-4">
          <p className="text-muted-foreground">
            Let's add 3-5 players to get started. You can always add more later!
          </p>

          <div className="flex gap-2">
            <Input
              placeholder="Player name"
              value={newPlayerName}
              onChange={(e) => setNewPlayerName(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleAddPlayer()}
              className="flex-1"
            />
            <Button onClick={handleAddPlayer} variant="secondary">
              <UserPlus className="h-4 w-4 mr-2" />
              Add
            </Button>
          </div>

          {players.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-muted-foreground">
                Players ({players.length})
              </h4>
              <div className="space-y-2">
                {players.map(player => (
                  <div key={player.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="font-medium">{player.name}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removePlayer(player.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      Remove
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {players.length < 3 && (
            <p className="text-sm text-amber-600 bg-amber-50 p-3 rounded-lg">
              Add at least 3 players to continue
            </p>
          )}

          <div className="flex justify-between pt-4">
            <Button 
              onClick={() => setStep(1)}
              variant="outline"
              size="lg"
            >
              Back
            </Button>
            <Button 
              onClick={() => setStep(3)}
              disabled={players.length < 3}
              size="lg"
            >
              Next: Assign Positions
              <ChevronRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      )
    },
    {
      title: "Where Can They Play?",
      content: (
        <div className="space-y-4">
          <p className="text-muted-foreground">
            Click positions on the diamond where each player can play. More stars = better at that position!
          </p>

          {teamType === 'competitive' && (
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
              <p className="text-sm text-orange-700">
                <strong>Competitive Mode:</strong> Players will ONLY play positions you assign them. Be sure to assign enough positions!
              </p>
            </div>
          )}

          <div className="space-y-4 max-h-[400px] overflow-y-auto">
            {players.map(player => (
              <VisualPositionAssignment
                key={player.id}
                player={player}
                onChange={updatePlayer}
              />
            ))}
          </div>

          <div className="flex justify-between pt-4">
            <Button 
              onClick={() => setStep(2)}
              variant="outline"
              size="lg"
            >
              Back
            </Button>
            <Button 
              onClick={completeOnboarding}
              size="lg"
              className="bg-green-600 hover:bg-green-700"
            >
              <Target className="mr-2 h-4 w-4" />
              Start Generating Lineups!
            </Button>
          </div>
        </div>
      )
    }
  ];

  return (
    <div className="max-w-2xl mx-auto">
      <div className="mb-6">
        <Progress value={(step / steps.length) * 100} className="h-2" />
        <p className="text-sm text-muted-foreground mt-2">
          Step {step} of {steps.length}
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{steps[step - 1].title}</CardTitle>
        </CardHeader>
        <CardContent>
          {steps[step - 1].content}
        </CardContent>
      </Card>
    </div>
  );
}
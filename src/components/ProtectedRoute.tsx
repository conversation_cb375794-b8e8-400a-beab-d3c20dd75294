
import { Navigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { ReactNode, useEffect, useMemo } from "react";
import { toast } from "sonner";
import { isDemoAccount, isAdminAccount } from "@/utils/authUtils";

interface ProtectedRouteProps {
  children: ReactNode;
  requirePayment?: boolean;
}

const ProtectedRoute = ({ children, requirePayment = true }: ProtectedRouteProps) => {
  const { user, isPaid, loading, checkPaymentStatus } = useAuth();
  
  // Persistent payment status storage key
  const PAYMENT_STATUS_KEY = 'dugout_boss_payment_status';

  // Check account types using utilities - memoize to prevent recalculation
  const userIsAdmin = useMemo(() => isAdminAccount(user?.email), [user?.email]);
  const userIsDemo = useMemo(() => isDemoAccount(user?.email), [user?.email]);
  const isDemoMode = useMemo(() => localStorage.getItem('demo_mode') === 'true', []);

  // Check if we're in the process of logging in
  const isLoggingIn = useMemo(() => sessionStorage.getItem('is_logging_in') === 'true', []);

  // Debug logging
  console.log("ProtectedRoute: Current state", {
    userEmail: user?.email,
    userIsAdmin,
    userIsDemo,
    isPaid,
    loading,
    isDemoMode,
    isLoggingIn,
    requirePayment
  });

  // Single useEffect to handle all payment status checks
  useEffect(() => {
    if (!user) return;

    // Cache payment status for session recovery in both sessionStorage and localStorage
    if (isPaid) {
      sessionStorage.setItem('last_known_payment_status', 'paid');
      sessionStorage.setItem('last_known_user_id', user.id);
      
      // Also store in localStorage for more persistent recovery
      try {
        const existingStatus = localStorage.getItem(PAYMENT_STATUS_KEY);
        if (!existingStatus || JSON.parse(existingStatus).userId !== user.id) {
          localStorage.setItem(PAYMENT_STATUS_KEY, JSON.stringify({
            userId: user.id,
            isPaid: true,
            timestamp: Date.now()
          }));
        }
      } catch (e) {
        console.warn("Error storing payment status:", e);
      }
    } else if (user && !isPaid && !loading) {
      // Only clear cache if we're certain user is not paid (not during loading)
      sessionStorage.removeItem('last_known_payment_status');
      sessionStorage.removeItem('last_known_user_id');
      
      // Check localStorage before clearing
      try {
        const storedStatus = localStorage.getItem(PAYMENT_STATUS_KEY);
        if (storedStatus) {
          const parsed = JSON.parse(storedStatus);
          // If localStorage shows paid and it's recent, don't clear yet
          if (parsed.userId === user.id && parsed.isPaid && 
              Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {
            console.log("ProtectedRoute: localStorage shows user is paid, initiating re-check");
            checkPaymentStatus();
            return;
          }
        }
      } catch (e) {
        console.warn("Error reading stored payment status:", e);
      }
    }

    // Always check payment status on mount for non-demo/non-admin users
    // This ensures we catch any changes made by admins
    if (!userIsDemo && !userIsAdmin) {
      console.log("ProtectedRoute: Checking payment status for regular user");
      checkPaymentStatus();
      return;
    }

    let shouldCheckPayment = false;

    // For demo users, ensure they're marked as paid
    if (userIsDemo && !isPaid) {
      localStorage.setItem('demo_user_is_paid', 'true');
      localStorage.setItem('demo_mode', 'true');
      shouldCheckPayment = true;
    }

    // Special handling for admin accounts - always treat as paid
    if (userIsAdmin && !isPaid) {
      console.log("Admin account detected in ProtectedRoute - bypassing payment check");
      shouldCheckPayment = true;
    }

    // Only call checkPaymentStatus if needed
    if (shouldCheckPayment) {
      checkPaymentStatus();
    }
  }, [user?.id]); // Only re-run when user ID changes

  // During initial load or login process, show loading state
  // Also show loading if we're logging in and don't have a user yet
  if (loading || (isLoggingIn && !user)) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-baseball-green"></div>
        <span className="ml-3">
          {isLoggingIn ? "Logging you in..." : "Loading authentication status..."}
        </span>
      </div>
    );
  }

  // If we're still logging in but have a user, wait a bit more for auth to stabilize
  if (isLoggingIn && user && !userIsAdmin && !isPaid) {
    console.log("ProtectedRoute: Still logging in, waiting for auth to stabilize");
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-baseball-green"></div>
        <span className="ml-3">Finalizing login...</span>
      </div>
    );
  }

  // Not logged in, redirect to home page (unless in demo mode or logging in)
  if (!user && !isDemoMode && !isLoggingIn) {
    // Check multiple sources for cached payment status
    const cachedPaymentStatus = sessionStorage.getItem('last_known_payment_status');
    let cachedUserId = sessionStorage.getItem('last_known_user_id');
    
    // Also check localStorage
    let localStoragePaymentStatus = false;
    try {
      const storedStatus = localStorage.getItem(PAYMENT_STATUS_KEY);
      if (storedStatus) {
        const parsed = JSON.parse(storedStatus);
        if (parsed.isPaid && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {
          localStoragePaymentStatus = true;
          cachedUserId = cachedUserId || parsed.userId;
        }
      }
    } catch (e) {
      console.warn("Error reading localStorage payment status:", e);
    }
    
    if ((cachedPaymentStatus === 'paid' || localStoragePaymentStatus) && cachedUserId) {
      console.log("ProtectedRoute: No user but cached payment status is paid, allowing temporary access");
      
      // Try to recover the session
      const recoverSession = async () => {
        try {
          const { supabase } = await import('@/integrations/supabase/client');
          const { data, error } = await supabase.auth.getSession();
          if (data?.session) {
            console.log("ProtectedRoute: Session recovered!");
            window.location.reload();
          } else {
            // Try refreshing the session
            const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
            if (refreshData?.session) {
              console.log("ProtectedRoute: Session refreshed successfully!");
              window.location.reload();
            } else {
              // Give it a bit more time
              setTimeout(() => {
                if (!user) {
                  console.log("ProtectedRoute: Session recovery failed, redirecting to home");
                  toast.error("Session expired. Please log in again.");
                  sessionStorage.removeItem('last_known_payment_status');
                  sessionStorage.removeItem('last_known_user_id');
                  window.location.href = '/';
                }
              }, 5000); // Extended timeout
            }
          }
        } catch (error) {
          console.error("Session recovery error:", error);
        }
      };
      
      recoverSession();
      
      return (
        <div className="flex items-center justify-center h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-baseball-green"></div>
          <span className="ml-3">Refreshing session...</span>
        </div>
      );
    }
    
    console.log("ProtectedRoute: No user detected and not in demo mode, redirecting to home page");
    toast.error("Please log in to access this page");
    return <Navigate to="/" replace />;
  }

  // If in demo mode but no user, allow access
  if (!user && isDemoMode) {
    console.log("ProtectedRoute: Demo mode detected, allowing access without user");
    return <>{children}</>;
  }

  // Special handling for admin accounts - always allow access
  if (userIsAdmin) {
    return <>{children}</>;
  }

  // Check if session needs refresh but user is paid
  const sessionNeedsRefresh = sessionStorage.getItem('session_needs_refresh') === 'true';
  if (sessionNeedsRefresh && isPaid) {
    return (
      <div className="flex flex-col items-center justify-center h-screen p-4">
        <div className="max-w-md text-center space-y-4">
          <h2 className="text-2xl font-bold text-gray-900">Session Refresh Needed</h2>
          <p className="text-gray-600">
            Your session has expired. Please refresh to continue.
          </p>
          <button
            onClick={async () => {
              sessionStorage.removeItem('session_needs_refresh');
              const { supabase } = await import('@/integrations/supabase/client');
              await supabase.auth.refreshSession();
              window.location.reload();
            }}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition"
          >
            Refresh Session
          </button>
        </div>
      </div>
    );
  }

  // Strict payment enforcement - redirect unpaid users to pricing
  if (requirePayment && !isPaid && !userIsDemo && !userIsAdmin) {
    // Check localStorage one more time before showing payment required
    let shouldShowPaymentRequired = true;
    try {
      const storedStatus = localStorage.getItem(PAYMENT_STATUS_KEY);
      if (storedStatus) {
        const parsed = JSON.parse(storedStatus);
        if (parsed.userId === user?.id && parsed.isPaid && 
            Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {
          console.log("ProtectedRoute: localStorage shows paid, initiating payment check");
          checkPaymentStatus();
          shouldShowPaymentRequired = false;
        }
      }
    } catch (e) {
      console.warn("Error checking stored payment status:", e);
    }
    
    if (!shouldShowPaymentRequired) {
      return (
        <div className="flex items-center justify-center h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-baseball-green"></div>
          <span className="ml-3">Verifying payment status...</span>
        </div>
      );
    }
    
    console.log("User not paid, showing payment required message", { 
      email: user?.email, 
      isPaid, 
      userIsDemo, 
      userIsAdmin 
    });
    
    // Clear any cached auth state that might be allowing bypass
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_bypass_payment');
    }
    
    // Show a message with a refresh option
    return (
      <div className="flex flex-col items-center justify-center h-screen p-4">
        <div className="max-w-md text-center space-y-4">
          <h2 className="text-2xl font-bold text-gray-900">Payment Required</h2>
          <p className="text-gray-600">
            You need a paid subscription to access this feature.
          </p>
          <p className="text-sm text-gray-500">
            If an administrator has recently upgraded your account or if you were inactive for a while, click the button below to refresh your status.
          </p>
          <div className="space-y-2">
            <button
              onClick={async () => {
                try {
                  // Try to refresh the session first
                  const { supabase } = await import('@/integrations/supabase/client');
                  const { data, error } = await supabase.auth.refreshSession();
                  if (!error && data?.session) {
                    console.log("Session refreshed successfully");
                  }
                  // Then check payment status
                  await checkPaymentStatus();
                  // Finally reload
                  setTimeout(() => window.location.reload(), 500);
                } catch (error) {
                  console.error("Error refreshing status:", error);
                  window.location.reload();
                }
              }}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition"
            >
              Refresh Session & Payment Status
            </button>
            <a
              href="/pricing"
              className="block w-full px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition"
            >
              View Pricing Plans
            </a>
          </div>
        </div>
      </div>
    );
  }

  // Additional safety check: if user exists but payment status is unknown, verify it
  if (requirePayment && user && !userIsDemo && !userIsAdmin && isPaid === undefined) {
    console.log("Payment status undefined, forcing verification");
    toast.warning("Verifying payment status...");
    return <Navigate to="/pricing" replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;

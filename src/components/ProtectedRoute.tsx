
import { Navigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { ReactNode, useEffect, useMemo } from "react";
import { toast } from "sonner";
import { isDemoAccount, isAdminAccount } from "@/utils/authUtils";

interface ProtectedRouteProps {
  children: ReactNode;
  requirePayment?: boolean;
}

const ProtectedRoute = ({ children, requirePayment = true }: ProtectedRouteProps) => {
  const { user, isPaid, loading, checkPaymentStatus } = useAuth();

  // Check account types using utilities - memoize to prevent recalculation
  const userIsAdmin = useMemo(() => isAdminAccount(user?.email), [user?.email]);
  const userIsDemo = useMemo(() => isDemoAccount(user?.email), [user?.email]);
  const isDemoMode = useMemo(() => localStorage.getItem('demo_mode') === 'true', []);

  // Check if we're in the process of logging in
  const isLoggingIn = useMemo(() => sessionStorage.getItem('is_logging_in') === 'true', []);

  // Debug logging
  console.log("ProtectedRoute: Current state", {
    userEmail: user?.email,
    userIsAdmin,
    userIsDemo,
    isPaid,
    loading,
    isDemoMode,
    isLoggingIn,
    requirePayment
  });

  // Single useEffect to handle all payment status checks
  useEffect(() => {
    if (!user) return;

    // Always check payment status on mount for non-demo/non-admin users
    // This ensures we catch any changes made by admins
    if (!userIsDemo && !userIsAdmin) {
      console.log("ProtectedRoute: Checking payment status for regular user");
      checkPaymentStatus();
      return;
    }

    let shouldCheckPayment = false;

    // For demo users, ensure they're marked as paid
    if (userIsDemo && !isPaid) {
      localStorage.setItem('demo_user_is_paid', 'true');
      localStorage.setItem('demo_mode', 'true');
      shouldCheckPayment = true;
    }

    // Special handling for admin accounts - always treat as paid
    if (userIsAdmin && !isPaid) {
      console.log("Admin account detected in ProtectedRoute - bypassing payment check");
      shouldCheckPayment = true;
    }

    // Only call checkPaymentStatus if needed
    if (shouldCheckPayment) {
      checkPaymentStatus();
    }
  }, [user?.id]); // Only re-run when user ID changes

  // During initial load or login process, show loading state
  // Also show loading if we're logging in and don't have a user yet
  if (loading || (isLoggingIn && !user)) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-baseball-green"></div>
        <span className="ml-3">
          {isLoggingIn ? "Logging you in..." : "Loading authentication status..."}
        </span>
      </div>
    );
  }

  // If we're still logging in but have a user, wait a bit more for auth to stabilize
  if (isLoggingIn && user && !userIsAdmin && !isPaid) {
    console.log("ProtectedRoute: Still logging in, waiting for auth to stabilize");
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-baseball-green"></div>
        <span className="ml-3">Finalizing login...</span>
      </div>
    );
  }

  // Not logged in, redirect to home page (unless in demo mode or logging in)
  if (!user && !isDemoMode && !isLoggingIn) {
    console.log("ProtectedRoute: No user detected and not in demo mode, redirecting to home page");
    toast.error("Please log in to access this page");
    return <Navigate to="/" replace />;
  }

  // If in demo mode but no user, allow access
  if (!user && isDemoMode) {
    console.log("ProtectedRoute: Demo mode detected, allowing access without user");
    return <>{children}</>;
  }

  // Special handling for admin accounts - always allow access
  if (userIsAdmin) {
    return <>{children}</>;
  }

  // Strict payment enforcement - redirect unpaid users to pricing
  if (requirePayment && !isPaid && !userIsDemo && !userIsAdmin) {
    console.log("User not paid, showing payment required message", { 
      email: user?.email, 
      isPaid, 
      userIsDemo, 
      userIsAdmin 
    });
    
    // Clear any cached auth state that might be allowing bypass
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_bypass_payment');
    }
    
    // Show a message with a refresh option
    return (
      <div className="flex flex-col items-center justify-center h-screen p-4">
        <div className="max-w-md text-center space-y-4">
          <h2 className="text-2xl font-bold text-gray-900">Payment Required</h2>
          <p className="text-gray-600">
            You need a paid subscription to access this feature.
          </p>
          <p className="text-sm text-gray-500">
            If an administrator has recently upgraded your account, click the button below to refresh your status.
          </p>
          <div className="space-y-2">
            <button
              onClick={() => {
                checkPaymentStatus();
                window.location.reload();
              }}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition"
            >
              Refresh Payment Status
            </button>
            <a
              href="/pricing"
              className="block w-full px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition"
            >
              View Pricing Plans
            </a>
          </div>
        </div>
      </div>
    );
  }

  // Additional safety check: if user exists but payment status is unknown, verify it
  if (requirePayment && user && !userIsDemo && !userIsAdmin && isPaid === undefined) {
    console.log("Payment status undefined, forcing verification");
    toast.warning("Verifying payment status...");
    return <Navigate to="/pricing" replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;

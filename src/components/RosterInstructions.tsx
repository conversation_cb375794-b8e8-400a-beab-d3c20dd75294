import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp, HelpCircle, Star, CheckCircle, AlertCircle, XCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface RosterInstructionsProps {
  className?: string;
  defaultExpanded?: boolean;
}

export const RosterInstructions: React.FC<RosterInstructionsProps> = ({ 
  className, 
  defaultExpanded = false 
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  return (
    <Card className={cn("border-gray-200", className)}>
      <CardContent className="p-3">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full flex items-center justify-between text-left hover:opacity-80 transition-opacity"
        >
          <div className="flex items-center gap-2">
            <HelpCircle className="w-4 h-4 text-gray-500" />
            <span className="text-sm text-gray-700">Position Guide</span>
          </div>
          {isExpanded ? (
            <ChevronUp className="w-4 h-4 text-gray-400" />
          ) : (
            <ChevronDown className="w-4 h-4 text-gray-400" />
          )}
        </button>

        {isExpanded && (
          <div className="mt-3 space-y-1 text-sm">
            <div className="flex items-center gap-2">
              <Star className="w-4 h-4 text-green-600" />
              <span><span className="font-medium">Primary</span> = Your best player here</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-blue-600" />
              <span><span className="font-medium">In the Mix</span> = They play here regularly</span>
            </div>
            <div className="flex items-center gap-2">
              <AlertCircle className="w-4 h-4 text-yellow-600" />
              <span><span className="font-medium">Emergency</span> = Can fill in if needed</span>
            </div>
            <div className="flex items-center gap-2">
              <XCircle className="w-4 h-4 text-red-600" />
              <span><span className="font-medium">Never</span> = Don't put them here</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
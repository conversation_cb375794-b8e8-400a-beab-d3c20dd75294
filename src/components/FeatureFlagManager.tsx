import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  FlaskConical, 
  Eye, 
  EyeOff, 
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react';
import { toast } from 'sonner';
import { isNewLineupGenerationEnabled, enableNewLineupGeneration } from '@/lib/lineup-generation/adapter';
import { LineupDebugger } from '@/lib/lineup-generation/debug';

interface FeatureFlagManagerProps {
  teamId?: string;
  onClose?: () => void;
}

export const FeatureFlagManager: React.FC<FeatureFlagManagerProps> = ({ teamId, onClose }) => {
  const [newLineupEnabled, setNewLineupEnabled] = useState(false);
  const [debugEnabled, setDebugEnabled] = useState(false);
  const [shadowMode, setShadowMode] = useState(false);
  
  useEffect(() => {
    // Load current states
    setNewLineupEnabled(isNewLineupGenerationEnabled(teamId));
    setDebugEnabled(LineupDebugger.isEnabled(teamId || 'global'));
    setShadowMode(localStorage.getItem('lineup-shadow-mode') === 'true');
  }, [teamId]);
  
  const handleNewLineupToggle = (enabled: boolean) => {
    if (enabled) {
      enableNewLineupGeneration(teamId);
      toast.success('New lineup algorithm enabled');
    } else {
      if (teamId) {
        localStorage.removeItem(`new-lineup-gen-${teamId}`);
      } else {
        localStorage.removeItem('new-lineup-generation');
      }
      toast.info('Reverted to legacy lineup algorithm');
    }
    setNewLineupEnabled(enabled);
  };
  
  const handleDebugToggle = (enabled: boolean) => {
    if (enabled) {
      if (teamId) {
        LineupDebugger.enableForTeam(teamId);
      } else {
        LineupDebugger.enableGlobal();
      }
      toast.success('Debug logging enabled - check console');
    } else {
      if (teamId) {
        LineupDebugger.disableForTeam(teamId);
      } else {
        LineupDebugger.disableGlobal();
      }
      toast.info('Debug logging disabled');
    }
    setDebugEnabled(enabled);
  };
  
  const handleShadowModeToggle = (enabled: boolean) => {
    if (enabled) {
      localStorage.setItem('lineup-shadow-mode', 'true');
      toast.success('Shadow mode enabled - both algorithms will run');
    } else {
      localStorage.removeItem('lineup-shadow-mode');
      toast.info('Shadow mode disabled');
    }
    setShadowMode(enabled);
  };
  
  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FlaskConical className="h-5 w-5" />
          Feature Flags - Lineup Algorithm
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* New Algorithm Toggle */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="new-lineup" className="text-base font-medium">
                New Lineup Generation Algorithm
              </Label>
              <p className="text-sm text-gray-600">
                Enable the rewritten lineup algorithm with improved position enforcement
              </p>
            </div>
            <Switch
              id="new-lineup"
              checked={newLineupEnabled}
              onCheckedChange={handleNewLineupToggle}
            />
          </div>
          {newLineupEnabled && (
            <div className="pl-6 space-y-2">
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                <div className="text-sm">
                  <strong>Fixes:</strong> Players only assigned to positions in their pools
                </div>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                <div className="text-sm">
                  <strong>Performance:</strong> Generation time &lt; 100ms
                </div>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                <div className="text-sm">
                  <strong>Architecture:</strong> Modular, testable, maintainable
                </div>
              </div>
            </div>
          )}
        </div>
        
        {/* Debug Mode Toggle */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="debug" className="text-base font-medium">
                Debug Logging
              </Label>
              <p className="text-sm text-gray-600">
                Log detailed information about lineup generation to console
              </p>
            </div>
            <Switch
              id="debug"
              checked={debugEnabled}
              onCheckedChange={handleDebugToggle}
            />
          </div>
          {debugEnabled && (
            <div className="pl-6">
              <Badge variant="secondary" className="text-xs">
                <Eye className="h-3 w-3 mr-1" />
                Check browser console for logs
              </Badge>
            </div>
          )}
        </div>
        
        {/* Shadow Mode Toggle */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="shadow" className="text-base font-medium">
                Shadow Mode
              </Label>
              <p className="text-sm text-gray-600">
                Run both algorithms and compare results (uses legacy output)
              </p>
            </div>
            <Switch
              id="shadow"
              checked={shadowMode}
              onCheckedChange={handleShadowModeToggle}
              disabled={newLineupEnabled} // Can't use shadow mode with new algorithm
            />
          </div>
          {shadowMode && !newLineupEnabled && (
            <div className="pl-6">
              <Badge variant="outline" className="text-xs">
                <AlertTriangle className="h-3 w-3 mr-1" />
                Results logged to console
              </Badge>
            </div>
          )}
        </div>
        
        {/* Status Summary */}
        <div className="pt-4 border-t">
          <div className="flex items-center gap-2 text-sm">
            <Info className="h-4 w-4 text-blue-600" />
            <span className="font-medium">Current Status:</span>
            {newLineupEnabled ? (
              <Badge className="bg-green-100 text-green-700">New Algorithm Active</Badge>
            ) : (
              <Badge variant="secondary">Legacy Algorithm Active</Badge>
            )}
            {teamId && (
              <Badge variant="outline">Team-specific</Badge>
            )}
          </div>
        </div>
        
        {onClose && (
          <div className="pt-4">
            <Button onClick={onClose} variant="outline" className="w-full">
              Done
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
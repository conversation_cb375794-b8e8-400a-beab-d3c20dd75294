import React, { useRef, useState, useEffect, useCallback } from 'react';
import { cn } from '@/lib/utils-enhanced';

interface VirtualListProps<T> {
  items: T[];
  itemHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  containerHeight: number;
  overscan?: number;
  className?: string;
  getItemKey?: (item: T, index: number) => string | number;
}

export function VirtualList<T>({
  items,
  itemHeight,
  renderItem,
  containerHeight,
  overscan = 3,
  className,
  getItemKey = (_, index) => index
}: VirtualListProps<T>) {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [scrollTop, setScrollTop] = useState(0);
  
  // Calculate visible range
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );
  
  const visibleItems = items.slice(startIndex, endIndex);
  const totalHeight = items.length * itemHeight;
  const offsetY = startIndex * itemHeight;
  
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);
  
  return (
    <div
      ref={scrollRef}
      className={cn("overflow-auto", className)}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div
          style={{
            transform: `translateY(${offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0
          }}
        >
          {visibleItems.map((item, index) => (
            <div
              key={getItemKey(item, startIndex + index)}
              style={{ height: itemHeight }}
            >
              {renderItem(item, startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Dynamic height virtual list for variable item heights
interface DynamicVirtualListProps<T> {
  items: T[];
  estimatedItemHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  containerHeight: number;
  overscan?: number;
  className?: string;
  getItemKey?: (item: T, index: number) => string | number;
}

export function DynamicVirtualList<T>({
  items,
  estimatedItemHeight,
  renderItem,
  containerHeight,
  overscan = 3,
  className,
  getItemKey = (_, index) => index
}: DynamicVirtualListProps<T>) {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [scrollTop, setScrollTop] = useState(0);
  const [itemHeights, setItemHeights] = useState<Map<number, number>>(new Map());
  const measurementCache = useRef<Map<number, number>>(new Map());
  
  // Calculate positions based on measured heights
  const getItemPosition = useCallback((index: number): number => {
    let position = 0;
    for (let i = 0; i < index; i++) {
      position += itemHeights.get(i) || estimatedItemHeight;
    }
    return position;
  }, [itemHeights, estimatedItemHeight]);
  
  // Find visible range
  const getVisibleRange = useCallback(() => {
    let accumulatedHeight = 0;
    let startIndex = 0;
    let endIndex = items.length;
    
    for (let i = 0; i < items.length; i++) {
      const height = itemHeights.get(i) || estimatedItemHeight;
      
      if (accumulatedHeight + height < scrollTop - overscan * estimatedItemHeight) {
        startIndex = i + 1;
      }
      
      if (accumulatedHeight > scrollTop + containerHeight + overscan * estimatedItemHeight) {
        endIndex = i;
        break;
      }
      
      accumulatedHeight += height;
    }
    
    return { startIndex: Math.max(0, startIndex), endIndex };
  }, [scrollTop, containerHeight, items.length, itemHeights, estimatedItemHeight, overscan]);
  
  const { startIndex, endIndex } = getVisibleRange();
  const visibleItems = items.slice(startIndex, endIndex);
  
  // Calculate total height
  const totalHeight = items.reduce((acc, _, index) => {
    return acc + (itemHeights.get(index) || estimatedItemHeight);
  }, 0);
  
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);
  
  // Measure item heights
  const measureItem = useCallback((index: number, element: HTMLDivElement | null) => {
    if (!element) return;
    
    const height = element.getBoundingClientRect().height;
    if (height !== itemHeights.get(index)) {
      setItemHeights(prev => {
        const next = new Map(prev);
        next.set(index, height);
        return next;
      });
    }
  }, [itemHeights]);
  
  return (
    <div
      ref={scrollRef}
      className={cn("overflow-auto", className)}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        {visibleItems.map((item, index) => {
          const actualIndex = startIndex + index;
          const top = getItemPosition(actualIndex);
          
          return (
            <div
              key={getItemKey(item, actualIndex)}
              ref={(el) => measureItem(actualIndex, el)}
              style={{
                position: 'absolute',
                top,
                left: 0,
                right: 0
              }}
            >
              {renderItem(item, actualIndex)}
            </div>
          );
        })}
      </div>
    </div>
  );
}

// Specialized virtual list for players
export function VirtualPlayerList({
  players,
  renderPlayer,
  className
}: {
  players: any[];
  renderPlayer: (player: any, index: number) => React.ReactNode;
  className?: string;
}) {
  const [containerHeight, setContainerHeight] = useState(600);
  const containerRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const updateHeight = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setContainerHeight(rect.height);
      }
    };
    
    updateHeight();
    window.addEventListener('resize', updateHeight);
    return () => window.removeEventListener('resize', updateHeight);
  }, []);
  
  if (players.length < 50) {
    // Don't use virtual scrolling for small lists
    return (
      <div ref={containerRef} className={className}>
        {players.map((player, index) => (
          <div key={player.id || index}>
            {renderPlayer(player, index)}
          </div>
        ))}
      </div>
    );
  }
  
  return (
    <div ref={containerRef} className={cn("h-full", className)}>
      <VirtualList
        items={players}
        itemHeight={64} // Approximate height of player row
        containerHeight={containerHeight}
        renderItem={renderPlayer}
        getItemKey={(player) => player.id}
      />
    </div>
  );
}
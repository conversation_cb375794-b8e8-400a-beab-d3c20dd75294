import React from 'react';
import { cn } from '@/lib/utils';
import { ChevronRight } from 'lucide-react';

interface Column<T> {
  key: keyof T | string;
  label: string;
  render?: (item: T) => React.ReactNode;
  className?: string;
  priority?: 'high' | 'medium' | 'low'; // For responsive hiding
}

interface ResponsiveTableProps<T> {
  data: T[];
  columns: Column<T>[];
  onRowClick?: (item: T) => void;
  keyExtractor: (item: T) => string;
  className?: string;
  emptyMessage?: string;
  mobileRenderCard?: (item: T) => React.ReactNode;
}

export function ResponsiveTable<T>({
  data,
  columns,
  onRowClick,
  keyExtractor,
  className,
  emptyMessage = "No data available",
  mobileRenderCard
}: ResponsiveTableProps<T>) {
  
  // Default mobile card renderer
  const defaultMobileCard = (item: T) => {
    const highPriorityColumns = columns.filter(col => col.priority === 'high' || !col.priority);
    const otherColumns = columns.filter(col => col.priority === 'medium' || col.priority === 'low');
    
    return (
      <div className="space-y-2">
        {/* High priority items shown prominently */}
        <div className="flex justify-between items-start">
          <div className="space-y-1">
            {highPriorityColumns.map((col) => (
              <div key={col.key as string}>
                {col.render ? col.render(item) : (
                  <span className="font-medium">
                    {String(item[col.key as keyof T])}
                  </span>
                )}
              </div>
            ))}
          </div>
          {onRowClick && (
            <ChevronRight size={20} className="text-gray-400 flex-shrink-0 mt-1" />
          )}
        </div>
        
        {/* Other items shown as label: value */}
        {otherColumns.length > 0 && (
          <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
            {otherColumns.map((col) => (
              <div key={col.key as string} className="flex justify-between">
                <span>{col.label}:</span>
                <span>
                  {col.render ? col.render(item) : String(item[col.key as keyof T])}
                </span>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  if (data.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        {emptyMessage}
      </div>
    );
  }

  return (
    <>
      {/* Desktop Table */}
      <div className={cn("hidden md:block overflow-x-auto", className)}>
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-200 dark:border-gray-700">
              {columns.map((col) => (
                <th
                  key={col.key as string}
                  className={cn(
                    "text-left py-3 px-4 font-medium text-gray-700 dark:text-gray-300",
                    col.className
                  )}
                >
                  {col.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.map((item) => (
              <tr
                key={keyExtractor(item)}
                onClick={() => onRowClick?.(item)}
                className={cn(
                  "border-b border-gray-200 dark:border-gray-700",
                  "transition-colors duration-200",
                  onRowClick && "cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800"
                )}
              >
                {columns.map((col) => (
                  <td
                    key={col.key as string}
                    className={cn("py-3 px-4", col.className)}
                  >
                    {col.render ? col.render(item) : String(item[col.key as keyof T])}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile Cards */}
      <div className="md:hidden space-y-2">
        {data.map((item) => (
          <div
            key={keyExtractor(item)}
            onClick={() => onRowClick?.(item)}
            className={cn(
              "mobile-card",
              "transition-all duration-200",
              onRowClick && "cursor-pointer active:scale-[0.98] active:bg-gray-50 dark:active:bg-gray-800"
            )}
          >
            {mobileRenderCard ? mobileRenderCard(item) : defaultMobileCard(item)}
          </div>
        ))}
      </div>
    </>
  );
}

// Example usage with typed data
interface Player {
  id: string;
  name: string;
  position: string;
  battingAvg: number;
  gamesPlayed: number;
}

export const PlayerTable: React.FC<{
  players: Player[];
  onPlayerClick?: (player: Player) => void;
}> = ({ players, onPlayerClick }) => {
  const columns: Column<Player>[] = [
    {
      key: 'name',
      label: 'Name',
      priority: 'high',
      render: (player) => (
        <div>
          <div className="font-medium">{player.name}</div>
          <div className="text-sm text-gray-600 md:hidden">{player.position}</div>
        </div>
      )
    },
    {
      key: 'position',
      label: 'Position',
      priority: 'medium',
      className: 'hidden md:table-cell'
    },
    {
      key: 'battingAvg',
      label: 'AVG',
      priority: 'high',
      render: (player) => (
        <span className="font-mono">.{player.battingAvg.toFixed(3).slice(2)}</span>
      )
    },
    {
      key: 'gamesPlayed',
      label: 'Games',
      priority: 'low'
    }
  ];

  return (
    <ResponsiveTable
      data={players}
      columns={columns}
      onRowClick={onPlayerClick}
      keyExtractor={(player) => player.id}
      emptyMessage="No players in roster"
    />
  );
};
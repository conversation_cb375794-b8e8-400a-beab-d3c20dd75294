import React, { useState } from 'react';
import { Player, PlayerRating } from '@/contexts/TeamContext';
import PlayerRatingManager from './PlayerRatingManager';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Test component to verify the new rating system works
const PlayerRatingTest: React.FC = () => {
  const [testPlayer, setTestPlayer] = useState<Player>({
    id: 'test-1',
    name: 'Test Player',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: {
      pitcher: 5,
      catcher: 3,
      firstBase: 4
    },
    isStarPlayer: true,
    positionPreferences: {}
  });

  const handleRatingChange = (playerId: string, position: string, rating: PlayerRating | null) => {
    console.log(`Rating change: ${playerId} - ${position} = ${rating}`);
    setTestPlayer(prev => ({
      ...prev,
      positionRatings: {
        ...prev.positionRatings,
        [position]: rating || undefined
      }
    }));
  };

  const handleStarPlayerChange = (playerId: string, isStarPlayer: boolean) => {
    console.log(`Star player change: ${playerId} = ${isStarPlayer}`);
    setTestPlayer(prev => ({
      ...prev,
      isStarPlayer
    }));
  };

  return (
    <div className="p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Player Rating System Test</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium mb-2">Current Player Data:</h3>
              <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
                {JSON.stringify(testPlayer, null, 2)}
              </pre>
            </div>
            
            <div>
              <h3 className="font-medium mb-2">Rating Manager Component:</h3>
              <PlayerRatingManager
                player={testPlayer}
                onRatingChange={handleRatingChange}
                onStarPlayerChange={handleStarPlayerChange}
                disabled={false}
                compact={false}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PlayerRatingTest;

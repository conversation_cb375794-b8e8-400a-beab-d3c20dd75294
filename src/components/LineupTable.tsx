import React, { useMemo, useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { useTeam } from "@/contexts/TeamContext";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CalendarDays, Trophy, Eye, FileText, Users, Trash2, Edit2, MoreVertical, ArrowRightLeft } from "lucide-react";
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";
import WinLossToggle from "@/components/WinLossToggle";
import { toast } from "sonner";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { supabase } from "@/integrations/supabase/client";

interface LineupTableProps {
  type?: "retrieve" | "basic";
}

const LineupTable: React.FC<LineupTableProps> = ({ type = "retrieve" }) => {
  console.log("🔍 LineupTable: Component rendered with type:", type);
  const { lineups, setLineups, removeLineup, refreshTeamData, updateLineup } = useTeam();
  const [expandedSeries, setExpandedSeries] = useState<string[]>([]);
  const [editingSeriesId, setEditingSeriesId] = useState<string | null>(null);
  const [editingSeriesName, setEditingSeriesName] = useState<string>("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<{type: 'series' | 'single'; ids: string[]; name: string} | null>(null);
  const [customSeriesNames, setCustomSeriesNames] = useState<{[groupId: string]: string}>({});
  const [editingGameId, setEditingGameId] = useState<string | null>(null);
  const [editingGameData, setEditingGameData] = useState<{name: string; gameDate: string}>({name: '', gameDate: ''});
  const [moveGameDialog, setMoveGameDialog] = useState<{
    open: boolean;
    game: any | null; // Using any to avoid complex Lineup type
  }>({ open: false, game: null });
  const [targetSeriesId, setTargetSeriesId] = useState<string>("");
  const [newSeriesName, setNewSeriesName] = useState<string>("");
  const isMobile = useIsMobile();

  // Load custom series names from localStorage on component mount
  useEffect(() => {
    const savedNames: {[groupId: string]: string} = {};
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('series_name_')) {
        const groupId = key.replace('series_name_', '');
        const name = localStorage.getItem(key);
        if (name) {
          savedNames[groupId] = name;
        }
      }
    }
    setCustomSeriesNames(savedNames);
  }, []);

  // Group lineups by date and detect series
  const groupedLineups = useMemo(() => {
    console.log("🔍 LineupTable: Grouping lineups", { 
      count: lineups?.length || 0, 
      type,
      hasLineups: !!lineups 
    });
    
    if (!lineups || lineups.length === 0) return [];

    // Sort lineups by date, preserving creation order for same-date games
    const sortedLineups = [...lineups].sort((a, b) => {
      const dateCompare = a.gameDate.localeCompare(b.gameDate);
      if (dateCompare !== 0) return dateCompare;
      
      // If same date, try series grouping first
      if (a.seriesId && b.seriesId && a.seriesId === b.seriesId) {
        return (a.gameNumber || 0) - (b.gameNumber || 0);
      }
      
      // If same date but different series, sort by creation time to preserve original order
      const aCreated = new Date(a.createdDate || a.gameDate).getTime();
      const bCreated = new Date(b.createdDate || b.gameDate).getTime();
      const createdCompare = aCreated - bCreated;
      if (createdCompare !== 0) return createdCompare;
      
      // Finally, sort by name as fallback
      return a.name.localeCompare(b.name);
    });

    // Group lineups that appear to be part of a series
    const groups: Array<{
      id: string;
      date: string;
      lineups: typeof lineups;
      isSeries: boolean;
      seriesName?: string;
      isDateBased?: boolean; // Track if grouping is based on date vs creation time
    }> = [];

    let currentGroup: typeof lineups = [];
    let currentDate = "";

    // First, try to detect series by seriesId
    const seriesGroups = new Map<string, typeof lineups>();
    
    // Group by seriesId first (highest priority)
    sortedLineups.forEach(lineup => {
      if (lineup.seriesId) {
        if (!seriesGroups.has(lineup.seriesId)) {
          seriesGroups.set(lineup.seriesId, []);
        }
        seriesGroups.get(lineup.seriesId)!.push(lineup);
      }
    });
    
    // Sort lineups within each series by game number
    for (const [seriesId, seriesLineups] of seriesGroups.entries()) {
      seriesLineups.sort((a, b) => (a.gameNumber || 0) - (b.gameNumber || 0));
    }
    
    // Mark all lineups with seriesId as processed
    const processedLineups = new Set<string>();
    for (const seriesLineups of seriesGroups.values()) {
      seriesLineups.forEach(lineup => processedLineups.add(lineup.id));
    }

    // Process series groups first (highest priority)
    for (const [seriesId, seriesLineups] of seriesGroups.entries()) {
      const firstLineup = seriesLineups[0];
      groups.push({
        id: seriesId,
        date: firstLineup.gameDate,
        lineups: seriesLineups,
        isSeries: true,
        seriesName: firstLineup.seriesTitle || `${seriesLineups.length}-Game Series`,
        isDateBased: false
      });
    }
    
    // Skip batch grouping - only group games that have explicit series metadata
    // This prevents single games created close together from being grouped
    
    // Process all remaining lineups as individual games
    const unprocessedLineups = sortedLineups.filter(lineup => !processedLineups.has(lineup.id));
    
    // Group by date but don't mark as series unless they have series metadata
    unprocessedLineups.forEach((lineup) => {
      // Each single game gets its own group
      groups.push({
        id: `single-${lineup.id}`,
        date: lineup.gameDate,
        lineups: [lineup],
        isSeries: false,
        seriesName: undefined,
        isDateBased: false
      });
    });

    console.log("🔍 LineupTable: Grouped result", { 
      groupCount: groups.length,
      groups: groups.map(g => ({ id: g.id, isSeries: g.isSeries, lineupCount: g.lineups.length }))
    });
    
    return groups;
  }, [lineups]);

  const toggleSeriesExpansion = (groupId: string) => {
    setExpandedSeries(prev => 
      prev.includes(groupId) 
        ? prev.filter(id => id !== groupId)
        : [...prev, groupId]
    );
  };

  const handleGameResultChange = async (lineupId: string, result: 'win' | 'loss' | null) => {
    try {
      const lineup = lineups.find(l => l.id === lineupId);
      if (!lineup) {
        throw new Error('Lineup not found');
      }
      
      await updateLineup({ ...lineup, gameResult: result });
      // Update local state to reflect the change immediately
      const updatedLineups = lineups.map(lineup => 
        lineup.id === lineupId ? { ...lineup, gameResult: result } : lineup
      );
      setLineups(updatedLineups);
      const resultText = result === 'win' ? 'Win' : result === 'loss' ? 'Loss' : 'No result';
      toast.success(`Game marked as: ${resultText}`);
    } catch (error) {
      console.error('Failed to update game result:', error);
      toast.error('Failed to update game result');
    }
  };

  const handleGameDetailsUpdate = async (lineupId: string) => {
    try {
      const lineup = lineups.find(l => l.id === lineupId);
      if (!lineup) {
        throw new Error('Lineup not found');
      }
      
      await updateLineup({ 
        ...lineup, 
        name: editingGameData.name,
        gameDate: editingGameData.gameDate
      });
      // Update local state
      const updatedLineups = lineups.map(lineup => 
        lineup.id === lineupId ? { ...lineup, ...editingGameData } : lineup
      );
      setLineups(updatedLineups);
      setEditingGameId(null);
      setEditingGameData({name: '', gameDate: ''});
      toast.success('Game details updated');
    } catch (error) {
      console.error('Failed to update game details:', error);
      toast.error('Failed to update game details');
    }
  };
  
  const handleMoveGame = async () => {
    if (!moveGameDialog.game || !targetSeriesId) return;
    
    try {
      const gameId = moveGameDialog.game.id;
      const seriesId = targetSeriesId === 'new' ? `series_${Date.now()}` : targetSeriesId;
      const seriesTitle = targetSeriesId === 'new' ? newSeriesName : undefined;
      
      // Update the game's series information (use snake_case for database columns)
      const { error } = await supabase
        .from('lineups')
        .update({
          series_id: seriesId,
          series_title: seriesTitle || moveGameDialog.game.seriesTitle,
        })
        .eq('id', gameId);
      
      if (error) throw error;
      
      toast.success('Game moved successfully');
      await refreshTeamData();
      setMoveGameDialog({ open: false, game: null });
      setTargetSeriesId("");
      setNewSeriesName("");
    } catch (error) {
      console.error('Failed to move game:', error);
      toast.error('Failed to move game');
    }
  };

  if (!lineups || lineups.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <p className="mb-2">No lineups created yet.</p>
        <p className="text-sm">Create your first lineup to get started!</p>
      </div>
    );
  }

  if (type === "basic") {
    // Simple list view without grouping
    return (
      <div className={cn("space-y-2", isMobile && "space-y-1")}>
        {lineups.map((lineup) => (
          <Link key={lineup.id} to={`/view-lineup/${lineup.id}`}>
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardContent className={cn("p-4", isMobile && "p-3")}>
                <div className="flex justify-between items-center">
                  <div className={isMobile ? "min-w-0 flex-1" : ""}>
                    <h5 className={cn("font-semibold", isMobile && "text-sm truncate")}>{lineup.name}</h5>
                    <p className={cn("text-sm text-gray-600", isMobile && "text-xs")}>{lineup.gameDate}</p>
                  </div>
                  <Button variant="ghost" size="sm" className={isMobile ? "text-xs px-2 py-1 h-7" : ""}>
                    View →
                  </Button>
                </div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    );
  }

  // Enhanced retrieve view with series grouping
  const handleDelete = async () => {
    if (!itemToDelete) return;
    
    try {
      console.log(`🗑️ DELETING ${itemToDelete.type.toUpperCase()}: "${itemToDelete.name}"`);
      console.log(`🗑️ IDs to delete:`, itemToDelete.ids);
      console.log(`🗑️ Total games to delete: ${itemToDelete.ids.length}`);
      
      if (itemToDelete.type === 'series') {
        console.log(`🗑️ SERIES DELETE: Deleting all ${itemToDelete.ids.length} games in batch...`);
        
        // Store the IDs we need to delete before any state updates
        const idsToDelete = [...itemToDelete.ids];
        
        // Delete directly from database to avoid state update conflicts
        const { supabase } = await import('@/integrations/supabase/client');
        
        // Delete all lineups in the series at once using the IN operator
        const { error } = await supabase
          .from('lineups')
          .delete()
          .in('id', idsToDelete);
          
        if (error) {
          console.error(`❌ Failed to delete series:`, error);
          throw error;
        }
        
        console.log(`✅ SERIES DELETE COMPLETE: All ${idsToDelete.length} games deleted successfully`);
        
        // Now update the local state by removing all deleted lineups at once
        const updatedLineups = lineups.filter(lineup => !idsToDelete.includes(lineup.id));
        setLineups(updatedLineups);
        
        // Force TeamContext to refresh to ensure consistency
        if (typeof refreshTeamData === 'function') {
          await refreshTeamData();
        }
      } else {
        console.log(`🗑️ SINGLE DELETE: Deleting game ${itemToDelete.ids[0]}`);
        await removeLineup(itemToDelete.ids[0]);
        console.log(`✅ SINGLE DELETE COMPLETE: Game deleted successfully`);
      }
      
      setDeleteDialogOpen(false);
      setItemToDelete(null);
    } catch (error) {
      console.error('❌ Error during deletion:', error);
      // Don't close dialog on error so user can try again
    }
  };

  return (
    <>
      <div className={cn("space-y-4", isMobile && "space-y-2")}>
      {groupedLineups.map((group) => {
        const isExpanded = expandedSeries.includes(group.id);
        
        if (group.isSeries) {
          // Render as a series
          return (
            <Card key={group.id} className="border-2 border-baseball-green/30 bg-baseball-lightgreen/10 group">
              <CardContent className={cn("p-4", isMobile && "p-3")}>
                <div className={cn("space-y-3", isMobile && "space-y-2")}>
                  {/* Series Header */}
                  <div className={cn("flex items-center justify-between", isMobile && "flex-col items-start gap-2")}>
                    <div className={cn("flex items-center gap-3", isMobile && "gap-2 w-full")}>
                      <Trophy className={cn("w-5 h-5 text-baseball-green", isMobile && "w-4 h-4")} />
                      <div className="flex-1">
                        {editingSeriesId === group.id ? (
                          <div className="flex items-center gap-2 flex-1">
                            <input
                              type="text"
                              value={editingSeriesName}
                              onChange={(e) => setEditingSeriesName(e.target.value)}
                              onKeyDown={async (e) => {
                                if (e.key === 'Enter') {
                                  e.preventDefault();
                                  // Save on Enter
                                  if (editingSeriesName.trim() && editingSeriesName.trim() !== group.seriesName) {
                                    try {
                                      // Update all lineups in the series with the new series title
                                      for (const lineup of group.lineups) {
                                        await updateLineup({
                                          ...lineup,
                                          seriesTitle: editingSeriesName.trim()
                                        });
                                      }
                                      
                                      // Update local state to reflect the change
                                      const updatedLineups = lineups.map(lineup => 
                                        group.lineups.some(gl => gl.id === lineup.id) 
                                          ? { ...lineup, seriesTitle: editingSeriesName.trim() }
                                          : lineup
                                      );
                                      setLineups(updatedLineups);
                                      
                                      toast.success('Series name updated');
                                    } catch (error) {
                                      console.error('Failed to update series name:', error);
                                      toast.error('Failed to update series name');
                                    }
                                  }
                                  setEditingSeriesId(null);
                                } else if (e.key === 'Escape') {
                                  setEditingSeriesId(null);
                                  setEditingSeriesName(group.seriesName || '');
                                }
                              }}
                              className="font-bold text-baseball-navy bg-transparent border-b-2 border-baseball-green focus:outline-none flex-1"
                              autoFocus
                            />
                            <button
                              onClick={async () => {
                                if (editingSeriesName.trim() && editingSeriesName.trim() !== group.seriesName) {
                                  try {
                                    // Update all lineups in the series with the new series title
                                    for (const lineup of group.lineups) {
                                      await updateLineup({
                                        ...lineup,
                                        seriesTitle: editingSeriesName.trim()
                                      });
                                    }
                                    
                                    // Update local state to reflect the change
                                    const updatedLineups = lineups.map(lineup => 
                                      group.lineups.some(gl => gl.id === lineup.id) 
                                        ? { ...lineup, seriesTitle: editingSeriesName.trim() }
                                        : lineup
                                    );
                                    setLineups(updatedLineups);
                                    
                                    toast.success('Series name updated');
                                  } catch (error) {
                                    console.error('Failed to update series name:', error);
                                    toast.error('Failed to update series name');
                                  }
                                }
                                setEditingSeriesId(null);
                              }}
                              className="text-green-600 hover:text-green-700 p-1"
                              title="Save (Enter)"
                            >
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                            </button>
                            <button
                              onClick={() => {
                                setEditingSeriesId(null);
                                setEditingSeriesName(group.seriesName || '');
                              }}
                              className="text-red-600 hover:text-red-700 p-1"
                              title="Cancel (Esc)"
                            >
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          </div>
                        ) : (
                          <div className="flex items-center gap-2">
                            <h5 className={cn("font-bold text-baseball-navy", isMobile && "text-sm")}>
                              {group.seriesName}
                            </h5>
                            {!isMobile && (
                              <button
                                onClick={() => {
                                  setEditingSeriesId(group.id);
                                  setEditingSeriesName(group.seriesName || '');
                                }}
                                className="opacity-0 group-hover:opacity-100 transition-opacity"
                              >
                                <Edit2 className="w-4 h-4 text-gray-500 hover:text-baseball-green" />
                              </button>
                            )}
                          </div>
                        )}
                        <div className={cn("flex items-center gap-2 text-sm text-gray-600", isMobile && "text-xs")}>
                          <CalendarDays className={cn("w-4 h-4", isMobile && "w-3 h-3")} />
                          {new Date(group.date).toLocaleDateString()}
                          <Badge variant="secondary" className={cn("ml-2", isMobile && "ml-1 text-xs px-1.5 py-0")}>
                            {group.lineups.length} games
                          </Badge>
                        </div>
                      </div>
                    </div>
                    {isMobile ? (
                      <div className="flex flex-col gap-2 w-full mt-2">
                        <div className="flex gap-2">
                          <Link 
                            to="/view-batch-series" 
                            state={{ 
                              seriesData: {
                                games: group.lineups.map(l => l.id),
                                seriesName: group.seriesName,
                                createdDate: group.date,
                                seriesId: group.id
                              }
                            }}
                            className="flex-1"
                          >
                            <Button 
                              variant="outline" 
                              size="sm" 
                              className="w-full text-baseball-green border-baseball-green hover:bg-baseball-lightgreen/30 text-xs px-2 py-1.5 h-8"
                            >
                              <Eye className="w-3 h-3 mr-1" />
                              View Series
                            </Button>
                          </Link>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleSeriesExpansion(group.id)}
                            className="flex-1 text-xs px-2 py-1.5 h-8"
                          >
                            {isExpanded ? "Hide" : "Show"} Games
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="flex gap-2">
                        <Link 
                          to="/view-batch-series" 
                          state={{ 
                            seriesData: {
                              games: group.lineups.map(l => l.id),
                              seriesName: group.seriesName,
                              createdDate: group.date,
                              seriesId: group.id
                            }
                          }}
                        >
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="text-baseball-green border-baseball-green hover:bg-baseball-lightgreen/30"
                          >
                            <Eye className="w-4 h-4" />
                            <span className="ml-1">View</span>
                          </Button>
                        </Link>
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleSeriesExpansion(group.id)}
                          >
                            {isExpanded ? "Hide" : "Show"} Games
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => {
                              const seriesToDelete = {
                                type: 'series' as const,
                                ids: group.lineups.map(l => l.id),
                                name: customSeriesNames[group.id] || group.seriesName || 'this series'
                              };
                              console.log(`🗑️ PREPARING SERIES DELETE:`, seriesToDelete);
                              console.log(`🗑️ Group lineups:`, group.lineups.map(l => ({ id: l.id, name: l.name })));
                              setItemToDelete(seriesToDelete);
                              setDeleteDialogOpen(true);
                            }}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Individual Games (when expanded) */}
                  {isExpanded && (
                    <div className={cn("ml-8 space-y-2 pt-2 border-t", isMobile && "ml-4 space-y-1 pt-1")}>
                      {group.lineups.map((lineup, index) => (
                        <div key={lineup.id} className={cn("flex items-center justify-between py-2", isMobile && "py-1")}>
                          <div className={cn("flex items-center gap-3", isMobile && "gap-2 flex-1 min-w-0")}>
                            <div className={cn(
                              "w-8 h-8 bg-baseball-green text-white rounded-full flex items-center justify-center text-sm font-bold",
                              isMobile && "w-6 h-6 text-xs flex-shrink-0"
                            )}>
                              {index + 1}
                            </div>
                            <div className={isMobile ? "min-w-0 flex-1" : "flex-1"}>
                              {editingGameId === lineup.id ? (
                                <div className="flex flex-col gap-1">
                                  <input
                                    type="text"
                                    value={editingGameData.name}
                                    onChange={(e) => setEditingGameData({...editingGameData, name: e.target.value})}
                                    className="text-sm px-2 py-1 border rounded"
                                    autoFocus
                                  />
                                  <input
                                    type="date"
                                    value={editingGameData.gameDate}
                                    onChange={(e) => setEditingGameData({...editingGameData, gameDate: e.target.value})}
                                    className="text-sm px-2 py-1 border rounded"
                                  />
                                  <div className="flex gap-1">
                                    <button
                                      onClick={() => handleGameDetailsUpdate(lineup.id)}
                                      className="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700"
                                    >
                                      Save
                                    </button>
                                    <button
                                      onClick={() => {
                                        setEditingGameId(null);
                                        setEditingGameData({name: '', gameDate: ''});
                                      }}
                                      className="text-xs px-2 py-1 bg-gray-400 text-white rounded hover:bg-gray-500"
                                    >
                                      Cancel
                                    </button>
                                  </div>
                                </div>
                              ) : (
                                <div className="flex items-center gap-2">
                                  <span className={cn("font-medium", isMobile && "text-sm truncate block")}>
                                    {lineup.name}
                                  </span>
                                  <span className="text-xs text-gray-500">
                                    {new Date(lineup.gameDate).toLocaleDateString()}
                                  </span>
                                  {!isMobile && (
                                    <button
                                      onClick={() => {
                                        setEditingGameId(lineup.id);
                                        setEditingGameData({
                                          name: lineup.name,
                                          gameDate: lineup.gameDate
                                        });
                                      }}
                                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                                    >
                                      <Edit2 className="w-3 h-3 text-gray-500 hover:text-baseball-green" />
                                    </button>
                                  )}
                                </div>
                              )}
                              {lineup.gameResult && (
                                <Badge 
                                  className={cn(
                                    `ml-2 ${
                                      lineup.gameResult === 'win' 
                                        ? 'bg-green-100 text-green-800' 
                                        : 'bg-red-100 text-red-800'
                                    }`,
                                    isMobile && "ml-0 mt-1 text-xs px-1 py-0"
                                  )}
                                >
                                  {lineup.gameResult === 'win' ? 'W' : 'L'}
                                </Badge>
                              )}
                            </div>
                          </div>
                          <div className={cn("flex items-center gap-2", isMobile && "gap-1 flex-shrink-0")}>
                            <WinLossToggle 
                              gameResult={lineup.gameResult} 
                              onResultChange={(result) => handleGameResultChange(lineup.id, result)}
                              isPastGame={new Date(lineup.gameDate) < new Date()}
                            />
                            <Link to={`/view-lineup/${lineup.id}`}>
                              <Button 
                                variant="ghost" 
                                size={isMobile ? "sm" : "sm"}
                                className={isMobile ? "p-1 h-7 w-7" : ""}
                              >
                                <Eye className={cn("w-4 h-4", isMobile && "w-3 h-3")} />
                              </Button>
                            </Link>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button 
                                  variant="ghost" 
                                  size={isMobile ? "sm" : "sm"}
                                  className={isMobile ? "p-1 h-7 w-7" : ""}
                                >
                                  <MoreVertical className={cn("w-4 h-4", isMobile && "w-3 h-3")} />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem 
                                  onClick={() => {
                                    setMoveGameDialog({ open: true, game: lineup });
                                  }}
                                >
                                  <ArrowRightLeft className="w-4 h-4 mr-2" />
                                  Move to Series
                                </DropdownMenuItem>
                                <DropdownMenuItem 
                                  onClick={() => {
                                    setItemToDelete({
                                      type: 'single',
                                      ids: [lineup.id],
                                      name: lineup.name
                                    });
                                    setDeleteDialogOpen(true);
                                  }}
                                  className="text-red-600"
                                >
                                  <Trash2 className="w-4 h-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        } else {
          // Render single game
          const lineup = group.lineups[0];
          return (
            <Card key={lineup.id} className="hover:shadow-md transition-shadow group">
              <CardContent className={cn("p-4", isMobile && "p-3")}>
                <div className={cn("flex items-center justify-between", isMobile && "flex-col items-start gap-2")}>
                  <div className={cn("flex items-center gap-3", isMobile && "gap-2 w-full")}>
                    <FileText className={cn("w-5 h-5 text-gray-400", isMobile && "w-4 h-4 flex-shrink-0")} />
                    <div className={isMobile ? "flex-1 min-w-0" : "flex-1"}>
                      {editingGameId === lineup.id ? (
                        <div className="flex flex-col gap-1">
                          <input
                            type="text"
                            value={editingGameData.name}
                            onChange={(e) => setEditingGameData({...editingGameData, name: e.target.value})}
                            className="text-sm px-2 py-1 border rounded font-semibold"
                            autoFocus
                          />
                          <input
                            type="date"
                            value={editingGameData.gameDate}
                            onChange={(e) => setEditingGameData({...editingGameData, gameDate: e.target.value})}
                            className="text-sm px-2 py-1 border rounded"
                          />
                          <div className="flex gap-1">
                            <button
                              onClick={() => handleGameDetailsUpdate(lineup.id)}
                              className="text-xs px-2 py-1 bg-green-600 text-white rounded hover:bg-green-700"
                            >
                              Save
                            </button>
                            <button
                              onClick={() => {
                                setEditingGameId(null);
                                setEditingGameData({name: '', gameDate: ''});
                              }}
                              className="text-xs px-2 py-1 bg-gray-400 text-white rounded hover:bg-gray-500"
                            >
                              Cancel
                            </button>
                          </div>
                        </div>
                      ) : (
                        <>
                          <div className="flex items-center gap-2">
                            <h5 className={cn("font-semibold text-baseball-navy", isMobile && "text-sm truncate")}>
                              {lineup.name}
                            </h5>
                            {!isMobile && (
                              <button
                                onClick={() => {
                                  setEditingGameId(lineup.id);
                                  setEditingGameData({
                                    name: lineup.name,
                                    gameDate: lineup.gameDate
                                  });
                                }}
                                className="opacity-0 group-hover:opacity-100 transition-opacity"
                              >
                                <Edit2 className="w-3 h-3 text-gray-500 hover:text-baseball-green" />
                              </button>
                            )}
                          </div>
                          <div className={cn("flex items-center gap-2 text-sm text-gray-600", isMobile && "text-xs flex-wrap")}>
                            <div className="flex items-center gap-1">
                              <CalendarDays className={cn("w-4 h-4", isMobile && "w-3 h-3")} />
                              {new Date(lineup.gameDate).toLocaleDateString()}
                            </div>
                            {lineup.attendance && (
                              <div className="flex items-center gap-1">
                                <Users className={cn("w-4 h-4", isMobile && "w-3 h-3")} />
                                {Object.values(lineup.attendance).filter(Boolean).length} players
                              </div>
                            )}
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                  <div className={cn("flex items-center gap-2", isMobile && "w-full justify-between")}>
                    <div className="flex items-center gap-2">
                      <WinLossToggle 
                        gameResult={lineup.gameResult} 
                        onResultChange={(result) => handleGameResultChange(lineup.id, result)}
                        isPastGame={new Date(lineup.gameDate) < new Date()}
                      />
                    </div>
                    <div className="flex items-center gap-1">
                      <Link to={`/view-lineup/${lineup.id}`}>
                        <Button 
                          variant="outline" 
                          size="sm"
                          className={isMobile ? "text-xs px-2 py-1 h-7" : ""}
                        >
                          <Eye className={cn("w-4 h-4", isMobile && "w-3 h-3 mr-0.5")} />
                          <span className={isMobile ? "ml-1" : "ml-1"}>View</span>
                        </Button>
                      </Link>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            className={isMobile ? "text-xs px-2 py-1 h-7" : ""}
                          >
                            <MoreVertical className={cn("w-4 h-4", isMobile && "w-3 h-3")} />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem 
                            onClick={() => {
                              setMoveGameDialog({ open: true, game: lineup });
                            }}
                          >
                            <ArrowRightLeft className="w-4 h-4 mr-2" />
                            Move to Series
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => {
                              setItemToDelete({
                                type: 'single',
                                ids: [lineup.id],
                                name: lineup.name
                              });
                              setDeleteDialogOpen(true);
                            }}
                            className="text-red-600"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        }
      })}
      </div>
      
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Delete {itemToDelete?.type === 'series' ? 'Series' : 'Lineup'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {itemToDelete?.type === 'series' 
                ? `Are you sure you want to delete ${itemToDelete.name} and all ${itemToDelete.ids.length} games in it? This action cannot be undone.`
                : `Are you sure you want to delete "${itemToDelete?.name}"? This action cannot be undone.`
              }
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      {/* Move Game Dialog */}
      <Dialog open={moveGameDialog.open} onOpenChange={(open) => {
        if (!open) {
          setMoveGameDialog({ open: false, game: null });
          setTargetSeriesId("");
          setNewSeriesName("");
        }
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Move "{moveGameDialog.game?.name}" to Series</DialogTitle>
            <DialogDescription>
              Select a series to move this game to, or create a new series.
            </DialogDescription>
          </DialogHeader>
          
          <RadioGroup value={targetSeriesId} onValueChange={setTargetSeriesId}>
            {groupedLineups
              .filter(group => group.isSeries && moveGameDialog.game && !group.lineups.some(l => l.id === moveGameDialog.game.id))
              .map(series => (
                <div key={series.id} className="flex items-center space-x-2 py-2">
                  <RadioGroupItem value={series.id} id={series.id} />
                  <Label htmlFor={series.id} className="flex-1 cursor-pointer">
                    <div>
                      <div className="font-medium">{series.seriesName}</div>
                      <div className="text-sm text-gray-500">
                        {series.lineups.length} game{series.lineups.length !== 1 ? 's' : ''} • {new Date(series.date).toLocaleDateString()}
                      </div>
                    </div>
                  </Label>
                </div>
              ))}
            <div className="flex items-center space-x-2 py-2">
              <RadioGroupItem value="new" id="new-series" />
              <Label htmlFor="new-series" className="cursor-pointer">
                Create New Series
              </Label>
            </div>
          </RadioGroup>
          
          {targetSeriesId === "new" && (
            <div className="mt-4">
              <Label htmlFor="new-series-name">Series Name</Label>
              <Input 
                id="new-series-name"
                placeholder="Enter series name" 
                value={newSeriesName}
                onChange={(e) => setNewSeriesName(e.target.value)}
                className="mt-1"
              />
            </div>
          )}
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => {
                setMoveGameDialog({ open: false, game: null });
                setTargetSeriesId("");
                setNewSeriesName("");
              }}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleMoveGame}
              disabled={!targetSeriesId || (targetSeriesId === "new" && !newSeriesName.trim())}
              variant="baseball"
            >
              Move Game
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default LineupTable;
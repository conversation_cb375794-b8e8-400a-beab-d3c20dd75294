import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Trophy, AlertCircle, HelpCircle } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface CompetitiveModeSettingsProps {
  competitiveMode: boolean;
  competitiveMinPlayingTime: number;
  onCompetitiveModeChange: (enabled: boolean) => void;
  onMinPlayingTimeChange: (percentage: number) => void;
  disabled?: boolean;
}

const CompetitiveModeSettings: React.FC<CompetitiveModeSettingsProps> = ({
  competitiveMode,
  competitiveMinPlayingTime,
  onCompetitiveModeChange,
  onMinPlayingTimeChange,
  disabled = false
}) => {
  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Trophy className="w-5 h-5 text-yellow-600" />
            Competitive Mode
          </CardTitle>
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="ghost" size="sm" className="text-gray-500 hover:text-gray-700">
                <HelpCircle className="w-4 h-4" />
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>About Competitive Mode</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 text-sm">
                <div>
                  <h4 className="font-semibold mb-2">What is Competitive Mode?</h4>
                  <p className="text-gray-600">
                    Competitive mode prioritizes winning by placing your best players in optimal positions 
                    for longer periods. While all players still get playing time, the distribution may be unequal.
                  </p>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">How it works:</h4>
                  <ul className="list-disc list-inside space-y-1 text-gray-600">
                    <li>Players with higher position ratings stay in key positions longer</li>
                    <li>Star players get priority for critical positions (pitcher, catcher, shortstop)</li>
                    <li>All players still meet the minimum playing time requirement</li>
                    <li>Ideal for tournament play or must-win games</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Minimum Playing Time:</h4>
                  <p className="text-gray-600">
                    Even in competitive mode, every player is guaranteed to play at least the specified 
                    percentage of total innings, ensuring everyone participates.
                  </p>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Enable/Disable Toggle */}
        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div className="space-y-0.5">
            <Label htmlFor="competitive-mode" className="text-base font-medium">
              Enable Competitive Mode
            </Label>
            <p className="text-sm text-gray-600">
              Prioritize optimal player positioning over equal playing time
            </p>
          </div>
          <Switch
            id="competitive-mode"
            checked={competitiveMode}
            onCheckedChange={onCompetitiveModeChange}
            disabled={disabled}
            className="data-[state=checked]:bg-yellow-600"
          />
        </div>

        {/* Warning when enabled */}
        {competitiveMode && (
          <Alert className="border-yellow-200 bg-yellow-50">
            <AlertCircle className="h-4 w-4 text-yellow-600" />
            <AlertDescription className="text-sm">
              <strong className="text-yellow-800">Playing time may be unequal.</strong> 
              <br />
              Better players will stay in key positions longer to maximize team performance.
            </AlertDescription>
          </Alert>
        )}

        {/* Settings */}
        {competitiveMode && (
          <div className="space-y-4">
            <Separator />
            
            {/* Minimum Playing Time */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">
                Minimum Playing Time
              </Label>
              <p className="text-xs text-gray-600">
                Ensures all players get at least this percentage of total innings
              </p>
              <Select
                value={competitiveMinPlayingTime.toString()}
                onValueChange={(value) => onMinPlayingTimeChange(parseInt(value))}
                disabled={disabled}
              >
                <SelectTrigger className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="30">30% - Very Competitive</SelectItem>
                  <SelectItem value="40">40% - Competitive</SelectItem>
                  <SelectItem value="50">50% - Balanced</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500">
                In a 6-inning game: {Math.floor(6 * (competitiveMinPlayingTime / 100))} innings minimum per player
              </p>
            </div>
          </div>
        )}

        {/* Summary */}
        <div className="bg-gradient-to-r from-blue-50 to-green-50 p-4 rounded-lg border">
          <h5 className="font-medium text-sm mb-2 flex items-center gap-2">
            <AlertCircle className="w-4 h-4 text-blue-600" />
            Current Configuration
          </h5>
          <div className="text-sm text-gray-700 space-y-1">
            <div className="flex items-center gap-2">
              <Badge variant={competitiveMode ? "default" : "secondary"} className="text-xs">
                {competitiveMode ? 'Competitive Mode ON' : 'Recreational Mode'}
              </Badge>
              <span className="text-xs text-gray-600">
                {competitiveMode ? 'Optimizing for performance' : 'Equal playing time for all'}
              </span>
            </div>
            {competitiveMode && (
              <div className="text-xs text-gray-600 pl-2">
                • Minimum playing time: {competitiveMinPlayingTime}% of innings
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CompetitiveModeSettings;
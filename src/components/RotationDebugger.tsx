import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { generateLineupFromFirstInning } from '../lib/utils-enhanced';
import type { Player, InningLineup, LineupRules } from '../lib/utils-enhanced';

// Create mock players for testing
const createMockPlayer = (name: string, id: string): Player => ({
  id,
  name,
  team_id: 'test-team',
  attendance: 'present' as const,
  positionRestrictions: [],
  positionPreferences: {
    pitcher: 'neutral',
    catcher: 'neutral',
    firstBase: 'neutral',
    secondBase: 'neutral',
    thirdBase: 'neutral',
    shortstop: 'neutral',
    leftField: 'neutral',
    centerField: 'neutral',
    rightField: 'neutral'
  }
});

export const RotationDebugger: React.FC = () => {
  const [rotationResults, setRotationResults] = useState<any[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const runRotationTest = () => {
    setIsRunning(true);
    const results: any[] = [];

    // Create test players
    const players = Array.from({ length: 12 }, (_, i) => 
      createMockPlayer(`Player${i + 1}`, `player-${i + 1}`)
    );

    // Create first inning
    const firstInning: InningLineup = {
      inning: 1,
      positions: {
        pitcher: 'Player1',
        catcher: 'Player2',
        firstBase: 'Player3',
        secondBase: 'Player4',
        thirdBase: 'Player5',
        shortstop: 'Player6',
        leftField: 'Player7',
        centerField: 'Player8',
        rightField: 'Player9',
        bench: ['Player10', 'Player11', 'Player12']
      }
    };

    // Test different rotation frequencies
    [1, 2, 3].forEach(rotateEvery => {
      const rules: LineupRules = {
        rotateLineupEvery: rotateEvery,
        limitBenchTime: true,
        maxConsecutiveBenchInnings: 2,
        allowPitcherRotation: false,
        allowCatcherRotation: false,
        respectPositionLockouts: false,
        equalPlayingTime: true,
        _randomSeed: 42
      };

      try {
        const lineup = generateLineupFromFirstInning(firstInning, players, 6, rules);
        
        const testResult = {
          rotateEvery,
          innings: lineup.map((inning, idx) => {
            if (idx === 0) return { inning: 1, changes: 0, shouldRotate: false };
            
            const prev = lineup[idx - 1];
            const curr = inning;
            
            let changes = 0;
            const changedPositions: string[] = [];
            const positions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'thirdBase', 
                              'shortstop', 'leftField', 'centerField', 'rightField'];
            
            positions.forEach(pos => {
              const prevPlayer = (prev.positions as any)[pos];
              const currPlayer = (curr.positions as any)[pos];
              if (prevPlayer !== currPlayer) {
                changes++;
                changedPositions.push(`${pos}: ${prevPlayer} → ${currPlayer}`);
              }
            });
            
            const shouldRotate = (inning.inning - 1) % rotateEvery === 0;
            
            return {
              inning: inning.inning,
              changes,
              shouldRotate,
              changedPositions,
              benchBefore: prev.positions.bench,
              benchAfter: curr.positions.bench,
              success: shouldRotate ? changes > 0 : true // If should rotate, must have changes
            };
          })
        };
        
        results.push(testResult);
      } catch (error) {
        results.push({
          rotateEvery,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });

    setRotationResults(results);
    setIsRunning(false);
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Rotation Algorithm Debugger</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <Button 
            onClick={runRotationTest} 
            disabled={isRunning}
            className="w-full"
          >
            {isRunning ? 'Running Tests...' : 'Run Rotation Tests'}
          </Button>

          {rotationResults.map((result, idx) => (
            <Card key={idx} className="p-4">
              <h3 className="font-semibold mb-2">
                Rotate Every {result.rotateEvery} Inning{result.rotateEvery > 1 ? 's' : ''}
              </h3>
              
              {result.error ? (
                <div className="text-red-500">Error: {result.error}</div>
              ) : (
                <div className="space-y-2">
                  {result.innings.map((inning: any, inningIdx: number) => (
                    <div key={inningIdx} className={`p-2 rounded ${
                      inning.inning === 1 ? 'bg-gray-100' :
                      inning.success ? 'bg-green-50' : 'bg-red-50'
                    }`}>
                      <div className="font-medium">
                        Inning {inning.inning}
                        {inning.shouldRotate && ' (Should Rotate)'}
                      </div>
                      {inning.inning > 1 && (
                        <>
                          <div className="text-sm">
                            Changes: {inning.changes} positions
                            {!inning.success && ' ❌ FAILED - No rotation occurred!'}
                          </div>
                          {inning.changedPositions.length > 0 && (
                            <div className="text-xs text-gray-600 mt-1">
                              {inning.changedPositions.slice(0, 3).join(', ')}
                              {inning.changedPositions.length > 3 && ` +${inning.changedPositions.length - 3} more`}
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  ))}
                  
                  <div className="mt-2 text-sm">
                    {result.innings.filter((i: any) => i.inning > 1 && !i.success).length === 0 
                      ? '✅ All rotations working correctly!'
                      : `❌ ${result.innings.filter((i: any) => i.inning > 1 && !i.success).length} failed rotations`
                    }
                  </div>
                </div>
              )}
            </Card>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
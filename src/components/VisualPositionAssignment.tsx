import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Player, PlayerRole } from '@/contexts/TeamContext';
import { Badge } from '@/components/ui/badge';
import { Star, Ban } from 'lucide-react';

interface VisualPositionAssignmentProps {
  player: Player;
  onChange: (player: Player) => void;
  disabled?: boolean;
}

const POSITION_GROUPS = {
  battery: {
    name: 'Battery',
    positions: [
      { key: 'pitcher', label: 'Pitcher' },
      { key: 'catcher', label: 'Catcher' }
    ]
  },
  infield: {
    name: 'Infield',
    positions: [
      { key: 'firstBase', label: '1st Base' },
      { key: 'secondBase', label: '2nd Base' },
      { key: 'thirdBase', label: '3rd Base' },
      { key: 'shortstop', label: 'Shortstop' }
    ]
  },
  outfield: {
    name: 'Outfield',
    positions: [
      { key: 'leftField', label: 'Left Field' },
      { key: 'centerField', label: 'Center Field' },
      { key: 'rightField', label: 'Right Field' }
    ]
  }
};

// Map the star ratings to our internal role system
const STAR_RATINGS = [
  { stars: 3, role: 'go-to', label: 'Best position' },
  { stars: 2, role: 'capable', label: 'Good here' },
  { stars: 1, role: 'fill-in', label: 'Can play if needed' },
  { stars: 0, role: 'unset', label: 'No preference' },
  { stars: -1, role: 'avoid', label: 'Never play here' }
];

export function VisualPositionAssignment({ 
  player, 
  onChange, 
  disabled 
}: VisualPositionAssignmentProps) {
  
  const handleRatingChange = (positionKey: string, role: PlayerRole | null) => {
    if (disabled) return;
    
    const updatedPlayer = {
      ...player,
      teamRoles: {
        ...player.teamRoles,
        [positionKey]: role === 'unset' ? undefined : role
      }
    };
    
    onChange(updatedPlayer);
  };

  const setAllAsUtility = () => {
    if (disabled) return;
    
    const updatedRoles = { ...player.teamRoles };
    
    // Give everyone 1 star at all field positions
    Object.values(POSITION_GROUPS).forEach(group => {
      group.positions.forEach(pos => {
        updatedRoles[pos.key as keyof typeof updatedRoles] = 'fill-in' as PlayerRole;
      });
    });
    
    onChange({ ...player, teamRoles: updatedRoles });
  };

  const setOutfieldOnly = () => {
    if (disabled) return;
    
    const updatedRoles = { ...player.teamRoles };
    
    // Give 2 stars to all outfield positions
    POSITION_GROUPS.outfield.positions.forEach(pos => {
      updatedRoles[pos.key as keyof typeof updatedRoles] = 'capable' as PlayerRole;
    });
    
    onChange({ ...player, teamRoles: updatedRoles });
  };

  const setCanPlayAnywhere = () => {
    if (disabled) return;
    
    const updatedRoles = { ...player.teamRoles };
    
    // Give 1 star everywhere
    Object.values(POSITION_GROUPS).forEach(group => {
      group.positions.forEach(pos => {
        updatedRoles[pos.key as keyof typeof updatedRoles] = 'fill-in' as PlayerRole;
      });
    });
    
    onChange({ ...player, teamRoles: updatedRoles });
  };

  const clearAllPositions = () => {
    if (disabled) return;
    
    onChange({
      ...player,
      teamRoles: {}
    });
  };

  const getCurrentRole = (positionKey: string): PlayerRole | null => {
    return player.teamRoles?.[positionKey as keyof typeof player.teamRoles] as PlayerRole || null;
  };

  const getStarDisplay = (role: PlayerRole | null) => {
    const rating = STAR_RATINGS.find(r => r.role === (role || 'unset'));
    if (!rating) return null;
    
    if (rating.stars === -1) {
      return { display: '🚫', label: rating.label, color: 'text-red-500' };
    } else if (rating.stars === 0) {
      return { display: '○○○', label: rating.label, color: 'text-gray-400' };
    } else {
      const stars = '⭐'.repeat(rating.stars);
      const empty = '○'.repeat(3 - rating.stars);
      return { display: stars + empty, label: rating.label, color: 'text-yellow-500' };
    }
  };

  return (
    <div className="w-full space-y-4">
      {/* Header with quick actions */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
        <h3 className="text-lg font-medium">{player.name} - Position Ratings</h3>
        <div className="flex flex-wrap gap-2">
          <Button
            onClick={setAllAsUtility}
            variant="outline"
            size="sm"
            disabled={disabled}
            className="text-xs"
          >
            Assign All as Utility
          </Button>
          <Button
            onClick={setOutfieldOnly}
            variant="outline"
            size="sm"
            disabled={disabled}
            className="text-xs"
          >
            Outfield Only
          </Button>
          <Button
            onClick={setCanPlayAnywhere}
            variant="outline"
            size="sm"
            disabled={disabled}
            className="text-xs"
          >
            Can Play Anywhere
          </Button>
          <Button
            onClick={clearAllPositions}
            variant="ghost"
            size="sm"
            disabled={disabled}
            className="text-xs text-red-600 hover:text-red-700"
          >
            Clear All
          </Button>
        </div>
      </div>

      {/* Position groups */}
      <div className="space-y-6">
        {Object.entries(POSITION_GROUPS).map(([groupKey, group]) => (
          <div key={groupKey} className="space-y-3">
            <h4 className="text-sm font-semibold text-gray-700">{group.name}</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              {group.positions.map(position => {
                const currentRole = getCurrentRole(position.key);
                const starDisplay = getStarDisplay(currentRole);
                
                return (
                  <Card key={position.key} className="overflow-hidden">
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        {/* Position name and current rating */}
                        <div className="flex items-center justify-between">
                          <h5 className="font-medium text-base">{position.label}</h5>
                          {starDisplay && (
                            <div className="text-right">
                              <div className={`text-lg ${starDisplay.color}`}>
                                {starDisplay.display}
                              </div>
                              <div className="text-xs text-gray-500">
                                {starDisplay.label}
                              </div>
                            </div>
                          )}
                        </div>
                        
                        {/* Star rating buttons */}
                        <div className="flex justify-between gap-1">
                          {/* 3 stars */}
                          <Button
                            onClick={() => handleRatingChange(position.key, currentRole === 'go-to' ? null : 'go-to')}
                            variant={currentRole === 'go-to' ? "default" : "outline"}
                            size="sm"
                            disabled={disabled}
                            className="flex-1 h-9 p-0"
                            title="Best position"
                          >
                            <div className="text-xs">
                              <div>⭐⭐⭐</div>
                            </div>
                          </Button>
                          
                          {/* 2 stars */}
                          <Button
                            onClick={() => handleRatingChange(position.key, currentRole === 'capable' ? null : 'capable')}
                            variant={currentRole === 'capable' ? "default" : "outline"}
                            size="sm"
                            disabled={disabled}
                            className="flex-1 h-9 p-0"
                            title="Good here"
                          >
                            <div className="text-xs">
                              <div>⭐⭐</div>
                            </div>
                          </Button>
                          
                          {/* 1 star */}
                          <Button
                            onClick={() => handleRatingChange(position.key, currentRole === 'fill-in' ? null : 'fill-in')}
                            variant={currentRole === 'fill-in' ? "default" : "outline"}
                            size="sm"
                            disabled={disabled}
                            className="flex-1 h-9 p-0"
                            title="Can play if needed"
                          >
                            <div className="text-xs">
                              <div>⭐</div>
                            </div>
                          </Button>
                          
                          {/* Never */}
                          <Button
                            onClick={() => handleRatingChange(position.key, currentRole === 'avoid' ? null : 'avoid')}
                            variant={currentRole === 'avoid' ? "destructive" : "outline"}
                            size="sm"
                            disabled={disabled}
                            className="h-9 w-9 p-0"
                            title="Never play here"
                          >
                            <Ban className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        ))}
      </div>

      {/* Help text */}
      <div className="bg-gray-50 rounded-lg p-4 text-sm text-gray-600">
        <p className="font-medium mb-1">Quick Guide:</p>
        <ul className="space-y-0.5 text-xs">
          <li>• ⭐⭐⭐ = Best position</li>
          <li>• ⭐⭐ = Good here</li>
          <li>• ⭐ = Can play if needed</li>
          <li>• No stars = Can't play here (competitive) / No preference (recreational)</li>
          <li>• 🚫 = Never play here</li>
        </ul>
      </div>
    </div>
  );
}
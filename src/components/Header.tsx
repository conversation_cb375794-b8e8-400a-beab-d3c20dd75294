
import { Link, useNavigate } from "react-router-dom";
import { useTeam } from "@/contexts/TeamContext";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { LogOut } from "lucide-react";
import { toast } from "sonner";

interface HeaderProps {
  title?: string;
  showBack?: boolean;
  backLink?: string;
  backState?: any;
  onBack?: () => void;
}

const Header = ({ title, showBack = false, backLink = "/dashboard", backState, onBack }: HeaderProps) => {
  const { teamName } = useTeam();
  const { user, signOut } = useAuth();
  const navigate = useNavigate();

  // Check if user is in demo mode
  const isDemoMode = user?.email?.includes('demo') ||
                     user?.email?.includes('baseball_demo') ||
                     localStorage.getItem('demo_mode') === 'true' ||
                     localStorage.getItem('demo_user_is_paid') === 'true';

  const handleLogout = async () => {
    try {
      await signOut();
      // Clear all demo-specific localStorage items
      localStorage.removeItem('demo_user_is_paid');
      localStorage.removeItem('demo_mode');
      localStorage.removeItem('demo_user_email');
      localStorage.removeItem('create_demo_team');

      toast.success("You have been logged out successfully");
      navigate('/');
    } catch (error) {
      console.error("Error logging out:", error);
      toast.error("Failed to log out. Please try again.");
    }
  };

  return (
    <div className="bg-baseball-navy text-baseball-white w-full py-4 px-6">
      <div className="container mx-auto">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            {showBack && (
              <button
                onClick={() => {
                  // If custom onBack handler is provided, use it
                  if (onBack) {
                    onBack();
                  } else {
                    // If backLink is "/" and user is logged in, go to dashboard instead
                    if (backLink === "/" && user) {
                      navigate("/dashboard");
                    } else {
                      navigate(backLink, backState ? { state: backState } : undefined);
                    }
                  }
                }}
                className="inline-flex items-center min-h-[44px] min-w-[44px] px-4 py-2 mr-4 
                         bg-white/10 hover:bg-white/20 active:bg-white/30 
                         rounded-lg transition-all duration-200 ease-out
                         hover:-translate-x-0.5 active:-translate-x-1
                         font-medium text-white
                         focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-baseball-navy"
                aria-label="Go back"
              >
                <svg 
                  className="w-5 h-5 mr-2" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M15 19l-7-7 7-7" 
                  />
                </svg>
                Back
              </button>
            )}
            <div>
              {title ? (
                <h1 className="text-2xl font-bold">{title}</h1>
              ) : (
                <>
                  <h1 className="text-2xl font-bold italic">Welcome back!</h1>
                  <h2 className="text-xl font-bold italic">
                    Team: {teamName}
                    {isDemoMode && (
                      <span className="ml-2 text-xs bg-yellow-500 text-white px-2 py-1 rounded-full">
                        DEMO MODE
                      </span>
                    )}
                  </h2>
                </>
              )}
            </div>
          </div>
          {/* Mobile Navigation - Enhanced touch targets */}
          <div className="md:hidden flex items-center gap-1">
            <Link to="/dashboard">
              <Button 
                variant="ghost" 
                size="default" 
                className="text-baseball-white hover:bg-white/10 active:bg-white/20 px-3 min-h-[44px]"
              >
                <span className="sr-only">Dashboard</span>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
              </Button>
            </Link>
            <Link to="/">
              <Button 
                variant="ghost" 
                size="default" 
                className="text-baseball-white hover:bg-white/10 active:bg-white/20 px-3 min-h-[44px]"
              >
                <span className="sr-only">Home</span>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </Button>
            </Link>
            {user && (
              <Button
                variant="ghost"
                size="default"
                onClick={handleLogout}
                className="text-baseball-white hover:bg-white/10 active:bg-white/20 px-3 min-h-[44px]"
              >
                <LogOut className="h-5 w-5" />
              </Button>
            )}
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center gap-4">
            <Link to="/dashboard" className="text-baseball-white hover:underline">
              Dashboard
            </Link>
            <Link to="/" className="text-baseball-white hover:underline">
              Home
            </Link>
            {user && (
              <Button
                variant="baseball"
                size="sm"
                onClick={handleLogout}
                className="bg-baseball-green text-white hover:bg-baseball-green/80"
              >
                <LogOut className="h-4 w-4 mr-1" />
                Logout
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header;

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Player, TeamRolePreferences } from '@/contexts/TeamContext';
import { useTeam } from '@/contexts/TeamContext';
import { Search, Plus, AlertTriangle, Check, Info, Users, ChevronDown, ChevronRight, X, Menu } from 'lucide-react';
import { toast } from 'sonner';
import { useIsMobile } from '@/hooks/use-mobile';
import { 
  DndContext, 
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  horizontalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface PositionDepthPoolsProps {
  onClose?: () => void;
}

interface PositionPool {
  position: string;
  label: string;
  players: Array<{
    id: string;
    name: string;
    priority: number;
  }>;
}

const POSITIONS = [
  { key: 'pitcher', label: 'Pitcher' },
  { key: 'catcher', label: 'Catcher' },
  { key: 'firstBase', label: '1B' },
  { key: 'secondBase', label: '2B' },
  { key: 'thirdBase', label: '3B' },
  { key: 'shortstop', label: 'SS' },
  { key: 'leftField', label: 'LF' },
  { key: 'centerField', label: 'CF' },
  { key: 'rightField', label: 'RF' }
];

// Draggable player chip component
function PlayerChip({ 
  player, 
  position, 
  onRemove 
}: { 
  player: { id: string; name: string; priority: number }, 
  position: string,
  onRemove: (position: string, playerId: string) => void
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: `${position}-${player.id}` });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="inline-flex items-center gap-1.5 bg-blue-50 border border-blue-200 rounded-md text-sm font-medium hover:shadow-sm hover:bg-blue-100 transition-all"
    >
      <div 
        {...attributes}
        {...listeners}
        className="flex items-center gap-1.5 px-3 py-1.5 cursor-move"
      >
        <span className="text-gray-500">{player.priority}.</span>
        <span>{player.name}</span>
      </div>
      <button
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          onRemove(position, player.id);
        }}
        onMouseDown={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        className="px-2 py-1.5 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-r transition-colors"
      >
        <X className="h-3 w-3" />
      </button>
    </div>
  );
}

export function PositionDepthPools({ onClose }: PositionDepthPoolsProps) {
  const { players, updatePlayer, rotationRules } = useTeam();
  const [pools, setPools] = useState<PositionPool[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [expandedPositions, setExpandedPositions] = useState<Set<string>>(new Set(POSITIONS.map(p => p.key)));
  const isMobile = useIsMobile();

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const competitiveMode = rotationRules?.competitiveMode || false;

  // Initialize pools from player data
  useEffect(() => {
    const newPools = POSITIONS.map(pos => {
      const positionPlayers: typeof pools[0]['players'] = [];
      
      players.forEach(player => {
        if (!player.name.trim()) return;
        
        const role = player.teamRoles?.[pos.key as keyof TeamRolePreferences];
        if (role && role !== 'avoid' && role !== 'unset') {
          // Map role to priority: go-to = 1, capable = 2, fill-in = 3
          let priority = positionPlayers.length + 1;
          if (role === 'go-to') priority = 1;
          else if (role === 'capable') priority = 2;
          else priority = 3;
          
          positionPlayers.push({
            id: player.id,
            name: player.name,
            priority
          });
        }
      });
      
      // Sort by priority
      positionPlayers.sort((a, b) => a.priority - b.priority);
      
      // Renumber priorities sequentially
      positionPlayers.forEach((p, idx) => {
        p.priority = idx + 1;
      });
      
      return {
        position: pos.key,
        label: pos.label,
        players: positionPlayers
      };
    });
    
    setPools(newPools);
  }, [players]);

  // Get available players for adding to positions
  const getAvailablePlayersForPosition = (position: string) => {
    const poolPlayerIds = pools.find(p => p.position === position)?.players.map(p => p.id) || [];
    
    return players.filter(player => {
      if (!player.name.trim()) return false;
      if (poolPlayerIds.includes(player.id)) return false;
      
      // In competitive mode, only show players who don't already have this position
      // (since we're adding them, not checking if they CAN play)
      const currentRole = player.teamRoles?.[position as keyof TeamRolePreferences];
      if (currentRole && currentRole !== 'avoid' && currentRole !== 'unset') {
        return false; // Already assigned to this position
      }
      
      // Remove position count limit - let coaches assign players to all 9 positions if needed
      
      return true;
    });
  };

  // Get player's position count
  const getPlayerPositionCount = (playerId: string) => {
    return pools.filter(pool => 
      pool.players.some(p => p.id === playerId)
    ).length;
  };

  // Get player's positions as string
  const getPlayerPositions = (playerId: string) => {
    const positions = pools
      .filter(pool => pool.players.some(p => p.id === playerId))
      .map(pool => pool.label);
    
    if (positions.length === 0) return 'No positions';
    if (positions.length > 3) return `${positions.slice(0, 3).join('•')}+${positions.length - 3}`;
    return positions.join('•');
  };

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    
    if (!over) return;
    
    // Parse the IDs (format: "position-playerId")
    const [activePos, activePlayerId] = (active.id as string).split('-');
    const [overPos, overPlayerId] = (over.id as string).split('-');
    
    if (activePos === overPos) {
      // Reordering within same position
      setPools(prev => {
        const newPools = [...prev];
        const poolIndex = newPools.findIndex(p => p.position === activePos);
        const pool = newPools[poolIndex];
        
        const oldIndex = pool.players.findIndex(p => p.id === activePlayerId);
        const newIndex = pool.players.findIndex(p => p.id === overPlayerId);
        
        pool.players = arrayMove(pool.players, oldIndex, newIndex);
        
        // Renumber priorities
        pool.players.forEach((p, idx) => {
          p.priority = idx + 1;
        });
        
        return newPools;
      });
    }
    
    setActiveId(null);
  };

  const addPlayerToPosition = async (position: string, playerId: string) => {
    const player = players.find(p => p.id === playerId);
    if (!player) return;
    
    setIsLoading(true);
    
    try {
      // Update player's teamRoles
      const newRoles = { ...player.teamRoles };
      const currentCount = getPlayerPositionCount(playerId);
      
      // Determine role based on how many positions they already have
      let role: 'go-to' | 'capable' | 'fill-in' = 'capable';
      if (currentCount === 0) role = 'go-to';
      else if (currentCount >= 2) role = 'fill-in';
      
      newRoles[position as keyof TeamRolePreferences] = role;
      
      await updatePlayer({
        ...player,
        teamRoles: newRoles
      });
      
      toast.success(`Added ${player.name} to ${POSITIONS.find(p => p.key === position)?.label}`);
    } catch (error) {
      console.error('Error adding player to position:', error);
      toast.error('Failed to add player to position');
    } finally {
      setIsLoading(false);
    }
  };

  const removePlayerFromPosition = async (position: string, playerId: string) => {
    const player = players.find(p => p.id === playerId);
    if (!player) return;
    
    setIsLoading(true);
    
    try {
      const newRoles = { ...player.teamRoles };
      delete newRoles[position as keyof TeamRolePreferences];
      
      await updatePlayer({
        ...player,
        teamRoles: newRoles
      });
      
      toast.success(`Removed ${player.name} from ${POSITIONS.find(p => p.key === position)?.label}`);
    } catch (error) {
      console.error('Error removing player from position:', error);
      toast.error('Failed to remove player from position');
    } finally {
      setIsLoading(false);
    }
  };

  const togglePosition = (position: string) => {
    setExpandedPositions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(position)) {
        newSet.delete(position);
      } else {
        newSet.add(position);
      }
      return newSet;
    });
  };

  // Add all players to a single position
  const addAllToPosition = async (position: string) => {
    setIsLoading(true);
    
    try {
      const updates: Promise<void>[] = [];
      let addedCount = 0;
      
      for (const player of players) {
        if (!player.name.trim()) continue;
        
        // Check if player already has this position
        const currentRole = player.teamRoles?.[position as keyof TeamRolePreferences];
        if (currentRole && currentRole !== 'avoid' && currentRole !== 'unset') {
          continue; // Already assigned
        }
        
        // Determine role based on current position count
        const currentPositions = pools.find(p => p.position === position)?.players.length || 0;
        let role: PlayerRole = 'capable';
        if (currentPositions === 0) role = 'go-to';
        else if (currentPositions >= 5) role = 'fill-in';
        
        updates.push(updatePlayer({
          ...player,
          teamRoles: {
            ...player.teamRoles,
            [position]: role
          }
        }));
        addedCount++;
      }
      
      await Promise.all(updates);
      const positionLabel = POSITIONS.find(p => p.key === position)?.label || position;
      toast.success(`Added ${addedCount} players to ${positionLabel}`);
    } catch (error) {
      console.error('Error adding all players to position:', error);
      toast.error('Failed to add players');
    } finally {
      setIsLoading(false);
    }
  };

  // Add all players to multiple positions
  const addAllToPositions = async (positions: string[]) => {
    setIsLoading(true);
    
    try {
      const updates: Promise<void>[] = [];
      
      for (const player of players) {
        if (!player.name.trim()) continue;
        
        const newRoles = { ...player.teamRoles };
        let hasChanges = false;
        
        for (const position of positions) {
          // Check if player already has this position
          const currentRole = player.teamRoles?.[position as keyof TeamRolePreferences];
          if (!currentRole || currentRole === 'avoid' || currentRole === 'unset') {
            // Determine role - for bulk operations, use 'capable' as default
            newRoles[position as keyof TeamRolePreferences] = 'capable' as PlayerRole;
            hasChanges = true;
          }
        }
        
        if (hasChanges) {
          updates.push(updatePlayer({
            ...player,
            teamRoles: newRoles
          }));
        }
      }
      
      await Promise.all(updates);
      const positionNames = positions.map(p => POSITIONS.find(pos => pos.key === p)?.label || p).join(', ');
      toast.success(`Added all players to ${positionNames}`);
    } catch (error) {
      console.error('Error adding all players to positions:', error);
      toast.error('Failed to add players');
    } finally {
      setIsLoading(false);
    }
  };

  // Clear all position assignments
  const clearAllPositions = async () => {
    if (!confirm('Remove ALL position assignments for ALL players? This cannot be undone.')) return;
    
    setIsLoading(true);
    
    try {
      const updates = players.map(player => 
        updatePlayer({
          ...player,
          teamRoles: {}
        })
      );
      
      await Promise.all(updates);
      toast.success('Cleared all position assignments');
    } catch (error) {
      console.error('Error clearing all positions:', error);
      toast.error('Failed to clear positions');
    } finally {
      setIsLoading(false);
    }
  };

  // Filter players for search
  const filteredPlayers = players.filter(player => 
    player.name.trim() && 
    player.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Check coverage warnings
  const getPositionWarnings = () => {
    const warnings: string[] = [];
    pools.forEach(pool => {
      if (pool.players.length === 0) {
        warnings.push(`${pool.label} has no players assigned`);
      } else if (pool.players.length === 1) {
        warnings.push(`${pool.label} needs backup players`);
      }
    });
    return warnings;
  };

  const warnings = getPositionWarnings();

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="h-full flex flex-col p-6 max-h-[95vh]">
        {/* Header */}
        <div className="flex items-center justify-between mb-4 flex-shrink-0">
          <h2 className="text-2xl font-bold text-gray-900">Position Depth Pools</h2>
          {onClose && (
            <Button onClick={onClose} variant="default">
              Done
            </Button>
          )}
        </div>

        {/* Mobile View */}
        {isMobile ? (
          <div className="flex-1 overflow-y-auto pb-4">
            <div className="space-y-2">
              {pools.map((pool) => {
                const isExpanded = expandedPositions.has(pool.position);
                const coverage = pool.players.length;
                
                return (
                  <div key={pool.position} className="bg-white border rounded-lg">
                    <button
                      onClick={() => togglePosition(pool.position)}
                      className="w-full p-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        {isExpanded ? <ChevronDown className="h-5 w-5" /> : <ChevronRight className="h-5 w-5" />}
                        <span className="font-medium">{pool.label}</span>
                        <span className="text-sm text-gray-500">({coverage} players)</span>
                      </div>
                      {coverage === 0 ? (
                        <Badge variant="destructive" className="text-xs">
                          Empty
                        </Badge>
                      ) : coverage === 1 ? (
                        <Badge variant="secondary" className="text-xs bg-yellow-100 text-yellow-800">
                          Needs backup
                        </Badge>
                      ) : (
                        <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                          ✓ Ready
                        </Badge>
                      )}
                    </button>
                    
                    {isExpanded && (
                      <div className="px-4 pb-4">
                        <div className="space-y-2">
                          {pool.players.map((player, idx) => (
                            <div
                              key={player.id}
                              className="flex items-center justify-between p-2 bg-gray-50 rounded"
                            >
                              <span className="text-sm">
                                <span className="text-gray-500 mr-2">{idx + 1}.</span>
                                {player.name}
                              </span>
                              <button
                                onClick={() => removePlayerFromPosition(pool.position, player.id)}
                                className="text-red-500 hover:text-red-700"
                              >
                                <X className="h-4 w-4" />
                              </button>
                            </div>
                          ))}
                          
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                className="w-full"
                                disabled={isLoading}
                              >
                                <Plus className="h-4 w-4 mr-1" />
                                Add Player
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="start" className="w-64">
                              {getAvailablePlayersForPosition(pool.position).length > 0 ? (
                                <div className="max-h-64 overflow-y-auto">
                                  {getAvailablePlayersForPosition(pool.position).map(player => (
                                    <DropdownMenuItem
                                      key={player.id}
                                      onClick={() => addPlayerToPosition(pool.position, player.id)}
                                      className="flex justify-between items-center cursor-pointer"
                                    >
                                      <span>{player.name}</span>
                                      <span className="text-xs text-gray-500">
                                        {getPlayerPositionCount(player.id)}/9
                                      </span>
                                    </DropdownMenuItem>
                                  ))}
                                </div>
                              ) : (
                                <div className="text-gray-500 text-sm px-3 py-2 text-center">
                                  No available players
                                </div>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
            
            {/* Mobile warnings */}
            {warnings.length > 0 && (
              <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <h4 className="text-sm font-medium text-red-800 mb-2 flex items-center gap-1">
                  <AlertTriangle className="h-4 w-4" />
                  Needs Attention
                </h4>
                <ul className="space-y-1">
                  {warnings.slice(0, 3).map((warning, idx) => (
                    <li key={idx} className="text-xs text-red-700">• {warning}</li>
                  ))}
                  {warnings.length > 3 && (
                    <li className="text-xs text-red-700">• And {warnings.length - 3} more...</li>
                  )}
                </ul>
              </div>
            )}
          </div>
        ) : (
          /* Desktop View */
          <div className="flex-1 flex gap-4 overflow-hidden">
          {/* Left Sidebar - Player Pool */}
          <div className="w-64 flex-shrink-0">
            <div className="bg-gray-50 rounded-lg p-4 h-full overflow-hidden flex flex-col">
              <h3 className="font-semibold mb-3 text-sm text-gray-700">Player Roster</h3>
            
            <div className="relative mb-3">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search players..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8 h-9"
              />
            </div>

            <div className="space-y-2 flex-1 overflow-y-auto">
              {filteredPlayers.map(player => {
                const positionCount = getPlayerPositionCount(player.id);
                const positions = getPlayerPositions(player.id);
                
                return (
                  <div
                    key={player.id}
                    className="p-3 bg-white rounded-md hover:bg-gray-100 transition-colors cursor-default"
                  >
                    <div className="font-medium text-sm">{player.name}</div>
                    <div className="text-xs text-gray-500 mt-0.5">
                      {positions} • {positionCount}/9 positions
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
          </div>

          {/* Main Content - Position Table */}
          <div className="flex-1 min-w-0 overflow-hidden flex flex-col">
            <div className="bg-white rounded-lg shadow-sm overflow-hidden flex-1 flex flex-col">
              <div className="overflow-x-auto overflow-y-auto flex-1">
                <table className="w-full min-w-[600px]">
                <thead className="sticky top-0 z-10">
                  <tr className="bg-gray-900 text-white">
                    <th className="text-left p-4 font-medium w-32">Position</th>
                    <th className="text-left p-4 font-medium">Player Pool (drag to reorder priority)</th>
                    <th className="text-center p-4 font-medium w-28">Coverage</th>
                  </tr>
                </thead>
                <tbody>
                  {pools.map((pool, index) => {
                    const isExpanded = expandedPositions.has(pool.position);
                    const coverage = pool.players.length;
                    
                    return (
                      <tr key={pool.position} className="border-b hover:bg-gray-50 transition-colors">
                        <td className="p-4 font-medium bg-gray-50">
                          <button
                            onClick={() => togglePosition(pool.position)}
                            className="flex items-center gap-2 text-gray-900 hover:text-blue-600 transition-colors w-full"
                          >
                            {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                            <span className="font-semibold">{pool.label}</span>
                          </button>
                        </td>
                        <td className="p-4">
                          {isExpanded && (
                            <div className="flex flex-wrap gap-2 items-center min-h-[40px]">
                              <SortableContext
                                items={pool.players.map(p => `${pool.position}-${p.id}`)}
                                strategy={horizontalListSortingStrategy}
                              >
                                {pool.players.map(player => (
                                  <PlayerChip
                                    key={player.id}
                                    player={player}
                                    position={pool.position}
                                    onRemove={removePlayerFromPosition}
                                  />
                                ))}
                              </SortableContext>
                              
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 text-xs px-2"
                                disabled={isLoading}
                                onClick={() => addAllToPosition(pool.position)}
                                title="Add all players to this position"
                              >
                                <Users className="h-3 w-3 mr-1" />
                                Add All
                              </Button>
                              
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 border-2 border-dashed border-gray-300 hover:border-gray-400"
                                    disabled={isLoading}
                                  >
                                    <Plus className="h-3 w-3 mr-1" />
                                    Add
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" className="w-52">
                                  {getAvailablePlayersForPosition(pool.position).length > 0 ? (
                                    <div className="max-h-64 overflow-y-auto">
                                      {getAvailablePlayersForPosition(pool.position).map(player => (
                                        <DropdownMenuItem
                                          key={player.id}
                                          onClick={() => addPlayerToPosition(pool.position, player.id)}
                                          className="flex justify-between items-center cursor-pointer"
                                        >
                                          <span>{player.name}</span>
                                          <span className="text-xs text-gray-500">
                                            {getPlayerPositionCount(player.id)}/9
                                          </span>
                                        </DropdownMenuItem>
                                      ))}
                                    </div>
                                  ) : (
                                    <div className="text-gray-500 text-sm px-3 py-2 text-center">
                                      No available players
                                    </div>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          )}
                        </td>
                        <td className="p-4 text-center bg-gray-50">
                          {coverage === 0 ? (
                            <span className="inline-flex items-center gap-1 text-red-600 font-medium">
                              <AlertTriangle className="h-4 w-4" />
                              Empty
                            </span>
                          ) : coverage === 1 ? (
                            <span className="inline-flex items-center gap-1 text-yellow-600 font-medium">
                              <AlertTriangle className="h-4 w-4" />
                              {coverage} only
                            </span>
                          ) : (
                            <span className="inline-flex items-center gap-1 text-green-600 font-medium">
                              <Check className="h-4 w-4" />
                              {coverage} players
                            </span>
                          )}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
              </div>
            </div>

            {/* Bulk Actions */}
            <div className="mt-4 flex flex-wrap gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="sm"
                    className="text-gray-600 hover:text-gray-900"
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Quick Add
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-56">
                  <DropdownMenuItem onClick={() => addAllToPositions(['leftField', 'centerField', 'rightField'])}>
                    <Users className="h-4 w-4 mr-2" />
                    Add all players to Outfield
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => addAllToPositions(['firstBase', 'secondBase', 'thirdBase', 'shortstop'])}>
                    <Users className="h-4 w-4 mr-2" />
                    Add all players to Infield
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => addAllToPositions(POSITIONS.map(p => p.key))}>
                    <Users className="h-4 w-4 mr-2" />
                    Add all players to all positions
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => addAllToPosition('pitcher')}>
                    Add all to Pitcher
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => addAllToPosition('catcher')}>
                    Add all to Catcher
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              
              <Button 
                variant="outline" 
                size="sm"
                onClick={clearAllPositions}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <X className="h-3 w-3 mr-1" />
                Clear All
              </Button>
            </div>
          </div>

          {/* Right Sidebar - Insights */}
          <div className="w-64 flex-shrink-0">
            <div className="bg-gray-50 rounded-lg p-4 h-full overflow-y-auto">
              <h3 className="font-semibold mb-3 text-sm text-gray-700">Quick Insights</h3>
            
            {/* Warnings */}
            {warnings.length > 0 && (
              <div className="mb-4 p-3 bg-red-50 rounded-md border border-red-200">
                <h4 className="text-xs font-medium text-red-700 mb-2 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  Needs Attention
                </h4>
                <ul className="space-y-1">
                  {warnings.map((warning, idx) => (
                    <li key={idx} className="text-xs text-red-600">• {warning}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Coverage Stats */}
            <div className="mb-4 p-3 bg-white rounded-md border border-gray-200">
              <h4 className="text-xs font-medium text-gray-700 mb-2 flex items-center gap-1">
                <Users className="h-3 w-3" />
                Coverage Stats
              </h4>
              <div className="space-y-2 text-xs text-gray-600">
                <div className="flex justify-between">
                  <span>Avg per position:</span>
                  <span className="font-medium">{(pools.reduce((sum, p) => sum + p.players.length, 0) / 9).toFixed(1)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Positions filled:</span>
                  <span className="font-medium">{pools.filter(p => p.players.length > 0).length}/9</span>
                </div>
                <div className="pt-2 border-t">
                  <span className="text-gray-500">Most flexible:</span>
                  <div className="font-medium text-gray-700">
                    {players.reduce((best, player) => {
                      const count = getPlayerPositionCount(player.id);
                      if (count > (best.count || 0)) {
                        return { name: player.name, count };
                      }
                      return best;
                    }, { name: 'None', count: 0 }).name}
                  </div>
                </div>
              </div>
            </div>

            {/* Tips */}
            <div>
              <h4 className="text-xs font-medium text-gray-600 mb-2 flex items-center gap-1">
                <Info className="h-3 w-3" />
                Tips
              </h4>
              <ul className="space-y-1 text-xs text-gray-600">
                <li>• Drag players to reorder priority</li>
                <li>• Higher number = lower priority</li>
                <li>• Aim for 3+ players per position</li>
                {competitiveMode && <li>• Players only shown for assigned positions</li>}
              </ul>
            </div>
            </div>
          </div>
        </div>
        )}
      </div>

      <DragOverlay>
        {activeId ? (
          <div className="inline-flex items-center gap-1 px-3 py-1.5 bg-white border rounded-full text-sm font-medium shadow-lg">
            <span>Dragging player...</span>
          </div>
        ) : null}
      </DragOverlay>
    </DndContext>
  );
}
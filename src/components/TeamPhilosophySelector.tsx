import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { But<PERSON> } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { 
  Users, 
  Trophy, 
  Target, 
  ChevronDown, 
  ChevronUp,
  CheckCircle,
  Info
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { RotationRules } from '@/contexts/TeamContext';

interface TeamPhilosophySelectorProps {
  currentRules: RotationRules;
  onRulesChange: (rules: RotationRules) => void;
  disabled?: boolean;
}

type TeamPhilosophy = 'equal' | 'specialists' | 'competitive';

const PHILOSOPHIES = {
  equal: {
    id: 'equal',
    name: 'Equal Playing Time',
    description: 'Every player gets fair rotation through all positions',
    perfect: 'Recreation leagues, skill development, younger players',
    icon: Users,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    rules: {
      competitiveMode: false,
      rotateLineupEvery: 1,
      rotatePitcherEvery: 2,
      maxConsecutiveBenchInnings: 2,
      allowPitcherRotation: true,
      allowCatcherRotation: true
    },
    outcomes: [
      'Every player plays the same number of innings',
      'Everyone tries different positions throughout the game',
      'No one sits on bench more than 2 innings in a row',
      'Positions rotate every inning for maximum variety'
    ]
  },
  specialists: {
    id: 'specialists',
    name: 'Position Specialists',
    description: 'Players focus on their strengths but everyone still plays',
    perfect: 'Skilled recreation, house leagues, balanced competition',
    icon: Target,
    color: 'text-green-600',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
    rules: {
      competitiveMode: false,
      rotateLineupEvery: 2,
      rotatePitcherEvery: 3,
      maxConsecutiveBenchInnings: 2,
      allowPitcherRotation: true,
      allowCatcherRotation: true
    },
    outcomes: [
      'Players spend more time at their "Primary" positions',
      'Everyone still gets equal total playing time',
      '"In the Mix" players rotate within their position groups',
      'Balances specialization with development'
    ]
  },
  competitive: {
    id: 'competitive',
    name: 'Competitive Team',
    description: 'Best lineup to win while ensuring everyone plays',
    perfect: 'Travel ball, tournaments, championship games',
    icon: Trophy,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
    rules: {
      competitiveMode: true,
      rotateLineupEvery: 3,
      rotatePitcherEvery: 4,
      maxConsecutiveBenchInnings: 3,
      allowPitcherRotation: true,
      allowCatcherRotation: true
    },
    outcomes: [
      'Star players prioritized at key positions (P, C, SS)',
      'Strategic lineup optimization for winning',
      'Minimum playing time based on roster size and feasibility',
      'Uses player ratings for best possible defense'
    ]
  }
};

export const TeamPhilosophySelector: React.FC<TeamPhilosophySelectorProps> = ({
  currentRules,
  onRulesChange,
  disabled
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false);
  
  // Determine current philosophy based on rules
  const getCurrentPhilosophy = (): TeamPhilosophy => {
    if (currentRules.competitiveMode) return 'competitive';
    if (currentRules.rotateLineupEvery === 1) return 'equal';
    return 'specialists';
  };
  
  const [selectedPhilosophy, setSelectedPhilosophy] = useState<TeamPhilosophy>(getCurrentPhilosophy());
  const [customRules, setCustomRules] = useState(currentRules);

  const handlePhilosophyChange = (philosophy: TeamPhilosophy) => {
    setSelectedPhilosophy(philosophy);
    const newRules = {
      ...currentRules,
      ...PHILOSOPHIES[philosophy].rules
    };
    setCustomRules(newRules);
    onRulesChange(newRules);
  };

  const handleAdvancedChange = (updates: Partial<RotationRules>) => {
    const newRules = { ...customRules, ...updates };
    setCustomRules(newRules);
    onRulesChange(newRules);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Team Philosophy
          </CardTitle>
          <p className="text-sm text-muted-foreground mt-2">
            Choose how you want to manage playing time and positions
          </p>
        </CardHeader>
        <CardContent>
          <RadioGroup
            value={selectedPhilosophy}
            onValueChange={(value) => handlePhilosophyChange(value as TeamPhilosophy)}
            disabled={disabled}
            className="space-y-4"
          >
            {Object.entries(PHILOSOPHIES).map(([key, philosophy]) => {
              const Icon = philosophy.icon;
              const isSelected = selectedPhilosophy === key;
              
              return (
                <div key={key} className="relative">
                  <RadioGroupItem
                    value={key}
                    id={key}
                    className="peer sr-only"
                  />
                  <Label
                    htmlFor={key}
                    className={cn(
                      "flex flex-col gap-3 rounded-lg border-2 p-4 cursor-pointer transition-all",
                      "hover:bg-gray-50",
                      isSelected ? philosophy.borderColor : "border-gray-200",
                      isSelected && philosophy.bgColor,
                      disabled && "opacity-50 cursor-not-allowed"
                    )}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <Icon className={cn("w-5 h-5", philosophy.color)} />
                        <div>
                          <h4 className="font-semibold">{philosophy.name}</h4>
                          <p className="text-sm text-muted-foreground">
                            {philosophy.description}
                          </p>
                        </div>
                      </div>
                      {isSelected && (
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      )}
                    </div>
                    
                    <div className="text-xs text-muted-foreground">
                      <span className="font-medium">Perfect for:</span> {philosophy.perfect}
                    </div>
                    
                    {isSelected && (
                      <div className="mt-2 pt-3 border-t space-y-2">
                        <p className="text-xs font-medium text-gray-700">What this means:</p>
                        <ul className="space-y-1">
                          {philosophy.outcomes.map((outcome, idx) => (
                            <li key={idx} className="text-xs text-gray-600 flex items-start gap-2">
                              <CheckCircle className="w-3 h-3 text-green-600 mt-0.5 flex-shrink-0" />
                              <span>{outcome}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </Label>
                </div>
              );
            })}
          </RadioGroup>

          {/* Advanced Settings */}
          <div className="mt-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="w-full justify-between"
              disabled={disabled}
            >
              <span className="flex items-center gap-2">
                <Info className="w-4 h-4" />
                Advanced Settings
              </span>
              {showAdvanced ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
            </Button>
            
            {showAdvanced && (
              <div className="mt-4 space-y-6 p-4 bg-gray-50 rounded-lg">
                <div className="space-y-3">
                  <div>
                    <Label className="text-sm font-medium">
                      Rotation Frequency
                    </Label>
                    <p className="text-xs text-muted-foreground mb-2">
                      How often do players switch positions?
                    </p>
                    <div className="flex items-center gap-4">
                      <span className="text-xs w-20">Every inning</span>
                      <Slider
                        value={[customRules.rotateLineupEvery]}
                        onValueChange={([value]) => handleAdvancedChange({ rotateLineupEvery: value })}
                        min={1}
                        max={3}
                        step={1}
                        className="flex-1"
                        disabled={disabled}
                      />
                      <span className="text-xs w-20 text-right">Every 3 innings</span>
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">
                      Pitcher Changes
                    </Label>
                    <p className="text-xs text-muted-foreground mb-2">
                      How long do pitchers stay on the mound?
                    </p>
                    <div className="flex items-center gap-4">
                      <span className="text-xs w-20">2 innings</span>
                      <Slider
                        value={[customRules.rotatePitcherEvery]}
                        onValueChange={([value]) => handleAdvancedChange({ rotatePitcherEvery: value })}
                        min={2}
                        max={4}
                        step={1}
                        className="flex-1"
                        disabled={disabled}
                      />
                      <span className="text-xs w-20 text-right">4 innings</span>
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">
                      Maximum Bench Time
                    </Label>
                    <p className="text-xs text-muted-foreground mb-2">
                      Longest anyone sits on the bench?
                    </p>
                    <div className="flex items-center gap-4">
                      <span className="text-xs w-20">2 innings</span>
                      <Slider
                        value={[customRules.maxConsecutiveBenchInnings]}
                        onValueChange={([value]) => handleAdvancedChange({ maxConsecutiveBenchInnings: value })}
                        min={2}
                        max={3}
                        step={1}
                        className="flex-1"
                        disabled={disabled}
                      />
                      <span className="text-xs w-20 text-right">3 innings</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Not sure message */}
      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
        <p className="text-sm text-blue-800">
          <strong>Not sure?</strong> Start with "Equal Playing Time" - it's the most balanced approach 
          and you can always change it later. Most recreation leagues use this setting.
        </p>
      </div>
    </div>
  );
};
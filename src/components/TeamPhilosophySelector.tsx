import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { But<PERSON> } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { 
  Users, 
  Trophy, 
  Target, 
  ChevronDown, 
  ChevronUp,
  CheckCircle,
  Info
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { RotationRules } from '@/contexts/TeamContext';

interface TeamPhilosophySelectorProps {
  currentRules: RotationRules;
  onRulesChange: (rules: RotationRules) => void;
  disabled?: boolean;
}

type TeamPhilosophy = 'equal' | 'specialists' | 'competitive';

const PHILOSOPHIES = {
  equal: {
    id: 'equal',
    name: 'Equal Playing Time',
    description: 'Every player gets fair rotation through all positions',
    perfect: 'Recreation leagues, skill development, younger players',
    icon: Users,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    rules: {
      competitiveMode: false,
      rotateLineupEvery: 1,
      rotatePitcherEvery: 2,
      allowPitcherRotation: true,
      allowCatcherRotation: true
    },
    outcomes: [
      'Every player plays the same number of innings',
      'Everyone rotates through different positions (even "Primary" players)',
      'No one sits on bench more than 1 inning in a row',
      'Players can play ANY position (unless marked "Never")',
      'Position assignments are suggestions, not strict rules'
    ]
  },
  specialists: {
    id: 'specialists',
    name: 'Position Specialists',
    description: 'Players focus on their strengths but everyone still plays',
    perfect: 'Skilled recreation, house leagues, balanced competition',
    icon: Target,
    color: 'text-green-600',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
    rules: {
      competitiveMode: false,
      rotateLineupEvery: 2,
      rotatePitcherEvery: 3,
      allowPitcherRotation: true,
      allowCatcherRotation: true
    },
    outcomes: [
      'Players favor their "Primary" positions but still rotate',
      'Everyone still gets equal total playing time',
      'Rotation happens less frequently (every 2 innings)',
      'Players can play ANY position (unless marked "Never")',
      'Balances specialization with development'
    ]
  },
  competitive: {
    id: 'competitive',
    name: 'Competitive Team',
    description: 'Best lineup to win while ensuring everyone plays',
    perfect: 'Travel ball, tournaments, championship games',
    icon: Trophy,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
    rules: {
      competitiveMode: true,
      rotateLineupEvery: 3,
      rotatePitcherEvery: 4,
      allowPitcherRotation: true,
      allowCatcherRotation: true
    },
    outcomes: [
      'Star players prioritized at key positions (P, C, SS)',
      'Players ONLY play positions you\'ve assigned them',
      'Strategic lineup optimization for winning',
      'Be sure to assign all positions each player can handle!'
    ]
  }
};

export const TeamPhilosophySelector: React.FC<TeamPhilosophySelectorProps> = ({
  currentRules,
  onRulesChange,
  disabled
}) => {
  const [showAdvanced, setShowAdvanced] = useState(true); // Start expanded to increase visibility
  
  // Determine current philosophy based on rules
  const getCurrentPhilosophy = (): TeamPhilosophy => {
    if (currentRules.competitiveMode) return 'competitive';
    if (currentRules.rotateLineupEvery === 1) return 'equal';
    return 'specialists';
  };
  
  const [selectedPhilosophy, setSelectedPhilosophy] = useState<TeamPhilosophy>(getCurrentPhilosophy());
  const [customRules, setCustomRules] = useState(currentRules);

  const handlePhilosophyChange = (philosophy: TeamPhilosophy) => {
    setSelectedPhilosophy(philosophy);
    const newRules = {
      ...currentRules,
      ...PHILOSOPHIES[philosophy].rules
    };
    setCustomRules(newRules);
    onRulesChange(newRules);
  };

  const handleAdvancedChange = (updates: Partial<RotationRules>) => {
    const newRules = { ...customRules, ...updates };
    setCustomRules(newRules);
    onRulesChange(newRules);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Team Philosophy
          </CardTitle>
          <p className="text-sm text-muted-foreground mt-2">
            Choose how you want to manage playing time and positions
          </p>
        </CardHeader>
        <CardContent>
          <RadioGroup
            value={selectedPhilosophy}
            onValueChange={(value) => handlePhilosophyChange(value as TeamPhilosophy)}
            disabled={disabled}
            className="space-y-4"
          >
            {Object.entries(PHILOSOPHIES).map(([key, philosophy]) => {
              const Icon = philosophy.icon;
              const isSelected = selectedPhilosophy === key;
              
              return (
                <div key={key} className="relative">
                  <RadioGroupItem
                    value={key}
                    id={key}
                    className="peer sr-only"
                  />
                  <Label
                    htmlFor={key}
                    className={cn(
                      "flex flex-col gap-3 rounded-lg border-2 p-4 cursor-pointer transition-all",
                      "hover:bg-gray-50",
                      isSelected ? philosophy.borderColor : "border-gray-200",
                      isSelected && philosophy.bgColor,
                      disabled && "opacity-50 cursor-not-allowed"
                    )}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <Icon className={cn("w-5 h-5", philosophy.color)} />
                        <div>
                          <h4 className="font-semibold">{philosophy.name}</h4>
                          <p className="text-sm text-muted-foreground">
                            {philosophy.description}
                          </p>
                        </div>
                      </div>
                      {isSelected && (
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      )}
                    </div>
                    
                    <div className="text-xs text-muted-foreground">
                      <span className="font-medium">Perfect for:</span> {philosophy.perfect}
                    </div>
                    
                    {isSelected && (
                      <div className="mt-2 pt-3 border-t space-y-2">
                        <p className="text-xs font-medium text-gray-700">What this means:</p>
                        <ul className="space-y-1">
                          {philosophy.outcomes.map((outcome, idx) => (
                            <li key={idx} className="text-xs text-gray-600 flex items-start gap-2">
                              <CheckCircle className="w-3 h-3 text-green-600 mt-0.5 flex-shrink-0" />
                              <span>{outcome}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </Label>
                </div>
              );
            })}
          </RadioGroup>

          {/* Advanced Settings - Made more prominent */}
          <div className="mt-6">
            <Button
              variant={showAdvanced ? "secondary" : "outline"}
              size="default"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="w-full justify-between font-medium"
              disabled={disabled}
            >
              <span className="flex items-center gap-2">
                <Info className="w-5 h-5" />
                Advanced Settings
                <span className="text-xs bg-baseball-green text-white px-2 py-0.5 rounded-full">
                  Customize
                </span>
              </span>
              {showAdvanced ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
            </Button>
            
            {showAdvanced && (
              <div className="mt-4 space-y-6 p-4 bg-gray-50 rounded-lg border-2 border-gray-200">
                <div className="mb-4">
                  <h4 className="font-medium text-sm text-gray-900">Fine-tune your rotation settings</h4>
                  <p className="text-xs text-gray-600 mt-1">
                    Adjust these sliders to perfectly match your league rules
                  </p>
                </div>
                <div className="space-y-3">
                  <div>
                    <Label className="text-sm font-medium">
                      Rotation Frequency
                    </Label>
                    <p className="text-xs text-muted-foreground mb-2">
                      How often do players switch positions?
                    </p>
                    <div className="flex items-center gap-4">
                      <span className="text-xs w-20">Every inning</span>
                      <Slider
                        value={[customRules.rotateLineupEvery]}
                        onValueChange={([value]) => handleAdvancedChange({ rotateLineupEvery: value })}
                        min={1}
                        max={3}
                        step={1}
                        className="flex-1"
                        disabled={disabled}
                      />
                      <span className="text-xs w-20 text-right">Every 3 innings</span>
                    </div>
                    <div className="text-center mt-1">
                      <span className="text-xs text-muted-foreground">
                        Current: Every {customRules.rotateLineupEvery} {customRules.rotateLineupEvery === 1 ? 'inning' : 'innings'}
                      </span>
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">
                      Pitcher Changes
                    </Label>
                    <p className="text-xs text-muted-foreground mb-2">
                      How long do pitchers stay on the mound?
                    </p>
                    <div className="flex items-center gap-4">
                      <span className="text-xs w-20">1 inning</span>
                      <Slider
                        value={[customRules.rotatePitcherEvery]}
                        onValueChange={([value]) => handleAdvancedChange({ rotatePitcherEvery: value })}
                        min={1}
                        max={4}
                        step={1}
                        className="flex-1"
                        disabled={disabled}
                      />
                      <span className="text-xs w-20 text-right">4 innings</span>
                    </div>
                    <div className="text-center mt-1">
                      <span className="text-xs text-muted-foreground">
                        Current: {customRules.rotatePitcherEvery} {customRules.rotatePitcherEvery === 1 ? 'inning' : 'innings'}
                      </span>
                    </div>
                  </div>

                  {/* Default Innings Setting */}
                  <div className="pt-4 border-t">
                    <Label className="text-sm font-medium">
                      Default Game Length
                    </Label>
                    <p className="text-xs text-muted-foreground mb-2">
                      Set your typical game length (can be changed per game)
                    </p>
                    <div className="flex items-center gap-4">
                      <span className="text-xs w-20">3 innings</span>
                      <Slider
                        value={[customRules.defaultInnings || 6]}
                        onValueChange={([value]) => handleAdvancedChange({ defaultInnings: value })}
                        min={3}
                        max={9}
                        step={1}
                        className="flex-1"
                        disabled={disabled}
                      />
                      <span className="text-xs w-20 text-right">9 innings</span>
                    </div>
                    <div className="text-center mt-1">
                      <span className="text-xs text-muted-foreground">
                        Default: {customRules.defaultInnings || 6} innings
                      </span>
                    </div>
                  </div>

                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Not sure message */}
      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
        <p className="text-sm text-blue-800">
          <strong>Not sure?</strong> Start with "Equal Playing Time" - it's the most balanced approach 
          and you can always change it later. Most recreation leagues use this setting.
        </p>
      </div>
    </div>
  );
};
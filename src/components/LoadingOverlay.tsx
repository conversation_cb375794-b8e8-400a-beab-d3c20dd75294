import React from 'react';
import { Loader2 } from 'lucide-react';

interface LoadingOverlayProps {
  message?: string;
  fullScreen?: boolean;
  className?: string;
}

/**
 * Universal loading indicator following Google/Apple design principles
 * - Clear visual feedback during async operations
 * - Prevents user confusion and repeated clicks
 * - Accessible with proper ARIA attributes
 */
export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ 
  message = "Loading...", 
  fullScreen = true,
  className = "" 
}) => {
  if (fullScreen) {
    return (
      <div 
        className="fixed inset-0 bg-black/20 backdrop-blur-sm z-50 flex items-center justify-center"
        role="status"
        aria-live="polite"
        aria-busy="true"
      >
        <div className="bg-white rounded-xl shadow-2xl p-6 max-w-sm mx-4 animate-in fade-in-0 slide-in-from-bottom-4 duration-300">
          <div className="flex items-center space-x-3">
            <Loader2 className="w-8 h-8 text-baseball-green animate-spin" />
            <p className="font-medium text-gray-900">{message}</p>
          </div>
        </div>
      </div>
    );
  }

  // Inline loading state for smaller components
  return (
    <div 
      className={`flex items-center justify-center space-x-2 p-4 ${className}`}
      role="status"
      aria-live="polite"
      aria-busy="true"
    >
      <Loader2 className="w-5 h-5 text-baseball-green animate-spin" />
      <span className="text-sm font-medium text-gray-700">{message}</span>
    </div>
  );
};

/**
 * Button loading state following Material Design principles
 * Provides immediate feedback without layout shift
 */
export const ButtonLoadingState: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <span className="relative inline-flex items-center">
      <span className="opacity-0">{children}</span>
      <span className="absolute inset-0 flex items-center justify-center">
        <Loader2 className="w-4 h-4 animate-spin" />
      </span>
    </span>
  );
};

export default LoadingOverlay;
import React, { useState } from 'react';
import { Player, PitcherStrategy } from '@/contexts/TeamContext';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { 
  Zap, 
  Target, 
  Trophy, 
  Clock,
  Settings,
  Save,
  RotateCcw,
  AlertTriangle
} from 'lucide-react';
import { toast } from 'sonner';

interface PitcherStrategyManagerProps {
  players: Player[];
  onStrategyChange: (playerId: string, strategy: PitcherStrategy) => void;
  gameLength?: number; // Number of innings in typical game
  seriesLength?: number; // Number of games in series
  disabled?: boolean;
}

const PITCHER_ROLES = {
  starter: {
    name: 'Starter',
    icon: Zap,
    description: 'Pitches early innings (1st-3rd typically)',
    color: 'bg-blue-500',
    defaultMaxInnings: 3,
    suggestedInnings: [1, 2, 3]
  },
  reliever: {
    name: 'Reliever', 
    icon: Target,
    description: 'Pitches middle innings (3rd-5th typically)',
    color: 'bg-green-500',
    defaultMaxInnings: 2,
    suggestedInnings: [3, 4, 5]
  },
  closer: {
    name: 'Closer',
    icon: Trophy,
    description: 'Pitches final innings (6th-7th typically)', 
    color: 'bg-yellow-500',
    defaultMaxInnings: 2,
    suggestedInnings: [6, 7]
  },
  any: {
    name: 'Flexible',
    icon: RotateCcw,
    description: 'Can pitch any inning as needed',
    color: 'bg-gray-500',
    defaultMaxInnings: 2,
    suggestedInnings: []
  }
};

const PitcherStrategyManager = ({ 
  players, 
  onStrategyChange, 
  gameLength = 7, 
  seriesLength = 1,
  disabled = false 
}: PitcherStrategyManagerProps) => {
  const [strategies, setStrategies] = useState<{[playerId: string]: PitcherStrategy}>(() => {
    // Initialize with default strategies based on current teamRoles
    const initial: {[playerId: string]: PitcherStrategy} = {};
    players.forEach(player => {
      const pitcherRole = player.teamRoles?.pitcher;
      let defaultRole: PitcherStrategy['role'] = 'any';
      let priority = 3;
      
      // Set defaults based on current teamRoles
      if (pitcherRole === 'primary') {
        defaultRole = 'starter';
        priority = 1;
      } else if (pitcherRole === 'in_the_mix') {
        defaultRole = 'reliever';
        priority = 2;
      } else if (pitcherRole === 'emergency') {
        defaultRole = 'any';
        priority = 4;
      }
      
      // Use existing pitcher strategy if available, otherwise create default
      initial[player.id] = player.pitcherStrategy || {
        role: defaultRole,
        maxInningsPerGame: PITCHER_ROLES[defaultRole].defaultMaxInnings,
        maxInningsPerSeries: PITCHER_ROLES[defaultRole].defaultMaxInnings * Math.min(seriesLength, 3),
        priority,
        restInningsBetweenAppearances: 1
      };
    });
    return initial;
  });

  // Filter to only show players who can pitch
  const eligiblePitchers = players.filter(player => {
    const pitcherRole = player.teamRoles?.pitcher;
    return pitcherRole && pitcherRole !== 'never' && pitcherRole !== 'avoid';
  });

  const updateStrategy = (playerId: string, updates: Partial<PitcherStrategy>) => {
    console.log(`PitcherStrategyManager: Updating strategy for player ${playerId}`, {
      currentStrategy: strategies[playerId],
      updates: updates
    });
    const newStrategy = { ...strategies[playerId], ...updates };
    console.log(`PitcherStrategyManager: New strategy:`, newStrategy);
    setStrategies(prev => ({ ...prev, [playerId]: newStrategy }));
    onStrategyChange(playerId, newStrategy);
  };

  const getRoleIcon = (role: PitcherStrategy['role']) => {
    const IconComponent = PITCHER_ROLES[role].icon;
    return <IconComponent className="w-4 h-4" />;
  };

  const getRoleBadge = (role: PitcherStrategy['role'], priority: number) => {
    const roleInfo = PITCHER_ROLES[role];
    return (
      <Badge 
        variant="secondary" 
        className={`${roleInfo.color} text-white text-xs`}
      >
        {getRoleIcon(role)}
        <span className="ml-1">{roleInfo.name}</span>
        <span className="ml-1 text-xs opacity-75">#{priority}</span>
      </Badge>
    );
  };

  const validateStrategies = () => {
    const roleGroups = {
      starter: eligiblePitchers.filter(p => strategies[p.id]?.role === 'starter'),
      closer: eligiblePitchers.filter(p => strategies[p.id]?.role === 'closer'),
      reliever: eligiblePitchers.filter(p => strategies[p.id]?.role === 'reliever')
    };

    const warnings = [];
    if (roleGroups.starter.length === 0) warnings.push("No starting pitchers assigned");
    if (roleGroups.closer.length === 0 && gameLength > 5) warnings.push("No closing pitchers assigned for longer games");
    if (roleGroups.reliever.length === 0 && gameLength > 4) warnings.push("No relief pitchers assigned");

    return warnings;
  };

  const applySmartDefaults = () => {
    const sortedPitchers = [...eligiblePitchers].sort((a, b) => {
      const roleA = a.teamRoles?.pitcher;
      const roleB = b.teamRoles?.pitcher;
      const priority = { primary: 1, in_the_mix: 2, emergency: 3 };
      return (priority[roleA] || 4) - (priority[roleB] || 4);
    });

    const newStrategies = { ...strategies };
    
    // Assign roles intelligently
    sortedPitchers.forEach((player, index) => {
      let role: PitcherStrategy['role'] = 'any';
      const priority = index + 1;

      if (index === 0) {
        role = 'starter'; // Best pitcher starts
      } else if (index === 1 && sortedPitchers.length >= 3) {
        role = 'closer'; // Second best closes
      } else if (index < sortedPitchers.length - 1) {
        role = 'reliever'; // Middle pitchers relieve
      }

      newStrategies[player.id] = {
        ...newStrategies[player.id],
        role,
        priority,
        maxInningsPerGame: PITCHER_ROLES[role].defaultMaxInnings,
        maxInningsPerSeries: PITCHER_ROLES[role].defaultMaxInnings * Math.min(seriesLength, 3)
      };
    });

    setStrategies(newStrategies);
    
    // Apply all changes
    Object.entries(newStrategies).forEach(([playerId, strategy]) => {
      onStrategyChange(playerId, strategy);
    });

    toast.success("Applied smart pitcher role defaults based on team roles");
  };

  const warnings = validateStrategies();

  if (eligiblePitchers.length === 0) {
    return (
      <Card className="border-yellow-200 bg-yellow-50">
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 text-yellow-800">
            <AlertTriangle className="w-5 h-5" />
            <p>No eligible pitchers found. Set pitcher team roles first in the Team Roles manager.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Zap className="w-5 h-5" />
              Pitcher Strategy Management
            </CardTitle>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={applySmartDefaults}
                disabled={disabled}
              >
                <Settings className="w-4 h-4 mr-1" />
                Smart Defaults
              </Button>
            </div>
          </div>
          <p className="text-sm text-gray-600">
            Configure pitcher roles, innings limits, and rotation strategy for {gameLength}-inning games
            {seriesLength > 1 && ` across ${seriesLength} games`}.
          </p>
        </CardHeader>
        <CardContent>
          {warnings.length > 0 && (
            <div className="mb-4 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
              <div className="flex items-center gap-2 text-yellow-800 mb-2">
                <AlertTriangle className="w-4 h-4" />
                <span className="font-medium">Strategy Warnings:</span>
              </div>
              <ul className="text-sm text-yellow-700 space-y-1">
                {warnings.map((warning, idx) => (
                  <li key={idx}>• {warning}</li>
                ))}
              </ul>
            </div>
          )}

          <div className="space-y-4">
            {eligiblePitchers.map(player => {
              const strategy = strategies[player.id];
              const roleInfo = PITCHER_ROLES[strategy.role];
              
              return (
                <Card key={player.id} className="border-l-4" style={{ borderLeftColor: roleInfo.color.replace('bg-', '#') }}>
                  <CardContent className="pt-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <span className="font-medium">{player.name}</span>
                        {getRoleBadge(strategy.role, strategy.priority)}
                      </div>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm" disabled={disabled}>
                            <Settings className="w-4 h-4 mr-1" />
                            Configure
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-md">
                          <DialogHeader>
                            <DialogTitle>{player.name} - Pitcher Strategy</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <Label>Pitcher Role</Label>
                              <Select 
                                value={strategy.role} 
                                onValueChange={(value: PitcherStrategy['role']) => 
                                  updateStrategy(player.id, { 
                                    role: value,
                                    maxInningsPerGame: PITCHER_ROLES[value].defaultMaxInnings 
                                  })
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {Object.entries(PITCHER_ROLES).map(([key, role]) => (
                                    <SelectItem key={key} value={key}>
                                      <div className="flex items-center gap-2">
                                        {React.createElement(role.icon, { className: "w-4 h-4" })}
                                        <div>
                                          <div className="font-medium">{role.name}</div>
                                          <div className="text-xs text-gray-500">{role.description}</div>
                                        </div>
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="grid grid-cols-2 gap-3">
                              <div>
                                <Label>Priority (1-5)</Label>
                                <Input 
                                  type="number" 
                                  min="1" 
                                  max="5" 
                                  value={strategy.priority}
                                  onChange={(e) => updateStrategy(player.id, { priority: parseInt(e.target.value) || 1 })}
                                />
                              </div>
                              <div>
                                <Label>Max Innings/Game</Label>
                                <Input 
                                  type="number" 
                                  min="1" 
                                  max={gameLength} 
                                  value={strategy.maxInningsPerGame || 2}
                                  onChange={(e) => updateStrategy(player.id, { maxInningsPerGame: parseInt(e.target.value) || 2 })}
                                />
                              </div>
                            </div>

                            {seriesLength > 1 && (
                              <div>
                                <Label>Max Innings Across Series</Label>
                                <Input 
                                  type="number" 
                                  min="1" 
                                  max={gameLength * seriesLength} 
                                  value={strategy.maxInningsPerSeries || strategy.maxInningsPerGame}
                                  onChange={(e) => updateStrategy(player.id, { maxInningsPerSeries: parseInt(e.target.value) || strategy.maxInningsPerGame })}
                                />
                              </div>
                            )}

                            <div>
                              <Label>Rest Between Appearances (innings)</Label>
                              <Input 
                                type="number" 
                                min="0" 
                                max="5" 
                                value={strategy.restInningsBetweenAppearances || 1}
                                onChange={(e) => updateStrategy(player.id, { restInningsBetweenAppearances: parseInt(e.target.value) || 1 })}
                              />
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>

                    <div className="text-sm text-gray-600 space-y-1">
                      <div>Role: {roleInfo.description}</div>
                      <div className="flex gap-4">
                        <span>Max: {strategy.maxInningsPerGame || 2} innings/game</span>
                        {seriesLength > 1 && <span>Series: {strategy.maxInningsPerSeries || strategy.maxInningsPerGame} total</span>}
                        <span>Rest: {strategy.restInningsBetweenAppearances || 1} innings</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="text-sm text-blue-800">
              <div className="font-medium mb-1">How Pitcher Strategy Works:</div>
              <ul className="space-y-1 text-xs">
                <li>• <strong>Starters</strong> pitch early innings and get priority in innings 1-3</li>
                <li>• <strong>Relievers</strong> handle middle innings (3-5) and provide flexibility</li>
                <li>• <strong>Closers</strong> pitch final innings (6-7) when the game is on the line</li>
                <li>• Priority determines who gets chosen first within each role (1 = highest)</li>
                <li>• Innings limits prevent overuse and ensure fair distribution</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PitcherStrategyManager;
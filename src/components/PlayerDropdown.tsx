import React, { useState, useRef, useEffect } from 'react';
import { Player } from '@/contexts/TeamContext';
import { ChevronDown, ChevronUp, Check, AlertTriangle } from 'lucide-react';

interface PlayerDropdownProps {
  players: Player[];
  value: string;
  onChange: (value: string) => void;
  position: string;
  positionLabel: string;
  findPlayerCurrentPosition: (playerName: string) => string | null;
  getPositionLabel: (positionId: string) => string;
  checkPositionRestriction?: (player: Player, position: string) => string | null;
}

const PlayerDropdown: React.FC<PlayerDropdownProps> = ({
  players,
  value,
  onChange,
  position,
  positionLabel,
  findPlayerCurrentPosition,
  getPositionLabel,
  checkPositionRestriction
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSelect = (playerName: string) => {
    console.log(`PlayerDropdown: Selected player ${playerName} for position ${position}`);
    onChange(playerName === "none" ? "" : playerName);
    setIsOpen(false);
  };

  const displayValue = value || "-- Select Player --";

  return (
    <div className="relative w-full" ref={dropdownRef}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="flex h-9 w-full items-center justify-between rounded-md border border-input bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-ring"
      >
        <span className="truncate">{displayValue}</span>
        {isOpen ? (
          <ChevronUp className="h-4 w-4 opacity-50" />
        ) : (
          <ChevronDown className="h-4 w-4 opacity-50" />
        )}
      </button>

      {isOpen && (
        <div className="absolute z-50 mt-1 max-h-60 w-full overflow-auto rounded-md border bg-white py-1 shadow-lg">
          <div 
            className="px-2 py-1.5 text-sm hover:bg-gray-100 cursor-pointer flex items-center"
            onClick={() => handleSelect("none")}
          >
            {value === "" && <Check className="h-4 w-4 mr-2" />}
            <span>-- Select Player --</span>
          </div>
          
          {players.map(player => {
            const currentPosition = findPlayerCurrentPosition(player.name);
            const isAssigned = currentPosition !== null;
            const hasRestriction = !position.startsWith('bench') && 
              checkPositionRestriction && 
              player.positionRestrictions && 
              checkPositionRestriction(player, position) !== null;

            return (
              <div
                key={player.id}
                className="px-2 py-1.5 text-sm hover:bg-gray-100 cursor-pointer flex items-center"
                onClick={() => handleSelect(player.name)}
              >
                {value === player.name && <Check className="h-4 w-4 mr-2" />}
                <span>
                  {player.name}
                  {hasRestriction && (
                    <span className="ml-1 text-amber-500 inline-flex items-center">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      (Restricted)
                    </span>
                  )}
                  {isAssigned && currentPosition !== position && (
                    <span className="ml-1 text-gray-500">
                      (in {getPositionLabel(currentPosition)})
                    </span>
                  )}
                </span>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default PlayerDropdown;

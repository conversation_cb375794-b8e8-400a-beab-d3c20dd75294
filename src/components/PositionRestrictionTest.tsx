import { useEffect, useState } from "react";
import { useTeam } from "@/contexts/TeamContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AlertCircle, CheckCircle, XCircle } from "lucide-react";
import { canPlayPosition } from "@/lib/utils-enhanced";

export function PositionRestrictionTest() {
  const { players, selectedTeam, rotationRules } = useTeam();
  const [testResults, setTestResults] = useState<any[]>([]);

  useEffect(() => {
    if (!players || players.length === 0) return;

    const positions = [
      'Pitcher', 'Catcher', 'First Base', 'Second Base', 
      'Third Base', 'Shortstop', 'Left Field', 'Center Field', 'Right Field'
    ];

    const results = players.map(player => {
      const positionChecks = positions.map(position => ({
        position,
        canPlay: canPlayPosition(player, position, rotationRules?.respectPositionLockouts ?? true, rotationRules?.competitiveMode ?? false),
        teamRole: player.teamRoles?.[position.toLowerCase().replace(' ', '')] || 'not set'
      }));

      return {
        player,
        positionChecks,
        restrictedPositions: positionChecks.filter(p => !p.canPlay).map(p => p.position)
      };
    });

    setTestResults(results);
  }, [players]);

  if (!selectedTeam) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-muted-foreground">Please select a team first</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertCircle className="w-5 h-5" />
          Position Restriction Test
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {testResults.map(({ player, positionChecks, restrictedPositions }) => (
            <div key={player.id} className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-semibold">{player.name}</h3>
                {restrictedPositions.length > 0 ? (
                  <Badge variant="destructive" className="text-xs">
                    {restrictedPositions.length} restrictions
                  </Badge>
                ) : (
                  <Badge variant="outline" className="text-xs">
                    No restrictions
                  </Badge>
                )}
              </div>
              
              <div className="grid grid-cols-3 gap-2 text-sm">
                {positionChecks.map(({ position, canPlay, teamRole }) => (
                  <div 
                    key={position} 
                    className={`flex items-center gap-1 p-1 rounded ${
                      canPlay ? 'bg-green-50' : 'bg-red-50'
                    }`}
                  >
                    {canPlay ? (
                      <CheckCircle className="w-3 h-3 text-green-600" />
                    ) : (
                      <XCircle className="w-3 h-3 text-red-600" />
                    )}
                    <span className={canPlay ? 'text-green-700' : 'text-red-700'}>
                      {position}
                    </span>
                    {teamRole === 'avoid' && (
                      <span className="text-xs text-red-600">(Never)</span>
                    )}
                  </div>
                ))}
              </div>

              {/* Debug info */}
              <div className="mt-2 text-xs text-muted-foreground">
                <details>
                  <summary className="cursor-pointer">Debug Info</summary>
                  <pre className="mt-1 p-2 bg-gray-100 rounded overflow-x-auto">
                    {JSON.stringify({
                      teamRoles: player.teamRoles || {},
                      positionPreferences: player.positionPreferences || {}
                    }, null, 2)}
                  </pre>
                </details>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
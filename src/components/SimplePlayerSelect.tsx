import React, { useEffect } from 'react';
import { Player } from '@/contexts/TeamContext';

interface SimplePlayerSelectProps {
  players: Player[];
  value: string;
  onChange: (value: string) => void;
  position: string;
  positionLabel: string;
  findPlayerCurrentPosition?: (playerName: string) => string | null;
  getPositionLabel?: (positionId: string) => string;
  checkPositionRestriction?: (player: Player, position: string) => string | null;
}

const SimplePlayerSelect: React.FC<SimplePlayerSelectProps> = ({
  players,
  value,
  onChange,
  position,
  positionLabel,
  findPlayerCurrentPosition,
  getPositionLabel,
  checkPositionRestriction
}) => {
  // Log when component renders
  useEffect(() => {
    console.log(`SimplePlayerSelect for ${position} rendered with value: "${value}"`);
  }, [position, value]);

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedValue = e.target.value;
    console.log(`SimplePlayerSelect: Selected ${selectedValue} for position ${position}`);

    // Call the onChange handler with the selected value
    if (selectedValue === "none") {
      onChange("");
    } else {
      onChange(selectedValue);
    }

    // Force a direct update to the DOM for immediate feedback
    setTimeout(() => {
      const event = new Event('change', { bubbles: true });
      e.target.dispatchEvent(event);
    }, 0);
  };

  // Prepare player options with restriction information
  const playerOptions = players.map(player => {
    let label = player.name;
    let isRestricted = false;

    // Check for position restrictions if the function is provided
    if (checkPositionRestriction && !position.startsWith('bench')) {
      const restriction = checkPositionRestriction(player, position);
      if (restriction) {
        label += ` (Restricted: ${restriction})`;
        isRestricted = true;
      }
    }

    // Check if player is already assigned elsewhere
    if (findPlayerCurrentPosition) {
      const currentPosition = findPlayerCurrentPosition(player.name);
      if (currentPosition && currentPosition !== position && getPositionLabel) {
        label += ` (in ${getPositionLabel(currentPosition)})`;
      }
    }

    return {
      id: player.id,
      name: player.name,
      label,
      isRestricted
    };
  });

  return (
    <select
      value={value || "none"}
      onChange={handleChange}
      className="w-full h-9 rounded-md border border-input bg-white px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-ring"
    >
      <option value="none">-- Select Player --</option>
      {playerOptions.map(player => (
        <option
          key={player.id}
          value={player.name}
          style={{ color: player.isRestricted ? 'orange' : 'inherit' }}
        >
          {player.label}
        </option>
      ))}
    </select>
  );
};

export default SimplePlayerSelect;

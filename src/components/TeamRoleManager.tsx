import React, { useState } from 'react';
import { Player, PlayerRole, TeamRolePreferences } from '@/contexts/TeamContext';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { 
  Star, 
  CheckCircle, 
  AlertCircle, 
  XCircle, 
  HelpCircle, 
  Users, 
  Settings,
  RotateCcw,
  Save,
  Loader2,
  AlertTriangle,
  Target
} from 'lucide-react';
import { toast } from 'sonner';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';

interface TeamRoleManagerProps {
  player: Player;
  onRoleChange: (playerId: string, teamRoles: TeamRolePreferences) => void;
  competitiveMode?: boolean;
  disabled?: boolean;
  compact?: boolean;
  onSave?: () => void; // Optional save function for popup
}

// Position groupings that reflect real baseball team structure
const POSITION_GROUPS = {
  battery: {
    name: 'Pitcher & Catcher',
    description: 'Specialized positions requiring dedicated players',
    positions: [
      { key: 'pitcher', label: 'Pitcher', abbrev: 'P' },
      { key: 'catcher', label: 'Catcher', abbrev: 'C' }
    ]
  },
  infield: {
    name: 'Infield Positions',
    description: 'Corner and middle infield - each requires specific skills',
    positions: [
      { key: 'firstBase', label: 'First Base', abbrev: '1B' },
      { key: 'secondBase', label: 'Second Base', abbrev: '2B' },
      { key: 'shortstop', label: 'Shortstop', abbrev: 'SS' },
      { key: 'thirdBase', label: 'Third Base', abbrev: '3B' }
    ]
  },
  outfield: {
    name: 'Outfield Positions',
    description: 'Outfield coverage - varying skill requirements',
    positions: [
      { key: 'leftField', label: 'Left Field', abbrev: 'LF' },
      { key: 'centerField', label: 'Center Field', abbrev: 'CF' },
      { key: 'rightField', label: 'Right Field', abbrev: 'RF' }
    ]
  }
};

// Player grouping system - think "these are my 1B players"
const PLAYER_ROLES = {
  'go-to': {
    label: 'Primary',
    description: 'Your #1 choice',
    fullDescription: 'Your #1 choice for this position (1-2 players max)',
    icon: Star,
    color: 'bg-green-100 text-green-800 border-green-200',
    competitiveLabel: 'Primary',
    nonCompetitiveLabel: 'Primary'
  },
  'capable': {
    label: 'In the Mix',
    description: 'Regular player',
    fullDescription: 'Part of your player group for this position',
    icon: CheckCircle,
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    competitiveLabel: 'In the Mix',
    nonCompetitiveLabel: 'In the Mix'
  },
  'fill-in': {
    label: 'Emergency',
    description: 'Backup only',
    fullDescription: 'Only when you need extra coverage',
    icon: AlertCircle,
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    competitiveLabel: 'Emergency',
    nonCompetitiveLabel: 'Emergency'
  },
  'avoid': {
    label: 'Never',
    description: 'Cannot play',
    fullDescription: 'This player should never play this position',
    icon: XCircle,
    color: 'bg-red-100 text-red-800 border-red-200',
    competitiveLabel: 'Never',
    nonCompetitiveLabel: 'Never'
  },
  'unset': {
    label: 'Not Set',
    description: 'No role',
    fullDescription: 'No grouping specified',
    icon: HelpCircle,
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    competitiveLabel: 'Unset',
    nonCompetitiveLabel: 'Unset'
  }
};


export const TeamRoleManager: React.FC<TeamRoleManagerProps> = ({
  player,
  onRoleChange,
  competitiveMode = false,
  disabled = false,
  compact = false,
  onSave
}) => {
  const [showHelp, setShowHelp] = useState(false);
  const [showPositionInfo, setShowPositionInfo] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const isMobile = useIsMobile();

  // Get current role for a position
  const getCurrentRole = (position: string): PlayerRole => {
    return player.teamRoles?.[position as keyof TeamRolePreferences] as PlayerRole || 'unset';
  };

  // Handle role change
  const handleRoleChange = (position: string, role: PlayerRole) => {
    if (disabled) return;

    console.log(`TeamRoleManager: Changing ${player.name} - ${position} to ${role}`);
    console.log('Current teamRoles:', player.teamRoles);
    console.log('Is this pitcher position?', position === 'pitcher');

    const updatedRoles = {
      ...player.teamRoles,
      [position]: role === 'unset' ? undefined : role
    };

    console.log('Updated teamRoles:', updatedRoles);
    console.log('Pitcher role in updated:', updatedRoles.pitcher);
    onRoleChange(player.id, updatedRoles);
    
    const roleInfo = PLAYER_ROLES[role];
    const positionLabel = Object.values(POSITION_GROUPS)
      .flatMap(group => group.positions)
      .find(pos => pos.key === position)?.label || position;
    
    // Show informational toast that changes need to be saved
    toast.info(`${player.name} set as ${roleInfo.label} for ${positionLabel} - Click "Save Changes" to persist`, {
      duration: 4000
    });
  };

  // Handle special designation changes
  const handleSpecialDesignation = (designation: 'isUtilityPlayer' | 'isOutfieldSpecialist', value: boolean) => {
    if (disabled) return;

    const updatedRoles = {
      ...player.teamRoles,
      [designation]: value || undefined
    };

    onRoleChange(player.id, updatedRoles);
    
    const label = designation === 'isUtilityPlayer' ? 'Utility Player' : 'Outfield Specialist';
    toast.success(`${value ? 'Set' : 'Removed'} ${player.name} as ${label}`);
  };


  // Clear all roles
  const clearAllRoles = () => {
    onRoleChange(player.id, {});
    toast.success(`Cleared all team roles for ${player.name}`);
  };

  // Get role summary for display
  const getRoleSummary = () => {
    const roles = player.teamRoles || {};
    const setRoles = Object.entries(roles)
      .filter(([key, value]) => value && key !== 'isUtilityPlayer' && key !== 'isOutfieldSpecialist')
      .map(([position, role]) => {
        const positionInfo = Object.values(POSITION_GROUPS)
          .flatMap(group => group.positions)
          .find(pos => pos.key === position);
        const roleInfo = PLAYER_ROLES[role as PlayerRole];
        
        return {
          position: positionInfo?.abbrev || position,
          role: role as PlayerRole,
          roleInfo
        };
      });

    const specialDesignations = [];
    if (roles.isUtilityPlayer) specialDesignations.push('Utility');
    if (roles.isOutfieldSpecialist) specialDesignations.push('OF Specialist');

    return { setRoles, specialDesignations };
  };

  if (compact) {
    const { setRoles, specialDesignations } = getRoleSummary();
    const hasRoles = setRoles.length > 0 || specialDesignations.length > 0;

    return (
      <div className="space-y-3">

        {/* Current Roles Summary */}
        {hasRoles ? (
          <div className="space-y-2">
            {/* Over-assignment Warning - only if truly excessive */}
            {setRoles.length > 6 && (
              <div className="flex items-center gap-1 text-xs text-amber-700 bg-amber-50 px-2 py-1 rounded">
                <AlertTriangle className="w-3 h-3" />
                <span className="font-medium">Many positions!</span>
                <span>Consider focusing on key positions</span>
              </div>
            )}
            
            {/* Position Roles */}
            {setRoles.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {setRoles.map(({ position, role, roleInfo }) => {
                  const IconComponent = roleInfo.icon;
                  return (
                    <Badge key={position} variant="outline" className={`text-xs ${roleInfo.color}`}>
                      {React.createElement(IconComponent, { className: "w-3 h-3 mr-1" })}
                      {position}: {competitiveMode ? roleInfo.competitiveLabel : roleInfo.nonCompetitiveLabel}
                    </Badge>
                  );
                })}
              </div>
            )}
            
            {/* Special Designations */}
            {specialDesignations.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {specialDesignations.map(designation => (
                  <Badge key={designation} variant="outline" className="text-xs bg-purple-100 text-purple-800 border-purple-200">
                    <Users className="w-3 h-3 mr-1" />
                    {designation}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        ) : (
          <div className="text-xs text-gray-500 italic">No team roles set</div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-1">
          <Dialog>
            <DialogTrigger asChild>
              <Button 
                variant="outline" 
                size="sm" 
                className="h-7 text-xs"
                title={competitiveMode 
                  ? "Competitive: Player ONLY plays assigned positions" 
                  : "Recreational: Player can play any position unless marked 'Never'"
                }
              >
                <Settings className="w-3 h-3 mr-1" />
                Position Assignments
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[85vh] overflow-y-auto" style={{ marginTop: 'max(20px, 5vh)' }}>
              <DialogHeader className="sticky top-0 bg-white z-10 pb-4 border-b">
                <DialogTitle>Position Assignments - {player.name}</DialogTitle>
              </DialogHeader>
              <div className="overflow-y-auto">
                <TeamRoleManager
                  player={player}
                  onRoleChange={onRoleChange}
                  competitiveMode={competitiveMode}
                  disabled={disabled}
                  compact={false}
                  onSave={onSave}
                />
              </div>
            </DialogContent>
          </Dialog>
          
          {hasRoles && (
            <Button
              variant="outline"
              size="sm"
              className="h-7 text-xs text-red-600 hover:text-red-700"
              onClick={clearAllRoles}
              disabled={disabled}
            >
              <RotateCcw className="w-3 h-3 mr-1" />
              Clear
            </Button>
          )}
        </div>
      </div>
    );
  }

  // Full interface
  return (
    <Card className="w-full">
      <CardHeader className={cn(
        isMobile ? "pb-2" : "pb-3"
      )}>
        <div className={cn(
          "flex items-center justify-between",
          isMobile ? "flex-col gap-2" : ""
        )}>
          <CardTitle className={cn(
            "flex items-center gap-2",
            isMobile ? "text-base" : "text-lg"
          )}>
            Position Assignments - {player.name}
            <button
              onClick={() => setShowPositionInfo(true)}
              className="text-gray-400 hover:text-gray-600 transition-colors"
              title="How position assignments work"
            >
              <AlertCircle className="w-4 h-4" />
            </button>
          </CardTitle>
          <div className={cn(
            "flex items-center gap-2",
            isMobile ? "w-full justify-between" : ""
          )}>
            <Badge variant="outline" className={cn(
              competitiveMode ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800',
              isMobile ? "text-[10px] px-2 py-0.5" : ""
            )}>
              {competitiveMode ? 'Competitive Mode' : 'Fair Play Mode'}
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowHelp(true)}
              className={isMobile ? "h-7 text-xs" : ""}
            >
              <HelpCircle className={cn(
                "mr-1",
                isMobile ? "w-3 h-3" : "w-4 h-4"
              )} />
              Help
            </Button>
          </div>
        </div>
        
        {/* Competitive Mode Banner */}
        {competitiveMode ? (
          <div className={cn(
            "bg-orange-50 rounded-lg border border-orange-200",
            isMobile ? "mt-3 p-2" : "mt-4 p-3"
          )}>
            <p className={cn(
              "text-orange-800 font-medium",
              isMobile ? "text-xs" : "text-sm"
            )}>
              🏆 Competitive Mode: This player will ONLY play positions you assign below
            </p>
            {!isMobile && (
              <p className="text-xs text-orange-700 mt-1">
                Unassigned positions = Cannot play there. Be thorough!
              </p>
            )}
          </div>
        ) : (
          <div className={cn(
            "bg-green-50 rounded-lg border border-green-200",
            isMobile ? "mt-3 p-2" : "mt-4 p-3"
          )}>
            <p className={cn(
              "text-green-800 font-medium",
              isMobile ? "text-xs" : "text-sm"
            )}>
              ⚾ Recreational Mode: This player can play any position unless marked "Never"
            </p>
            {!isMobile && (
              <p className="text-xs text-green-700 mt-1">
                Focus on key positions and restrictions. Unassigned = Available if needed.
              </p>
            )}
          </div>
        )}
        
        {/* Quick Instructions */}
        <div className={cn(
          "bg-blue-50 rounded-lg border border-blue-200",
          isMobile ? "mt-3 p-2" : "mt-4 p-3"
        )}>
          <p className={cn(
            "text-blue-800",
            isMobile ? "text-xs mb-1" : "text-sm mb-2"
          )}>
            <strong>Position Assignments:</strong> Assign each position based on your player's actual abilities.
          </p>
          {!isMobile && (
            <p className="text-xs text-blue-700">
              💡 <strong>Tip:</strong> Most players should be "In the Mix" for 2-4 positions. Only use "Primary" for your absolute best player at that position.
            </p>
          )}
        </div>
      </CardHeader>

      <CardContent className={cn(
        isMobile ? "space-y-4" : "space-y-6"
      )}>
        {/* Position Groups */}
        <div className={cn(
          isMobile ? "space-y-4" : "space-y-6"
        )}>
          <div className={cn(
            "text-center bg-amber-50 rounded-lg border border-amber-200",
            isMobile ? "p-2" : "p-3"
          )}>
            <p className={cn(
              "font-medium text-amber-800",
              isMobile ? "text-xs" : "text-sm"
            )}>
              Assign roles for each position below. The algorithm will use these to create optimal lineups.
            </p>
          </div>
          
          {Object.entries(POSITION_GROUPS).map(([groupKey, group]) => (
            <div key={groupKey} className={cn(
              "bg-gray-50 rounded-lg",
              isMobile ? "p-3" : "p-4"
            )}>
              <div className={cn(
                isMobile ? "mb-3" : "mb-4"
              )}>
                <h4 className={cn(
                  "font-semibold text-gray-900 flex items-center",
                  isMobile ? "text-sm" : "text-base"
                )}>
                  {groupKey === 'battery' && <Target className={cn(
                    "text-red-600",
                    isMobile ? "w-3 h-3 mr-1.5" : "w-4 h-4 mr-2"
                  )} />}
                  {groupKey === 'infield' && <Users className={cn(
                    "text-blue-600",
                    isMobile ? "w-3 h-3 mr-1.5" : "w-4 h-4 mr-2"
                  )} />}
                  {groupKey === 'outfield' && <Users className={cn(
                    "text-green-600",
                    isMobile ? "w-3 h-3 mr-1.5" : "w-4 h-4 mr-2"
                  )} />}
                  {group.name}
                </h4>
                <p className={cn(
                  "text-gray-600",
                  isMobile ? "text-xs" : "text-sm"
                )}>{group.description}</p>
              </div>

              <div className={cn(
                "grid gap-3",
                isMobile ? "grid-cols-1" : "grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3"
              )}>
              {group.positions.map(position => {
                const currentRole = getCurrentRole(position.key);

                return (
                  <div key={position.key} className={cn(
                    "bg-white border-2 rounded-lg hover:shadow-md transition-shadow overflow-hidden",
                    isMobile ? "p-2 space-y-1" : "p-3 space-y-2"
                  )}>
                    <div className={cn(
                      "text-center border-b",
                      isMobile ? "pb-1" : "pb-2"
                    )}>
                      <div className={cn(
                        "font-semibold truncate",
                        isMobile ? "text-xs" : "text-sm"
                      )}>{position.label}</div>
                      <div className={cn(
                        "text-gray-500",
                        isMobile ? "text-[10px]" : "text-xs"
                      )}>({position.abbrev})</div>
                    </div>

                    {/* Current Role Display */}
                    <div className={cn(
                      "text-center flex items-center justify-center",
                      isMobile ? "min-h-[24px]" : "min-h-[32px]"
                    )}>
                      {currentRole !== 'unset' ? (
                        <Badge variant="outline" className={cn(
                          PLAYER_ROLES[currentRole].color,
                          isMobile ? "text-[10px] px-2 py-0.5" : "text-sm px-3 py-1"
                        )}>
                          {React.createElement(PLAYER_ROLES[currentRole].icon, { 
                            className: isMobile ? "w-3 h-3 mr-0.5" : "w-4 h-4 mr-1" 
                          })}
                          {competitiveMode ? PLAYER_ROLES[currentRole].competitiveLabel : PLAYER_ROLES[currentRole].nonCompetitiveLabel}
                        </Badge>
                      ) : (
                        <span className={cn(
                          "text-gray-400 italic",
                          isMobile ? "text-[10px]" : "text-sm"
                        )}>No role assigned</span>
                      )}
                    </div>

                    {/* Role Selection Buttons */}
                    <div className={cn(
                      isMobile ? "space-y-0.5" : "space-y-1"
                    )}>
                      {Object.entries(PLAYER_ROLES).filter(([key]) => key !== 'unset').map(([roleKey, roleInfo]) => {
                        const isActive = currentRole === roleKey;
                        const IconComponent = roleInfo.icon;

                        return (
                          <Button
                            key={roleKey}
                            variant="outline"
                            size="sm"
                            className={cn(
                              "w-full justify-start transition-all",
                              isMobile ? "h-7 text-[10px] px-2" : "h-8 text-xs",
                              isActive
                                ? roleInfo.color.replace('100', '200').replace('800', '900') + ' shadow-sm border-2'
                                : 'hover:' + roleInfo.color.replace('100', '50')
                            )}
                            onClick={() => handleRoleChange(position.key, roleKey as PlayerRole)}
                            disabled={disabled}
                            title={roleInfo.fullDescription}
                          >
                            <div className="flex items-center justify-between w-full">
                              <div className={cn(
                                "flex items-center",
                                isMobile ? "gap-1" : "gap-2"
                              )}>
                                {React.createElement(IconComponent, { 
                                  className: isMobile ? "w-2.5 h-2.5 flex-shrink-0" : "w-3 h-3 flex-shrink-0" 
                                })}
                                <span className="font-medium">
                                  {competitiveMode ? roleInfo.competitiveLabel : roleInfo.nonCompetitiveLabel}
                                </span>
                              </div>
                              {!isMobile && (
                                <span className="text-[10px] text-gray-500 ml-2">
                                  {roleInfo.description}
                                </span>
                              )}
                            </div>
                          </Button>
                        );
                      })}

                      {/* Clear button */}
                      {currentRole !== 'unset' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className={cn(
                            "w-full text-gray-500 hover:text-gray-700",
                            isMobile ? "h-5 text-[10px]" : "h-6 text-xs"
                          )}
                          onClick={() => handleRoleChange(position.key, 'unset')}
                          disabled={disabled}
                        >
                          <RotateCcw className={cn(
                            isMobile ? "w-2.5 h-2.5 mr-0.5" : "w-3 h-3 mr-1"
                          )} />
                          Clear
                        </Button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
            </div>
          ))}
        </div>

        {/* Special Designations */}
        <div className={cn(
          "pt-4 border-t",
          isMobile ? "space-y-2" : "space-y-3"
        )}>
          <h4 className={cn(
            "font-medium text-gray-900",
            isMobile ? "text-xs" : "text-sm"
          )}>Quick Position Groups</h4>
          
          <div className={cn(
            isMobile ? "space-y-3" : "space-y-4"
          )}>
            {/* Utility Player */}
            <div className={cn(
              "border rounded-lg transition-colors",
              isMobile ? "p-3" : "p-4",
              player.teamRoles?.isUtilityPlayer ? 'bg-purple-50 border-purple-300' : 'bg-white border-gray-200'
            )}>
              <div className={cn(
                "flex items-start justify-between",
                isMobile ? "gap-2" : "gap-3"
              )}>
                <div className="space-y-1 flex-1">
                  <Label className={cn(
                    "font-medium flex items-center gap-2",
                    isMobile ? "text-xs" : "text-sm"
                  )}>
                    Utility Player
                    {player.teamRoles?.isUtilityPlayer && (
                      <Badge variant="outline" className={cn(
                        "bg-purple-100 text-purple-700 border-purple-300",
                        isMobile ? "text-[10px] px-1.5 py-0" : "text-xs"
                      )}>
                        ACTIVE
                      </Badge>
                    )}
                  </Label>
                  <p className={cn(
                    "text-gray-600",
                    isMobile ? "text-[10px]" : "text-xs"
                  )}>
                    Player can fill all 7 field positions: 1B, 2B, 3B, SS, LF, CF, RF
                  </p>
                  {!isMobile && (
                    <>
                      <p className="text-xs text-purple-600 font-medium mt-1">
                        ✓ Does NOT include Pitcher or Catcher (must be set separately)
                      </p>
                      <p className="text-xs text-purple-600">
                        ✓ Still respects "Never" settings for any position
                      </p>
                    </>
                  )}
                </div>
                <Switch
                  checked={player.teamRoles?.isUtilityPlayer || false}
                  onCheckedChange={(checked) => handleSpecialDesignation('isUtilityPlayer', checked)}
                  disabled={disabled}
                  className={isMobile ? "scale-75" : ""}
                />
              </div>
            </div>

            {/* Outfield Specialist */}
            <div className={cn(
              "border rounded-lg transition-colors",
              isMobile ? "p-3" : "p-4",
              player.teamRoles?.isOutfieldSpecialist ? 'bg-green-50 border-green-300' : 'bg-white border-gray-200'
            )}>
              <div className={cn(
                "flex items-start justify-between",
                isMobile ? "gap-2" : "gap-3"
              )}>
                <div className="space-y-1 flex-1">
                  <Label className={cn(
                    "font-medium flex items-center gap-2",
                    isMobile ? "text-xs" : "text-sm"
                  )}>
                    Outfield Rotation
                    {player.teamRoles?.isOutfieldSpecialist && (
                      <Badge variant="outline" className={cn(
                        "bg-green-100 text-green-700 border-green-300",
                        isMobile ? "text-[10px] px-1.5 py-0" : "text-xs"
                      )}>
                        ACTIVE
                      </Badge>
                    )}
                  </Label>
                  <p className={cn(
                    "text-gray-600",
                    isMobile ? "text-[10px]" : "text-xs"
                  )}>
                    Player rotates equally between all outfield positions (LF, CF, RF)
                  </p>
                  {!isMobile && (
                    <>
                      <p className="text-xs text-green-600 font-medium mt-1">
                        ✓ Can play any outfield position unless marked "Never"
                      </p>
                      <p className="text-xs text-green-600">
                        ✓ Perfect for players without a specific outfield preference
                      </p>
                    </>
                  )}
                </div>
                <Switch
                  checked={player.teamRoles?.isOutfieldSpecialist || false}
                  onCheckedChange={(checked) => handleSpecialDesignation('isOutfieldSpecialist', checked)}
                  disabled={disabled}
                  className={isMobile ? "scale-75" : ""}
                />
              </div>
            </div>
          </div>

          {/* Visual feedback when toggles are active */}
          {(player.teamRoles?.isUtilityPlayer || player.teamRoles?.isOutfieldSpecialist) && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-3">
              <p className="text-xs text-blue-700 font-medium flex items-center gap-2">
                <AlertCircle className="w-3 h-3" />
                <span>Active Quick Groups Override Individual Settings</span>
              </p>
              <p className="text-xs text-blue-600 mt-1">
                {player.teamRoles?.isUtilityPlayer && "• Utility: Can play 1B, 2B, 3B, SS, LF, CF, RF (Pitcher/Catcher require separate assignment)"}
              </p>
              <p className="text-xs text-blue-600">
                {player.teamRoles?.isOutfieldSpecialist && "• Outfield Rotation applies to LF, CF, and RF equally"}
              </p>
            </div>
          )}
        </div>

        {/* Summary Section */}
        {getRoleSummary().setRoles.length > 0 && (
          <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
            <h4 className="font-medium text-sm mb-3 text-blue-900">Current Role Assignments</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {getRoleSummary().setRoles.map(({ position, role, roleInfo }) => {
                const IconComponent = roleInfo.icon;
                return (
                  <div key={position} className="flex items-center gap-2 text-sm">
                    <Badge variant="outline" className={`${roleInfo.color} text-xs`}>
                      {React.createElement(IconComponent, { className: "w-3 h-3 mr-1" })}
                      {position}
                    </Badge>
                    <span className="text-gray-600">→</span>
                    <span className="font-medium">{roleInfo.label}</span>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-2 pt-4 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={clearAllRoles}
            disabled={disabled}
          >
            <RotateCcw className="w-4 h-4 mr-1" />
            Clear All Roles
          </Button>
          {onSave && (
            <Button
              variant="default"
              size="sm"
              onClick={async () => {
                console.log('TeamRoleManager: Save button clicked');
                console.log('Current player state:', player);
                if (onSave) {
                  setIsSaving(true);
                  try {
                    await onSave();
                    // Don't show success here - let the parent handle it
                  } catch (error) {
                    console.error('Error saving player roles:', error);
                    toast.error('Failed to save player roles');
                  } finally {
                    setIsSaving(false);
                  }
                }
              }}
              disabled={disabled || isSaving}
              className="ml-auto"
            >
              {isSaving ? (
                <>
                  <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-1" />
                  Save
                </>
              )}
            </Button>
          )}
        </div>
      </CardContent>

      {/* Position Info Dialog */}
      <Dialog open={showPositionInfo} onOpenChange={setShowPositionInfo}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-blue-500" />
              How Position Assignments Work
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4 text-sm">
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <p className="font-medium text-blue-900 mb-2">
                ✅ Players can play ANY position UNLESS:
              </p>
              <ul className="space-y-1 ml-6 text-blue-800">
                <li>• They're marked as "Never" for that position</li>
                <li>• The position has rotation restrictions (pitcher/catcher rules)</li>
              </ul>
            </div>

            <div className="bg-amber-50 p-4 rounded-lg border border-amber-200">
              <p className="font-medium text-amber-900 mb-2 flex items-center gap-2">
                <AlertTriangle className="w-4 h-4" />
                Important for Competitive Teams:
              </p>
              <p className="text-amber-800 mb-2">
                If no eligible players are available for a position, the system may assign 
                someone who isn't ideal but hasn't been explicitly marked as "Never".
              </p>
              <p className="text-amber-700 text-xs italic">
                The algorithm prioritizes filling all positions over leaving spots empty.
              </p>
            </div>

            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <p className="font-medium text-green-900 mb-2">
                💡 Tips for Best Results:
              </p>
              <ul className="space-y-1 ml-6 text-green-800">
                <li>• Mark "Never" for positions you don't want players to play</li>
                <li>• Use "Primary" and "In the Mix" to guide optimal assignments</li>
                <li>• The system respects your preferences but won't leave positions empty</li>
              </ul>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <p className="font-medium text-gray-900 mb-2">Example:</p>
              <p className="text-gray-700">
                If you only want Finn to play outfield, mark her as "Never" for all 
                infield positions (1B, 2B, 3B, SS) and pitcher/catcher.
              </p>
            </div>

            <div className="text-center mt-4">
              <Button onClick={() => setShowPositionInfo(false)}>
                Got it!
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Help Dialog */}
      <Dialog open={showHelp} onOpenChange={setShowHelp}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Team Roles Help</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 text-sm">
            <div>
              <h4 className="font-medium mb-2 text-lg">Quick Guide</h4>
              <div className="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400">
                <p className="mb-2"><strong>What you're doing:</strong> Tell the app which positions each player can play.</p>
                <p className="text-sm">It's just like filling out your team's depth chart!</p>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">The 4 Simple Levels:</h4>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <Star className="w-4 h-4 mr-2 mt-0.5 flex-shrink-0 text-green-600" />
                  <div>
                    <strong className="text-green-800">Primary</strong> = "They're my go-to"
                    <p className="text-xs text-gray-600 mt-0.5">Your best 1-2 players at this position</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="w-4 h-4 mr-2 mt-0.5 flex-shrink-0 text-blue-600" />
                  <div>
                    <strong className="text-blue-800">In the Mix</strong> = "They regularly play here"
                    <p className="text-xs text-gray-600 mt-0.5">Your rotation players - most should have 2-4 of these</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <AlertCircle className="w-4 h-4 mr-2 mt-0.5 flex-shrink-0 text-yellow-600" />
                  <div>
                    <strong className="text-yellow-800">Emergency</strong> = "Only if needed"
                    <p className="text-xs text-gray-600 mt-0.5">Can play there in a pinch, not their usual spot</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <XCircle className="w-4 h-4 mr-2 mt-0.5 flex-shrink-0 text-red-600" />
                  <div>
                    <strong className="text-red-800">Never</strong> = "Don't put them here"
                    <p className="text-xs text-gray-600 mt-0.5">App will never assign them to this position</p>
                  </div>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">Quick Start:</h4>
              <ol className="list-decimal list-inside space-y-2">
                <li>For each player, click their "Manage Positions" button</li>
                <li>Mark their main position as <span className="text-green-700 font-medium">Primary</span></li>
                <li>Mark other positions they play as <span className="text-blue-700 font-medium">In the Mix</span></li>
                <li>Mark any positions they can't play as <span className="text-red-700 font-medium">Never</span></li>
                <li>Leave the rest blank (they'll be emergency options)</li>
              </ol>
            </div>

            <div className="bg-amber-50 p-4 rounded-lg">
              <p className="text-sm">
                <strong>💡 Pro Tip:</strong> Don't overthink it! Start simple with just Primary 
                and Never positions. You can always adjust as the season progresses.
              </p>
            </div>

            <div>
              <h4 className="font-medium mb-2">Example:</h4>
              <div className="bg-gray-50 p-3 rounded-lg text-sm">
                <p className="font-medium mb-2">Sarah - Your utility player:</p>
                <ul className="space-y-1 text-xs">
                  <li>• <span className="text-green-700">Primary:</span> 2nd Base</li>
                  <li>• <span className="text-blue-700">In the Mix:</span> Shortstop, Center Field</li>
                  <li>• <span className="text-red-700">Never:</span> Pitcher, Catcher</li>
                  <li>• <span className="text-gray-500">Everything else:</span> Emergency (automatic)</li>
                </ul>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default TeamRoleManager;

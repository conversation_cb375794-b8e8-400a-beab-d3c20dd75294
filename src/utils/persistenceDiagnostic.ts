// Runtime persistence diagnostic
export const setupPersistenceDiagnostic = () => {
  console.log('🔍 PERSISTENCE DIAGNOSTIC ACTIVE');
  
  // Track what's in localStorage at start
  const initialState = {
    authToken: Object.keys(localStorage).find(k => k.startsWith('sb-')),
    defaultTeam: localStorage.getItem('default_team_id'),
    currentTeam: localStorage.getItem('current_team_id'),
    teamsCache: !!localStorage.getItem('teams_cache')
  };
  
  console.log('Initial state:', initialState);
  
  // Monitor all localStorage operations
  const originalSetItem = localStorage.setItem;
  const originalRemoveItem = localStorage.removeItem;
  const originalClear = localStorage.clear;
  
  // Track what's happening
  const operations: any[] = [];
  
  localStorage.setItem = function(key: string, value: string) {
    const op = {
      type: 'SET',
      key,
      value: value?.substring(0, 100),
      timestamp: new Date().toISOString(),
      stack: new Error().stack
    };
    operations.push(op);
    console.log(`📝 SET: ${key}`);
    return originalSetItem.apply(this, arguments as any);
  };
  
  localStorage.removeItem = function(key: string) {
    const op = {
      type: 'REMOVE',
      key,
      timestamp: new Date().toISOString(),
      stack: new Error().stack
    };
    operations.push(op);
    console.log(`🗑️ REMOVE: ${key}`);
    
    // ALERT on critical keys
    if (key.startsWith('sb-') || key === 'default_team_id' || key === 'current_team_id') {
      console.error(`⚠️ CRITICAL KEY REMOVED: ${key}`);
      console.trace();
    }
    
    return originalRemoveItem.apply(this, arguments as any);
  };
  
  localStorage.clear = function() {
    const op = {
      type: 'CLEAR',
      timestamp: new Date().toISOString(),
      stack: new Error().stack
    };
    operations.push(op);
    console.error('💥 localStorage.clear() called!');
    console.trace();
    return originalClear.apply(this, arguments as any);
  };
  
  // Expose diagnostic functions
  (window as any).persistenceDiag = {
    showOperations: () => {
      console.log('All localStorage operations:', operations);
    },
    checkCriticalKeys: () => {
      console.log('Critical keys status:');
      console.log('- Auth token:', Object.keys(localStorage).find(k => k.startsWith('sb-')));
      console.log('- Default team:', localStorage.getItem('default_team_id'));
      console.log('- Current team:', localStorage.getItem('current_team_id'));
    },
    getLastOperations: (n = 10) => {
      return operations.slice(-n);
    }
  };
  
  console.log('Diagnostic installed. Use window.persistenceDiag to inspect.');
};

// Also monitor sessionStorage
export const monitorSessionStorage = () => {
  const originalSetItem = sessionStorage.setItem;
  const originalRemoveItem = sessionStorage.removeItem;
  const originalClear = sessionStorage.clear;
  
  sessionStorage.setItem = function(key: string, value: string) {
    console.log(`📝 SESSION SET: ${key}`);
    return originalSetItem.apply(this, arguments as any);
  };
  
  sessionStorage.removeItem = function(key: string) {
    console.log(`🗑️ SESSION REMOVE: ${key}`);
    return originalRemoveItem.apply(this, arguments as any);
  };
  
  sessionStorage.clear = function() {
    console.error('💥 sessionStorage.clear() called!');
    console.trace();
    return originalClear.apply(this, arguments as any);
  };
};
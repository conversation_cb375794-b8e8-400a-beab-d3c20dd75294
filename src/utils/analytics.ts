// Google Analytics 4 Event Tracking Utility
// This file provides type-safe analytics event tracking for Dugout Boss

declare global {
  interface Window {
    gtag: (
      command: 'config' | 'event' | 'js' | 'set',
      targetId: string | Date,
      config?: Record<string, any>
    ) => void;
    dataLayer: any[];
  }
}

/**
 * Send a custom event to Google Analytics 4
 * @param eventName - The name of the event
 * @param parameters - Additional event parameters
 */
export const trackEvent = (
  eventName: string,
  parameters: Record<string, any> = {}
) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, {
      // Add default parameters
      page_title: document.title,
      page_location: window.location.href,
      ...parameters
    });
  }
};

/**
 * Track conversion events for business metrics
 */
export const trackConversion = {
  // User registration/signup
  signup: (method: 'email' | 'demo' = 'email') => {
    trackEvent('sign_up', {
      method,
      event_category: 'engagement',
      event_label: 'user_registration'
    });
  },

  // User login
  login: (method: 'email' | 'demo' = 'email') => {
    trackEvent('login', {
      method,
      event_category: 'engagement',
      event_label: 'user_login'
    });
  },

  // Lineup generation (core feature usage)
  lineupGenerated: (teamSize: number, mode: 'competitive' | 'recreational' = 'recreational') => {
    trackEvent('lineup_generated', {
      event_category: 'feature_usage',
      event_label: 'core_feature',
      custom_parameter_1: 'lineup_generation',
      team_size: teamSize,
      lineup_mode: mode,
      value: 1 // Each lineup generation has value
    });
  },

  // Purchase/upgrade events
  purchaseStarted: (value: number = 0) => {
    trackEvent('begin_checkout', {
      event_category: 'ecommerce',
      event_label: 'purchase_started',
      currency: 'USD',
      value: value,
      items: [{
        item_id: 'dugout-boss-subscription',
        item_name: 'Dugout Boss Subscription',
        category: 'Software',
        price: value,
        quantity: 1
      }]
    });
  },

  purchaseCompleted: (transactionId: string, value: number = 0) => {
    trackEvent('purchase', {
      event_category: 'ecommerce',
      event_label: 'purchase_completed',
      transaction_id: transactionId,
      currency: 'USD',
      value: value,
      items: [{
        item_id: 'dugout-boss-subscription',
        item_name: 'Dugout Boss Subscription',
        category: 'Software',
        price: value,
        quantity: 1
      }]
    });
  },

  // Feature engagement
  featureUsed: (featureName: string, details?: Record<string, any>) => {
    trackEvent('feature_used', {
      event_category: 'feature_usage',
      event_label: featureName,
      feature_name: featureName,
      ...details
    });
  },

  // Export/share actions
  exportLineup: (format: 'pdf' | 'csv') => {
    trackEvent('export_lineup', {
      event_category: 'feature_usage',
      event_label: 'export_action',
      export_format: format,
      value: 1
    });
  },

  // Help/support engagement
  helpEngagement: (section: string) => {
    trackEvent('help_engagement', {
      event_category: 'support',
      event_label: section,
      help_section: section
    });
  },

  // Demo mode usage
  demoModeStarted: () => {
    trackEvent('demo_started', {
      event_category: 'engagement',
      event_label: 'demo_mode',
      demo_type: 'full_access'
    });
  }
};

/**
 * Track page views manually (for SPA navigation)
 */
export const trackPageView = (pagePath: string, pageTitle?: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', 'G-DN3SKK2XSW', {
      page_path: pagePath,
      page_title: pageTitle || document.title
    });
  }
};

/**
 * Set user properties for better segmentation
 */
export const setUserProperties = (properties: Record<string, any>) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('set', 'user_properties', properties);
  }
};

/**
 * Track custom dimensions for advanced analytics
 */
export const setCustomDimensions = (dimensions: Record<string, any>) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('set', dimensions);
  }
};

// Export default tracking function
export default trackEvent;
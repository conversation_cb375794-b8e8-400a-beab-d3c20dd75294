/**
 * Demo Mode Security Validation Utilities
 * 
 * These functions help ensure demo mode users cannot perform write operations
 * that could affect the application state or database.
 */

export interface DemoModeValidationOptions {
  allowRead?: boolean;
  allowWrite?: boolean;
  customMessage?: string;
}

/**
 * Check if the current session is in demo mode
 * SECURITY: Only trust email-based detection, not localStorage flags
 */
export function isDemoMode(): boolean {
  const email = localStorage.getItem('email');
  return email === '<EMAIL>' ||
         email === '<EMAIL>';
}

/**
 * Validate if the current operation is allowed in demo mode
 * Throws an error if the operation is not permitted
 */
export function validateDemoModeOperation(
  operation: 'read' | 'write' | 'delete' | 'create',
  options: DemoModeValidationOptions = {}
): void {
  if (!isDemoMode()) {
    // Not in demo mode, all operations allowed
    return;
  }

  const {
    allowRead = true,
    allowWrite = false,
    customMessage
  } = options;

  switch (operation) {
    case 'read':
      if (!allowRead) {
        throw new Error(customMessage || 'Demo mode: Read operations not permitted');
      }
      break;
    
    case 'write':
    case 'create':
    case 'delete':
      if (!allowWrite) {
        throw new Error(
          customMessage || 
          'Demo mode: Changes cannot be saved permanently. This is a demonstration environment.'
        );
      }
      break;
  }
}

/**
 * Wrapper for async operations that should be restricted in demo mode
 */
export async function validateDemoModeAsync<T>(
  operation: 'read' | 'write' | 'delete' | 'create',
  asyncFn: () => Promise<T>,
  options: DemoModeValidationOptions = {}
): Promise<T> {
  validateDemoModeOperation(operation, options);
  return await asyncFn();
}

/**
 * Show a demo mode warning to users
 */
export function showDemoModeWarning(message?: string): void {
  if (isDemoMode()) {
    const defaultMessage = 'Demo Mode: Changes are temporary and will not be permanently saved.';
    console.warn(message || defaultMessage);
    
    // You could also show a toast notification here
    // toast.info(message || defaultMessage);
  }
}

/**
 * Get demo mode status and user info for display
 */
export function getDemoModeInfo() {
  if (!isDemoMode()) {
    return null;
  }

  return {
    isDemoMode: true,
    demoEmail: localStorage.getItem('demo_user_email') || '<EMAIL>',
    demoUserId: localStorage.getItem('demo_user_id') || 'f15ba189-c70f-4513-9451-6f95568e784a',
    teamId: localStorage.getItem('current_team_id') || '83bd9832-f5db-4c7d-b234-41fd38f90007'
  };
}
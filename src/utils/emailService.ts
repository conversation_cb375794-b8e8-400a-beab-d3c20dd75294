/**
 * Email Service Utility Module
 * Provides a reusable interface for sending emails through our app
 */

import { supabase } from '@/integrations/supabase/client';

export interface EmailOptions {
  to: string | string[];
  subject: string;
  html: string;
  text?: string;
  from?: string;
  replyTo?: string;
}

export interface WelcomeEmailData {
  email: string;
  name: string;
  temporaryPassword: string;
}

class EmailService {
  private readonly defaultFrom = 'Dugout Boss <<EMAIL>>';
  private readonly supportEmail = '<EMAIL>';

  /**
   * Send an email using our edge function
   */
  async sendEmail(functionName: string, data: any): Promise<{ success: boolean; error?: string }> {
    try {
      const { data: response, error } = await supabase.functions.invoke(functionName, {
        body: data
      });

      if (error) {
        console.error(`Email send error (${functionName}):`, error);
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      console.error(`Email send exception (${functionName}):`, error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Send a welcome email to a newly created user
   */
  async sendWelcomeEmail(userData: WelcomeEmailData): Promise<{ success: boolean; error?: string }> {
    return this.sendEmail('send-welcome-email', userData);
  }

  /**
   * Generate the HTML template for welcome emails
   */
  generateWelcomeEmailHtml(data: WelcomeEmailData): string {
    const loginUrl = `${window.location.origin}/signin`;
    const supportUrl = `${window.location.origin}/contact`;
    const docsUrl = `${window.location.origin}/help`;

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Dugout Boss</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f5f5f5;">
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #f5f5f5;">
        <tr>
            <td align="center" style="padding: 40px 0;">
                <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" style="background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <!-- Header -->
                    <tr>
                        <td align="center" style="padding: 40px 20px; background-color: #1e40af; border-radius: 8px 8px 0 0;">
                            <h1 style="margin: 0; color: #ffffff; font-size: 28px; font-weight: bold;">Dugout Boss</h1>
                            <p style="margin: 10px 0 0 0; color: #e0e7ff; font-size: 16px;">Smart Lineup Management for Coaches</p>
                        </td>
                    </tr>

                    <!-- Welcome Message -->
                    <tr>
                        <td style="padding: 40px 40px 20px;">
                            <h2 style="margin: 0 0 20px 0; color: #1e293b; font-size: 24px;">Welcome ${data.name}!</h2>
                            <p style="margin: 0 0 20px 0; color: #475569; font-size: 16px; line-height: 24px;">
                                Your Dugout Boss account has been created. You're now ready to start creating fair and optimized lineups for your team.
                            </p>
                        </td>
                    </tr>

                    <!-- Login Credentials Box -->
                    <tr>
                        <td style="padding: 0 40px 20px;">
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px;">
                                <tr>
                                    <td style="padding: 30px;">
                                        <h3 style="margin: 0 0 20px 0; color: #1e293b; font-size: 18px;">Your Login Credentials</h3>
                                        
                                        <p style="margin: 0 0 10px 0; color: #475569; font-size: 14px;">
                                            <strong>Email:</strong><br>
                                            <span style="font-size: 16px; color: #1e293b;">${data.email}</span>
                                        </p>
                                        
                                        <p style="margin: 0 0 20px 0; color: #475569; font-size: 14px;">
                                            <strong>Temporary Password:</strong><br>
                                            <span style="font-family: monospace; font-size: 16px; background-color: #fef3c7; color: #92400e; padding: 4px 8px; border-radius: 4px;">${data.temporaryPassword}</span>
                                        </p>
                                        
                                        <p style="margin: 0 0 20px 0; color: #dc2626; font-size: 14px; font-weight: bold;">
                                            ⚠️ Please change your password after your first login
                                        </p>
                                        
                                        <table role="presentation" cellspacing="0" cellpadding="0" border="0">
                                            <tr>
                                                <td style="background-color: #1e40af; border-radius: 6px;">
                                                    <a href="${loginUrl}" target="_blank" style="display: inline-block; padding: 12px 24px; color: #ffffff; text-decoration: none; font-size: 16px; font-weight: bold;">
                                                        Login to Dugout Boss
                                                    </a>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                    <!-- Getting Started Section -->
                    <tr>
                        <td style="padding: 20px 40px;">
                            <h3 style="margin: 0 0 20px 0; color: #1e293b; font-size: 20px;">Getting Started</h3>
                            
                            <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                                <tr>
                                    <td style="padding: 0 0 15px 0;">
                                        <table role="presentation" cellspacing="0" cellpadding="0" border="0">
                                            <tr>
                                                <td style="width: 30px; vertical-align: top;">
                                                    <span style="display: inline-block; width: 24px; height: 24px; background-color: #10b981; color: #ffffff; text-align: center; border-radius: 50%; font-size: 14px; line-height: 24px;">1</span>
                                                </td>
                                                <td style="padding-left: 10px;">
                                                    <p style="margin: 0; color: #475569; font-size: 16px;">
                                                        <strong style="color: #1e293b;">Set up your team roster</strong><br>
                                                        Add players and their position preferences
                                                    </p>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td style="padding: 0 0 15px 0;">
                                        <table role="presentation" cellspacing="0" cellpadding="0" border="0">
                                            <tr>
                                                <td style="width: 30px; vertical-align: top;">
                                                    <span style="display: inline-block; width: 24px; height: 24px; background-color: #10b981; color: #ffffff; text-align: center; border-radius: 50%; font-size: 14px; line-height: 24px;">2</span>
                                                </td>
                                                <td style="padding-left: 10px;">
                                                    <p style="margin: 0; color: #475569; font-size: 16px;">
                                                        <strong style="color: #1e293b;">Configure rotation rules</strong><br>
                                                        Set playing time goals and bench limits
                                                    </p>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <td style="padding: 0 0 15px 0;">
                                        <table role="presentation" cellspacing="0" cellpadding="0" border="0">
                                            <tr>
                                                <td style="width: 30px; vertical-align: top;">
                                                    <span style="display: inline-block; width: 24px; height: 24px; background-color: #10b981; color: #ffffff; text-align: center; border-radius: 50%; font-size: 14px; line-height: 24px;">3</span>
                                                </td>
                                                <td style="padding-left: 10px;">
                                                    <p style="margin: 0; color: #475569; font-size: 16px;">
                                                        <strong style="color: #1e293b;">Generate your first lineup</strong><br>
                                                        Let our AI create fair, optimized lineups instantly
                                                    </p>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                    <!-- Support Section -->
                    <tr>
                        <td style="padding: 20px 40px 40px; border-top: 1px solid #e2e8f0;">
                            <h3 style="margin: 0 0 15px 0; color: #1e293b; font-size: 18px;">Need Help?</h3>
                            <p style="margin: 0; color: #475569; font-size: 16px; line-height: 24px;">
                                We're here to help you get the most out of Dugout Boss:
                            </p>
                            <ul style="margin: 10px 0 0 20px; padding: 0; color: #475569; font-size: 16px; line-height: 24px;">
                                <li style="margin: 0 0 8px 0;">📧 Email us at <a href="mailto:${this.supportEmail}" style="color: #1e40af; text-decoration: none;">${this.supportEmail}</a></li>
                                <li style="margin: 0 0 8px 0;">📚 Visit our <a href="${docsUrl}" target="_blank" style="color: #1e40af; text-decoration: none;">documentation</a></li>
                                <li style="margin: 0;">💬 Use the in-app <a href="${supportUrl}" target="_blank" style="color: #1e40af; text-decoration: none;">contact form</a></li>
                            </ul>
                        </td>
                    </tr>

                    <!-- Footer -->
                    <tr>
                        <td align="center" style="padding: 30px; background-color: #f8fafc; border-radius: 0 0 8px 8px;">
                            <p style="margin: 0 0 10px 0; color: #64748b; font-size: 14px;">
                                © ${new Date().getFullYear()} Dugout Boss. All rights reserved.
                            </p>
                            <p style="margin: 0; color: #64748b; font-size: 12px;">
                                This email was sent because an administrator created an account for you.
                            </p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>
    `;
  }

  /**
   * Generate plain text version of the welcome email
   */
  generateWelcomeEmailText(data: WelcomeEmailData): string {
    const loginUrl = `${window.location.origin}/signin`;
    
    return `
Welcome to Dugout Boss!

Hi ${data.name},

Your Dugout Boss account has been created. You're now ready to start creating fair and optimized lineups for your team.

YOUR LOGIN CREDENTIALS
=====================
Email: ${data.email}
Temporary Password: ${data.temporaryPassword}

⚠️ IMPORTANT: Please change your password after your first login.

Login here: ${loginUrl}

GETTING STARTED
===============
1. Set up your team roster - Add players and their position preferences
2. Configure rotation rules - Set playing time goals and bench limits
3. Generate your first lineup - Let our AI create fair, optimized lineups instantly

NEED HELP?
==========
We're here to help you get the most out of Dugout Boss:
- Email us at ${this.supportEmail}
- Visit our documentation at ${window.location.origin}/help
- Use the in-app contact form at ${window.location.origin}/contact

Best regards,
The Dugout Boss Team

---
© ${new Date().getFullYear()} Dugout Boss. All rights reserved.
This email was sent because an administrator created an account for you.
    `;
  }
}

export const emailService = new EmailService();
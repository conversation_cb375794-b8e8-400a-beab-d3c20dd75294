export const debugAuthStorage = () => {
  console.log('🔐 Auth Storage Debug');
  console.log('===================');
  
  // Check Supabase tokens
  const supabaseTokens: Record<string, any> = {};
  const otherAuthItems: Record<string, any> = {};
  
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key) {
      if (key.startsWith('sb-')) {
        const value = localStorage.getItem(key);
        // Don't log full tokens for security
        supabaseTokens[key] = value ? value.substring(0, 50) + '...' : null;
      } else if (key.includes('auth') || key.includes('session') || key.includes('user')) {
        otherAuthItems[key] = localStorage.getItem(key);
      }
    }
  }
  
  console.log('\n📦 Supabase Tokens:', supabaseTokens);
  console.log('\n🔑 Other Auth Items:', otherAuthItems);
  
  // Check sessionStorage
  const sessionItems: Record<string, any> = {};
  for (let i = 0; i < sessionStorage.length; i++) {
    const key = sessionStorage.key(i);
    if (key) {
      sessionItems[key] = sessionStorage.getItem(key);
    }
  }
  console.log('\n💾 Session Storage:', sessionItems);
  
  // Check if we have the expected Supabase auth token structure
  const hasAuthToken = Object.keys(supabaseTokens).some(key => 
    key.includes('auth-token') && !key.includes('code-verifier')
  );
  
  console.log('\n✅ Has Auth Token:', hasAuthToken);
  
  return {
    supabaseTokens,
    otherAuthItems,
    sessionItems,
    hasAuthToken
  };
};

// Add to window for easy console debugging
if (typeof window !== 'undefined') {
  (window as any).debugAuthStorage = debugAuthStorage;
}
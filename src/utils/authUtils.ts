// Utility functions for managing authentication state and localStorage
// SECURITY: These functions should only be used for UI state management
// All privilege checks must be verified server-side via Supabase Auth and RLS policies

import { supabase } from '@/integrations/supabase/client';

// Validate email format
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
};

// Simple authentication check like the reference site
export const isLoggedIn = async (): Promise<boolean> => {
  const { data: { session } } = await supabase.auth.getSession();
  return !!session;
};

// Simple login function with validation
export const setUserSession = (email: string, teamname: string) => {
  const trimmedEmail = email.trim();
  const trimmedTeamname = teamname.trim();
  
  if (!isValidEmail(trimmedEmail)) {
    throw new Error('Invalid email format');
  }
  
  if (!trimmedTeamname) {
    throw new Error('Team name cannot be empty');
  }
  
  localStorage.setItem('email', trimmedEmail);
  localStorage.setItem('teamname', trimmedTeamname);
  console.log('User session set:', { email: trimmedEmail, teamname: trimmedTeamname });
};

// Simple logout function
export const clearUserSession = () => {
  // Clear all authentication-related localStorage items
  localStorage.removeItem('email');
  localStorage.removeItem('teamname');
  localStorage.removeItem('demo_mode');
  localStorage.removeItem('current_team_id');
  localStorage.removeItem('is_admin_account'); // SECURITY: Remove admin flag
  localStorage.removeItem('demo_user_is_paid');
  localStorage.removeItem('demo_user_id');
  localStorage.removeItem('demo_user_email');
  localStorage.removeItem('demo_team');
  localStorage.removeItem('demo_lineups');
  localStorage.removeItem('demo_player_ids');
  localStorage.removeItem('demo_data_initialized');
  localStorage.removeItem('original_user_email');
  // Do NOT clear Supabase auth tokens - let Supabase manage its own tokens
  // Only clear our app-specific session storage items
  sessionStorage.removeItem('auth_session_initialized');
  console.log('User session cleared (Supabase tokens preserved)');
};

// SECURITY: Only allow specific demo accounts - no localStorage bypass
export const isDemoMode = (): boolean => {
  const email = localStorage.getItem('email');
  // Only trust email-based detection for security
  return email === '<EMAIL>' ||
         email === '<EMAIL>';
};

// Set demo mode - only for specific demo account
export const setDemoMode = () => {
  localStorage.setItem('email', '<EMAIL>');
  localStorage.setItem('teamname', 'Demo Softball Team');
  localStorage.setItem('demo_mode', 'true');
  localStorage.setItem('current_team_id', '83bd9832-f5db-4c7d-b234-41fd38f90007');
  console.log('Demo mode set');
};

// Get current user info
export const getCurrentUser = () => {
  return {
    email: localStorage.getItem('email'),
    teamname: localStorage.getItem('teamname'),
    isDemo: isDemoMode(),
    isAdmin: isAdminAccount()
  };
};

// Legacy functions for compatibility
export const clearAllDemoFlags = clearUserSession;
export const clearAllAdminFlags = clearUserSession;

// SECURITY: Remove admin flag setting - admin status must come from database
export const setAdminAccountFlags = () => {
  // This function intentionally does nothing for security
  // Admin status is determined by database-side verification only
  console.warn('Admin flags should not be set client-side for security');
};

export const setDemoAccountFlags = setDemoMode;
export const clearAllAuthFlags = () => {
  clearUserSession();
  // IMPORTANT: Do NOT clear Supabase auth tokens (sb-* keys)
  // These are managed by Supabase and required for session persistence
};

// SECURITY: Only trust email-based demo detection
export const isDemoAccount = (email?: string | null): boolean => {
  // If an email is provided, use email-based detection only
  if (email !== undefined && email !== null) {
    const trimmedEmail = email.trim();
    return trimmedEmail === '<EMAIL>' ||
           trimmedEmail === '<EMAIL>' ||
           trimmedEmail?.includes('baseball_demo');
  }

  // If no email is provided, check stored email only
  const storedEmail = localStorage.getItem('email');
  return storedEmail === '<EMAIL>' ||
         storedEmail === '<EMAIL>';
};

// SECURITY: Restrict admin account detection to specific emails only
export const isAdminAccount = (emailParam?: string | null): boolean => {
  // Define admin emails - these should match server-side configuration
  const ADMIN_EMAILS = [
    '<EMAIL>',
    '<EMAIL>'
  ];
  
  const emailToCheck = emailParam || localStorage.getItem('email');
  
  if (!emailToCheck) return false;
  
  return ADMIN_EMAILS.includes(emailToCheck.trim());
};

export const getAccountType = (email?: string | null): 'admin' | 'demo' | 'regular' => {
  if (isDemoAccount(email)) return 'demo';
  if (isAdminAccount(email)) return 'admin';
  return 'regular';
};

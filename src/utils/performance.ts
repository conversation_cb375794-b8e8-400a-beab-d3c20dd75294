// Performance monitoring utility
export const measurePerformance = (name: string) => {
  const start = performance.now();
  
  return {
    end: () => {
      const duration = performance.now() - start;
      
      // Only log in development
      if (process.env.NODE_ENV === 'development') {
        const emoji = duration < 50 ? '⚡' : duration < 200 ? '🔥' : '🐌';
        console.log(`${emoji} ${name}: ${duration.toFixed(2)}ms`);
      }
      
      // Send to analytics if available
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'timing_complete', {
          event_category: 'Performance',
          name,
          value: Math.round(duration)
        });
      }
      
      return duration;
    }
  };
};

// Debounce utility
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  
  return function debounced(...args: Parameters<T>) {
    if (timeout) clearTimeout(timeout);
    
    timeout = setTimeout(() => {
      func(...args);
    }, wait);
  };
}

// Throttle utility
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle = false;
  
  return function throttled(...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => {
        inThrottle = false;
      }, limit);
    }
  };
}

// Memory-efficient array chunking
export function* chunkArray<T>(array: T[], chunkSize: number): Generator<T[]> {
  for (let i = 0; i < array.length; i += chunkSize) {
    yield array.slice(i, i + chunkSize);
  }
}

// Request idle callback wrapper
export const requestIdleCallback = 
  (typeof window !== 'undefined' && window.requestIdleCallback) ||
  ((cb: IdleRequestCallback) => setTimeout(cb, 1));

// Cancel idle callback wrapper  
export const cancelIdleCallback =
  (typeof window !== 'undefined' && window.cancelIdleCallback) ||
  ((id: number) => clearTimeout(id));

// Batch updates using requestIdleCallback
export class BatchProcessor<T> {
  private queue: T[] = [];
  private processing = false;
  private idleCallbackId: number | null = null;
  
  constructor(
    private processor: (items: T[]) => void | Promise<void>,
    private batchSize: number = 10
  ) {}
  
  add(item: T) {
    this.queue.push(item);
    this.scheduleProcessing();
  }
  
  addMany(items: T[]) {
    this.queue.push(...items);
    this.scheduleProcessing();
  }
  
  private scheduleProcessing() {
    if (this.processing || this.queue.length === 0) return;
    
    if (this.idleCallbackId) {
      cancelIdleCallback(this.idleCallbackId);
    }
    
    this.idleCallbackId = requestIdleCallback(() => {
      this.processQueue();
    });
  }
  
  private async processQueue() {
    if (this.processing || this.queue.length === 0) return;
    
    this.processing = true;
    
    try {
      while (this.queue.length > 0) {
        const batch = this.queue.splice(0, this.batchSize);
        await this.processor(batch);
        
        // Yield to browser between batches
        await new Promise(resolve => setTimeout(resolve, 0));
      }
    } finally {
      this.processing = false;
      this.idleCallbackId = null;
    }
  }
  
  flush() {
    return this.processQueue();
  }
}

// Performance observer for monitoring
export class PerformanceMonitor {
  private observer: PerformanceObserver | null = null;
  
  start() {
    if (typeof window === 'undefined' || !window.PerformanceObserver) return;
    
    this.observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.duration > 100) {
          console.warn(`Slow operation detected: ${entry.name} took ${entry.duration.toFixed(2)}ms`);
        }
      }
    });
    
    this.observer.observe({ entryTypes: ['measure', 'navigation'] });
  }
  
  stop() {
    this.observer?.disconnect();
    this.observer = null;
  }
  
  mark(name: string) {
    if (typeof window !== 'undefined' && window.performance) {
      performance.mark(name);
    }
  }
  
  measure(name: string, startMark: string, endMark: string) {
    if (typeof window !== 'undefined' && window.performance) {
      try {
        performance.measure(name, startMark, endMark);
      } catch (e) {
        // Marks may not exist
      }
    }
  }
}
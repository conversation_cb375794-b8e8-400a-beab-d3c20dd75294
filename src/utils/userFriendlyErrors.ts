// User-friendly error message translations

export interface LineupGenerationError {
  code: string;
  message: string;
  context?: {
    position?: string;
    [key: string]: any;
  };
}

export const USER_FRIENDLY_ERRORS: Record<string, string> = {
  // Position-related
  'POSITION_CONSTRAINT_VIOLATION': 'Not enough players for all positions',
  'POSITION_RESTRICTIONS_IMPOSSIBLE': 'Cannot fill {position} - need more players who can play there',
  'POSITION_RESTRICTIONS_IMPOSSIBLE_FALLBACK': 'Check position assignments - some positions have no eligible players',
  
  // Player-related
  'INSUFFICIENT_PLAYERS': 'Need at least 9 players to create a lineup',
  'INVALID_FIRST_INNING': 'The starting lineup has errors - check position assignments',
  
  // Rotation-related
  'ROTATION_PLAN_INVALID': 'Cannot rotate players with current settings',
  'CONSTRAINT_SOLVER_FAILED': 'Try assigning more players to different positions',
  
  // Generation-related
  'GENERATION_FAILED': 'Unable to create lineup - try adjusting position assignments',
  'QUALITY_FAILURE': 'Add more position assignments to improve lineup quality'
};

export function translateError(error: LineupGenerationError | Error | string): string {
  // Handle string errors
  if (typeof error === 'string') {
    return USER_FRIENDLY_ERRORS[error] || error;
  }
  
  // Handle Error objects
  if (error instanceof Error) {
    // Check if the error message contains a known error code
    for (const [code, friendlyMessage] of Object.entries(USER_FRIENDLY_ERRORS)) {
      if (error.message.includes(code)) {
        return friendlyMessage;
      }
    }
    return error.message;
  }
  
  // Handle LineupGenerationError objects
  const baseMessage = USER_FRIENDLY_ERRORS[error.code] || error.message;
  
  // Replace context placeholders
  if (error.context) {
    let message = baseMessage;
    for (const [key, value] of Object.entries(error.context)) {
      message = message.replace(`{${key}}`, value);
    }
    return message;
  }
  
  return baseMessage;
}

export function getGenerationMessage(coachingStyle: number): string {
  if (coachingStyle < 30) return "Generating fair play lineup with equal playing time...";
  if (coachingStyle < 70) return "Generating balanced lineup...";
  return "Generating competitive lineup to maximize win probability...";
}
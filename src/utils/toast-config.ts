import { toast as sonnerToast } from 'sonner';

/**
 * Enhanced toast notifications following Material Design and iOS HIG principles
 * Provides clear, immediate feedback for all user actions
 */

export const toast = {
  success: (message: string, options?: any) => {
    sonnerToast.success(message, {
      duration: 3000,
      position: 'top-center',
      style: {
        background: '#1a472a',
        color: 'white',
        fontWeight: '500',
        fontSize: '14px',
        borderRadius: '8px',
        padding: '12px 20px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      },
      className: 'animate-in slide-in-from-top-2 fade-in-0',
      ...options,
    });
  },

  error: (message: string, options?: any) => {
    sonnerToast.error(message, {
      duration: 5000,
      position: 'top-center',
      style: {
        background: '#dc2626',
        color: 'white',
        fontWeight: '500',
        fontSize: '14px',
        borderRadius: '8px',
        padding: '12px 20px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      },
      className: 'animate-in slide-in-from-top-2 fade-in-0',
      action: {
        label: 'Dismiss',
        onClick: () => {},
      },
      ...options,
    });
  },

  loading: (message: string, options?: any) => {
    return sonnerToast.loading(message, {
      position: 'top-center',
      style: {
        background: 'white',
        color: '#1f2937',
        fontWeight: '500',
        fontSize: '14px',
        borderRadius: '8px',
        padding: '12px 20px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        border: '1px solid #e5e7eb',
      },
      className: 'animate-in slide-in-from-top-2 fade-in-0',
      ...options,
    });
  },

  info: (message: string, options?: any) => {
    sonnerToast.info(message, {
      duration: 4000,
      position: 'top-center',
      style: {
        background: '#3b82f6',
        color: 'white',
        fontWeight: '500',
        fontSize: '14px',
        borderRadius: '8px',
        padding: '12px 20px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      },
      className: 'animate-in slide-in-from-top-2 fade-in-0',
      ...options,
    });
  },

  promise: async <T,>(
    promise: Promise<T>,
    msgs: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    }
  ) => {
    return sonnerToast.promise(promise, {
      loading: msgs.loading,
      success: msgs.success,
      error: msgs.error,
      position: 'top-center',
      style: {
        fontWeight: '500',
        fontSize: '14px',
        borderRadius: '8px',
        padding: '12px 20px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      },
    });
  },
};

export default toast;
export const verifySupabaseTokens = () => {
  console.log('🔐 Checking Supabase tokens in localStorage...');
  console.log('================================================');
  
  let tokenCount = 0;
  const tokens: Record<string, string> = {};
  
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key?.startsWith('sb-')) {
      tokenCount++;
      const value = localStorage.getItem(key);
      // Only show first 50 chars for security
      tokens[key] = value ? value.substring(0, 50) + '...' : 'null';
      console.log(`✅ Found: ${key}`);
      console.log(`   Value: ${tokens[key]}`);
    }
  }
  
  if (tokenCount === 0) {
    console.log('❌ No Supabase tokens found! Session persistence will fail.');
  } else {
    console.log(`\n✅ Found ${tokenCount} Supabase tokens. Session should persist.`);
  }
  
  // Also check for specific expected tokens
  const expectedTokenKeys = [
    'sb-auth-token',
    'sb-refresh-token',
    'sb-access-token'
  ];
  
  console.log('\n🔍 Checking for specific token patterns:');
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key?.includes('auth-token') && key.startsWith('sb-')) {
      console.log(`✅ Auth token found: ${key}`);
    }
  }
  
  return {
    tokenCount,
    tokens,
    hasAuthToken: Object.keys(tokens).some(k => k.includes('auth-token'))
  };
};

// Auto-run on page load in development
if (import.meta.env.DEV && typeof window !== 'undefined') {
  window.addEventListener('load', () => {
    setTimeout(() => {
      console.log('\n🚀 Auto-checking Supabase tokens on page load...');
      verifySupabaseTokens();
    }, 1000);
  });
  
  // Add to window for easy access
  (window as any).verifySupabaseTokens = verifySupabaseTokens;
}
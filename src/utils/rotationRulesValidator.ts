import { RotationRules } from '@/contexts/TeamContext';

export interface ValidationResult {
  isValid: boolean;
  warnings: string[];
  autoAdjustments: Partial<RotationRules>;
}

export interface RuleConflict {
  rule: keyof RotationRules;
  conflictsWith: string;
  severity: 'error' | 'warning';
  message: string;
  autoAdjust?: Partial<RotationRules>;
}

/**
 * Validates rotation rules and identifies conflicts with competitive mode
 */
export function validateRotationRules(rules: RotationRules): ValidationResult {
  const warnings: string[] = [];
  const autoAdjustments: Partial<RotationRules> = {};
  const conflicts: RuleConflict[] = [];

  if (rules.competitiveMode) {
    // Check for conflicts with competitive mode
    
    // 1. Equal Playing Time conflicts with competitive mode
    if (rules.equalPlayingTime) {
      conflicts.push({
        rule: 'equalPlayingTime',
        conflictsWith: 'competitiveMode',
        severity: 'error',
        message: 'Equal Playing Time conflicts with Competitive Mode. In competitive mode, star players get priority and playing time may be unequal.',
        autoAdjust: { equalPlayingTime: false }
      });
    }

    // 2. Rotate All Players is fine in competitive mode - it just means rotation still happens

    // 3. Prioritize Outfield Rotation conflicts with optimal positioning
    if (rules.prioritizeOutfieldRotation) {
      conflicts.push({
        rule: 'prioritizeOutfieldRotation',
        conflictsWith: 'competitiveMode',
        severity: 'warning',
        message: 'Prioritize Outfield Rotation may conflict with competitive positioning. Players will be placed based on ratings rather than rotation priority.',
        autoAdjust: { prioritizeOutfieldRotation: false }
      });
    }

    // 4. Rotation frequency conflicts
    if (rules.rotateLineupEvery === 1) {
      conflicts.push({
        rule: 'rotateLineupEvery',
        conflictsWith: 'competitiveMode',
        severity: 'warning',
        message: 'Rotating every inning reduces the benefit of competitive mode. Consider increasing rotation frequency to 2-3 innings.',
      });
    }

    // 5. Minimum playing time validation
    if (rules.competitiveMinPlayingTime && rules.competitiveMinPlayingTime < 30) {
      conflicts.push({
        rule: 'competitiveMinPlayingTime',
        conflictsWith: 'fairness',
        severity: 'warning',
        message: 'Very low minimum playing time (<30%) may result in some players barely playing. Consider increasing for better team morale.',
      });
    }
  } else {
    // Non-competitive mode validations
    
    // Manual rotation with equal playing time
    if (rules.rotationMethod === 'manual' && rules.equalPlayingTime) {
      conflicts.push({
        rule: 'rotationMethod',
        conflictsWith: 'equalPlayingTime',
        severity: 'warning',
        message: 'Manual rotation makes it difficult to ensure equal playing time. Consider using standard rotation.',
      });
    }
  }

  // General validations (apply to both modes)
  
  // Position lockout warnings
  if (!rules.respectPositionLockouts && rules.competitiveMode) {
    conflicts.push({
      rule: 'respectPositionLockouts',
      conflictsWith: 'safety',
      severity: 'warning',
      message: 'Not respecting position lockouts may place players in positions they cannot safely play. This is not recommended even in competitive mode.',
    });
  }

  // Pitcher/Catcher rotation with star players
  if (rules.competitiveMode && !rules.allowPitcherRotation) {
    conflicts.push({
      rule: 'allowPitcherRotation',
      conflictsWith: 'pitcherHealth',
      severity: 'warning',
      message: 'Not rotating pitchers in competitive mode may overwork star pitchers. Consider enabling pitcher rotation for player safety.',
    });
  }

  // Process conflicts and generate adjustments
  conflicts.forEach(conflict => {
    if (conflict.severity === 'error') {
      warnings.push(`❌ ${conflict.message}`);
      if (conflict.autoAdjust) {
        Object.assign(autoAdjustments, conflict.autoAdjust);
      }
    } else {
      warnings.push(`⚠️ ${conflict.message}`);
    }
  });

  return {
    isValid: conflicts.filter(c => c.severity === 'error').length === 0,
    warnings,
    autoAdjustments
  };
}

/**
 * Get recommended settings based on mode
 */
export function getRecommendedSettings(competitiveMode: boolean): Partial<RotationRules> {
  if (competitiveMode) {
    return {
      competitiveMode: true,
      equalPlayingTime: false,
      rotatePlayers: true,
      respectPositionLockouts: true,
      allowPitcherRotation: true,
      allowCatcherRotation: true,
      prioritizeOutfieldRotation: false,
      limitBenchTime: true,
      rotateLineupEvery: 3,
      rotatePitcherEvery: 3,
      competitiveMinPlayingTime: 40
    };
  } else {
    return {
      competitiveMode: false,
      equalPlayingTime: true,
      rotatePlayers: true,
      respectPositionLockouts: true,
      allowPitcherRotation: false,
      allowCatcherRotation: true,
      prioritizeOutfieldRotation: true,
      limitBenchTime: true,
      rotateLineupEvery: 1,
      rotatePitcherEvery: 2,
      competitiveMinPlayingTime: undefined
    };
  }
}

/**
 * Get explanation for each rule in context of current mode
 */
export function getRuleExplanation(rule: keyof RotationRules, competitiveMode: boolean): string {
  const explanations: Record<keyof RotationRules, { competitive: string; recreational: string }> = {
    rotationMethod: {
      competitive: 'In competitive mode, standard rotation is recommended to optimize player positioning based on ratings.',
      recreational: 'Choose between automatic rotation for fairness or manual control for specific strategies.'
    },
    equalPlayingTime: {
      competitive: 'Disabled in competitive mode. Star players and high-rated players will play more in key positions.',
      recreational: 'Ensures all players get similar playing time throughout the game.'
    },
    rotatePlayers: {
      competitive: 'Players still rotate but the best players get priority for key positions (pitcher, catcher, shortstop).',
      recreational: 'All players rotate through positions to give everyone varied experience.'
    },
    respectPositionLockouts: {
      competitive: 'Always respected for safety. High ratings cannot override physical limitations or safety concerns.',
      recreational: 'Prevents players from being assigned to positions they cannot safely play.'
    },
    allowPitcherRotation: {
      competitive: 'Recommended even in competitive mode to prevent overuse injuries. Star pitchers will still get priority.',
      recreational: 'Keeps the same pitcher unless manually changed, good for developing pitchers.'
    },
    allowCatcherRotation: {
      competitive: 'Allows catcher rotation while still prioritizing high-rated catchers when they do play.',
      recreational: 'Rotates catchers to share the physically demanding position.'
    },
    prioritizeOutfieldRotation: {
      competitive: 'Disabled - players are positioned based on ratings, not rotation patterns.',
      recreational: 'Ensures players rotate through outfield positions more frequently for variety.'
    },
    limitBenchTime: {
      competitive: 'Still important - ensures minimum playing time is met even for non-star players.',
      recreational: 'Prevents any player from sitting too long consecutively.'
    },
    rotateLineupEvery: {
      competitive: 'Set to 2-3 innings to allow star players to establish rhythm in key positions.',
      recreational: 'Usually set to 1 for maximum rotation and playing time equality.'
    },
    rotatePitcherEvery: {
      competitive: 'Set to 3+ innings to let star pitchers establish dominance while preventing overuse.',
      recreational: 'Set to 2-3 innings to share pitching opportunities.'
    },
    competitiveMode: {
      competitive: 'Prioritizes winning through optimal positioning based on player ratings and abilities.',
      recreational: 'Focuses on fair play, development, and equal opportunities for all players.'
    },
    competitiveMinPlayingTime: {
      competitive: 'Ensures even bench players get meaningful playing time (recommended 40-50%).',
      recreational: 'Not applicable - equal playing time ensures everyone plays similar amounts.'
    }
  };

  const explanation = explanations[rule];
  return competitiveMode ? explanation.competitive : explanation.recreational;
}
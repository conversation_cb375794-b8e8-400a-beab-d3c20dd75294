import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import * as serviceWorkerRegistration from './utils/serviceWorkerRegistration'
import { toast } from 'sonner'

// No need to clear localStorage as we're using the database for all data

createRoot(document.getElementById("root")!).render(<App />);

// Register service worker for PWA support
serviceWorkerRegistration.register({
  onSuccess: () => {
    console.log('App is ready for offline use!');
  },
  onUpdate: (registration) => {
    toast.info('New version available!', {
      action: {
        label: 'Update',
        onClick: () => {
          if (registration && registration.waiting) {
            registration.waiting.postMessage({ type: 'SKIP_WAITING' });
            window.location.reload();
          }
        }
      },
      duration: Infinity
    });
  }
});

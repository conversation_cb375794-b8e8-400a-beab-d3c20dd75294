// Performance configuration and constants

export const PERFORMANCE_CONFIG = {
  // Caching
  cache: {
    teamsDataTTL: 5 * 60 * 1000, // 5 minutes
    lineupDetailsTTL: 10 * 60 * 1000, // 10 minutes
    playerDataTTL: 5 * 60 * 1000, // 5 minutes
    useLocalStorage: true,
    useSessionStorage: true,
  },
  
  // Debouncing
  debounce: {
    searchDelay: 300, // 300ms
    autoSaveDelay: 1500, // 1.5 seconds
    filterDelay: 200, // 200ms
    resizeDelay: 150, // 150ms
  },
  
  // Throttling
  throttle: {
    scrollDelay: 100, // 100ms
    dragDelay: 50, // 50ms
  },
  
  // Virtual scrolling
  virtualScrolling: {
    enableThreshold: 50, // Enable when list has 50+ items
    itemBuffer: 3, // Render 3 extra items above/below viewport
    scrollDebounce: 10, // 10ms
  },
  
  // Lazy loading
  lazyLoading: {
    imageLoadDelay: 0, // Load immediately when in viewport
    componentLoadDelay: 100, // 100ms delay for heavy components
    intersectionThreshold: 0.1, // 10% visibility triggers load
  },
  
  // Optimistic updates
  optimisticUI: {
    enabled: true,
    showLoadingIndicators: true,
    autoRevertOnError: true,
    revertDelay: 0, // Immediate revert on error
  },
  
  // Performance monitoring
  monitoring: {
    enabled: process.env.NODE_ENV === 'development',
    slowOperationThreshold: 100, // Log operations slower than 100ms
    reportToAnalytics: true,
  },
  
  // Resource limits
  limits: {
    maxPlayersPerTeam: 100,
    maxLineupsPerTeam: 500,
    maxInningsPerLineup: 20,
    maxConcurrentRequests: 3,
  },
  
  // Batch processing
  batching: {
    enabled: true,
    batchSize: 10,
    batchDelay: 50, // 50ms between batches
  }
};

// Performance hints for different operations
export const PERFORMANCE_HINTS = {
  lineupGeneration: {
    useOptimized: true,
    precomputeEligibility: true,
    cachePositionScores: true,
    parallelizeCalculations: false, // Single-threaded is faster for small teams
  },
  
  dataFetching: {
    useParallelQueries: true,
    prefetchOnHover: true,
    cacheAggressively: true,
    compressLocalStorage: false, // Not worth the CPU cost
  },
  
  rendering: {
    useReactMemo: true,
    useReactCallback: true,
    avoidInlineStyles: true,
    minimizeReRenders: true,
  }
};

// Preload critical resources
export const preloadResources = () => {
  if (typeof window === 'undefined') return;
  
  // Preload fonts
  const link = document.createElement('link');
  link.rel = 'preload';
  link.as = 'font';
  link.type = 'font/woff2';
  link.crossOrigin = 'anonymous';
  link.href = '/fonts/your-font.woff2';
  document.head.appendChild(link);
  
  // Prefetch common routes
  const routes = ['/dashboard', '/team-roster', '/create-lineup'];
  routes.forEach(route => {
    const prefetchLink = document.createElement('link');
    prefetchLink.rel = 'prefetch';
    prefetchLink.href = route;
    document.head.appendChild(prefetchLink);
  });
};

// Initialize performance optimizations
export const initializePerformance = () => {
  // Enable React concurrent features
  if (typeof window !== 'undefined') {
    // @ts-ignore
    window.__REACT_DEVTOOLS_GLOBAL_HOOK__ = {
      supportsFiber: true,
      inject: () => {},
      onCommitFiberRoot: () => {},
      onCommitFiberUnmount: () => {},
    };
  }
  
  // Set up intersection observer for lazy loading
  if ('IntersectionObserver' in window) {
    // Images lazy loading is handled by native loading="lazy"
  }
  
  // Set up performance observer
  if ('PerformanceObserver' in window) {
    try {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > PERFORMANCE_CONFIG.monitoring.slowOperationThreshold) {
            console.warn(`Slow operation: ${entry.name} took ${entry.duration}ms`);
          }
        }
      });
      observer.observe({ entryTypes: ['measure', 'navigation'] });
    } catch (e) {
      // Some browsers don't support all entry types
    }
  }
  
  // Preload resources
  preloadResources();
};
import { describe, it, expect } from 'vitest';
import { generateOptimalLineup } from '../lib/utils-enhanced';
import type { Player, LineupRules } from '../lib/utils-enhanced';

describe('Rotation Balance Fix Tests', () => {
  it('should ensure equal playing time with rotateLineupEvery = 1', () => {
    // Create 12 test players
    const testPlayers: Player[] = [
      { id: '1', name: '<PERSON>', teamRoles: {} },
      { id: '2', name: '<PERSON><PERSON>', teamRoles: {} },
      { id: '3', name: '<PERSON>', teamRoles: {} },
      { id: '4', name: '<PERSON>', teamRoles: {} },
      { id: '5', name: '<PERSON>', teamRoles: {} },
      { id: '6', name: '<PERSON>', teamRoles: {} },
      { id: '7', name: '<PERSON>', teamRoles: {} },
      { id: '8', name: '<PERSON><PERSON>', teamRoles: {} },
      { id: '9', name: '<PERSON>', teamRoles: {} },
      { id: '10', name: '<PERSON>', teamRoles: {} },
      { id: '11', name: '<PERSON><PERSON>', teamRoles: {} },
      { id: '12', name: 'Jordan', teamRoles: {} }
    ];

    const rules: LineupRules = {
      limitBenchTime: true,
      maxConsecutiveBenchInnings: 2,
      allowPitcherRotation: true,
      allowCatcherRotation: true,
      respectPositionLockouts: false,
      equalPlayingTime: true,
      rotateLineupEvery: 1, // CRITICAL: Rotate every inning
      rotatePitcherEvery: 2
    };

    const totalInnings = 20;
    const lineup = generateOptimalLineup(testPlayers, totalInnings, rules);

    // Calculate stats
    const playerStats = new Map<string, { field: number; bench: number }>();
    testPlayers.forEach(p => playerStats.set(p.name, { field: 0, bench: 0 }));

    lineup.forEach(inning => {
      // Count field appearances
      ['pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'].forEach(pos => {
        const player = (inning.positions as any)[pos];
        if (player) {
          playerStats.get(player)!.field++;
        }
      });

      // Count bench appearances
      inning.positions.bench.forEach(player => {
        if (player) {
          playerStats.get(player)!.bench++;
        }
      });
    });

    // Check balance
    const fieldTimes = Array.from(playerStats.values()).map(s => s.field);
    const min = Math.min(...fieldTimes);
    const max = Math.max(...fieldTimes);
    const range = max - min;
    const idealFieldInnings = (totalInnings * 9) / testPlayers.length; // 15 innings

    console.log('\n📊 Test Results:');
    console.log(`Players: ${testPlayers.length}`);
    console.log(`Total innings: ${totalInnings}`);
    console.log(`Ideal field innings per player: ${idealFieldInnings.toFixed(1)}`);
    console.log(`Actual range: ${min} - ${max} (difference: ${range})`);

    // Log individual stats
    Array.from(playerStats.entries())
      .sort((a, b) => b[1].field - a[1].field)
      .forEach(([name, stats]) => {
        console.log(`  ${name}: Field=${stats.field}, Bench=${stats.bench}`);
      });

    // CRITICAL ASSERTIONS
    // With 12 players and 20 innings, ideal is 15 field innings per player
    // We should allow a small variance but not the extreme imbalance we saw before
    expect(range).toBeLessThanOrEqual(3); // Max 3 innings difference
    expect(min).toBeGreaterThanOrEqual(13); // Everyone plays at least 13 innings
    expect(max).toBeLessThanOrEqual(17); // No one plays more than 17 innings

    // Check bench streaks
    let maxBenchStreak = 0;
    testPlayers.forEach(player => {
      let currentStreak = 0;
      let playerMaxStreak = 0;

      lineup.forEach(inning => {
        if (inning.positions.bench.includes(player.name)) {
          currentStreak++;
          playerMaxStreak = Math.max(playerMaxStreak, currentStreak);
        } else {
          currentStreak = 0;
        }
      });

      maxBenchStreak = Math.max(maxBenchStreak, playerMaxStreak);
    });

    expect(maxBenchStreak).toBeLessThanOrEqual(rules.maxConsecutiveBenchInnings);
  });

  it('should rotate players every inning when rotateLineupEvery = 1', () => {
    const testPlayers: Player[] = [
      { id: '1', name: 'Player1', teamRoles: {} },
      { id: '2', name: 'Player2', teamRoles: {} },
      { id: '3', name: 'Player3', teamRoles: {} },
      { id: '4', name: 'Player4', teamRoles: {} },
      { id: '5', name: 'Player5', teamRoles: {} },
      { id: '6', name: 'Player6', teamRoles: {} },
      { id: '7', name: 'Player7', teamRoles: {} },
      { id: '8', name: 'Player8', teamRoles: {} },
      { id: '9', name: 'Player9', teamRoles: {} },
      { id: '10', name: 'Player10', teamRoles: {} }
    ];

    const rules: LineupRules = {
      limitBenchTime: true,
      maxConsecutiveBenchInnings: 2,
      allowPitcherRotation: true,
      allowCatcherRotation: true,
      respectPositionLockouts: false,
      equalPlayingTime: true,
      rotateLineupEvery: 1,
      rotatePitcherEvery: 3
    };

    const lineup = generateOptimalLineup(testPlayers, 6, rules);

    // Check that rotation happens between innings
    let rotationCount = 0;
    for (let i = 1; i < lineup.length; i++) {
      const prev = lineup[i - 1];
      const curr = lineup[i];

      // Count position changes
      let changes = 0;
      ['pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'].forEach(pos => {
        if ((prev.positions as any)[pos] !== (curr.positions as any)[pos]) {
          changes++;
        }
      });

      if (changes > 0) {
        rotationCount++;
      }

      console.log(`Inning ${i} -> ${i + 1}: ${changes} position changes`);
    }

    // With rotateLineupEvery = 1, we should see rotation in every transition
    expect(rotationCount).toBeGreaterThanOrEqual(4); // At least 4 out of 5 transitions should have rotation
  });
});
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import * as authUtils from '@/utils/authUtils';

// Create a proper localStorage mock that persists data
const createStorageMock = () => {
  let store: Record<string, string> = {};
  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => { store[key] = value; }),
    removeItem: vi.fn((key: string) => { delete store[key]; }),
    clear: vi.fn(() => { store = {}; }),
    length: 0,
    key: vi.fn(() => null),
  };
};

// Replace the existing localStorage mock from vitest-setup
const localStorageMock = createStorageMock();
const sessionStorageMock = createStorageMock();

Object.defineProperty(global, 'localStorage', { value: localStorageMock, writable: true });
Object.defineProperty(global, 'sessionStorage', { value: sessionStorageMock, writable: true });

describe('Authentication Logic Tests', () => {
  beforeEach(() => {
    localStorage.clear();
    sessionStorage.clear();
  });

  describe('Account Type Detection', () => {
    it('should correctly identify demo accounts', () => {
      // Test email-based detection
      expect(authUtils.isDemoAccount('<EMAIL>')).toBe(true);
      expect(authUtils.isDemoAccount('<EMAIL>')).toBe(true);
      expect(authUtils.isDemoAccount('<EMAIL>')).toBe(true);
      expect(authUtils.isDemoAccount('<EMAIL>')).toBe(false);
      
      // Test localStorage fallback
      localStorage.setItem('demo_mode', 'true');
      expect(authUtils.isDemoAccount()).toBe(true);
      
      localStorage.removeItem('demo_mode');
      expect(authUtils.isDemoAccount()).toBe(false);
    });

    it('should correctly identify admin accounts', () => {
      // Test Noah's special account
      expect(authUtils.isAdminAccount('<EMAIL>')).toBe(true);
      
      // Test admin email patterns
      expect(authUtils.isAdminAccount('<EMAIL>')).toBe(true);
      expect(authUtils.isAdminAccount('<EMAIL>')).toBe(true);
      expect(authUtils.isAdminAccount('<EMAIL>')).toBe(false);
      
      // Test localStorage flag
      localStorage.setItem('is_admin_account', 'true');
      expect(authUtils.isAdminAccount('<EMAIL>')).toBe(true);
      
      localStorage.removeItem('is_admin_account');
      expect(authUtils.isAdminAccount('<EMAIL>')).toBe(false);
    });

    it('should determine account type correctly', () => {
      expect(authUtils.getAccountType('<EMAIL>')).toBe('demo');
      expect(authUtils.getAccountType('<EMAIL>')).toBe('admin');
      expect(authUtils.getAccountType('<EMAIL>')).toBe('admin');
      expect(authUtils.getAccountType('<EMAIL>')).toBe('regular');
      
      // Demo should take precedence over admin if both conditions match
      localStorage.setItem('demo_mode', 'true');
      localStorage.setItem('is_admin_account', 'true');
      expect(authUtils.getAccountType()).toBe('demo');
    });

    it('should handle null/undefined email inputs safely', () => {
      expect(authUtils.isDemoAccount(null)).toBe(false);
      expect(authUtils.isDemoAccount(undefined)).toBe(false);
      expect(authUtils.isDemoAccount('')).toBe(false);
      
      expect(authUtils.isAdminAccount(null)).toBe(false);
      expect(authUtils.isAdminAccount(undefined)).toBe(false);
      expect(authUtils.isAdminAccount('')).toBe(false);
      
      expect(authUtils.getAccountType(null)).toBe('regular');
      expect(authUtils.getAccountType(undefined)).toBe('regular');
      expect(authUtils.getAccountType('')).toBe('regular');
    });
  });

  describe('Session Management', () => {
    it('should detect logged in state correctly', () => {
      expect(authUtils.isLoggedIn()).toBe(false);
      
      localStorage.setItem('email', '<EMAIL>');
      expect(authUtils.isLoggedIn()).toBe(false); // Still need teamname
      
      localStorage.setItem('teamname', 'Test Team');
      expect(authUtils.isLoggedIn()).toBe(true);
      
      localStorage.removeItem('email');
      expect(authUtils.isLoggedIn()).toBe(false);
    });

    it('should set user session correctly', () => {
      authUtils.setUserSession('<EMAIL>', 'Eagles');
      
      expect(localStorage.getItem('email')).toBe('<EMAIL>');
      expect(localStorage.getItem('teamname')).toBe('Eagles');
      expect(authUtils.isLoggedIn()).toBe(true);
    });

    it('should clear user session completely', () => {
      // Set up initial state
      localStorage.setItem('email', '<EMAIL>');
      localStorage.setItem('teamname', 'Test Team');
      localStorage.setItem('demo_mode', 'true');
      localStorage.setItem('current_team_id', 'team123');
      sessionStorage.setItem('temp_data', 'test');
      
      authUtils.clearUserSession();
      
      expect(localStorage.getItem('email')).toBeNull();
      expect(localStorage.getItem('teamname')).toBeNull();
      expect(localStorage.getItem('demo_mode')).toBeNull();
      expect(localStorage.getItem('current_team_id')).toBeNull();
      expect(sessionStorage.getItem('temp_data')).toBeNull();
    });

    it('should get current user info correctly', () => {
      localStorage.setItem('email', '<EMAIL>');
      localStorage.setItem('teamname', 'Eagles');
      
      const user = authUtils.getCurrentUser();
      expect(user.email).toBe('<EMAIL>');
      expect(user.teamname).toBe('Eagles');
      expect(user.isDemo).toBe(false);
      expect(user.isAdmin).toBe(false);
      
      // Test with demo user
      localStorage.setItem('demo_mode', 'true');
      const demoUser = authUtils.getCurrentUser();
      expect(demoUser.isDemo).toBe(true);
    });
  });

  describe('Demo Mode Management', () => {
    it('should set demo mode correctly', () => {
      authUtils.setDemoMode();
      
      expect(localStorage.getItem('email')).toBe('<EMAIL>');
      expect(localStorage.getItem('teamname')).toBe('Demo Softball Team');
      expect(localStorage.getItem('demo_mode')).toBe('true');
      expect(localStorage.getItem('current_team_id')).toBe('83bd9832-f5db-4c7d-b234-41fd38f90007');
      expect(authUtils.isDemoMode()).toBe(true);
    });

    it('should detect demo mode from multiple sources', () => {
      // Email-based detection
      localStorage.setItem('email', '<EMAIL>');
      expect(authUtils.isDemoMode()).toBe(true);
      
      localStorage.removeItem('email');
      expect(authUtils.isDemoMode()).toBe(false);
      
      // Flag-based detection
      localStorage.setItem('demo_mode', 'true');
      expect(authUtils.isDemoMode()).toBe(true);
    });
  });

  describe('Legacy Function Compatibility', () => {
    it('should maintain backward compatibility for flag functions', () => {
      // Test legacy admin flag function
      authUtils.setAdminAccountFlags();
      expect(localStorage.getItem('email')).toBe('<EMAIL>');
      expect(localStorage.getItem('is_admin_account')).toBe('true');
      
      // Test legacy demo flag function  
      authUtils.setDemoAccountFlags();
      expect(localStorage.getItem('demo_mode')).toBe('true');
      expect(localStorage.getItem('email')).toBe('<EMAIL>');
      
      // Test legacy clear functions
      authUtils.clearAllAuthFlags();
      expect(localStorage.getItem('email')).toBeNull();
      expect(localStorage.getItem('teamname')).toBeNull();
    });
  });

  describe('Security Edge Cases', () => {
    it('should prevent privilege escalation via localStorage manipulation', () => {
      // User tries to set admin flag without proper authentication
      localStorage.setItem('is_admin_account', 'true');
      localStorage.setItem('email', '<EMAIL>');
      
      // Admin status should only be based on email pattern or Noah's account
      expect(authUtils.isAdminAccount('<EMAIL>')).toBe(true); // This actually passes due to localStorage flag
      
      // But account type detection should be consistent
      expect(authUtils.getAccountType('<EMAIL>')).toBe('admin');
    });

    it('should handle conflicting flags correctly', () => {
      // Set both demo and admin flags
      localStorage.setItem('demo_mode', 'true');
      localStorage.setItem('is_admin_account', 'true');
      
      // Demo should take precedence
      expect(authUtils.getAccountType()).toBe('demo');
      
      // Clear demo mode, admin should be detected
      localStorage.removeItem('demo_mode');
      expect(authUtils.getAccountType()).toBe('admin');
    });

    it('should handle malformed data gracefully', () => {
      // Set invalid localStorage values
      localStorage.setItem('demo_mode', 'invalid');
      localStorage.setItem('is_admin_account', 'maybe');
      
      expect(authUtils.isDemoMode()).toBe(false);
      expect(authUtils.isAdminAccount()).toBe(false);
    });
  });

  describe('Authentication Flow Edge Cases', () => {
    it('should handle empty string inputs', () => {
      authUtils.setUserSession('', '');
      expect(authUtils.isLoggedIn()).toBe(false);
      
      authUtils.setUserSession('<EMAIL>', '');
      expect(authUtils.isLoggedIn()).toBe(false);
      
      authUtils.setUserSession('', 'Team Name');
      expect(authUtils.isLoggedIn()).toBe(false);
    });

    it('should handle whitespace-only inputs', () => {
      authUtils.setUserSession('   ', '   ');
      expect(authUtils.isLoggedIn()).toBe(true); // This might be a bug - should probably trim and validate
      
      const user = authUtils.getCurrentUser();
      expect(user.email).toBe('   ');
      expect(user.teamname).toBe('   ');
    });

    it('should handle special characters in email/team names', () => {
      const specialEmail = '<EMAIL>';
      const specialTeam = 'Team "Quotes" & <HTML>';
      
      authUtils.setUserSession(specialEmail, specialTeam);
      expect(authUtils.isLoggedIn()).toBe(true);
      
      const user = authUtils.getCurrentUser();
      expect(user.email).toBe(specialEmail);
      expect(user.teamname).toBe(specialTeam);
    });
  });

  describe('Potential Security Issues Found', () => {
    it('SECURITY ISSUE: localStorage admin flag allows privilege escalation', () => {
      // A malicious user could set this flag to gain admin access
      localStorage.setItem('is_admin_account', 'true');
      expect(authUtils.isAdminAccount('<EMAIL>')).toBe(true);
      
      // This is a security vulnerability - admin status should only be determined 
      // by server-side validation, not client-side localStorage flags
    });

    it('SECURITY ISSUE: Demo mode can be enabled by any user', () => {
      // A regular user could enable demo mode to bypass payment
      localStorage.setItem('demo_mode', 'true');
      expect(authUtils.isDemoMode()).toBe(true);
      expect(authUtils.getAccountType()).toBe('demo');
      
      // This could allow bypassing payment verification
    });

    it('BUG: isLoggedIn accepts whitespace-only values', () => {
      localStorage.setItem('email', '   ');
      localStorage.setItem('teamname', '   ');
      expect(authUtils.isLoggedIn()).toBe(true);
      
      // This should probably return false for whitespace-only values
    });

    it('BUG: No email validation in setUserSession', () => {
      authUtils.setUserSession('not-an-email', 'Team');
      expect(authUtils.isLoggedIn()).toBe(true);
      
      // Should validate email format before setting session
    });
  });
});
import { describe, it, expect } from 'vitest';
import { 
  canPlayPosition, 
  generateInitialLineup,
  validateLineup,
  validateGameLineup,
  buildPositionMatrix,
  canPlayPositionStrict
} from '../lib/utils-enhanced';
import { buildPositionMatrix as buildStrictMatrix } from '../lib/strict-position-eligibility';
import { Player, LineupRules } from '@/contexts/TeamContext';

describe('Strict Position Eligibility Tests', () => {
  describe('canPlayPosition function - MODE SPECIFIC BEHAVIOR', () => {
    it('COMPETITIVE MODE: should NOT allow player to play position not in position_preferences', () => {
      const player: Player = {
        id: '1',
        name: '<PERSON>',
        positionPreferences: {
          leftField: 'preferred',
          rightField: 'neutral',
          secondBase: 'preferred',
          centerField: 'preferred'
          // NOTE: No catcher entry means she CANNOT catch in competitive mode
        },
        pitcher_restriction: false,
        catcher_restriction: false,
        first_base_restriction: false
      };
      
      // COMPETITIVE MODE: Should NOT be able to play catcher (not in preferences)
      expect(canPlayPosition(player, 'Catcher', true, true)).toBe(false);
      expect(canPlayPosition(player, 'catcher', true, true)).toBe(false);
      
      // Should be able to play positions in preferences
      expect(canPlayPosition(player, 'Left Field', true, true)).toBe(true);
      expect(canPlayPosition(player, 'Second Base', true, true)).toBe(true);
    });
    
    it('FAIR PLAY MODE: should allow player to play position not in position_preferences', () => {
      const player: Player = {
        id: '1',
        name: 'Avery',
        positionPreferences: {
          leftField: 'preferred',
          rightField: 'neutral',
          secondBase: 'preferred',
          centerField: 'preferred'
          // NOTE: No catcher entry - she CAN catch in fair play mode
        },
        pitcher_restriction: false,
        catcher_restriction: false,
        first_base_restriction: false
      };
      
      // FAIR PLAY MODE: Should be able to play catcher (not explicitly avoided)
      expect(canPlayPosition(player, 'Catcher', true, false)).toBe(true);
      expect(canPlayPosition(player, 'catcher', true, false)).toBe(true);
      
      // Should also be able to play positions in preferences
      expect(canPlayPosition(player, 'Left Field', true, false)).toBe(true);
      expect(canPlayPosition(player, 'Second Base', true, false)).toBe(true);
    });
    
    it('should respect avoid preferences in BOTH modes', () => {
      const player: Player = {
        id: '2',
        name: 'Blake',
        positionPreferences: {
          pitcher: 'avoid',
          catcher: 'preferred',
          firstBase: 'neutral'
        }
      };
      
      // Both competitive and fair play modes should respect "avoid"
      expect(canPlayPosition(player, 'Pitcher', true, true)).toBe(false);  // competitive
      expect(canPlayPosition(player, 'Pitcher', true, false)).toBe(false); // fair play
      expect(canPlayPosition(player, 'Catcher', true, true)).toBe(true);
      expect(canPlayPosition(player, 'Catcher', true, false)).toBe(true);
      expect(canPlayPosition(player, 'First Base', true, true)).toBe(true);
      expect(canPlayPosition(player, 'First Base', true, false)).toBe(true);
    });
    
    it('should respect medical restrictions', () => {
      const player: Player = {
        id: '3',
        name: 'Charlie',
        positionPreferences: {
          pitcher: 'preferred',
          catcher: 'preferred'
        },
        pitcher_restriction: true, // Medical restriction
        catcher_restriction: false
      };
      
      // Should NOT be able to pitch due to medical restriction
      expect(canPlayPosition(player, 'Pitcher', true)).toBe(false);
      expect(canPlayPosition(player, 'Catcher', true)).toBe(true);
    });
    
    it('MODE SPECIFIC: players with no position_preferences', () => {
      const player: Player = {
        id: '4',
        name: 'Dylan',
        // No positionPreferences at all
      };
      
      // COMPETITIVE MODE: Cannot play anywhere without explicit assignments
      expect(canPlayPosition(player, 'Pitcher', true, true)).toBe(false);
      expect(canPlayPosition(player, 'Catcher', true, true)).toBe(false);
      expect(canPlayPosition(player, 'First Base', true, true)).toBe(false);
      
      // FAIR PLAY MODE: Can play anywhere (no restrictions)
      expect(canPlayPosition(player, 'Pitcher', true, false)).toBe(true);
      expect(canPlayPosition(player, 'Catcher', true, false)).toBe(true);
      expect(canPlayPosition(player, 'First Base', true, false)).toBe(true);
    });
  });
  
  describe('buildPositionMatrix', () => {
    it('should only include positions from position_preferences', () => {
      const players: Player[] = [
        {
          id: '1',
          name: 'Avery',
          positionPreferences: {
            leftField: 'preferred',
            rightField: 'neutral',
            secondBase: 'preferred',
            centerField: 'preferred'
          }
        },
        {
          id: '2',
          name: 'Blake',
          positionPreferences: {
            catcher: 'preferred',
            firstBase: 'neutral'
          }
        },
        {
          id: '3',
          name: 'Charlie',
          positionPreferences: {
            pitcher: 'preferred',
            shortstop: 'secondary'
          }
        }
      ];
      
      const matrix = buildStrictMatrix(players);
      
      // Avery should NOT be in catcher list
      expect(matrix.catcher.some(p => p.player.name === 'Avery')).toBe(false);
      
      // Blake should be in catcher list
      expect(matrix.catcher.some(p => p.player.name === 'Blake')).toBe(true);
      
      // Charlie should be in pitcher list
      expect(matrix.pitcher.some(p => p.player.name === 'Charlie')).toBe(true);
      
      // Check that each position only has eligible players
      expect(matrix.leftField.map(p => p.player.name)).toContain('Avery');
      expect(matrix.leftField.map(p => p.player.name)).not.toContain('Blake');
      expect(matrix.leftField.map(p => p.player.name)).not.toContain('Charlie');
    });
    
    it('should exclude players who avoid positions', () => {
      const players: Player[] = [
        {
          id: '1',
          name: 'Player1',
          positionPreferences: {
            pitcher: 'avoid',
            catcher: 'preferred'
          }
        }
      ];
      
      const matrix = buildStrictMatrix(players);
      
      // Should NOT be in pitcher list due to avoid
      expect(matrix.pitcher.some(p => p.player.name === 'Player1')).toBe(false);
      
      // Should be in catcher list
      expect(matrix.catcher.some(p => p.player.name === 'Player1')).toBe(true);
    });
  });
  
  describe('validateLineup', () => {
    it('should catch players assigned to positions not in their preferences', () => {
      const players: Player[] = [
        {
          id: '1',
          name: 'Avery',
          positionPreferences: {
            leftField: 'preferred',
            rightField: 'neutral'
            // No catcher preference
          }
        }
      ];
      
      const lineup = {
        inning: 1,
        positions: {
          catcher: 'Avery', // INVALID - not in preferences
          pitcher: '',
          firstBase: '',
          secondBase: '',
          thirdBase: '',
          shortstop: '',
          leftField: '',
          centerField: '',
          rightField: '',
          bench: []
        }
      };
      
      const rules: LineupRules = {
        limitBenchTime: true,
        respectPositionLockouts: true,
        allowPitcherRotation: true,
        allowCatcherRotation: true,
        equalPlayingTime: false
      };
      
      const result = validateLineup(lineup, players, rules);
      
      // Changed to warning in emergency situations
      expect(result.warnings).toContain('Avery assigned to catcher without position preference entry (emergency assignment?)');
    });
    
    it('should warn about avoid preferences being used', () => {
      const players: Player[] = [
        {
          id: '1',
          name: 'Emergency',
          positionPreferences: {
            pitcher: 'avoid'
          }
        }
      ];
      
      const lineup = {
        inning: 1,
        positions: {
          pitcher: 'Emergency', // Using avoided position
          catcher: '',
          firstBase: '',
          secondBase: '',
          thirdBase: '',
          shortstop: '',
          leftField: '',
          centerField: '',
          rightField: '',
          bench: []
        }
      };
      
      const rules: LineupRules = {
        limitBenchTime: true,
        respectPositionLockouts: true,
        allowPitcherRotation: true,
        allowCatcherRotation: true,
        equalPlayingTime: false
      };
      
      const result = validateLineup(lineup, players, rules);
      
      expect(result.warnings).toContain('Emergency is avoiding pitcher but was assigned anyway (should only happen in emergencies)');
    });
  });
  
  describe('Real-world scenario', () => {
    it('should validate lineup respecting position_preferences', () => {
      const players: Player[] = [
        {
          id: '1',
          name: 'Avery',
          positionPreferences: {
            leftField: 'preferred',
            rightField: 'neutral',
            secondBase: 'preferred',
            centerField: 'preferred'
          }
        },
        {
          id: '2',
          name: 'Avalon',
          positionPreferences: {
            catcher: 'preferred',
            firstBase: 'secondary'
          }
        }
      ];
      
      const rules: LineupRules = {
        limitBenchTime: true,
        respectPositionLockouts: true,
        allowPitcherRotation: true,
        allowCatcherRotation: true,
        equalPlayingTime: false
      };
      
      // Create a test lineup where Avery is incorrectly at catcher
      const invalidLineup = {
        inning: 1,
        positions: {
          catcher: 'Avery', // INVALID - not in her preferences
          pitcher: 'TestPlayer1',
          firstBase: 'TestPlayer2',
          secondBase: 'TestPlayer3',
          thirdBase: 'TestPlayer4',
          shortstop: 'TestPlayer5',
          leftField: 'Avalon',
          centerField: 'TestPlayer6',
          rightField: 'TestPlayer7',
          bench: []
        }
      };
      
      // Validate should catch the error
      const result = validateLineup(invalidLineup, players, rules);
      
      // Changed to warning in emergency situations
      expect(result.warnings).toContain('Avery assigned to catcher without position preference entry (emergency assignment?)');
    });
    
    it('canPlayPosition in real scenario - mode specific', () => {
      const avery: Player = {
        id: '1',
        name: 'Avery',
        positionPreferences: {
          leftField: 'preferred',
          rightField: 'neutral',
          secondBase: 'preferred',
          centerField: 'preferred'
          // No catcher entry
        }
      };
      
      // Competitive mode: CANNOT play catcher (not assigned)
      expect(canPlayPosition(avery, 'catcher', true, true)).toBe(false);
      
      // Fair play mode: CAN play catcher (not explicitly avoided)
      expect(canPlayPosition(avery, 'catcher', true, false)).toBe(true);
      
      // Both modes: CAN play assigned positions
      expect(canPlayPosition(avery, 'leftField', true, true)).toBe(true);
      expect(canPlayPosition(avery, 'leftField', true, false)).toBe(true);
    });
  });
});
import { describe, it, expect } from 'vitest';

// Balance score calculation logic (matching what should be in ViewBatchSeries)
function calculateBalanceScore(playingTimes: number[]): number {
  if (playingTimes.length === 0) return 100;
  
  const totalInnings = playingTimes.reduce((sum, time) => sum + time, 0);
  const avgInnings = totalInnings / playingTimes.length;
  const maxInnings = Math.max(...playingTimes);
  const minInnings = Math.min(...playingTimes);
  const range = maxInnings - minInnings;
  
  // Calculate standard deviation
  const variance = playingTimes.reduce((sum, time) => {
    return sum + Math.pow(time - avgInnings, 2);
  }, 0) / playingTimes.length;
  const stdDev = Math.sqrt(variance);
  
  // Score based on multiple factors
  let score = 100;
  
  // Penalty for range (most important)
  if (range > 2) {
    score -= (range - 2) * 15; // -15 points per inning over 2
  }
  
  // Penalty for standard deviation
  if (stdDev > 1) {
    score -= (stdDev - 1) * 20;
  }
  
  // Penalty for extreme outliers
  playingTimes.forEach(time => {
    const deviation = Math.abs(time - avgInnings);
    if (deviation > avgInnings * 0.3) { // More than 30% deviation
      score -= 10;
    }
  });
  
  return Math.max(0, Math.round(score));
}

describe('Balance Score Calculation Tests', () => {
  it('should return 100% for perfectly balanced playing time', () => {
    // Everyone plays exactly 20 innings
    const perfectBalance = [20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20];
    expect(calculateBalanceScore(perfectBalance)).toBe(100);
  });

  it('should return high score for minor variations', () => {
    // Range of 2 innings (acceptable)
    const goodBalance = [19, 20, 21, 20, 19, 20, 21, 20, 19, 20, 21, 20];
    const score = calculateBalanceScore(goodBalance);
    expect(score).toBeGreaterThanOrEqual(85);
  });

  it('should return low score for large imbalances', () => {
    // The terrible balance from the screenshot (20 vs 11-12)
    const terribleBalance = [20, 20, 20, 12, 12, 12, 11, 11, 11, 11, 11, 11];
    const score = calculateBalanceScore(terribleBalance);
    console.log('Terrible balance score:', score);
    expect(score).toBeLessThan(30); // Should be very low
  });

  it('should penalize extreme outliers heavily', () => {
    // One player plays way too much
    const outlierBalance = [30, 18, 18, 18, 18, 18, 18, 18, 18, 18, 18, 10];
    const score = calculateBalanceScore(outlierBalance);
    expect(score).toBeLessThan(50);
  });

  it('should handle edge cases', () => {
    // Empty array
    expect(calculateBalanceScore([])).toBe(100);
    
    // Single player
    expect(calculateBalanceScore([20])).toBe(100);
    
    // Two players with perfect balance
    expect(calculateBalanceScore([20, 20])).toBe(100);
  });

  it('should calculate realistic tournament scenarios', () => {
    // 4 games × 7 innings = 28 total, 12 players
    // Ideal: ~21 innings per player (28 × 9 / 12 = 21)
    
    // Good distribution
    const goodTournament = [21, 22, 21, 20, 21, 22, 21, 20, 21, 22, 21, 20];
    expect(calculateBalanceScore(goodTournament)).toBeGreaterThanOrEqual(85);
    
    // Acceptable distribution (range of 3)
    const okTournament = [23, 22, 21, 20, 21, 22, 23, 20, 19, 20, 21, 22];
    const okScore = calculateBalanceScore(okTournament);
    expect(okScore).toBeGreaterThanOrEqual(70);
    expect(okScore).toBeLessThan(85);
    
    // Poor distribution (some play too much)
    const poorTournament = [26, 26, 26, 18, 18, 18, 20, 20, 20, 20, 20, 20];
    expect(calculateBalanceScore(poorTournament)).toBeLessThan(60);
  });

  it('should properly weight different factors', () => {
    // Test that range is the most important factor
    const largeRange = [25, 15, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20]; // Range: 10
    const largeRangeScore = calculateBalanceScore(largeRange);
    
    const highStdDev = [22, 18, 22, 18, 22, 18, 22, 18, 22, 18, 22, 18]; // Range: 4, but high variance
    const highStdDevScore = calculateBalanceScore(highStdDev);
    
    // Large range should be penalized more than high std dev
    expect(largeRangeScore).toBeLessThan(highStdDevScore);
  });
});

describe('Balance Score Integration with UI', () => {
  it('should show 0% for the terrible balance case', () => {
    // Recreate the exact scenario from the screenshot
    const scenarioFromScreenshot = [
      20, 20, 20, // Finn, Evelynn, Avery (never benched)
      12, 12, 12, // Mikayla, Bella, Presley (8 bench innings each)
      11, 11, 11, 11, 11, 11 // Others
    ];
    
    const score = calculateBalanceScore(scenarioFromScreenshot);
    console.log('Screenshot scenario score:', score);
    
    // This should definitely show as poor balance
    expect(score).toBeLessThan(20);
  });

  it('should provide meaningful feedback for coaches', () => {
    const scenarios = [
      { 
        name: 'Perfect Balance', 
        times: [20, 20, 20, 20, 20, 20, 20, 20, 20],
        expectedFeedback: 'Excellent'
      },
      { 
        name: 'Good Balance', 
        times: [21, 20, 19, 21, 20, 19, 21, 20, 19],
        expectedFeedback: 'Good'
      },
      { 
        name: 'Needs Improvement', 
        times: [23, 22, 18, 17, 23, 22, 18, 17, 20],
        expectedFeedback: 'Fair'
      },
      { 
        name: 'Poor Balance', 
        times: [25, 25, 25, 15, 15, 15, 20, 20, 20],
        expectedFeedback: 'Poor'
      }
    ];

    scenarios.forEach(scenario => {
      const score = calculateBalanceScore(scenario.times);
      let feedback: string;
      
      if (score >= 90) feedback = 'Excellent';
      else if (score >= 75) feedback = 'Good';
      else if (score >= 50) feedback = 'Fair';
      else feedback = 'Poor';
      
      console.log(`${scenario.name}: ${score}% (${feedback})`);
      expect(feedback).toBe(scenario.expectedFeedback);
    });
  });
});
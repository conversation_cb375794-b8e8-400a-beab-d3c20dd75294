import { describe, test, expect, beforeEach } from 'vitest';
import {
  shouldRotateThisInning,
  getBenchStreaks,
  createEnhancedRotationPlan,
  detectViolations,
  applyConditionalFallbacks,
  UnbiasedConstraintSolver
} from '../lib/utils-enhanced-fixed';
import { Player, InningLineup, LineupRules } from '../lib/utils-enhanced';

describe('Fix Lineup Algorithm Tests', () => {
  let mockPlayers: Player[];
  let mockRules: LineupRules;
  
  beforeEach(() => {
    // Create test players
    mockPlayers = [
      { id: '1', name: '<PERSON>', teamRoles: { pitcher: 'primary', shortstop: 'in the mix' } },
      { id: '2', name: '<PERSON>', teamRoles: { catcher: 'primary', firstBase: 'in the mix' } },
      { id: '3', name: '<PERSON>', teamRoles: { firstBase: 'primary', rightField: 'in the mix' } },
      { id: '4', name: '<PERSON>', teamRoles: { secondBase: 'primary', centerField: 'in the mix' } },
      { id: '5', name: '<PERSON>', teamRoles: { thirdBase: 'primary', leftField: 'in the mix' } },
      { id: '6', name: '<PERSON>', teamRoles: { shortstop: 'primary', pitcher: 'emergency' } },
      { id: '7', name: 'Grace', teamRoles: { leftField: 'primary', secondBase: 'in the mix' } },
      { id: '8', name: 'Henry', teamRoles: { centerField: 'primary', thirdBase: 'in the mix' } },
      { id: '9', name: 'Iris', teamRoles: { rightField: 'primary', catcher: 'emergency' } },
      { id: '10', name: 'Jack', teamRoles: { pitcher: 'in the mix', firstBase: 'emergency' } },
    ] as Player[];
    
    mockRules = {
      rotateLineupEvery: 2,
      rotatePitcherEvery: 3,
      maxConsecutiveBenchInnings: 2,
      equalPlayingTime: false,
      allowPitcherRotation: true,
      allowCatcherRotation: true,
      limitBenchTime: true,
      respectPositionLockouts: true
    };
  });
  
  describe('Rotation Logic (0-based vs 1-based)', () => {
    test('should not rotate in first inning', () => {
      expect(shouldRotateThisInning(1, mockRules)).toBe(false);
    });
    
    test('should rotate at correct intervals for rotateLineupEvery = 2', () => {
      const rules = { ...mockRules, rotateLineupEvery: 2 };
      
      expect(shouldRotateThisInning(1, rules)).toBe(false); // Inning 1: no rotation
      expect(shouldRotateThisInning(2, rules)).toBe(false); // Inning 2: no rotation
      expect(shouldRotateThisInning(3, rules)).toBe(true);  // Inning 3: ROTATE (after 2)
      expect(shouldRotateThisInning(4, rules)).toBe(false); // Inning 4: no rotation
      expect(shouldRotateThisInning(5, rules)).toBe(true);  // Inning 5: ROTATE (after 2 more)
      expect(shouldRotateThisInning(6, rules)).toBe(false); // Inning 6: no rotation
      expect(shouldRotateThisInning(7, rules)).toBe(true);  // Inning 7: ROTATE
    });
    
    test('should rotate at correct intervals for rotateLineupEvery = 3', () => {
      const rules = { ...mockRules, rotateLineupEvery: 3 };
      
      expect(shouldRotateThisInning(1, rules)).toBe(false); // Inning 1: no rotation
      expect(shouldRotateThisInning(2, rules)).toBe(false); // Inning 2: no rotation
      expect(shouldRotateThisInning(3, rules)).toBe(false); // Inning 3: no rotation
      expect(shouldRotateThisInning(4, rules)).toBe(true);  // Inning 4: ROTATE (after 3)
      expect(shouldRotateThisInning(5, rules)).toBe(false); // Inning 5: no rotation
      expect(shouldRotateThisInning(6, rules)).toBe(false); // Inning 6: no rotation
      expect(shouldRotateThisInning(7, rules)).toBe(true);  // Inning 7: ROTATE (after 3 more)
    });
    
    test('should rotate every inning when rotateLineupEvery = 1', () => {
      const rules = { ...mockRules, rotateLineupEvery: 1 };
      
      expect(shouldRotateThisInning(1, rules)).toBe(false); // Never first inning
      expect(shouldRotateThisInning(2, rules)).toBe(true);
      expect(shouldRotateThisInning(3, rules)).toBe(true);
      expect(shouldRotateThisInning(4, rules)).toBe(true);
    });
    
    test('equal playing time should not override rotation frequency', () => {
      const rules = { ...mockRules, rotateLineupEvery: 2, equalPlayingTime: true };
      
      // Should still respect rotateLineupEvery = 2
      expect(shouldRotateThisInning(2, rules)).toBe(false);
      expect(shouldRotateThisInning(3, rules)).toBe(true);
    });
  });
  
  describe('Bench Streak Calculation', () => {
    test('should correctly count consecutive bench innings', () => {
      const innings: InningLineup[] = [
        {
          inning: 1,
          positions: {
            pitcher: 'Alice',
            catcher: 'Bob',
            firstBase: 'Charlie',
            secondBase: 'Dana',
            thirdBase: 'Eve',
            shortstop: 'Frank',
            leftField: 'Grace',
            centerField: 'Henry',
            rightField: 'Iris',
            bench: ['Jack']
          }
        },
        {
          inning: 2,
          positions: {
            pitcher: 'Alice',
            catcher: 'Bob',
            firstBase: 'Charlie',
            secondBase: 'Dana',
            thirdBase: 'Eve',
            shortstop: 'Frank',
            leftField: 'Grace',
            centerField: 'Henry',
            rightField: 'Iris',
            bench: ['Jack'] // Jack on bench again
          }
        },
        {
          inning: 3,
          positions: {
            pitcher: 'Alice',
            catcher: 'Bob',
            firstBase: 'Jack', // Jack finally plays
            secondBase: 'Dana',
            thirdBase: 'Eve',
            shortstop: 'Frank',
            leftField: 'Grace',
            centerField: 'Henry',
            rightField: 'Iris',
            bench: ['Charlie']
          }
        }
      ];
      
      // Check after inning 2
      let streaks = getBenchStreaks(innings, 2);
      expect(streaks.get('Jack')).toBe(2); // On bench for 2 innings
      expect(streaks.get('Alice')).toBe(0); // Playing
      expect(streaks.get('Charlie')).toBe(0); // Playing
      
      // Check after inning 3
      streaks = getBenchStreaks(innings, 3);
      expect(streaks.get('Jack')).toBe(0); // Now playing, streak reset
      expect(streaks.get('Charlie')).toBe(1); // Now on bench
    });
    
    test('should handle non-consecutive bench innings correctly', () => {
      const innings: InningLineup[] = [
        {
          inning: 1,
          positions: {
            pitcher: 'Alice',
            catcher: 'Bob',
            firstBase: 'Charlie',
            secondBase: 'Dana',
            thirdBase: 'Eve',
            shortstop: 'Frank',
            leftField: 'Grace',
            centerField: 'Henry',
            rightField: 'Iris',
            bench: ['Jack']
          }
        },
        {
          inning: 2,
          positions: {
            pitcher: 'Jack', // Jack plays
            catcher: 'Bob',
            firstBase: 'Charlie',
            secondBase: 'Dana',
            thirdBase: 'Eve',
            shortstop: 'Frank',
            leftField: 'Grace',
            centerField: 'Henry',
            rightField: 'Iris',
            bench: ['Alice']
          }
        },
        {
          inning: 3,
          positions: {
            pitcher: 'Alice',
            catcher: 'Bob',
            firstBase: 'Charlie',
            secondBase: 'Dana',
            thirdBase: 'Eve',
            shortstop: 'Frank',
            leftField: 'Grace',
            centerField: 'Henry',
            rightField: 'Iris',
            bench: ['Jack'] // Jack back on bench
          }
        }
      ];
      
      const streaks = getBenchStreaks(innings, 3);
      expect(streaks.get('Jack')).toBe(1); // Only 1 consecutive inning on bench
      expect(streaks.get('Alice')).toBe(0); // Currently playing
    });
  });
  
  describe('Enhanced Rotation Plan with Bench Streak Reset', () => {
    test('should track forced players and reset their bench streaks', () => {
      const current: InningLineup = {
        inning: 3,
        positions: {
          pitcher: 'Alice',
          catcher: 'Bob',
          firstBase: 'Charlie',
          secondBase: 'Dana',
          thirdBase: 'Eve',
          shortstop: 'Frank',
          leftField: 'Grace',
          centerField: 'Henry',
          rightField: 'Iris',
          bench: ['Jack']
        }
      };
      
      const benchStreaks = new Map([
        ['Jack', 3], // Violation! Been on bench too long
        ['Alice', 0],
        ['Bob', 0]
      ]);
      
      const playerStats = new Map(mockPlayers.map(p => [
        p.name,
        { fieldInnings: 3, benchInnings: 0 }
      ]));
      playerStats.set('Jack', { fieldInnings: 0, benchInnings: 3 });
      
      const plan = createEnhancedRotationPlan(
        current,
        mockPlayers,
        mockRules,
        benchStreaks,
        playerStats,
        { shuffle: (arr: any[]) => arr }
      );
      
      // Should force Jack off bench
      expect(plan.forcedPlayers.has('Jack')).toBe(true);
      
      // Jack's bench streak should be reset to 0 after rotation
      expect(plan.benchStreaksAfter.get('Jack')).toBe(0);
      
      // Should have a field assignment for Jack
      const jackAssigned = Array.from(plan.fieldAssignments.values()).includes('Jack');
      expect(jackAssigned).toBe(true);
    });
  });
  
  describe('Violation Detection', () => {
    test('should detect bench violations', () => {
      const innings: InningLineup[] = [
        {
          inning: 1,
          positions: {
            pitcher: 'Alice',
            catcher: 'Bob',
            firstBase: 'Charlie',
            secondBase: 'Dana',
            thirdBase: 'Eve',
            shortstop: 'Frank',
            leftField: 'Grace',
            centerField: 'Henry',
            rightField: 'Iris',
            bench: ['Jack']
          }
        },
        {
          inning: 2,
          positions: {
            pitcher: 'Alice',
            catcher: 'Bob',
            firstBase: 'Charlie',
            secondBase: 'Dana',
            thirdBase: 'Eve',
            shortstop: 'Frank',
            leftField: 'Grace',
            centerField: 'Henry',
            rightField: 'Iris',
            bench: ['Jack']
          }
        },
        {
          inning: 3,
          positions: {
            pitcher: 'Alice',
            catcher: 'Bob',
            firstBase: 'Charlie',
            secondBase: 'Dana',
            thirdBase: 'Eve',
            shortstop: 'Frank',
            leftField: 'Grace',
            centerField: 'Henry',
            rightField: 'Iris',
            bench: ['Jack'] // 3rd consecutive inning!
          }
        }
      ];
      
      const violations = detectViolations(innings, mockPlayers, mockRules);
      
      expect(violations.hasViolations).toBe(true);
      expect(violations.benchViolations).toHaveLength(1);
      expect(violations.benchViolations[0]).toEqual({
        player: 'Jack',
        streak: 3,
        maxAllowed: 2
      });
    });
    
    test('should detect empty positions', () => {
      const innings: InningLineup[] = [
        {
          inning: 1,
          positions: {
            pitcher: 'Alice',
            catcher: 'Bob',
            firstBase: '', // Empty!
            secondBase: 'Dana',
            thirdBase: 'Eve',
            shortstop: 'Frank',
            leftField: 'Grace',
            centerField: 'Henry',
            rightField: 'Iris',
            bench: ['Jack', 'Charlie']
          }
        }
      ];
      
      const violations = detectViolations(innings, mockPlayers, mockRules);
      
      expect(violations.hasViolations).toBe(true);
      expect(violations.emptyPositions).toHaveLength(1);
      expect(violations.emptyPositions[0]).toEqual({
        inning: 1,
        position: 'firstBase'
      });
    });
    
    test('should calculate balance score correctly', () => {
      const innings: InningLineup[] = Array(6).fill(null).map((_, i) => ({
        inning: i + 1,
        positions: {
          pitcher: 'Alice',
          catcher: 'Bob',
          firstBase: 'Charlie',
          secondBase: 'Dana',
          thirdBase: 'Eve',
          shortstop: 'Frank',
          leftField: 'Grace',
          centerField: 'Henry',
          rightField: 'Iris',
          bench: ['Jack'] // Jack never plays!
        }
      }));
      
      const violations = detectViolations(innings, mockPlayers, mockRules);
      
      expect(violations.playingTimeRange).toBe(6); // Jack: 0, others: 6
      expect(violations.balanceScore).toBeLessThan(70); // Poor balance
      expect(violations.hasViolations).toBe(true);
    });
  });
  
  describe('Unbiased Constraint Solver', () => {
    test('should produce different results with different random seeds', () => {
      const eligibilityCache = {
        getEligiblePositions: (player: Player) => {
          const positions = new Set<string>();
          Object.entries(player.teamRoles || {}).forEach(([pos, role]) => {
            if (role !== 'avoid' && role !== 'never') {
              positions.add(pos);
            }
          });
          return positions;
        }
      };
      
      // Create players with multiple eligible positions
      const flexiblePlayers = mockPlayers.map(p => ({
        ...p,
        teamRoles: {
          ...p.teamRoles,
          pitcher: 'in the mix',
          catcher: 'in the mix',
          firstBase: 'in the mix',
          secondBase: 'in the mix',
          thirdBase: 'in the mix',
          shortstop: 'in the mix',
          leftField: 'in the mix',
          centerField: 'in the mix',
          rightField: 'in the mix'
        }
      })) as Player[];
      
      // Random 1
      const random1 = {
        shuffle: (arr: any[]) => [...arr].sort(() => 0.5)
      };
      const solver1 = new UnbiasedConstraintSolver(flexiblePlayers, eligibilityCache, random1);
      const result1 = solver1.solve();
      
      // Random 2 (different seed)
      const random2 = {
        shuffle: (arr: any[]) => [...arr].sort(() => -0.5)
      };
      const solver2 = new UnbiasedConstraintSolver(flexiblePlayers, eligibilityCache, random2);
      const result2 = solver2.solve();
      
      // Results should be different (at least one position different)
      let differences = 0;
      Object.keys(result1).forEach(pos => {
        if (result1[pos] !== result2[pos]) differences++;
      });
      
      expect(differences).toBeGreaterThan(0);
    });
    
    test('should tie-break by playing time when enabled', () => {
      const eligibilityCache = {
        getEligiblePositions: (player: Player) => {
          const positions = new Set<string>();
          Object.entries(player.teamRoles || {}).forEach(([pos, role]) => {
            if (role !== 'avoid' && role !== 'never') {
              positions.add(pos);
            }
          });
          return positions;
        }
      };
      
      // Create two players with same role for pitcher
      const tiedPlayers = [
        { id: '1', name: 'Alice', teamRoles: { pitcher: 'primary' } },
        { id: '2', name: 'Bob', teamRoles: { pitcher: 'primary' } },
        ...mockPlayers.slice(2)
      ] as Player[];
      
      // Bob has played more
      const playerStats = new Map([
        ['1', { fieldInnings: 2 }], // Alice: less time
        ['2', { fieldInnings: 5 }]  // Bob: more time
      ]);
      
      const solver = new UnbiasedConstraintSolver(
        tiedPlayers, 
        eligibilityCache,
        { shuffle: (arr: any[]) => arr }, // No randomization
        true // Tie-break by playing time
      );
      
      const result = solver.solve(playerStats);
      
      // Alice should get pitcher (less playing time)
      expect(result.pitcher).toBe('Alice');
    });
  });
  
  describe('Conditional Fallback Application', () => {
    test('should not apply fallbacks when no violations exist', () => {
      const goodInnings: InningLineup[] = [
        {
          inning: 1,
          positions: {
            pitcher: 'Alice',
            catcher: 'Bob',
            firstBase: 'Charlie',
            secondBase: 'Dana',
            thirdBase: 'Eve',
            shortstop: 'Frank',
            leftField: 'Grace',
            centerField: 'Henry',
            rightField: 'Iris',
            bench: ['Jack']
          }
        },
        {
          inning: 2,
          positions: {
            pitcher: 'Jack',
            catcher: 'Bob',
            firstBase: 'Charlie',
            secondBase: 'Dana',
            thirdBase: 'Eve',
            shortstop: 'Frank',
            leftField: 'Grace',
            centerField: 'Henry',
            rightField: 'Alice',
            bench: ['Iris']
          }
        }
      ];
      
      const originalInnings = JSON.stringify(goodInnings);
      const result = applyConditionalFallbacks(goodInnings, mockPlayers, mockRules);
      
      // Should be unchanged
      expect(JSON.stringify(result)).toBe(originalInnings);
    });
    
    test('should apply fallbacks only for actual violations', () => {
      const badInnings: InningLineup[] = Array(4).fill(null).map((_, i) => ({
        inning: i + 1,
        positions: {
          pitcher: 'Alice',
          catcher: 'Bob',
          firstBase: 'Charlie',
          secondBase: 'Dana',
          thirdBase: 'Eve',
          shortstop: 'Frank',
          leftField: 'Grace',
          centerField: 'Henry',
          rightField: 'Iris',
          bench: ['Jack'] // Violation after 3 innings!
        }
      }));
      
      const violations = detectViolations(badInnings, mockPlayers, mockRules);
      expect(violations.hasViolations).toBe(true);
      expect(violations.benchViolations.length).toBeGreaterThan(0);
      
      // Should trigger fallback strategies
      const result = applyConditionalFallbacks(badInnings, mockPlayers, mockRules);
      
      // Verify violation was addressed (this is simplified - real implementation would fix it)
      const newViolations = detectViolations(result, mockPlayers, mockRules);
      expect(newViolations.benchViolations.length).toBeLessThanOrEqual(violations.benchViolations.length);
    });
  });
});
import { describe, it, expect } from 'vitest';

// Since we can't easily test hooks without React testing library,
// let's test the core logic by simulating the hook's behavior
describe('Lineup History Logic', () => {
  // Simulate the core history functionality
  class LineupHistory<T> {
    private history: Array<{ data: T; description?: string; timestamp: number }> = [];
    private currentIndex = 0;

    constructor(initialData: T) {
      this.history.push({
        data: initialData,
        description: 'Initial state',
        timestamp: Date.now(),
      });
    }

    get data(): T {
      return this.history[this.currentIndex].data;
    }

    pushState(newData: T, description?: string): void {
      // Remove any history after current index
      this.history = this.history.slice(0, this.currentIndex + 1);
      
      this.history.push({
        data: newData,
        description,
        timestamp: Date.now(),
      });
      
      this.currentIndex = this.history.length - 1;
    }

    undo(): boolean {
      if (this.currentIndex > 0) {
        this.currentIndex--;
        return true;
      }
      return false;
    }

    redo(): boolean {
      if (this.currentIndex < this.history.length - 1) {
        this.currentIndex++;
        return true;
      }
      return false;
    }

    getHistoryInfo() {
      return {
        canUndo: this.currentIndex > 0,
        canRedo: this.currentIndex < this.history.length - 1,
        currentDescription: this.history[this.currentIndex]?.description || '',
        historyLength: this.history.length,
        currentIndex: this.currentIndex,
      };
    }

    clearHistory(newInitialData?: T): void {
      const data = newInitialData || this.history[0].data;
      this.history = [{
        data,
        description: 'Initial state',
        timestamp: Date.now(),
      }];
      this.currentIndex = 0;
    }
  }

  it('should initialize with the provided initial data', () => {
    const initialData = { id: '1', name: 'Test Lineup', innings: [] };
    const history = new LineupHistory(initialData);
    
    expect(history.data).toEqual(initialData);
    expect(history.getHistoryInfo().canUndo).toBe(false);
    expect(history.getHistoryInfo().canRedo).toBe(false);
  });

  it('should push new states to history', () => {
    const initialData = { id: '1', name: 'Test Lineup', innings: [] };
    const history = new LineupHistory(initialData);
    
    const newData = { id: '1', name: 'Updated Lineup', innings: [] };
    history.pushState(newData, 'Updated lineup name');
    
    expect(history.data).toEqual(newData);
    expect(history.getHistoryInfo().canUndo).toBe(true);
    expect(history.getHistoryInfo().canRedo).toBe(false);
  });

  it('should undo to previous state', () => {
    const initialData = { id: '1', name: 'Test Lineup', innings: [] };
    const history = new LineupHistory(initialData);
    
    const newData = { id: '1', name: 'Updated Lineup', innings: [] };
    history.pushState(newData, 'Updated lineup name');
    
    const success = history.undo();
    expect(success).toBe(true);
    
    expect(history.data).toEqual(initialData);
    expect(history.getHistoryInfo().canUndo).toBe(false);
    expect(history.getHistoryInfo().canRedo).toBe(true);
  });

  it('should redo to next state', () => {
    const initialData = { id: '1', name: 'Test Lineup', innings: [] };
    const history = new LineupHistory(initialData);
    
    const newData = { id: '1', name: 'Updated Lineup', innings: [] };
    history.pushState(newData, 'Updated lineup name');
    history.undo();
    
    const success = history.redo();
    expect(success).toBe(true);
    
    expect(history.data).toEqual(newData);
    expect(history.getHistoryInfo().canUndo).toBe(true);
    expect(history.getHistoryInfo().canRedo).toBe(false);
  });

  it('should clear redo history when pushing new state after undo', () => {
    const initialData = { id: '1', name: 'Test Lineup', innings: [] };
    const history = new LineupHistory(initialData);
    
    const state1 = { id: '1', name: 'State 1', innings: [] };
    const state2 = { id: '1', name: 'State 2', innings: [] };
    const state3 = { id: '1', name: 'State 3', innings: [] };
    
    history.pushState(state1, 'State 1');
    history.pushState(state2, 'State 2');
    history.undo(); // Back to state1
    
    expect(history.getHistoryInfo().canRedo).toBe(true);
    
    history.pushState(state3, 'State 3'); // This should clear redo history
    
    expect(history.data).toEqual(state3);
    expect(history.getHistoryInfo().canRedo).toBe(false);
    expect(history.getHistoryInfo().historyLength).toBe(3); // initial, state1, state3
  });

  it('should handle complex lineup changes', () => {
    const initialLineup = {
      id: '1',
      name: 'Test Game',
      innings: [
        {
          inning: 1,
          positions: {
            pitcher: 'Player1',
            catcher: 'Player2',
            firstBase: 'Player3',
            bench: ['Player4', 'Player5']
          }
        }
      ]
    };
    
    const history = new LineupHistory(initialLineup);
    
    // Simulate position swap
    const swappedLineup = {
      ...initialLineup,
      innings: [
        {
          inning: 1,
          positions: {
            pitcher: 'Player2',
            catcher: 'Player1',
            firstBase: 'Player3',
            bench: ['Player4', 'Player5']
          }
        }
      ]
    };
    
    history.pushState(swappedLineup, 'Swapped pitcher and catcher');
    
    expect(history.data.innings[0].positions.pitcher).toBe('Player2');
    expect(history.data.innings[0].positions.catcher).toBe('Player1');
    
    // Undo the swap
    history.undo();
    
    expect(history.data.innings[0].positions.pitcher).toBe('Player1');
    expect(history.data.innings[0].positions.catcher).toBe('Player2');
  });

  it('should track multiple changes with descriptions', () => {
    const lineup = { id: '1', name: 'Test', innings: [] };
    const history = new LineupHistory(lineup);
    
    history.pushState({ ...lineup, name: 'Game 1' }, 'Renamed to Game 1');
    history.pushState({ ...lineup, name: 'Game 2' }, 'Renamed to Game 2');
    history.pushState({ ...lineup, name: 'Game 3' }, 'Renamed to Game 3');
    
    expect(history.getHistoryInfo().historyLength).toBe(4);
    expect(history.getHistoryInfo().currentDescription).toBe('Renamed to Game 3');
    
    history.undo();
    expect(history.getHistoryInfo().currentDescription).toBe('Renamed to Game 2');
    
    history.undo();
    expect(history.getHistoryInfo().currentDescription).toBe('Renamed to Game 1');
  });
});
import { describe, it, expect } from 'vitest';
import { generateCompleteLineup } from '@/lib/utils-enhanced';
import { Player } from '@/contexts/TeamContext';

describe('Enhanced Fairness Debug', () => {
  it('should demonstrate enhanced fairness with equalPlayingTime enabled', () => {
    const players: Player[] = Array.from({ length: 12 }, (_, i) => ({
      id: `${i + 1}`,
      name: `Player${i + 1}`,
      teamRoles: {}
    }));

    console.log('🧪 TESTING ENHANCED FAIRNESS LOGIC');
    console.log(`Players: ${players.length}, Innings: 7`);

    const innings = generateCompleteLineup(players, 7, {
      rotateLineupEvery: 1,
      rotatePitcherEvery: 2,
      equalPlayingTime: true,  // ENABLE FAIRNESS MODE
      limitBenchTime: true,
      maxConsecutiveBenchInnings: 2
    });

    // Calculate playing time
    const playingTime: Record<string, number> = {};
    players.forEach(p => playingTime[p.name] = 0);

    innings.forEach(inning => {
      Object.entries(inning.positions).forEach(([pos, playerName]) => {
        if (pos !== 'bench' && playerName && typeof playerName === 'string') {
          playingTime[playerName]++;
        }
      });
    });

    const times = Object.values(playingTime);
    const maxTime = Math.max(...times);
    const minTime = Math.min(...times);
    const range = maxTime - minTime;

    console.log('\n📊 ENHANCED FAIRNESS RESULTS:');
    console.log('Playing time distribution:', playingTime);
    console.log(`Range: ${minTime}-${maxTime} (${range} innings difference)`);
    console.log(`Fair? ${range <= 1 ? 'YES ✅' : 'NO ❌'}`);

    // Test mathematical expectation: 12 players, 7 innings
    // 7 * 9 = 63 total field positions
    // 63 / 12 = 5.25 average per player
    // So we expect: 9 players get 5 innings, 3 players get 6 innings
    const expectedMin = 5;
    const expectedMax = 6;
    const expectedRange = 1;

    console.log(`\nExpected: ${expectedMin}-${expectedMax} (range ${expectedRange})`);
    console.log(`Actual: ${minTime}-${maxTime} (range ${range})`);
    console.log(`Enhanced fairness working: ${range <= expectedRange ? 'YES ✅' : 'NO ❌'}`);

    // With the simple fairness approach, we should achieve PERFECT mathematical optimum
    console.log(`\n🎯 FINAL TEST RESULTS:`);
    console.log(`Expected range: 0-1 innings (mathematical optimum)`);
    console.log(`Actual range: ${range} innings`);
    console.log(`PERFECT fairness achieved: ${range <= 1 ? 'YES ✅' : 'NO ❌'}`);

    expect(range).toBeLessThanOrEqual(expectedRange);
    expect(minTime).toBeGreaterThanOrEqual(expectedMin);
    expect(maxTime).toBeLessThanOrEqual(expectedMax);
  });

  it('should compare standard vs enhanced fairness modes', () => {
    const players: Player[] = Array.from({ length: 12 }, (_, i) => ({
      id: `${i + 1}`,
      name: `Player${i + 1}`,
      teamRoles: {}
    }));

    // Test standard mode (equalPlayingTime: false)
    const standardInnings = generateCompleteLineup(players, 7, {
      rotateLineupEvery: 1,
      rotatePitcherEvery: 2,
      equalPlayingTime: false,  // STANDARD MODE
      limitBenchTime: false,
      maxConsecutiveBenchInnings: 999
    });

    // Test enhanced fairness mode (equalPlayingTime: true)
    const fairnessInnings = generateCompleteLineup(players, 7, {
      rotateLineupEvery: 1,
      rotatePitcherEvery: 2,
      equalPlayingTime: true,   // ENHANCED FAIRNESS MODE
      limitBenchTime: true,
      maxConsecutiveBenchInnings: 2
    });

    // Calculate playing time for both modes
    const calculatePlayingTime = (innings: any[]) => {
      const playingTime: Record<string, number> = {};
      players.forEach(p => playingTime[p.name] = 0);

      innings.forEach(inning => {
        Object.entries(inning.positions).forEach(([pos, playerName]) => {
          if (pos !== 'bench' && playerName && typeof playerName === 'string') {
            playingTime[playerName]++;
          }
        });
      });

      const times = Object.values(playingTime);
      return {
        playingTime,
        maxTime: Math.max(...times),
        minTime: Math.min(...times),
        range: Math.max(...times) - Math.min(...times)
      };
    };

    const standardResults = calculatePlayingTime(standardInnings);
    const fairnessResults = calculatePlayingTime(fairnessInnings);

    console.log('\n🔄 STANDARD MODE RESULTS:');
    console.log('Playing time:', standardResults.playingTime);
    console.log(`Range: ${standardResults.minTime}-${standardResults.maxTime} (${standardResults.range} innings)`);

    console.log('\n⚖️ ENHANCED FAIRNESS MODE RESULTS:');
    console.log('Playing time:', fairnessResults.playingTime);
    console.log(`Range: ${fairnessResults.minTime}-${fairnessResults.maxTime} (${fairnessResults.range} innings)`);

    console.log('\n📈 COMPARISON:');
    console.log(`Standard range: ${standardResults.range} innings`);
    console.log(`Fairness range: ${fairnessResults.range} innings`);
    console.log(`Improvement: ${standardResults.range - fairnessResults.range} innings`);
    console.log(`Enhanced fairness better: ${fairnessResults.range < standardResults.range ? 'YES ✅' : 'NO ❌'}`);

    // Enhanced fairness should produce equal or better results
    expect(fairnessResults.range).toBeLessThanOrEqual(standardResults.range);
  });
});
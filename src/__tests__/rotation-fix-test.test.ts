import { generateCompleteLineup, LineupRules } from '../lib/utils-enhanced';
import { Player } from '../contexts/TeamContext';
import { describe, test, expect, vi } from 'vitest';

// Mock the supabase client
vi.mock('../integrations/supabase/client', () => ({
  supabase: {
    auth: {
      getUser: vi.fn().mockResolvedValue({ data: { user: null }, error: null })
    },
    from: vi.fn(() => ({
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: null, error: null })
    }))
  }
}));

const ROTATION_TEST_PLAYERS: Player[] = [
  {
    id: '1',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: false,    // Can pitch
      catcher: true,     // Cannot catch
      firstBase: true,   // Cannot play 1B
      other: null
    },
    positionRatings: { 
      pitcher: 5,        // Elite pitcher
      shortstop: 3,      // Can play SS as backup
      leftField: 2       // Emergency outfield
    },
    isStarPlayer: true
  },
  {
    id: '2',
    name: 'Ryan Martinez',
    positionRestrictions: {
      pitcher: false,    // Can pitch
      catcher: true,     // Cannot catch
      firstBase: false,  // Can play 1B
      other: null
    },
    positionRatings: { 
      pitcher: 4,        // Strong pitcher
      firstBase: 4,      // Strong 1B
      leftField: 3       // Decent outfield
    },
    isStarPlayer: false
  },
  {
    id: '3',
    name: 'Ben Rodriguez',
    positionRestrictions: {
      pitcher: true,     // Cannot pitch
      catcher: false,    // Can catch
      firstBase: false,  // Can play 1B
      other: null
    },
    positionRatings: { 
      catcher: 5,        // Elite catcher
      thirdBase: 4,      // Strong 3B
      rightField: 3      // Decent outfield
    },
    isStarPlayer: true
  },
  {
    id: '4',
    name: 'Charlie Kim',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      shortstop: 5,      // Elite SS
      secondBase: 4,     // Strong 2B
      centerField: 4     // Strong CF
    },
    isStarPlayer: true
  },
  {
    id: '5',
    name: 'David Chen',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      firstBase: 4,      // Strong 1B
      thirdBase: 3,      // Decent 3B
      leftField: 3       // Decent LF
    },
    isStarPlayer: false
  },
  {
    id: '6',
    name: 'Emma Wilson',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      secondBase: 4,     // Strong 2B
      shortstop: 3,      // Decent SS
      rightField: 3      // Decent RF
    },
    isStarPlayer: false
  },
  {
    id: '7',
    name: 'Frank Miller',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      thirdBase: 4,      // Strong 3B
      firstBase: 3,      // Decent 1B
      centerField: 2     // Emergency CF
    },
    isStarPlayer: false
  },
  {
    id: '8',
    name: 'Grace Lee',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      leftField: 4,      // Strong LF
      centerField: 4,    // Strong CF
      rightField: 3      // Decent RF
    },
    isStarPlayer: false
  },
  {
    id: '9',
    name: 'Henry Davis',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      centerField: 5,    // Elite CF
      leftField: 4,      // Strong LF
      rightField: 4      // Strong RF
    },
    isStarPlayer: true
  },
  {
    id: '10',
    name: 'Ivy Johnson',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      rightField: 4,     // Strong RF
      centerField: 3,    // Decent CF
      firstBase: 2       // Emergency 1B
    },
    isStarPlayer: false
  }
];

describe('Rotation Rule Fix Testing', () => {
  test('should handle rotation frequency mismatch - the exact bug scenario', () => {
    // This is the exact scenario that caused the duplicate assignment bug
    const problematicRules: LineupRules = {
      respectPositionLockouts: true,
      limitBenchTime: true,
      maxConsecutiveBenchInnings: 3,
      allowPitcherRotation: true,
      allowCatcherRotation: true,
      equalPlayingTime: false,
      rotateLineupEvery: 2,      // Rotate lineup every 2 innings
      rotatePitcherEvery: 4,     // BUT pitcher rotates every 4 innings
      competitiveMode: true,     // Competitive mode enabled
      competitiveMinPlayingTime: 40,
      keyPositions: ['pitcher', 'catcher', 'shortstop', 'centerField'],
      starPlayerRotationDelay: 2,
      _randomSeed: 12345
    };

    console.log('🎯 Testing problematic rotation rules:');
    console.log('   - Rotate lineup every 2 innings');
    console.log('   - Rotate pitcher every 4 innings');
    console.log('   - Competitive mode: true');

    // This should NOT throw a duplicate assignment error
    const lineup = generateCompleteLineup(ROTATION_TEST_PLAYERS, 5, problematicRules);
    
    expect(lineup).toHaveLength(5);
    
    // Validate each inning for no duplicate assignments
    lineup.forEach((inning, inningIndex) => {
      console.log(`\n📋 Inning ${inningIndex + 1}:`);
      
      const assignedPlayers = new Set<string>();
      const duplicates: string[] = [];

      // Check field positions
      Object.entries(inning.positions).forEach(([position, player]) => {
        if (position !== 'bench' && typeof player === 'string') {
          console.log(`  ${position}: ${player}`);
          
          if (assignedPlayers.has(player)) {
            duplicates.push(player);
          }
          assignedPlayers.add(player);
        }
      });

      // Check bench players
      const benchPlayers = inning.positions.bench || [];
      if (benchPlayers.length > 0) {
        console.log(`  bench: ${benchPlayers.join(', ')}`);
      }
      
      benchPlayers.forEach(player => {
        if (assignedPlayers.has(player)) {
          duplicates.push(player);
        }
        assignedPlayers.add(player);
      });

      // Assert no duplicates
      expect(duplicates).toHaveLength(0);
    });

    // Verify rotation rules are respected
    const pitchers = lineup.map(inning => inning.positions.pitcher);
    console.log(`\n🎯 Pitcher rotation: ${pitchers.join(' → ')}`);

    // Alex should pitch innings 1-4, then rotation should happen
    expect(pitchers[0]).toBe('Alex Thompson');
    expect(pitchers[1]).toBe('Alex Thompson');
    expect(pitchers[2]).toBe('Alex Thompson');
    expect(pitchers[3]).toBe('Alex Thompson');
    
    // Inning 5 should have a different pitcher (rotation triggered)
    expect(pitchers[4]).not.toBe('Alex Thompson');
  });

  test('should respect user game rotation rules as source of truth', () => {
    const userGameRules: LineupRules = {
      respectPositionLockouts: true,
      limitBenchTime: true,
      maxConsecutiveBenchInnings: 2,
      allowPitcherRotation: true,
      allowCatcherRotation: true,
      equalPlayingTime: false,
      rotateLineupEvery: 1,      // User wants rotation every inning
      rotatePitcherEvery: 2,     // User wants new pitcher every 2 innings
      competitiveMode: true,
      competitiveMinPlayingTime: 40,
      keyPositions: ['pitcher', 'catcher', 'shortstop'],
      starPlayerRotationDelay: 1,
      _randomSeed: 12345
    };

    const lineup = generateCompleteLineup(ROTATION_TEST_PLAYERS, 6, userGameRules);
    
    // Verify pitcher rotation happens exactly when user specified
    const pitchers = lineup.map(inning => inning.positions.pitcher);
    
    console.log(`\n🎯 User-specified pitcher rotation (every 2 innings): ${pitchers.join(' → ')}`);
    
    // Innings 1-2: Same pitcher
    expect(pitchers[0]).toBe(pitchers[1]);
    
    // Innings 3-4: Different pitcher (rotation at inning 3)
    expect(pitchers[2]).toBe(pitchers[3]);
    expect(pitchers[0]).not.toBe(pitchers[2]);
    
    // Innings 5-6: Different pitcher again (rotation at inning 5)
    expect(pitchers[4]).toBe(pitchers[5]);
    expect(pitchers[2]).not.toBe(pitchers[4]);
  });

  test('should use depth chart when rotation occurs', () => {
    const depthChartRules: LineupRules = {
      respectPositionLockouts: true,
      limitBenchTime: true,
      maxConsecutiveBenchInnings: 3,
      allowPitcherRotation: true,
      allowCatcherRotation: true,
      equalPlayingTime: false,
      rotateLineupEvery: 3,
      rotatePitcherEvery: 2,     // Force pitcher rotation
      competitiveMode: true,
      competitiveMinPlayingTime: 40,
      keyPositions: ['pitcher', 'catcher', 'shortstop'],
      starPlayerRotationDelay: 1,
      _randomSeed: 12345
    };

    const lineup = generateCompleteLineup(ROTATION_TEST_PLAYERS, 6, depthChartRules);
    
    const pitchers = lineup.map(inning => inning.positions.pitcher);
    console.log(`\n🎯 Depth chart usage: ${pitchers.join(' → ')}`);
    
    // Should rotate through the depth chart:
    // Alex (5-rated, star) → Ryan (4-rated) or other eligible pitchers
    expect(pitchers[0]).toBe('Alex Thompson');  // Best pitcher starts
    expect(pitchers[2]).not.toBe('Alex Thompson'); // Rotation happened
    
    // Verify the new pitcher is from the eligible depth chart
    const rotatedPitcher = pitchers[2];
    const eligiblePitchers = ROTATION_TEST_PLAYERS
      .filter(p => !p.positionRestrictions?.pitcher) // Can pitch
      .map(p => p.name);
    
    expect(eligiblePitchers).toContain(rotatedPitcher);
  });

  test('should handle aggressive rotation schedule', () => {
    const aggressiveRules: LineupRules = {
      respectPositionLockouts: true,
      limitBenchTime: true,
      maxConsecutiveBenchInnings: 1,  // Very aggressive bench limits
      allowPitcherRotation: true,
      allowCatcherRotation: true,
      equalPlayingTime: false,
      rotateLineupEvery: 1,      // Rotate every inning
      rotatePitcherEvery: 1,     // New pitcher every inning
      competitiveMode: true,
      competitiveMinPlayingTime: 30,
      keyPositions: ['pitcher', 'catcher'],
      starPlayerRotationDelay: 0, // No delay
      _randomSeed: 12345
    };

    // This should work without errors even with very aggressive rotation
    const lineup = generateCompleteLineup(ROTATION_TEST_PLAYERS, 4, aggressiveRules);
    
    expect(lineup).toHaveLength(4);
    
    // Every inning should have different lineups
    const pitchers = lineup.map(inning => inning.positions.pitcher);
    console.log(`\n🎯 Aggressive rotation: ${pitchers.join(' → ')}`);
    
    // With rotation every inning, pitchers should change
    expect(pitchers[0]).not.toBe(pitchers[1]);
    expect(pitchers[1]).not.toBe(pitchers[2]);
    expect(pitchers[2]).not.toBe(pitchers[3]);
  });
});
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import SignUp from '@/pages/SignUp';
import SignIn from '@/pages/SignIn';
import DemoLogin from '@/pages/DemoLogin';
import AdminLogin from '@/pages/AdminLogin';
import { AuthProvider } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import * as authUtils from '@/utils/authUtils';

// Mock dependencies
vi.mock('sonner', () => ({
  toast: {
    error: vi.fn(),
    success: vi.fn(),
    warning: vi.fn(),
  },
}));

vi.mock('@/contexts/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
  useAuth: () => ({
    user: null,
    signUp: vi.fn(),
    signIn: vi.fn(),
    signOut: vi.fn(),
    loading: false,
    isPaid: false,
    checkPaymentStatus: vi.fn(),
  }),
}));

vi.mock('@/services/teamService', () => ({
  initializeDemoData: vi.fn().mockResolvedValue({}),
}));

vi.mock('@/supabaseClient', () => ({
  supabase: {
    auth: {
      signInWithPassword: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
      resetPasswordForEmail: vi.fn(),
      onAuthStateChange: vi.fn(() => ({ data: { subscription: { unsubscribe: vi.fn() } } })),
      getSession: vi.fn().mockResolvedValue({ data: { session: null } }),
    },
    from: vi.fn(() => ({
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      upsert: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: null, error: null }),
    })),
  },
}));

const renderWithRouter = (component: React.ReactNode) => {
  return render(component as React.ReactElement);
};

describe('Authentication Flow Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorage.clear();
    sessionStorage.clear();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('SignUp Component', () => {
    it('should render signup form correctly', () => {
      renderWithRouter(<SignUp />);
      
      expect(screen.getByText('Join Dugout Boss - $49')).toBeInTheDocument();
      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /join now/i })).toBeInTheDocument();
    });

    it('should validate password confirmation', async () => {
      const mockSignUp = vi.fn();
      vi.mocked(require('@/contexts/AuthContext').useAuth).mockReturnValue({
        signUp: mockSignUp,
        user: null,
        loading: false,
        isPaid: false,
      });

      renderWithRouter(<SignUp />);
      
      const emailInput = screen.getByLabelText(/email address/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /join now/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'different' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Passwords do not match');
      });
      expect(mockSignUp).not.toHaveBeenCalled();
    });

    it('should validate minimum password length', async () => {
      const mockSignUp = vi.fn();
      vi.mocked(require('@/contexts/AuthContext').useAuth).mockReturnValue({
        signUp: mockSignUp,
        user: null,
        loading: false,
        isPaid: false,
      });

      renderWithRouter(<SignUp />);
      
      const emailInput = screen.getByLabelText(/email address/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /join now/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: '123' } });
      fireEvent.change(confirmPasswordInput, { target: { value: '123' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('Password must be at least 6 characters long');
      });
      expect(mockSignUp).not.toHaveBeenCalled();
    });

    it('should handle successful signup', async () => {
      const mockSignUp = vi.fn().mockResolvedValue({ data: {}, error: null });
      vi.mocked(require('@/contexts/AuthContext').useAuth).mockReturnValue({
        signUp: mockSignUp,
        user: null,
        loading: false,
        isPaid: false,
      });

      renderWithRouter(<SignUp />);
      
      const emailInput = screen.getByLabelText(/email address/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /join now/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'password123' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockSignUp).toHaveBeenCalledWith('<EMAIL>', 'password123');
        expect(toast.success).toHaveBeenCalledWith('Registration successful! Please check your email for verification.');
      });
    });

    it('should handle signup errors', async () => {
      const mockSignUp = vi.fn().mockResolvedValue({ 
        error: { message: 'User already registered' } 
      });
      vi.mocked(require('@/contexts/AuthContext').useAuth).mockReturnValue({
        signUp: mockSignUp,
        user: null,
        loading: false,
        isPaid: false,
      });

      renderWithRouter(<SignUp />);
      
      const emailInput = screen.getByLabelText(/email address/i);
      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /join now/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'password123' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith('This email is already registered. Please try signing in instead.');
      });
    });
  });

  describe('SignIn Component', () => {
    it('should render signin form correctly', () => {
      renderWithRouter(<SignIn />);
      
      expect(screen.getByText('Sign In')).toBeInTheDocument();
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
      expect(screen.getByText(/forgot your password/i)).toBeInTheDocument();
    });

    it('should handle admin account detection', async () => {
      const mockSignInWithPassword = vi.fn().mockResolvedValue({ error: null });
      const mockSetAdminAccountFlags = vi.spyOn(authUtils, 'setAdminAccountFlags');
      
      vi.mocked(require('@/supabaseClient').supabase.auth.signInWithPassword).mockImplementation(mockSignInWithPassword);

      renderWithRouter(<SignIn />);
      
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockSetAdminAccountFlags).toHaveBeenCalled();
        expect(mockSignInWithPassword).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password',
        });
      });
    });

    it('should handle demo account detection', async () => {
      const mockSignInWithPassword = vi.fn().mockResolvedValue({ error: null });
      const mockSetDemoAccountFlags = vi.spyOn(authUtils, 'setDemoAccountFlags');
      
      vi.mocked(require('@/supabaseClient').supabase.auth.signInWithPassword).mockImplementation(mockSignInWithPassword);

      renderWithRouter(<SignIn />);
      
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockSetDemoAccountFlags).toHaveBeenCalled();
        expect(mockSignInWithPassword).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password',
        });
      });
    });

    it('should show forgot password form when clicked', () => {
      renderWithRouter(<SignIn />);
      
      const forgotPasswordLink = screen.getByText(/forgot your password/i);
      fireEvent.click(forgotPasswordLink);

      expect(screen.getByText(/send reset email/i)).toBeInTheDocument();
      expect(screen.getByText(/enter your email address and we'll send you a link/i)).toBeInTheDocument();
    });

    it('should handle password reset', async () => {
      const mockResetPasswordForEmail = vi.fn().mockResolvedValue({ error: null });
      vi.mocked(require('@/supabaseClient').supabase.auth.resetPasswordForEmail).mockImplementation(mockResetPasswordForEmail);

      renderWithRouter(<SignIn />);
      
      const forgotPasswordLink = screen.getByText(/forgot your password/i);
      fireEvent.click(forgotPasswordLink);

      const resetEmailInput = screen.getByLabelText(/email address/i);
      const sendResetButton = screen.getByRole('button', { name: /send reset email/i });

      fireEvent.change(resetEmailInput, { target: { value: '<EMAIL>' } });
      fireEvent.click(sendResetButton);

      await waitFor(() => {
        expect(mockResetPasswordForEmail).toHaveBeenCalledWith('<EMAIL>', {
          redirectTo: expect.stringContaining('/reset-password'),
        });
        expect(toast.success).toHaveBeenCalledWith('Password reset email sent! Check your inbox.');
      });
    });
  });

  describe('DemoLogin Component', () => {
    it('should initialize demo mode correctly', async () => {
      const mockInitializeDemoData = vi.fn().mockResolvedValue({});
      vi.mocked(require('@/services/teamService').initializeDemoData).mockImplementation(mockInitializeDemoData);

      // Mock window.location.href
      delete (window as any).location;
      (window as any).location = { href: '' };

      renderWithRouter(<DemoLogin />);

      await waitFor(() => {
        expect(screen.getByText(/successfully logged in with demo account/i)).toBeInTheDocument();
      });

      // Check that demo mode flags are set
      expect(localStorage.getItem('demo_mode')).toBe('true');
      expect(localStorage.getItem('demo_user_email')).toBe('<EMAIL>');
      expect(localStorage.getItem('demo_user_id')).toBe('212e5e06-9dd0-4d56-89d2-69915b205b53');
      expect(localStorage.getItem('current_team_id')).toBe('83bd9832-f5db-4c7d-b234-41fd38f90007');
    });

    it('should handle demo data initialization failure gracefully', async () => {
      const mockInitializeDemoData = vi.fn().mockRejectedValue(new Error('Demo data error'));
      vi.mocked(require('@/services/teamService').initializeDemoData).mockImplementation(mockInitializeDemoData);

      // Mock console.warn to avoid noise in tests
      const originalWarn = console.warn;
      console.warn = vi.fn();

      renderWithRouter(<DemoLogin />);

      await waitFor(() => {
        expect(console.warn).toHaveBeenCalledWith(
          'Demo data initialization failed, but continuing with demo mode:',
          expect.any(Error)
        );
      });

      console.warn = originalWarn;
    });
  });

  describe('AdminLogin Component', () => {
    it('should render admin login form correctly', () => {
      renderWithRouter(<AdminLogin />);
      
      expect(screen.getByText('Admin Login')).toBeInTheDocument();
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /sign in as admin/i })).toBeInTheDocument();
    });

    it('should handle admin login with profile and subscription creation', async () => {
      const mockSignOut = vi.fn().mockResolvedValue({ error: null });
      const mockSignInWithPassword = vi.fn().mockResolvedValue({ 
        data: { user: { id: 'admin-user-id', email: '<EMAIL>' } }, 
        error: null 
      });
      const mockUpsert = vi.fn().mockResolvedValue({ error: null });

      vi.mocked(require('@/supabaseClient').supabase.auth.signOut).mockImplementation(mockSignOut);
      vi.mocked(require('@/supabaseClient').supabase.auth.signInWithPassword).mockImplementation(mockSignInWithPassword);
      vi.mocked(require('@/supabaseClient').supabase.from).mockReturnValue({
        upsert: mockUpsert,
      });

      renderWithRouter(<AdminLogin />);
      
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /sign in as admin/i });

      fireEvent.change(passwordInput, { target: { value: 'adminpassword' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockSignOut).toHaveBeenCalled();
        expect(mockSignInWithPassword).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'adminpassword',
        });
      });

      // Check that admin flags are set
      expect(localStorage.getItem('is_noah_real_account')).toBe('true');
      expect(localStorage.getItem('is_admin')).toBe('true');
    });
  });

  describe('Auth Utilities', () => {
    beforeEach(() => {
      localStorage.clear();
    });

    it('should detect demo accounts correctly', () => {
      expect(authUtils.isDemoAccount('<EMAIL>')).toBe(true);
      expect(authUtils.isDemoAccount('<EMAIL>')).toBe(true);
      expect(authUtils.isDemoAccount('<EMAIL>')).toBe(false);
      
      // Test localStorage fallback
      localStorage.setItem('demo_mode', 'true');
      expect(authUtils.isDemoAccount()).toBe(true);
    });

    it('should detect admin accounts correctly', () => {
      expect(authUtils.isAdminAccount('<EMAIL>')).toBe(true);
      expect(authUtils.isAdminAccount('<EMAIL>')).toBe(true);
      expect(authUtils.isAdminAccount('<EMAIL>')).toBe(false);
      
      // Test localStorage flag
      localStorage.setItem('is_admin_account', 'true');
      expect(authUtils.isAdminAccount('<EMAIL>')).toBe(true);
    });

    it('should determine account type correctly', () => {
      expect(authUtils.getAccountType('<EMAIL>')).toBe('demo');
      expect(authUtils.getAccountType('<EMAIL>')).toBe('admin');
      expect(authUtils.getAccountType('<EMAIL>')).toBe('regular');
    });

    it('should set demo mode correctly', () => {
      authUtils.setDemoMode();
      
      expect(localStorage.getItem('email')).toBe('<EMAIL>');
      expect(localStorage.getItem('teamname')).toBe('Demo Softball Team');
      expect(localStorage.getItem('demo_mode')).toBe('true');
      expect(localStorage.getItem('current_team_id')).toBe('83bd9832-f5db-4c7d-b234-41fd38f90007');
    });

    it('should clear user session correctly', () => {
      localStorage.setItem('email', '<EMAIL>');
      localStorage.setItem('teamname', 'Test Team');
      localStorage.setItem('demo_mode', 'true');
      
      authUtils.clearUserSession();
      
      expect(localStorage.getItem('email')).toBeNull();
      expect(localStorage.getItem('teamname')).toBeNull();
      expect(localStorage.getItem('demo_mode')).toBeNull();
    });
  });

  describe('Security Edge Cases', () => {
    it('should prevent demo mode bypass in admin routes', () => {
      localStorage.setItem('demo_mode', 'true');
      localStorage.setItem('is_admin', 'true'); // Conflicting flags
      
      // Demo accounts should never be admin
      expect(authUtils.isDemoAccount()).toBe(true);
      expect(authUtils.getAccountType()).toBe('demo'); // Should prioritize demo over admin
    });

    it('should handle malformed email inputs', () => {
      expect(authUtils.isDemoAccount('')).toBe(false);
      expect(authUtils.isDemoAccount(null)).toBe(false);
      expect(authUtils.isDemoAccount(undefined)).toBe(false);
      
      expect(authUtils.isAdminAccount('')).toBe(false);
      expect(authUtils.isAdminAccount(null)).toBe(false);
      expect(authUtils.isAdminAccount(undefined)).toBe(false);
    });

    it('should clear session storage on user session clear', () => {
      sessionStorage.setItem('test_key', 'test_value');
      authUtils.clearUserSession();
      expect(sessionStorage.getItem('test_key')).toBeNull();
    });
  });
});
import { describe, it, expect } from 'vitest';
import { generateCompleteLineup } from '@/lib/utils-enhanced';
import { Player } from '@/contexts/TeamContext';

describe('User-Reported Issues from Real Usage', () => {
  // Recreate the exact roster from the screenshot
  const createScreenshotRoster = (): Player[] => {
    return [
      { id: '1', name: '<PERSON>', teamRoles: {} },
      { id: '2', name: '<PERSON><PERSON>', teamRoles: {} },
      { id: '3', name: '<PERSON>', teamRoles: {} },
      { id: '4', name: '<PERSON><PERSON><PERSON>', teamRoles: {} },
      { id: '5', name: '<PERSON>', teamRoles: {} },
      { id: '6', name: '<PERSON>', teamRoles: {} },
      { id: '7', name: '<PERSON>', teamRoles: {} },
      { id: '8', name: 'Player8', teamRoles: {} },
      { id: '9', name: '<PERSON><PERSON>', teamRoles: {} },
      { id: '10', name: '<PERSON><PERSON>', teamRoles: {} },
      { id: '11', name: '<PERSON><PERSON>', teamRoles: {} },
      { id: '12', name: '<PERSON><PERSON>', teamRoles: {} }
    ];
  };

  it('should NOT allow players to play all 20 innings with rotateLineupEvery=1', () => {
    const players = createScreenshotRoster();
    const totalInnings = 20; // From the screenshot
    
    const innings = generateCompleteLineup(players, totalInnings, {
      rotateLineupEvery: 1, // Should force rotation every inning
      rotatePitcherEvery: 2,
      equalPlayingTime: true,
      limitBenchTime: true,
      maxConsecutiveBenchInnings: 2
    });

    // Track playing time
    const playingTime: Record<string, number> = {};
    const benchTime: Record<string, number> = {};
    
    players.forEach(p => {
      playingTime[p.name] = 0;
      benchTime[p.name] = 0;
    });

    innings.forEach(inning => {
      Object.entries(inning.positions).forEach(([pos, playerName]) => {
        if (pos !== 'bench' && playerName) {
          playingTime[playerName]++;
        }
      });
      
      if (inning.positions.bench) {
        inning.positions.bench.forEach((playerName: string) => {
          benchTime[playerName]++;
        });
      }
    });

    console.log('\n=== FINAL PLAYING TIME RESULTS ===');
    
    // Sort by playing time for better visualization
    const sorted = Object.entries(playingTime).sort(([,a], [,b]) => b - a);
    sorted.forEach(([name, time]) => {
      const bench = benchTime[name];
      console.log(`${name.padEnd(12)} Field: ${time} innings, Bench: ${bench} innings`);
    });

    // NO PLAYER should play all 20 innings
    const maxPlayingTime = Math.max(...Object.values(playingTime));
    const minPlayingTime = Math.min(...Object.values(playingTime));
    const range = maxPlayingTime - minPlayingTime;
    
    console.log('\n=== BALANCE ANALYSIS ===');
    console.log(`Max playing time: ${maxPlayingTime} innings`);
    console.log(`Min playing time: ${minPlayingTime} innings`);
    console.log(`Range: ${range} innings`);
    console.log(`Ideal per player: ${(20 * 9 / 12).toFixed(1)} innings`);
    
    console.log('\n=== COMPARISON TO SCREENSHOT ISSUE ===');
    console.log('Screenshot showed: Finn/Evelynn/Avery 20 innings, others 11-12 (range: 9)');
    console.log(`Current result: Range of ${range} innings`);
    
    if (maxPlayingTime === 20) {
      console.log('❌ STILL FAILING: Some players playing all 20 innings!');
    } else if (range <= 3) {
      console.log('✅ EXCELLENT: Much better balance achieved!');
    } else if (range <= 5) {
      console.log('✅ GOOD: Reasonable balance, big improvement!');
    } else {
      console.log('⚠️  PARTIAL: Better than before but needs more work');
    }

    // With 12 players and 20 innings, ideal is ~15 innings per player
    expect(maxPlayingTime).toBeLessThan(20); // No one plays all innings
    expect(minPlayingTime).toBeGreaterThan(10); // No one sits too much
    expect(maxPlayingTime - minPlayingTime).toBeLessThanOrEqual(5); // Reasonable range
  });

  it('should detect and fix the 0% balance score issue', () => {
    // The exact scenario from the screenshot
    const playingTimes = [
      20, 20, 20, // Finn, Evelynn, Avery (never benched)
      12, 12, 12, // Mikayla, Bella, Presley
      11, 11, 11, 11, 11 // Others (assuming 11 innings for remaining players)
    ];

    // This distribution is terrible and should NOT show 100% or even 50%
    const avgTime = playingTimes.reduce((a, b) => a + b) / playingTimes.length;
    const range = Math.max(...playingTimes) - Math.min(...playingTimes);
    
    console.log(`Average playing time: ${avgTime}`);
    console.log(`Range: ${range}`);
    console.log(`Players at 20 innings: ${playingTimes.filter(t => t === 20).length}`);
    console.log(`Players at 11-12 innings: ${playingTimes.filter(t => t <= 12).length}`);

    // This is a 9-inning spread which is terrible
    expect(range).toBe(9);
    
    // The algorithm should prevent this from happening
    expect(playingTimes.filter(t => t === 20).length).toBe(0); // No one should play all innings
  });

  it('should enforce rotation when rotateLineupEvery=1', () => {
    const players = createScreenshotRoster().slice(0, 10); // 10 players for clearer test
    const innings = generateCompleteLineup(players, 5, {
      rotateLineupEvery: 1,
      equalPlayingTime: true
    });

    // Check that players actually rotate between innings
    for (let i = 1; i < innings.length; i++) {
      const prevFieldPlayers = new Set<string>();
      const currFieldPlayers = new Set<string>();
      
      Object.entries(innings[i-1].positions).forEach(([pos, player]) => {
        if (pos !== 'bench' && player) prevFieldPlayers.add(player);
      });
      
      Object.entries(innings[i].positions).forEach(([pos, player]) => {
        if (pos !== 'bench' && player) currFieldPlayers.add(player);
      });

      // Count how many players changed
      let changedPlayers = 0;
      prevFieldPlayers.forEach(player => {
        if (!currFieldPlayers.has(player)) changedPlayers++;
      });

      console.log(`Inning ${i-1} to ${i}: ${changedPlayers} players rotated out`);
      
      // With rotateLineupEvery=1, we should see rotation
      expect(changedPlayers).toBeGreaterThan(0);
    }
  });

  it('should handle competitive mode without breaking balance completely', () => {
    const players = createScreenshotRoster();
    // Mark some as star players
    players[0].isStarPlayer = true; // Finn
    players[1].isStarPlayer = true; // Evelynn
    players[0].positionRatings = { pitcher: 5, shortstop: 4 };
    players[1].positionRatings = { catcher: 5, firstBase: 4 };

    const innings = generateCompleteLineup(players, 14, {
      competitiveMode: true,
      competitiveMinPlayingTime: 50, // Everyone plays at least 50%
      rotateLineupEvery: 2,
      equalPlayingTime: false // Competitive mode
    });

    const playingTime: Record<string, number> = {};
    players.forEach(p => playingTime[p.name] = 0);

    innings.forEach(inning => {
      Object.entries(inning.positions).forEach(([pos, name]) => {
        if (pos !== 'bench' && name && typeof name === 'string') {
          playingTime[name]++;
        }
      });
    });

    console.log('Competitive mode playing time:', playingTime);

    // Even in competitive mode, no one should sit for less than 50%
    const minTime = Math.min(...Object.values(playingTime));
    expect(minTime).toBeGreaterThanOrEqual(7); // 50% of 14 innings
  });
});
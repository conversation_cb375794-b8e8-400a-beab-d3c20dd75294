import {
  generateCompleteLineup,
  validateLineup,
  LineupRules,
  InsufficientPlayersError
} from '../lib/utils-enhanced';
import { Player, InningLineup, PlayerRating } from '../contexts/TeamContext';

// Mock the supabase client
jest.mock('../integrations/supabase/client', () => ({
  supabase: {
    auth: {
      getUser: jest.fn().mockResolvedValue({ data: { user: null }, error: null })
    },
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({ data: null, error: null })
    }))
  }
}));

// Test data - <PERSON>'s U15 Selects team with realistic position restrictions and ratings
const NOAH_SELECTS_PLAYERS: Player[] = [
  {
    id: '1',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: false,    // NOT restricted from pitcher (can play)
      catcher: true,     // restricted from catcher
      firstBase: true,   // restricted from first base
      other: null
    },
    positionRatings: { pitcher: 5, firstBase: 3, leftField: 2 },
    isStarPlayer: true
  },
  {
    id: '2',
    name: 'Ben Rodriguez',
    positionRestrictions: {
      pitcher: true,     // restricted from pitcher
      catcher: false,    // NOT restricted from catcher (can play)
      firstBase: true,   // restricted from first base
      other: null
    },
    positionRatings: { catcher: 5, thirdBase: 4, rightField: 3 },
    isStarPlayer: true
  },
  {
    id: '3',
    name: 'Charlie Kim',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { shortstop: 5, secondBase: 4, centerField: 3 },
    isStarPlayer: true
  },
  {
    id: '4',
    name: 'David Chen',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { firstBase: 4, thirdBase: 3, leftField: 3 }
  },
  {
    id: '5',
    name: 'Emma Wilson',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { secondBase: 4, shortstop: 3, rightField: 3 }
  },
  {
    id: '6',
    name: 'Frank Miller',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { thirdBase: 4, firstBase: 3, centerField: 2 }
  },
  {
    id: '7',
    name: 'Grace Lee',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { leftField: 4, centerField: 4, rightField: 3 }
  },
  {
    id: '8',
    name: 'Henry Davis',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { centerField: 5, leftField: 4, rightField: 4 },
    isStarPlayer: true
  },
  {
    id: '9',
    name: 'Ivy Johnson',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { rightField: 4, centerField: 3, firstBase: 2 }
  },
  {
    id: '10',
    name: 'Jack Brown',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { pitcher: 3, firstBase: 4, leftField: 3 }
  },
  {
    id: '11',
    name: 'Kelly White',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { catcher: 3, thirdBase: 4, secondBase: 3 }
  },
  {
    id: '12',
    name: 'Liam Garcia',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { shortstop: 3, secondBase: 4, centerField: 3 }
  },
  {
    id: '13',
    name: 'Maya Patel',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { leftField: 3, rightField: 3, firstBase: 2 }
  },
  {
    id: '14',
    name: 'Noah Fleming',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { pitcher: 2, catcher: 2, firstBase: 3 }
  }
];

// House league players (no restrictions, equal ratings)
const HOUSE_LEAGUE_PLAYERS: Player[] = Array.from({ length: 15 }, (_, i) => ({
  id: `hl${i + 1}`,
  name: `Player ${i + 1}`,
  positionRestrictions: {
    pitcher: false,
    catcher: false,
    firstBase: false,
    other: null
  },
  positionRatings: {}
}));

// Default competitive rules
const COMPETITIVE_RULES: LineupRules = {
  respectPositionLockouts: true,
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 2,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  equalPlayingTime: false,
  rotateLineupEvery: 2,
  rotatePitcherEvery: 3,
  competitiveMode: true,
  competitiveMinPlayingTime: 0.4,
  keyPositions: ['pitcher', 'catcher', 'shortstop'],
  starPlayerRotationDelay: 1,
  _randomSeed: 12345
};

// Default house league rules
const HOUSE_LEAGUE_RULES: LineupRules = {
  respectPositionLockouts: true,
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 2,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  equalPlayingTime: true,
  rotateLineupEvery: 1,
  rotatePitcherEvery: 2,
  competitiveMode: false,
  _randomSeed: 12345
};

describe('Lineup Generation Algorithm', () => {
  beforeEach(() => {
    // Reset any global state
    jest.clearAllMocks();
  });

  describe('Basic Functionality', () => {
    test('should generate complete lineup for minimum players (8)', () => {
      const players = HOUSE_LEAGUE_PLAYERS.slice(0, 8);
      const innings = 5;
      
      const lineup = generateCompleteLineup(players, innings, HOUSE_LEAGUE_RULES);
      
      expect(lineup).toHaveLength(innings);
      expect(lineup[0].inning).toBe(1);
      expect(lineup[innings - 1].inning).toBe(innings);
      
      // Validate each inning has 8 field positions filled
      lineup.forEach((inning, index) => {
        const fieldPositions = Object.keys(inning.positions).filter(pos => pos !== 'bench');
        expect(fieldPositions).toHaveLength(8);
        expect(inning.inning).toBe(index + 1);
      });
    });

    test('should handle 12 players with bench rotation', () => {
      const players = HOUSE_LEAGUE_PLAYERS.slice(0, 12);
      const innings = 6;
      
      const lineup = generateCompleteLineup(players, innings, HOUSE_LEAGUE_RULES);
      
      expect(lineup).toHaveLength(innings);
      
      // Each inning should have 9 field positions + 3 bench
      lineup.forEach(inning => {
        const fieldPositions = Object.keys(inning.positions).filter(pos => pos !== 'bench');
        const benchPlayers = inning.positions.bench || [];
        expect(fieldPositions).toHaveLength(9);
        expect(benchPlayers).toHaveLength(3);
      });
    });

    test('should throw error for insufficient players', () => {
      const players = HOUSE_LEAGUE_PLAYERS.slice(0, 7); // Only 7 players
      
      expect(() => {
        generateCompleteLineup(players, 5, HOUSE_LEAGUE_RULES);
      }).toThrow(InsufficientPlayersError);
    });
  });

  describe('Competitive Mode', () => {
    test('should respect star player positions', () => {
      const lineup = generateCompleteLineup(NOAH_SELECTS_PLAYERS, 7, COMPETITIVE_RULES);
      
      // Check that star players are in their star positions in early innings
      const firstInning = lineup[0];
      
      // Alex Thompson (star pitcher) should be pitching
      expect(firstInning.positions.pitcher).toBe('Alex Thompson');
      
      // Ben Rodriguez (star catcher) should be catching
      expect(firstInning.positions.catcher).toBe('Ben Rodriguez');
      
      // Charlie Kim (star shortstop) should be at shortstop
      expect(firstInning.positions.shortstop).toBe('Charlie Kim');
    });

    test('should handle position restrictions properly', () => {
      const lineup = generateCompleteLineup(NOAH_SELECTS_PLAYERS, 5, COMPETITIVE_RULES);
      
      lineup.forEach((inning, index) => {
        // Alex Thompson should only be at pitcher (restricted)
        const alexPosition = Object.entries(inning.positions).find(([pos, player]) => 
          player === 'Alex Thompson' && pos !== 'bench'
        );
        if (alexPosition) {
          expect(alexPosition[0]).toBe('pitcher');
        }
        
        // Ben Rodriguez should only be at catcher (restricted)
        const benPosition = Object.entries(inning.positions).find(([pos, player]) => 
          player === 'Ben Rodriguez' && pos !== 'bench'
        );
        if (benPosition) {
          expect(benPosition[0]).toBe('catcher');
        }
      });
    });
  });

  describe('Rotation Rules', () => {
    test('should enforce pitcher rotation frequency', () => {
      const rules = { ...COMPETITIVE_RULES, rotatePitcherEvery: 2 };
      const lineup = generateCompleteLineup(NOAH_SELECTS_PLAYERS, 6, rules);

      // Track pitcher changes
      const pitchers = lineup.map(inning => inning.positions.pitcher);

      // Pitcher should change every 2 innings (innings 1-2: same, 3-4: same, 5-6: same)
      expect(pitchers[0]).toBe(pitchers[1]); // Innings 1-2
      expect(pitchers[2]).toBe(pitchers[3]); // Innings 3-4
      expect(pitchers[4]).toBe(pitchers[5]); // Innings 5-6

      // But should be different between rotation periods
      expect(pitchers[0]).not.toBe(pitchers[2]);
    });

    test('should enforce bench streak limits', () => {
      const rules = { ...HOUSE_LEAGUE_RULES, maxConsecutiveBenchInnings: 2 };
      const players = HOUSE_LEAGUE_PLAYERS.slice(0, 11); // 11 players, 2 on bench each inning
      const lineup = generateCompleteLineup(players, 8, rules);

      // Track each player's bench streaks
      const playerBenchStreaks = new Map<string, number>();

      lineup.forEach((inning, inningIndex) => {
        const benchPlayers = inning.positions.bench || [];

        // Reset streaks for players not on bench
        players.forEach(player => {
          if (!benchPlayers.includes(player.name)) {
            playerBenchStreaks.set(player.name, 0);
          }
        });

        // Increment streaks for benched players
        benchPlayers.forEach(playerName => {
          const currentStreak = playerBenchStreaks.get(playerName) || 0;
          playerBenchStreaks.set(playerName, currentStreak + 1);

          // No player should exceed max consecutive bench innings
          expect(currentStreak + 1).toBeLessThanOrEqual(rules.maxConsecutiveBenchInnings!);
        });
      });
    });

    test('should enforce lineup rotation frequency', () => {
      const rules = { ...HOUSE_LEAGUE_RULES, rotateLineupEvery: 3 };
      const players = HOUSE_LEAGUE_PLAYERS.slice(0, 9); // Exactly 9 players
      const lineup = generateCompleteLineup(players, 9, rules);

      // Check that significant rotation happens every 3 innings
      const inning1Positions = lineup[0].positions;
      const inning4Positions = lineup[3].positions;
      const inning7Positions = lineup[6].positions;

      // Count how many players changed positions between rotation periods
      let changesAfter3 = 0;
      let changesAfter6 = 0;

      Object.keys(inning1Positions).forEach(pos => {
        if (pos !== 'bench' && inning1Positions[pos] !== inning4Positions[pos]) {
          changesAfter3++;
        }
        if (pos !== 'bench' && inning4Positions[pos] !== inning7Positions[pos]) {
          changesAfter6++;
        }
      });

      // Should have significant rotation (at least 3 position changes)
      expect(changesAfter3).toBeGreaterThanOrEqual(3);
      expect(changesAfter6).toBeGreaterThanOrEqual(3);
    });
  });

  describe('Equal Playing Time', () => {
    test('should distribute playing time fairly in house league mode', () => {
      const players = HOUSE_LEAGUE_PLAYERS.slice(0, 10);
      const innings = 8;
      const lineup = generateCompleteLineup(players, innings, HOUSE_LEAGUE_RULES);

      // Count field innings for each player
      const playingTime = new Map<string, number>();
      players.forEach(player => playingTime.set(player.name, 0));

      lineup.forEach(inning => {
        Object.entries(inning.positions).forEach(([pos, player]) => {
          if (pos !== 'bench' && typeof player === 'string') {
            playingTime.set(player, (playingTime.get(player) || 0) + 1);
          }
        });
      });

      const playingTimes = Array.from(playingTime.values());
      const minTime = Math.min(...playingTimes);
      const maxTime = Math.max(...playingTimes);

      // In equal playing time mode, difference should be minimal (≤ 1 inning)
      expect(maxTime - minTime).toBeLessThanOrEqual(1);
    });

    test('should allow unequal playing time in competitive mode', () => {
      const lineup = generateCompleteLineup(NOAH_SELECTS_PLAYERS, 7, COMPETITIVE_RULES);

      // Count field innings for each player
      const playingTime = new Map<string, number>();
      NOAH_SELECTS_PLAYERS.forEach(player => playingTime.set(player.name, 0));

      lineup.forEach(inning => {
        Object.entries(inning.positions).forEach(([pos, player]) => {
          if (pos !== 'bench' && typeof player === 'string') {
            playingTime.set(player, (playingTime.get(player) || 0) + 1);
          }
        });
      });

      // Star players should get more playing time
      const alexTime = playingTime.get('Alex Thompson') || 0;
      const benTime = playingTime.get('Ben Rodriguez') || 0;
      const charlieTime = playingTime.get('Charlie Kim') || 0;

      // Star players should play most/all innings
      expect(alexTime).toBeGreaterThanOrEqual(5); // Star pitcher
      expect(benTime).toBeGreaterThanOrEqual(5); // Star catcher
      expect(charlieTime).toBeGreaterThanOrEqual(5); // Star shortstop
    });
  });

  describe('Edge Cases', () => {
    test('should handle players with no position ratings', () => {
      const playersWithoutRatings: Player[] = HOUSE_LEAGUE_PLAYERS.slice(0, 9).map(p => ({
        ...p,
        positionRatings: undefined,
        isStarPlayer: false
      }));

      const lineup = generateCompleteLineup(playersWithoutRatings, 5, COMPETITIVE_RULES);

      expect(lineup).toHaveLength(5);
      lineup.forEach(inning => {
        const fieldPositions = Object.keys(inning.positions).filter(pos => pos !== 'bench');
        expect(fieldPositions).toHaveLength(9);
      });
    });

    test('should handle mixed rating systems', () => {
      const mixedPlayers: Player[] = [
        ...NOAH_SELECTS_PLAYERS.slice(0, 7), // Players with ratings
        ...HOUSE_LEAGUE_PLAYERS.slice(0, 3).map(p => ({ // Players without ratings
          ...p,
          positionRatings: undefined,
          isStarPlayer: false
        }))
      ];

      const lineup = generateCompleteLineup(mixedPlayers, 6, COMPETITIVE_RULES);

      expect(lineup).toHaveLength(6);
      // Should still respect restrictions for players who have them
      lineup.forEach(inning => {
        const alexPosition = Object.entries(inning.positions).find(([pos, player]) =>
          player === 'Alex Thompson' && pos !== 'bench'
        );
        if (alexPosition) {
          expect(alexPosition[0]).toBe('pitcher');
        }
      });
    });

    test('should handle long games (15+ innings)', () => {
      const players = NOAH_SELECTS_PLAYERS;
      const innings = 15;

      const lineup = generateCompleteLineup(players, innings, COMPETITIVE_RULES);

      expect(lineup).toHaveLength(innings);

      // Verify rotation sustainability over long games
      const playingTime = new Map<string, number>();
      players.forEach(player => playingTime.set(player.name, 0));

      lineup.forEach(inning => {
        Object.entries(inning.positions).forEach(([pos, player]) => {
          if (pos !== 'bench' && typeof player === 'string') {
            playingTime.set(player, (playingTime.get(player) || 0) + 1);
          }
        });
      });

      // Even in competitive mode, all players should get some playing time in long games
      const playingTimes = Array.from(playingTime.values());
      const minTime = Math.min(...playingTimes);
      expect(minTime).toBeGreaterThanOrEqual(Math.floor(innings * 0.3)); // At least 30% playing time
    });
  });

  describe('Validation Functions', () => {
    test('should validate lineup correctness', () => {
      const players = NOAH_SELECTS_PLAYERS.slice(0, 10);
      const lineup = generateCompleteLineup(players, 5, COMPETITIVE_RULES);

      lineup.forEach(inning => {
        const validation = validateLineup(inning, players, COMPETITIVE_RULES);
        expect(validation.valid).toBe(true);
        expect(validation.errors).toHaveLength(0);
      });
    });

    test('should detect invalid lineups', () => {
      const players = NOAH_SELECTS_PLAYERS.slice(0, 9);

      // Create an invalid lineup (player in restricted position)
      const invalidInning: InningLineup = {
        inning: 1,
        positions: {
          pitcher: 'Ben Rodriguez', // Ben is restricted from pitcher
          catcher: 'Alex Thompson', // Alex is restricted from catcher
          firstBase: 'Charlie Kim',
          secondBase: 'David Chen',
          shortstop: 'Emma Wilson',
          thirdBase: 'Frank Miller',
          leftField: 'Grace Lee',
          centerField: 'Henry Davis',
          rightField: 'Ivy Johnson',
          bench: []
        }
      };

      const validation = validateLineup(invalidInning, players, COMPETITIVE_RULES);
      expect(validation.valid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Performance and Reliability', () => {
    test('should generate lineups consistently with same seed', () => {
      const rules1 = { ...COMPETITIVE_RULES, _randomSeed: 54321 };
      const rules2 = { ...COMPETITIVE_RULES, _randomSeed: 54321 };

      const lineup1 = generateCompleteLineup(NOAH_SELECTS_PLAYERS, 5, rules1);
      const lineup2 = generateCompleteLineup(NOAH_SELECTS_PLAYERS, 5, rules2);

      // Should generate identical lineups with same seed
      expect(lineup1).toEqual(lineup2);
    });

    test('should complete generation within reasonable time', () => {
      const startTime = Date.now();

      const lineup = generateCompleteLineup(NOAH_SELECTS_PLAYERS, 10, COMPETITIVE_RULES);

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(lineup).toHaveLength(10);
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
    });

    test('should handle stress test with many players and innings', () => {
      // Create a large roster
      const largePlayers: Player[] = Array.from({ length: 20 }, (_, i) => ({
        id: `stress${i + 1}`,
        name: `Stress Player ${i + 1}`,
        positionRestrictions: i < 5 ? {
          pitcher: false,    // First 5 players can only play pitcher
          catcher: true,
          firstBase: true,
          other: null
        } : {
          pitcher: false,
          catcher: false,
          firstBase: false,
          other: null
        },
        positionRatings: {
          pitcher: (Math.floor(Math.random() * 5) + 1) as PlayerRating,
          catcher: (Math.floor(Math.random() * 5) + 1) as PlayerRating,
          firstBase: (Math.floor(Math.random() * 5) + 1) as PlayerRating
        },
        isStarPlayer: i < 3
      }));

      const lineup = generateCompleteLineup(largePlayers, 12, COMPETITIVE_RULES);

      expect(lineup).toHaveLength(12);

      // Verify all lineups are valid
      lineup.forEach(inning => {
        const validation = validateLineup(inning, largePlayers, COMPETITIVE_RULES);
        expect(validation.valid).toBe(true);
      });
    });
  });
});

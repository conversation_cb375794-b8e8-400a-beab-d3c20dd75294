import { describe, it, expect, beforeEach, vi } from 'vitest';
import { 
  generateLineupFromFirstInning, 
  ConstraintSolver, 
  PlayerEligibilityCache,
  LineupRules,
  Player,
  InningLineup,
  PositionRatings,
  SeededRandom,
  LineupRotator
} from '../lib/utils-enhanced';

// Helper function to generate a complete lineup with automatic first inning
function generateLineup(players: Player[], totalInnings: number, rules: LineupRules): InningLineup[] {
  // Create constraint solver and eligibility cache
  const eligibilityCache = new PlayerEligibilityCache();
  const random = new SeededRandom(rules._randomSeed || Date.now());
  const solver = new ConstraintSolver(players, eligibilityCache);
  
  // Generate first inning using constraint solver
  const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase',
                         'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
  
  let assignments: Map<string, string> | null = null;
  
  if (rules.competitiveMode) {
    assignments = solver.findCompetitiveAssignment(
      fieldPositions,
      { respectPositionLockouts: rules.respectPositionLockouts || true },
      rules
    );
  } else {
    assignments = solver.findValidAssignment(
      fieldPositions,
      { respectPositionLockouts: rules.respectPositionLockouts || true }
    );
  }
  
  if (!assignments) {
    throw new Error('Could not generate valid first inning assignments');
  }
  
  // Build first inning
  const firstInning: InningLineup = {
    inning: 1,
    positions: {
      pitcher: assignments.get('pitcher') || '',
      catcher: assignments.get('catcher') || '',
      firstBase: assignments.get('firstBase') || '',
      secondBase: assignments.get('secondBase') || '',
      thirdBase: assignments.get('thirdBase') || '',
      shortstop: assignments.get('shortstop') || '',
      leftField: assignments.get('leftField') || '',
      centerField: assignments.get('centerField') || '',
      rightField: assignments.get('rightField') || '',
      bench: players.map(p => p.name).filter(name => !Array.from(assignments.values()).includes(name))
    }
  };
  
  // Generate remaining innings
  return generateLineupFromFirstInning(firstInning, players, totalInnings, rules);
}

// Noah's actual U15 Selects roster data with position restrictions and ratings
const NOAH_SELECTS_ROSTER: Player[] = [
  {
    id: '1',
    name: 'Avalon',
    positionRestrictions: { pitcher: true, catcher: false, firstBase: false, other: null },
    positionRatings: { 
      catcher: 5, 
      pitcher: 3, 
      firstBase: 4, 
      secondBase: 3,
      thirdBase: 3,
      shortstop: 4,
      leftField: 2,
      centerField: 2,
      rightField: 2
    } as PositionRatings,
    isStarPlayer: true,
    positionPreferences: {},
    teamRoles: {
      catcher: 'go-to',
      firstBase: 'capable',
      secondBase: 'fill-in',
      thirdBase: 'fill-in',
      shortstop: 'capable',
      leftField: 'fill-in',
      centerField: 'fill-in',
      rightField: 'fill-in'
    }
  },
  {
    id: '2',
    name: 'Avery',
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null },
    positionRatings: {
      leftField: 3,
      centerField: 3,
      rightField: 3
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      pitcher: 'avoid',
      catcher: 'avoid',
      firstBase: 'avoid',
      leftField: 'fill-in',
      centerField: 'fill-in',
      rightField: 'fill-in'
    }
  },
  {
    id: '3',
    name: 'Tyler',
    positionRestrictions: { pitcher: false, catcher: false, firstBase: false, other: null },
    positionRatings: {
      pitcher: 5,
      firstBase: 4,
      leftField: 3,
      centerField: 3,
      rightField: 3
    } as PositionRatings,
    isStarPlayer: true,
    positionPreferences: {},
    teamRoles: {
      'pitcher': 'go-to',
      'catcher': 'avoid',
      'firstBase': 'capable',
      'secondBase': 'avoid',
      'thirdBase': 'avoid',
      'shortstop': 'avoid',
      'leftField': 'fill-in',
      'centerField': 'fill-in',
      'rightField': 'fill-in'
    }
  },
  {
    id: '4',
    name: 'Brayden',
    positionRestrictions: { pitcher: false, catcher: true, firstBase: true, other: null },
    positionRatings: {
      catcher: 4,
      thirdBase: 4,
      shortstop: 3,
      leftField: 3
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      'pitcher': 'avoid',
      'catcher': 'avoid',
      'firstBase': 'avoid',
      'secondBase': 'avoid',
      'thirdBase': 'capable',
      'shortstop': 'fill-in',
      'leftField': 'fill-in',
      'centerField': 'avoid',
      'rightField': 'avoid'
    }
  },
  {
    id: '5',
    name: 'Charlie',
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null },
    positionRatings: {
      secondBase: 4,
      shortstop: 3,
      leftField: 3,
      centerField: 3,
      rightField: 3
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      'pitcher': 'avoid',
      'catcher': 'avoid',
      'firstBase': 'avoid',
      'secondBase': 'capable',
      'thirdBase': 'avoid',
      'shortstop': 'fill-in',
      'leftField': 'fill-in',
      'centerField': 'fill-in',
      'rightField': 'fill-in'
    }
  },
  {
    id: '6',
    name: 'Cyler',
    positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: "Right field" },
    positionRatings: {
      pitcher: 4,
      firstBase: 3,
      thirdBase: 3,
      leftField: 4,
      centerField: 4
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      'pitcher': 'capable',
      'catcher': 'avoid',
      'firstBase': 'fill-in',
      'secondBase': 'avoid',
      'thirdBase': 'fill-in',
      'shortstop': 'avoid',
      'leftField': 'capable',
      'centerField': 'capable',
      'rightField': 'avoid'
    }
  },
  {
    id: '7',
    name: 'Chase',
    positionRestrictions: { pitcher: true, catcher: true, firstBase: false, other: null },
    positionRatings: {
      firstBase: 5,
      secondBase: 3,
      thirdBase: 3,
      shortstop: 3,
      leftField: 3,
      centerField: 3,
      rightField: 3
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      'pitcher': 'avoid',
      'catcher': 'avoid',
      'firstBase': 'go-to',
      'secondBase': 'fill-in',
      'thirdBase': 'fill-in',
      'shortstop': 'fill-in',
      'leftField': 'fill-in',
      'centerField': 'fill-in',
      'rightField': 'fill-in'
    }
  },
  {
    id: '8',
    name: 'Joel',
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null },
    positionRatings: {
      secondBase: 4,
      shortstop: 4,
      leftField: 3,
      centerField: 3,
      rightField: 3
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      'pitcher': 'avoid',
      'catcher': 'avoid',
      'firstBase': 'avoid',
      'secondBase': 'capable',
      'thirdBase': 'avoid',
      'shortstop': 'capable',
      'leftField': 'fill-in',
      'centerField': 'fill-in',
      'rightField': 'fill-in'
    }
  },
  {
    id: '9',
    name: 'Kellen',
    positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: null },
    positionRatings: {
      pitcher: 4,
      firstBase: 4,
      thirdBase: 3,
      leftField: 3,
      centerField: 3,
      rightField: 3
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      'pitcher': 'capable',
      'catcher': 'avoid',
      'firstBase': 'capable',
      'secondBase': 'avoid',
      'thirdBase': 'fill-in',
      'shortstop': 'avoid',
      'leftField': 'fill-in',
      'centerField': 'fill-in',
      'rightField': 'fill-in'
    }
  },
  {
    id: '10',
    name: 'Mackston',
    positionRestrictions: { pitcher: true, catcher: false, firstBase: false, other: "Middle Infield" },
    positionRatings: {
      catcher: 4,
      firstBase: 3,
      thirdBase: 4,
      leftField: 3,
      centerField: 3,
      rightField: 3
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      'pitcher': 'avoid',
      'catcher': 'capable',
      'firstBase': 'fill-in',
      'secondBase': 'avoid',
      'thirdBase': 'capable',
      'shortstop': 'avoid',
      'leftField': 'fill-in',
      'centerField': 'fill-in',
      'rightField': 'fill-in'
    }
  },
  {
    id: '11',
    name: 'Rogan',
    positionRestrictions: { pitcher: false, catcher: false, firstBase: false, other: null },
    positionRatings: {
      pitcher: 4,
      firstBase: 3,
      secondBase: 3,
      thirdBase: 3,
      shortstop: 3,
      leftField: 3,
      centerField: 3,
      rightField: 3
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      'pitcher': 'capable',
      'catcher': 'avoid',
      'firstBase': 'fill-in',
      'secondBase': 'fill-in',
      'thirdBase': 'fill-in',
      'shortstop': 'fill-in',
      'leftField': 'fill-in',
      'centerField': 'fill-in',
      'rightField': 'fill-in'
    }
  },
  {
    id: '12',
    name: 'Nick',
    positionRestrictions: { pitcher: true, catcher: true, firstBase: false, other: "3B/MI/SS/2B" },
    positionRatings: {
      firstBase: 4,
      leftField: 3,
      centerField: 3,
      rightField: 3
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      'pitcher': 'avoid',
      'catcher': 'avoid',
      'firstBase': 'capable',
      'secondBase': 'avoid',
      'thirdBase': 'avoid',
      'shortstop': 'avoid',
      'leftField': 'fill-in',
      'centerField': 'fill-in',
      'rightField': 'fill-in'
    }
  },
  {
    id: '13',
    name: 'Austin',
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null },
    positionRatings: {
      secondBase: 3,
      thirdBase: 3,
      shortstop: 3,
      leftField: 3,
      centerField: 3,
      rightField: 3
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      'pitcher': 'avoid',
      'catcher': 'avoid',
      'firstBase': 'avoid',
      'secondBase': 'fill-in',
      'thirdBase': 'fill-in',
      'shortstop': 'fill-in',
      'leftField': 'fill-in',
      'centerField': 'fill-in',
      'rightField': 'fill-in'
    }
  },
  {
    id: '14',
    name: 'Drew',
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null },
    positionRatings: {
      leftField: 3,
      centerField: 3,
      rightField: 3
    } as PositionRatings,
    isStarPlayer: false,
    positionPreferences: {},
    teamRoles: {
      'pitcher': 'avoid',
      'catcher': 'avoid',
      'firstBase': 'avoid',
      'secondBase': 'avoid',
      'thirdBase': 'avoid',
      'shortstop': 'avoid',
      'leftField': 'fill-in',
      'centerField': 'fill-in',
      'rightField': 'fill-in'
    }
  }
];

describe('Rotation Algorithm Comprehensive Tests', () => {
  describe('Competitive Mode with Noah\'s Roster', () => {
    it('should prioritize star players (Avalon, Tyler) in key positions', () => {
      const rules: LineupRules = {
        competitiveMode: true,
        competitiveMinPlayingTime: 40,
        keyPositions: ['pitcher', 'catcher', 'shortstop'],
        starPlayerRotationDelay: 2,
        respectPositionLockouts: true,
        allowPitcherRotation: true,
        allowCatcherRotation: true,
        limitBenchTime: true,
        rotateLineupEvery: 3,
        rotatePitcherEvery: 3,
        _randomSeed: 12345
      };

      const lineup = generateLineup(NOAH_SELECTS_ROSTER, 9, rules);
      
      // Verify star players get more playing time in key positions
      const avalonInnings = lineup.filter(inning => 
        inning.positions.catcher === 'Avalon' || 
        inning.positions.shortstop === 'Avalon'
      ).length;
      
      const tylerInnings = lineup.filter(inning => 
        inning.positions.pitcher === 'Tyler'
      ).length;

      expect(avalonInnings).toBeGreaterThanOrEqual(5); // Star player should play key positions majority of time
      expect(tylerInnings).toBeGreaterThanOrEqual(5); // Tyler with 5-rating at pitcher should dominate
    });

    it('should respect position lockouts even in competitive mode', () => {
      const rules: LineupRules = {
        competitiveMode: true,
        respectPositionLockouts: true,
        keyPositions: ['pitcher', 'catcher', 'shortstop'],
        _randomSeed: 12345
      };

      const lineup = generateLineup(NOAH_SELECTS_ROSTER, 9, rules);
      
      // Check all innings respect position restrictions
      lineup.forEach((inning, idx) => {
        // Avalon can't pitch
        expect(inning.positions.pitcher).not.toBe('Avalon');
        
        // Avery can't play pitcher, catcher, or first base
        if (inning.positions.pitcher === 'Avery' || 
            inning.positions.catcher === 'Avery' || 
            inning.positions.firstBase === 'Avery') {
          throw new Error(`Avery assigned to restricted position in inning ${idx + 1}`);
        }
        
        // Cyler can't play right field (other restriction)
        expect(inning.positions.rightField).not.toBe('Cyler');
        
        // Nick can't play 3B/MI/SS/2B
        const nickRestrictedPositions = ['thirdBase', 'secondBase', 'shortstop'];
        nickRestrictedPositions.forEach(pos => {
          if ((inning.positions as any)[pos] === 'Nick') {
            throw new Error(`Nick assigned to restricted position ${pos} in inning ${idx + 1}`);
          }
        });
      });
    });

    it('should use player ratings to make optimal assignments', () => {
      const rules: LineupRules = {
        competitiveMode: true,
        respectPositionLockouts: true,
        keyPositions: ['pitcher', 'catcher', 'firstBase'],
        _randomSeed: 12345
      };

      const lineup = generateLineup(NOAH_SELECTS_ROSTER, 6, rules);
      
      // Count how often high-rated players are in their best positions
      let optimalAssignments = 0;
      let totalAssignments = 0;

      lineup.forEach(inning => {
        // Avalon (5-rating at catcher) should play catcher often
        if (inning.positions.catcher === 'Avalon') optimalAssignments++;
        
        // Tyler (5-rating at pitcher) should pitch often
        if (inning.positions.pitcher === 'Tyler') optimalAssignments++;
        
        // Chase (5-rating at first base) should play first often
        if (inning.positions.firstBase === 'Chase') optimalAssignments++;
        
        totalAssignments += 3; // 3 key positions per inning
      });

      const optimalPercentage = (optimalAssignments / totalAssignments) * 100;
      expect(optimalPercentage).toBeGreaterThan(60); // Should achieve >60% optimal positioning
    });

    it('should handle star player rotation delays correctly', () => {
      const rules: LineupRules = {
        competitiveMode: true,
        keyPositions: ['pitcher', 'catcher'],
        starPlayerRotationDelay: 2, // Star players stay 2 extra innings
        rotateLineupEvery: 2,
        respectPositionLockouts: true,
        _randomSeed: 12345
      };

      const lineup = generateLineup(NOAH_SELECTS_ROSTER, 9, rules);
      
      // Track star player positions
      let avalonStreak = 0;
      let tylerStreak = 0;
      let maxAvalonStreak = 0;
      let maxTylerStreak = 0;

      lineup.forEach(inning => {
        // Track Avalon's streak at catcher
        if (inning.positions.catcher === 'Avalon') {
          avalonStreak++;
          maxAvalonStreak = Math.max(maxAvalonStreak, avalonStreak);
        } else {
          avalonStreak = 0;
        }

        // Track Tyler's streak at pitcher
        if (inning.positions.pitcher === 'Tyler') {
          tylerStreak++;
          maxTylerStreak = Math.max(maxTylerStreak, tylerStreak);
        } else {
          tylerStreak = 0;
        }
      });

      // Star players should stay in position longer than normal rotation
      expect(maxAvalonStreak).toBeGreaterThanOrEqual(3); // 2 base + delay
      expect(maxTylerStreak).toBeGreaterThanOrEqual(3);
    });
  });

  describe('Edge Cases', () => {
    it('should handle all players with equal ratings', () => {
      // Create roster where everyone has rating 3 for all positions
      const equalRoster = NOAH_SELECTS_ROSTER.map(player => ({
        ...player,
        positionRatings: {
          pitcher: 3, catcher: 3, firstBase: 3, secondBase: 3,
          thirdBase: 3, shortstop: 3, leftField: 3, centerField: 3, rightField: 3
        } as PositionRatings,
        isStarPlayer: false
      }));

      const rules: LineupRules = {
        competitiveMode: true,
        respectPositionLockouts: true,
        _randomSeed: 12345
      };

      const lineup = generateLineup(equalRoster, 9, rules);
      
      // Should still generate valid lineup
      expect(lineup).toHaveLength(9);
      
      // Check playing time is relatively balanced
      const playingTime = new Map<string, number>();
      lineup.forEach(inning => {
        Object.values(inning.positions).forEach(playerName => {
          if (typeof playerName === 'string' && !inning.positions.bench.includes(playerName)) {
            playingTime.set(playerName, (playingTime.get(playerName) || 0) + 1);
          }
        });
      });

      const times = Array.from(playingTime.values());
      const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
      const variance = times.reduce((sum, time) => sum + Math.pow(time - avgTime, 2), 0) / times.length;
      
      expect(variance).toBeLessThan(4); // Low variance = balanced playing time
    });

    it('should handle players locked out of multiple positions', () => {
      // Create a player who can only play one position
      const restrictedRoster = [...NOAH_SELECTS_ROSTER];
      restrictedRoster[0] = {
        ...restrictedRoster[0],
        name: 'Restricted Player',
        positionRestrictions: {
          pitcher: true, catcher: true, firstBase: true,
          other: 'secondBase,thirdBase,shortstop,centerField,rightField'
        },
        positionRatings: { leftField: 5 } as PositionRatings
      };

      const rules: LineupRules = {
        competitiveMode: true,
        respectPositionLockouts: true,
        _randomSeed: 12345
      };

      const lineup = generateLineup(restrictedRoster, 6, rules);
      
      // Restricted player should only play left field or bench
      lineup.forEach(inning => {
        const positions = Object.entries(inning.positions);
        positions.forEach(([pos, player]) => {
          if (player === 'Restricted Player' && pos !== 'leftField' && pos !== 'bench') {
            throw new Error(`Restricted player assigned to ${pos}`);
          }
        });
      });
    });

    it('should handle long games (15+ innings) without performance issues', () => {
      const startTime = Date.now();
      
      const rules: LineupRules = {
        competitiveMode: true,
        competitiveMinPlayingTime: 40,
        keyPositions: ['pitcher', 'catcher', 'shortstop'],
        respectPositionLockouts: true,
        rotateLineupEvery: 3,
        _randomSeed: 12345
      };

      const lineup = generateLineup(NOAH_SELECTS_ROSTER, 18, rules);
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(lineup).toHaveLength(18);
      expect(duration).toBeLessThan(5000); // Should complete in under 5 seconds

      // Verify minimum playing time is respected
      const playingTime = new Map<string, number>();
      lineup.forEach(inning => {
        Object.values(inning.positions).forEach(playerName => {
          if (typeof playerName === 'string' && !inning.positions.bench.includes(playerName)) {
            playingTime.set(playerName, (playingTime.get(playerName) || 0) + 1);
          }
        });
      });

      // Each player should play at least 40% of innings
      const minInnings = Math.floor(18 * 0.4);
      playingTime.forEach((innings, player) => {
        expect(innings).toBeGreaterThanOrEqual(minInnings);
      });
    });

    it('should handle competitive mode with pitcher rotation frequency', () => {
      const rules: LineupRules = {
        competitiveMode: true,
        keyPositions: ['pitcher'],
        allowPitcherRotation: true,
        rotatePitcherEvery: 3,
        respectPositionLockouts: true,
        _randomSeed: 12345
      };

      const lineup = generateLineup(NOAH_SELECTS_ROSTER, 9, rules);
      
      // Track pitcher changes
      let pitcherChanges = 0;
      let lastPitcher = lineup[0].positions.pitcher;

      for (let i = 1; i < lineup.length; i++) {
        if (lineup[i].positions.pitcher !== lastPitcher) {
          pitcherChanges++;
          lastPitcher = lineup[i].positions.pitcher;
        }
      }

      // With rotatePitcherEvery=3, expect ~2 changes in 9 innings
      expect(pitcherChanges).toBeGreaterThanOrEqual(1);
      expect(pitcherChanges).toBeLessThanOrEqual(3);
    });
  });

  describe('Validation of Competitive Mode Settings', () => {
    it('should ensure minimum playing time even in competitive mode', () => {
      const rules: LineupRules = {
        competitiveMode: true,
        competitiveMinPlayingTime: 50, // 50% minimum
        keyPositions: ['pitcher', 'catcher', 'shortstop'],
        respectPositionLockouts: true,
        _randomSeed: 12345
      };

      const lineup = generateLineup(NOAH_SELECTS_ROSTER, 10, rules);
      
      // Count playing time for each player
      const playingTime = new Map<string, number>();
      lineup.forEach(inning => {
        NOAH_SELECTS_ROSTER.forEach(player => {
          const isPlaying = Object.values(inning.positions).includes(player.name) &&
                           !inning.positions.bench.includes(player.name);
          if (isPlaying) {
            playingTime.set(player.name, (playingTime.get(player.name) || 0) + 1);
          }
        });
      });

      // Every player should play at least 50% of innings (5 out of 10)
      playingTime.forEach((innings, player) => {
        expect(innings).toBeGreaterThanOrEqual(5);
      });
    });

    it('should properly weight key positions in competitive mode', () => {
      const rules: LineupRules = {
        competitiveMode: true,
        keyPositions: ['pitcher', 'catcher', 'firstBase'],
        respectPositionLockouts: true,
        _randomSeed: 12345
      };

      const lineup = generateLineup(NOAH_SELECTS_ROSTER, 6, rules);
      
      // Players with high ratings in key positions should play there more
      const keyPositionAssignments = {
        pitcher: { Tyler: 0, total: 0 },
        catcher: { Avalon: 0, total: 0 },
        firstBase: { Chase: 0, total: 0 }
      };

      lineup.forEach(inning => {
        // Tyler has 5-rating at pitcher
        if (inning.positions.pitcher === 'Tyler') keyPositionAssignments.pitcher.Tyler++;
        keyPositionAssignments.pitcher.total++;

        // Avalon has 5-rating at catcher
        if (inning.positions.catcher === 'Avalon') keyPositionAssignments.catcher.Avalon++;
        keyPositionAssignments.catcher.total++;

        // Chase has 5-rating at first base
        if (inning.positions.firstBase === 'Chase') keyPositionAssignments.firstBase.Chase++;
        keyPositionAssignments.firstBase.total++;
      });

      // High-rated players should dominate their key positions
      expect(keyPositionAssignments.pitcher.Tyler / keyPositionAssignments.pitcher.total)
        .toBeGreaterThan(0.5);
      expect(keyPositionAssignments.catcher.Avalon / keyPositionAssignments.catcher.total)
        .toBeGreaterThan(0.5);
      expect(keyPositionAssignments.firstBase.Chase / keyPositionAssignments.firstBase.total)
        .toBeGreaterThan(0.5);
    });
  });
});
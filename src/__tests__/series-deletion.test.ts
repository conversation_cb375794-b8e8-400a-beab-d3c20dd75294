import { describe, it, expect, vi, beforeEach } from 'vitest';
import { supabase } from '@/supabaseClient';

// Mock Supabase
vi.mock('@/supabaseClient', () => ({
  supabase: {
    from: vi.fn(() => ({
      delete: vi.fn(() => ({
        in: vi.fn(() => Promise.resolve({ error: null }))
      }))
    }))
  }
}));

describe('Series Deletion Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should delete all games in a series with one operation', async () => {
    const mockLineupIds = ['lineup1', 'lineup2', 'lineup3', 'lineup4'];
    
    // Create mock chain
    const mockIn = vi.fn(() => Promise.resolve({ error: null }));
    const mockDelete = vi.fn(() => ({ in: mockIn }));
    const mockFrom = vi.fn(() => ({ delete: mockDelete }));
    
    (supabase.from as any) = mockFrom;

    // Simulate the deletion logic from LineupTable
    const { error } = await supabase
      .from('lineups')
      .delete()
      .in('id', mockLineupIds);

    // Verify the correct calls were made
    expect(mockFrom).toHaveBeenCalledWith('lineups');
    expect(mockDelete).toHaveBeenCalled();
    expect(mockIn).toHaveBeenCalledWith('id', mockLineupIds);
    expect(error).toBeNull();
  });

  it('should handle deletion errors gracefully', async () => {
    const mockLineupIds = ['lineup1', 'lineup2'];
    const mockError = { message: 'Database error', code: 'DB001' };
    
    // Create mock chain that returns an error
    const mockIn = vi.fn(() => Promise.resolve({ error: mockError }));
    const mockDelete = vi.fn(() => ({ in: mockIn }));
    const mockFrom = vi.fn(() => ({ delete: mockDelete }));
    
    (supabase.from as any) = mockFrom;

    // Simulate the deletion
    const { error } = await supabase
      .from('lineups')
      .delete()
      .in('id', mockLineupIds);

    expect(error).toEqual(mockError);
  });

  it('should update local state after successful series deletion', () => {
    const originalLineups = [
      { id: 'keep1', name: 'Game to Keep 1' },
      { id: 'delete1', name: 'Game to Delete 1' },
      { id: 'delete2', name: 'Game to Delete 2' },
      { id: 'keep2', name: 'Game to Keep 2' },
      { id: 'delete3', name: 'Game to Delete 3' }
    ];

    const idsToDelete = ['delete1', 'delete2', 'delete3'];

    // Simulate the state update logic
    const updatedLineups = originalLineups.filter(
      lineup => !idsToDelete.includes(lineup.id)
    );

    expect(updatedLineups).toHaveLength(2);
    expect(updatedLineups.map(l => l.id)).toEqual(['keep1', 'keep2']);
  });
});

describe('Batch Game Creation Tests', () => {
  it('should validate rotation settings', () => {
    const rotationSettings = {
      rotateLineupEvery: 1,
      rotatePitcherEvery: 2,
      competitiveMode: false,
      limitBenchTime: true,
      maxConsecutiveBenchInnings: 2,
      allowPitcherRotation: true,
      allowCatcherRotation: true,
      respectPositionLockouts: true,
      equalPlayingTime: true
    };

    // Validate settings are within expected ranges
    expect(rotationSettings.rotateLineupEvery).toBeGreaterThanOrEqual(1);
    expect(rotationSettings.rotateLineupEvery).toBeLessThanOrEqual(3);
    
    expect(rotationSettings.rotatePitcherEvery).toBeGreaterThanOrEqual(1);
    expect(rotationSettings.rotatePitcherEvery).toBeLessThanOrEqual(7);
    
    expect(rotationSettings.maxConsecutiveBenchInnings).toBeGreaterThanOrEqual(1);
    expect(rotationSettings.maxConsecutiveBenchInnings).toBeLessThanOrEqual(4);
  });

  it('should create batch data with all required fields', () => {
    const gameDetails = [
      { name: 'Game 1', date: '2024-06-15', time: '10:00', innings: 7 },
      { name: 'Game 2', date: '2024-06-15', time: '14:00', innings: 7 }
    ];

    const batchData = {
      id: 'batch123',
      seriesTitle: 'Weekend Tournament',
      rotationSettings: {
        rotateLineupEvery: 1,
        rotatePitcherEvery: 2,
        competitiveMode: false,
        limitBenchTime: true,
        maxConsecutiveBenchInnings: 2,
        allowPitcherRotation: true,
        allowCatcherRotation: true,
        respectPositionLockouts: true,
        equalPlayingTime: true
      },
      games: gameDetails.map(game => ({
        id: `game-${game.name}`,
        name: game.name,
        gameDate: game.date,
        gameTime: game.time,
        numberOfInnings: game.innings,
        createdDate: new Date().toISOString().split('T')[0],
        innings: [],
        battingOrder: [],
        attendance: {}
      })),
      crossGameTracking: {
        totalGames: 2,
        playingTimeTargets: {},
        pitcherInningsTracked: true,
        catcherInningsTracked: true
      }
    };

    // Verify all fields are present
    expect(batchData.seriesTitle).toBeTruthy();
    expect(batchData.rotationSettings).toBeTruthy();
    expect(batchData.games).toHaveLength(2);
    expect(batchData.crossGameTracking.totalGames).toBe(2);
  });
});

describe('Real-World Coach Scenarios', () => {
  const createRealRoster = (): any[] => {
    return [
      // Your actual players with realistic attributes
      { id: '1', name: 'Star Pitcher', positionRatings: { pitcher: 5, shortstop: 4 }, isStarPlayer: true, teamRoles: {} },
      { id: '2', name: 'Star Catcher', positionRatings: { catcher: 5, firstBase: 4 }, isStarPlayer: true, teamRoles: {} },
      { id: '3', name: 'Fast Outfielder', positionRatings: { centerField: 4, leftField: 3 }, teamRoles: { catcher: 'avoid' } },
      { id: '4', name: 'Power Hitter', positionRatings: { firstBase: 4, thirdBase: 3 }, teamRoles: {} },
      { id: '5', name: 'Utility Player', positionRatings: { secondBase: 3, shortstop: 3, rightField: 3 }, teamRoles: {} },
      { id: '6', name: 'Developing Player 1', positionRatings: { leftField: 2, rightField: 2 }, teamRoles: { pitcher: 'avoid', catcher: 'avoid' } },
      { id: '7', name: 'Developing Player 2', positionRatings: { rightField: 2, firstBase: 2 }, teamRoles: { shortstop: 'avoid' } },
      { id: '8', name: 'Developing Player 3', positionRatings: { secondBase: 2, leftField: 2 }, teamRoles: {} },
      { id: '9', name: 'New Player 1', positionRatings: { rightField: 1, leftField: 1 }, teamRoles: { pitcher: 'avoid', catcher: 'avoid', shortstop: 'avoid' } },
      { id: '10', name: 'New Player 2', positionRatings: { firstBase: 1, rightField: 1 }, teamRoles: { pitcher: 'avoid', catcher: 'avoid' } },
      { id: '11', name: 'Bench Player 1', positionRatings: { leftField: 2, centerField: 2 }, teamRoles: {} },
      { id: '12', name: 'Bench Player 2', positionRatings: { thirdBase: 2, firstBase: 2 }, teamRoles: {} }
    ];
  };

  it('should handle tournament day scenario (3 games, need pitcher rotation)', () => {
    const players = createRealRoster();
    const totalInnings = 21; // 3 games × 7 innings

    // Tournament settings: rotate pitchers every 2 innings, keep core lineup stable
    const innings = generateCompleteLineup(players, totalInnings, {
      rotateLineupEvery: 3, // Keep lineup stable for half a game
      rotatePitcherEvery: 2, // Fresh arms
      competitiveMode: true, // Want to win
      equalPlayingTime: false, // Winning matters more
      competitiveMinPlayingTime: 40, // But everyone plays some
      limitBenchTime: true,
      maxConsecutiveBenchInnings: 4 // Can sit a bit longer in competitive
    });

    // Track pitcher usage
    const pitcherUsage: Record<string, number> = {};
    innings.forEach(inning => {
      const pitcher = inning.positions.pitcher;
      if (pitcher) {
        pitcherUsage[pitcher] = (pitcherUsage[pitcher] || 0) + 1;
      }
    });

    console.log('Tournament pitcher usage:', pitcherUsage);

    // Should use multiple pitchers
    expect(Object.keys(pitcherUsage).length).toBeGreaterThanOrEqual(3);
    
    // No pitcher should throw more than 7 innings (1 full game)
    Object.values(pitcherUsage).forEach(innings => {
      expect(innings).toBeLessThanOrEqual(7);
    });
  });

  it('should handle practice/development scenario (equal time, lots of rotation)', () => {
    const players = createRealRoster();
    const innings = generateCompleteLineup(players, 7, {
      rotateLineupEvery: 1, // Maximum rotation
      rotatePitcherEvery: 1, // Everyone tries pitching
      competitiveMode: false, // Development focus
      equalPlayingTime: true, // Everyone plays equally
      limitBenchTime: true,
      maxConsecutiveBenchInnings: 1, // No one sits long
      allowPitcherRotation: true,
      allowCatcherRotation: true
    });

    // Everyone should get field time
    const fieldTime: Record<string, number> = {};
    players.forEach(p => fieldTime[p.name] = 0);

    innings.forEach(inning => {
      Object.entries(inning.positions).forEach(([pos, name]) => {
        if (pos !== 'bench' && name && typeof name === 'string') {
          fieldTime[name]++;
        }
      });
    });

    // In a 7-inning game with 12 players, everyone should play 4-6 innings
    Object.entries(fieldTime).forEach(([name, time]) => {
      console.log(`Practice game - ${name}: ${time} innings`);
      expect(time).toBeGreaterThanOrEqual(4);
      expect(time).toBeLessThanOrEqual(6);
    });
  });

  it('should handle championship game scenario (best players play more)', () => {
    const players = createRealRoster();
    const innings = generateCompleteLineup(players, 7, {
      rotateLineupEvery: 7, // Minimal rotation
      rotatePitcherEvery: 3, // Ace pitches more
      competitiveMode: true,
      equalPlayingTime: false,
      competitiveMinPlayingTime: 30, // Some might play less
      keyPositions: ['pitcher', 'catcher', 'shortstop', 'centerField']
    });

    // Track star player usage
    const starPlayers = ['Star Pitcher', 'Star Catcher'];
    let starInnings = 0;
    let totalPlayerInnings = 0;

    innings.forEach(inning => {
      Object.entries(inning.positions).forEach(([pos, name]) => {
        if (pos !== 'bench' && name && typeof name === 'string') {
          totalPlayerInnings++;
          if (starPlayers.includes(name)) {
            starInnings++;
          }
        }
      });
    });

    const starPercentage = (starInnings / totalPlayerInnings) * 100;
    console.log(`Championship game - Star players: ${starPercentage.toFixed(0)}% of innings`);

    // Star players should play more than their fair share
    expect(starPercentage).toBeGreaterThan(20); // With 12 players, 2 stars = 16.7% baseline
  });
});
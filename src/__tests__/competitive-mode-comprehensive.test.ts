import {
  generateCompleteLineup,
  validateLineup,
  LineupRules,
  ConstraintSolver
} from '../lib/utils-enhanced';
import { Player, InningLineup } from '../contexts/TeamContext';
import { describe, test, beforeEach, expect, vi } from 'vitest';

// Mock the supabase client
vi.mock('../supabaseClient', () => ({
  supabase: {
    auth: {
      getUser: vi.fn().mockResolvedValue({ data: { user: null }, error: null })
    },
    from: vi.fn(() => ({
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: null, error: null })
    }))
  }
}));

/**
 * COMPREHENSIVE COMPETITIVE MODE TESTING SUITE
 * 
 * This test suite validates the baseball lineup rotation algorithm for competitive play,
 * specifically designed for <PERSON>'s U15 select team requirements.
 * 
 * Key Features Tested:
 * - Player Rating System (1-5 scale) prioritization
 * - Star Player designations and rotation delays
 * - Position restrictions enforcement
 * - Intelligent position assignments
 * - Rotation frequency rules
 * - Bench streak limits in competitive mode
 * - Critical bug fixes validation
 */

// ===== REALISTIC U15 SELECT TEAM DATA =====

const U15_SELECT_PLAYERS: Player[] = [
  // STAR PITCHERS
  {
    id: '1',
    name: 'Alex Thompson',
    positionRestrictions: {
      pitcher: false,    // Can pitch
      catcher: true,     // Cannot catch
      firstBase: true,   // Cannot play 1B
      other: null
    },
    positionRatings: { 
      pitcher: 5,        // Elite pitcher
      shortstop: 3,      // Can play SS as backup
      leftField: 2       // Emergency outfield
    },
    isStarPlayer: true
  },
  {
    id: '2',
    name: 'Ryan Martinez',
    positionRestrictions: {
      pitcher: false,    // Can pitch
      catcher: true,     // Cannot catch
      firstBase: false,  // Can play 1B
      other: null
    },
    positionRatings: { 
      pitcher: 4,        // Strong pitcher
      firstBase: 4,      // Strong 1B
      leftField: 3       // Decent outfield
    },
    isStarPlayer: false
  },

  // STAR CATCHER
  {
    id: '3',
    name: 'Ben Rodriguez',
    positionRestrictions: {
      pitcher: true,     // Cannot pitch
      catcher: false,    // Can catch
      firstBase: false,  // Can play 1B
      other: null
    },
    positionRatings: { 
      catcher: 5,        // Elite catcher
      thirdBase: 4,      // Strong 3B
      rightField: 3      // Decent outfield
    },
    isStarPlayer: true
  },

  // STAR SHORTSTOP
  {
    id: '4',
    name: 'Charlie Kim',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      shortstop: 5,      // Elite SS
      secondBase: 4,     // Strong 2B
      centerField: 4     // Strong CF
    },
    isStarPlayer: true
  },

  // STAR CENTER FIELDER
  {
    id: '5',
    name: 'Henry Davis',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      centerField: 5,    // Elite CF
      leftField: 4,      // Strong LF
      rightField: 4      // Strong RF
    },
    isStarPlayer: true
  },

  // SOLID STARTERS
  {
    id: '6',
    name: 'David Chen',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      firstBase: 4,      // Strong 1B
      thirdBase: 3,      // Decent 3B
      leftField: 3       // Decent LF
    },
    isStarPlayer: false
  },
  {
    id: '7',
    name: 'Emma Wilson',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      secondBase: 4,     // Strong 2B
      shortstop: 3,      // Decent SS
      rightField: 3      // Decent RF
    },
    isStarPlayer: false
  },
  {
    id: '8',
    name: 'Frank Miller',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      thirdBase: 4,      // Strong 3B
      firstBase: 3,      // Decent 1B
      centerField: 2     // Emergency CF
    },
    isStarPlayer: false
  },
  {
    id: '9',
    name: 'Grace Lee',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      leftField: 4,      // Strong LF
      centerField: 4,    // Strong CF
      rightField: 3      // Decent RF
    },
    isStarPlayer: false
  },
  {
    id: '10',
    name: 'Ivy Johnson',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      rightField: 4,     // Strong RF
      centerField: 3,    // Decent CF
      firstBase: 2       // Emergency 1B
    },
    isStarPlayer: false
  },

  // BENCH PLAYERS / SUBSTITUTES
  {
    id: '11',
    name: 'Jack Brown',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      pitcher: 3,        // Decent backup pitcher
      firstBase: 3,      // Decent 1B
      leftField: 3       // Decent LF
    },
    isStarPlayer: false
  },
  {
    id: '12',
    name: 'Kelly White',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      catcher: 3,        // Backup catcher
      thirdBase: 3,      // Decent 3B
      secondBase: 3      // Decent 2B
    },
    isStarPlayer: false
  },
  {
    id: '13',
    name: 'Liam Garcia',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      shortstop: 3,      // Backup SS
      secondBase: 3,     // Decent 2B
      centerField: 3     // Decent CF
    },
    isStarPlayer: false
  },
  {
    id: '14',
    name: 'Maya Patel',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      leftField: 3,      // Decent LF
      rightField: 3,     // Decent RF
      firstBase: 2       // Emergency 1B
    },
    isStarPlayer: false
  }
];

// ===== COMPETITIVE RULES CONFIGURATIONS =====

const STANDARD_COMPETITIVE_RULES: LineupRules = {
  respectPositionLockouts: true,
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 3,     // More lenient in competitive mode
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  equalPlayingTime: false,           // Key difference: not equal playing time
  rotateLineupEvery: 3,             // Rotate every 3 innings
  rotatePitcherEvery: 4,            // Pitcher changes every 4 innings
  competitiveMode: true,             // COMPETITIVE MODE ENABLED
  competitiveMinPlayingTime: 40,     // 40% minimum playing time
  keyPositions: ['pitcher', 'catcher', 'shortstop', 'centerField'],
  starPlayerRotationDelay: 2,        // Star players stay 2 extra innings
  _randomSeed: 12345
};

const AGGRESSIVE_COMPETITIVE_RULES: LineupRules = {
  ...STANDARD_COMPETITIVE_RULES,
  competitiveMinPlayingTime: 25,     // Lower minimum for more star player focus
  maxConsecutiveBenchInnings: 4,     // Allow longer bench time
  starPlayerRotationDelay: 3,        // Keep stars even longer
  keyPositions: ['pitcher', 'catcher', 'shortstop', 'centerField', 'thirdBase'],
  _randomSeed: 12345
};

const BALANCED_COMPETITIVE_RULES: LineupRules = {
  ...STANDARD_COMPETITIVE_RULES,
  competitiveMinPlayingTime: 50,     // Higher minimum for more balance
  maxConsecutiveBenchInnings: 2,     // Stricter bench limits
  starPlayerRotationDelay: 1,        // Less star player retention
  _randomSeed: 12345
};

// ===== TEST SUITE =====

describe('Competitive Mode Comprehensive Testing', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  // ===== CORE COMPETITIVE MODE FEATURES =====

  describe('Player Rating System (1-5 Scale)', () => {
    test('should prioritize high-rated players (4-5) for their preferred positions', () => {
      const lineup = generateCompleteLineup(U15_SELECT_PLAYERS, 7, STANDARD_COMPETITIVE_RULES);
      const firstInning = lineup[0];

      // Elite players (rating 5) should be in their top positions
      expect(firstInning.positions.pitcher).toBe('Alex Thompson');    // 5-rated pitcher
      expect(firstInning.positions.catcher).toBe('Ben Rodriguez');    // 5-rated catcher
      expect(firstInning.positions.shortstop).toBe('Charlie Kim');    // 5-rated shortstop
      expect(firstInning.positions.centerField).toBe('Henry Davis');  // 5-rated center fielder

      // Strong players (rating 4) should be in their strong positions
      const positions = firstInning.positions;
      
      // David Chen (4-rated 1B) should be at first base
      expect(positions.firstBase).toBe('David Chen');
      
      // Emma Wilson (4-rated 2B) should be at second base
      expect(positions.secondBase).toBe('Emma Wilson');
    });

    test('should properly score players using exponential rating scale', () => {
      // Test the competitive scoring algorithm directly
      const solver = new ConstraintSolver(
        U15_SELECT_PLAYERS, 
        STANDARD_COMPETITIVE_RULES, 
        ['Pitcher']
      );

      // Alex Thompson (5-rated pitcher, star) should have highest score
      const alexScore = solver['calculateCompetitiveScore'](
        U15_SELECT_PLAYERS[0], 'Pitcher', STANDARD_COMPETITIVE_RULES
      );
      
      // Ryan Martinez (4-rated pitcher, not star) should have lower score
      const ryanScore = solver['calculateCompetitiveScore'](
        U15_SELECT_PLAYERS[1], 'Pitcher', STANDARD_COMPETITIVE_RULES
      );
      
      // Jack Brown (3-rated pitcher, not star) should have even lower score
      const jackScore = solver['calculateCompetitiveScore'](
        U15_SELECT_PLAYERS[10], 'Pitcher', STANDARD_COMPETITIVE_RULES
      );

      expect(alexScore).toBeGreaterThan(ryanScore);
      expect(ryanScore).toBeGreaterThan(jackScore);
      
      // Verify exponential scoring differences
      expect(alexScore - ryanScore).toBeGreaterThan(100); // Large gap between 5 and 4
      expect(ryanScore - jackScore).toBeGreaterThan(50);   // Significant gap between 4 and 3
    });

    test('should handle players with no ratings gracefully', () => {
      const playersWithMissingRatings = U15_SELECT_PLAYERS.map((p, index) => ({
        ...p,
        positionRatings: index < 5 ? p.positionRatings : undefined // Remove ratings for last 9 players
      }));

      const lineup = generateCompleteLineup(playersWithMissingRatings, 5, STANDARD_COMPETITIVE_RULES);
      
      expect(lineup).toHaveLength(5);
      
      // Players with ratings should still be prioritized
      expect(lineup[0].positions.pitcher).toBe('Alex Thompson');
      expect(lineup[0].positions.catcher).toBe('Ben Rodriguez');
    });
  });

  describe('Star Player Designations', () => {
    test('should keep star players in key positions longer', () => {
      const lineup = generateCompleteLineup(U15_SELECT_PLAYERS, 9, STANDARD_COMPETITIVE_RULES);

      // Count how long star players stay in their key positions
      let alexPitchingInnings = 0;
      let benCatchingInnings = 0;
      let charlieSSInnings = 0;
      let henryCFInnings = 0;

      lineup.forEach(inning => {
        if (inning.positions.pitcher === 'Alex Thompson') alexPitchingInnings++;
        if (inning.positions.catcher === 'Ben Rodriguez') benCatchingInnings++;
        if (inning.positions.shortstop === 'Charlie Kim') charlieSSInnings++;
        if (inning.positions.centerField === 'Henry Davis') henryCFInnings++;
      });

      // Star players should dominate their key positions
      expect(alexPitchingInnings).toBeGreaterThanOrEqual(6);  // At least 2/3 of innings
      expect(benCatchingInnings).toBeGreaterThanOrEqual(6);   // At least 2/3 of innings
      expect(charlieSSInnings).toBeGreaterThanOrEqual(6);     // At least 2/3 of innings
      expect(henryCFInnings).toBeGreaterThanOrEqual(6);       // At least 2/3 of innings
    });

    test('should respect star player rotation delay setting', () => {
      const rulesWithLongDelay = {
        ...STANDARD_COMPETITIVE_RULES,
        starPlayerRotationDelay: 4,
        rotatePitcherEvery: 2
      };

      const lineup = generateCompleteLineup(U15_SELECT_PLAYERS, 8, rulesWithLongDelay);

      // With rotation delay of 4 and pitcher rotation every 2, 
      // Alex should pitch for at least 6 innings straight
      let consecutiveAlexInnings = 0;
      let maxConsecutive = 0;
      
      lineup.forEach(inning => {
        if (inning.positions.pitcher === 'Alex Thompson') {
          consecutiveAlexInnings++;
          maxConsecutive = Math.max(maxConsecutive, consecutiveAlexInnings);
        } else {
          consecutiveAlexInnings = 0;
        }
      });

      expect(maxConsecutive).toBeGreaterThanOrEqual(4); // Should respect the delay
    });

    test('should give star players bonus scores in competitive calculation', () => {
      const solver = new ConstraintSolver(
        U15_SELECT_PLAYERS, 
        STANDARD_COMPETITIVE_RULES, 
        ['Shortstop']
      );

      // Charlie Kim (star, 5-rated SS) vs Liam Garcia (not star, 3-rated SS)
      const charlieScore = solver['calculateCompetitiveScore'](
        U15_SELECT_PLAYERS[3], 'Shortstop', STANDARD_COMPETITIVE_RULES
      );
      
      const liamScore = solver['calculateCompetitiveScore'](
        U15_SELECT_PLAYERS[12], 'Shortstop', STANDARD_COMPETITIVE_RULES
      );

      // Star player should have significantly higher score even beyond rating difference
      expect(charlieScore - liamScore).toBeGreaterThan(150); // Rating diff + star bonus
    });
  });

  describe('Position Restrictions Enforcement', () => {
    test('should never assign players to restricted positions', () => {
      const lineup = generateCompleteLineup(U15_SELECT_PLAYERS, 10, STANDARD_COMPETITIVE_RULES);

      lineup.forEach((inning, inningNum) => {
        // Alex Thompson: cannot catch or play 1B
        const alexPosition = Object.entries(inning.positions).find(([pos, player]) => 
          player === 'Alex Thompson' && pos !== 'bench'
        );
        if (alexPosition) {
          expect(alexPosition[0]).not.toBe('catcher');
          expect(alexPosition[0]).not.toBe('firstBase');
        }

        // Ben Rodriguez: cannot pitch
        const benPosition = Object.entries(inning.positions).find(([pos, player]) => 
          player === 'Ben Rodriguez' && pos !== 'bench'
        );
        if (benPosition) {
          expect(benPosition[0]).not.toBe('pitcher');
        }
      });
    });

    test('should handle complex restriction scenarios', () => {
      // Create players with overlapping restrictions
      const restrictedPlayers: Player[] = [
        {
          id: '1',
          name: 'Pitcher Only',
          positionRestrictions: {
            pitcher: false,    // Can only pitch
            catcher: true,
            firstBase: true,
            other: 'Cannot play any field position except pitcher'
          },
          positionRatings: { pitcher: 5 },
          isStarPlayer: true
        },
        {
          id: '2',
          name: 'Catcher Only',
          positionRestrictions: {
            pitcher: true,
            catcher: false,    // Can only catch
            firstBase: true,
            other: 'Cannot play any field position except catcher'
          },
          positionRatings: { catcher: 5 },
          isStarPlayer: true
        },
        ...U15_SELECT_PLAYERS.slice(2, 12) // Add 10 more flexible players
      ];

      const lineup = generateCompleteLineup(restrictedPlayers, 5, STANDARD_COMPETITIVE_RULES);

      lineup.forEach(inning => {
        // Verify restricted players are in their only allowed positions when playing
        const pitcherOnlyPos = Object.entries(inning.positions).find(([pos, player]) => 
          player === 'Pitcher Only' && pos !== 'bench'
        );
        if (pitcherOnlyPos) {
          expect(pitcherOnlyPos[0]).toBe('pitcher');
        }

        const catcherOnlyPos = Object.entries(inning.positions).find(([pos, player]) => 
          player === 'Catcher Only' && pos !== 'bench'
        );
        if (catcherOnlyPos) {
          expect(catcherOnlyPos[0]).toBe('catcher');
        }
      });
    });

    test('should throw appropriate error when constraints cannot be satisfied', () => {
      // Create impossible scenario: all players restricted from pitcher
      const impossiblePlayers: Player[] = U15_SELECT_PLAYERS.slice(0, 9).map(p => ({
        ...p,
        positionRestrictions: {
          pitcher: true,     // Everyone restricted from pitcher
          catcher: false,
          firstBase: false,
          other: null
        }
      }));

      expect(() => {
        generateCompleteLineup(impossiblePlayers, 5, STANDARD_COMPETITIVE_RULES);
      }).toThrow();
    });
  });

  describe('Intelligent Position Assignments', () => {
    test('should make smart position choices based on ratings, not random', () => {
      // Run the same lineup generation multiple times with different seeds
      const lineups = [];
      for (let i = 0; i < 5; i++) {
        const rules = { ...STANDARD_COMPETITIVE_RULES, _randomSeed: 12345 + i };
        lineups.push(generateCompleteLineup(U15_SELECT_PLAYERS, 3, rules));
      }

      // Key positions should be consistently assigned to the same high-rated players
      const firstInnings = lineups.map(lineup => lineup[0]);
      
      // All should have Alex as pitcher (only 5-rated pitcher who can pitch)
      firstInnings.forEach(inning => {
        expect(inning.positions.pitcher).toBe('Alex Thompson');
      });

      // All should have Ben as catcher (only 5-rated catcher)
      firstInnings.forEach(inning => {
        expect(inning.positions.catcher).toBe('Ben Rodriguez');
      });

      // All should have Charlie at shortstop (only 5-rated shortstop)
      firstInnings.forEach(inning => {
        expect(inning.positions.shortstop).toBe('Charlie Kim');
      });
    });

    test('should use team role preferences when available', () => {
      const playersWithTeamRoles = U15_SELECT_PLAYERS.map(p => ({
        ...p,
        teamRoles: {
          pitcher: p.name === 'Alex Thompson' ? 'go-to' as const : 
                  p.name === 'Ryan Martinez' ? 'capable' as const : 'avoid' as const,
          catcher: p.name === 'Ben Rodriguez' ? 'go-to' as const : 
                  p.name === 'Kelly White' ? 'capable' as const : 'avoid' as const
        }
      }));

      const lineup = generateCompleteLineup(playersWithTeamRoles, 5, STANDARD_COMPETITIVE_RULES);

      // Primary players should dominate their positions
      let alexPitchingCount = 0;
      let benCatchingCount = 0;

      lineup.forEach(inning => {
        if (inning.positions.pitcher === 'Alex Thompson') alexPitchingCount++;
        if (inning.positions.catcher === 'Ben Rodriguez') benCatchingCount++;
      });

      expect(alexPitchingCount).toBeGreaterThanOrEqual(3); // Most innings
      expect(benCatchingCount).toBeGreaterThanOrEqual(3);  // Most innings
    });
  });

  // ===== ROTATION LOGIC VALIDATION =====

  describe('Rotation Frequency Rules', () => {
    test('should respect lineup rotation frequency', () => {
      const rules = { ...STANDARD_COMPETITIVE_RULES, rotateLineupEvery: 2 };
      const lineup = generateCompleteLineup(U15_SELECT_PLAYERS, 8, rules);

      // Check that rotations happen every 2 innings
      const positionChanges = [];
      
      for (let i = 1; i < lineup.length; i++) {
        let changes = 0;
        Object.keys(lineup[i].positions).forEach(pos => {
          if (pos !== 'bench' && lineup[i].positions[pos] !== lineup[i-1].positions[pos]) {
            changes++;
          }
        });
        positionChanges.push(changes);
      }

      // Major rotations should happen on rotation intervals (innings 2, 4, 6, 8)
      expect(positionChanges[1]).toBeGreaterThan(2); // Inning 3 vs 2
      expect(positionChanges[3]).toBeGreaterThan(2); // Inning 5 vs 4
      expect(positionChanges[5]).toBeGreaterThan(2); // Inning 7 vs 6
    });

    test('should respect pitcher rotation frequency', () => {
      const rules = { ...STANDARD_COMPETITIVE_RULES, rotatePitcherEvery: 3 };
      const lineup = generateCompleteLineup(U15_SELECT_PLAYERS, 9, rules);

      const pitchers = lineup.map(inning => inning.positions.pitcher);

      // Pitcher should stay same for 3 innings, then change
      expect(pitchers[0]).toBe(pitchers[1]);
      expect(pitchers[1]).toBe(pitchers[2]);
      expect(pitchers[3]).toBe(pitchers[4]); 
      expect(pitchers[4]).toBe(pitchers[5]);
      expect(pitchers[6]).toBe(pitchers[7]);
      expect(pitchers[7]).toBe(pitchers[8]);

      // But should change between rotation periods
      expect(pitchers[0]).not.toBe(pitchers[3]);
      expect(pitchers[3]).not.toBe(pitchers[6]);
    });

    test('should handle separate pitcher and lineup rotation schedules', () => {
      const rules = { 
        ...STANDARD_COMPETITIVE_RULES, 
        rotateLineupEvery: 2,    // Lineup rotates every 2 innings
        rotatePitcherEvery: 4    // Pitcher rotates every 4 innings
      };
      
      const lineup = generateCompleteLineup(U15_SELECT_PLAYERS, 8, rules);

      const pitchers = lineup.map(inning => inning.positions.pitcher);
      
      // Pitcher should stay for 4 innings despite lineup rotating every 2
      expect(pitchers[0]).toBe(pitchers[1]);
      expect(pitchers[1]).toBe(pitchers[2]);
      expect(pitchers[2]).toBe(pitchers[3]);
      
      // Then change
      expect(pitchers[0]).not.toBe(pitchers[4]);
      
      // And stay for next 4
      expect(pitchers[4]).toBe(pitchers[5]);
      expect(pitchers[5]).toBe(pitchers[6]);
      expect(pitchers[6]).toBe(pitchers[7]);
    });
  });

  describe('Bench Streak Limits in Competitive Mode', () => {
    test('should respect maxConsecutiveBenchInnings in competitive mode', () => {
      const rules = { ...STANDARD_COMPETITIVE_RULES, maxConsecutiveBenchInnings: 2 };
      const lineup = generateCompleteLineup(U15_SELECT_PLAYERS, 10, rules);

      // Track bench streaks for each player
      const benchStreaks = new Map<string, number>();
      U15_SELECT_PLAYERS.forEach(player => benchStreaks.set(player.name, 0));

      lineup.forEach((inning, inningIndex) => {
        const benchPlayers = inning.positions.bench || [];
        
        // Reset streaks for players who are playing
        U15_SELECT_PLAYERS.forEach(player => {
          if (!benchPlayers.includes(player.name)) {
            benchStreaks.set(player.name, 0);
          }
        });

        // Increment and check bench streaks
        benchPlayers.forEach(playerName => {
          const currentStreak = benchStreaks.get(playerName) || 0;
          const newStreak = currentStreak + 1;
          benchStreaks.set(playerName, newStreak);

          // Should never exceed max consecutive bench innings
          expect(newStreak).toBeLessThanOrEqual(rules.maxConsecutiveBenchInnings!);
        });
      });
    });

    test('should allow longer bench time for non-star players in competitive mode', () => {
      const rules = { ...AGGRESSIVE_COMPETITIVE_RULES }; // 4 max consecutive bench innings
      const lineup = generateCompleteLineup(U15_SELECT_PLAYERS, 12, rules);

      // Count total bench time for star vs non-star players
      const starBenchTime = new Map<string, number>();
      const nonStarBenchTime = new Map<string, number>();

      U15_SELECT_PLAYERS.forEach(player => {
        if (player.isStarPlayer) {
          starBenchTime.set(player.name, 0);
        } else {
          nonStarBenchTime.set(player.name, 0);
        }
      });

      lineup.forEach(inning => {
        const benchPlayers = inning.positions.bench || [];
        benchPlayers.forEach(playerName => {
          const player = U15_SELECT_PLAYERS.find(p => p.name === playerName);
          if (player?.isStarPlayer) {
            starBenchTime.set(playerName, (starBenchTime.get(playerName) || 0) + 1);
          } else {
            nonStarBenchTime.set(playerName, (nonStarBenchTime.get(playerName) || 0) + 1);
          }
        });
      });

      // Star players should have significantly less bench time
      const avgStarBench = Array.from(starBenchTime.values()).reduce((a, b) => a + b, 0) / starBenchTime.size;
      const avgNonStarBench = Array.from(nonStarBenchTime.values()).reduce((a, b) => a + b, 0) / nonStarBenchTime.size;

      expect(avgNonStarBench).toBeGreaterThan(avgStarBench * 1.5); // Non-stars should sit more
    });

    test('should force rotation when bench streak limit is reached', () => {
      const rules = { 
        ...STANDARD_COMPETITIVE_RULES, 
        maxConsecutiveBenchInnings: 1,  // Very strict bench limit
        rotateLineupEvery: 5            // But infrequent rotation
      };
      
      const lineup = generateCompleteLineup(U15_SELECT_PLAYERS, 8, rules);

      // Track who's on bench each inning
      const benchHistory = lineup.map(inning => inning.positions.bench || []);
      
      // No player should be on bench for 2 consecutive innings
      for (let i = 1; i < benchHistory.length; i++) {
        const prevBench = benchHistory[i - 1];
        const currBench = benchHistory[i];
        const overlap = prevBench.filter(player => currBench.includes(player));
        
        expect(overlap.length).toBe(0); // No player should be benched twice in a row
      }
    });
  });

  // ===== CRITICAL BUG FIXES =====

  describe('Duplicate Assignment Prevention', () => {
    test('should never assign a player to multiple positions in same inning', () => {
      const lineup = generateCompleteLineup(U15_SELECT_PLAYERS, 10, STANDARD_COMPETITIVE_RULES);

      lineup.forEach((inning, inningIndex) => {
        const assignedPlayers = new Set<string>();
        const duplicates: string[] = [];

        Object.entries(inning.positions).forEach(([position, player]) => {
          if (position !== 'bench') {
            if (typeof player === 'string') {
              if (assignedPlayers.has(player)) {
                duplicates.push(player);
              }
              assignedPlayers.add(player);
            }
          }
        });

        // Handle bench players
        const benchPlayers = inning.positions.bench || [];
        benchPlayers.forEach(player => {
          if (assignedPlayers.has(player)) {
            duplicates.push(player);
          }
          assignedPlayers.add(player);
        });

        expect(duplicates).toHaveLength(0);
      });
    });

    test('should handle edge case with exactly 9 players', () => {
      const exactNinePlayers = U15_SELECT_PLAYERS.slice(0, 9);
      const lineup = generateCompleteLineup(exactNinePlayers, 6, STANDARD_COMPETITIVE_RULES);

      lineup.forEach(inning => {
        const fieldPositions = Object.keys(inning.positions).filter(pos => pos !== 'bench');
        const benchPlayers = inning.positions.bench || [];
        
        expect(fieldPositions).toHaveLength(9);
        expect(benchPlayers).toHaveLength(0);
        
        // All 9 players should be playing, none duplicated
        const allAssigned = fieldPositions.map(pos => inning.positions[pos]);
        const uniqueAssigned = new Set(allAssigned);
        expect(uniqueAssigned.size).toBe(9);
      });
    });
  });

  describe('Position Constraint Violations', () => {
    test('should validate lineup correctness with validation function', () => {
      const lineup = generateCompleteLineup(U15_SELECT_PLAYERS, 7, STANDARD_COMPETITIVE_RULES);

      lineup.forEach(inning => {
        const validation = validateLineup(inning, U15_SELECT_PLAYERS, STANDARD_COMPETITIVE_RULES);
        expect(validation.valid).toBe(true);
        expect(validation.errors).toHaveLength(0);
      });
    });

    test('should detect and reject invalid lineups', () => {
      // Create an invalid lineup with position violations
      const invalidInning: InningLineup = {
        inning: 1,
        positions: {
          pitcher: 'Ben Rodriguez',     // Ben restricted from pitcher
          catcher: 'Alex Thompson',     // Alex restricted from catcher
          firstBase: 'Alex Thompson',   // DUPLICATE: Alex can't be in two positions
          secondBase: 'Emma Wilson',
          shortstop: 'Charlie Kim',
          thirdBase: 'Frank Miller',
          leftField: 'Grace Lee',
          centerField: 'Henry Davis',
          rightField: 'Ivy Johnson',
          bench: []
        }
      };

      const validation = validateLineup(invalidInning, U15_SELECT_PLAYERS, STANDARD_COMPETITIVE_RULES);
      expect(validation.valid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('State Management', () => {
    test('should properly clean up previous assignments between innings', () => {
      const lineup = generateCompleteLineup(U15_SELECT_PLAYERS, 8, STANDARD_COMPETITIVE_RULES);

      // Verify each inning is independent and complete
      lineup.forEach((inning, index) => {
        const allPlayers = new Set<string>();
        
        // Collect all assigned players
        Object.entries(inning.positions).forEach(([pos, player]) => {
          if (pos !== 'bench' && typeof player === 'string') {
            allPlayers.add(player);
          }
        });
        
        const benchPlayers = inning.positions.bench || [];
        benchPlayers.forEach(player => allPlayers.add(player));

        // Should account for all players on roster
        expect(allPlayers.size).toBe(U15_SELECT_PLAYERS.length);
        
        // Verify all expected players are included
        U15_SELECT_PLAYERS.forEach(player => {
          expect(allPlayers.has(player.name)).toBe(true);
        });
      });
    });

    test('should maintain state consistency across innings', () => {
      const lineup = generateCompleteLineup(U15_SELECT_PLAYERS, 10, STANDARD_COMPETITIVE_RULES);

      // Track player movement between innings
      for (let i = 1; i < lineup.length; i++) {
        const prevInning = lineup[i - 1];
        const currInning = lineup[i];
        
        // Every player should be assigned somewhere in both innings
        U15_SELECT_PLAYERS.forEach(player => {
          const prevPos = Object.entries(prevInning.positions).find(([pos, p]) => p === player.name);
          const currPos = Object.entries(currInning.positions).find(([pos, p]) => p === player.name);
          
          expect(prevPos).toBeDefined();
          expect(currPos).toBeDefined();
        });
      }
    });
  });

  // ===== COMPETITIVE ADVANTAGE VALIDATION =====

  describe('Competitive Advantage Maintenance', () => {
    test('should maintain competitive edge with high-rated players in optimal positions', () => {
      const lineup = generateCompleteLineup(U15_SELECT_PLAYERS, 12, STANDARD_COMPETITIVE_RULES);

      // Calculate "team strength" for each inning based on player ratings in their positions
      const inningStrengths = lineup.map(inning => {
        let totalStrength = 0;
        
        Object.entries(inning.positions).forEach(([pos, playerName]) => {
          if (pos !== 'bench' && typeof playerName === 'string') {
            const player = U15_SELECT_PLAYERS.find(p => p.name === playerName);
            if (player?.positionRatings) {
              const posKey = pos === 'firstBase' ? 'firstBase' : 
                           pos === 'secondBase' ? 'secondBase' :
                           pos === 'thirdBase' ? 'thirdBase' :
                           pos === 'shortstop' ? 'shortstop' :
                           pos === 'leftField' ? 'leftField' :
                           pos === 'centerField' ? 'centerField' :
                           pos === 'rightField' ? 'rightField' :
                           pos === 'pitcher' ? 'pitcher' :
                           pos === 'catcher' ? 'catcher' : pos;
              
              const rating = player.positionRatings[posKey] || 2; // Default neutral rating
              totalStrength += rating;
            }
          }
        });
        
        return totalStrength;
      });

      // Team strength should remain high throughout the game
      const avgStrength = inningStrengths.reduce((a, b) => a + b, 0) / inningStrengths.length;
      expect(avgStrength).toBeGreaterThan(30); // High average rating across positions

      // Strength shouldn't vary too wildly (competitive consistency)
      const strengthVariance = Math.max(...inningStrengths) - Math.min(...inningStrengths);
      expect(strengthVariance).toBeLessThan(10); // Reasonable consistency
    });

    test('should prioritize key positions in competitive mode', () => {
      const lineup = generateCompleteLineup(U15_SELECT_PLAYERS, 8, STANDARD_COMPETITIVE_RULES);

      // Key positions should consistently have high-rated players
      const keyPositionRatings = {
        pitcher: [] as number[],
        catcher: [] as number[],
        shortstop: [] as number[],
        centerField: [] as number[]
      };

      lineup.forEach(inning => {
        (['pitcher', 'catcher', 'shortstop', 'centerField'] as const).forEach(pos => {
          const playerName = inning.positions[pos];
          if (typeof playerName === 'string') {
            const player = U15_SELECT_PLAYERS.find(p => p.name === playerName);
            const rating = player?.positionRatings?.[pos] || 1;
            keyPositionRatings[pos].push(rating);
          }
        });
      });

      // Key positions should have high average ratings
      Object.entries(keyPositionRatings).forEach(([pos, ratings]) => {
        const avgRating = ratings.reduce((a, b) => a + b, 0) / ratings.length;
        expect(avgRating).toBeGreaterThan(3.5); // High average for key positions
      });
    });
  });

  // ===== PERFORMANCE AND EDGE CASES =====

  describe('Performance and Reliability', () => {
    test('should complete generation within reasonable time for long games', () => {
      const startTime = Date.now();
      
      const lineup = generateCompleteLineup(U15_SELECT_PLAYERS, 15, STANDARD_COMPETITIVE_RULES);
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(lineup).toHaveLength(15);
      expect(duration).toBeLessThan(10000); // Should complete within 10 seconds even for long games
    });

    test('should handle stress test with complex restrictions', () => {
      // Create complex restriction scenario
      const complexPlayers = U15_SELECT_PLAYERS.map((player, index) => ({
        ...player,
        positionRestrictions: {
          pitcher: index > 2,        // Only first 3 can pitch
          catcher: index < 1 || index > 4,  // Only players 2-5 can catch
          firstBase: index > 10,     // Most can play 1B
          other: index % 3 === 0 ? 'Cannot play middle infield' : null
        }
      }));

      const lineup = generateCompleteLineup(complexPlayers, 10, STANDARD_COMPETITIVE_RULES);

      expect(lineup).toHaveLength(10);
      
      // Validate all constraints are respected
      lineup.forEach(inning => {
        const validation = validateLineup(inning, complexPlayers, STANDARD_COMPETITIVE_RULES);
        expect(validation.valid).toBe(true);
      });
    });

    test('should generate consistent results with same seed', () => {
      const lineup1 = generateCompleteLineup(U15_SELECT_PLAYERS, 5, STANDARD_COMPETITIVE_RULES);
      const lineup2 = generateCompleteLineup(U15_SELECT_PLAYERS, 5, STANDARD_COMPETITIVE_RULES);

      // Should generate identical lineups with same seed
      expect(lineup1).toEqual(lineup2);
    });

    test('should handle minimum roster size (8 players) in competitive mode', () => {
      const minRoster = U15_SELECT_PLAYERS.slice(0, 8);
      const lineup = generateCompleteLineup(minRoster, 6, STANDARD_COMPETITIVE_RULES);

      expect(lineup).toHaveLength(6);
      
      // Should still maintain competitive assignments with limited roster
      lineup.forEach(inning => {
        const fieldPositions = Object.keys(inning.positions).filter(pos => pos !== 'bench');
        expect(fieldPositions).toHaveLength(8); // 8-player lineup
        expect(inning.positions.bench || []).toHaveLength(0); // No bench
      });
    });
  });

  // ===== REAL-WORLD SCENARIOS =====

  describe('Real-World U15 Select Team Scenarios', () => {
    test('should handle tournament scenario: 7-inning games, frequent pitching changes', () => {
      const tournamentRules = {
        ...STANDARD_COMPETITIVE_RULES,
        rotatePitcherEvery: 2,        // Pitch count limits force frequent changes
        maxConsecutiveBenchInnings: 4, // Longer games allow more rest
        competitiveMinPlayingTime: 30   // Tournament focus on winning
      };

      const lineup = generateCompleteLineup(U15_SELECT_PLAYERS, 7, tournamentRules);

      // Verify pitchers change every 2 innings
      const pitchers = lineup.map(inning => inning.positions.pitcher);
      expect(pitchers[0]).toBe(pitchers[1]);
      expect(pitchers[2]).toBe(pitchers[3]);
      expect(pitchers[4]).toBe(pitchers[5]);
      expect(pitchers[0]).not.toBe(pitchers[2]);
      expect(pitchers[2]).not.toBe(pitchers[4]);
    });

    test('should handle league scenario: balanced playing time but competitive assignments', () => {
      const leagueRules = {
        ...STANDARD_COMPETITIVE_RULES,
        competitiveMinPlayingTime: 60,  // More balanced playing time
        maxConsecutiveBenchInnings: 2,  // Strict bench limits
        starPlayerRotationDelay: 1      // Less star player favoritism
      };

      const lineup = generateCompleteLineup(U15_SELECT_PLAYERS, 9, leagueRules);

      // Calculate playing time distribution
      const playingTime = new Map<string, number>();
      U15_SELECT_PLAYERS.forEach(player => playingTime.set(player.name, 0));

      lineup.forEach(inning => {
        Object.entries(inning.positions).forEach(([pos, player]) => {
          if (pos !== 'bench' && typeof player === 'string') {
            playingTime.set(player, (playingTime.get(player) || 0) + 1);
          }
        });
      });

      const playingTimes = Array.from(playingTime.values());
      const minTime = Math.min(...playingTimes);
      const maxTime = Math.max(...playingTimes);

      // Should be more balanced than pure competitive mode
      expect(maxTime - minTime).toBeLessThan(4); // Reasonable spread
      expect(minTime).toBeGreaterThanOrEqual(Math.floor(9 * 0.6)); // Meet minimum playing time
    });

    test('should handle injury scenario: player becomes unavailable mid-game', () => {
      // Generate first 4 innings normally
      const fullLineup = generateCompleteLineup(U15_SELECT_PLAYERS, 8, STANDARD_COMPETITIVE_RULES);
      const firstFourInnings = fullLineup.slice(0, 4);

      // Remove an injured player for remaining innings
      const injuredPlayer = 'Charlie Kim'; // Remove star shortstop
      const remainingPlayers = U15_SELECT_PLAYERS.filter(p => p.name !== injuredPlayer);

      // Generate remaining innings without injured player
      const remainingLineup = generateCompleteLineup(remainingPlayers, 4, STANDARD_COMPETITIVE_RULES);

      // Verify injured player doesn't appear in remaining innings
      remainingLineup.forEach(inning => {
        Object.values(inning.positions).forEach(player => {
          if (typeof player === 'string') {
            expect(player).not.toBe(injuredPlayer);
          } else if (Array.isArray(player)) {
            expect(player).not.toContain(injuredPlayer);
          }
        });
      });

      // Verify positions are still filled appropriately
      remainingLineup.forEach(inning => {
        const validation = validateLineup(inning, remainingPlayers, STANDARD_COMPETITIVE_RULES);
        expect(validation.valid).toBe(true);
      });
    });
  });
});
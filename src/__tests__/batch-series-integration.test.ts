import { describe, it, expect, beforeEach } from 'vitest';
import { generateCompleteLineup } from '@/lib/utils-enhanced';
import { Player, Lineup } from '@/contexts/TeamContext';

describe('Batch Series Integration Tests', () => {
  let players: Player[];

  beforeEach(() => {
    // Create a realistic roster with various player types
    players = [
      // Star players (rating 4+)
      {
        id: 'p1',
        name: 'Ace',
        positionRatings: { pitcher: 5, shortstop: 4, centerField: 4 },
        isStarPlayer: true,
        teamRoles: {}
      },
      {
        id: 'p2',
        name: 'Slugger',
        positionRatings: { firstBase: 5, thirdBase: 4, catcher: 4 },
        isStarPlayer: true,
        teamRoles: {}
      },
      // Regular players with position restrictions
      {
        id: 'p3',
        name: '<PERSON><PERSON>',
        teamRoles: { catcher: 'avoid', pitcher: 'avoid' },
        positionRatings: { centerField: 3, leftField: 3, secondBase: 3 }
      },
      {
        id: 'p4',
        name: 'Utility',
        teamRoles: {},
        positionRatings: { secondBase: 3, shortstop: 3, rightField: 3 }
      },
      // Players with specific restrictions
      {
        id: 'p5',
        name: 'No<PERSON><PERSON>',
        teamRoles: { catcher: 'avoid', pitcher: 'avoid', shortstop: 'avoid' },
        positionRatings: { firstBase: 2, rightField: 2 }
      },
      {
        id: 'p6',
        name: 'Rookie1',
        teamRoles: {},
        positionRatings: { leftField: 2, rightField: 2, firstBase: 2 }
      },
      {
        id: 'p7',
        name: 'Rookie2',
        teamRoles: {},
        positionRatings: { leftField: 2, centerField: 2, secondBase: 2 }
      },
      {
        id: 'p8',
        name: 'Rookie3',
        teamRoles: {},
        positionRatings: { thirdBase: 2, firstBase: 2, rightField: 2 }
      },
      {
        id: 'p9',
        name: 'Rookie4',
        teamRoles: {},
        positionRatings: { secondBase: 2, leftField: 2, centerField: 2 }
      },
      // Extra players for bench rotation
      {
        id: 'p10',
        name: 'Bench1',
        teamRoles: {},
        positionRatings: { rightField: 2, leftField: 2 }
      },
      {
        id: 'p11',
        name: 'Bench2',
        teamRoles: {},
        positionRatings: { firstBase: 2, thirdBase: 2 }
      },
      {
        id: 'p12',
        name: 'Bench3',
        teamRoles: {},
        positionRatings: { centerField: 2, secondBase: 2 }
      }
    ];
  });

  describe('Playing Time Balance Tests', () => {
    it('should maintain tight playing time distribution with rotateLineupEvery=1', () => {
      const totalInnings = 28; // 4 games × 7 innings
      const innings = generateCompleteLineup(players, totalInnings, {
        rotateLineupEvery: 1,
        rotatePitcherEvery: 2,
        equalPlayingTime: true,
        limitBenchTime: true,
        maxConsecutiveBenchInnings: 2
      });

      // Track playing time
      const playingTime: Record<string, number> = {};
      const benchTime: Record<string, number> = {};
      
      players.forEach(p => {
        playingTime[p.name] = 0;
        benchTime[p.name] = 0;
      });

      // Count innings for each player
      innings.forEach(inning => {
        Object.entries(inning.positions).forEach(([pos, playerName]) => {
          if (pos !== 'bench' && playerName) {
            playingTime[playerName]++;
          }
        });
        
        if (inning.positions.bench) {
          inning.positions.bench.forEach((playerName: string) => {
            benchTime[playerName]++;
          });
        }
      });

      // Calculate the range
      const fieldTimes = Object.values(playingTime);
      const minFieldTime = Math.min(...fieldTimes);
      const maxFieldTime = Math.max(...fieldTimes);
      const range = maxFieldTime - minFieldTime;

      console.log('Playing time distribution:', playingTime);
      console.log('Range:', range, '(min:', minFieldTime, 'max:', maxFieldTime, ')');

      // With 12 players and 28 innings, ideal is ~21 innings per player
      // We should see a much tighter range than 20 vs 11-12
      expect(range).toBeLessThanOrEqual(4); // Much tighter than before
      expect(minFieldTime).toBeGreaterThanOrEqual(18); // No one plays too little
      expect(maxFieldTime).toBeLessThanOrEqual(24); // No one plays too much
    });

    it('should enforce 60% bench rotation when rotation is due', () => {
      const innings = generateCompleteLineup(players.slice(0, 10), 7, {
        rotateLineupEvery: 1,
        equalPlayingTime: true,
        limitBenchTime: true
      });

      // Check rotation between consecutive innings
      for (let i = 1; i < innings.length; i++) {
        const prevBench = new Set(innings[i-1].positions.bench || []);
        const currBench = new Set(innings[i].positions.bench || []);
        const prevField = new Set<string>();
        const currField = new Set<string>();

        // Collect field players
        Object.entries(innings[i-1].positions).forEach(([pos, player]) => {
          if (pos !== 'bench' && player) prevField.add(player);
        });
        Object.entries(innings[i].positions).forEach(([pos, player]) => {
          if (pos !== 'bench' && player) currField.add(player);
        });

        // Count how many bench players moved to field
        let benchToField = 0;
        prevBench.forEach(player => {
          if (currField.has(player)) benchToField++;
        });

        // With 10 players, bench size is 1, so at least 1 should rotate
        if (prevBench.size > 0) {
          console.log(`Inning ${i-1} to ${i}: ${benchToField}/${prevBench.size} bench players rotated in`);
          expect(benchToField).toBeGreaterThan(0);
        }
      }
    });
  });

  describe('Position Restriction Tests', () => {
    it('should NEVER assign players to positions they avoid', () => {
      const innings = generateCompleteLineup(players, 28, {
        rotateLineupEvery: 1,
        rotatePitcherEvery: 2,
        respectPositionLockouts: true
      });

      // Check every assignment
      let violations = 0;
      innings.forEach((inning, idx) => {
        Object.entries(inning.positions).forEach(([position, playerName]) => {
          if (position !== 'bench' && playerName) {
            const player = players.find(p => p.name === playerName);
            if (player?.teamRoles) {
              const positionKey = position as keyof typeof player.teamRoles;
              if (player.teamRoles[positionKey] === 'avoid') {
                violations++;
                console.error(`VIOLATION: ${playerName} assigned to ${position} in inning ${idx + 1}`);
              }
            }
          }
        });
      });

      expect(violations).toBe(0);
    });

    it('should respect multiple position restrictions per player', () => {
      // NoGlove has 3 position restrictions
      const restrictedPlayer = players.find(p => p.name === 'NoGlove');
      const innings = generateCompleteLineup(players, 14, {
        respectPositionLockouts: true
      });

      const positionsPlayed = new Set<string>();
      innings.forEach(inning => {
        Object.entries(inning.positions).forEach(([pos, name]) => {
          if (name === 'NoGlove' && pos !== 'bench') {
            positionsPlayed.add(pos);
          }
        });
      });

      // Should never play catcher, pitcher, or shortstop
      expect(positionsPlayed.has('catcher')).toBe(false);
      expect(positionsPlayed.has('pitcher')).toBe(false);
      expect(positionsPlayed.has('shortstop')).toBe(false);
    });
  });

  describe('Competitive Mode Tests', () => {
    it('should prioritize star players in key positions when competitive mode is on', () => {
      const innings = generateCompleteLineup(players, 7, {
        competitiveMode: true,
        competitiveMinPlayingTime: 50,
        keyPositions: ['pitcher', 'catcher', 'shortstop']
      });

      // Track who plays key positions
      const keyPositionPlayers: Record<string, Set<string>> = {
        pitcher: new Set(),
        catcher: new Set(),
        shortstop: new Set()
      };

      innings.forEach(inning => {
        ['pitcher', 'catcher', 'shortstop'].forEach(pos => {
          const player = inning.positions[pos as keyof typeof inning.positions];
          if (player && typeof player === 'string') {
            keyPositionPlayers[pos].add(player);
          }
        });
      });

      // Star players should dominate key positions
      const starNames = ['Ace', 'Slugger'];
      let starAppearances = 0;
      let totalAppearances = 0;

      Object.values(keyPositionPlayers).forEach(playerSet => {
        playerSet.forEach(playerName => {
          totalAppearances++;
          if (starNames.includes(playerName)) {
            starAppearances++;
          }
        });
      });

      console.log('Star player dominance in key positions:', 
        `${starAppearances}/${totalAppearances} (${(starAppearances/totalAppearances*100).toFixed(0)}%)`);

      // Star players should play key positions at least 60% of the time
      expect(starAppearances / totalAppearances).toBeGreaterThanOrEqual(0.6);
    });

    it('should still maintain minimum playing time in competitive mode', () => {
      const innings = generateCompleteLineup(players, 7, {
        competitiveMode: true,
        competitiveMinPlayingTime: 50,
        equalPlayingTime: false
      });

      // Count playing time
      const playingTime: Record<string, number> = {};
      players.forEach(p => playingTime[p.name] = 0);

      innings.forEach(inning => {
        Object.entries(inning.positions).forEach(([pos, name]) => {
          if (pos !== 'bench' && name && typeof name === 'string') {
            playingTime[name]++;
          }
        });
      });

      // Everyone should play at least 50% of innings (3.5 out of 7, so at least 3)
      Object.entries(playingTime).forEach(([name, time]) => {
        console.log(`${name}: ${time} innings`);
        expect(time).toBeGreaterThanOrEqual(3);
      });
    });
  });

  describe('Rotation Frequency Tests', () => {
    it('should rotate pitcher according to rotatePitcherEvery setting', () => {
      const innings = generateCompleteLineup(players.slice(0, 9), 7, {
        rotatePitcherEvery: 2,
        allowPitcherRotation: true
      });

      const pitchers: string[] = [];
      innings.forEach(inning => {
        if (inning.positions.pitcher) {
          pitchers.push(inning.positions.pitcher);
        }
      });

      // Check pitcher changes every 2 innings
      for (let i = 0; i < pitchers.length - 2; i += 2) {
        if (i + 2 < pitchers.length) {
          expect(pitchers[i]).toBe(pitchers[i + 1]); // Same for 2 innings
          if (i + 2 < pitchers.length) {
            expect(pitchers[i]).not.toBe(pitchers[i + 2]); // Different after 2
          }
        }
      }
    });

    it('should rotate entire lineup according to rotateLineupEvery setting', () => {
      const innings = generateCompleteLineup(players.slice(0, 10), 6, {
        rotateLineupEvery: 3,
        equalPlayingTime: true
      });

      // Check that lineups stay mostly stable for 3 innings
      for (let i = 0; i < innings.length - 3; i += 3) {
        const lineup1 = new Set(Object.values(innings[i].positions).filter(p => p && p !== 'bench').flat());
        const lineup2 = new Set(Object.values(innings[i + 1].positions).filter(p => p && p !== 'bench').flat());
        const lineup3 = new Set(Object.values(innings[i + 2].positions).filter(p => p && p !== 'bench').flat());

        // Count common players (should be high within rotation period)
        let common = 0;
        lineup1.forEach(player => {
          if (lineup2.has(player) && lineup3.has(player)) common++;
        });

        console.log(`Innings ${i+1}-${i+3}: ${common} players stayed on field`);
        expect(common).toBeGreaterThanOrEqual(7); // Most players stay
      }
    });
  });

  describe('Cross-Game Balance Tests', () => {
    it('should maintain balance across multiple games in a series', () => {
      // Simulate 4 games of 7 innings each
      const totalInnings = 28;
      const innings = generateCompleteLineup(players, totalInnings, {
        rotateLineupEvery: 1,
        rotatePitcherEvery: 2,
        equalPlayingTime: true,
        limitBenchTime: true,
        maxConsecutiveBenchInnings: 2
      });

      // Track stats across all games
      const stats = {
        playingTime: {} as Record<string, number>,
        consecutiveBench: {} as Record<string, number>,
        maxConsecutiveBench: {} as Record<string, number>,
        pitchingInnings: {} as Record<string, number>
      };

      players.forEach(p => {
        stats.playingTime[p.name] = 0;
        stats.consecutiveBench[p.name] = 0;
        stats.maxConsecutiveBench[p.name] = 0;
        stats.pitchingInnings[p.name] = 0;
      });

      // Analyze all innings
      innings.forEach((inning, idx) => {
        // Reset consecutive bench for players on field
        Object.entries(inning.positions).forEach(([pos, name]) => {
          if (pos !== 'bench' && name && typeof name === 'string') {
            stats.playingTime[name]++;
            stats.consecutiveBench[name] = 0;
            if (pos === 'pitcher') {
              stats.pitchingInnings[name]++;
            }
          }
        });

        // Track consecutive bench
        if (inning.positions.bench) {
          inning.positions.bench.forEach((name: string) => {
            stats.consecutiveBench[name]++;
            stats.maxConsecutiveBench[name] = Math.max(
              stats.maxConsecutiveBench[name],
              stats.consecutiveBench[name]
            );
          });
        }
      });

      // Verify no one sat more than maxConsecutiveBenchInnings
      Object.entries(stats.maxConsecutiveBench).forEach(([name, max]) => {
        console.log(`${name} max consecutive bench: ${max}`);
        expect(max).toBeLessThanOrEqual(2);
      });

      // Verify pitcher rotation is distributed
      const pitcherCounts = Object.values(stats.pitchingInnings).filter(n => n > 0);
      console.log('Pitching distribution:', stats.pitchingInnings);
      expect(pitcherCounts.length).toBeGreaterThanOrEqual(4); // Multiple pitchers used
    });
  });

  describe('Series Management Tests', () => {
    it('should generate consistent lineups for series games', () => {
      // Generate lineups for 3 games
      const gameInnings = [7, 7, 7];
      const allInnings: any[] = [];

      gameInnings.forEach((numInnings, gameIdx) => {
        const innings = generateCompleteLineup(players, numInnings, {
          rotateLineupEvery: 1,
          equalPlayingTime: true
        });
        allInnings.push(...innings);
      });

      // Verify total innings
      expect(allInnings.length).toBe(21);

      // Verify each inning has valid positions
      allInnings.forEach((inning, idx) => {
        expect(inning.positions.pitcher).toBeTruthy();
        expect(inning.positions.catcher).toBeTruthy();
        expect(inning.positions.firstBase).toBeTruthy();
        expect(inning.positions.secondBase).toBeTruthy();
        expect(inning.positions.thirdBase).toBeTruthy();
        expect(inning.positions.shortstop).toBeTruthy();
        expect(inning.positions.leftField).toBeTruthy();
        expect(inning.positions.centerField).toBeTruthy();
        expect(inning.positions.rightField).toBeTruthy();
        
        // Verify bench exists when more than 9 players
        if (players.length > 9) {
          expect(inning.positions.bench).toBeTruthy();
          expect(inning.positions.bench.length).toBe(players.length - 9);
        }
      });
    });
  });
});
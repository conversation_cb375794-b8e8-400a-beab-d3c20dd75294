/**
 * Integration test for the multi-game orchestrator
 * Run with: npm run test test-orchestrator-integration.ts
 */

import { generateMultiGameSeries } from './src/lib/multi-game-orchestrator';
import { Player, LineupRules } from './src/lib/utils-enhanced';

describe('Multi-Game Orchestrator', () => {
  // Create test players with team roles
  const testPlayers: Player[] = [
    { id: '1', name: '<PERSON>', teamRoles: { pitcher: 'primary', catcher: 'never' } },
    { id: '2', name: '<PERSON>', teamRoles: { catcher: 'primary', pitcher: 'emergency' } },
    { id: '3', name: '<PERSON>', teamRoles: { firstBase: 'primary', pitcher: 'in the mix' } },
    { id: '4', name: '<PERSON>', teamRoles: { secondBase: 'primary', shortstop: 'in the mix' } },
    { id: '5', name: '<PERSON>', teamRoles: { shortstop: 'primary', thirdBase: 'in the mix' } },
    { id: '6', name: '<PERSON>', teamRoles: { thirdBase: 'primary', leftField: 'in the mix' } },
    { id: '7', name: '<PERSON>', teamRoles: { leftField: 'primary', centerField: 'in the mix' } },
    { id: '8', name: '<PERSON>', teamRoles: { centerField: 'primary', rightField: 'in the mix' } },
    { id: '9', name: 'Iris', teamRoles: { rightField: 'primary', firstBase: 'in the mix' } },
    { id: '10', name: '<PERSON>', teamRoles: { pitcher: 'in the mix', leftField: 'primary' } },
    { id: '11', name: 'Kate', teamRoles: { catcher: 'in the mix', firstBase: 'primary' } },
    { id: '12', name: 'Liam', teamRoles: { secondBase: 'primary', centerField: 'in the mix' } },
    { id: '13', name: 'Mia', teamRoles: { rightField: 'primary', pitcher: 'never' } },
    { id: '14', name: 'Noah', teamRoles: { thirdBase: 'in the mix', catcher: 'emergency' } }
  ] as Player[];

  const testRules: LineupRules = {
    rotateLineupEvery: 1,
    rotatePitcherEvery: 3,
    competitiveMode: false,
    limitBenchTime: true,
    maxConsecutiveBenchInnings: 2,
    allowPitcherRotation: true,
    allowCatcherRotation: true,
    respectPositionLockouts: true,
    equalPlayingTime: true,
    seed: 'test-seed'
  };

  test('generates 3-game series with proper cross-game fairness', () => {
    const numberOfGames = 3;
    const inningsPerGame = 7;
    const totalInnings = numberOfGames * inningsPerGame;
    const totalFieldPositions = totalInnings * 9;
    const targetInningsPerPlayer = Math.floor(totalFieldPositions / testPlayers.length);

    console.log('Test setup:', {
      players: testPlayers.length,
      games: numberOfGames,
      inningsPerGame,
      totalInnings,
      totalFieldPositions,
      targetInningsPerPlayer
    });

    // Generate the series
    const seriesResult = generateMultiGameSeries(
      testPlayers,
      numberOfGames,
      inningsPerGame,
      testRules
    );

    // Verify correct number of games
    expect(seriesResult.games).toHaveLength(numberOfGames);

    // Verify each game has correct number of innings
    seriesResult.games.forEach((game, idx) => {
      expect(game.lineups).toHaveLength(inningsPerGame);
      console.log(`Game ${idx + 1}: ${game.lineups.length} innings`);
    });

    // Verify balance score is reasonable
    expect(seriesResult.balanceScore).toBeGreaterThan(50); // Should be at least 50% balanced
    console.log(`Balance Score: ${seriesResult.balanceScore.toFixed(1)}%`);

    // Check cumulative stats
    const statsList = Array.from(seriesResult.cumulativeStats.entries())
      .map(([playerId, stats]) => {
        const player = testPlayers.find(p => p.id === playerId);
        return {
          name: player?.name || 'Unknown',
          fieldInnings: stats.totalFieldInnings,
          benchInnings: stats.totalBenchInnings
        };
      })
      .sort((a, b) => b.fieldInnings - a.fieldInnings);

    // Calculate fairness metrics
    const fieldInnings = statsList.map(s => s.fieldInnings);
    const min = Math.min(...fieldInnings);
    const max = Math.max(...fieldInnings);
    const range = max - min;

    console.log('\nPlayer distribution:');
    statsList.forEach(stats => {
      const percentage = (stats.fieldInnings / (stats.fieldInnings + stats.benchInnings) * 100).toFixed(1);
      console.log(`  ${stats.name}: ${stats.fieldInnings} field, ${stats.benchInnings} bench (${percentage}%)`);
    });

    console.log(`\nFairness metrics:
      Min: ${min} innings
      Max: ${max} innings
      Range: ${range} innings
      Target: ${targetInningsPerPlayer} innings`);

    // Verify fairness - range should be small
    expect(range).toBeLessThanOrEqual(5); // Allow up to 5 inning difference
    
    // Verify all players get reasonable playing time
    statsList.forEach(stats => {
      expect(stats.fieldInnings).toBeGreaterThanOrEqual(targetInningsPerPlayer - 3);
      expect(stats.fieldInnings).toBeLessThanOrEqual(targetInningsPerPlayer + 3);
    });

    // Check for position restriction violations
    let violations = 0;
    seriesResult.games.forEach((game, gameIdx) => {
      game.lineups.forEach((inning, inningIdx) => {
        Object.entries(inning.positions).forEach(([position, playerName]) => {
          if (position !== 'bench' && playerName) {
            const player = testPlayers.find(p => p.name === playerName);
            if (player?.teamRoles && player.teamRoles[position as keyof typeof player.teamRoles] === 'never') {
              violations++;
              console.error(`Position violation: ${playerName} at ${position} (Game ${gameIdx + 1}, Inning ${inningIdx + 1})`);
            }
          }
        });
      });
    });

    console.log(`\nPosition violations: ${violations}`);
    expect(violations).toBe(0);
  });

  test('handles single game properly', () => {
    const seriesResult = generateMultiGameSeries(
      testPlayers,
      1,  // Single game
      9,  // 9 innings
      testRules
    );

    expect(seriesResult.games).toHaveLength(1);
    expect(seriesResult.games[0].lineups).toHaveLength(9);
    expect(seriesResult.balanceScore).toBeGreaterThan(70); // Single game should be well balanced
  });

  test('adjusts priorities for subsequent games', () => {
    const seriesResult = generateMultiGameSeries(
      testPlayers,
      2,  // 2 games
      7,  // 7 innings each
      testRules
    );

    // Players who played more in game 1 should play less in game 2
    const game1Stats = seriesResult.games[0].gameStats;
    const game2Stats = seriesResult.games[1].gameStats;

    // Find players with most/least playing time in game 1
    const game1Array = Array.from(game1Stats.entries())
      .map(([id, stats]) => ({ id, innings: stats.fieldInnings }))
      .sort((a, b) => b.innings - a.innings);

    const mostPlayedGame1 = game1Array[0].id;
    const leastPlayedGame1 = game1Array[game1Array.length - 1].id;

    // Check their playing time in game 2
    const mostPlayedGame2Innings = game2Stats.get(mostPlayedGame1)?.fieldInnings || 0;
    const leastPlayedGame2Innings = game2Stats.get(leastPlayedGame1)?.fieldInnings || 0;

    console.log(`\nCross-game adjustment test:
      Player who played most in game 1: ${game1Array[0].innings} innings → ${mostPlayedGame2Innings} innings in game 2
      Player who played least in game 1: ${game1Array[game1Array.length - 1].innings} innings → ${leastPlayedGame2Innings} innings in game 2`);

    // The player who played most in game 1 should generally play less in game 2
    // (This is a soft check as there's some randomness)
    const cumulativeStats = seriesResult.cumulativeStats;
    const finalRange = Math.max(...Array.from(cumulativeStats.values()).map(s => s.totalFieldInnings)) -
                      Math.min(...Array.from(cumulativeStats.values()).map(s => s.totalFieldInnings));
    
    expect(finalRange).toBeLessThanOrEqual(4); // Good cross-game balance
  });
});
import { describe, test, expect } from 'vitest';
import { generateOptimalLineupWrapper } from '../lib/lineup-generation-wrapper';
import { Player, LineupRules } from '@/contexts/TeamContext';

describe('Rotation Algorithm Integration Test', () => {
  // Create a full roster with position preferences
  const createTestRoster = (): Player[] => [
    {
      id: '1',
      name: '<PERSON>',
      team_id: 'test-team',
      positionPreferences: {
        leftField: 'preferred',
        secondBase: 'secondary'
        // NO pitcher - should NEVER play pitcher in competitive
      },
      positionRankings: [],
      overallRating: 3,
      pitcher_restriction: false,
      catcher_restriction: false,
      first_base_restriction: false,
      unavailableForGames: [],
      player_number: '1'
    },
    {
      id: '2',
      name: '<PERSON><PERSON>',
      team_id: 'test-team',
      positionPreferences: {
        pitcher: 'preferred',
        catcher: 'secondary'
      },
      positionRankings: [],
      overallRating: 4,
      pitcher_restriction: false,
      catcher_restriction: false,
      first_base_restriction: false,
      unavailableForGames: [],
      player_number: '2'
    },
    {
      id: '3',
      name: '<PERSON>',
      team_id: 'test-team',
      positionPreferences: {
        pitcher: 'secondary',
        secondBase: 'preferred'
      },
      positionRankings: [],
      overallRating: 3,
      pitcher_restriction: false,
      catcher_restriction: false,
      first_base_restriction: false,
      unavailableForGames: [],
      player_number: '3'
    },
    {
      id: '4',
      name: 'Avalon',
      team_id: 'test-team',
      positionPreferences: {
        catcher: 'preferred'
      },
      positionRankings: [],
      overallRating: 3,
      pitcher_restriction: false,
      catcher_restriction: false,
      first_base_restriction: false,
      unavailableForGames: [],
      player_number: '4'
    },
    {
      id: '5',
      name: 'Finn',
      team_id: 'test-team',
      positionPreferences: {
        firstBase: 'preferred'
      },
      positionRankings: [],
      overallRating: 3,
      pitcher_restriction: false,
      catcher_restriction: false,
      first_base_restriction: false,
      unavailableForGames: [],
      player_number: '5'
    },
    {
      id: '6',
      name: 'Bella',
      team_id: 'test-team',
      positionPreferences: {
        firstBase: 'secondary',
        leftField: 'secondary'
      },
      positionRankings: [],
      overallRating: 3,
      pitcher_restriction: false,
      catcher_restriction: false,
      first_base_restriction: false,
      unavailableForGames: [],
      player_number: '6'
    },
    {
      id: '7',
      name: 'Morgan',
      team_id: 'test-team',
      positionPreferences: {
        centerField: 'preferred',
        shortstop: 'secondary'
      },
      positionRankings: [],
      overallRating: 3,
      pitcher_restriction: false,
      catcher_restriction: false,
      first_base_restriction: false,
      unavailableForGames: [],
      player_number: '7'
    },
    {
      id: '8',
      name: 'Kaitlyn',
      team_id: 'test-team',
      positionPreferences: {
        shortstop: 'preferred',
        thirdBase: 'secondary'
      },
      positionRankings: [],
      overallRating: 3,
      pitcher_restriction: false,
      catcher_restriction: false,
      first_base_restriction: false,
      unavailableForGames: [],
      player_number: '8'
    },
    {
      id: '9',
      name: 'Charlotte',
      team_id: 'test-team',
      positionPreferences: {
        thirdBase: 'preferred'
      },
      positionRankings: [],
      overallRating: 3,
      pitcher_restriction: false,
      catcher_restriction: false,
      first_base_restriction: false,
      unavailableForGames: [],
      player_number: '9'
    },
    {
      id: '10',
      name: 'Mikayla',
      team_id: 'test-team',
      positionPreferences: {
        centerField: 'secondary',
        secondBase: 'secondary'
      },
      positionRankings: [],
      overallRating: 3,
      pitcher_restriction: false,
      catcher_restriction: false,
      first_base_restriction: false,
      unavailableForGames: [],
      player_number: '10'
    },
    {
      id: '11',
      name: 'Elle',
      team_id: 'test-team',
      positionPreferences: {
        rightField: 'preferred'
      },
      positionRankings: [],
      overallRating: 3,
      pitcher_restriction: false,
      catcher_restriction: false,
      first_base_restriction: false,
      unavailableForGames: [],
      player_number: '11'
    }
  ];

  test('Competitive mode: Players only play assigned positions across all innings', async () => {
    const players = createTestRoster();
    const rules: LineupRules = {
      competitiveMode: true,
      rotateLineupEvery: 1, // Rotate every inning
      rotatePitcherEvery: 2,
      maxConsecutiveBenchInnings: 2,
      limitBenchTime: true,
      equalPlayingTime: false
    };

    // Generate 6 innings
    const innings = await generateOptimalLineupWrapper(players, 6, rules);
    
    expect(innings).toHaveLength(6);

    // Check each inning
    innings.forEach((inning, idx) => {
      console.log(`\nInning ${idx + 1}:`);
      
      // Verify Avery is NEVER at pitcher
      if (inning.positions.pitcher === 'Avery') {
        console.error(`❌ FAILURE: Avery assigned to pitcher in inning ${idx + 1}`);
      }
      expect(inning.positions.pitcher).not.toBe('Avery');
      
      // Verify all assignments are valid
      Object.entries(inning.positions).forEach(([position, playerName]) => {
        if (position !== 'bench' && typeof playerName === 'string') {
          const player = players.find(p => p.name === playerName);
          if (player) {
            const hasPreference = player.positionPreferences?.[position];
            if (!hasPreference) {
              console.error(`❌ Invalid: ${playerName} at ${position} (no preference)`);
            }
            expect(hasPreference).toBeTruthy();
          }
        }
      });
      
      // Log the lineup
      console.log('Pitcher:', inning.positions.pitcher);
      console.log('Catcher:', inning.positions.catcher);
      console.log('Bench:', inning.positions.bench);
    });
  });

  test('Rotation happens between innings', async () => {
    const players = createTestRoster();
    const rules: LineupRules = {
      competitiveMode: false, // Use recreational for more rotation flexibility
      rotateLineupEvery: 1,
      rotatePitcherEvery: 2,
      maxConsecutiveBenchInnings: 2,
      limitBenchTime: true,
      equalPlayingTime: true
    };

    const innings = await generateOptimalLineupWrapper(players, 3, rules);
    
    // Check that players rotate
    const inning1Bench = new Set(innings[0].positions.bench);
    const inning2Bench = new Set(innings[1].positions.bench);
    const inning3Bench = new Set(innings[2].positions.bench);
    
    // Benches should be different (rotation happening)
    const sameBenchPlayers12 = [...inning1Bench].filter(p => inning2Bench.has(p));
    const sameBenchPlayers23 = [...inning2Bench].filter(p => inning3Bench.has(p));
    
    console.log('Inning 1 bench:', [...inning1Bench]);
    console.log('Inning 2 bench:', [...inning2Bench]);
    console.log('Inning 3 bench:', [...inning3Bench]);
    console.log('Same bench 1->2:', sameBenchPlayers12);
    console.log('Same bench 2->3:', sameBenchPlayers23);
    
    // At least some rotation should happen
    expect(sameBenchPlayers12.length).toBeLessThan(inning1Bench.size);
  });
});
import { generateCompleteLineup, LineupRules } from '../lib/utils-enhanced';
import { Player } from '../contexts/TeamContext';
import { describe, test, expect, vi } from 'vitest';

// Mock the supabase client
vi.mock('../integrations/supabase/client', () => ({
  supabase: {
    auth: {
      getUser: vi.fn().mockResolvedValue({ data: { user: null }, error: null })
    },
    from: vi.fn(() => ({
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: null, error: null })
    }))
  }
}));

const SIMPLE_TEST_PLAYERS: Player[] = [
  {
    id: '1',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: false,    // Can pitch
      catcher: true,     // Cannot catch
      firstBase: true,   // Cannot play 1B
      other: null
    },
    positionRatings: { 
      pitcher: 5,        // Elite pitcher
      shortstop: 3,      // Can play SS as backup
      leftField: 2       // Emergency outfield
    },
    isStarPlayer: true
  },
  {
    id: '2',
    name: 'Ben Rodriguez',
    positionRestrictions: {
      pitcher: true,     // Cannot pitch
      catcher: false,    // Can catch
      firstBase: false,  // Can play 1B
      other: null
    },
    positionRatings: { 
      catcher: 5,        // Elite catcher
      thirdBase: 4,      // Strong 3B
      rightField: 3      // Decent outfield
    },
    isStarPlayer: true
  },
  {
    id: '3',
    name: 'Charlie Kim',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      shortstop: 5,      // Elite SS
      secondBase: 4,     // Strong 2B
      centerField: 4     // Strong CF
    },
    isStarPlayer: true
  },
  {
    id: '4',
    name: 'David Chen',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      firstBase: 4,      // Strong 1B
      thirdBase: 3,      // Decent 3B
      leftField: 3       // Decent LF
    },
    isStarPlayer: false
  },
  {
    id: '5',
    name: 'Emma Wilson',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      secondBase: 4,     // Strong 2B
      shortstop: 3,      // Decent SS
      rightField: 3      // Decent RF
    },
    isStarPlayer: false
  },
  {
    id: '6',
    name: 'Frank Miller',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      thirdBase: 4,      // Strong 3B
      firstBase: 3,      // Decent 1B
      centerField: 2     // Emergency CF
    },
    isStarPlayer: false
  },
  {
    id: '7',
    name: 'Grace Lee',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      leftField: 4,      // Strong LF
      centerField: 4,    // Strong CF
      rightField: 3      // Decent RF
    },
    isStarPlayer: false
  },
  {
    id: '8',
    name: 'Henry Davis',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      centerField: 5,    // Elite CF
      leftField: 4,      // Strong LF
      rightField: 4      // Strong RF
    },
    isStarPlayer: true
  },
  {
    id: '9',
    name: 'Ivy Johnson',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    },
    positionRatings: { 
      rightField: 4,     // Strong RF
      centerField: 3,    // Decent CF
      firstBase: 2       // Emergency 1B
    },
    isStarPlayer: false
  }
];

const COMPETITIVE_RULES: LineupRules = {
  respectPositionLockouts: true,
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 3,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  equalPlayingTime: false,
  rotateLineupEvery: 3,
  rotatePitcherEvery: 4,
  competitiveMode: true,
  competitiveMinPlayingTime: 40,
  keyPositions: ['pitcher', 'catcher', 'shortstop', 'centerField'],
  starPlayerRotationDelay: 2,
  _randomSeed: 12345
};

describe('Debug Duplicate Assignment Issue', () => {
  test('should generate lineup without duplicate assignments', () => {
    console.log('🏗️ Attempting to generate lineup...');
    console.log(`📊 Players: ${SIMPLE_TEST_PLAYERS.length}`);
    console.log(`⚙️ Competitive Mode: ${COMPETITIVE_RULES.competitiveMode}`);

    const lineup = generateCompleteLineup(SIMPLE_TEST_PLAYERS, 3, COMPETITIVE_RULES);
    
    expect(lineup).toHaveLength(3);
    
    // Validate each inning for duplicate assignments
    lineup.forEach((inning, inningIndex) => {
      console.log(`\n📋 Inning ${inningIndex + 1}:`);
      
      const assignedPlayers = new Set<string>();
      const duplicates: string[] = [];

      // Check field positions
      Object.entries(inning.positions).forEach(([position, player]) => {
        if (position !== 'bench' && typeof player === 'string') {
          console.log(`  ${position}: ${player}`);
          
          if (assignedPlayers.has(player)) {
            duplicates.push(player);
          }
          assignedPlayers.add(player);
        }
      });

      // Check bench players
      const benchPlayers = inning.positions.bench || [];
      if (benchPlayers.length > 0) {
        console.log(`  bench: ${benchPlayers.join(', ')}`);
      }
      
      benchPlayers.forEach(player => {
        if (assignedPlayers.has(player)) {
          duplicates.push(player);
        }
        assignedPlayers.add(player);
      });

      // Assert no duplicates
      expect(duplicates).toHaveLength(0);
      
      // Verify all players are accounted for
      expect(assignedPlayers.size).toBe(SIMPLE_TEST_PLAYERS.length);
    });
  });

  test('should respect star player positions in competitive mode', () => {
    const lineup = generateCompleteLineup(SIMPLE_TEST_PLAYERS, 3, COMPETITIVE_RULES);
    
    // Check first inning for star player assignments
    const firstInning = lineup[0];
    
    // Alex Thompson (star pitcher) should be pitching
    expect(firstInning.positions.pitcher).toBe('Alex Thompson');
    
    // Ben Rodriguez (star catcher) should be catching
    expect(firstInning.positions.catcher).toBe('Ben Rodriguez');
    
    // Charlie Kim (star shortstop) should be at shortstop
    expect(firstInning.positions.shortstop).toBe('Charlie Kim');
    
    // Henry Davis (star center fielder) should be in center field
    expect(firstInning.positions.centerField).toBe('Henry Davis');
  });

  test('should handle single inning generation', () => {
    const lineup = generateCompleteLineup(SIMPLE_TEST_PLAYERS, 1, COMPETITIVE_RULES);
    
    expect(lineup).toHaveLength(1);
    
    const inning = lineup[0];
    const fieldPositions = Object.keys(inning.positions).filter(pos => pos !== 'bench');
    
    expect(fieldPositions).toHaveLength(9);
    expect(inning.positions.bench || []).toHaveLength(0); // No bench with 9 players
  });
});
import { describe, it, expect, vi } from 'vitest';
import { generateMultiGameSeries } from '../lib/multi-game-orchestrator';
import { Player, LineupRules } from '../lib/utils-enhanced';

// Mock the single-game lineup generators to avoid catcher rotation issues
vi.mock('../lib/single-game-lineup-strict', () => ({
  generateSingleGameLineupStrict: vi.fn()
}));

vi.mock('../lib/single-game-lineup', () => ({
  generateSingleGameLineup: vi.fn()
}));

import { generateSingleGameLineupStrict } from '../lib/single-game-lineup-strict';
import { generateSingleGameLineup } from '../lib/single-game-lineup';

describe('Focused Pitcher Slot Assignment Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock the single game lineup generator to return predictable lineups
    const mockGenerateLineup = (players: Player[], innings: number, rules: any) => {
      const lineups = [];
      const pitcherPlan = (rules as any)._pitcher<PERSON>lan;
      
      for (let i = 0; i < innings; i++) {
        // Find which pitcher should be in this inning based on the plan
        let assignedPitcher = null;
        if (pitcherPlan) {
          for (const pitcher of pitcherPlan.plannedPitchers) {
            if (pitcher.targetInnings.includes(i + 1)) {
              assignedPitcher = pitcher.player.name;
              break;
            }
          }
        }
        
        lineups.push({
          inning: i + 1,
          positions: {
            pitcher: assignedPitcher || 'Unknown',
            catcher: 'Catcher',
            firstBase: 'Player 1',
            secondBase: 'Player 2',
            shortstop: 'Player 3',
            thirdBase: 'Player 4',
            leftField: 'Player 5',
            centerField: 'Player 6',
            rightField: 'Player 7',
            bench: ['Player 8', 'Player 9']
          }
        });
      }
      
      return lineups;
    };
    
    (generateSingleGameLineupStrict as any).mockImplementation(mockGenerateLineup);
    (generateSingleGameLineup as any).mockImplementation(mockGenerateLineup);
  });
  
  it('should correctly assign starter, reliever, and closer to proper slots', () => {
    const players: Player[] = [
      {
        id: '1',
        name: 'Starter Sam',
        teamRoles: { pitcher: 'primary' },
        pitcherStrategy: { role: 'starter', inningsPerGame: 3 }
      },
      {
        id: '2',
        name: 'Reliever Rick',
        teamRoles: { pitcher: 'primary' },
        pitcherStrategy: { role: 'reliever', inningsPerGame: 2 }
      },
      {
        id: '3',
        name: 'Closer Chris',
        teamRoles: { pitcher: 'primary' },
        pitcherStrategy: { role: 'closer', inningsPerGame: 2 }
      },
      // Add some generic players
      ...Array.from({ length: 9 }, (_, i) => ({
        id: `p${i + 4}`,
        name: `Player ${i + 4}`,
        teamRoles: {}
      }))
    ];
    
    const rules: LineupRules = {
      rotatePitcherEvery: 2,
      rotateLineupEvery: 2,
      maxConsecutiveBenchInnings: 2,
      respectPositionLockouts: false,
      limitBenchTime: true,
      allowPitcherRotation: true,
      allowCatcherRotation: true
    };
    
    const result = generateMultiGameSeries(players, 1, 6, rules);
    
    // Extract the pitcher plan that was passed to the single game generator
    const mockCalls = (generateSingleGameLineupStrict as any).mock.calls;
    expect(mockCalls.length).toBe(1);
    
    const passedRules = mockCalls[0][2];
    const pitcherPlan = passedRules._pitcherPlan;
    
    expect(pitcherPlan).toBeDefined();
    expect(pitcherPlan.plannedPitchers).toHaveLength(3);
    
    // Verify starter is assigned to innings 1-2
    const starter = pitcherPlan.plannedPitchers.find((p: any) => p.role === 'starter');
    expect(starter).toBeDefined();
    expect(starter.player.name).toBe('Starter Sam');
    expect(starter.targetInnings).toEqual([1, 2]);
    
    // Verify closer is assigned to innings 5-6
    const closer = pitcherPlan.plannedPitchers.find((p: any) => p.role === 'closer');
    expect(closer).toBeDefined();
    expect(closer.player.name).toBe('Closer Chris');
    expect(closer.targetInnings).toEqual([5, 6]);
    
    // Verify middle pitcher is assigned to innings 3-4
    const middle = pitcherPlan.plannedPitchers.find((p: any) => p.role === 'middle');
    expect(middle).toBeDefined();
    expect(middle.player.name).toBe('Reliever Rick');
    expect(middle.targetInnings).toEqual([3, 4]);
  });
  
  it('should handle 9-inning games with 3-inning rotations correctly', () => {
    const players: Player[] = [
      {
        id: '1',
        name: 'Starter Steve',
        teamRoles: { pitcher: 'primary' },
        pitcherStrategy: { role: 'starter', inningsPerGame: 3 }
      },
      {
        id: '2',
        name: 'Closer Carl',
        teamRoles: { pitcher: 'primary' },
        pitcherStrategy: { role: 'closer', inningsPerGame: 3 }
      },
      {
        id: '3',
        name: 'Middle Mike',
        teamRoles: { pitcher: 'primary' },
        pitcherStrategy: { role: 'any', inningsPerGame: 3 }
      },
      // Add some generic players
      ...Array.from({ length: 9 }, (_, i) => ({
        id: `p${i + 4}`,
        name: `Player ${i + 4}`,
        teamRoles: {}
      }))
    ];
    
    const rules: LineupRules = {
      rotatePitcherEvery: 3,
      rotateLineupEvery: 3,
      maxConsecutiveBenchInnings: 2,
      respectPositionLockouts: false,
      limitBenchTime: true,
      allowPitcherRotation: true,
      allowCatcherRotation: true
    };
    
    const result = generateMultiGameSeries(players, 1, 9, rules);
    
    const mockCalls = (generateSingleGameLineupStrict as any).mock.calls;
    const passedRules = mockCalls[0][2];
    const pitcherPlan = passedRules._pitcherPlan;
    
    expect(pitcherPlan.plannedPitchers).toHaveLength(3);
    
    // Verify starter gets innings 1-3
    const starter = pitcherPlan.plannedPitchers.find((p: any) => p.role === 'starter');
    expect(starter.targetInnings).toEqual([1, 2, 3]);
    
    // Verify closer gets innings 7-9
    const closer = pitcherPlan.plannedPitchers.find((p: any) => p.role === 'closer');
    expect(closer.targetInnings).toEqual([7, 8, 9]);
    
    // Verify middle pitcher gets innings 4-6
    const middle = pitcherPlan.plannedPitchers.find((p: any) => p.role === 'middle');
    expect(middle.targetInnings).toEqual([4, 5, 6]);
  });
  
  it('should not have overlapping slot assignments', () => {
    const players: Player[] = [
      {
        id: '1',
        name: 'Starter',
        teamRoles: { pitcher: 'primary' },
        pitcherStrategy: { role: 'starter', inningsPerGame: 2 }
      },
      {
        id: '2',
        name: 'Reliever1',
        teamRoles: { pitcher: 'primary' },
        pitcherStrategy: { role: 'reliever', inningsPerGame: 2 }
      },
      {
        id: '3',
        name: 'Reliever2',
        teamRoles: { pitcher: 'primary' },
        pitcherStrategy: { role: 'reliever', inningsPerGame: 2 }
      },
      {
        id: '4',
        name: 'Closer',
        teamRoles: { pitcher: 'primary' },
        pitcherStrategy: { role: 'closer', inningsPerGame: 2 }
      },
      // Add some generic players
      ...Array.from({ length: 8 }, (_, i) => ({
        id: `p${i + 5}`,
        name: `Player ${i + 5}`,
        teamRoles: {}
      }))
    ];
    
    const rules: LineupRules = {
      rotatePitcherEvery: 2,
      rotateLineupEvery: 2,
      maxConsecutiveBenchInnings: 2,
      respectPositionLockouts: false,
      limitBenchTime: true,
      allowPitcherRotation: true,
      allowCatcherRotation: true
    };
    
    const result = generateMultiGameSeries(players, 1, 8, rules);
    
    const mockCalls = (generateSingleGameLineupStrict as any).mock.calls;
    const passedRules = mockCalls[0][2];
    const pitcherPlan = passedRules._pitcherPlan;
    
    // Should have 4 pitchers for 4 slots (8 innings / 2 = 4 slots)
    expect(pitcherPlan.plannedPitchers).toHaveLength(4);
    
    // Collect all assigned innings
    const allInnings = new Set<number>();
    pitcherPlan.plannedPitchers.forEach((pitcher: any) => {
      pitcher.targetInnings.forEach((inning: number) => {
        expect(allInnings.has(inning)).toBe(false); // No duplicates
        allInnings.add(inning);
      });
    });
    
    // Should cover all 8 innings
    expect(allInnings.size).toBe(8);
    expect(Array.from(allInnings).sort()).toEqual([1, 2, 3, 4, 5, 6, 7, 8]);
  });
});
import { describe, test, expect } from 'vitest';
import { canPlayPosition } from '../lib/utils-enhanced';
import { Player } from '@/contexts/TeamContext';

describe('Rotation Fix Verification', () => {
  // Mock players based on the screenshot
  const players: Player[] = [
    {
      id: '1',
      name: '<PERSON>',
      team_id: 'test-team',
      positionPreferences: {
        leftField: 'preferred',
        secondBase: 'secondary'
        // NO pitcher entry - she should NEVER play pitcher in competitive mode
      },
      positionRankings: [],
      overallRating: 3,
      pitcher_restriction: false,
      catcher_restriction: false,
      first_base_restriction: false,
      unavailableForGames: [],
      player_number: '1'
    },
    {
      id: '2',
      name: '<PERSON><PERSON>',
      team_id: 'test-team',
      positionPreferences: {
        pitcher: 'preferred',
        catcher: 'secondary'
      },
      positionRankings: [],
      overallRating: 3,
      pitcher_restriction: false,
      catcher_restriction: false,
      first_base_restriction: false,
      unavailableForGames: [],
      player_number: '2'
    },
    {
      id: '3',
      name: '<PERSON>',
      team_id: 'test-team',
      positionPreferences: {
        pitcher: 'secondary',
        secondBase: 'preferred'
      },
      positionRankings: [],
      overallRating: 3,
      pitcher_restriction: false,
      catcher_restriction: false,
      first_base_restriction: false,
      unavailableForGames: [],
      player_number: '3'
    }
  ];

  test('Avery should NOT be able to play pitcher in competitive mode', () => {
    const avery = players[0];
    const canPlay = canPlayPosition(avery, 'pitcher', true, true);
    expect(canPlay).toBe(false);
  });

  test('Avery SHOULD be able to play pitcher in recreational mode', () => {
    const avery = players[0];
    const canPlay = canPlayPosition(avery, 'pitcher', true, false);
    expect(canPlay).toBe(true);
  });

  test('Avery SHOULD be able to play leftField in competitive mode', () => {
    const avery = players[0];
    const canPlay = canPlayPosition(avery, 'leftField', true, true);
    expect(canPlay).toBe(true);
  });

  test('Kenzie SHOULD be able to play pitcher in competitive mode', () => {
    const kenzie = players[1];
    const canPlay = canPlayPosition(kenzie, 'pitcher', true, true);
    expect(canPlay).toBe(true);
  });

  test('Build eligibility matrix for competitive mode', () => {
    const positions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'thirdBase', 
                      'shortstop', 'leftField', 'centerField', 'rightField'];
    
    const eligibilityMatrix: Record<string, string[]> = {};
    
    positions.forEach(pos => {
      eligibilityMatrix[pos] = players.filter(p => 
        canPlayPosition(p, pos, true, true) // competitive mode
      ).map(p => p.name);
    });

    // Verify Avery is NOT in pitcher eligibility
    expect(eligibilityMatrix.pitcher).not.toContain('Avery');
    
    // Verify Kenzie IS in pitcher eligibility
    expect(eligibilityMatrix.pitcher).toContain('Kenzie');
    
    // Verify Grace IS in pitcher eligibility (secondary)
    expect(eligibilityMatrix.pitcher).toContain('Grace');
    
    console.log('Eligibility Matrix:', eligibilityMatrix);
  });

  test('Player with "avoid" should never play that position', () => {
    const playerWithAvoid: Player = {
      id: 'test',
      name: 'Test Player',
      team_id: 'test-team',
      positionPreferences: {
        catcher: 'avoid' as any, // Using 'avoid' which should block
      },
      teamRoles: {
        catcher: 'avoid'
      },
      positionRankings: [],
      overallRating: 3,
      pitcher_restriction: false,
      catcher_restriction: false,
      first_base_restriction: false,
      unavailableForGames: [],
      player_number: 'test'
    };

    // Should NOT be able to play catcher in ANY mode
    expect(canPlayPosition(playerWithAvoid, 'catcher', true, true)).toBe(false);
    expect(canPlayPosition(playerWithAvoid, 'catcher', true, false)).toBe(false);
  });

  test('Medical restrictions always apply', () => {
    const restrictedPlayer: Player = {
      id: 'restricted',
      name: 'Restricted Player',
      team_id: 'test-team',
      positionPreferences: {
        pitcher: 'preferred' // Even with preference
      },
      positionRankings: [],
      overallRating: 3,
      pitcher_restriction: true, // Medical restriction
      catcher_restriction: false,
      first_base_restriction: false,
      unavailableForGames: [],
      player_number: 'restricted'
    };

    // Should NOT be able to play pitcher even with preference
    expect(canPlayPosition(restrictedPlayer, 'pitcher', true, true)).toBe(false);
    expect(canPlayPosition(restrictedPlayer, 'pitcher', true, false)).toBe(false);
  });
});
import { describe, it, expect } from 'vitest';
import { generateCompleteLineup } from '@/lib/utils-enhanced';
import { Player } from '@/contexts/TeamContext';

describe('Simple Fairness Demo', () => {
  it('should demonstrate PERFECT equal playing time', () => {
    console.log('\n🎯 === SIMPLE FAIRNESS DEMONSTRATION ===');
    
    const players: Player[] = Array.from({ length: 12 }, (_, i) => ({
      id: `${i + 1}`,
      name: `Player${i + 1}`,
      teamRoles: {}
    }));

    console.log('📋 SCENARIO: 12 players, 7 innings (63 total field positions)');
    console.log('🧮 MATH: 63 ÷ 12 = 5.25 average innings per player');
    console.log('🎯 MATHEMATICAL OPTIMUM: 9 players get 5 innings, 3 players get 6 innings');
    console.log('📏 BEST POSSIBLE RANGE: 1 inning difference (5-6 innings)');

    const innings = generateCompleteLineup(players, 7, {
      rotateLineupEvery: 1,
      rotatePitcherEvery: 2,
      equalPlayingTime: true,  // Enable simple fairness mode
      limitBenchTime: true,
      maxConsecutiveBenchInnings: 2
    });

    // Calculate final playing time
    const playingTime: Record<string, number> = {};
    players.forEach(p => playingTime[p.name] = 0);

    innings.forEach(inning => {
      Object.entries(inning.positions).forEach(([pos, playerName]) => {
        if (pos !== 'bench' && playerName && typeof playerName === 'string') {
          playingTime[playerName]++;
        }
      });
    });

    const times = Object.values(playingTime);
    const maxTime = Math.max(...times);
    const minTime = Math.min(...times);
    const range = maxTime - minTime;

    // Count how many players got each number of innings
    const playersAt5 = times.filter(t => t === 5).length;
    const playersAt6 = times.filter(t => t === 6).length;

    console.log('\n📊 FINAL RESULTS:');
    console.log('Playing time distribution:', playingTime);
    console.log(`Range: ${minTime} to ${maxTime} innings (${range} difference)`);
    console.log(`Distribution: ${playersAt5} players at 5 innings, ${playersAt6} players at 6 innings`);
    
    console.log('\n🏆 SUCCESS CRITERIA:');
    console.log(`✅ Range = ${range} (mathematical optimum is 1)`);
    console.log(`✅ Distribution matches optimum: ${playersAt5 === 9 && playersAt6 === 3 ? 'YES' : 'NO'}`);
    console.log(`✅ Mathematical optimum achieved: ${range === 1 && minTime === 5 && maxTime === 6 ? 'YES' : 'NO'}`);

    // Verify mathematical optimum fairness
    expect(range).toBe(1); // Mathematical optimum: 1 inning difference
    expect(minTime).toBe(5); // Minimum should be 5 innings
    expect(maxTime).toBe(6); // Maximum should be 6 innings
    expect(playersAt5).toBe(9); // 9 players should get 5 innings
    expect(playersAt6).toBe(3); // 3 players should get 6 innings
    
    console.log('\n🎉 PERFECT EQUAL PLAYING TIME ACHIEVED!');
  });

  it('should show the inning-by-inning fairness progression', () => {
    console.log('\n📈 === INNING-BY-INNING FAIRNESS PROGRESSION ===');
    
    const players: Player[] = Array.from({ length: 12 }, (_, i) => ({
      id: `${i + 1}`,
      name: `Player${i + 1}`,
      teamRoles: {}
    }));

    const innings = generateCompleteLineup(players, 7, {
      rotateLineupEvery: 1,
      rotatePitcherEvery: 2,
      equalPlayingTime: true,
      limitBenchTime: true,
      maxConsecutiveBenchInnings: 2
    });

    console.log('\n📋 How the simple algorithm ensures fairness:');
    console.log('Each inning: Assign the 9 players with LEAST playing time to field positions');
    
    // Track progression
    const progressiveStats: Record<string, number> = {};
    players.forEach(p => progressiveStats[p.name] = 0);

    innings.forEach((inning, index) => {
      // Update stats for this inning
      Object.entries(inning.positions).forEach(([pos, playerName]) => {
        if (pos !== 'bench' && playerName && typeof playerName === 'string') {
          progressiveStats[playerName]++;
        }
      });

      // Show the current distribution
      const currentTimes = Object.values(progressiveStats);
      const currentRange = Math.max(...currentTimes) - Math.min(...currentTimes);
      
      console.log(`\nInning ${index + 1}:`);
      console.log(`  Field: [${Object.entries(inning.positions)
        .filter(([pos]) => pos !== 'bench')
        .map(([pos, player]) => `${player}`)
        .join(', ')}]`);
      console.log(`  Bench: [${inning.positions.bench.join(', ')}]`);
      console.log(`  Playing time range: ${Math.min(...currentTimes)}-${Math.max(...currentTimes)} (${currentRange} difference)`);
    });

    const finalTimes = Object.values(progressiveStats);
    const finalRange = Math.max(...finalTimes) - Math.min(...finalTimes);

    console.log(`\n🎯 FINAL FAIRNESS: ${finalRange === 1 ? 'MATHEMATICAL OPTIMUM ACHIEVED!' : `${finalRange} inning difference (optimum is 1)`}`);
    
    expect(finalRange).toBe(1); // Mathematical optimum for 12 players, 7 innings
  });
});
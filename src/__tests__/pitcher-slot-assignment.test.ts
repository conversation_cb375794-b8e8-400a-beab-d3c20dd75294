import { describe, it, expect } from 'vitest';
import { generateMultiGameSeries } from '../lib/multi-game-orchestrator';
import { Player, LineupRules } from '../lib/utils-enhanced';

describe('Pitcher Slot Assignment Tests', () => {
  // Helper function to create test players
  const createTestPlayers = (): Player[] => {
    const players: Player[] = [];
    
    // Create starter
    players.push({
      id: '1',
      name: 'Starter Sam',
      teamRoles: {
        pitcher: 'primary',
        firstBase: 'capable',
        catcher: 'never',
        secondBase: 'capable',
        shortstop: 'capable',
        thirdBase: 'capable',
        leftField: 'capable',
        centerField: 'capable',
        rightField: 'capable'
      },
      pitcherStrategy: {
        role: 'starter',
        inningsPerGame: 3
      }
    });
    
    // Create reliever
    players.push({
      id: '2',
      name: '<PERSON>lie<PERSON> <PERSON>',
      teamRoles: {
        pitcher: 'primary',
        firstBase: 'capable',
        catcher: 'never',
        secondBase: 'capable',
        shortstop: 'capable',
        thirdBase: 'capable',
        leftField: 'capable',
        centerField: 'capable',
        rightField: 'capable'
      },
      pitcherStrategy: {
        role: 'reliever',
        inningsPerGame: 2
      }
    });
    
    // Create closer
    players.push({
      id: '3',
      name: 'Closer Chris',
      teamRoles: {
        pitcher: 'primary',
        firstBase: 'capable',
        catcher: 'never',
        secondBase: 'capable',
        shortstop: 'capable',
        thirdBase: 'capable',
        leftField: 'capable',
        centerField: 'capable',
        rightField: 'capable'
      },
      pitcherStrategy: {
        role: 'closer',
        inningsPerGame: 2
      }
    });
    
    // Create additional pitchers with "any" role
    for (let i = 4; i <= 6; i++) {
      players.push({
        id: i.toString(),
        name: `Pitcher ${i}`,
        teamRoles: {
          pitcher: 'primary',
          firstBase: 'capable',
          catcher: 'never',
          secondBase: 'capable',
          shortstop: 'capable',
          thirdBase: 'capable',
          leftField: 'capable',
          centerField: 'capable',
          rightField: 'capable'
        },
        pitcherStrategy: {
          role: 'any',
          inningsPerGame: 2
        }
      });
    }
    
    // Create field players
    for (let i = 7; i <= 12; i++) {
      players.push({
        id: i.toString(),
        name: `Player ${i}`,
        teamRoles: {
          pitcher: 'never',
          firstBase: 'capable',
          catcher: i === 7 || i === 8 ? 'primary' : 'never', // Players 7 and 8 can catch
          secondBase: 'capable',
          shortstop: 'capable',
          thirdBase: 'capable',
          leftField: 'capable',
          centerField: 'capable',
          rightField: 'capable'
        }
      });
    }
    
    return players;
  };
  
  it('should assign starter to first slot, closer to last slot, and reliever to middle', () => {
    const players = createTestPlayers();
    const rules: LineupRules = {
      rotatePitcherEvery: 2,
      rotateLineupEvery: 2,
      maxConsecutiveBenchInnings: 2,
      respectPositionLockouts: false,
      limitBenchTime: true,
      allowPitcherRotation: true,
      allowCatcherRotation: true
    };
    
    const result = generateMultiGameSeries(players, 1, 6, rules);
    const game = result.games[0];
    
    // Extract pitcher assignments by inning
    const pitchersByInning: Record<number, string> = {};
    game.lineups.forEach((lineup, index) => {
      const pitcher = lineup.positions.pitcher;
      if (pitcher) {
        pitchersByInning[index + 1] = pitcher;
      }
    });
    
    // Verify starter is in first slot (innings 1-2)
    expect(pitchersByInning[1]).toBe('Starter Sam');
    expect(pitchersByInning[2]).toBe('Starter Sam');
    
    // Verify closer is in last slot (innings 5-6)
    expect(pitchersByInning[5]).toBe('Closer Chris');
    expect(pitchersByInning[6]).toBe('Closer Chris');
    
    // Verify middle innings have reliever or other pitchers
    const middlePitcher1 = pitchersByInning[3];
    const middlePitcher2 = pitchersByInning[4];
    expect(middlePitcher1).toBe(middlePitcher2); // Same pitcher for 2 innings
    expect(['Reliever Rick', 'Pitcher 4', 'Pitcher 5', 'Pitcher 6']).toContain(middlePitcher1);
  });
  
  it('should handle 9-inning game with proper slot distribution', () => {
    const players = createTestPlayers();
    const rules: LineupRules = {
      rotatePitcherEvery: 3,
      rotateLineupEvery: 2,
      maxConsecutiveBenchInnings: 2,
      respectPositionLockouts: true
    };
    
    const result = generateMultiGameSeries(players, 1, 9, rules);
    const game = result.games[0];
    
    // Extract pitcher assignments
    const pitchersByInning: Record<number, string> = {};
    game.lineups.forEach((lineup, index) => {
      const pitcher = lineup.positions.pitcher;
      if (pitcher) {
        pitchersByInning[index + 1] = pitcher;
      }
    });
    
    // Verify starter in innings 1-3
    expect(pitchersByInning[1]).toBe('Starter Sam');
    expect(pitchersByInning[2]).toBe('Starter Sam');
    expect(pitchersByInning[3]).toBe('Starter Sam');
    
    // Verify closer in innings 7-9
    expect(pitchersByInning[7]).toBe('Closer Chris');
    expect(pitchersByInning[8]).toBe('Closer Chris');
    expect(pitchersByInning[9]).toBe('Closer Chris');
    
    // Verify middle innings have different pitcher
    const middlePitcher = pitchersByInning[4];
    expect(pitchersByInning[4]).toBe(middlePitcher);
    expect(pitchersByInning[5]).toBe(middlePitcher);
    expect(pitchersByInning[6]).toBe(middlePitcher);
    expect(['Reliever Rick', 'Pitcher 4', 'Pitcher 5', 'Pitcher 6']).toContain(middlePitcher);
  });
  
  it('should handle multiple games with fair pitcher distribution', () => {
    const players = createTestPlayers();
    const rules: LineupRules = {
      rotatePitcherEvery: 2,
      rotateLineupEvery: 2,
      maxConsecutiveBenchInnings: 2,
      respectPositionLockouts: false,
      limitBenchTime: true,
      allowPitcherRotation: true,
      allowCatcherRotation: true
    };
    
    const result = generateMultiGameSeries(players, 3, 6, rules);
    
    // Track pitcher usage across all games
    const pitcherGamesPlayed = new Map<string, Set<number>>();
    
    result.games.forEach((game, gameIndex) => {
      const pitchersInGame = new Set<string>();
      
      game.lineups.forEach(lineup => {
        const pitcher = lineup.positions.pitcher;
        if (pitcher) {
          pitchersInGame.add(pitcher);
        }
      });
      
      pitchersInGame.forEach(pitcher => {
        if (!pitcherGamesPlayed.has(pitcher)) {
          pitcherGamesPlayed.set(pitcher, new Set());
        }
        pitcherGamesPlayed.get(pitcher)!.add(gameIndex);
      });
    });
    
    // Verify all pitchers got to play
    const allPitcherNames = ['Starter Sam', 'Reliever Rick', 'Closer Chris', 'Pitcher 4', 'Pitcher 5', 'Pitcher 6'];
    allPitcherNames.forEach(name => {
      expect(pitcherGamesPlayed.has(name)).toBe(true);
      const gamesPlayed = pitcherGamesPlayed.get(name)!.size;
      expect(gamesPlayed).toBeGreaterThan(0);
      expect(gamesPlayed).toBeLessThanOrEqual(3);
    });
    
    // Verify fair distribution (no pitcher plays all games while others play none)
    const gamesCounts = Array.from(pitcherGamesPlayed.values()).map(s => s.size);
    const minGames = Math.min(...gamesCounts);
    const maxGames = Math.max(...gamesCounts);
    expect(maxGames - minGames).toBeLessThanOrEqual(2); // Reasonable fairness
  });
  
  it('should not have overlapping pitcher assignments in the same game', () => {
    const players = createTestPlayers();
    const rules: LineupRules = {
      rotatePitcherEvery: 2,
      rotateLineupEvery: 2,
      maxConsecutiveBenchInnings: 2,
      respectPositionLockouts: false,
      limitBenchTime: true,
      allowPitcherRotation: true,
      allowCatcherRotation: true
    };
    
    const result = generateMultiGameSeries(players, 2, 8, rules);
    
    result.games.forEach((game, gameIndex) => {
      // Track which innings each pitcher plays
      const pitcherInnings = new Map<string, number[]>();
      
      game.lineups.forEach((lineup, inningIndex) => {
        const pitcher = lineup.positions.pitcher;
        if (pitcher) {
          if (!pitcherInnings.has(pitcher)) {
            pitcherInnings.set(pitcher, []);
          }
          pitcherInnings.get(pitcher)!.push(inningIndex + 1);
        }
      });
      
      // Verify each pitcher's innings are consecutive
      pitcherInnings.forEach((innings, pitcherName) => {
        const sorted = [...innings].sort((a, b) => a - b);
        for (let i = 1; i < sorted.length; i++) {
          expect(sorted[i] - sorted[i-1]).toBe(1);
        }
        
        // Verify they pitch for the expected number of consecutive innings
        expect(innings.length).toBe(rules.rotatePitcherEvery);
      });
      
      // Verify all innings have a pitcher
      for (let i = 1; i <= 8; i++) {
        const pitcher = game.lineups[i - 1].positions.pitcher;
        expect(pitcher).toBeTruthy();
      }
    });
  });
  
  it('should handle edge case with only starter and closer (no middle relievers)', () => {
    const players: Player[] = [];
    
    // Create only starter and closer
    players.push({
      id: '1',
      name: 'Starter Only',
      teamRoles: {
        pitcher: 'primary',
        firstBase: 'capable',
        catcher: 'never',
        secondBase: 'capable',
        shortstop: 'capable',
        thirdBase: 'capable',
        leftField: 'capable',
        centerField: 'capable',
        rightField: 'capable'
      },
      pitcherStrategy: {
        role: 'starter',
        inningsPerGame: 3
      }
    });
    
    players.push({
      id: '2',
      name: 'Closer Only',
      teamRoles: {
        pitcher: 'primary',
        firstBase: 'capable',
        catcher: 'never',
        secondBase: 'capable',
        shortstop: 'capable',
        thirdBase: 'capable',
        leftField: 'capable',
        centerField: 'capable',
        rightField: 'capable'
      },
      pitcherStrategy: {
        role: 'closer',
        inningsPerGame: 3
      }
    });
    
    // Add one "any" role pitcher for middle innings
    players.push({
      id: '3',
      name: 'Middle Pitcher',
      teamRoles: {
        pitcher: 'primary',
        firstBase: 'capable',
        catcher: 'never',
        secondBase: 'capable',
        shortstop: 'capable',
        thirdBase: 'capable',
        leftField: 'capable',
        centerField: 'capable',
        rightField: 'capable'
      }
    });
    
    // Add field players
    for (let i = 4; i <= 9; i++) {
      players.push({
        id: i.toString(),
        name: `Player ${i}`,
        teamRoles: {
          pitcher: 'never',
          firstBase: 'capable',
          catcher: i === 4 || i === 5 ? 'primary' : 'never', // Players 4 and 5 can catch
          secondBase: 'capable',
          shortstop: 'capable',
          thirdBase: 'capable',
          leftField: 'capable',
          centerField: 'capable',
          rightField: 'capable'
        }
      });
    }
    
    const rules: LineupRules = {
      rotatePitcherEvery: 2,
      rotateLineupEvery: 2,
      maxConsecutiveBenchInnings: 2,
      respectPositionLockouts: false,
      limitBenchTime: true,
      allowPitcherRotation: true,
      allowCatcherRotation: true
    };
    
    const result = generateMultiGameSeries(players, 1, 6, rules);
    const game = result.games[0];
    
    // Extract pitcher assignments
    const pitchersByInning: Record<number, string> = {};
    game.lineups.forEach((lineup, index) => {
      const pitcher = lineup.positions.pitcher;
      if (pitcher) {
        pitchersByInning[index + 1] = pitcher;
      }
    });
    
    // Verify assignments
    expect(pitchersByInning[1]).toBe('Starter Only');
    expect(pitchersByInning[2]).toBe('Starter Only');
    expect(pitchersByInning[3]).toBe('Middle Pitcher');
    expect(pitchersByInning[4]).toBe('Middle Pitcher');
    expect(pitchersByInning[5]).toBe('Closer Only');
    expect(pitchersByInning[6]).toBe('Closer Only');
  });
});
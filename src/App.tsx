
import { useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import { TeamProvider } from "./contexts/TeamContext";
import { initializePerformance } from "@/config/performance";
import { MobileWrapper } from "@/components/MobileWrapper";

import HomePage from "./pages/HomePage";
import Dashboard from "./pages/Dashboard";
import NotFound from "./pages/NotFound";
import TeamRoster from "./pages/TeamRoster";
import TeamManagement from "./pages/TeamManagement";
import CreateLineup from "./pages/CreateLineup";
import BatchGameCreation from "./pages/BatchGameCreation";
import BatchLineupGeneration from "./pages/BatchLineupGeneration";
import ViewBatchSeries from "./pages/ViewBatchSeries";
import PitcherSettings from "./pages/PitcherSettings";
import SetLineupAttendance from "./pages/SetLineupAttendance";
import SimpleSetFirstInning from "./pages/SimpleSetFirstInning";
import BasicSetFirstInning from "./pages/BasicSetFirstInning";
import BattingOrder from "./pages/BattingOrder";
import ViewLineup from "./pages/ViewLineup";
import EditInning from "./pages/EditInning";
import RotationRules from "./pages/RotationRules";
import UserProfile from "./pages/UserProfile";
// FEATURE_FLAG assistantCoachAccess false
// import AssistantCoaches from "./pages/AssistantCoaches";
import PlayerStats from "./pages/PlayerStats";
import FAQ from "./pages/FAQ";
import Contact from "./pages/Contact";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import Pricing from "./pages/Pricing";
import PaymentSuccess from "./pages/PaymentSuccess";
import PaymentCanceled from "./pages/PaymentCanceled";
import ProtectedRoute from "./components/ProtectedRoute";
import AdminRoute from "./components/AdminRoute";
import ErrorBoundary from "./components/ErrorBoundary";
import TestSupabaseConnection from "./test-supabase-connection";
import RunMigrations from "./run-migrations";
import DemoLogin from "./pages/DemoLogin";

import SignIn from "./pages/SignIn";
import SignUp from "./pages/SignUp";
import ResetPassword from "./pages/ResetPassword";
import AuthCallback from "./pages/AuthCallback";
import AdminAccess from "./pages/AdminAccess";
import TestDemo from "./pages/TestDemo";
import TestLogin from "./pages/TestLogin";
import AdminLogin from "./pages/AdminLogin";
import TestPage from "./pages/TestPage";

// Admin pages
import AdminDashboard from "./pages/AdminDashboard";
import AdminUsers from "./pages/AdminUsers";
import AdminBilling from "./pages/AdminBilling";
import AdminTeams from "./pages/AdminTeams";
import AdminSettings from "./pages/AdminSettings";
import AdminAuditLogs from "./pages/AdminAuditLogs";
import AdminMaintenance from "./pages/AdminMaintenance";

const queryClient = new QueryClient();

const App = () => {
  // Initialize performance optimizations
  useEffect(() => {
    initializePerformance();
  }, []);
  
  return (
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <AuthProvider>
          <TeamProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter
              future={{
                v7_startTransition: true,
                v7_relativeSplatPath: true
              }}
            >
            <MobileWrapper>
              <Routes>
                {/* Public routes */}
                <Route path="/" element={<HomePage />} />
              <Route path="/test" element={<TestPage />} />
              <Route path="/pricing" element={<Pricing />} />
              <Route path="/faq" element={<FAQ />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/privacy-policy" element={<PrivacyPolicy />} />
              <Route path="/payment-success" element={<PaymentSuccess />} />
              <Route path="/payment-canceled" element={<PaymentCanceled />} />
              <Route path="/test-connection" element={<TestSupabaseConnection />} />
              <Route path="/run-migrations" element={<RunMigrations />} />
              <Route path="/demo-login" element={<DemoLogin />} />
              <Route path="/sign-in" element={<SignIn />} />
              <Route path="/signup" element={<SignUp />} />
              <Route path="/reset-password" element={<ResetPassword />} />
              <Route path="/auth/callback" element={<AuthCallback />} />
              <Route path="/admin-access" element={<AdminAccess />} />
              <Route path="/admin-login" element={<AdminLogin />} />
              <Route path="/test-demo" element={<TestDemo />} />
              <Route path="/test-login" element={<TestLogin />} />

              {/* Protected routes - require authentication and payment */}
              <Route path="/dashboard" element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
              <Route path="/team-management" element={<ProtectedRoute><TeamManagement /></ProtectedRoute>} />
              <Route path="/team-roster" element={<ProtectedRoute><TeamRoster /></ProtectedRoute>} />
              <Route path="/pitcher-settings" element={<ProtectedRoute><PitcherSettings /></ProtectedRoute>} />
              <Route path="/create-lineup" element={<ProtectedRoute><CreateLineup /></ProtectedRoute>} />
              <Route path="/batch-games" element={<ProtectedRoute><BatchGameCreation /></ProtectedRoute>} />
              <Route path="/batch-lineup-generation" element={<ProtectedRoute><BatchLineupGeneration /></ProtectedRoute>} />
              <Route path="/view-batch-series" element={<ProtectedRoute><ViewBatchSeries /></ProtectedRoute>} />
              <Route path="/set-lineup-attendance" element={<ProtectedRoute><SetLineupAttendance /></ProtectedRoute>} />
              <Route path="/set-first-inning" element={<ProtectedRoute><SimpleSetFirstInning /></ProtectedRoute>} />
              <Route path="/basic-set-first-inning" element={<ProtectedRoute><BasicSetFirstInning /></ProtectedRoute>} />
              <Route path="/batting-order" element={<ProtectedRoute><BattingOrder /></ProtectedRoute>} />
              <Route path="/view-lineup/:id" element={<ProtectedRoute><ViewLineup /></ProtectedRoute>} />
              <Route path="/edit-inning" element={<ProtectedRoute><EditInning /></ProtectedRoute>} />
              <Route path="/rotation-rules" element={<ProtectedRoute><RotationRules /></ProtectedRoute>} />
              <Route path="/user-profile" element={<ProtectedRoute><UserProfile /></ProtectedRoute>} />
              {/* FEATURE_FLAG assistantCoachAccess false */}
              {/* <Route path="/assistant-coaches" element={<ProtectedRoute><AssistantCoaches /></ProtectedRoute>} /> */}
              <Route path="/player-stats/:playerId" element={<ProtectedRoute><PlayerStats /></ProtectedRoute>} />

              {/* Admin routes - require admin privileges */}
              <Route path="/admin" element={<AdminRoute><AdminDashboard /></AdminRoute>} />
              <Route path="/admin/users" element={<AdminRoute><AdminUsers /></AdminRoute>} />
              <Route path="/admin/billing" element={<AdminRoute><AdminBilling /></AdminRoute>} />
              <Route path="/admin/teams" element={<AdminRoute><AdminTeams /></AdminRoute>} />
              <Route path="/admin/audit-logs" element={<AdminRoute><AdminAuditLogs /></AdminRoute>} />
              <Route path="/admin/settings" element={<AdminRoute><AdminSettings /></AdminRoute>} />
              <Route path="/admin/maintenance" element={<AdminRoute><AdminMaintenance /></AdminRoute>} />

              {/* Catch all route */}
              <Route path="*" element={<NotFound />} />
            </Routes>
            </MobileWrapper>
          </BrowserRouter>
        </TeamProvider>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
  </ErrorBoundary>
  );
};

export default App;

import { describe, it, expect, beforeEach } from 'vitest';
import { 
  LineupRotator, 
  PlayerEligibilityCache, 
  SeededRandom,
  generateLineupFromFirstInning,
  rotatePlayersForNextInning
} from '../utils-enhanced';
import type { Player, InningLineup, LineupRules } from '../utils-enhanced';

// Mock data for testing
const createMockPlayer = (name: string, id: string, restrictions?: string[]): Player => ({
  id,
  name,
  team_id: 'test-team',
  attendance: 'present',
  positionRestrictions: restrictions,
  positionPreferences: {
    pitcher: 'neutral',
    catcher: 'neutral',
    firstBase: 'neutral',
    secondBase: 'neutral',
    thirdBase: 'neutral',
    shortstop: 'neutral',
    leftField: 'neutral',
    centerField: 'neutral',
    rightField: 'neutral'
  }
});

const createFirstInning = (fieldPlayers: string[], benchPlayers: string[]): InningLineup => ({
  inning: 1,
  positions: {
    pitcher: fieldPlayers[0] || '',
    catcher: fieldPlayers[1] || '',
    firstBase: fieldPlayers[2] || '',
    secondBase: fieldPlayers[3] || '',
    thirdBase: fieldPlayers[4] || '',
    shortstop: fieldPlayers[5] || '',
    leftField: fieldPlayers[6] || '',
    centerField: fieldPlayers[7] || '',
    rightField: fieldPlayers[8] || '',
    bench: benchPlayers
  }
});

describe('Baseball Lineup Rotation Algorithm', () => {
  let players: Player[];
  let eligibilityCache: PlayerEligibilityCache;
  let random: SeededRandom;
  
  beforeEach(() => {
    // Create 12 players for testing
    players = Array.from({ length: 12 }, (_, i) => 
      createMockPlayer(`Player${i + 1}`, `player-${i + 1}`)
    );
    
    eligibilityCache = new PlayerEligibilityCache();
    random = new SeededRandom(42);
  });

  describe('LineupRotator - Basic Rotation', () => {
    it('should rotate players when rotateLineupEvery is 1', () => {
      const rules: LineupRules = {
        rotateLineupEvery: 1,
        limitBenchTime: true,
        maxConsecutiveBenchInnings: 2,
        allowPitcherRotation: false,
        allowCatcherRotation: false,
        respectPositionLockouts: false,
        equalPlayingTime: true
      };

      const rotator = new LineupRotator(rules, eligibilityCache, random);
      
      // Create first inning with specific players
      const firstInning = createFirstInning(
        ['Player1', 'Player2', 'Player3', 'Player4', 'Player5', 'Player6', 'Player7', 'Player8', 'Player9'],
        ['Player10', 'Player11', 'Player12']
      );

      // Rotate to second inning
      const secondInning = rotator.rotateLineup(firstInning, players, 2);
      
      // Verify rotation happened - at least one field player should be different
      const firstFieldPlayers = Object.values(firstInning.positions).filter(p => typeof p === 'string' && p !== '');
      const secondFieldPlayers = Object.values(secondInning.positions).filter(p => typeof p === 'string' && p !== '');
      
      const changedPositions = firstFieldPlayers.filter((player, index) => 
        player !== secondFieldPlayers[index]
      );
      
      expect(changedPositions.length).toBeGreaterThan(0);
      console.log(`Rotation with rotateLineupEvery=1: ${changedPositions.length} positions changed`);
    });

    it('should NOT rotate players when rotateLineupEvery is 2 on even innings', () => {
      const rules: LineupRules = {
        rotateLineupEvery: 2,
        limitBenchTime: true,
        maxConsecutiveBenchInnings: 2,
        allowPitcherRotation: false,
        allowCatcherRotation: false,
        respectPositionLockouts: false,
        equalPlayingTime: true
      };

      const rotator = new LineupRotator(rules, eligibilityCache, random);
      
      const firstInning = createFirstInning(
        ['Player1', 'Player2', 'Player3', 'Player4', 'Player5', 'Player6', 'Player7', 'Player8', 'Player9'],
        ['Player10', 'Player11', 'Player12']
      );

      // Rotate to second inning (should NOT rotate since (2-1) % 2 = 1)
      const secondInning = rotator.rotateLineup(firstInning, players, 2);
      
      // Verify NO rotation happened
      expect(secondInning.positions.pitcher).toBe(firstInning.positions.pitcher);
      expect(secondInning.positions.catcher).toBe(firstInning.positions.catcher);
      expect(secondInning.positions.firstBase).toBe(firstInning.positions.firstBase);
      
      console.log('Rotation with rotateLineupEvery=2 on inning 2: No rotation expected');
    });

    it('should rotate players when rotateLineupEvery is 2 on odd innings after first', () => {
      const rules: LineupRules = {
        rotateLineupEvery: 2,
        limitBenchTime: false, // Disable bench time limit to isolate rotation frequency
        allowPitcherRotation: false,
        allowCatcherRotation: false,
        respectPositionLockouts: false,
        equalPlayingTime: true
      };

      const rotator = new LineupRotator(rules, eligibilityCache, random);
      
      const secondInning = createFirstInning(
        ['Player1', 'Player2', 'Player3', 'Player4', 'Player5', 'Player6', 'Player7', 'Player8', 'Player9'],
        ['Player10', 'Player11', 'Player12']
      );
      secondInning.inning = 2;

      // Rotate to third inning (should rotate since (3-1) % 2 = 0)
      const thirdInning = rotator.rotateLineup(secondInning, players, 3);
      
      // Verify rotation happened
      const secondFieldPlayers = Object.values(secondInning.positions).filter(p => typeof p === 'string' && p !== '');
      const thirdFieldPlayers = Object.values(thirdInning.positions).filter(p => typeof p === 'string' && p !== '');
      
      const changedPositions = secondFieldPlayers.filter((player, index) => 
        player !== thirdFieldPlayers[index]
      );
      
      expect(changedPositions.length).toBeGreaterThan(0);
      console.log(`Rotation with rotateLineupEvery=2 on inning 3: ${changedPositions.length} positions changed`);
    });
  });

  describe('LineupRotator - Forced Rotation for Bench Time', () => {
    it('should force rotation when players exceed max bench time', () => {
      const rules: LineupRules = {
        rotateLineupEvery: 3, // Normally would not rotate
        limitBenchTime: true,
        maxConsecutiveBenchInnings: 2,
        allowPitcherRotation: false,
        allowCatcherRotation: false,
        respectPositionLockouts: false,
        equalPlayingTime: true
      };

      const rotator = new LineupRotator(rules, eligibilityCache, random);
      
      // Create previous innings where Player10 has been on bench for 2 innings
      const firstInning = createFirstInning(
        ['Player1', 'Player2', 'Player3', 'Player4', 'Player5', 'Player6', 'Player7', 'Player8', 'Player9'],
        ['Player10', 'Player11', 'Player12']
      );
      
      const secondInning = { ...firstInning, inning: 2 };
      
      // Third inning - normally wouldn't rotate (3 % 3 != 1), but should force due to bench time
      const allInnings = [firstInning, secondInning];
      const thirdInning = rotator.rotateLineup(secondInning, players, 3, undefined, allInnings);
      
      // Verify that at least one bench player moved to field
      const benchPlayersMoved = firstInning.positions.bench.filter(
        player => !thirdInning.positions.bench.includes(player)
      );
      
      expect(benchPlayersMoved.length).toBeGreaterThan(0);
      console.log(`Forced rotation due to bench time: ${benchPlayersMoved.join(', ')} moved to field`);
    });
  });

  describe('LineupRotator - Pitcher Rotation', () => {
    it('should rotate pitcher according to rotatePitcherEvery setting', () => {
      const rules: LineupRules = {
        rotateLineupEvery: 1,
        limitBenchTime: true,
        allowPitcherRotation: true,
        rotatePitcherEvery: 3,
        allowCatcherRotation: false,
        respectPositionLockouts: false,
        equalPlayingTime: true
      };

      const rotator = new LineupRotator(rules, eligibilityCache, random);
      
      // Make Player10 eligible for pitcher
      players[9].positionPreferences!.pitcher = 'preferred';
      
      const firstInning = createFirstInning(
        ['Player1', 'Player2', 'Player3', 'Player4', 'Player5', 'Player6', 'Player7', 'Player8', 'Player9'],
        ['Player10', 'Player11', 'Player12']
      );

      // Innings 2 and 3 should not rotate pitcher
      const secondInning = rotator.rotateLineup(firstInning, players, 2);
      expect(secondInning.positions.pitcher).toBe(firstInning.positions.pitcher);
      
      const thirdInning = rotator.rotateLineup(secondInning, players, 3);
      expect(thirdInning.positions.pitcher).toBe(firstInning.positions.pitcher);
      
      // Inning 4 should rotate pitcher (4 % 3 = 1)
      const fourthInning = rotator.rotateLineup(thirdInning, players, 4);
      expect(fourthInning.positions.pitcher).not.toBe(firstInning.positions.pitcher);
      
      console.log(`Pitcher rotation: ${firstInning.positions.pitcher} -> ${fourthInning.positions.pitcher} at inning 4`);
    });
  });

  describe('generateLineupFromFirstInning - Full Game Generation', () => {
    it('should generate correct number of innings with proper rotation', () => {
      const rules: LineupRules = {
        rotateLineupEvery: 1,
        limitBenchTime: true,
        maxConsecutiveBenchInnings: 2,
        allowPitcherRotation: false,
        allowCatcherRotation: false,
        respectPositionLockouts: false,
        equalPlayingTime: true,
        _randomSeed: 42
      };

      const firstInning = createFirstInning(
        ['Player1', 'Player2', 'Player3', 'Player4', 'Player5', 'Player6', 'Player7', 'Player8', 'Player9'],
        ['Player10', 'Player11', 'Player12']
      );

      const lineup = generateLineupFromFirstInning(firstInning, players, 7, rules);
      
      expect(lineup.length).toBe(7);
      expect(lineup[0]).toEqual(firstInning);
      
      // Verify rotations happened in subsequent innings
      let totalRotations = 0;
      for (let i = 1; i < lineup.length; i++) {
        const prevInning = lineup[i - 1];
        const currInning = lineup[i];
        
        const prevFieldPlayers = Object.values(prevInning.positions)
          .filter(p => typeof p === 'string' && p !== '');
        const currFieldPlayers = Object.values(currInning.positions)
          .filter(p => typeof p === 'string' && p !== '');
        
        const changes = prevFieldPlayers.filter((player, index) => 
          player !== currFieldPlayers[index]
        ).length;
        
        if (changes > 0) totalRotations++;
        console.log(`Inning ${i} -> ${i + 1}: ${changes} position changes`);
      }
      
      expect(totalRotations).toBeGreaterThan(0);
      console.log(`Total innings with rotations: ${totalRotations}`);
    });

    it('should track bench time correctly across innings', () => {
      const rules: LineupRules = {
        rotateLineupEvery: 2,
        limitBenchTime: true,
        maxConsecutiveBenchInnings: 2,
        allowPitcherRotation: false,
        allowCatcherRotation: false,
        respectPositionLockouts: false,
        equalPlayingTime: true,
        _randomSeed: 42
      };

      const firstInning = createFirstInning(
        ['Player1', 'Player2', 'Player3', 'Player4', 'Player5', 'Player6', 'Player7', 'Player8', 'Player9'],
        ['Player10', 'Player11', 'Player12']
      );

      const lineup = generateLineupFromFirstInning(firstInning, players, 5, rules);
      
      // Track bench time for each player
      const benchTime: Record<string, number> = {};
      players.forEach(p => benchTime[p.name] = 0);
      
      lineup.forEach(inning => {
        inning.positions.bench.forEach(player => {
          benchTime[player]++;
        });
      });
      
      // No player should be on bench for more than maxConsecutiveBenchInnings in a row
      const maxConsecutiveBench: Record<string, number> = {};
      players.forEach(p => maxConsecutiveBench[p.name] = 0);
      
      for (let playerName of players.map(p => p.name)) {
        let currentStreak = 0;
        for (let inning of lineup) {
          if (inning.positions.bench.includes(playerName)) {
            currentStreak++;
            maxConsecutiveBench[playerName] = Math.max(maxConsecutiveBench[playerName], currentStreak);
          } else {
            currentStreak = 0;
          }
        }
      }
      
      // Check that no player exceeds max consecutive bench innings
      Object.entries(maxConsecutiveBench).forEach(([player, streak]) => {
        if (streak > 0) {
          console.log(`${player}: max consecutive bench innings = ${streak}`);
          expect(streak).toBeLessThanOrEqual(rules.maxConsecutiveBenchInnings! + 1); // +1 for edge cases
        }
      });
    });
  });

  describe('Position Preferences in Rotation', () => {
    it('should respect position preferences during rotation', () => {
      // Set up position preferences
      players[9].positionPreferences = {
        ...players[9].positionPreferences,
        firstBase: 'preferred',
        pitcher: 'avoid'
      };
      
      players[10].positionPreferences = {
        ...players[10].positionPreferences,
        centerField: 'preferred',
        catcher: 'avoid'
      };

      const rules: LineupRules = {
        rotateLineupEvery: 1,
        limitBenchTime: true,
        maxConsecutiveBenchInnings: 2,
        allowPitcherRotation: false,
        allowCatcherRotation: false,
        respectPositionLockouts: false,
        equalPlayingTime: true
      };

      const rotator = new LineupRotator(rules, eligibilityCache, random);
      
      const firstInning = createFirstInning(
        ['Player1', 'Player2', 'Player3', 'Player4', 'Player5', 'Player6', 'Player7', 'Player8', 'Player9'],
        ['Player10', 'Player11', 'Player12']
      );

      const secondInning = rotator.rotateLineup(firstInning, players, 2);
      
      // Check if Player10 (prefers centerField) got assigned to centerField if rotated in
      if (!secondInning.positions.bench.includes('Player10')) {
        console.log(`Player10 assigned to: ${Object.entries(secondInning.positions).find(([_, p]) => p === 'Player10')?.[0]}`);
      }
      
      // Check if Player11 avoids catcher position
      if (!secondInning.positions.bench.includes('Player11')) {
        expect(secondInning.positions.catcher).not.toBe('Player11');
      }
    });
  });

  describe('Edge Cases', () => {
    it('should handle minimum number of players (8)', () => {
      const minPlayers = players.slice(0, 8);
      const rules: LineupRules = {
        rotateLineupEvery: 1,
        limitBenchTime: false,
        allowPitcherRotation: false,
        allowCatcherRotation: false,
        respectPositionLockouts: false,
        equalPlayingTime: true
      };

      const rotator = new LineupRotator(rules, eligibilityCache, random);
      
      const firstInning = createFirstInning(
        minPlayers.map(p => p.name),
        [] // No bench players with minimum roster
      );

      // Should still work but no rotation possible
      const secondInning = rotator.rotateLineup(firstInning, minPlayers, 2);
      
      // All positions should still be filled
      const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'thirdBase', 'shortstop', 'leftField', 'centerField', 'rightField'];
      fieldPositions.forEach(pos => {
        expect(secondInning.positions[pos as keyof typeof secondInning.positions]).toBeTruthy();
      });
      
      console.log('Minimum players test: All positions filled with 8 players');
    });

    it('should handle position restrictions correctly', () => {
      // Player10 can only play outfield
      players[9].positionRestrictions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'thirdBase', 'shortstop'];
      
      const rules: LineupRules = {
        rotateLineupEvery: 1,
        limitBenchTime: true,
        maxConsecutiveBenchInnings: 1,
        allowPitcherRotation: false,
        allowCatcherRotation: false,
        respectPositionLockouts: true,
        equalPlayingTime: true
      };

      const rotator = new LineupRotator(rules, eligibilityCache, random);
      
      const firstInning = createFirstInning(
        ['Player1', 'Player2', 'Player3', 'Player4', 'Player5', 'Player6', 'Player7', 'Player8', 'Player9'],
        ['Player10', 'Player11', 'Player12']
      );

      const secondInning = rotator.rotateLineup(firstInning, players, 2);
      
      // If Player10 is in the field, should only be in outfield
      if (!secondInning.positions.bench.includes('Player10')) {
        const player10Position = Object.entries(secondInning.positions)
          .find(([_, p]) => p === 'Player10')?.[0];
        
        if (player10Position && player10Position !== 'bench') {
          expect(['leftField', 'centerField', 'rightField']).toContain(player10Position);
          console.log(`Player10 with infield restrictions assigned to: ${player10Position}`);
        }
      }
    });
  });
});

describe('Bug Reproduction - Players Not Rotating', () => {
  it('should identify why players are not rotating between innings', () => {
    const rules: LineupRules = {
      rotateLineupEvery: 1, // Should rotate every inning
      limitBenchTime: true,
      maxConsecutiveBenchInnings: 2,
      allowPitcherRotation: false,
      allowCatcherRotation: false,
      respectPositionLockouts: false,
      equalPlayingTime: true,
      _randomSeed: 42
    };

    const players = Array.from({ length: 12 }, (_, i) => 
      createMockPlayer(`Player${i + 1}`, `player-${i + 1}`)
    );

    const firstInning = createFirstInning(
      ['Player1', 'Player2', 'Player3', 'Player4', 'Player5', 'Player6', 'Player7', 'Player8', 'Player9'],
      ['Player10', 'Player11', 'Player12']
    );

    // Test the main entry point used by UI
    const lineup = generateLineupFromFirstInning(firstInning, players, 5, rules);
    
    // Detailed analysis of each inning transition
    console.log('\n=== ROTATION BUG ANALYSIS ===');
    for (let i = 0; i < lineup.length - 1; i++) {
      const curr = lineup[i];
      const next = lineup[i + 1];
      
      console.log(`\nInning ${i + 1} -> ${i + 2}:`);
      
      // Check each position
      const positions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'thirdBase', 
                        'shortstop', 'leftField', 'centerField', 'rightField'];
      
      let changes = 0;
      positions.forEach(pos => {
        const currPlayer = (curr.positions as any)[pos];
        const nextPlayer = (next.positions as any)[pos];
        if (currPlayer !== nextPlayer) {
          changes++;
          console.log(`  ${pos}: ${currPlayer} -> ${nextPlayer}`);
        }
      });
      
      // Check bench changes
      const benchChanges = curr.positions.bench.filter(
        p => !next.positions.bench.includes(p)
      );
      
      console.log(`  Total position changes: ${changes}`);
      console.log(`  Players moved from bench: ${benchChanges.join(', ') || 'none'}`);
      
      if (changes === 0) {
        console.log('  ⚠️ NO ROTATION OCCURRED!');
      }
    }
    
    // Expect at least some rotations
    const hasRotation = lineup.some((inning, i) => {
      if (i === 0) return false;
      const prev = lineup[i - 1];
      return Object.keys(inning.positions).some(pos => 
        pos !== 'bench' && inning.positions[pos as keyof typeof inning.positions] !== 
        prev.positions[pos as keyof typeof prev.positions]
      );
    });
    
    expect(hasRotation).toBe(true);
  });
});
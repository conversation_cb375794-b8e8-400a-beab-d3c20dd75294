import { LineupRequest, ValidationResult, FIELD_POSITIONS } from '../types';

/**
 * Pre-generation validator - Checks if lineup generation is possible
 */
export class PreGenerationValidator {
  validate(request: LineupRequest): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Check that we have pools for all required positions
    const providedPositions = new Set(request.pools.map(p => p.position));
    const missingPositions = FIELD_POSITIONS.filter(pos => !providedPositions.has(pos));
    
    if (missingPositions.length > 0) {
      errors.push(`Missing position pools for: ${missingPositions.join(', ')}`);
    }
    
    // Check each position has at least one available player
    for (const pool of request.pools) {
      const availableInPool = pool.players.filter(p => 
        request.availablePlayers.includes(p.playerId)
      );
      
      if (availableInPool.length === 0) {
        errors.push(`No available players for ${pool.position}`);
      } else if (availableInPool.length === 1) {
        warnings.push(`Only one player available for ${pool.position}`);
      }
    }
    
    // Check we have enough unique players total
    const uniqueAvailable = new Set(request.availablePlayers);
    if (uniqueAvailable.size < 9) {
      errors.push(`Need at least 9 players, have ${uniqueAvailable.size}`);
    }
    
    // In competitive mode, ensure all available players are in at least one pool
    if (request.mode === 'competitive') {
      const playersInPools = new Set<string>();
      for (const pool of request.pools) {
        pool.players.forEach(p => playersInPools.add(p.playerId));
      }
      
      const unassignedPlayers = request.availablePlayers.filter(id => !playersInPools.has(id));
      if (unassignedPlayers.length > 0) {
        warnings.push(`${unassignedPlayers.length} available players have no position assignments in competitive mode`);
      }
    }
    
    // Check for duplicate player entries in same position
    for (const pool of request.pools) {
      const playerIds = pool.players.map(p => p.playerId);
      const uniqueIds = new Set(playerIds);
      if (playerIds.length !== uniqueIds.size) {
        errors.push(`Duplicate players in ${pool.position} pool`);
      }
    }
    
    // Validate options
    if (request.options.minPlayingTime < 0 || request.options.minPlayingTime > 100) {
      errors.push(`Invalid minPlayingTime: ${request.options.minPlayingTime}. Must be 0-100`);
    }
    
    if (request.options.maxBenchInnings < 0) {
      errors.push(`Invalid maxBenchInnings: ${request.options.maxBenchInnings}. Must be >= 0`);
    }
    
    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }
}
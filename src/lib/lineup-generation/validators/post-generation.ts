import { Lineup, LineupRequest, ValidationResult, FIELD_POSITIONS } from '../types';
import { PositionRules } from '../rules/position-rules';

/**
 * Post-generation validator - Verifies the generated lineup is valid
 */
export class PostGenerationValidator {
  validate(lineup: Lineup, request: LineupRequest): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const rules = new PositionRules(request.pools);
    
    // Check all positions are filled
    for (const position of FIELD_POSITIONS) {
      if (!lineup.assignments.has(position)) {
        errors.push(`Position ${position} is empty`);
      }
    }
    
    // Check for duplicate assignments
    const assignedPlayers = new Set<string>();
    const playerPositions = new Map<string, string>();
    
    for (const [position, playerId] of lineup.assignments) {
      if (assignedPlayers.has(playerId)) {
        errors.push(`Player ${playerId} assigned to multiple positions: ${playerPositions.get(playerId)} and ${position}`);
      }
      assignedPlayers.add(playerId);
      playerPositions.set(playerId, position);
    }
    
    // Verify all assigned players were in available list
    for (const playerId of assignedPlayers) {
      if (!request.availablePlayers.includes(playerId)) {
        errors.push(`Player ${playerId} was assigned but not in available players list`);
      }
    }
    
    // CRITICAL: Verify position eligibility (fixes competitive mode bug)
    for (const [position, playerId] of lineup.assignments) {
      if (!rules.canPlayerPlayPosition(playerId, position)) {
        const pool = request.pools.find(p => p.position === position);
        const player = pool?.players.find(p => p.playerId === playerId);
        const playerName = player?.name || playerId;
        errors.push(`CRITICAL: ${playerName} assigned to ${position} but not in that position's pool`);
      }
    }
    
    // Check bench players
    const benchSet = new Set(lineup.benchPlayers);
    
    // No player should be both on field and bench
    for (const benchPlayer of lineup.benchPlayers) {
      if (assignedPlayers.has(benchPlayer)) {
        errors.push(`Player ${benchPlayer} is both on field and bench`);
      }
    }
    
    // All available players should be either on field or bench
    const allAssigned = new Set([...assignedPlayers, ...benchSet]);
    for (const availablePlayer of request.availablePlayers) {
      if (!allAssigned.has(availablePlayer)) {
        warnings.push(`Available player ${availablePlayer} not assigned to field or bench`);
      }
    }
    
    // Verify total player count
    const totalPlayers = assignedPlayers.size + benchSet.size;
    if (totalPlayers !== request.availablePlayers.length) {
      warnings.push(`Player count mismatch: ${totalPlayers} assigned vs ${request.availablePlayers.length} available`);
    }
    
    // Check batting order if provided
    if (lineup.battingOrder) {
      const battingSet = new Set(lineup.battingOrder);
      
      // All field players should be in batting order
      for (const fieldPlayer of assignedPlayers) {
        if (!battingSet.has(fieldPlayer)) {
          warnings.push(`Field player ${fieldPlayer} not in batting order`);
        }
      }
      
      // No bench players in batting order
      for (const battingPlayer of lineup.battingOrder) {
        if (benchSet.has(battingPlayer)) {
          errors.push(`Bench player ${battingPlayer} in batting order`);
        }
      }
      
      // Correct number in batting order
      if (lineup.battingOrder.length !== 9 && lineup.battingOrder.length !== 10) {
        warnings.push(`Unusual batting order length: ${lineup.battingOrder.length}`);
      }
    }
    
    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }
}
import { PositionPool } from '../types';

/**
 * Position Rules - Simple and clear
 * If a player is in a position's pool, they can play there. Period.
 * No complex logic, no legacy preferences, just pools.
 */
export class PositionRules {
  private positionMap: Map<string, Set<string>>; // position -> Set of playerIds
  private playerMap: Map<string, Set<string>>;  // playerId -> Set of positions
  
  constructor(private pools: PositionPool[]) {
    this.positionMap = new Map();
    this.playerMap = new Map();
    
    // Build lookup maps for fast access
    for (const pool of pools) {
      const playerSet = new Set<string>();
      
      for (const player of pool.players) {
        playerSet.add(player.playerId);
        
        // Update player -> positions map
        if (!this.playerMap.has(player.playerId)) {
          this.playerMap.set(player.playerId, new Set());
        }
        this.playerMap.get(player.playerId)!.add(pool.position);
      }
      
      this.positionMap.set(pool.position, playerSet);
    }
  }

  /**
   * SIMPLIFIED: If player is in pool, they can play there
   */
  canPlayerPlayPosition(playerId: string, position: string): boolean {
    const pool = this.positionMap.get(position);
    return pool ? pool.has(playerId) : false;
  }

  /**
   * Get all players who can play a position, sorted by priority
   */
  getPlayersForPosition(position: string): Array<{playerId: string; name: string; priority: number}> {
    const pool = this.pools.find(p => p.position === position);
    if (!pool) return [];
    
    return pool.players
      .sort((a, b) => a.priority - b.priority)
      .map(p => ({ 
        playerId: p.playerId, 
        name: p.name,
        priority: p.priority 
      }));
  }

  /**
   * Get all positions a player can play
   */
  getPositionsForPlayer(playerId: string): string[] {
    const positions = this.playerMap.get(playerId);
    return positions ? Array.from(positions) : [];
  }

  /**
   * Count how many positions each player can play
   */
  getPlayerFlexibility(): Map<string, number> {
    const flexibility = new Map<string, number>();
    
    for (const [playerId, positions] of this.playerMap) {
      flexibility.set(playerId, positions.size);
    }
    
    return flexibility;
  }

  /**
   * Find positions with limited options
   */
  getConstrainedPositions(threshold: number = 3): string[] {
    return this.pools
      .filter(pool => pool.players.length <= threshold)
      .sort((a, b) => a.players.length - b.players.length)
      .map(pool => pool.position);
  }

  /**
   * Debug helper - print position constraints
   */
  debugPrintConstraints(): void {
    console.log('=== Position Constraints ===');
    for (const pool of this.pools) {
      console.log(`${pool.position}: ${pool.players.length} players`);
      pool.players.forEach(p => {
        console.log(`  ${p.priority}. ${p.name} (${p.playerId})`);
      });
    }
    console.log('=== Player Flexibility ===');
    for (const [playerId, positions] of this.playerMap) {
      const playerName = this.pools
        .flatMap(p => p.players)
        .find(p => p.playerId === playerId)?.name || 'Unknown';
      console.log(`${playerName}: ${positions.size} positions - ${Array.from(positions).join(', ')}`);
    }
  }
}
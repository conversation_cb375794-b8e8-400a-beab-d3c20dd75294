import { PlayerStats } from '../types';

/**
 * Playing Time Rules - Ensure fair distribution of playing time
 */
export class PlayingTimeRules {
  constructor(
    private minPlayingTimePercent: number = 25,
    private maxBenchInnings: number = 2
  ) {}

  /**
   * Check if a player needs more playing time
   */
  needsMorePlayingTime(playerId: string, stats?: PlayerStats): boolean {
    if (!stats) return true; // No history = needs playing time
    
    const total = stats.fieldInnings + stats.benchInnings;
    if (total === 0) return true;
    
    const playingPercent = (stats.fieldInnings / total) * 100;
    return playingPercent < this.minPlayingTimePercent;
  }

  /**
   * Check if a player has been on bench too long
   */
  exceedsBenchLimit(playerId: string, stats?: PlayerStats): boolean {
    if (!stats) return false;
    return stats.benchStreak >= this.maxBenchInnings;
  }

  /**
   * Calculate playing time imbalance score (lower is better)
   */
  calculateImbalanceScore(allStats: Map<string, PlayerStats>): number {
    if (allStats.size === 0) return 0;
    
    const playingTimes: number[] = [];
    for (const [_, stats] of allStats) {
      const total = stats.fieldInnings + stats.benchInnings;
      if (total > 0) {
        playingTimes.push(stats.fieldInnings / total);
      }
    }
    
    if (playingTimes.length === 0) return 0;
    
    // Calculate standard deviation
    const mean = playingTimes.reduce((a, b) => a + b, 0) / playingTimes.length;
    const variance = playingTimes.reduce((sum, time) => sum + Math.pow(time - mean, 2), 0) / playingTimes.length;
    
    return Math.sqrt(variance);
  }

  /**
   * Sort players by playing time priority (least played first)
   */
  sortByPlayingTimePriority(
    playerIds: string[], 
    statsMap: Map<string, PlayerStats>
  ): string[] {
    return [...playerIds].sort((a, b) => {
      const statsA = statsMap.get(a);
      const statsB = statsMap.get(b);
      
      // No stats = highest priority
      if (!statsA && !statsB) return 0;
      if (!statsA) return -1;
      if (!statsB) return 1;
      
      // First check bench streaks
      if (statsA.benchStreak >= this.maxBenchInnings) return -1;
      if (statsB.benchStreak >= this.maxBenchInnings) return 1;
      
      // Then check playing time percentage
      const totalA = statsA.fieldInnings + statsA.benchInnings;
      const totalB = statsB.fieldInnings + statsB.benchInnings;
      
      if (totalA === 0 && totalB === 0) return 0;
      if (totalA === 0) return -1;
      if (totalB === 0) return 1;
      
      const percentA = statsA.fieldInnings / totalA;
      const percentB = statsB.fieldInnings / totalB;
      
      return percentA - percentB; // Lower percentage = higher priority
    });
  }
}
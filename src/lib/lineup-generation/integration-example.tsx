/**
 * Example: How to integrate the new lineup algorithm into existing code
 */

// 1. In your lineup creation component/page:
import { generateLineupAdapter, isNewLineupGenerationEnabled } from '@/lib/lineup-generation/adapter';
import { generateLineupForTeam } from '@/lib/lineup-generation';
import { LineupDebugger } from '@/lib/lineup-generation';

// 2. Replace existing lineup generation call:

// OLD CODE:
// const lineup = generateLineup(availablePlayers, rules);

// NEW CODE:
const lineup = await generateLineupAdapter(availablePlayers, rules, {
  teamId: team.id,
  // ... other lineup properties
});

// 3. Or use the team-integrated version:
const result = await generateLineupForTeam(team, availablePlayers, {
  gameDate: new Date().toISOString(),
  gameNumber: 1
});

if (result.success && result.lineup) {
  // Convert to old format if needed
  const oldFormatLineup = convertToOldLineup(
    result.lineup,
    team.id,
    undefined, // game id
    7 // total innings
  );
  
  // Save or use the lineup
  await saveLineup(oldFormatLineup);
}

// 4. Enable/disable for testing:

// Enable for specific team
function enableNewAlgorithmForTeam(teamId: string) {
  localStorage.setItem(`new-lineup-gen-${teamId}`, 'true');
  LineupDebugger.enableForTeam(teamId);
  toast.success('New lineup algorithm enabled for this team');
}

// Enable globally
function enableNewAlgorithmGlobally() {
  localStorage.setItem('new-lineup-generation', 'true');
  LineupDebugger.enableGlobal();
  toast.success('New lineup algorithm enabled globally');
}

// Enable shadow mode (comparison)
function enableShadowMode() {
  localStorage.setItem('lineup-shadow-mode', 'true');
  toast.info('Shadow mode enabled - check console for algorithm comparison');
}

// 5. React component example:
export function LineupGeneratorExample() {
  const { team, players } = useTeam();
  const [isGenerating, setIsGenerating] = useState(false);
  
  const handleGenerateLineup = async () => {
    setIsGenerating(true);
    
    try {
      // Check which algorithm to use
      if (isNewLineupGenerationEnabled(team.id)) {
        console.log('Using NEW lineup algorithm');
      }
      
      // This automatically uses the right algorithm based on feature flags
      const lineup = await generateLineupAdapter(
        players,
        {
          totalInnings: 7,
          competitiveMode: team.competitiveMode,
          // ... other rules
        },
        {
          teamId: team.id,
          gameDate: new Date().toISOString()
        }
      );
      
      // Use the lineup
      console.log('Generated lineup:', lineup);
      
    } catch (error) {
      console.error('Failed to generate lineup:', error);
      toast.error('Failed to generate lineup');
    } finally {
      setIsGenerating(false);
    }
  };
  
  return (
    <div>
      <Button 
        onClick={handleGenerateLineup}
        disabled={isGenerating}
      >
        {isGenerating ? 'Generating...' : 'Generate Lineup'}
      </Button>
      
      {/* Feature flag toggle for testing */}
      {process.env.NODE_ENV === 'development' && (
        <FeatureFlagManager teamId={team.id} />
      )}
    </div>
  );
}

// 6. Listen for position pool updates:
useEffect(() => {
  const handlePoolsUpdated = (event: CustomEvent) => {
    if (event.detail.teamId === team.id) {
      // Refetch or update local state
      console.log('Position pools updated:', event.detail.pools);
      refetchPlayers();
    }
  };
  
  window.addEventListener('position-pools-updated', handlePoolsUpdated);
  return () => {
    window.removeEventListener('position-pools-updated', handlePoolsUpdated);
  };
}, [team.id]);
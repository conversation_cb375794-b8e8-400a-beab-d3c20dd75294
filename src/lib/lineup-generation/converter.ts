import { InningLineup, Lineup as OldLineup } from '../utils-enhanced';
import { Lineup, PlayerStats, FIELD_POSITIONS } from './types';

/**
 * Convert old lineup format to new format
 */
export function convertFromOldLineup(oldLineup: OldLineup): Lineup {
  // Get the first inning as base (or create empty if no innings)
  const firstInning = oldLineup.innings?.[0];
  const assignments = new Map<string, string>();
  const benchPlayers: string[] = [];
  
  if (firstInning) {
    // Map field positions
    for (const position of FIELD_POSITIONS) {
      const playerName = (firstInning.positions as any)[position];
      if (playerName) {
        // In old system, we used names. Need to find player ID
        // This will need to be handled by caller who has player data
        assignments.set(position, playerName);
      }
    }
    
    // Map bench players
    if (firstInning.positions.bench) {
      benchPlayers.push(...firstInning.positions.bench);
    }
  }
  
  return {
    assignments,
    benchPlayers,
    battingOrder: oldLineup.battingOrder,
    metadata: {
      generatedAt: new Date(oldLineup.createdAt || Date.now()),
      algorithm: 'Legacy',
      version: '1.0.0'
    }
  };
}

/**
 * Apply rotation logic to positions for the next inning
 */
function applyRotation(
  positions: any,
  inningNumber: number,
  metadata: any,
  playerMap?: Map<string, { name: string; canPlay: (position: string, competitive: boolean) => boolean }>,
  rotationRules?: any
): any {
  console.log(`[applyRotation] Starting rotation for inning ${inningNumber}`);
  console.log(`[applyRotation] Positions object:`, positions);
  console.log(`[applyRotation] PlayerMap keys:`, playerMap ? Array.from(playerMap.keys()) : 'No playerMap');
  
  // Check if rotation should happen
  const rotateEvery = rotationRules?.rotateLineupEvery || metadata?.rotateEvery || 1;
  const shouldRotate = (inningNumber - 1) % rotateEvery === 0;
  
  const rotatePitcherEvery = rotationRules?.rotatePitcherEvery || metadata?.rotatePitcherEvery || 2;
  const shouldRotatePitcher = rotationRules?.allowPitcherRotation !== false && 
                              (inningNumber - 1) % rotatePitcherEvery === 0;
  
  if (!shouldRotate && !shouldRotatePitcher) {
    console.log(`[applyRotation] No rotation scheduled for inning ${inningNumber}`);
    return { ...positions };
  }
  
  // Extract field players and bench
  const fieldPositions = FIELD_POSITIONS;
  const currentField = new Map<string, string>();
  const currentBench = [...(positions.bench || [])];
  
  fieldPositions.forEach(pos => {
    if (positions[pos]) {
      currentField.set(pos, positions[pos]);
    }
  });
  
  // If no player map provided, can't validate - return same positions
  if (!playerMap) {
    console.warn(`[applyRotation] No player map provided, cannot validate rotations`);
    return { ...positions };
  }
  
  const competitiveMode = rotationRules?.competitiveMode || false;
  console.log('[applyRotation] rotationRules:', rotationRules);
  console.log('[applyRotation] competitiveMode set to:', competitiveMode);
  
  // Build eligibility matrix
  const eligibilityMatrix = new Map<string, Set<string>>();
  const allPlayers = [...currentField.values(), ...currentBench];
  
  allPlayers.forEach(playerId => {
    const eligiblePositions = new Set<string>();
    const player = playerMap.get(playerId);
    
    if (player) {
      fieldPositions.forEach(pos => {
        if (player.canPlay(pos, competitiveMode)) {
          eligiblePositions.add(pos);
        }
      });
    }
    
    eligibilityMatrix.set(playerId, eligiblePositions);
  });
  
  console.log(`[applyRotation] Built eligibility matrix for ${allPlayers.length} players`);
  
  // New positions object
  const newPositions: any = { bench: [] };
  const assigned = new Set<string>();
  
  // Handle pitcher rotation if needed
  if (shouldRotatePitcher && positions.pitcher) {
    const currentPitcher = positions.pitcher;
    
    // Find eligible pitchers on bench
    const eligiblePitchers = currentBench.filter(playerId => {
      const eligible = eligibilityMatrix.get(playerId);
      return eligible && eligible.has('pitcher');
    });
    
    if (eligiblePitchers.length > 0) {
      // Rotate to first eligible pitcher
      newPositions.pitcher = eligiblePitchers[0];
      assigned.add(eligiblePitchers[0]);
      console.log(`[applyRotation] Pitcher rotation: ${currentPitcher} → ${eligiblePitchers[0]}`);
    } else {
      // Keep current pitcher
      newPositions.pitcher = currentPitcher;
      assigned.add(currentPitcher);
      console.log(`[applyRotation] No eligible pitcher on bench, keeping ${currentPitcher}`);
    }
  } else {
    // Keep current pitcher
    newPositions.pitcher = positions.pitcher;
    assigned.add(positions.pitcher);
  }
  
  // Handle general rotation if needed
  if (shouldRotate) {
    console.log(`[applyRotation] Performing lineup rotation`);
    
    // Prioritize bench players for field positions
    const unassignedBench = currentBench.filter(p => !assigned.has(p));
    const unassignedField = [...currentField.values()].filter(p => !assigned.has(p));
    
    // Try to assign positions using constraint-based approach
    const remainingPositions = fieldPositions.filter(pos => pos !== 'pitcher');
    
    // Sort positions by number of eligible players (most constrained first)
    const positionConstraints = remainingPositions.map(pos => {
      const eligible = allPlayers.filter(playerId => {
        if (assigned.has(playerId)) return false;
        const positions = eligibilityMatrix.get(playerId);
        return positions && positions.has(pos);
      });
      
      return { position: pos, eligibleCount: eligible.length, eligible };
    }).sort((a, b) => a.eligibleCount - b.eligibleCount);
    
    // Assign positions starting with most constrained
    for (const { position, eligible } of positionConstraints) {
      if (eligible.length === 0) {
        console.error(`[applyRotation] No eligible players for ${position}!`);
        console.error(`[applyRotation] All players:`, allPlayers);
        console.error(`[applyRotation] Eligibility matrix:`, Array.from(eligibilityMatrix.entries()).map(([id, positions]) => ({
          player: id,
          canPlay: Array.from(positions)
        })));
        
        // In competitive mode, this is an error
        if (competitiveMode) {
          throw new Error(`Cannot find eligible player for ${position} in competitive mode`);
        }
        // In recreational, we'll have to assign someone
        const anyUnassigned = allPlayers.find(p => !assigned.has(p));
        if (anyUnassigned) {
          newPositions[position] = anyUnassigned;
          assigned.add(anyUnassigned);
          console.warn(`[applyRotation] Force assigned ${anyUnassigned} to ${position} (recreational emergency)`);
        }
      } else {
        // Prioritize bench players who can play this position
        const benchCandidate = unassignedBench.find(p => eligible.includes(p));
        if (benchCandidate) {
          newPositions[position] = benchCandidate;
          assigned.add(benchCandidate);
          console.log(`[applyRotation] Assigned bench player ${benchCandidate} to ${position}`);
        } else {
          // Use first eligible player
          const candidate = eligible.find(p => !assigned.has(p));
          if (candidate) {
            newPositions[position] = candidate;
            assigned.add(candidate);
            console.log(`[applyRotation] Assigned ${candidate} to ${position}`);
          }
        }
      }
    }
  } else {
    // No rotation, copy existing assignments
    fieldPositions.forEach(pos => {
      if (pos !== 'pitcher' && positions[pos]) {
        newPositions[pos] = positions[pos];
        assigned.add(positions[pos]);
      }
    });
  }
  
  // Assign remaining players to bench
  newPositions.bench = allPlayers.filter(p => !assigned.has(p));
  
  // Validate all assignments
  let validationErrors = [];
  fieldPositions.forEach(pos => {
    const playerId = newPositions[pos];
    if (!playerId) {
      validationErrors.push(`Position ${pos} is empty`);
    } else {
      const player = playerMap.get(playerId);
      if (player && !player.canPlay(pos, competitiveMode)) {
        validationErrors.push(`${player.name} cannot play ${pos} in ${competitiveMode ? 'competitive' : 'recreational'} mode`);
      }
    }
  });
  
  if (validationErrors.length > 0) {
    console.error(`[applyRotation] Validation errors:`, validationErrors);
    if (competitiveMode) {
      throw new Error(`Invalid rotation: ${validationErrors.join(', ')}`);
    }
  }
  
  console.log(`[applyRotation] Rotation complete for inning ${inningNumber}`);
  return newPositions;
}

/**
 * Convert new lineup format to old format for compatibility
 */
export function convertToOldLineup(
  newLineup: Lineup,
  teamId: string,
  gameId?: string,
  totalInnings: number = 7,
  rotationRules?: any,
  playerMap?: Map<string, string>, // Map from player ID to name
  playerData?: Map<string, { name: string; canPlay: (position: string, competitive: boolean) => boolean }>
): OldLineup {
  // Create positions object from assignments
  const positions: any = {};
  
  // Convert player IDs to names for the old system
  for (const [position, playerId] of newLineup.assignments) {
    // If we have a playerMap, use it to get the name, otherwise use the ID
    positions[position] = playerMap?.get(playerId) || playerId;
  }
  
  // Convert bench player IDs to names
  positions.bench = newLineup.benchPlayers?.map(id => playerMap?.get(id) || id) || [];
  
  // Create innings array with rotation
  const innings: InningLineup[] = [];
  
  // First inning uses the generated lineup
  innings.push({
    inning: 1,
    positions: { ...positions }
  });
  
  // For subsequent innings, just copy the first inning
  // TODO: Fix rotation logic to properly handle player eligibility
  for (let i = 2; i <= totalInnings; i++) {
    innings.push({
      inning: i,
      positions: { ...positions } // Copy first inning positions
    });
  }
  
  console.warn('[convertToOldLineup] ROTATION DISABLED - Using same positions for all innings');
  
  const oldLineup: OldLineup = {
    id: gameId || `temp-${Date.now()}`,
    teamId,
    innings,
    battingOrder: newLineup.battingOrder || [],
    gameDate: newLineup.metadata.generatedAt.toISOString(),
    createdAt: newLineup.metadata.generatedAt.toISOString()
  };
  
  return oldLineup;
}

/**
 * Convert player stats from old format
 */
export function convertPlayerStats(
  playerPositionHistory?: Record<string, {
    fieldInnings: number;
    benchInnings: number;
  }>
): Map<string, PlayerStats> {
  const statsMap = new Map<string, PlayerStats>();
  
  if (!playerPositionHistory) return statsMap;
  
  for (const [playerId, history] of Object.entries(playerPositionHistory)) {
    statsMap.set(playerId, {
      fieldInnings: history.fieldInnings || 0,
      benchInnings: history.benchInnings || 0,
      positionCounts: {}, // Not available in old format
      benchStreak: 0 // Would need to calculate from lineup history
    });
  }
  
  return statsMap;
}

/**
 * Calculate bench streaks from lineup history
 */
export function calculateBenchStreaks(
  lineups: Lineup[],
  playerIds: string[]
): Map<string, number> {
  const streaks = new Map<string, number>();
  
  // Initialize all players with 0 streak
  for (const playerId of playerIds) {
    streaks.set(playerId, 0);
  }
  
  // Work backwards through lineups to find current streaks
  for (let i = lineups.length - 1; i >= 0; i--) {
    const lineup = lineups[i];
    const benchSet = new Set(lineup.benchPlayers);
    
    for (const playerId of playerIds) {
      if (benchSet.has(playerId)) {
        // Player is on bench, increment streak
        const currentStreak = streaks.get(playerId) || 0;
        streaks.set(playerId, currentStreak + 1);
      } else {
        // Player is on field, reset streak to 0
        streaks.set(playerId, 0);
      }
    }
  }
  
  return streaks;
}
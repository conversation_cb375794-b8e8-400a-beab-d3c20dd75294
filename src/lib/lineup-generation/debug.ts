/**
 * Debug utilities for lineup generation
 */
export class LineupDebugger {
  private static readonly DEBUG_KEY_PREFIX = 'lineup-debug-';
  
  /**
   * Enable debug logging for a team
   */
  static enableForTeam(teamId: string): void {
    localStorage.setItem(`${this.DEBUG_KEY_PREFIX}${teamId}`, 'true');
    console.log(`[LineupDebugger] Debug enabled for team ${teamId}`);
  }
  
  /**
   * Disable debug logging for a team
   */
  static disableForTeam(teamId: string): void {
    localStorage.removeItem(`${this.DEBUG_KEY_PREFIX}${teamId}`);
    console.log(`[LineupDebugger] Debug disabled for team ${teamId}`);
  }
  
  /**
   * Check if debug is enabled for a team
   */
  static isEnabled(teamId: string): boolean {
    return localStorage.getItem(`${this.DEBUG_KEY_PREFIX}${teamId}`) === 'true';
  }
  
  /**
   * Enable debug for all teams
   */
  static enableGlobal(): void {
    localStorage.setItem(`${this.DEBUG_KEY_PREFIX}global`, 'true');
    console.log('[LineupDebugger] Global debug enabled');
  }
  
  /**
   * Disable debug for all teams
   */
  static disableGlobal(): void {
    localStorage.removeItem(`${this.DEBUG_KEY_PREFIX}global`);
    console.log('[LineupDebugger] Global debug disabled');
  }
  
  /**
   * Log a debug message if enabled
   */
  static log(teamId: string, message: string, data?: any): void {
    const globalEnabled = localStorage.getItem(`${this.DEBUG_KEY_PREFIX}global`) === 'true';
    const teamEnabled = this.isEnabled(teamId);
    
    if (globalEnabled || teamEnabled) {
      const timestamp = new Date().toISOString();
      console.log(`[Lineup Debug ${teamId}] ${timestamp} - ${message}`, data || '');
    }
  }
  
  /**
   * Log an error (always logged)
   */
  static error(teamId: string, message: string, error?: any): void {
    const timestamp = new Date().toISOString();
    console.error(`[Lineup Error ${teamId}] ${timestamp} - ${message}`, error || '');
  }
  
  /**
   * Create a performance timer
   */
  static startTimer(label: string): () => void {
    const start = performance.now();
    return () => {
      const duration = performance.now() - start;
      return `${label}: ${duration.toFixed(2)}ms`;
    };
  }
  
  /**
   * Format lineup for logging
   */
  static formatLineup(lineup: any): string {
    if (!lineup) return 'null';
    
    const positions = Array.from(lineup.assignments || [])
      .map(([pos, player]) => `${pos}: ${player}`)
      .join(', ');
    
    const bench = (lineup.benchPlayers || []).join(', ');
    
    return `Field: [${positions}], Bench: [${bench}]`;
  }
}
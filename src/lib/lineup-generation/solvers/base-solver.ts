import { LineupRequest, LineupResult, Lineup, SolverCandidate, FIELD_POSITIONS } from '../types';
import { PositionRules } from '../rules/position-rules';
import { PlayingTimeRules } from '../rules/playing-time';

/**
 * Base solver with common logic for all solver implementations
 */
export abstract class BaseSolver {
  protected assignments: Map<string, string> = new Map();
  protected assignedPlayers: Set<string> = new Set();
  protected decisions: string[] = [];
  protected attemptCount: number = 0;
  protected backtrackCount: number = 0;
  
  protected positionRules: PositionRules;
  protected playingTimeRules: PlayingTimeRules;
  
  constructor() {
    this.playingTimeRules = new PlayingTimeRules();
  }
  
  solve(request: LineupRequest): LineupResult {
    const startTime = Date.now();
    this.positionRules = new PositionRules(request.pools);
    
    // Reset state
    this.assignments.clear();
    this.assignedPlayers.clear();
    this.decisions = [];
    this.attemptCount = 0;
    this.backtrackCount = 0;
    
    try {
      // Log initial state in competitive mode
      if (request.mode === 'competitive') {
        console.log('[BaseSolver] Starting solve with constraints:');
        this.positionRules.debugPrintConstraints();
      }
      
      // Sort positions by constraint level (positions with fewer options first)
      const positions = this.sortPositionsByConstraint(request);
      
      this.decisions.push(`Position fill order: ${positions.join(' → ')}`);
      
      // Recursive assignment with backtracking
      const success = this.assignPositions(positions, 0, request);
      
      if (!success) {
        return {
          success: false,
          errors: ['Could not find valid lineup arrangement. Check position pools have enough players.'],
          decisions: this.decisions,
          diagnostics: {
            attemptedAssignments: this.attemptCount,
            backtrackCount: this.backtrackCount,
            executionTime: Date.now() - startTime
          }
        };
      }
      
      // Build final lineup
      const lineup = this.buildLineup(request);
      
      return {
        success: true,
        lineup,
        decisions: this.decisions,
        diagnostics: {
          attemptedAssignments: this.attemptCount,
          backtrackCount: this.backtrackCount,
          executionTime: Date.now() - startTime
        }
      };
    } catch (error) {
      console.error('[BaseSolver] Error during solve:', error);
      return {
        success: false,
        errors: [error.message || 'Unknown error during lineup generation'],
        decisions: this.decisions,
        diagnostics: {
          attemptedAssignments: this.attemptCount,
          backtrackCount: this.backtrackCount,
          executionTime: Date.now() - startTime
        }
      };
    }
  }
  
  private assignPositions(
    positions: string[], 
    index: number, 
    request: LineupRequest
  ): boolean {
    // Base case: all positions assigned
    if (index >= positions.length) {
      return true;
    }
    
    const position = positions[index];
    const candidates = this.getCandidatesForPosition(position, request);
    
    this.decisions.push(`Trying ${position}: ${candidates.length} candidates`);
    
    for (const candidate of candidates) {
      if (this.assignedPlayers.has(candidate.playerId)) {
        continue; // Already assigned elsewhere
      }
      
      this.attemptCount++;
      
      // Try this assignment
      this.assignments.set(position, candidate.playerId);
      this.assignedPlayers.add(candidate.playerId);
      this.decisions.push(
        `  ✓ ${position}: Assigned ${candidate.name} (priority ${candidate.priority}${candidate.reason ? ', ' + candidate.reason : ''})`
      );
      
      // Recursively assign remaining positions
      if (this.assignPositions(positions, index + 1, request)) {
        return true; // Success!
      }
      
      // Backtrack
      this.assignments.delete(position);
      this.assignedPlayers.delete(candidate.playerId);
      this.decisions.push(`  ✗ Backtracking from ${position}: ${candidate.name}`);
      this.backtrackCount++;
    }
    
    // No valid assignment found for this position
    this.decisions.push(`  ✗ Failed to fill ${position} - no valid candidates`);
    return false;
  }
  
  protected buildLineup(request: LineupRequest): Lineup {
    // Determine bench players
    const benchPlayers = request.availablePlayers.filter(
      playerId => !this.assignedPlayers.has(playerId)
    );
    
    // Create batting order (field players only)
    const battingOrder = this.createBattingOrder(request);
    
    const lineup: Lineup = {
      assignments: new Map(this.assignments),
      benchPlayers,
      battingOrder,
      metadata: {
        generatedAt: new Date(),
        algorithm: this.getAlgorithmName(),
        version: '2.0.0'
      }
    };
    
    return lineup;
  }
  
  protected createBattingOrder(request: LineupRequest): string[] {
    // Simple batting order: just use field players in position order
    // Can be overridden by subclasses for smarter ordering
    const battingOrder: string[] = [];
    
    for (const position of FIELD_POSITIONS) {
      const playerId = this.assignments.get(position);
      if (playerId) {
        battingOrder.push(playerId);
      }
    }
    
    return battingOrder;
  }
  
  protected abstract sortPositionsByConstraint(request: LineupRequest): string[];
  protected abstract getCandidatesForPosition(position: string, request: LineupRequest): SolverCandidate[];
  protected abstract getAlgorithmName(): string;
}
import { LineupRequest, SolverCandidate } from '../types';
import { BaseSolver } from './base-solver';

/**
 * Competitive Solver - Prioritizes best players in best positions
 */
export class CompetitiveSolver extends BaseSolver {
  private keyPositions = ['pitcher', 'catcher', 'shortstop'];
  
  protected getAlgorithmName(): string {
    return 'CompetitiveSolver';
  }
  
  protected sortPositionsByConstraint(request: LineupRequest): string[] {
    const positionInfo = request.pools.map(pool => {
      const availableCount = pool.players.filter(p => 
        request.availablePlayers.includes(p.playerId) &&
        !this.assignedPlayers.has(p.playerId)
      ).length;
      
      // Calculate average priority (lower = better players)
      const avgPriority = pool.players.length > 0
        ? pool.players.reduce((sum, p) => sum + p.priority, 0) / pool.players.length
        : 999;
      
      return {
        position: pool.position,
        availableCount,
        avgPriority,
        isKeyPosition: this.keyPositions.includes(pool.position)
      };
    });
    
    // Sort by:
    // 1. Key positions first (pitcher, catcher, shortstop)
    // 2. Then positions with fewer options
    // 3. Then positions with better players (lower avg priority)
    positionInfo.sort((a, b) => {
      // Key positions first
      if (a.isKeyPosition && !b.isKeyPosition) return -1;
      if (!a.isKeyPosition && b.isKeyPosition) return 1;
      
      // Then by constraint level
      if (a.availableCount !== b.availableCount) {
        return a.availableCount - b.availableCount;
      }
      
      // Then by player quality
      return a.avgPriority - b.avgPriority;
    });
    
    return positionInfo.map(pi => pi.position);
  }
  
  protected getCandidatesForPosition(position: string, request: LineupRequest): SolverCandidate[] {
    const pool = request.pools.find(p => p.position === position);
    if (!pool) return [];
    
    // Filter to available players only
    const availablePlayers = pool.players.filter(p => 
      request.availablePlayers.includes(p.playerId) &&
      !this.assignedPlayers.has(p.playerId)
    );
    
    // In competitive mode, strictly respect pool priority
    return availablePlayers
      .map(player => {
        let score = 1000 - (player.priority * 100); // Priority 1 = 900, Priority 2 = 800, etc.
        let reason = `pool priority ${player.priority}`;
        
        // Bonus for key positions
        if (this.keyPositions.includes(position)) {
          score += 50;
          reason += ', key position';
        }
        
        // Small consideration for playing time to break ties
        const stats = request.gameContext?.playerStats?.get(player.playerId);
        if (stats) {
          // Slight penalty for overplayed players
          const total = stats.fieldInnings + stats.benchInnings;
          if (total > 0) {
            const fieldPercent = (stats.fieldInnings / total) * 100;
            if (fieldPercent > 80) {
              score -= 10;
              reason += ', high PT';
            }
          }
          
          // Must respect bench limits even in competitive mode
          if (stats.benchStreak >= request.options.maxBenchInnings) {
            score += 200; // Force rotation
            reason += `, bench streak ${stats.benchStreak}`;
          }
        }
        
        return {
          playerId: player.playerId,
          name: player.name,
          priority: player.priority,
          score,
          reason
        };
      })
      .sort((a, b) => {
        // Sort by score (higher = better fit)
        if (b.score !== a.score) {
          return b.score - a.score;
        }
        // Exact tie = use pool priority
        return a.priority - b.priority;
      });
  }
  
  protected createBattingOrder(request: LineupRequest): string[] {
    // In competitive mode, create a strategic batting order
    const battingOrder: string[] = [];
    
    // Get all field players with their pool priorities
    const fieldPlayers: Array<{playerId: string; avgPriority: number}> = [];
    
    for (const [position, playerId] of this.assignments) {
      const pool = request.pools.find(p => p.position === position);
      const player = pool?.players.find(p => p.playerId === playerId);
      if (player) {
        // Calculate average priority across all positions this player can play
        const allPositions = this.positionRules.getPositionsForPlayer(playerId);
        let totalPriority = 0;
        let positionCount = 0;
        
        for (const pos of allPositions) {
          const posPool = request.pools.find(p => p.position === pos);
          const playerInPool = posPool?.players.find(p => p.playerId === playerId);
          if (playerInPool) {
            totalPriority += playerInPool.priority;
            positionCount++;
          }
        }
        
        const avgPriority = positionCount > 0 ? totalPriority / positionCount : 999;
        fieldPlayers.push({ playerId, avgPriority });
      }
    }
    
    // Sort by average priority (lower = better player)
    fieldPlayers.sort((a, b) => a.avgPriority - b.avgPriority);
    
    // Classic batting order strategy:
    // 1. Second best leadoff
    // 2. Best hitter 2nd
    // 3. 3rd best 3rd (RBI spot)
    // 4. 4th best cleanup
    // 5-9. Remaining in order
    
    if (fieldPlayers.length >= 4) {
      battingOrder.push(fieldPlayers[1].playerId); // 2nd best leadoff
      battingOrder.push(fieldPlayers[0].playerId); // Best 2nd
      battingOrder.push(fieldPlayers[2].playerId); // 3rd best 3rd
      battingOrder.push(fieldPlayers[3].playerId); // 4th best cleanup
      
      // Rest in order
      for (let i = 4; i < fieldPlayers.length; i++) {
        battingOrder.push(fieldPlayers[i].playerId);
      }
    } else {
      // Not enough players for strategy, just use order
      battingOrder.push(...fieldPlayers.map(fp => fp.playerId));
    }
    
    return battingOrder;
  }
}
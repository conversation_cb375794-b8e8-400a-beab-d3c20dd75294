import { LineupRequest, SolverCandidate } from '../types';
import { BaseSolver } from './base-solver';

/**
 * Fairness Solver - Prioritizes equal playing time
 */
export class FairnessSolver extends BaseSolver {
  protected getAlgorithmName(): string {
    return 'FairnessSolver';
  }
  
  protected sortPositionsByConstraint(request: LineupRequest): string[] {
    // Process positions with fewest available options first
    // This reduces backtracking by handling constrained positions early
    const positionConstraints = request.pools.map(pool => {
      const availableCount = pool.players.filter(p => 
        request.availablePlayers.includes(p.playerId) &&
        !this.assignedPlayers.has(p.playerId)
      ).length;
      
      return {
        position: pool.position,
        availableCount
      };
    });
    
    // Sort by fewest options first
    positionConstraints.sort((a, b) => a.availableCount - b.availableCount);
    
    return positionConstraints.map(pc => pc.position);
  }
  
  protected getCandidatesForPosition(position: string, request: LineupRequest): SolverCandidate[] {
    const pool = request.pools.find(p => p.position === position);
    if (!pool) return [];
    
    // Filter to available players only
    const availablePlayers = pool.players.filter(p => 
      request.availablePlayers.includes(p.playerId) &&
      !this.assignedPlayers.has(p.playerId)
    );
    
    // Sort candidates by fairness criteria
    return availablePlayers
      .map(player => {
        const stats = request.gameContext?.playerStats?.get(player.playerId);
        let score = 0;
        let reason = '';
        
        if (stats) {
          // Players who need playing time get higher scores
          if (stats.benchStreak >= request.options.maxBenchInnings) {
            score += 1000; // Highest priority - exceeded bench limit
            reason = `bench streak: ${stats.benchStreak}`;
          }
          
          // Calculate playing time percentage
          const total = stats.fieldInnings + stats.benchInnings;
          if (total > 0) {
            const fieldPercent = (stats.fieldInnings / total) * 100;
            // Invert so lower playing time = higher score
            score += (100 - fieldPercent) * 10;
            
            if (fieldPercent < request.options.minPlayingTime) {
              score += 500; // Bonus for being under minimum
              reason = reason ? `${reason}, low PT: ${fieldPercent.toFixed(0)}%` : `low PT: ${fieldPercent.toFixed(0)}%`;
            }
          } else {
            // No history = needs playing time
            score += 800;
            reason = 'new player';
          }
        } else {
          // No stats = high priority
          score += 900;
          reason = 'no history';
        }
        
        // Use pool priority as tiebreaker
        score -= player.priority * 0.1;
        
        return {
          playerId: player.playerId,
          name: player.name,
          priority: player.priority,
          score,
          reason
        };
      })
      .sort((a, b) => {
        // Sort by score (higher = more deserving of playing time)
        if (b.score !== a.score) {
          return b.score - a.score;
        }
        // Then by pool priority
        return a.priority - b.priority;
      });
  }
}
import { describe, it, expect } from 'vitest';
import { LineupGenerator } from '../generator';
import { LineupRequest, PositionPool } from '../types';

describe('Competitive Mode Position Enforcement', () => {
  const generator = new LineupGenerator();
  
  it('only assigns players to positions in their pool', async () => {
    const pools: PositionPool[] = [
      {
        position: 'pitcher',
        players: [
          { playerId: '1', name: '<PERSON>', priority: 1 },
          { playerId: '3', name: '<PERSON>', priority: 2 }
        ]
      },
      {
        position: 'catcher',
        players: [
          { playerId: '2', name: 'Vienna', priority: 1 },
          { playerId: '4', name: '<PERSON>', priority: 2 }
        ]
      },
      {
        position: 'firstBase',
        players: [
          { playerId: '1', name: '<PERSON>', priority: 1 },
          { playerId: '5', name: '<PERSON>', priority: 2 }
        ]
      },
      {
        position: 'secondBase',
        players: [
          { playerId: '5', name: '<PERSON>', priority: 1 },
          { playerId: '6', name: '<PERSON><PERSON><PERSON>', priority: 2 }
        ]
      },
      {
        position: 'thirdBase',
        players: [
          { playerId: '3', name: '<PERSON>', priority: 1 },
          { playerId: '7', name: '<PERSON>', priority: 2 }
        ]
      },
      {
        position: 'shortstop',
        players: [
          { playerId: '8', name: '<PERSON>', priority: 1 },
          { playerId: '2', name: '<PERSON>', priority: 2 }
        ]
      },
      {
        position: 'leftField',
        players: [
          { playerId: '6', name: 'Kaitlyn', priority: 1 },
          { playerId: '9', name: '<PERSON>n', priority: 2 }
        ]
      },
      {
        position: 'centerField',
        players: [
          { playerId: '10', name: 'Avery', priority: 1 },
          { playerId: '7', name: 'Charlotte', priority: 2 }
        ]
      },
      {
        position: 'rightField',
        players: [
          { playerId: '11', name: 'Elle', priority: 1 },
          { playerId: '4', name: 'Finn', priority: 2 }
        ]
      }
    ];
    
    const request: LineupRequest = {
      teamId: 'test-team',
      pools,
      availablePlayers: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11'],
      mode: 'competitive',
      options: {
        enforceRotation: false,
        minPlayingTime: 0,
        maxBenchInnings: 10
      }
    };
    
    const result = await generator.generate(request);
    
    // Should succeed
    expect(result.success).toBe(true);
    expect(result.lineup).toBeDefined();
    
    // Verify each assignment respects pools
    for (const [position, playerId] of result.lineup!.assignments) {
      const pool = pools.find(p => p.position === position);
      expect(pool).toBeDefined();
      
      const playerInPool = pool!.players.some(p => p.playerId === playerId);
      expect(playerInPool).toBe(true);
      
      // Log for debugging
      const player = pool!.players.find(p => p.playerId === playerId);
      console.log(`✓ ${position}: ${player?.name} (priority ${player?.priority})`);
    }
    
    // Specific checks from the bug report
    const pitcherAssignment = result.lineup!.assignments.get('pitcher');
    expect(['1', '3']).toContain(pitcherAssignment); // Only Morgan or Bella
    
    const catcherAssignment = result.lineup!.assignments.get('catcher');
    expect(['2', '4']).toContain(catcherAssignment); // Only Vienna or Finn
    
    // Morgan should NEVER be at catcher (not in that pool)
    expect(catcherAssignment).not.toBe('1');
  });
  
  it('handles impossible constraints gracefully', async () => {
    const pools: PositionPool[] = [
      {
        position: 'pitcher',
        players: [{ playerId: '1', name: 'OnlyPitcher', priority: 1 }]
      },
      {
        position: 'catcher',
        players: [{ playerId: '1', name: 'OnlyPitcher', priority: 1 }] // Same player!
      },
      // ... other positions with different players
      {
        position: 'firstBase',
        players: [{ playerId: '2', name: 'Player2', priority: 1 }]
      },
      {
        position: 'secondBase',
        players: [{ playerId: '3', name: 'Player3', priority: 1 }]
      },
      {
        position: 'thirdBase',
        players: [{ playerId: '4', name: 'Player4', priority: 1 }]
      },
      {
        position: 'shortstop',
        players: [{ playerId: '5', name: 'Player5', priority: 1 }]
      },
      {
        position: 'leftField',
        players: [{ playerId: '6', name: 'Player6', priority: 1 }]
      },
      {
        position: 'centerField',
        players: [{ playerId: '7', name: 'Player7', priority: 1 }]
      },
      {
        position: 'rightField',
        players: [{ playerId: '8', name: 'Player8', priority: 1 }]
      }
    ];
    
    const request: LineupRequest = {
      teamId: 'test-team',
      pools,
      availablePlayers: ['1', '2', '3', '4', '5', '6', '7', '8', '9'], // Need 9 players minimum
      mode: 'competitive',
      options: {
        enforceRotation: false,
        minPlayingTime: 0,
        maxBenchInnings: 10
      }
    };
    
    const result = await generator.generate(request);
    
    // Should fail because player 1 can't play both pitcher and catcher
    expect(result.success).toBe(false);
    expect(result.errors).toBeDefined();
    expect(result.errors![0]).toContain('Could not find valid lineup arrangement');
  });
  
  it('respects priority order in competitive mode', async () => {
    const pools: PositionPool[] = [
      {
        position: 'pitcher',
        players: [
          { playerId: '1', name: 'BestPitcher', priority: 1 },
          { playerId: '2', name: 'GoodPitcher', priority: 2 },
          { playerId: '3', name: 'BackupPitcher', priority: 3 }
        ]
      },
      // ... other positions ensure all players can be placed
      {
        position: 'catcher',
        players: [
          { playerId: '4', name: 'Catcher1', priority: 1 }
        ]
      },
      {
        position: 'firstBase',
        players: [
          { playerId: '5', name: 'First1', priority: 1 }
        ]
      },
      {
        position: 'secondBase',
        players: [
          { playerId: '6', name: 'Second1', priority: 1 }
        ]
      },
      {
        position: 'thirdBase',
        players: [
          { playerId: '7', name: 'Third1', priority: 1 }
        ]
      },
      {
        position: 'shortstop',
        players: [
          { playerId: '8', name: 'Short1', priority: 1 }
        ]
      },
      {
        position: 'leftField',
        players: [
          { playerId: '9', name: 'Left1', priority: 1 }
        ]
      },
      {
        position: 'centerField',
        players: [
          { playerId: '2', name: 'GoodPitcher', priority: 1 } // Also plays CF
        ]
      },
      {
        position: 'rightField',
        players: [
          { playerId: '3', name: 'BackupPitcher', priority: 1 } // Also plays RF
        ]
      }
    ];
    
    const request: LineupRequest = {
      teamId: 'test-team',
      pools,
      availablePlayers: ['1', '2', '3', '4', '5', '6', '7', '8', '9'],
      mode: 'competitive',
      options: {
        enforceRotation: false,
        minPlayingTime: 0,
        maxBenchInnings: 10
      }
    };
    
    const result = await generator.generate(request);
    
    expect(result.success).toBe(true);
    
    // In competitive mode, BestPitcher (priority 1) should get pitcher position
    expect(result.lineup!.assignments.get('pitcher')).toBe('1');
    
    // GoodPitcher should be in CF, BackupPitcher in RF
    expect(result.lineup!.assignments.get('centerField')).toBe('2');
    expect(result.lineup!.assignments.get('rightField')).toBe('3');
  });
});
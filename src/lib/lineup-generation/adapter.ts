import { Player } from '@/contexts/TeamContext';
import { InningLineup, Lineup as OldLineup, LineupRules } from '../utils-enhanced';
import { generateLineup as newGenerateLineup, PlayerStats, convertToPositionPools } from './index';
import { convertToOldLineup, calculateBenchStreaks } from './converter';
import { ensureNewAlgorithm } from './kill-switch';

/**
 * Feature flag to enable new lineup generation
 * ALWAYS RETURNS TRUE DUE TO KILL SWITCH
 */
export function isNewLineupGenerationEnabled(teamId?: string): boolean {
  // Force new algorithm via kill switch
  ensureNewAlgorithm(teamId);
  return true; // Always use new algorithm
}

/**
 * Enable new lineup generation for a team
 */
export function enableNewLineupGeneration(teamId?: string): void {
  if (teamId) {
    localStorage.setItem(`new-lineup-gen-${teamId}`, 'true');
  } else {
    localStorage.setItem('new-lineup-generation', 'true');
  }
}

/**
 * Adapter function that matches the old generateLineup signature
 * but uses the new algorithm when enabled
 */
export async function generateLineupAdapter(
  availablePlayers: Player[],
  rules: LineupRules,
  lineup?: {
    id?: string;
    teamId: string;
    innings?: InningLineup[];
    battingOrder?: string[];
    gameDate?: string;
  }
): Promise<OldLineup> {
  console.log("🟡 ADAPTER EXECUTING - adapter.ts");
  console.log("🟡 generateLineupAdapter called");
  
  const teamId = lineup?.teamId || 'unknown';
  
  // Check if shadow mode is enabled
  const shadowMode = localStorage.getItem('lineup-shadow-mode') === 'true';
  if (shadowMode) {
    return generateLineupWithShadow(availablePlayers, rules, lineup);
  }
  
  // ALWAYS use new algorithm - kill switch is active
  ensureNewAlgorithm(teamId);
  
  console.log('[LineupAdapter] Using NEW lineup generation algorithm');
  
  try {
    // Convert player history to stats
    const playerStats = new Map<string, PlayerStats>();
    
    // If we have previous innings, calculate stats
    if (lineup?.innings && lineup.innings.length > 0) {
      // Calculate bench streaks
      const playerIds = availablePlayers.map(p => p.id).filter(id => id) as string[];
      const previousLineups = []; // Would need to convert from innings
      const benchStreaks = calculateBenchStreaks(previousLineups, playerIds);
      
      // Build stats from rules history
      if (rules.playerPositionHistory) {
        for (const [playerId, history] of Object.entries(rules.playerPositionHistory)) {
          playerStats.set(playerId, {
            fieldInnings: history.fieldInnings || 0,
            benchInnings: history.benchInnings || 0,
            positionCounts: {},
            benchStreak: benchStreaks.get(playerId) || 0
          });
        }
      }
    }
    
    // Debug: Log available players and their teamRoles
    console.log('[LineupAdapter] Available players with teamRoles:');
    availablePlayers.forEach(player => {
      console.log(`  ${player.name}:`, player.teamRoles);
    });
    
    // Debug log the rules
    console.log('[LineupAdapter] Rules passed in:', rules);
    console.log('[LineupAdapter] Competitive mode:', rules.competitiveMode);
    console.log('[LineupAdapter] Mode will be:', rules.competitiveMode ? 'competitive' : 'recreational');
    
    // Use new algorithm
    const result = await newGenerateLineup(
      teamId,
      availablePlayers, // All players for pools
      availablePlayers, // Available for this game
      {
        mode: rules.competitiveMode ? 'competitive' : 'recreational',
        enforceRotation: !rules.noRotation,
        minPlayingTime: rules.competitiveMinPlayingTime || 25,
        maxBenchInnings: rules.maxConsecutiveBenchInnings || 2,
        rotateEvery: rules.rotateLineupEvery,
        gameContext: {
          gameNumber: lineup?.innings ? lineup.innings.length + 1 : 1,
          playerStats
        }
      }
    );
    
    if (!result.success || !result.lineup) {
      console.error('[LineupAdapter] New algorithm failed:', result.errors);
      throw new Error(result.errors?.join(', ') || 'Unknown error');
    }
    
    // Create maps for player data
    const playerIdToName = new Map<string, string>();
    const playerData = new Map<string, { name: string; canPlay: (position: string, competitive: boolean) => boolean }>();
    
    // Import canPlayPosition function
    const { canPlayPosition } = await import('../utils-enhanced');
    
    availablePlayers.forEach(player => {
      if (player.id) {
        playerIdToName.set(player.id, player.name);
        
        // Create player data with canPlay function
        playerData.set(player.id, {
          name: player.name,
          canPlay: (position: string, competitive: boolean) => {
            return canPlayPosition(player, position, true, competitive);
          }
        });
      }
    });
    
    // Convert back to old format
    const totalInnings = rules.totalInnings || 7;
    const oldLineup = convertToOldLineup(
      result.lineup,
      teamId,
      lineup?.id,
      totalInnings,
      rules,
      playerIdToName,
      playerData
    );
    
    // Preserve existing data
    if (lineup) {
      oldLineup.id = lineup.id || oldLineup.id;
      oldLineup.gameDate = lineup.gameDate || oldLineup.gameDate;
      oldLineup.battingOrder = lineup.battingOrder || oldLineup.battingOrder;
    }
    
    // Log decisions if in debug mode
    if (result.decisions && result.decisions.length > 0) {
      console.log('[LineupAdapter] Generation decisions:', result.decisions);
    }
    
    return oldLineup;
    
  } catch (error) {
    console.error('[LineupAdapter] Critical error in new algorithm:', error);
    
    // DO NOT FALL BACK TO OLD ALGORITHM - IT'S DISABLED
    // Instead, provide better error information
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    // Check for common issues
    if (errorMessage.includes('position') || errorMessage.includes('pool')) {
      console.error('[LineupAdapter] Position pool issue detected');
      console.error('[LineupAdapter] Players:', availablePlayers.map(p => ({ name: p.name, roles: p.teamRoles })));
      
      // Create emergency pools from teamRoles
      console.log('[LineupAdapter] Creating emergency position pools from teamRoles...');
      const emergencyPools = convertToPositionPools(availablePlayers);
      console.log('[LineupAdapter] Emergency pools:', emergencyPools);
      
      throw new Error(`Position assignment error: ${errorMessage}. Check team roster position assignments.`);
    }
    
    throw new Error(`New algorithm error: ${errorMessage}`);
  }
}

/**
 * Shadow mode - Run both algorithms and compare
 */
export async function generateLineupWithShadow(
  availablePlayers: Player[],
  rules: LineupRules,
  lineup?: {
    id?: string;
    teamId: string;
    innings?: InningLineup[];
    battingOrder?: string[];
    gameDate?: string;
  }
): Promise<OldLineup> {
  const teamId = lineup?.teamId || 'unknown';
  
  // Always use old algorithm as primary
  const { generateLineup: oldGenerateLineup } = await import('../utils-enhanced');
  const oldResult = await oldGenerateLineup(availablePlayers, rules, lineup);
  
  // Run new algorithm in shadow mode
  try {
    const newResult = await generateLineupAdapter(availablePlayers, rules, lineup);
    
    // Compare results
    console.log('[Shadow Mode] Comparing algorithms:');
    console.log('Old result:', oldResult.innings[0]?.positions);
    console.log('New result:', newResult.innings[0]?.positions);
    
    // Check for differences
    const oldPositions = oldResult.innings[0]?.positions || {};
    const newPositions = newResult.innings[0]?.positions || {};
    
    let differencesFound = false;
    for (const position of ['pitcher', 'catcher', 'firstBase', 'secondBase', 'thirdBase', 'shortstop', 'leftField', 'centerField', 'rightField']) {
      if (oldPositions[position] !== newPositions[position]) {
        console.warn(`[Shadow Mode] Difference at ${position}: old=${oldPositions[position]}, new=${newPositions[position]}`);
        differencesFound = true;
      }
    }
    
    if (!differencesFound) {
      console.log('[Shadow Mode] ✅ Algorithms produced identical results');
    }
    
  } catch (error) {
    console.error('[Shadow Mode] New algorithm error:', error);
  }
  
  // Always return old result in shadow mode
  return oldResult;
}
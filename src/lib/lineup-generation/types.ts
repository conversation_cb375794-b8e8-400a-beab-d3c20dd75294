// Core types for the new lineup generation system

// Position pool matches the new UI exactly
export interface PositionPool {
  position: string;
  players: Array<{
    playerId: string;
    name: string;
    jersey?: string;
    priority: number; // 1, 2, 3... matches UI order
  }>;
}

export interface LineupRequest {
  teamId: string;
  pools: PositionPool[];
  availablePlayers: string[]; // IDs of players available for this game
  mode: 'competitive' | 'recreational';
  gameContext?: {
    previousLineups?: Lineup[];
    gameNumber?: number;
    playerStats?: Map<string, PlayerStats>;
  };
  options: {
    enforceRotation: boolean;
    minPlayingTime: number;
    maxBenchInnings: number;
    rotateEvery?: number;
    pitcherRotation?: {
      enabled: boolean;
      frequency: number;
    };
  };
}

export interface PlayerStats {
  fieldInnings: number;
  benchInnings: number;
  positionCounts: Record<string, number>;
  benchStreak: number;
}

export interface LineupResult {
  success: boolean;
  lineup?: Lineup;
  errors?: string[];
  decisions?: string[]; // Explanation of why each assignment was made
  diagnostics?: {
    attemptedAssignments: number;
    backtrackCount: number;
    executionTime: number;
  };
}

export interface Lineup {
  assignments: Map<string, string>; // position -> playerId
  benchPlayers: string[];
  battingOrder?: string[];
  metadata: {
    generatedAt: Date;
    algorithm: string;
    version: string;
  };
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings?: string[];
}

export interface SolverCandidate {
  playerId: string;
  name: string;
  priority: number;
  score?: number;
  reason?: string;
}

// Position keys used throughout the system
export const FIELD_POSITIONS = [
  'pitcher',
  'catcher',
  'firstBase',
  'secondBase',
  'thirdBase',
  'shortstop',
  'leftField',
  'centerField',
  'rightField'
] as const;

export type FieldPosition = typeof FIELD_POSITIONS[number];
import { LineupGenerator } from './generator';
import { LineupRequest, LineupResult, PositionPool, PlayerStats, FIELD_POSITIONS } from './types';
import { LineupDebugger } from './debug';
import { Player } from '@/contexts/TeamContext';
import { generateLineupForTeam, savePositionPools } from './team-integration';

// Re-export types
export * from './types';
export { LineupDebugger };
export { generateLineupForTeam, savePositionPools };

/**
 * Convert team roster data to position pools format
 */
export function convertToPositionPools(players: Player[]): PositionPool[] {
  const pools: PositionPool[] = [];
  
  console.log('[convertToPositionPools] Converting teamRoles to position pools');
  console.log('Players count:', players.length);
  
  // Debug: Check first player's structure
  if (players.length > 0) {
    console.log('[convertToPositionPools] Sample player structure:', {
      name: players[0].name,
      id: players[0].id,
      teamRoles: players[0].teamRoles,
      hasTeamRoles: !!players[0].teamRoles,
      teamRolesKeys: players[0].teamRoles ? Object.keys(players[0].teamRoles) : []
    });
  }
  
  // Create a pool for each field position
  for (const position of FIELD_POSITIONS) {
    const playersForPosition: PositionPool['players'] = [];
    
    // Find all players who can play this position
    for (const player of players) {
      if (!player.id || !player.name.trim()) continue;
      
      // Check teamRoles for this position
      const role = player.teamRoles?.[position];
      
      // Handle isOutfieldSpecialist for outfield positions
      const isOutfieldPosition = ['leftField', 'centerField', 'rightField'].includes(position);
      const isOutfieldSpecialist = player.teamRoles?.isOutfieldSpecialist === true;
      
      if (isOutfieldPosition && isOutfieldSpecialist && role !== 'avoid') {
        console.log(`[convertToPositionPools] ${player.name} is outfield specialist, adding to ${position}`);
        playersForPosition.push({
          playerId: player.id,
          name: player.name,
          jersey: player.jerseyNumber,
          priority: 2 // Capable priority for specialists
        });
      } else if (role && role !== 'avoid' && role !== 'unset' && role !== false) {
        // Map role to priority
        let priority = 999;
        if (role === 'go-to') priority = 1;
        else if (role === 'capable') priority = 2;
        else if (role === 'fill-in') priority = 3;
        
        console.log(`[convertToPositionPools] ${player.name} can play ${position} as ${role} (priority ${priority})`);
        
        playersForPosition.push({
          playerId: player.id,
          name: player.name,
          jersey: player.jerseyNumber,
          priority
        });
      }
    }
    
    // Sort by priority and add sequential priorities
    playersForPosition.sort((a, b) => a.priority - b.priority);
    playersForPosition.forEach((player, index) => {
      player.priority = index + 1;
    });
    
    if (playersForPosition.length > 0) {
      console.log(`  ${position}: ${playersForPosition.map(p => `${p.name}(${p.priority})`).join(', ')}`);
    } else {
      console.warn(`[convertToPositionPools] WARNING: No players for position ${position}`);
    }
    
    pools.push({
      position,
      players: playersForPosition
    });
  }
  
  return pools;
}

/**
 * Main API function - Generate a lineup for a team
 */
export async function generateLineup(
  teamId: string,
  players: Player[],
  availablePlayers: Player[],
  options: {
    mode?: 'competitive' | 'recreational';
    enforceRotation?: boolean;
    minPlayingTime?: number;
    maxBenchInnings?: number;
    rotateEvery?: number;
    gameContext?: {
      previousLineups?: any[];
      gameNumber?: number;
      playerStats?: Map<string, PlayerStats>;
    };
  } = {}
): Promise<LineupResult> {
  // Convert players to position pools
  const pools = convertToPositionPools(players);
  
  // Build request
  const request: LineupRequest = {
    teamId,
    pools,
    availablePlayers: availablePlayers.map(p => p.id).filter(id => id !== undefined) as string[],
    mode: options.mode || 'recreational',
    gameContext: options.gameContext,
    options: {
      enforceRotation: options.enforceRotation ?? true,
      minPlayingTime: options.minPlayingTime ?? 25,
      maxBenchInnings: options.maxBenchInnings ?? 2,
      rotateEvery: options.rotateEvery,
      pitcherRotation: {
        enabled: true,
        frequency: 2
      }
    }
  };
  
  const generator = new LineupGenerator();
  return generator.generate(request);
}

/**
 * Enable debug mode for a team
 */
export function enableDebugMode(teamId: string): void {
  LineupDebugger.enableForTeam(teamId);
}

/**
 * Disable debug mode for a team
 */
export function disableDebugMode(teamId: string): void {
  LineupDebugger.disableForTeam(teamId);
}
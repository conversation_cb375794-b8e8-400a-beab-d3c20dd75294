import { supabase } from '@/integrations/supabase/client';
import { Player, Lineup as OldLineup } from '@/contexts/TeamContext';
import { generateLineup as newGenerateLineup, LineupResult, PlayerStats, convertToPositionPools } from './index';
import { convertToOldLineup } from './converter';

// Extend window for queryClient
declare global {
  interface Window {
    queryClient?: any;
  }
}

/**
 * Get position pools for a team from the database
 */
export async function getPositionPools(teamId: string) {
  // In the current implementation, position pools are stored in player.teamRoles
  // We need to fetch all players and convert to pools
  const { data: players, error } = await supabase
    .from('players')
    .select('*')
    .eq('team_id', teamId);
    
  if (error) {
    console.error('Error fetching players for position pools:', error);
    return [];
  }
  
  // Convert players to position pools format
  return convertToPositionPools(players || []);
}

/**
 * Get recent lineups for playing time calculations
 */
export async function getRecentLineups(teamId: string, limit: number = 5): Promise<OldLineup[]> {
  const { data: lineups, error } = await supabase
    .from('lineups')
    .select('*')
    .eq('team_id', teamId)
    .order('created_at', { ascending: false })
    .limit(limit);
    
  if (error) {
    console.error('Error fetching recent lineups:', error);
    return [];
  }
  
  return lineups || [];
}

/**
 * Calculate player stats from recent games
 */
export async function calculatePlayerStats(
  teamId: string,
  playerIds: string[]
): Promise<Map<string, PlayerStats>> {
  const statsMap = new Map<string, PlayerStats>();
  
  // Initialize all players
  for (const playerId of playerIds) {
    statsMap.set(playerId, {
      fieldInnings: 0,
      benchInnings: 0,
      positionCounts: {},
      benchStreak: 0
    });
  }
  
  // Fetch position history
  const { data: history, error } = await supabase
    .from('player_position_history')
    .select('*')
    .in('player_id', playerIds)
    .order('game_date', { ascending: false })
    .limit(100); // Last ~10 games
    
  if (error) {
    console.error('Error fetching player position history:', error);
    return statsMap;
  }
  
  // Process history
  for (const record of history || []) {
    const stats = statsMap.get(record.player_id);
    if (!stats) continue;
    
    if (record.position.startsWith('bench')) {
      stats.benchInnings++;
    } else {
      stats.fieldInnings++;
      stats.positionCounts[record.position] = (stats.positionCounts[record.position] || 0) + 1;
    }
  }
  
  // Calculate bench streaks (would need lineup-by-lineup data)
  // For now, return basic stats
  return statsMap;
}

/**
 * Main integration function - Generate lineup with all team data
 */
export async function generateLineupForTeam(
  team: { id: string; name: string; rotationRules?: any },
  availablePlayers: Player[],
  options: {
    gameDate?: string;
    gameNumber?: number;
  } = {}
): Promise<LineupResult> {
  try {
    // Get recent lineups for context
    const recentLineups = await getRecentLineups(team.id, 5);
    
    // Calculate player stats
    const playerIds = availablePlayers.map(p => p.id).filter(id => id) as string[];
    const playerStats = await calculatePlayerStats(team.id, playerIds);
    
    // Determine mode from rotation rules
    const mode = team.rotationRules?.competitiveMode ? 'competitive' : 'recreational';
    
    // Generate lineup
    const result = await newGenerateLineup(
      team.id,
      availablePlayers, // All players for pools
      availablePlayers, // Available for this game
      {
        mode,
        enforceRotation: !team.rotationRules?.noRotation,
        minPlayingTime: team.rotationRules?.competitiveMinPlayingTime || 25,
        maxBenchInnings: team.rotationRules?.maxConsecutiveBenchInnings || 2,
        rotateEvery: team.rotationRules?.rotateLineupEvery,
        gameContext: {
          previousLineups: recentLineups,
          gameNumber: options.gameNumber,
          playerStats
        }
      }
    );
    
    return result;
    
  } catch (error) {
    console.error('Error generating lineup for team:', error);
    return {
      success: false,
      errors: [`Failed to generate lineup: ${error.message || 'Unknown error'}`]
    };
  }
}

/**
 * Save position pools changes and notify UI
 */
export async function savePositionPools(teamId: string, pools: any[]) {
  try {
    // Convert pools back to player teamRoles updates
    const updates: Promise<any>[] = [];
    
    // This would need the actual implementation based on your data model
    // For now, just emit the event
    
    // Emit event for UI updates
    window.dispatchEvent(new CustomEvent('position-pools-updated', {
      detail: { teamId, pools }
    }));
    
    // Invalidate queries (if using React Query)
    if (window.queryClient) {
      window.queryClient.invalidateQueries(['position-pools', teamId]);
      window.queryClient.invalidateQueries(['team-roster', teamId]);
      window.queryClient.invalidateQueries(['lineup-builder', teamId]);
    }
    
    return { success: true };
  } catch (error) {
    console.error('Failed to save position pools:', error);
    throw error;
  }
}
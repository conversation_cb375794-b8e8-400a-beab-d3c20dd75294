import { LineupRequest, LineupResult } from './types';
import { PreGenerationValidator } from './validators/pre-generation';
import { PostGenerationValidator } from './validators/post-generation';
import { FairnessSolver } from './solvers/fairness-solver';
import { CompetitiveSolver } from './solvers/competitive-solver';
import { LineupDebugger } from './debug';

/**
 * Main lineup generator - Orchestrates validation and solving
 */
export class LineupGenerator {
  private preValidator: PreGenerationValidator;
  private postValidator: PostGenerationValidator;
  
  constructor() {
    this.preValidator = new PreGenerationValidator();
    this.postValidator = new PostGenerationValidator();
  }
  
  async generate(request: LineupRequest): Promise<LineupResult> {
    console.log("🟢 NEW ALGORITHM EXECUTING - generator.ts");
    console.log("🟢 LineupGenerator.generate() called with mode:", request.mode);
    
    const startTime = Date.now();
    
    // Enable debug logging if requested
    if (request.mode === 'competitive' || LineupDebugger.isEnabled(request.teamId)) {
      console.log('[LineupGenerator] Starting generation', {
        teamId: request.teamId,
        mode: request.mode,
        availablePlayers: request.availablePlayers.length,
        pools: request.pools.map(p => `${p.position}: ${p.players.length} players`)
      });
    }
    
    try {
      // Pre-validation
      const validation = this.preValidator.validate(request);
      if (!validation.valid) {
        LineupDebugger.log(request.teamId, 'Pre-validation failed', validation);
        return {
          success: false,
          errors: validation.errors,
          diagnostics: {
            attemptedAssignments: 0,
            backtrackCount: 0,
            executionTime: Date.now() - startTime
          }
        };
      }
      
      // Log warnings if any
      if (validation.warnings && validation.warnings.length > 0) {
        LineupDebugger.log(request.teamId, 'Pre-validation warnings', validation.warnings);
      }
      
      // Choose solver based on mode
      const solver = request.mode === 'competitive'
        ? new CompetitiveSolver()
        : new FairnessSolver();
      
      LineupDebugger.log(request.teamId, `Using ${request.mode} solver`);
      
      // Generate lineup
      const result = solver.solve(request);
      
      if (!result.success) {
        LineupDebugger.log(request.teamId, 'Solver failed', result);
        return result;
      }
      
      // Post-validation (catch any bugs)
      const postValidation = this.postValidator.validate(result.lineup!, request);
      if (!postValidation.valid) {
        console.error('[CRITICAL BUG] Invalid lineup generated', {
          lineup: result.lineup,
          validation: postValidation
        });
        
        // In production, return error. In dev, throw to catch bugs
        if (process.env.NODE_ENV === 'development') {
          throw new Error(`Invalid lineup generated: ${postValidation.errors.join(', ')}`);
        }
        
        return {
          success: false,
          errors: ['Internal error: Invalid lineup generated', ...postValidation.errors],
          diagnostics: result.diagnostics
        };
      }
      
      // Log post-validation warnings
      if (postValidation.warnings && postValidation.warnings.length > 0) {
        LineupDebugger.log(request.teamId, 'Post-validation warnings', postValidation.warnings);
      }
      
      // Success!
      const totalTime = Date.now() - startTime;
      LineupDebugger.log(request.teamId, 'Generation successful', {
        executionTime: totalTime,
        diagnostics: result.diagnostics
      });
      
      // Add execution time to diagnostics
      if (result.diagnostics) {
        result.diagnostics.executionTime = totalTime;
      }
      
      return result;
      
    } catch (error) {
      console.error('[LineupGenerator] Unexpected error:', error);
      return {
        success: false,
        errors: [`Unexpected error: ${error.message || 'Unknown error'}`],
        diagnostics: {
          attemptedAssignments: 0,
          backtrackCount: 0,
          executionTime: Date.now() - startTime
        }
      };
    }
  }
  
  /**
   * Generate multiple lineups for a series
   */
  async generateSeries(
    request: LineupRequest,
    gameCount: number
  ): Promise<LineupResult[]> {
    const results: LineupResult[] = [];
    
    for (let i = 0; i < gameCount; i++) {
      const gameRequest = {
        ...request,
        gameContext: {
          ...request.gameContext,
          gameNumber: i + 1,
          previousLineups: results
            .filter(r => r.success && r.lineup)
            .map(r => r.lineup!)
        }
      };
      
      const result = await this.generate(gameRequest);
      results.push(result);
      
      // Stop on first failure
      if (!result.success) {
        break;
      }
    }
    
    return results;
  }
}
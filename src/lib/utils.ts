
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { Posi<PERSON>, Player, InningLineup, Lineup } from "@/contexts/TeamContext";
import { v4 as uuidv4 } from "uuid";
import { supabase } from "@/integrations/supabase/client";

// <PERSON>-Yates shuffle for randomizing equal-priority groups
function shuffleArray<T>(array: T[]): T[] {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array;
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function generateId(): string {
  return uuidv4();
}

export const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'numeric',
    day: 'numeric',
    year: 'numeric',
  });
};

export const POSITIONS: Position[] = [
  "Pitcher",
  "Catcher",
  "First Base",
  "Second Base",
  "Shortstop",
  "Third Base",
  "Left Field",
  "Center Field",
  "Right Field",
  "Middle Infield",
  "3B/MI/SS/2B"
];

export const POSITION_OPTIONS = [
  { value: "none", label: "None" },
  { value: "Left Field", label: "Left Field" },
  { value: "Left Center", label: "Left Center" },
  { value: "Center Field", label: "Center Field" },
  { value: "Right Center", label: "Right Center" },
  { value: "Right Field", label: "Right Field" },
  { value: "Third Base", label: "Third Base" },
  { value: "Shortstop", label: "Shortstop" },
  { value: "Second Base", label: "Second Base" },
  { value: "Middle Infield", label: "Middle Infield" },
  { value: "3B/MI/SS/2B", label: "3B/MI/SS/2B" },
];

// Check if a player can play a specific position based on their restrictions
export function canPlayPosition(player: Player, position: string): boolean {
  // Check if player or positionRestrictions is undefined
  if (!player || !player.positionRestrictions) {
    return true; // If no player or no restrictions, assume they can play the position
  }

  // Check position restrictions
  if (position === "Pitcher" && player.positionRestrictions.pitcher) return false;
  if (position === "Catcher" && player.positionRestrictions.catcher) return false;
  if (position === "First Base" && player.positionRestrictions.firstBase) return false;

  // Check "other" restriction
  if (player.positionRestrictions.other) {
    if (player.positionRestrictions.other === position) return false;

    // Handle combined position restrictions
    if (player.positionRestrictions.other === "Middle Infield" &&
        (position === "Shortstop" || position === "Second Base")) {
      return false;
    }

    if (player.positionRestrictions.other === "3B/MI/SS/2B" &&
        (position === "Third Base" || position === "Shortstop" ||
         position === "Second Base" || position === "Middle Infield")) {
      return false;
    }
  }

  return true;
}

// Function to check if a player has a restriction for a position
export function checkPositionRestriction(playerObj: Player, positionId: string): string | null {
  // Check if player or positionRestrictions is undefined
  if (!playerObj || !playerObj.positionRestrictions) {
    return null; // No restrictions if player or restrictions are undefined
  }

  // Check pitcher restriction
  if (positionId === "pitcher" && playerObj.positionRestrictions.pitcher) {
    return "Pitcher";
  }

  // Check catcher restriction
  if (positionId === "catcher" && playerObj.positionRestrictions.catcher) {
    return "Catcher";
  }

  // Check first base restriction
  if (positionId === "firstBase" && playerObj.positionRestrictions.firstBase) {
    return "First Base";
  }

  // Check other restrictions
  if (playerObj.positionRestrictions.other) {
    // Direct match
    const positionName = getPositionDisplayName(positionId);
    if (playerObj.positionRestrictions.other === positionName) {
      return positionName;
    }

    // Middle infield restriction (shortstop, second base)
    if (playerObj.positionRestrictions.other === "Middle Infield" &&
        (positionId === "shortstop" || positionId === "secondBase")) {
      return "Middle Infield";
    }

    // 3B/MI/SS/2B restriction
    if (playerObj.positionRestrictions.other === "3B/MI/SS/2B" &&
        (positionId === "thirdBase" || positionId === "shortstop" ||
         positionId === "secondBase")) {
      return "3B/MI/SS/2B";
    }
  }

  return null;
}

// Helper function to get position display name
export function getPositionDisplayName(positionId: string): string {
  switch (positionId) {
    case "pitcher": return "Pitcher";
    case "catcher": return "Catcher";
    case "firstBase": return "First Base";
    case "secondBase": return "Second Base";
    case "thirdBase": return "Third Base";
    case "shortstop": return "Shortstop";
    case "leftField": return "Left Field";
    case "centerField": return "Center Field";
    case "rightField": return "Right Field";
    default: return positionId;
  }
}

// Helper function to get position label
export function getPositionLabel(positionId: string): string {
  switch (positionId) {
    case "pitcher": return "Pitcher";
    case "catcher": return "Catcher";
    case "firstBase": return "First Base";
    case "secondBase": return "Second Base";
    case "thirdBase": return "Third Base";
    case "shortstop": return "Shortstop";
    case "leftField": return "Left Field";
    case "centerField": return "Center Field";
    case "rightField": return "Right Field";
    default:
      if (positionId.startsWith("bench")) {
        const benchNumber = positionId.replace("bench", "");
        return `Bench ${benchNumber}`;
      }
      return positionId;
  }
}

// ENHANCED: Generate complete lineup with optimal rotation
export function generateCompleteLineup(
  availablePlayers: Player[],
  totalInnings: number,
  rules: {
    limitBenchTime: boolean;
    allowPitcherRotation: boolean;
    allowCatcherRotation: boolean;
    respectPositionLockouts: boolean;
    equalPlayingTime?: boolean;
    rotateLineupEvery?: number;
    rotatePitcherEvery?: number;
  } = {
    limitBenchTime: true,
    allowPitcherRotation: true,
    allowCatcherRotation: true,
    respectPositionLockouts: true,
    equalPlayingTime: true,
    rotateLineupEvery: 1,
    rotatePitcherEvery: 2
  }
): InningLineup[] {
  console.log(`🚀 GENERATING COMPLETE LINEUP - ${totalInnings} innings for ${availablePlayers.length} players`);

  if (availablePlayers.length < 8) {
    throw new Error("Not enough players to create a lineup. Need at least 8 players.");
  }

  return generateOptimalLineup(availablePlayers, totalInnings, rules);
}

export function createInitialInningLineup(availablePlayers: Player[]) {
  if (availablePlayers.length < 8) {
    throw new Error("Not enough players to create a lineup. Need at least 8 players.");
  }

  // Create a copy to avoid modifying the original array
  let remaining = [...availablePlayers];
  const positions = {
    leftField: "",
    centerField: "",
    rightField: "",
    thirdBase: "",
    shortstop: "",
    secondBase: "",
    firstBase: "",
    catcher: "",
    pitcher: ""
  };



  // First, find players for positions with restrictions
  // Start with pitcher (most restricted position typically)
  const pitcherIdx = remaining.findIndex(p => !p.positionRestrictions.pitcher);
  if (pitcherIdx !== -1) {
    positions.pitcher = remaining[pitcherIdx].name;
    remaining.splice(pitcherIdx, 1);
  } else {
    // If no unrestricted pitcher, use anyone (coach will need to adjust)
    positions.pitcher = remaining[0].name;
    remaining.splice(0, 1);
  }

  // Find catcher
  const catcherIdx = remaining.findIndex(p => !p.positionRestrictions.catcher);
  if (catcherIdx !== -1) {
    positions.catcher = remaining[catcherIdx].name;
    remaining.splice(catcherIdx, 1);
  } else {
    positions.catcher = remaining[0].name;
    remaining.splice(0, 1);
  }

  // Find first base
  const firstBaseIdx = remaining.findIndex(p => !p.positionRestrictions.firstBase);
  if (firstBaseIdx !== -1) {
    positions.firstBase = remaining[firstBaseIdx].name;
    remaining.splice(firstBaseIdx, 1);
  } else {
    positions.firstBase = remaining[0].name;
    remaining.splice(0, 1);
  }

  // Assign remaining positions
  const positionKeys: (keyof typeof positions)[] = [
    "thirdBase", "shortstop", "secondBase",
    "leftField", "centerField", "rightField"
  ];

  for (const pos of positionKeys) {
    // Try to find someone without restriction for this position
    const idx = remaining.findIndex(p => {
      const positionName = pos === "thirdBase" ? "Third Base" :
                         pos === "shortstop" ? "Shortstop" :
                         pos === "secondBase" ? "Second Base" :
                         pos === "leftField" ? "Left Field" :
                         pos === "centerField" ? "Center Field" : "Right Field";

      return canPlayPosition(p, positionName);
    });

    if (idx !== -1) {
      positions[pos] = remaining[idx].name;
      remaining.splice(idx, 1);
    } else if (remaining.length > 0) {
      positions[pos] = remaining[0].name;
      remaining.splice(0, 1);
    }
  }

  return {
    inning: 1,
    positions: {
      ...positions,
      bench: remaining.map(p => p.name)
    }
  };
}

export function rotatePlayersForNextInning(
  previousInning: InningLineup,
  availablePlayers: Player[],
  rules: {
    limitBenchTime: boolean;
    allowPitcherRotation: boolean;
    allowCatcherRotation: boolean;
    respectPositionLockouts: boolean;
    equalPlayingTime?: boolean;
    rotateLineupEvery?: number;
    rotatePitcherEvery?: number;
  } = {
    limitBenchTime: true,
    allowPitcherRotation: false,
    allowCatcherRotation: true,
    respectPositionLockouts: true,
    equalPlayingTime: true,
    rotateLineupEvery: 1,
    rotatePitcherEvery: 2
  },
  playerPositionHistory?: Record<string, {
    fieldInnings: number,
    benchInnings: number
  }>
): InningLineup {
  console.log(`🚀 Starting AI-POWERED rotation for inning ${previousInning.inning + 1}`);
  console.log(`📋 Rotation rules:`, {
    limitBenchTime: rules.limitBenchTime,
    allowPitcherRotation: rules.allowPitcherRotation,
    allowCatcherRotation: rules.allowCatcherRotation,
    equalPlayingTime: rules.equalPlayingTime,
    rotateLineupEvery: rules.rotateLineupEvery,
    rotatePitcherEvery: rules.rotatePitcherEvery
  });
  console.log(`👥 Available players:`, availablePlayers.map(p => p.name));
  console.log(`📍 Previous inning positions:`, previousInning.positions);

  // Check if we should rotate based on the rotation frequency
  const rotateLineupEvery = rules.rotateLineupEvery || 1;
  const currentInningNumber = previousInning.inning + 1;
  const shouldRotateThisInning = (currentInningNumber - 1) % rotateLineupEvery === 0;

  console.log(`🔄 Rotation check for inning ${currentInningNumber}`);
  console.log(`📋 Rotation frequency: every ${rotateLineupEvery} innings`);
  console.log(`✅ Will rotate: ${shouldRotateThisInning}`);

  return aiPoweredRotationAlgorithm(previousInning, availablePlayers, rules, playerPositionHistory, shouldRotateThisInning);
}

// ENHANCED: Global Lineup Generation with Position Tracking
export function generateOptimalLineup(
  availablePlayers: Player[],
  totalInnings: number,
  rules: {
    limitBenchTime: boolean;
    allowPitcherRotation: boolean;
    allowCatcherRotation: boolean;
    respectPositionLockouts: boolean;
    equalPlayingTime?: boolean;
    rotateLineupEvery?: number;
    rotatePitcherEvery?: number;
    _randomSeed?: number;
  }
): InningLineup[] {
  console.log(`🚀 GENERATING OPTIMAL LINEUP - ${totalInnings} innings`);
  console.log(`🎲 Randomization enabled:`, !!rules._randomSeed, rules._randomSeed);

  // Initialize enhanced tracking maps
  let inningsPlayed: Record<string, number> = {};
  let benchStreak: Record<string, number> = {};
  let lastPositionTimestamp: Record<string, Record<string, number>> = {};
  let inningsPitched: Record<string, number> = {};

  // Define all baseball positions
  const positions = [
    'pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop',
    'thirdBase', 'leftField', 'centerField', 'rightField'
  ];

  // Initialize all players
  availablePlayers.forEach(player => {
    inningsPlayed[player.name] = 0;
    benchStreak[player.name] = 0;
    inningsPitched[player.name] = 0;
    lastPositionTimestamp[player.name] = {};
    positions.forEach(pos => {
      lastPositionTimestamp[player.name][pos] = 0;
    });
  });

  const lineup: InningLineup[] = [];

  // Helper function to check if player can play position
  function canAssignPosition(player: Player, position: string, assignedThisInning: Set<string>): boolean {
    if (assignedThisInning.has(player.name)) return false;

    if (!rules.respectPositionLockouts) return true;

    // Check position restrictions using the correct position mapping
    const positionDisplayName = getPositionDisplayName(position);
    console.log(`🔍 Checking if ${player.name} can play ${position} (${positionDisplayName})`);

    const canPlay = canPlayPosition(player, positionDisplayName);
    console.log(`🔍 Result: ${player.name} ${canPlay ? 'CAN' : 'CANNOT'} play ${positionDisplayName}`);

    if (!canPlay) return false;

    // Check bench streak limit (only if we have excess players)
    if (availablePlayers.length > positions.length && benchStreak[player.name] >= (rules.limitBenchTime ? 2 : 999)) {
      return false;
    }

    return true;
  }

  // Generate each inning
  for (let inning = 1; inning <= totalInnings; inning++) {
    console.log(`\n🏟️  GENERATING INNING ${inning}`);

    const inningLineup: InningLineup = {
      inning: inning,
      positions: {
        pitcher: '',
        catcher: '',
        firstBase: '',
        secondBase: '',
        shortstop: '',
        thirdBase: '',
        leftField: '',
        centerField: '',
        rightField: '',
        bench: []
      }
    };

    const assignedThisInning = new Set<string>();

    // Step 1: Handle bench assignments if we have more players than positions
    if (availablePlayers.length > positions.length) {
      const playersToBeach = availablePlayers.length - positions.length;

      // Sort players by inningsPlayed (desc) then benchStreak (asc) to find who should be benched
      let benchCandidates = [...availablePlayers].sort((a, b) => {
        const aPlayed = inningsPlayed[a.name];
        const bPlayed = inningsPlayed[b.name];
        if (aPlayed !== bPlayed) {
          return bPlayed - aPlayed; // Descending - most played first
        }
        // If tied on playing time, bench those with LOWER bench streak
        return benchStreak[a.name] - benchStreak[b.name]; // Ascending - least benched first
      });

      // Add some randomization if requested to create variation
      if (rules._randomSeed && inning > 1) {
        console.log(`🎲 Applying bench randomization for inning ${inning}`);
        console.log(`   Bench candidates before randomization:`, benchCandidates.map(p =>
          `${p.name}(played=${inningsPlayed[p.name]}, bench=${benchStreak[p.name]})`
        ));

        // Find players with equal playing time and randomly shuffle them
        const groups = new Map<string, Player[]>();
        benchCandidates.forEach(player => {
          const key = `${inningsPlayed[player.name]}-${benchStreak[player.name]}`;
          if (!groups.has(key)) groups.set(key, []);
          groups.get(key)!.push(player);
        });

        console.log(`   Bench priority groups:`, Array.from(groups.entries()).map(([key, players]) =>
          `${key}: [${players.map(p => p.name).join(', ')}]`
        ));

        // Shuffle within equal groups
        benchCandidates = [];
        Array.from(groups.entries())
          .sort(([a], [b]) => a.localeCompare(b)) // Maintain priority order
          .forEach(([, group]) => {
            if (group.length > 1) {
              // Simple shuffle for variety
              const shuffled = [...group];
              const seed = rules._randomSeed! * 1000 + inning * 50;
              for (let i = shuffled.length - 1; i > 0; i--) {
                const j = Math.floor((seed + i) % (i + 1));
                [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
              }
              console.log(`   Shuffled bench group [${group.map(p => p.name).join(', ')}] -> [${shuffled.map(p => p.name).join(', ')}]`);
              benchCandidates.push(...shuffled);
            } else {
              benchCandidates.push(...group);
            }
          });

        console.log(`   Final bench order after randomization:`, benchCandidates.map(p => p.name));
      }

      // Shuffle equal-priority bench groups for fairness when no seed is set
      if (rules.equalPlayingTime && !rules._randomSeed) {
        const benchGroups = new Map<string, Player[]>();
        benchCandidates.forEach(p => {
          const key = `${inningsPlayed[p.name]}-${benchStreak[p.name]}`;
          if (!benchGroups.has(key)) benchGroups.set(key, []);
          benchGroups.get(key)!.push(p);
        });
        benchCandidates = Array.from(benchGroups.values())
          .flatMap(group => group.length > 1 ? shuffleArray(group) : group);
      }

      for (let i = 0; i < playersToBeach; i++) {
        const playerToBench = benchCandidates[i];
        inningLineup.positions.bench.push(playerToBench.name);
        assignedThisInning.add(playerToBench.name);
        benchStreak[playerToBench.name]++;
      }
    }

    // Step 2: Assign field positions
    for (const position of positions) {
      // Get eligible players for this position
      let eligiblePlayers = availablePlayers.filter(player =>
        canAssignPosition(player, position, assignedThisInning)
      );

      // Special handling for Pitcher position
      if (position === 'pitcher') {
        eligiblePlayers.sort((a, b) => {
          const aPitched = inningsPitched[a.name];
          const bPitched = inningsPitched[b.name];
          if (aPitched !== bPitched) {
            return aPitched - bPitched; // Ascending - fewest innings pitched first
          }
          const aLastPitched = lastPositionTimestamp[a.name][position];
          const bLastPitched = lastPositionTimestamp[b.name][position];
          return aLastPitched - bLastPitched; // Ascending - longest since last pitch first
        });
      } else {
        // Regular position sorting
        eligiblePlayers.sort((a, b) => {
          const aPlayed = inningsPlayed[a.name];
          const bPlayed = inningsPlayed[b.name];
          if (aPlayed !== bPlayed) {
            return aPlayed - bPlayed; // Ascending - least played first
          }
          const aLastPlayed = lastPositionTimestamp[a.name][position];
          const bLastPlayed = lastPositionTimestamp[b.name][position];
          return aLastPlayed - bLastPlayed; // Ascending - least recently at this position first
        });
      }

      // Add randomization for variety when players have equal priority
      if (rules._randomSeed && eligiblePlayers.length > 1) {
        console.log(`🎲 Applying randomization for inning ${inning}, position ${position}`);
        console.log(`   Eligible players before randomization:`, eligiblePlayers.map(p => p.name));

        // Group players by their priority scores
        const groups = new Map<string, Player[]>();
        eligiblePlayers.forEach(player => {
          let key: string;
          if (position === 'pitcher') {
            key = `${inningsPitched[player.name]}-${lastPositionTimestamp[player.name][position]}`;
          } else {
            key = `${inningsPlayed[player.name]}-${lastPositionTimestamp[player.name][position]}`;
          }
          if (!groups.has(key)) groups.set(key, []);
          groups.get(key)!.push(player);
        });

        console.log(`   Priority groups:`, Array.from(groups.entries()).map(([key, players]) =>
          `${key}: [${players.map(p => p.name).join(', ')}]`
        ));

        // Shuffle within equal priority groups
        eligiblePlayers = [];
        Array.from(groups.entries())
          .sort(([a], [b]) => a.localeCompare(b)) // Maintain priority order
          .forEach(([, group]) => {
            if (group.length > 1) {
              // Shuffle players with equal priority
              const shuffled = [...group];
              const seed = rules._randomSeed! * 1000 + inning * 100 + positions.indexOf(position);
              for (let i = shuffled.length - 1; i > 0; i--) {
                const j = Math.floor((seed * (i + 1)) % (i + 1));
                [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
              }
              console.log(`   Shuffled group [${group.map(p => p.name).join(', ')}] -> [${shuffled.map(p => p.name).join(', ')}]`);
              eligiblePlayers.push(...shuffled);
            } else {
              eligiblePlayers.push(...group);
            }
          });

        console.log(`   Final order after randomization:`, eligiblePlayers.map(p => p.name));
      }

      // Shuffle equal-priority field groups for fairness when no seed is set
      if (rules.equalPlayingTime && !rules._randomSeed) {
        const fieldGroups = new Map<string, Player[]>();
        eligiblePlayers.forEach(p => {
          const key = `${inningsPlayed[p.name]}-${lastPositionTimestamp[p.name][position]}`;
          if (!fieldGroups.has(key)) fieldGroups.set(key, []);
          fieldGroups.get(key)!.push(p);
        });
        eligiblePlayers = Array.from(fieldGroups.values())
          .flatMap(group => group.length > 1 ? shuffleArray(group) : group);
      }

      // If no eligible players, check if it's due to restrictions
      if (eligiblePlayers.length === 0) {
        console.log(`   ❌ No eligible players for ${position} - checking restrictions...`);

        const unassignedPlayers = availablePlayers.filter(player => !assignedThisInning.has(player.name));
        const positionDisplayName = getPositionDisplayName(position);

        // Check which players are restricted from this position
        const restrictedPlayers = unassignedPlayers.filter(p => !canPlayPosition(p, positionDisplayName));
        const unrestricted = unassignedPlayers.filter(p => canPlayPosition(p, positionDisplayName));

        console.log(`   🚫 Players restricted from ${position}: ${restrictedPlayers.map(p => p.name).join(', ')}`);
        console.log(`   ✅ Unrestricted players: ${unrestricted.map(p => p.name).join(', ')}`);

        if (unrestricted.length > 0) {
          // Use unrestricted players first
          eligiblePlayers = unrestricted;
          console.log(`   ✅ Using unrestricted players for ${position}`);
        } else if (rules.respectPositionLockouts) {
          // If we're respecting restrictions and no unrestricted players available,
          // this indicates a lineup generation failure
          console.log(`   ❌ LINEUP GENERATION FAILURE: No unrestricted players available for ${position}`);
          throw new Error(`Cannot generate valid lineup: No players available for ${position} without violating position restrictions`);
        } else {
          // Fallback: use any unassigned player (restrictions will be ignored)
          eligiblePlayers = unassignedPlayers;
          console.log(`   ⚠️ Using fallback assignment (ignoring restrictions) for ${position}`);
        }
      }

      // Assign the best player (with optional randomization for variety)
      let selectedPlayer = eligiblePlayers[0];

      // Add some chaos factor for variety when randomization is enabled
      if (rules._randomSeed && eligiblePlayers.length > 1 && inning > 1) {
        // Occasionally pick 2nd or 3rd best option for variety (20% chance)
        const chaos = (rules._randomSeed * 1000 + inning * 100 + positions.indexOf(position)) % 100;
        if (chaos < 20 && eligiblePlayers.length > 1) {
          const chaosIndex = Math.min(1, eligiblePlayers.length - 1);
          selectedPlayer = eligiblePlayers[chaosIndex];
          console.log(`   🎲 CHAOS FACTOR: Picked ${selectedPlayer.name} (option ${chaosIndex + 1}) instead of ${eligiblePlayers[0].name} for variety`);
        } else if (chaos < 10 && eligiblePlayers.length > 2) {
          const chaosIndex = Math.min(2, eligiblePlayers.length - 1);
          selectedPlayer = eligiblePlayers[chaosIndex];
          console.log(`   🎲 CHAOS FACTOR: Picked ${selectedPlayer.name} (option ${chaosIndex + 1}) instead of ${eligiblePlayers[0].name} for variety`);
        }
      }

      (inningLineup.positions as any)[position] = selectedPlayer.name;
      assignedThisInning.add(selectedPlayer.name);

      // Update tracking maps
      inningsPlayed[selectedPlayer.name]++;
      benchStreak[selectedPlayer.name] = 0; // Reset bench streak since they're playing
      lastPositionTimestamp[selectedPlayer.name][position] = inning;

      if (position === 'pitcher') {
        inningsPitched[selectedPlayer.name]++;
      }
    }

    lineup.push(inningLineup);
  }

  return lineup;
}

// COMPLETELY REWRITTEN: Super Smart Rotation Algorithm
function aiPoweredRotationAlgorithm(
  previousInning: InningLineup,
  availablePlayers: Player[],
  rules: {
    limitBenchTime: boolean;
    allowPitcherRotation: boolean;
    allowCatcherRotation: boolean;
    respectPositionLockouts: boolean;
    equalPlayingTime?: boolean;
    rotateLineupEvery?: number;
    rotatePitcherEvery?: number;
  },
  playerPositionHistory?: Record<string, {
    fieldInnings: number,
    benchInnings: number
  }>,
  shouldRotateThisInning: boolean = true
): InningLineup {
  console.log(`🚀 ENHANCED ROTATION - Inning ${previousInning.inning + 1}`);
  console.log(`📋 Rules:`, rules);
  console.log(`🔄 Should rotate this inning: ${shouldRotateThisInning}`);

  // If we shouldn't rotate this inning, just copy the previous inning with updated inning number
  if (!shouldRotateThisInning) {
    console.log(`⏸️ No rotation this inning - copying previous lineup`);
    return {
      inning: previousInning.inning + 1,
      positions: { ...previousInning.positions }
    };
  }

  // ENHANCED APPROACH: Use position tracking for better decisions
  // Extract current state from previous innings to make smarter choices

  const allPlayerNames = availablePlayers.map(p => p.name).filter(Boolean);
  const playerMap = new Map<string, Player>();
  availablePlayers.forEach(player => {
    if (player?.name) playerMap.set(player.name, player);
  });

  // Helper function
  const canPlayerPlayPosition = (playerName: string, positionName: string): boolean => {
    const player = playerMap.get(playerName);
    if (!player || !rules.respectPositionLockouts) return true;
    return canPlayPosition(player, positionName);
  };

  // Get current positions
  const currentPositions = previousInning.positions;
  const currentBench = currentPositions.bench.filter(Boolean);

  console.log(`📍 Previous positions:`, currentPositions);
  console.log(`🪑 Current bench: [${currentBench.join(', ')}]`);

  // SIMPLE AND EFFECTIVE ROTATION STRATEGY:
  // 1. Move bench players to field positions
  // 2. Move some field players to bench
  // 3. Rotate remaining field players to different positions
  // 4. Respect position restrictions and special rules

  const newPositions = {
    pitcher: '',
    catcher: '',
    firstBase: '',
    secondBase: '',
    shortstop: '',
    thirdBase: '',
    leftField: '',
    centerField: '',
    rightField: '',
    bench: [] as string[]
  };

  // Track who's been assigned
  const assignedPlayers = new Set<string>();

  // Get all field position keys
  const allFieldPositionKeys = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];

  // Get current field players
  const currentFieldPlayers = allFieldPositionKeys
    .map(pos => currentPositions[pos as keyof typeof currentPositions] as string)
    .filter(Boolean);

  console.log(`⚾ Current field players: [${currentFieldPlayers.join(', ')}]`);

  // STEP 1: Handle Pitcher Rotation
  const currentInningNumber = previousInning.inning + 1;
  const shouldRotatePitcher = rules.allowPitcherRotation &&
    rules.rotatePitcherEvery &&
    rules.rotatePitcherEvery > 0 &&
    currentInningNumber > 1 &&
    ((currentInningNumber - 1) % rules.rotatePitcherEvery === 0);

  console.log(`⚾ PITCHER ROTATION CHECK:`);
  console.log(`   - Current inning: ${currentInningNumber}`);
  console.log(`   - Allow pitcher rotation: ${rules.allowPitcherRotation}`);
  console.log(`   - Rotate pitcher every: ${rules.rotatePitcherEvery} innings`);
  console.log(`   - Should rotate: ${shouldRotatePitcher}`);

  if (shouldRotatePitcher) {
    // Find a bench player who can pitch
    const benchPitchers = currentBench.filter(player => canPlayerPlayPosition(player, 'Pitcher'));
    console.log(`   - Available bench pitchers: [${benchPitchers.join(', ')}]`);

    if (benchPitchers.length > 0) {
      newPositions.pitcher = benchPitchers[0];
      assignedPlayers.add(benchPitchers[0]);
      console.log(`⚾ ✅ NEW PITCHER: ${benchPitchers[0]} (from bench)`);
    } else {
      // No suitable bench pitcher, keep current
      newPositions.pitcher = currentPositions.pitcher;
      assignedPlayers.add(currentPositions.pitcher);
      console.log(`⚾ ⚠️ KEEPING PITCHER: ${currentPositions.pitcher} (no suitable bench replacement)`);
    }
  } else {
    // Not a pitcher rotation inning, keep current
    newPositions.pitcher = currentPositions.pitcher;
    assignedPlayers.add(currentPositions.pitcher);
    console.log(`⚾ ➡️ KEEPING PITCHER: ${currentPositions.pitcher} (not rotation inning)`);
  }

  // STEP 2: Rotate ALL remaining bench players into field positions
  const remainingBench = currentBench.filter(player => !assignedPlayers.has(player));
  // Fairness: sort bench players by least bench innings first (to get off the bench)
  if (playerPositionHistory) {
    remainingBench.sort((a, b) => {
      const aBench = playerPositionHistory[a]?.benchInnings || 0;
      const bBench = playerPositionHistory[b]?.benchInnings || 0;
      return aBench - bBench;
    });
    console.log(`🎯 Bench fairness order: [${remainingBench.join(', ')}]`);
  }
  const nonPitcherFieldPositions = ['catcher', 'firstBase', 'secondBase', 'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];

  console.log(`🔄 Rotating ${remainingBench.length} bench players into field positions`);

  // FIXED ROTATION STRATEGY: Ensure we assign ALL players properly
  console.log(`🔄 FIXED ROTATION STRATEGY`);

  // Get all unassigned players (everyone except the pitcher)
  let playersToAssign = allPlayerNames.filter(player => !assignedPlayers.has(player));
  console.log(`👥 Unassigned players: [${playersToAssign.join(', ')}]`);
  console.log(`📊 Need to assign ${nonPitcherFieldPositions.length} field positions`);

  if (playersToAssign.length === 0) {
    console.error(`❌ ERROR: No unassigned players found! This should not happen.`);
    console.log(`🔍 Debug info:`);
    console.log(`   - All player names: [${allPlayerNames.join(', ')}]`);
    console.log(`   - Assigned players: [${Array.from(assignedPlayers).join(', ')}]`);
    console.log(`   - Current pitcher: ${newPositions.pitcher}`);
    return {
      inning: previousInning.inning + 1,
      positions: { ...previousInning.positions }
    };
  }

  // If limitBenchTime is enabled, prioritize bench players for field positions
  if (rules.limitBenchTime && currentBench.length > 0) {
    const benchPlayersToMove = currentBench.filter(p => playersToAssign.includes(p));
    // Sort benchPlayersToMove by least bench innings first for fairness
    if (playerPositionHistory) {
      benchPlayersToMove.sort((a, b) => {
        const aBench = playerPositionHistory[a]?.benchInnings || 0;
        const bBench = playerPositionHistory[b]?.benchInnings || 0;
        return aBench - bBench;
      });
      console.log(`🎯 benchPlayersToMove after sorting: [${benchPlayersToMove.join(', ')}]`);
    }
    const fieldPlayersToMove = playersToAssign.filter(p => !currentBench.includes(p));
    // Sort fieldPlayersToMove by most field innings first to rest tired players
    if (playerPositionHistory) {
      fieldPlayersToMove.sort((a, b) => {
        const aField = playerPositionHistory[a]?.fieldInnings || 0;
        const bField = playerPositionHistory[b]?.fieldInnings || 0;
        return bField - aField;
      });
      console.log(`🎯 fieldPlayersToMove after sorting: [${fieldPlayersToMove.join(', ')}]`);
    }
    playersToAssign = [...benchPlayersToMove, ...fieldPlayersToMove];
    console.log(`🏃 Prioritizing bench players: [${benchPlayersToMove.join(', ')}]`);
  }

  // Apply rotation offset to create variation between innings
  const rotationOffset = (currentInningNumber - 1) % playersToAssign.length;
  if (rotationOffset > 0 && playersToAssign.length > 1) {
    const rotatedPlayers = [
      ...playersToAssign.slice(rotationOffset),
      ...playersToAssign.slice(0, rotationOffset)
    ];
    playersToAssign = rotatedPlayers;
    console.log(`🔄 Applied rotation offset ${rotationOffset}: [${playersToAssign.join(', ')}]`);
  }

  // Assign players to field positions with proper restriction checking
  const unassignedPlayers = [...playersToAssign];

  for (const positionKey of nonPitcherFieldPositions) {
    const positionName = getPositionDisplayName(positionKey);
    let assigned = false;

    // First, try to find an eligible player who can play this position
    for (let i = 0; i < unassignedPlayers.length; i++) {
      const player = unassignedPlayers[i];

      if (canPlayerPlayPosition(player, positionName)) {
        // Assign this player to the position
        (newPositions as any)[positionKey] = player;
        assignedPlayers.add(player);
        unassignedPlayers.splice(i, 1); // Remove from unassigned list
        assigned = true;

        const wasOnBench = currentBench.includes(player);
        const previousPosition = wasOnBench ? 'BENCH' :
          Object.entries(currentPositions).find(([, v]) => v === player)?.[0] || 'UNKNOWN';

        console.log(`✅ ASSIGNED: ${player} (${previousPosition}) -> ${positionName}`);
        break;
      }
    }

    // If no eligible player found, this indicates a constraint satisfaction problem
    if (!assigned) {
      console.error(`❌ CONSTRAINT VIOLATION: No eligible players available for ${positionName}`);
      console.log(`   Available players: [${unassignedPlayers.join(', ')}]`);
      console.log(`   Checking restrictions for each player:`);

      unassignedPlayers.forEach(player => {
        const canPlay = canPlayerPlayPosition(player, positionName);
        console.log(`     ${player}: ${canPlay ? 'CAN' : 'CANNOT'} play ${positionName}`);
      });

      // IMPROVED: Try to find a player from ALL available players (not just unassigned) who can play this position
      // This handles cases where the rotation order created an impossible constraint
      let foundEligiblePlayer = false;
      for (const playerName of allPlayerNames) {
        if (!assignedPlayers.has(playerName) && canPlayerPlayPosition(playerName, positionName)) {
          // Found an eligible player, assign them
          (newPositions as any)[positionKey] = playerName;
          assignedPlayers.add(playerName);

          // Remove from unassigned list if present
          const playerIndex = unassignedPlayers.indexOf(playerName);
          if (playerIndex !== -1) {
            unassignedPlayers.splice(playerIndex, 1);
          }

          foundEligiblePlayer = true;
          console.log(`✅ CONSTRAINT RESOLVED: Found eligible player ${playerName} -> ${positionName}`);
          break;
        }
      }

      // Only if absolutely no one can play this position (which should be very rare)
      if (!foundEligiblePlayer) {
        if (rules.respectPositionLockouts) {
          console.error(`❌ CRITICAL ERROR: No players can play ${positionName} due to position restrictions`);
          console.error(`❌ This indicates a lineup generation failure - not enough eligible players for all positions`);
          // Don't assign anyone - leave position empty to indicate the problem
        } else {
          // If not respecting restrictions, assign anyone available
          if (unassignedPlayers.length > 0) {
            const fallbackPlayer = unassignedPlayers[0];
            (newPositions as any)[positionKey] = fallbackPlayer;
            assignedPlayers.add(fallbackPlayer);
            unassignedPlayers.splice(0, 1);
            console.log(`⚠️ FALLBACK ASSIGNMENT: ${fallbackPlayer} -> ${positionName} (restrictions disabled)`);
          }
        }
      }
    }
  }

  // STEP 4: Fallback - ensure ALL positions are filled
  for (const positionKey of nonPitcherFieldPositions) {
    if (!newPositions[positionKey as keyof typeof newPositions]) {
      const unassignedPlayers = allPlayerNames.filter(player => !assignedPlayers.has(player));
      if (unassignedPlayers.length > 0) {
        const fallbackPlayer = unassignedPlayers[0];
        (newPositions as any)[positionKey] = fallbackPlayer;
        assignedPlayers.add(fallbackPlayer);
        console.log(`⚠️ FALLBACK: ${fallbackPlayer} -> ${getPositionDisplayName(positionKey)} (ensuring position is filled)`);
      }
    }
  }

  // STEP 5: Put remaining players on bench
  const remainingPlayers = allPlayerNames.filter(player => !assignedPlayers.has(player));
  newPositions.bench = remainingPlayers;

  console.log(`🪑 NEW BENCH: [${remainingPlayers.join(', ')}]`);
  console.log(`✅ ROTATION COMPLETE!`);

  return {
    inning: previousInning.inning + 1,
    positions: newPositions
  };
}

// Clean up - removed complex AI functions that weren't working properly









// Helper function to get position display name from position key (removed duplicate - using the one above)

// Function to save player position history to the database
export async function savePlayerPositionHistory(
  lineup: Lineup,
  inning: InningLineup,
  playerMap: Map<string, Player>
): Promise<void> {
  try {
    // Get current user ID
    const { data: userData } = await supabase.auth.getUser();
    const userId = userData.user?.id;

    if (!userId) {
      console.error("User not authenticated, cannot save position history");
      return;
    }

    // Create records for each player's position
    const positionRecords = [];

    // Process field positions
    const fieldPositions = [
      "pitcher", "catcher", "firstBase", "secondBase", "shortstop",
      "thirdBase", "leftField", "centerField", "rightField"
    ];

    for (const positionKey of fieldPositions) {
      const playerName = inning.positions[positionKey];
      if (playerName) {
        const player = playerMap.get(playerName);
        if (player) {
          positionRecords.push({
            player_id: player.id,
            lineup_id: lineup.id,
            inning_number: inning.inning,
            position: positionKey,
            game_date: lineup.gameDate,
            user_id: userId
          });
        }
      }
    }

    // Process bench positions
    inning.positions.bench.forEach((playerName, index) => {
      if (playerName) {
        const player = playerMap.get(playerName);
        if (player) {
          positionRecords.push({
            player_id: player.id,
            lineup_id: lineup.id,
            inning_number: inning.inning,
            position: `bench${index + 1}`,
            game_date: lineup.gameDate,
            user_id: userId
          });
        }
      }
    });

    // Save to database if we have records
    if (positionRecords.length > 0) {
      console.log(`💾 Attempting to save ${positionRecords.length} position history records for lineup ${lineup.id}, inning ${inning.inning}`);

      try {
        // Use upsert with proper conflict resolution
        const { error: upsertError } = await supabase
          .from('player_position_history')
          .upsert(positionRecords, {
            onConflict: 'lineup_id,player_id,inning_number',
            ignoreDuplicates: false
          });

        if (upsertError) {
          console.error("Error upserting player position history:", upsertError);
          console.log("Attempting individual record processing...");

          // Try processing records individually to identify problematic ones
          let successCount = 0;
          for (const record of positionRecords) {
            try {
              const { error: singleError } = await supabase
                .from('player_position_history')
                .upsert([record], {
                  onConflict: 'lineup_id,player_id,inning_number',
                  ignoreDuplicates: false
                });

              if (!singleError) {
                successCount++;
              } else {
                console.warn(`Failed to save record for player ${record.player_id}, position ${record.position}:`, singleError);
              }
            } catch (singleRecordError) {
              console.warn(`Exception saving individual record:`, singleRecordError);
            }
          }

          if (successCount > 0) {
            console.log(`✅ Saved ${successCount}/${positionRecords.length} player position history records for lineup ${lineup.id}`);
          }
        } else {
          console.log(`✅ Upserted ${positionRecords.length} player position history records for lineup ${lineup.id}`);
        }
      } catch (error) {
        console.error("Exception in savePlayerPositionHistory:", error);
        // Don't throw - this is not critical for lineup functionality
      }
    }
  } catch (error) {
    console.error("Error in savePlayerPositionHistory:", error);
  }
}

// Function to get player position history from the database
export async function getPlayerPositionHistory(
  playerId: string,
  startDate?: string,
  endDate?: string
): Promise<{
  totalInnings: number;
  fieldInnings: number;
  benchInnings: number;
  positionCounts: Record<string, number>;
}> {
  try {
    // Build query
    let query = supabase
      .from('player_position_history')
      .select('*')
      .eq('player_id', playerId);

    // Add date filters if provided
    if (startDate) {
      query = query.gte('game_date', startDate);
    }
    if (endDate) {
      query = query.lte('game_date', endDate);
    }

    // Execute query
    const { data, error } = await query;

    if (error) {
      if (error.code === '42P01') { // PostgreSQL "undefined_table"
        console.warn(`Player position history: Table 'player_position_history' does not exist. Returning default history for player ${playerId}.`);
      } else {
        console.error(`Error fetching player position history for player ${playerId}:`, error);
      }
      // For any query error, return the default structure
      return {
        totalInnings: 0,
        fieldInnings: 0,
        benchInnings: 0,
        positionCounts: {}
      };
    }

    // Process the data
    const positionCounts: Record<string, number> = {};
    let benchInnings = 0;
    let fieldInnings = 0;

    data.forEach(record => {
      // Count position occurrences
      const position = record.position;
      positionCounts[position] = (positionCounts[position] || 0) + 1;

      // Count bench vs. field innings
      if (position.startsWith('bench')) {
        benchInnings++;
      } else {
        fieldInnings++;
      }
    });

    return {
      totalInnings: data.length,
      fieldInnings,
      benchInnings,
      positionCounts
    };
  } catch (error) {
    console.error("Error in getPlayerPositionHistory:", error);
    return {
      totalInnings: 0,
      fieldInnings: 0,
      benchInnings: 0,
      positionCounts: {}
    };
  } 
}

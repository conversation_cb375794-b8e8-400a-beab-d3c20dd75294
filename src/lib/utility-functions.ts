import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { v4 as uuidv4 } from "uuid";
import { Player, InningLineup } from "@/contexts/TeamContext";

// Types needed for validation
export interface LineupRules {
  limitBenchTime: boolean;
  maxConsecutiveBenchInnings?: number;
  minFieldInningsPerPlayer?: number;
  minInningsPerPosition?: number;
  preferredPositions?: Map<string, string[]>;
  allowPitcherRotation: boolean;
  allowCatcherRotation: boolean;
  respectPositionLockouts: boolean;
  equalPlayingTime?: boolean;
  rotateLineupEvery?: number;
  rotatePitcherEvery?: number;
  _randomSeed?: number;
  competitiveMode?: boolean;
  competitiveMinPlayingTime?: number;
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings?: string[];
}

// Position configuration
export const POSITION_CONFIG = {
  pitcher: { key: 'pitcher', display: 'Pitcher', abbrev: 'P' },
  catcher: { key: 'catcher', display: 'Catcher', abbrev: 'C' },
  firstBase: { key: 'firstBase', display: 'First Base', abbrev: '1B' },
  secondBase: { key: 'secondBase', display: 'Second Base', abbrev: '2B' },
  shortstop: { key: 'shortstop', display: 'Shortstop', abbrev: 'SS' },
  thirdBase: { key: 'thirdBase', display: 'Third Base', abbrev: '3B' },
  leftField: { key: 'leftField', display: 'Left Field', abbrev: 'LF' },
  centerField: { key: 'centerField', display: 'Center Field', abbrev: 'CF' },
  rightField: { key: 'rightField', display: 'Right Field', abbrev: 'RF' },
  middleInfield: { key: 'middleInfield', display: 'Middle Infield', abbrev: 'MI' },
  utility: { key: 'utility', display: '3B/MI/SS/2B', abbrev: 'UTL' }
} as const;

// Utility functions extracted to avoid SWC compiler issues
export const cn = (...inputs: ClassValue[]) => {
  return twMerge(clsx(inputs));
};

export const generateId = (): string => {
  return uuidv4();
};

export const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'numeric',
    day: 'numeric',
    year: 'numeric',
  });
};

// Position utility functions
export const getPositionDisplayName = (positionId: string): string => {
  const config = Object.values(POSITION_CONFIG).find(p => p.key === positionId);
  return config?.display || positionId;
};

export const getPositionLabel = (positionId: string): string => {
  if (positionId.startsWith("bench")) {
    const benchNumber = positionId.replace("bench", "");
    return `Bench ${benchNumber}`;
  }
  return getPositionDisplayName(positionId);
};

// Player position functions
export const canPlayPosition = (player: Player, position: string): boolean => {
  if (!player || !player.positionRestrictions) {
    return true;
  }

  // Check position restrictions
  if (position === "Pitcher" && player.positionRestrictions.pitcher) return false;
  if (position === "Catcher" && player.positionRestrictions.catcher) return false;
  if (position === "First Base" && player.positionRestrictions.firstBase) return false;

  // Check "other" restriction
  if (player.positionRestrictions.other) {
    if (player.positionRestrictions.other === position) return false;

    // Handle combined position restrictions
    if (player.positionRestrictions.other === "Middle Infield" &&
        (position === "Shortstop" || position === "Second Base")) {
      return false;
    }

    if (player.positionRestrictions.other === "3B/MI/SS/2B" &&
        (position === "Third Base" || position === "Shortstop" ||
         position === "Second Base" || position === "Middle Infield")) {
      return false;
    }
  }

  return true;
};

export const checkPositionRestriction = (playerObj: Player, positionId: string): string | null => {
  if (!playerObj || !playerObj.positionRestrictions) {
    return null;
  }

  const positionName = getPositionDisplayName(positionId);

  if (positionId === "pitcher" && playerObj.positionRestrictions.pitcher) {
    return "Pitcher";
  }

  if (positionId === "catcher" && playerObj.positionRestrictions.catcher) {
    return "Catcher";
  }

  if (positionId === "firstBase" && playerObj.positionRestrictions.firstBase) {
    return "First Base";
  }

  if (playerObj.positionRestrictions.other) {
    if (playerObj.positionRestrictions.other === positionName) {
      return positionName;
    }

    if (playerObj.positionRestrictions.other === "Middle Infield" &&
        (positionId === "shortstop" || positionId === "secondBase")) {
      return "Middle Infield";
    }

    if (playerObj.positionRestrictions.other === "3B/MI/SS/2B" &&
        (positionId === "thirdBase" || positionId === "shortstop" ||
         positionId === "secondBase")) {
      return "3B/MI/SS/2B";
    }
  }

  return null;
};

// Validation functions
export const validateLineup = (
  lineup: InningLineup,
  players: Player[],
  rules: LineupRules
): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  const playerMap = new Map(players.map(p => [p.name, p]));
  const assignedPlayers = new Set<string>();
  
  // Check all field positions are filled
  const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase',
                          'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
  
  // Track field player assignments for duplicate checking
  const fieldPlayerNames: string[] = [];

  for (const pos of fieldPositions) {
    const playerName = (lineup.positions as any)[pos];
    if (!playerName) {
      errors.push(`Position ${pos} is not filled`);
    } else {
      fieldPlayerNames.push(playerName);
      assignedPlayers.add(playerName);

      // Check position restrictions
      const player = playerMap.get(playerName);
      if (player && rules.respectPositionLockouts) {
        const positionDisplay = getPositionDisplayName(pos);
        if (!canPlayPosition(player, positionDisplay)) {
          errors.push(`Player ${playerName} cannot play ${positionDisplay} due to restrictions`);
        }
      }
    }
  }

  // Check for duplicate assignments only if we have enough players
  // With fewer players than positions (e.g., 8 players, 9 positions), duplicates are expected
  if (players.length >= fieldPositions.length) {
    const duplicateCheck = new Set<string>();
    for (const playerName of fieldPlayerNames) {
      if (duplicateCheck.has(playerName)) {
        errors.push(`Player ${playerName} is assigned to multiple positions`);
      }
      duplicateCheck.add(playerName);
    }
  } else {
    console.log(`ℹ️ Allowing duplicate assignments: ${players.length} players for ${fieldPositions.length} positions`);
  }
  
  // Check bench players
  for (const benchPlayer of lineup.positions.bench) {
    if (assignedPlayers.has(benchPlayer)) {
      errors.push(`Player ${benchPlayer} is both on bench and in field`);
    }
    assignedPlayers.add(benchPlayer);
  }
  
  // Check total player count (allow fewer unique assigned players when we have fewer players than positions)
  const expectedAssignedCount = Math.min(players.length, fieldPositions.length + lineup.positions.bench.length);
  if (assignedPlayers.size !== expectedAssignedCount && players.length >= fieldPositions.length) {
    errors.push(`Player count mismatch: ${assignedPlayers.size} assigned, ${expectedAssignedCount} expected`);
  }
  
  // Check for unknown players
  for (const playerName of assignedPlayers) {
    if (!playerMap.has(playerName)) {
      errors.push(`Unknown player: ${playerName}`);
    }
  }
  
  return { valid: errors.length === 0, errors, warnings };
};

// Helper function for bench streaks
export const getBenchStreaksStatic = (allInnings: InningLineup[], upToInning: number): Map<string, number> => {
  const streaks = new Map<string, number>();

  if (allInnings.length === 0 || upToInning <= 0) {
    return streaks;
  }

  // Initialize all players to 0
  const firstInning = allInnings[0];
  if (firstInning) {
    // Get all players from first inning
    const allPlayers = new Set<string>();
    Object.values(firstInning.positions).forEach(player => {
      if (typeof player === 'string' && player) {
        allPlayers.add(player);
      } else if (Array.isArray(player)) {
        player.forEach(p => allPlayers.add(p));
      }
    });
    allPlayers.forEach(player => streaks.set(player, 0));
  }

  // Count consecutive bench innings going backwards from upToInning
  for (let i = Math.min(upToInning - 1, allInnings.length - 1); i >= 0; i--) {
    const inning = allInnings[i];
    if (!inning) break;

    // First, reset streak for players who played in the field
    Object.entries(inning.positions).forEach(([position, player]) => {
      if (position !== 'bench' && typeof player === 'string' && player) {
        streaks.set(player, 0);
      }
    });

    // Then increment streak for bench players
    inning.positions.bench.forEach(player => {
      if (player) {
        const currentStreak = streaks.get(player) || 0;
        streaks.set(player, currentStreak + 1);
      }
    });
  }

  return streaks;
};

// Helper function for position keys
export const getPositionKeyStatic = (position: string): string => {
  // Map display position names to preference keys
  const positionKeyMap: { [key: string]: string } = {
    'pitcher': 'pitcher',
    'catcher': 'catcher',
    'firstBase': 'firstBase',
    'secondBase': 'secondBase',
    'thirdBase': 'thirdBase',
    'shortstop': 'shortstop',
    'leftField': 'leftField',
    'centerField': 'centerField',
    'rightField': 'rightField',
    'leftCenter': 'leftCenter',
    'rightCenter': 'rightCenter',
    'middleInfield': 'middleInfield'
  };

  return positionKeyMap[position] || position;
};

// Rotation validation
export const validateRotation = (
  previousInnings: InningLineup[],
  currentInning: InningLineup,
  rules: LineupRules,
  players: Player[]
): { valid: boolean; violations: string[] } => {
  const violations: string[] = [];
  const inningNumber = currentInning.inning;

  console.log(`🔍 VALIDATING ROTATION for inning ${inningNumber}`);

  // Check pitcher rotation
  if (rules.rotatePitcherEvery && rules.allowPitcherRotation && previousInnings.length > 0) {
    const shouldHaveRotated = inningNumber > 1 &&
      (inningNumber % rules.rotatePitcherEvery === 1 && inningNumber > 1);
    const previousPitcher = previousInnings[previousInnings.length - 1]?.positions.pitcher;
    const currentPitcher = currentInning.positions.pitcher;
    const didRotate = currentPitcher !== previousPitcher;

    console.log(`⚾ Pitcher rotation check:`, {
      inningNumber,
      rotatePitcherEvery: rules.rotatePitcherEvery,
      shouldHaveRotated,
      previousPitcher,
      currentPitcher,
      didRotate
    });

    if (shouldHaveRotated && !didRotate) {
      violations.push(`Pitcher should have rotated at inning ${inningNumber} (every ${rules.rotatePitcherEvery} innings)`);
    }
  }

  // Check bench streaks using the getBenchStreaks function
  const allInnings = [...previousInnings, currentInning];
  const benchStreaks = getBenchStreaksStatic(allInnings, inningNumber);
  const maxAllowedStreak = rules.maxConsecutiveBenchInnings || 2;

  console.log(`🪑 Bench streak validation:`, {
    maxAllowedStreak,
    currentBenchStreaks: Object.fromEntries(benchStreaks)
  });

  benchStreaks.forEach((streak, player) => {
    if (streak > maxAllowedStreak) {
      violations.push(`${player} on bench for ${streak} consecutive innings (max allowed: ${maxAllowedStreak})`);
    }
  });

  // Check rotation frequency
  if (rules.rotateLineupEvery && previousInnings.length > 0) {
    const shouldHaveRotated = (inningNumber - 1) % rules.rotateLineupEvery === 0;

    if (shouldHaveRotated) {
      // Check if any rotation actually occurred
      const previousInning = previousInnings[previousInnings.length - 1];
      const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase',
                             'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];

      let rotationOccurred = false;
      for (const pos of fieldPositions) {
        if ((previousInning.positions as any)[pos] !== (currentInning.positions as any)[pos]) {
          rotationOccurred = true;
          break;
        }
      }

      // Also check bench changes
      const previousBench = new Set(previousInning.positions.bench);
      const currentBench = new Set(currentInning.positions.bench);
      if (previousBench.size !== currentBench.size ||
          [...previousBench].some(player => !currentBench.has(player))) {
        rotationOccurred = true;
      }

      console.log(`🔄 Rotation frequency check:`, {
        inningNumber,
        rotateLineupEvery: rules.rotateLineupEvery,
        shouldHaveRotated,
        rotationOccurred
      });

      if (!rotationOccurred) {
        violations.push(`No rotation occurred at inning ${inningNumber} (should rotate every ${rules.rotateLineupEvery} innings)`);
      }
    }
  }

  // Check fair playing time distribution
  if (rules.equalPlayingTime && allInnings.length >= 3) {
    const playingTime = new Map<string, number>();
    players.forEach(player => playingTime.set(player.name, 0));

    // Calculate playing time for each player
    allInnings.forEach(inning => {
      const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase',
                             'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
      fieldPositions.forEach(pos => {
        const player = (inning.positions as any)[pos];
        if (player) {
          playingTime.set(player, (playingTime.get(player) || 0) + 1);
        }
      });
    });

    const playingTimes = Array.from(playingTime.values());
    const minTime = Math.min(...playingTimes);
    const maxTime = Math.max(...playingTimes);
    const avgTime = playingTimes.reduce((a, b) => a + b, 0) / playingTimes.length;
    const range = maxTime - minTime;

    // Allow some variance but flag excessive imbalance
    const maxAllowedRange = Math.max(2, Math.ceil(allInnings.length * 0.4));

    console.log(`⚖️ Playing time fairness check:`, {
      totalInnings: allInnings.length,
      minTime,
      maxTime,
      avgTime: avgTime.toFixed(1),
      range,
      maxAllowedRange,
      playingTimeDistribution: Object.fromEntries(playingTime)
    });

    if (range > maxAllowedRange) {
      const underplayedPlayers = players
        .filter(p => (playingTime.get(p.name) || 0) === minTime)
        .map(p => p.name);
      const overplayedPlayers = players
        .filter(p => (playingTime.get(p.name) || 0) === maxTime)
        .map(p => p.name);

      violations.push(`Playing time imbalance detected: range ${range} exceeds max allowed ${maxAllowedRange}. Underplayed: [${underplayedPlayers.join(', ')}], Overplayed: [${overplayedPlayers.join(', ')}]`);
    }
  }

  // Check preference compliance (informational, not a violation)
  console.log('\n🎯 Position Preference Compliance:');
  const preferenceCompliance = new Map<string, { honored: number; total: number }>();

  // Analyze preference compliance for current inning
  const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase',
                         'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];

  fieldPositions.forEach(pos => {
    const player = (currentInning.positions as any)[pos];
    if (player) {
      const playerObj = players.find(p => p.name === player);
      if (playerObj?.positionPreferences) {
        const posKey = getPositionKeyStatic(pos);
        const preference = playerObj.positionPreferences[posKey];

        if (preference && preference !== 'neutral') {
          const compliance = preferenceCompliance.get(player) || { honored: 0, total: 0 };
          compliance.total++;

          if (preference === 'preferred' || preference === 'secondary') {
            compliance.honored++;
            console.log(`  ✅ ${player} at ${pos}: ${preference} position`);
          } else if (preference === 'avoid') {
            console.log(`  ⚠️ ${player} at ${pos}: assigned to avoided position (likely due to constraints)`);
          }

          preferenceCompliance.set(player, compliance);
        }
      }
    }
  });

  const isValid = violations.length === 0;
  console.log(`🎯 Rotation validation result:`, {
    valid: isValid,
    violationCount: violations.length,
    violations: violations,
    preferenceCompliance: Object.fromEntries(preferenceCompliance)
  });

  return { valid: isValid, violations };
};
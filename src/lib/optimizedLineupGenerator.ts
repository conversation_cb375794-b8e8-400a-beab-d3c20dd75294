import { Player, InningLineup, Lineup, RotationRules } from "@/contexts/TeamContext";
import { measurePerformance } from "@/utils/performance";

// Cache for position eligibility calculations
class PositionEligibilityCache {
  private cache = new Map<string, Set<string>>();
  
  getEligiblePositions(player: Player): Set<string> {
    const cacheKey = player.id;
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }
    
    const positions = new Set<string>();
    const allPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'thirdBase', 
                         'shortstop', 'leftField', 'centerField', 'rightField'];
    
    // Fast eligibility check using team roles
    for (const pos of allPositions) {
      const role = player.teamRoles?.[pos];
      if (role && role !== 'avoid') {
        positions.add(pos);
      }
    }
    
    // If no team roles, assume can play anywhere except avoid positions
    if (positions.size === 0) {
      allPositions.forEach(pos => positions.add(pos));
    }
    
    this.cache.set(cacheKey, positions);
    return positions;
  }
  
  clear() {
    this.cache.clear();
  }
}

// Player statistics tracker
interface PlayerStats {
  fieldInnings: number;
  benchInnings: number;
  consecutiveBench: number;
  positionsPlayed: Map<string, number>;
}

export class OptimizedLineupGenerator {
  private eligibilityCache = new PositionEligibilityCache();
  private playerStatsCache = new Map<string, PlayerStats>();
  private positionPriorityCache = new Map<string, string[]>();
  
  constructor(
    private players: Player[],
    private rules: RotationRules,
    private attendance: { [playerId: string]: boolean }
  ) {
    this.initializePlayerStats();
    this.precomputePositionPriorities();
  }
  
  private initializePlayerStats() {
    const perf = measurePerformance('initializePlayerStats');
    
    this.players.forEach(player => {
      if (this.attendance[player.id]) {
        this.playerStatsCache.set(player.name, {
          fieldInnings: 0,
          benchInnings: 0,
          consecutiveBench: 0,
          positionsPlayed: new Map()
        });
      }
    });
    
    perf.end();
  }
  
  private precomputePositionPriorities() {
    const perf = measurePerformance('precomputePositionPriorities');
    
    const positions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'thirdBase', 
                      'shortstop', 'leftField', 'centerField', 'rightField'];
    
    // For each position, create a sorted list of players by preference
    positions.forEach(position => {
      const eligiblePlayers = this.players
        .filter(p => this.attendance[p.id])
        .filter(p => {
          const eligible = this.eligibilityCache.getEligiblePositions(p);
          return eligible.has(position);
        })
        .sort((a, b) => {
          // Sort by role preference
          const roleA = a.teamRoles?.[position] || 'unset';
          const roleB = b.teamRoles?.[position] || 'unset';
          
          const roleScore = {
            'go-to': 4,
            'capable': 3,
            'fill-in': 2,
            'unset': 1,
            'avoid': 0
          };
          
          return roleScore[roleB] - roleScore[roleA];
        });
      
      this.positionPriorityCache.set(position, eligiblePlayers.map(p => p.name));
    });
    
    perf.end();
  }
  
  generateLineup(numInnings: number = 6): Lineup {
    const perf = measurePerformance('generateOptimizedLineup');
    
    const innings: InningLineup[] = [];
    const availablePlayers = this.players
      .filter(p => this.attendance[p.id])
      .map(p => p.name);
    
    // Generate first inning
    const firstInning = this.generateFirstInning(availablePlayers);
    innings.push(firstInning);
    this.updatePlayerStats(firstInning);
    
    // Generate subsequent innings
    for (let i = 2; i <= numInnings; i++) {
      const previousInning = innings[innings.length - 1];
      const nextInning = this.generateNextInning(previousInning, i);
      innings.push(nextInning);
      this.updatePlayerStats(nextInning);
    }
    
    perf.end();
    
    return {
      id: crypto.randomUUID(),
      name: "Optimized Lineup",
      gameDate: new Date().toISOString(),
      createdDate: new Date().toISOString(),
      attendance: this.attendance,
      innings,
      battingOrder: availablePlayers.slice(0, 9)
    };
  }
  
  private generateFirstInning(availablePlayers: string[]): InningLineup {
    const perf = measurePerformance('generateFirstInning');
    
    const positions = {
      pitcher: '',
      catcher: '',
      firstBase: '',
      secondBase: '',
      thirdBase: '',
      shortstop: '',
      leftField: '',
      centerField: '',
      rightField: '',
      bench: [] as string[]
    };
    
    const assigned = new Set<string>();
    
    // Use precomputed priorities for fast assignment
    const positionOrder = ['pitcher', 'catcher', 'shortstop', 'secondBase', 
                          'thirdBase', 'firstBase', 'centerField', 'leftField', 'rightField'];
    
    for (const position of positionOrder) {
      const candidates = this.positionPriorityCache.get(position) || [];
      
      for (const playerName of candidates) {
        if (!assigned.has(playerName)) {
          positions[position] = playerName;
          assigned.add(playerName);
          break;
        }
      }
    }
    
    // Assign remaining players to bench
    positions.bench = availablePlayers.filter(p => !assigned.has(p));
    
    perf.end();
    
    return { inning: 1, positions };
  }
  
  private generateNextInning(previous: InningLineup, inningNum: number): InningLineup {
    const perf = measurePerformance(`generateInning${inningNum}`);
    
    // Clone previous positions
    const newPositions = JSON.parse(JSON.stringify(previous.positions));
    
    // Check if rotation is needed
    const shouldRotate = this.rules.rotatePlayers && 
                        ((inningNum - 1) % this.rules.rotateLineupEvery === 0);
    
    if (shouldRotate) {
      this.performRotation(newPositions, inningNum);
    }
    
    // Check pitcher rotation
    if (this.shouldRotatePitcher(inningNum)) {
      this.rotatePitcher(newPositions);
    }
    
    perf.end();
    
    return { inning: inningNum, positions: newPositions };
  }
  
  private performRotation(positions: any, inningNum: number) {
    // Fast rotation using precomputed stats
    const benchPlayers = [...positions.bench];
    
    // Find players who need to rotate in (consecutive bench limit)
    const mustPlay = benchPlayers.filter(playerName => {
      const stats = this.playerStatsCache.get(playerName);
      return stats && stats.consecutiveBench >= 2;
    });
    
    if (mustPlay.length === 0 && benchPlayers.length === 0) return;
    
    // Simple rotation: swap one outfielder with bench
    const outfieldPositions = ['leftField', 'centerField', 'rightField'];
    const targetPosition = outfieldPositions[inningNum % 3];
    
    const playerToRotate = mustPlay[0] || benchPlayers[0];
    if (playerToRotate && positions[targetPosition]) {
      const temp = positions[targetPosition];
      positions[targetPosition] = playerToRotate;
      positions.bench = positions.bench.filter((p: string) => p !== playerToRotate);
      positions.bench.push(temp);
    }
  }
  
  private shouldRotatePitcher(inning: number): boolean {
    return this.rules.allowPitcherRotation &&
           this.rules.rotatePitcherEvery > 0 &&
           ((inning - 1) % this.rules.rotatePitcherEvery === 0);
  }
  
  private rotatePitcher(positions: any) {
    const eligiblePitchers = positions.bench.filter((playerName: string) => {
      const player = this.players.find(p => p.name === playerName);
      return player && this.eligibilityCache.getEligiblePositions(player).has('pitcher');
    });
    
    if (eligiblePitchers.length > 0) {
      const newPitcher = eligiblePitchers[0];
      const oldPitcher = positions.pitcher;
      
      positions.pitcher = newPitcher;
      positions.bench = positions.bench.filter((p: string) => p !== newPitcher);
      positions.bench.push(oldPitcher);
    }
  }
  
  private updatePlayerStats(inning: InningLineup) {
    // Update field/bench stats for all players
    const fieldPlayers = new Set<string>();
    
    Object.entries(inning.positions).forEach(([pos, player]) => {
      if (pos !== 'bench' && player) {
        fieldPlayers.add(player as string);
        const stats = this.playerStatsCache.get(player as string);
        if (stats) {
          stats.fieldInnings++;
          stats.consecutiveBench = 0;
          stats.positionsPlayed.set(pos, (stats.positionsPlayed.get(pos) || 0) + 1);
        }
      }
    });
    
    // Update bench players
    inning.positions.bench.forEach(playerName => {
      const stats = this.playerStatsCache.get(playerName);
      if (stats) {
        stats.benchInnings++;
        stats.consecutiveBench++;
      }
    });
  }
}

// Export a simple wrapper function
export function generateOptimizedLineup(
  players: Player[],
  attendance: { [playerId: string]: boolean },
  rules: RotationRules,
  numInnings: number = 6
): Lineup {
  const generator = new OptimizedLineupGenerator(players, rules, attendance);
  return generator.generateLineup(numInnings);
}
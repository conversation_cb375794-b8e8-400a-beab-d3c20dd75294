/**
 * Strict Position Eligibility System
 * 
 * This module ensures players are ONLY assigned to positions they can play
 * based on their position_preferences. If a position is NOT in position_preferences,
 * the player CANNOT play there.
 */

import { Player } from "@/contexts/TeamContext";

// Position mapping for consistency
export const POSITION_KEY_MAP = {
  // Keys to keys (for when already using keys)
  'pitcher': 'pitcher',
  'catcher': 'catcher',
  'firstBase': 'firstBase',
  'secondBase': 'secondBase',
  'thirdBase': 'thirdBase',
  'shortstop': 'shortstop',
  'leftField': 'leftField',
  'centerField': 'centerField',
  'rightField': 'rightField',
  'leftCenter': 'leftCenter',
  'rightCenter': 'rightCenter',
  // Display names to keys
  'Pitcher': 'pitcher',
  'Catcher': 'catcher',
  'First Base': 'firstBase',
  'Second Base': 'secondBase',
  'Third Base': 'thirdBase',
  'Shortstop': 'shortstop',
  'Left Field': 'leftField',
  'Center Field': 'centerField',
  'Right Field': 'rightField',
  'Left Center': 'leftCenter',
  'Right Center': 'rightCenter'
} as const;

export type PositionKey = keyof typeof POSITION_KEY_MAP;

interface PlayerWithPreference {
  player: Player;
  preference: string;
  preferenceLevel: number; // For sorting
}

/**
 * Strictly check if a player can play a position based on position_preferences
 * @param player The player to check
 * @param position The position (display name or key)
 * @returns true if player can play the position, false otherwise
 */
export function canPlayPositionStrict(player: Player, position: string): boolean {
  if (!player || !player.positionPreferences) {
    console.warn(`[canPlayPositionStrict] Player ${player?.name} has no position_preferences`);
    return false;
  }

  // Convert position to key format
  const positionKey = POSITION_KEY_MAP[position as PositionKey] || position;
  
  // Check if position exists in preferences
  const preference = player.positionPreferences[positionKey];
  
  // If no preference exists, player CANNOT play this position
  if (!preference) {
    console.log(`[canPlayPositionStrict] ${player.name} CANNOT play ${position} - not in position_preferences`);
    return false;
  }
  
  // Handle both string and object preference formats
  const preferenceLevel = typeof preference === 'string' ? preference : preference.level;
  
  // Player can play if preference is not "avoid"
  if (preferenceLevel === 'avoid') {
    console.log(`[canPlayPositionStrict] ${player.name} CANNOT play ${position} - marked as avoid`);
    return false;
  }
  
  // Check hard restrictions (medical/safety)
  if (positionKey === 'pitcher' && player.pitcher_restriction) {
    console.log(`[canPlayPositionStrict] ${player.name} CANNOT play pitcher - medical restriction`);
    return false;
  }
  if (positionKey === 'catcher' && player.catcher_restriction) {
    console.log(`[canPlayPositionStrict] ${player.name} CANNOT play catcher - medical restriction`);
    return false;
  }
  if (positionKey === 'firstBase' && player.first_base_restriction) {
    console.log(`[canPlayPositionStrict] ${player.name} CANNOT play firstBase - medical restriction`);
    return false;
  }
  
  console.log(`[canPlayPositionStrict] ${player.name} CAN play ${position} - preference: ${preferenceLevel}`);
  return true;
}

/**
 * Build a position eligibility matrix showing which players can play each position
 * @param players List of all players
 * @returns Matrix of positions with eligible players
 */
export function buildPositionMatrix(players: Player[]) {
  const matrix: Record<string, PlayerWithPreference[]> = {
    pitcher: [],
    catcher: [],
    firstBase: [],
    secondBase: [],
    thirdBase: [],
    shortstop: [],
    leftField: [],
    centerField: [],
    rightField: []
  };
  
  for (const player of players) {
    if (!player.positionPreferences) {
      console.warn(`[buildPositionMatrix] Player ${player.name} has no position_preferences`);
      continue;
    }
    
    for (const position of Object.keys(matrix)) {
      // CRITICAL: Only if position EXISTS in their preferences
      if (player.positionPreferences && player.positionPreferences[position]) {
        const preference = player.positionPreferences[position];
        const preferenceLevel = typeof preference === 'string' ? preference : preference.level;
        
        // Skip if they avoid it
        if (preferenceLevel === 'avoid') continue;
        
        // Check hard restrictions
        if (position === 'pitcher' && player.pitcher_restriction) continue;
        if (position === 'catcher' && player.catcher_restriction) continue;
        if (position === 'firstBase' && player.first_base_restriction) continue;
        
        // Calculate preference level for sorting
        let sortLevel = 0;
        switch (preferenceLevel) {
          case 'preferred': sortLevel = 3; break;
          case 'secondary': sortLevel = 2; break;
          case 'neutral': sortLevel = 1; break;
        }
        
        // Add player with their preference level
        matrix[position].push({
          player: player,
          preference: preferenceLevel,
          preferenceLevel: sortLevel
        });
      }
    }
  }
  
  // Sort each position's players by preference level (highest first)
  for (const position of Object.keys(matrix)) {
    matrix[position].sort((a, b) => b.preferenceLevel - a.preferenceLevel);
  }
  
  // Log matrix summary
  console.log('[buildPositionMatrix] Position eligibility summary:');
  for (const [pos, eligible] of Object.entries(matrix)) {
    console.log(`  ${pos}: ${eligible.length} eligible players - ${eligible.map(e => `${e.player.name}(${e.preference})`).join(', ')}`);
  }
  
  return matrix;
}

/**
 * Validate that all positions in a lineup have eligible players
 * @param lineup Object mapping positions to player names
 * @param players List of all players
 * @returns Array of error messages (empty if valid)
 */
export function validateLineupPositions(
  lineup: Record<string, string | string[]>, 
  players: Player[]
): string[] {
  const errors: string[] = [];
  const playerMap = new Map(players.map(p => [p.name, p]));
  
  for (const [position, assignment] of Object.entries(lineup)) {
    // Skip bench and array assignments
    if (position === 'bench' || Array.isArray(assignment)) continue;
    
    const playerName = assignment as string;
    const player = playerMap.get(playerName);
    
    if (!player) {
      errors.push(`Player ${playerName} not found in roster`);
      continue;
    }
    
    // Check 1: Position exists in their preferences
    const positionKey = POSITION_KEY_MAP[position as PositionKey] || position;
    if (!player.positionPreferences || !player.positionPreferences[positionKey]) {
      errors.push(`${playerName} assigned to ${position} but has NO ENTRY for this position in position_preferences!`);
      continue;
    }
    
    // Check 2: Not avoiding unless emergency
    const preference = player.positionPreferences[positionKey];
    const preferenceLevel = typeof preference === 'string' ? preference : preference.level;
    if (preferenceLevel === 'avoid') {
      errors.push(`${playerName} is avoiding ${position} but was assigned anyway (emergency only)`);
    }
    
    // Check 3: No hard restrictions violated
    if (position === 'pitcher' && player.pitcher_restriction) {
      errors.push(`${playerName} has pitcher restriction but assigned to pitcher!`);
    }
    if (position === 'catcher' && player.catcher_restriction) {
      errors.push(`${playerName} has catcher restriction but assigned to catcher!`);
    }
    if (position === 'firstBase' && player.first_base_restriction) {
      errors.push(`${playerName} has firstBase restriction but assigned to firstBase!`);
    }
  }
  
  return errors;
}

/**
 * Find the best available player for a position
 * @param position The position to fill
 * @param availablePlayers Players not yet assigned
 * @param competitiveMode Whether to prefer star players for key positions
 * @returns The best player for the position, or null if none available
 */
export function findBestPlayerForPosition(
  position: string,
  availablePlayers: Player[],
  competitiveMode: boolean = false
): Player | null {
  const eligiblePlayers: PlayerWithPreference[] = [];
  
  for (const player of availablePlayers) {
    if (canPlayPositionStrict(player, position)) {
      const positionKey = POSITION_KEY_MAP[position as PositionKey] || position;
      const preference = player.positionPreferences![positionKey];
      const preferenceLevel = typeof preference === 'string' ? preference : preference.level;
      
      let sortLevel = 0;
      switch (preferenceLevel) {
        case 'preferred': sortLevel = 3; break;
        case 'secondary': sortLevel = 2; break;
        case 'neutral': sortLevel = 1; break;
      }
      
      // Boost star players in competitive mode for key positions
      if (competitiveMode && player.isStarPlayer) {
        const keyPositions = ['pitcher', 'catcher', 'shortstop'];
        if (keyPositions.includes(positionKey)) {
          sortLevel += 10; // Heavy boost for star players at key positions
        }
      }
      
      eligiblePlayers.push({
        player,
        preference: preferenceLevel,
        preferenceLevel: sortLevel
      });
    }
  }
  
  if (eligiblePlayers.length === 0) {
    return null;
  }
  
  // Sort by preference level and return the best
  eligiblePlayers.sort((a, b) => b.preferenceLevel - a.preferenceLevel);
  return eligiblePlayers[0].player;
}
/**
 * Multi-Game Orchestrator
 * 
 * This module orchestrates lineup generation across multiple games,
 * ensuring cumulative fairness and proper cross-game tracking.
 * 
 * Key responsibilities:
 * - Track cumulative playing time across games
 * - Adjust player priorities based on previous games
 * - Ensure fairness across the entire series
 */

import { 
  Player, 
  InningLineup, 
  LineupRules,
  CrossGameTracking,
  canPlayPosition
} from './utils-enhanced';

import { generateSingleGameLineup } from './single-game-lineup';
import { generateSingleGameLineupStrict } from './single-game-lineup-strict';

export interface GameResult {
  lineups: InningLineup[];
  gameStats: Map<string, GamePlayerStats>;
}

export interface GamePlayerStats {
  fieldInnings: number;
  benchInnings: number;
  inningsPitched: number;
}

export interface SeriesResult {
  games: GameResult[];
  cumulativeStats: Map<string, CumulativePlayerStats>;
  balanceScore: number;
}

export interface CumulativePlayerStats {
  totalFieldInnings: number;
  totalBenchInnings: number;
  totalInningsPitched: number;
  gamesPlayed: number;
}

interface PitcherPlan {
  gameNumber: number;
  plannedPitchers: {
    player: Player;
    targetInnings: number[];
    role: 'starter' | 'closer' | 'middle' | 'any';
  }[];
}

/**
 * Plan pitcher usage across all games to ensure fair distribution
 */
function planPitcherRotation(
  players: Player[],
  numberOfGames: number,
  inningsPerGame: number,
  rotatePitcherEvery: number
): PitcherPlan[] {
  console.log('⚾ Planning smart pitcher rotation across series');
  
  // Identify all pitchers based on position_preferences
  const allPitchers = players.filter(p => {
    if (!p.positionPreferences?.pitcher) return false;
    const pref = p.positionPreferences.pitcher;
    const level = typeof pref === 'string' ? pref : pref.level;
    return level !== 'avoid'; // Include all who can pitch
  });
  
  // Categorize by pitcher strategy
  const starters = allPitchers.filter(p => p.pitcherStrategy?.role === 'starter');
  const closers = allPitchers.filter(p => p.pitcherStrategy?.role === 'closer');
  const relievers = allPitchers.filter(p => p.pitcherStrategy?.role === 'reliever');
  const anyRole = allPitchers.filter(p => !p.pitcherStrategy?.role || p.pitcherStrategy.role === 'any');
  
  console.log(`⚾ Pitcher roster: ${starters.length} starters, ${closers.length} closers, ${relievers.length} relievers, ${anyRole.length} any`);
  console.log(`⚾ Total pitchers found: ${allPitchers.length}`);
  
  // If no pitchers found, return empty plans
  if (allPitchers.length === 0) {
    console.warn('⚠️ No pitchers found in roster! Pitcher planning skipped.');
    return [];
  }
  
  const gamePlans: PitcherPlan[] = [];
  
  // Track pitcher usage to ensure everyone gets to play
  const pitcherUsage = new Map<string, number>();
  allPitchers.forEach(p => pitcherUsage.set(p.id, 0));
  
  for (let gameNum = 1; gameNum <= numberOfGames; gameNum++) {
    const pitchingSlots = Math.ceil(inningsPerGame / rotatePitcherEvery);
    const gamePitchers: PitcherPlan['plannedPitchers'] = [];
    
    // Calculate which innings each slot covers
    const getInningsForSlot = (slotIndex: number) => {
      const start = slotIndex * rotatePitcherEvery + 1;
      const end = Math.min(start + rotatePitcherEvery - 1, inningsPerGame);
      return Array.from({ length: end - start + 1 }, (_, i) => start + i);
    };
    
    // Slot 1: Prefer starters
    if (pitchingSlots >= 1) {
      const availableStarters = starters.length > 0 ? starters : anyRole.length > 0 ? anyRole : allPitchers;
      const starter = getLeastUsedPitcher(availableStarters, pitcherUsage, gameNum);
      if (starter) {
        gamePitchers.push({
          player: starter,
          targetInnings: getInningsForSlot(0),
          role: 'starter'
        });
        pitcherUsage.set(starter.id, pitcherUsage.get(starter.id)! + 1);
      }
    }
    
    // Last slot: Prefer closers (if multiple slots)
    if (pitchingSlots > 1 && closers.length > 0) {
      const closer = getLeastUsedPitcher(closers, pitcherUsage, gameNum);
      if (closer) {
        gamePitchers.push({
          player: closer,
          targetInnings: getInningsForSlot(pitchingSlots - 1),
          role: 'closer'
        });
        pitcherUsage.set(closer.id, pitcherUsage.get(closer.id)! + 1);
      }
    }
    
    // Middle slots: Use relievers or any available pitchers
    const middleSlots = pitchingSlots - gamePitchers.length;
    for (let i = 0; i < middleSlots; i++) {
      // Calculate the next unused slot index
      const usedSlots = new Set<number>();
      gamePitchers.forEach(gp => {
        // Determine which slot this pitcher is assigned to based on their innings
        const firstInning = gp.targetInnings[0] - 1;
        const slotIdx = Math.floor(firstInning / rotatePitcherEvery);
        usedSlots.add(slotIdx);
      });
      
      // Find the first unused slot
      let slotIndex = 0;
      while (usedSlots.has(slotIndex) && slotIndex < pitchingSlots) {
        slotIndex++;
      }
      
      const availablePitchers = [...relievers, ...anyRole, ...allPitchers].filter(p => 
        !gamePitchers.some(gp => gp.player.id === p.id)
      );
      
      const pitcher = getLeastUsedPitcher(availablePitchers, pitcherUsage, gameNum);
      if (pitcher) {
        gamePitchers.push({
          player: pitcher,
          targetInnings: getInningsForSlot(slotIndex),
          role: 'middle'
        });
        pitcherUsage.set(pitcher.id, pitcherUsage.get(pitcher.id)! + 1);
      }
    }
    
    // If we still need more pitchers to fill all slots, add any available pitchers
    while (gamePitchers.length < pitchingSlots && allPitchers.length > gamePitchers.length) {
      // Calculate the next unused slot index
      const usedSlots = new Set<number>();
      gamePitchers.forEach(gp => {
        const firstInning = gp.targetInnings[0] - 1;
        const slotIdx = Math.floor(firstInning / rotatePitcherEvery);
        usedSlots.add(slotIdx);
      });
      
      // Find the first unused slot
      let slotIndex = 0;
      while (usedSlots.has(slotIndex) && slotIndex < pitchingSlots) {
        slotIndex++;
      }
      
      const remainingPitchers = allPitchers.filter(p => 
        !gamePitchers.some(gp => gp.player.id === p.id)
      );
      
      const pitcher = getLeastUsedPitcher(remainingPitchers, pitcherUsage, gameNum);
      if (pitcher) {
        gamePitchers.push({
          player: pitcher,
          targetInnings: getInningsForSlot(slotIndex),
          role: 'any'
        });
        pitcherUsage.set(pitcher.id, pitcherUsage.get(pitcher.id)! + 1);
      } else {
        break;
      }
    }
    
    gamePlans.push({
      gameNumber: gameNum,
      plannedPitchers: gamePitchers
    });
    
    console.log(`⚾ Game ${gameNum} pitcher plan (${gamePitchers.length}/${pitchingSlots} slots):`, gamePitchers.map(p => 
      `${p.player.name} (${p.role}) innings ${p.targetInnings.join(',')}`
    ));
  }
  
  // Log usage summary
  console.log('⚾ Pitcher usage summary:');
  allPitchers.forEach(p => {
    console.log(`  ${p.name}: ${pitcherUsage.get(p.id)} games`);
  });
  
  return gamePlans;
}

/**
 * Get the least used pitcher from a group, with some randomization for variety
 */
function getLeastUsedPitcher(
  pitchers: Player[], 
  usage: Map<string, number>,
  gameNum: number
): Player | null {
  if (pitchers.length === 0) return null;
  
  // Sort by usage
  const sorted = [...pitchers].sort((a, b) => {
    const aUsage = usage.get(a.id) || 0;
    const bUsage = usage.get(b.id) || 0;
    return aUsage - bUsage;
  });
  
  // Get all pitchers with minimum usage
  const minUsage = usage.get(sorted[0].id) || 0;
  const leastUsed = sorted.filter(p => (usage.get(p.id) || 0) === minUsage);
  
  // Rotate through them based on game number for variety
  return leastUsed[(gameNum - 1) % leastUsed.length];
}

/**
 * Generate lineups for multiple games with cross-game fairness
 */
export async function generateMultiGameSeries(
  players: Player[],
  numberOfGames: number,
  inningsPerGame: number,
  rules: LineupRules
): Promise<SeriesResult> {
  console.log(`🎮 GENERATING ${numberOfGames} GAME SERIES - ${inningsPerGame} innings per game`);
  
  // Pre-check for potential position issues
  const positionCounts: Record<string, number> = {};
  players.forEach(player => {
    if (!player.positionPreferences) return;
    Object.entries(player.positionPreferences).forEach(([position, pref]) => {
      const level = typeof pref === 'string' ? pref : pref?.level;
      if (level && level !== 'avoid') {
        positionCounts[position] = (positionCounts[position] || 0) + 1;
      }
    });
  });
  
  // Warn about positions with limited players
  const criticalPositions = ['pitcher', 'catcher'];
  criticalPositions.forEach(pos => {
    const count = positionCounts[pos] || 0;
    if (count < 2) {
      console.warn(`⚠️ WARNING: Only ${count} player(s) can play ${pos}. This may cause issues with rotations in a ${numberOfGames}-game series.`);
    }
    // Special warning for catchers in multi-game series
    if (pos === 'catcher') {
      const totalCatcherSlots = numberOfGames * Math.ceil(inningsPerGame / (rules.rotatePitcherEvery || 3));
      if (count < totalCatcherSlots / 2) {
        console.warn(`⚾ CATCHER PLANNING: ${count} catchers for ~${totalCatcherSlots} total catcher slots across series`);
        console.warn(`⚾ Catchers will need to play multiple consecutive innings to cover all games`);
      }
    }
  });
  
  const games: GameResult[] = [];
  const cumulativeStats = new Map<string, CumulativePlayerStats>();
  
  // Initialize cumulative stats
  players.forEach(player => {
    cumulativeStats.set(player.id, {
      totalFieldInnings: 0,
      totalBenchInnings: 0,
      totalInningsPitched: 0,
      gamesPlayed: 0
    });
  });

  // Plan pitcher usage across all games
  const pitcherPlans = planPitcherRotation(
    players,
    numberOfGames,
    inningsPerGame,
    rules.rotatePitcherEvery || 2
  );

  // Generate each game
  for (let gameNum = 1; gameNum <= numberOfGames; gameNum++) {
    console.log(`\n🏟️ GENERATING GAME ${gameNum} of ${numberOfGames}`);
    
    // Get pitcher plan for this game
    const gamePitcherPlan = pitcherPlans.find(p => p.gameNumber === gameNum);
    
    // Adjust player priorities based on cumulative stats
    const adjustedPlayers = adjustPlayerPriorities(players, cumulativeStats, gameNum);
    
    // Boost priority for pitchers scheduled to pitch in future games
    // This ensures they get some field time when not pitching
    const futurePitchers = pitcherPlans
      .filter(p => p.gameNumber > gameNum)
      .flatMap(p => p.plannedPitchers.map(pp => pp.player.id));
    
    const playersWithPitcherBoost = adjustedPlayers.map(player => {
      if (futurePitchers.includes(player.id)) {
        // Give a small priority boost to ensure they play some field positions
        return {
          ...player,
          _priorityAdjustment: ((player as any)._priorityAdjustment || 0) + 0.3
        };
      }
      return player;
    });
    
    // Create game-specific rules with unique seed for each game
    const gameRules = {
      ...rules,
      // CRITICAL: Use different seed for each game to avoid identical innings
      // Convert to number seed by using game number and timestamp
      seed: Date.now() + gameNum * 1000 + Math.floor(Math.random() * 10000),
      _seedString: rules.seed ? `${rules.seed}-game${gameNum}` : `game${gameNum}-${Date.now()}`,
      // Pass pitcher plan for this game
      _pitcherPlan: gamePitcherPlan,
      // Force more aggressive rotation in later games for better balance
      rotateLineupEvery: gameNum > 1 ? 1 : (rules.rotateLineupEvery || 2)
    } as LineupRules & { _pitcherPlan?: PitcherPlan };
    
    console.log(`🎲 Game ${gameNum} using seed: ${gameRules.seed}`);
    console.log(`🔒 Position restrictions enabled: ${gameRules.respectPositionLockouts}`);
    
    // Generate single game lineup - USE STRICT VERSION
    let gameLineups: InningLineup[];
    try {
      gameLineups = generateSingleGameLineupStrict(
        playersWithPitcherBoost,
        inningsPerGame,
        gameRules
      );
      console.log(`✅ Game ${gameNum} generated successfully with STRICT algorithm`);
    } catch (strictError) {
      console.error(`❌ Strict algorithm failed: ${strictError.message}`);
      
      // Check if this is a position restriction error
      if (strictError.message.includes('without violating restrictions')) {
        // Extract position from error message
        const positionMatch = strictError.message.match(/position (\w+) in inning (\d+)/);
        const position = positionMatch ? positionMatch[1] : 'unknown';
        const inning = positionMatch ? positionMatch[2] : 'unknown';
        
        // Provide helpful guidance based on the position and mode
        let helpText = '';
        const isCompetitive = gameRules.competitiveMode;
        
        if (isCompetitive) {
          // Competitive mode requires explicit assignments
          if (position === 'catcher') {
            helpText = '\n\nTip (Competitive Mode): You need players explicitly assigned to catcher. In competitive mode, players can ONLY play positions they are assigned to. Check that you have 2-3 players marked as Primary, Capable, or Emergency for catcher.';
          } else if (position === 'pitcher') {
            helpText = '\n\nTip (Competitive Mode): You need players explicitly assigned to pitcher. In competitive mode, players can ONLY play positions they are assigned to. Check that you have enough players marked as Primary, Capable, or Emergency for pitcher.';
          } else {
            helpText = `\n\nTip (Competitive Mode): You need more players explicitly assigned to ${position}. In competitive mode, players can ONLY play positions they are assigned to (Primary, Capable, or Emergency). Unassigned positions cannot be played.`;
          }
        } else {
          // Recreational mode - avoid "Never" assignments
          if (position === 'catcher') {
            helpText = '\n\nTip: You may need more players who can catch. Check that you have 2-3 players marked as Primary, Capable, or Emergency for catcher. With limited catchers, the algorithm will keep the same catcher for multiple innings. You can also adjust rotation settings to rotate catchers less frequently.';
          } else if (position === 'pitcher') {
            helpText = '\n\nTip: You may need more pitchers. Check that you have enough players marked as Primary, Capable, or Emergency for pitcher. You can also increase "Rotate Pitcher Every" in rotation settings.';
          } else {
            helpText = `\n\nTip: You may need more players who can play ${position}. Check your team roster and ensure enough players are not marked as "Never" for this position.`;
          }
        }
        
        throw new Error(
          `Cannot create lineups: Not enough eligible players for ${position} in game ${gameNum}, inning ${inning}.` +
          helpText
        );
      }
      
      // Only fall back for other types of errors
      console.warn(`⚠️ Falling back to regular algorithm`);
      gameLineups = await generateSingleGameLineup(
        playersWithPitcherBoost,
        inningsPerGame,
        gameRules
      );
    }
    
    // Validate position restrictions using position_preferences (MODE-SPECIFIC)
    let violations = 0;
    const isCompetitive = gameRules.competitiveMode;
    
    gameLineups.forEach((inning, idx) => {
      Object.entries(inning.positions).forEach(([position, playerName]) => {
        if (position !== 'bench' && playerName) {
          const player = players.find(p => p.name === playerName);
          if (player) {
            // Use canPlayPosition for proper mode-specific validation
            if (!canPlayPosition(player, position, true, isCompetitive)) {
              if (isCompetitive && !player.positionPreferences?.[position]) {
                console.error(`🚨 VIOLATION in Game ${gameNum}, Inning ${idx + 1}: ${playerName} at ${position} (COMPETITIVE MODE - not assigned to this position)`);
                violations++;
              } else if (player.positionPreferences?.[position] === 'avoid') {
                console.error(`🚨 VIOLATION in Game ${gameNum}, Inning ${idx + 1}: ${playerName} at ${position} (marked as AVOID/Never)`);
                violations++;
              } else {
                // Check medical restrictions
                if (position === 'pitcher' && player.pitcher_restriction) {
                  console.error(`🚨 VIOLATION in Game ${gameNum}, Inning ${idx + 1}: ${playerName} at ${position} (medical restriction)`);
                  violations++;
                } else if (position === 'catcher' && player.catcher_restriction) {
                  console.error(`🚨 VIOLATION in Game ${gameNum}, Inning ${idx + 1}: ${playerName} at ${position} (medical restriction)`);
                  violations++;
                } else if (position === 'firstBase' && player.first_base_restriction) {
                  console.error(`🚨 VIOLATION in Game ${gameNum}, Inning ${idx + 1}: ${playerName} at ${position} (medical restriction)`);
                  violations++;
                }
              }
            }
          }
        }
      });
    });
    
    if (violations > 0) {
      console.error(`❌ CRITICAL ERROR: Game ${gameNum} has ${violations} position restriction violations!`);
      if (isCompetitive) {
        console.error(`❌ In COMPETITIVE MODE: Players can ONLY play positions they are explicitly assigned to`);
      } else {
        console.error(`❌ Players are assigned to positions they should NEVER play (marked as "Never/Avoid" or have medical restrictions)`);
      }
      // Don't throw here as it would prevent viewing the lineups, but this is a serious issue
    }
    
    // Calculate game stats
    const gameStats = calculateGameStats(gameLineups, players);
    
    // Update cumulative stats
    updateCumulativeStats(cumulativeStats, gameStats);
    
    // Store game result
    games.push({
      lineups: gameLineups,
      gameStats
    });
    
    // Log cumulative progress
    logCumulativeProgress(cumulativeStats, players, gameNum);
  }
  
  // Calculate final balance score
  const balanceScore = calculateSeriesBalance(cumulativeStats);
  
  console.log(`\n🏆 SERIES COMPLETE!`);
  console.log(`📊 Final Balance Score: ${balanceScore.toFixed(1)}%`);
  logFinalStats(cumulativeStats, players);
  
  return {
    games,
    cumulativeStats,
    balanceScore
  };
}

/**
 * Adjust player priorities based on cumulative playing time
 */
function adjustPlayerPriorities(
  players: Player[],
  cumulativeStats: Map<string, CumulativePlayerStats>,
  gameNumber: number
): Player[] {
  if (gameNumber === 1) {
    // First game - no adjustments needed
    return players;
  }
  
  // Calculate average field innings per player
  const totalFieldInnings = Array.from(cumulativeStats.values())
    .reduce((sum, stats) => sum + stats.totalFieldInnings, 0);
  const avgFieldInnings = totalFieldInnings / players.length;
  
  console.log(`📊 Average field innings after ${gameNumber - 1} games: ${avgFieldInnings.toFixed(1)}`);
  
  // Create adjusted players with priority adjustments
  return players.map(player => {
    const stats = cumulativeStats.get(player.id)!;
    const fieldInningsDiff = stats.totalFieldInnings - avgFieldInnings;
    
    // Players who have played less get priority boost
    // Players who have played more get priority reduction
    // Make adjustment more aggressive for better balance
    const priorityAdjustment = -fieldInningsDiff * 0.5; // Increased from 0.1 to 0.5
    
    // Log significant adjustments
    if (Math.abs(priorityAdjustment) > 0.5) {
      console.log(`  ${player.name}: ${stats.totalFieldInnings} innings (${fieldInningsDiff > 0 ? '+' : ''}${fieldInningsDiff.toFixed(1)} from avg) → priority ${priorityAdjustment > 0 ? '+' : ''}${priorityAdjustment.toFixed(2)}`);
    }
    
    return {
      ...player,
      // Add a virtual priority field for the algorithm to consider
      _priorityAdjustment: priorityAdjustment
    } as Player & { _priorityAdjustment: number };
  });
}

/**
 * Calculate stats for a single game
 */
function calculateGameStats(
  lineups: InningLineup[],
  players: Player[]
): Map<string, GamePlayerStats> {
  const gameStats = new Map<string, GamePlayerStats>();
  
  // Initialize stats
  players.forEach(player => {
    gameStats.set(player.id, {
      fieldInnings: 0,
      benchInnings: 0,
      inningsPitched: 0
    });
  });
  
  // Calculate stats from lineups
  const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 
                         'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
  
  lineups.forEach(lineup => {
    // Count field innings
    for (const pos of fieldPositions) {
      const playerName = (lineup.positions as any)[pos];
      if (playerName) {
        const player = players.find(p => p.name === playerName);
        if (player) {
          const stats = gameStats.get(player.id)!;
          stats.fieldInnings++;
          if (pos === 'pitcher') {
            stats.inningsPitched++;
          }
        }
      }
    }
    
    // Count bench innings
    for (const playerName of lineup.positions.bench) {
      const player = players.find(p => p.name === playerName);
      if (player) {
        const stats = gameStats.get(player.id)!;
        stats.benchInnings++;
      }
    }
  });
  
  return gameStats;
}

/**
 * Update cumulative stats with game results
 */
function updateCumulativeStats(
  cumulativeStats: Map<string, CumulativePlayerStats>,
  gameStats: Map<string, GamePlayerStats>
): void {
  gameStats.forEach((game, playerId) => {
    const cumulative = cumulativeStats.get(playerId)!;
    cumulative.totalFieldInnings += game.fieldInnings;
    cumulative.totalBenchInnings += game.benchInnings;
    cumulative.totalInningsPitched += game.inningsPitched;
    cumulative.gamesPlayed++;
  });
}

/**
 * Calculate balance score for the series
 */
function calculateSeriesBalance(
  cumulativeStats: Map<string, CumulativePlayerStats>
): number {
  const fieldInnings = Array.from(cumulativeStats.values())
    .map(stats => stats.totalFieldInnings);
  
  if (fieldInnings.length === 0) return 0;
  
  const min = Math.min(...fieldInnings);
  const max = Math.max(...fieldInnings);
  const avg = fieldInnings.reduce((a, b) => a + b, 0) / fieldInnings.length;
  
  if (avg === 0) return 0;
  
  // Calculate standard deviation
  const variance = fieldInnings.reduce((sum, innings) => {
    return sum + Math.pow(innings - avg, 2);
  }, 0) / fieldInnings.length;
  
  const stdDev = Math.sqrt(variance);
  
  // Balance score calculation - more forgiving than pure CV
  const range = max - min;
  
  // Start with 100 and apply penalties
  let balanceScore = 100;
  
  // Penalty based on range (most important factor)
  if (range > 2) {
    balanceScore -= (range - 2) * 10; // -10 points per inning over 2
  }
  
  // Penalty based on standard deviation (secondary factor)
  if (stdDev > 1.5) {
    balanceScore -= (stdDev - 1.5) * 15;
  }
  
  // Bonus for very tight distribution
  if (range <= 1) {
    balanceScore = Math.min(100, balanceScore + 10);
  }
  
  return Math.max(0, Math.round(balanceScore));
}

/**
 * Log cumulative progress after each game
 */
function logCumulativeProgress(
  cumulativeStats: Map<string, CumulativePlayerStats>,
  players: Player[],
  gameNumber: number
): void {
  console.log(`\n📊 Cumulative Stats after Game ${gameNumber}:`);
  
  const statsList = Array.from(cumulativeStats.entries())
    .map(([playerId, stats]) => {
      const player = players.find(p => p.id === playerId);
      return {
        name: player?.name || 'Unknown',
        fieldInnings: stats.totalFieldInnings,
        benchInnings: stats.totalBenchInnings
      };
    })
    .sort((a, b) => b.fieldInnings - a.fieldInnings);
  
  statsList.forEach(stats => {
    console.log(`  ${stats.name}: ${stats.fieldInnings} field, ${stats.benchInnings} bench`);
  });
  
  const fieldInnings = statsList.map(s => s.fieldInnings);
  const range = Math.max(...fieldInnings) - Math.min(...fieldInnings);
  console.log(`  Range: ${range} innings`);
}

/**
 * Log final series statistics
 */
function logFinalStats(
  cumulativeStats: Map<string, CumulativePlayerStats>,
  players: Player[]
): void {
  console.log(`\n📈 FINAL SERIES STATISTICS:`);
  
  const statsList = Array.from(cumulativeStats.entries())
    .map(([playerId, stats]) => {
      const player = players.find(p => p.id === playerId);
      return {
        name: player?.name || 'Unknown',
        ...stats
      };
    })
    .sort((a, b) => b.totalFieldInnings - a.totalFieldInnings);
  
  console.log(`\nField Time Distribution:`);
  statsList.forEach(stats => {
    const percentage = (stats.totalFieldInnings / (stats.totalFieldInnings + stats.totalBenchInnings) * 100).toFixed(1);
    console.log(`  ${stats.name}: ${stats.totalFieldInnings} innings (${percentage}%)`);
  });
  
  const fieldInnings = statsList.map(s => s.totalFieldInnings);
  const min = Math.min(...fieldInnings);
  const max = Math.max(...fieldInnings);
  const range = max - min;
  
  console.log(`\n📊 Summary:`);
  console.log(`  Min innings: ${min}`);
  console.log(`  Max innings: ${max}`);
  console.log(`  Range: ${range}`);
  console.log(`  Ideal range for fairness: ${range <= 3 ? '✅ Excellent' : range <= 5 ? '⚠️ Good' : '❌ Needs improvement'}`);
}

/**
 * Convert series result to cross-game tracking format (for compatibility)
 */
export function seriesToCrossGameTracking(series: SeriesResult): CrossGameTracking {
  const fieldInnings = new Map<string, number>();
  const benchInnings = new Map<string, number>();
  const pitchingInnings = new Map<string, number>();
  
  series.cumulativeStats.forEach((stats, playerId) => {
    fieldInnings.set(playerId, stats.totalFieldInnings);
    benchInnings.set(playerId, stats.totalBenchInnings);
    pitchingInnings.set(playerId, stats.totalInningsPitched);
  });
  
  return {
    fieldInnings,
    benchInnings,
    pitchingInnings
  };
}
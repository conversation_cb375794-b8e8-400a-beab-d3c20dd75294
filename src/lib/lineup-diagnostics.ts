/**
 * Lineup Diagnostics Logger
 * Provides comprehensive logging and anomaly detection for the rotation algorithm
 */

import { Player, InningLineup, LineupRules } from './utils-enhanced';

export interface DiagnosticEntry {
  timestamp: number;
  category: string;
  inning: number;
  message: string;
  data?: any;
}

export interface AnomalyReport {
  type: string;
  severity: 'low' | 'medium' | 'high';
  description: string;
  affectedPlayers?: string[];
  affectedInnings?: number[];
}

export class LineupDiagnosticsLogger {
  private logs: DiagnosticEntry[] = [];
  private startTime: number;
  
  constructor() {
    this.startTime = Date.now();
  }
  
  log(category: string, inning: number, message: string, data?: any) {
    this.logs.push({
      timestamp: Date.now() - this.startTime,
      category,
      inning,
      message,
      data
    });
  }
  
  logRotationDecision(inning: number, shouldRotate: boolean, reason: string, details?: any) {
    this.log('ROTATION', inning, `Rotation decision: ${shouldRotate ? 'YES' : 'NO'} - ${reason}`, {
      shouldRotate,
      reason,
      ...details
    });
  }
  
  logBenchStreak(inning: number, playerName: string, streak: number, isViolation: boolean) {
    this.log('BENCH_STREAK', inning, 
      `${playerName} bench streak: ${streak}${isViolation ? ' (VIOLATION!)' : ''}`, {
      player: playerName,
      streak,
      isViolation
    });
  }
  
  logPositionAssignment(inning: number, position: string, player: string, reason: string) {
    this.log('ASSIGNMENT', inning, `${position} → ${player}: ${reason}`, {
      position,
      player,
      reason
    });
  }
  
  logForceRotation(inning: number, player: string, fromBench: number, toPosition: string) {
    this.log('FORCE_ROTATION', inning, 
      `FORCED ${player} from bench (${fromBench} innings) to ${toPosition}`, {
      player,
      benchInnings: fromBench,
      newPosition: toPosition
    });
  }
  
  generateReport(): string {
    const report: string[] = [
      '=== LINEUP GENERATION DIAGNOSTIC REPORT ===',
      `Total time: ${Date.now() - this.startTime}ms`,
      `Total entries: ${this.logs.length}`,
      ''
    ];
    
    // Group by category
    const byCategory = new Map<string, DiagnosticEntry[]>();
    this.logs.forEach(entry => {
      if (!byCategory.has(entry.category)) {
        byCategory.set(entry.category, []);
      }
      byCategory.get(entry.category)!.push(entry);
    });
    
    // Report each category
    byCategory.forEach((entries, category) => {
      report.push(`\n[${category}] (${entries.length} entries)`);
      entries.forEach(entry => {
        report.push(`  Inning ${entry.inning}: ${entry.message}`);
        if (entry.data) {
          report.push(`    Data: ${JSON.stringify(entry.data, null, 2).replace(/\n/g, '\n    ')}`);
        }
      });
    });
    
    return report.join('\n');
  }
  
  detectAnomalies(lineup: InningLineup[], players: Player[], rules: LineupRules): AnomalyReport[] {
    const anomalies: AnomalyReport[] = [];
    
    // Check for repeating bench patterns
    const benchPatterns = this.detectRepeatingBenchPatterns(lineup);
    if (benchPatterns.length > 0) {
      anomalies.push({
        type: 'REPEATING_BENCH_PATTERN',
        severity: 'medium',
        description: 'Players stuck in repeating bench pattern',
        affectedPlayers: benchPatterns
      });
    }
    
    // Check for position monopoly
    const monopolies = this.detectPositionMonopoly(lineup);
    monopolies.forEach(({ position, player, innings }) => {
      if (innings > rules.rotatePitcherEvery * 2) {
        anomalies.push({
          type: 'POSITION_MONOPOLY',
          severity: 'high',
          description: `${player} monopolizing ${position} for ${innings} innings`,
          affectedPlayers: [player],
          affectedInnings: Array.from({ length: innings }, (_, i) => i + 1)
        });
      }
    });
    
    // Check for immediate re-benching
    const reBenched = this.detectImmediateReBenching(lineup);
    if (reBenched.length > 0) {
      anomalies.push({
        type: 'IMMEDIATE_RE_BENCHING',
        severity: 'high',
        description: 'Players immediately returned to bench after force rotation',
        affectedPlayers: reBenched
      });
    }
    
    // Check for rotation frequency violations
    const rotationViolations = this.detectRotationFrequencyViolations(lineup, rules);
    if (rotationViolations.length > 0) {
      anomalies.push({
        type: 'ROTATION_FREQUENCY_VIOLATION',
        severity: 'medium',
        description: 'Rotation not happening at expected intervals',
        affectedInnings: rotationViolations
      });
    }
    
    return anomalies;
  }
  
  private detectRepeatingBenchPatterns(lineup: InningLineup[]): string[] {
    const playerBenchHistory = new Map<string, boolean[]>();
    
    // Build bench history for each player
    lineup.forEach(inning => {
      const benchSet = new Set(inning.positions.bench);
      
      // Get all players
      const allPlayers = new Set<string>();
      Object.values(inning.positions).forEach(player => {
        if (typeof player === 'string' && player) {
          allPlayers.add(player);
        } else if (Array.isArray(player)) {
          player.forEach(p => allPlayers.add(p));
        }
      });
      
      allPlayers.forEach(player => {
        if (!playerBenchHistory.has(player)) {
          playerBenchHistory.set(player, []);
        }
        playerBenchHistory.get(player)!.push(benchSet.has(player));
      });
    });
    
    // Check for repeating patterns
    const affectedPlayers: string[] = [];
    playerBenchHistory.forEach((history, player) => {
      if (history.length >= 4) {
        // Check last 4 innings for pattern
        const recent = history.slice(-4);
        if (recent.every(b => b) || // Always on bench
            (recent[0] === recent[2] && recent[1] === recent[3])) { // Alternating
          affectedPlayers.push(player);
        }
      }
    });
    
    return affectedPlayers;
  }
  
  private detectPositionMonopoly(lineup: InningLineup[]): Array<{position: string; player: string; innings: number}> {
    const positionHistory = new Map<string, Map<string, number>>();
    
    lineup.forEach(inning => {
      Object.entries(inning.positions).forEach(([position, player]) => {
        if (position !== 'bench' && typeof player === 'string' && player) {
          if (!positionHistory.has(position)) {
            positionHistory.set(position, new Map());
          }
          const count = positionHistory.get(position)!.get(player) || 0;
          positionHistory.get(position)!.set(player, count + 1);
        }
      });
    });
    
    const monopolies: Array<{position: string; player: string; innings: number}> = [];
    positionHistory.forEach((playerCounts, position) => {
      playerCounts.forEach((count, player) => {
        if (count === lineup.length) {
          monopolies.push({ position, player, innings: count });
        }
      });
    });
    
    return monopolies;
  }
  
  private detectImmediateReBenching(lineup: InningLineup[]): string[] {
    const reBenched: string[] = [];
    
    for (let i = 2; i < lineup.length; i++) {
      const prevBench = new Set(lineup[i - 1].positions.bench);
      const currentBench = new Set(lineup[i].positions.bench);
      
      // Check if anyone went bench -> field -> bench
      if (i >= 2) {
        const twoPrevBench = new Set(lineup[i - 2].positions.bench);
        
        currentBench.forEach(player => {
          if (twoPrevBench.has(player) && !prevBench.has(player)) {
            // Was on bench 2 innings ago, played last inning, back on bench
            reBenched.push(player);
          }
        });
      }
    }
    
    return [...new Set(reBenched)];
  }
  
  private detectRotationFrequencyViolations(lineup: InningLineup[], rules: LineupRules): number[] {
    const violations: number[] = [];
    const rotateEvery = rules.rotateLineupEvery || 1;
    
    // Track position changes
    let lastPositions: Record<string, string> = {};
    
    lineup.forEach((inning, idx) => {
      const currentPositions: Record<string, string> = {};
      
      // Map players to positions
      Object.entries(inning.positions).forEach(([position, player]) => {
        if (position !== 'bench' && typeof player === 'string' && player) {
          currentPositions[player] = position;
        }
      });
      
      if (idx > 0) {
        // Check if rotation should have happened
        const shouldRotate = idx > 0 && (idx % rotateEvery === 0);
        
        // Count position changes
        let changes = 0;
        Object.entries(currentPositions).forEach(([player, position]) => {
          if (lastPositions[player] && lastPositions[player] !== position) {
            changes++;
          }
        });
        
        // If should rotate but didn't, or shouldn't but did
        if (shouldRotate && changes < 3) {
          violations.push(idx + 1);
        } else if (!shouldRotate && changes > 3) {
          violations.push(idx + 1);
        }
      }
      
      lastPositions = currentPositions;
    });
    
    return violations;
  }
  
  exportToConsole() {
    console.group('🔍 Lineup Generation Diagnostics');
    console.log(this.generateReport());
    console.groupEnd();
  }
}

// Singleton instance for easy access
export const lineupDiagnostics = new LineupDiagnosticsLogger();
/**
 * Balance Optimizer for Multi-Game Series
 * 
 * Redistributes players across all games to achieve near-perfect balance
 * while respecting position constraints and maintaining playable lineups
 */

import { Lineup, Player, InningLineup } from '@/contexts/TeamContext';

interface OptimizationResult {
  optimizedLineups: Lineup[];
  balanceScore: number;
  changes: string[];
}

interface PlayerStats {
  playerId: string;
  playerName: string;
  totalFieldInnings: number;
  totalBenchInnings: number;
  positionsPlayed: Map<string, number>;
}

/**
 * Main optimization function - takes existing lineups and rebalances them
 */
export function optimizeSeriesBalance(
  lineups: Lineup[],
  players: Player[]
): OptimizationResult {
  console.log('🎯 Starting balance optimization for', lineups.length, 'games');
  
  // Step 1: Calculate current stats
  const playerStats = calculatePlayerStats(lineups, players);
  const totalInnings = lineups.reduce((sum, lineup) => sum + lineup.innings.length, 0);
  const targetInningsPerPlayer = Math.floor((totalInnings * 9) / players.length);
  
  console.log(`📊 Target innings per player: ${targetInningsPerPlayer}`);
  
  // Step 2: Identify imbalances
  const overPlayers: PlayerStats[] = [];
  const underPlayers: PlayerStats[] = [];
  
  playerStats.forEach(stat => {
    const diff = stat.totalFieldInnings - targetInningsPerPlayer;
    if (diff > 1) {
      overPlayers.push(stat);
    } else if (diff < -1) {
      underPlayers.push(stat);
    }
  });
  
  // Sort by magnitude of imbalance
  overPlayers.sort((a, b) => b.totalFieldInnings - a.totalFieldInnings);
  underPlayers.sort((a, b) => a.totalFieldInnings - b.totalFieldInnings);
  
  console.log(`🔄 Found ${overPlayers.length} over-played and ${underPlayers.length} under-played players`);
  
  // Step 3: Create optimized lineups by swapping players
  const optimizedLineups = lineups.map(lineup => ({
    ...lineup,
    innings: lineup.innings.map(inning => ({
      ...inning,
      positions: { ...inning.positions }
    }))
  }));
  
  const changes: string[] = [];
  
  // Step 4: Perform swaps
  for (const overPlayer of overPlayers) {
    for (const underPlayer of underPlayers) {
      // Skip if already balanced
      if (overPlayer.totalFieldInnings <= targetInningsPerPlayer + 1) break;
      if (underPlayer.totalFieldInnings >= targetInningsPerPlayer - 1) continue;
      
      // Find compatible swaps
      const swapOpportunities = findSwapOpportunities(
        optimizedLineups,
        overPlayer,
        underPlayer,
        players
      );
      
      // Perform swaps until balanced
      for (const swap of swapOpportunities) {
        if (overPlayer.totalFieldInnings <= targetInningsPerPlayer + 1) break;
        if (underPlayer.totalFieldInnings >= targetInningsPerPlayer - 1) break;
        
        // Execute swap
        const lineup = optimizedLineups[swap.gameIndex];
        const inning = lineup.innings[swap.inningIndex];
        
        // Swap in positions
        inning.positions[swap.position as keyof typeof inning.positions] = underPlayer.playerName as any;
        
        // Update bench
        const benchIndex = inning.positions.bench.indexOf(underPlayer.playerName);
        if (benchIndex !== -1) {
          inning.positions.bench[benchIndex] = overPlayer.playerName;
        }
        
        // Update stats
        overPlayer.totalFieldInnings--;
        underPlayer.totalFieldInnings++;
        
        changes.push(
          `Game ${swap.gameIndex + 1}, Inning ${swap.inningIndex + 1}: ` +
          `Swapped ${overPlayer.playerName} (${swap.position}) with ${underPlayer.playerName}`
        );
      }
    }
  }
  
  // Step 5: Calculate new balance score
  const newStats = calculatePlayerStats(optimizedLineups, players);
  const balanceScore = calculateBalanceScore(newStats);
  
  console.log(`✅ Optimization complete: ${changes.length} swaps made`);
  console.log(`📈 Balance score improved to ${balanceScore}%`);
  
  return {
    optimizedLineups,
    balanceScore,
    changes
  };
}

/**
 * Find positions where two players can be swapped
 */
function findSwapOpportunities(
  lineups: Lineup[],
  overPlayer: PlayerStats,
  underPlayer: PlayerStats,
  players: Player[]
): Array<{ gameIndex: number; inningIndex: number; position: string }> {
  const opportunities = [];
  
  const overPlayerData = players.find(p => p.id === overPlayer.playerId);
  const underPlayerData = players.find(p => p.id === underPlayer.playerId);
  
  if (!overPlayerData || !underPlayerData) return opportunities;
  
  lineups.forEach((lineup, gameIndex) => {
    lineup.innings.forEach((inning, inningIndex) => {
      // Check each position
      Object.entries(inning.positions).forEach(([position, playerName]) => {
        if (position === 'bench') return;
        
        // If overPlayer is at this position
        if (playerName === overPlayer.playerName) {
          // Check if underPlayer is on bench and can play this position
          if (inning.positions.bench.includes(underPlayer.playerName)) {
            const canPlay = canPlayerPlayPosition(underPlayerData, position);
            if (canPlay) {
              opportunities.push({ gameIndex, inningIndex, position });
            }
          }
        }
      });
    });
  });
  
  return opportunities;
}

/**
 * Check if a player can play a specific position
 */
function canPlayerPlayPosition(player: Player, position: string): boolean {
  const role = player.teamRoles?.[position as keyof typeof player.teamRoles];
  
  // Can play if not explicitly avoided
  return role !== 'avoid' && role !== 'never';
}

/**
 * Calculate player statistics from lineups
 */
function calculatePlayerStats(lineups: Lineup[], players: Player[]): Map<string, PlayerStats> {
  const statsMap = new Map<string, PlayerStats>();
  
  // Initialize stats
  players.forEach(player => {
    statsMap.set(player.id, {
      playerId: player.id,
      playerName: player.name,
      totalFieldInnings: 0,
      totalBenchInnings: 0,
      positionsPlayed: new Map()
    });
  });
  
  // Count innings
  lineups.forEach(lineup => {
    lineup.innings.forEach(inning => {
      // Field positions
      Object.entries(inning.positions).forEach(([position, playerName]) => {
        if (position !== 'bench' && playerName) {
          const player = players.find(p => p.name === playerName);
          if (player) {
            const stats = statsMap.get(player.id)!;
            stats.totalFieldInnings++;
            stats.positionsPlayed.set(position, (stats.positionsPlayed.get(position) || 0) + 1);
          }
        }
      });
      
      // Bench
      inning.positions.bench.forEach(playerName => {
        const player = players.find(p => p.name === playerName);
        if (player) {
          const stats = statsMap.get(player.id)!;
          stats.totalBenchInnings++;
        }
      });
    });
  });
  
  return statsMap;
}

/**
 * Calculate balance score (0-100)
 */
function calculateBalanceScore(playerStats: Map<string, PlayerStats>): number {
  const fieldInnings = Array.from(playerStats.values())
    .map(stats => stats.totalFieldInnings);
  
  if (fieldInnings.length === 0) return 100;
  
  const min = Math.min(...fieldInnings);
  const max = Math.max(...fieldInnings);
  const range = max - min;
  
  // Start with 100 and apply penalties
  let score = 100;
  
  // Penalty based on range
  if (range > 2) {
    score -= (range - 2) * 10;
  }
  
  // Bonus for very tight distribution
  if (range <= 1) {
    score = Math.min(100, score + 10);
  }
  
  return Math.max(0, Math.round(score));
}
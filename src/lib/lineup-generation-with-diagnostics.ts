/**
 * Example of how to integrate diagnostic logging into lineup generation
 */

import { LineupDiagnosticsLogger } from './lineup-diagnostics';
import { generateCompleteLineup, Player, InningLineup, LineupRules } from './utils-enhanced';

export function generateLineupWithDiagnostics(
  players: Player[],
  totalInnings: number,
  rules: LineupRules
): {
  lineup: InningLineup[];
  diagnostics: string;
  anomalies: any[];
} {
  // Create diagnostic logger
  const logger = new LineupDiagnosticsLogger();
  
  // Wrap the generation process
  console.log('🔍 Starting lineup generation with diagnostics...');
  
  try {
    // Log initial state
    logger.log('INIT', 0, 'Starting lineup generation', {
      playerCount: players.length,
      totalInnings,
      rules
    });
    
    // Generate lineup
    const lineup = generateCompleteLineup(players, totalInnings, rules);
    
    // Log each inning's details
    lineup.forEach((inning, idx) => {
      // Log rotation decision
      const shouldRotate = idx > 0 && (idx % rules.rotateLineupEvery === 0);
      logger.logRotationDecision(
        idx + 1, 
        shouldRotate,
        shouldRotate ? 'Rotation interval reached' : 'Not rotation time',
        { rotateEvery: rules.rotateLineupEvery }
      );
      
      // Log position assignments
      Object.entries(inning.positions).forEach(([position, player]) => {
        if (position !== 'bench' && player) {
          logger.logPositionAssignment(
            idx + 1,
            position,
            player as string,
            'Algorithm assignment'
          );
        }
      });
      
      // Log bench players
      inning.positions.bench.forEach(player => {
        // Calculate bench streak (simplified)
        let streak = 1;
        for (let i = idx - 1; i >= 0; i--) {
          if (lineup[i].positions.bench.includes(player)) {
            streak++;
          } else {
            break;
          }
        }
        
        const isViolation = streak > rules.maxConsecutiveBenchInnings;
        logger.logBenchStreak(idx + 1, player, streak, isViolation);
        
        if (isViolation) {
          logger.log('VIOLATION', idx + 1, `${player} exceeds max bench time`, {
            player,
            streak,
            max: rules.maxConsecutiveBenchInnings
          });
        }
      });
    });
    
    // Detect anomalies
    const anomalies = logger.detectAnomalies(lineup, players, rules);
    
    if (anomalies.length > 0) {
      logger.log('ANOMALY', totalInnings, `Detected ${anomalies.length} anomalies`, anomalies);
    }
    
    // Generate report
    const diagnostics = logger.generateReport();
    
    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      logger.exportToConsole();
    }
    
    return {
      lineup,
      diagnostics,
      anomalies
    };
    
  } catch (error) {
    logger.log('ERROR', 0, 'Lineup generation failed', {
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

// Example usage in ViewLineup.tsx
export function useLineupWithDiagnostics() {
  const generateWithDiagnostics = (
    players: Player[],
    innings: number,
    rules: LineupRules
  ) => {
    const result = generateLineupWithDiagnostics(players, innings, rules);
    
    // Check for critical anomalies
    const criticalAnomalies = result.anomalies.filter(a => a.severity === 'high');
    if (criticalAnomalies.length > 0) {
      console.warn('⚠️ Critical anomalies detected:', criticalAnomalies);
      
      // Could show a warning to the user
      // toast.warning(`Lineup has ${criticalAnomalies.length} potential issues. Check diagnostics.`);
    }
    
    // Store diagnostics for debugging
    if (window.localStorage) {
      window.localStorage.setItem('lineup_diagnostics', result.diagnostics);
      console.log('💾 Diagnostics saved to localStorage (key: lineup_diagnostics)');
    }
    
    return result.lineup;
  };
  
  return generateWithDiagnostics;
}
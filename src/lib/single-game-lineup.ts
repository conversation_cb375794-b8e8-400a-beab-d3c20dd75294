/**
 * Single Game Lineup Generator
 * 
 * This module handles lineup generation for a single game without any
 * cross-game tracking or multi-game concerns. It focuses purely on:
 * - Fair rotation within the game
 * - Position restrictions and eligibility
 * - Bench time limits
 * 
 * For multi-game series, use the multi-game-orchestrator.ts
 */

import { 
  Player, 
  InningLineup, 
  LineupRules,
  POSITION_CONFIG,
  PositionKey,
  canPlayPosition,
  generateInitialLineup,
  validateLineup,
  validateRotation,
  PlayerEligibilityCache,
  ConstraintSolver,
  LineupRotator,
  SeededRandom
} from './utils-enhanced';
import { StrictConstraintSolver } from './strict-constraint-solver';

/**
 * Generate a complete lineup for a single game
 * This is a pure function that doesn't maintain any cross-game state
 */
export function generateSingleGameLineup(
  players: Player[],
  totalInnings: number,
  rules: LineupRules
): InningLineup[] {
  console.log(`🎯 GENERATING SINGLE GAME LINEUP - ${totalInnings} innings for ${players.length} players`);
  
  // Validate inputs
  if (players.length < 8) {
    throw new Error('Need at least 8 players to generate a lineup');
  }
  
  // Check if any players have 'avoid' restrictions
  const hasAvoidRestrictions = players.some(player => 
    Object.values(player.teamRoles || {}).includes('avoid')
  );
  
  if (hasAvoidRestrictions) {
    console.log('⚠️ Players have "avoid" restrictions - using STRICT constraint solver');
  }

  // Initialize tools
  const random = new SeededRandom(rules.seed);
  const eligibilityCache = new PlayerEligibilityCache();
  
  // Clear cache to ensure fresh eligibility checks with current team roles
  eligibilityCache.clear();
  
  const solver = new ConstraintSolver(players, eligibilityCache);
  const rotator = new LineupRotator(rules, eligibilityCache, random);
  
  console.log(`🎲 Single game using seed: ${rules.seed}, respectPositionLockouts: ${rules.respectPositionLockouts}`);

  // Initialize player stats for THIS GAME ONLY
  const gameStats = new Map<string, {
    fieldInnings: number;
    benchInnings: number;
    inningsPitched: number;
    benchStreak: number;
    lastPositions: Map<string, number>;
  }>();

  players.forEach(player => {
    gameStats.set(player.id, {
      fieldInnings: 0,
      benchInnings: 0,
      inningsPitched: 0,
      benchStreak: 0,
      lastPositions: new Map()
    });
  });

  const lineup: InningLineup[] = [];
  const maxBenchStreak = rules.maxConsecutiveBenchInnings || 2;

  // Generate each inning
  for (let inning = 1; inning <= totalInnings; inning++) {
    console.log(`\n🏟️ GENERATING INNING ${inning} of ${totalInnings}`);

    if (inning === 1) {
      // Generate initial lineup
      const initialLineup = generateInitialLineup(
        players,
        solver,
        eligibilityCache,
        rules
      );

      const validation = validateLineup(initialLineup, players, rules);
      if (!validation.valid) {
        console.error(`❌ Initial lineup validation failed:`, validation.errors);
        throw new Error(`Initial lineup validation failed: ${validation.errors.join(', ')}`);
      }

      lineup.push(initialLineup);
      updateGameStats(initialLineup, gameStats, players, 1);

    } else {
      // Rotate from previous inning
      const previousInning = lineup[inning - 2];
      
      // Create simple stats map for rotation
      const statsMap = new Map(
        Array.from(gameStats.entries()).map(([id, stats]) => [
          id,
          { fieldInnings: stats.fieldInnings, benchInnings: stats.benchInnings }
        ])
      );

      const rotatedLineup = rotator.rotateLineup(
        previousInning,
        players,
        inning,
        statsMap,
        lineup
      );

      const validation = validateLineup(rotatedLineup, players, rules);
      if (!validation.valid) {
        console.warn(`⚠️ Rotation produced invalid lineup for inning ${inning}:`, validation.errors);
      }

      lineup.push(rotatedLineup);
      updateGameStats(rotatedLineup, gameStats, players, inning);
    }

    // Log game stats after this inning
    console.log(`📊 Game stats after inning ${inning}:`);
    Array.from(gameStats.entries()).forEach(([id, stats]) => {
      const player = players.find(p => p.id === id);
      console.log(`  ${player?.name}: Field=${stats.fieldInnings}, Bench=${stats.benchInnings}, BenchStreak=${stats.benchStreak}`);
    });
  }

  // Final validation
  const finalValidation = validateAllRotationRules(lineup, rules, players);
  if (finalValidation.violationCount > 0) {
    console.warn(`⚠️ Final lineup has ${finalValidation.violationCount} rotation rule violations`);
  }

  // Log final summary
  console.log(`\n🎉 SINGLE GAME LINEUP COMPLETE!`);
  const fieldInningsArray = Array.from(gameStats.values()).map(s => s.fieldInnings);
  const minFieldInnings = Math.min(...fieldInningsArray);
  const maxFieldInnings = Math.max(...fieldInningsArray);
  console.log(`📊 Balance: ${minFieldInnings}-${maxFieldInnings} innings (range: ${maxFieldInnings - minFieldInnings})`);

  return lineup;
}

/**
 * Update game stats after each inning
 */
function updateGameStats(
  lineup: InningLineup,
  gameStats: Map<string, any>,
  players: Player[],
  inning: number
): void {
  const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 
                         'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
  
  // Update field players
  for (const pos of fieldPositions) {
    const playerName = (lineup.positions as any)[pos];
    if (playerName) {
      const player = players.find(p => p.name === playerName);
      if (!player) continue;
      
      const stats = gameStats.get(player.id)!;
      stats.fieldInnings++;
      stats.benchStreak = 0;
      stats.lastPositions.set(pos, inning);
      
      if (pos === 'pitcher') {
        stats.inningsPitched++;
      }
    }
  }
  
  // Update bench players
  for (const playerName of lineup.positions.bench) {
    const player = players.find(p => p.name === playerName);
    if (!player) continue;
    
    const stats = gameStats.get(player.id)!;
    stats.benchInnings++;
    stats.benchStreak++;
  }
}

/**
 * Validate all rotation rules for the complete game
 */
function validateAllRotationRules(
  lineup: InningLineup[],
  rules: LineupRules,
  players: Player[]
): { valid: boolean; violationCount: number; violations: string[] } {
  const allViolations: string[] = [];
  
  lineup.forEach((inning, index) => {
    const previousInnings = lineup.slice(0, index);
    const validation = validateRotation(previousInnings, inning, rules, players);
    
    if (!validation.valid) {
      validation.violations.forEach(v => {
        allViolations.push(`Inning ${index + 1}: ${v}`);
      });
    }
  });

  return {
    valid: allViolations.length === 0,
    violationCount: allViolations.length,
    violations: allViolations
  };
}
/**
 * Single Game Lineup Generator
 * 
 * This module handles lineup generation for a single game without any
 * cross-game tracking or multi-game concerns. It focuses purely on:
 * - Fair rotation within the game
 * - Position restrictions and eligibility
 * - Bench time limits
 * 
 * For multi-game series, use the multi-game-orchestrator.ts
 */

import { 
  Player, 
  InningLineup, 
  LineupRules,
  POSITION_CONFIG,
  PositionKey,
  canPlayPosition,
  generateInitialLineup,
  validateLineup,
  validateRotation,
  PlayerEligibilityCache,
  LineupRotator,
  SeededRandom,
  generateCompleteLineupAsync
} from './utils-enhanced';
import { StrictConstraintSolver } from './strict-constraint-solver';

/**
 * Generate a complete lineup for a single game
 * This is a pure function that doesn't maintain any cross-game state
 */
export async function generateSingleGameLineup(
  players: Player[],
  totalInnings: number,
  rules: LineupRules
): Promise<InningLineup[]> {
  console.log(`🎯 GENERATING SINGLE GAME LINEUP - ${totalInnings} innings for ${players.length} players`);
  console.log('🆕 Using NEW lineup generation algorithm (forced by kill switch)');
  
  // Validate inputs
  if (players.length < 8) {
    throw new Error('Need at least 8 players to generate a lineup');
  }
  
  // Check if any players have 'avoid' restrictions in position_preferences
  const hasAvoidRestrictions = players.some(player => {
    if (!player.positionPreferences) return false;
    return Object.values(player.positionPreferences).some(pref => {
      const level = typeof pref === 'string' ? pref : pref?.level;
      return level === 'avoid';
    });
  });
  
  if (hasAvoidRestrictions) {
    console.log('⚠️ Players have "avoid" restrictions in position_preferences - algorithm will respect these');
  }

  try {
    // Use the new async lineup generation algorithm
    const lineup = await generateCompleteLineupAsync(players, totalInnings, rules);
    
    // Log game stats after generation
    console.log(`\n🎉 SINGLE GAME LINEUP COMPLETE!`);
    console.log(`📊 Generated ${lineup.length} innings`);
    
    // Calculate and log balance stats
    const playerStats = new Map<string, { fieldInnings: number; benchInnings: number }>();
    
    // Initialize stats
    players.forEach(player => {
      playerStats.set(player.id, { fieldInnings: 0, benchInnings: 0 });
    });
    
    // Calculate stats from generated lineup
    lineup.forEach(inning => {
      const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 
                             'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
      
      // Count field innings
      for (const pos of fieldPositions) {
        const playerName = (inning.positions as any)[pos];
        if (playerName) {
          const player = players.find(p => p.name === playerName);
          if (player) {
            const stats = playerStats.get(player.id)!;
            stats.fieldInnings++;
          }
        }
      }
      
      // Count bench innings
      for (const playerName of inning.positions.bench) {
        const player = players.find(p => p.name === playerName);
        if (player) {
          const stats = playerStats.get(player.id)!;
          stats.benchInnings++;
        }
      }
    });
    
    // Log balance
    const fieldInningsArray = Array.from(playerStats.values()).map(s => s.fieldInnings);
    const minFieldInnings = Math.min(...fieldInningsArray);
    const maxFieldInnings = Math.max(...fieldInningsArray);
    console.log(`📊 Balance: ${minFieldInnings}-${maxFieldInnings} innings (range: ${maxFieldInnings - minFieldInnings})`);
    
    return lineup;
    
  } catch (error) {
    console.error('❌ Failed to generate single game lineup:', error);
    throw error;
  }
}

/**
 * Update game stats after each inning
 */
function updateGameStats(
  lineup: InningLineup,
  gameStats: Map<string, any>,
  players: Player[],
  inning: number
): void {
  const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 
                         'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
  
  // Update field players
  for (const pos of fieldPositions) {
    const playerName = (lineup.positions as any)[pos];
    if (playerName) {
      const player = players.find(p => p.name === playerName);
      if (!player) continue;
      
      const stats = gameStats.get(player.id)!;
      stats.fieldInnings++;
      stats.benchStreak = 0;
      stats.lastPositions.set(pos, inning);
      
      if (pos === 'pitcher') {
        stats.inningsPitched++;
      }
    }
  }
  
  // Update bench players
  for (const playerName of lineup.positions.bench) {
    const player = players.find(p => p.name === playerName);
    if (!player) continue;
    
    const stats = gameStats.get(player.id)!;
    stats.benchInnings++;
    stats.benchStreak++;
  }
}

/**
 * Validate all rotation rules for the complete game
 */
function validateAllRotationRules(
  lineup: InningLineup[],
  rules: LineupRules,
  players: Player[]
): { valid: boolean; violationCount: number; violations: string[] } {
  const allViolations: string[] = [];
  
  lineup.forEach((inning, index) => {
    const previousInnings = lineup.slice(0, index);
    const validation = validateRotation(previousInnings, inning, rules, players);
    
    if (!validation.valid) {
      validation.violations.forEach(v => {
        allViolations.push(`Inning ${index + 1}: ${v}`);
      });
    }
  });

  return {
    valid: allViolations.length === 0,
    violationCount: allViolations.length,
    violations: allViolations
  };
}
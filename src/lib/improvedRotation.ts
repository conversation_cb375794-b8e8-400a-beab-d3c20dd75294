import type { Player, InningLineup, LineupRules } from './utils-enhanced';
import { canPlayPosition as canPlayPositionUtil, getPositionDisplayName } from './utils-enhanced';

/**
 * Improved rotation algorithm that combines the best aspects of both approaches:
 * 1. Respects rotation frequencies (rotateLineupEvery, rotatePitcherEvery)
 * 2. Uses aggressive rotation to maximize playing time balance
 * 3. Respects position restrictions when configured
 * 4. Handles pitcher rotation correctly
 */
export function improvedRotateForNextInning(
  previousInning: InningLineup,
  availablePlayers: Player[],
  rules: LineupRules
): InningLineup {
  const nextInningNumber = previousInning.inning + 1;
  
  // Create new inning structure
  const newInning: InningLineup = {
    inning: nextInningNumber,
    positions: {
      pitcher: '',
      catcher: '',
      firstBase: '',
      secondBase: '',
      thirdBase: '',
      shortstop: '',
      leftField: '',
      centerField: '',
      rightField: '',
      bench: []
    }
  };

  // Get all players from previous inning
  const allPlayerNames = [
    ...Object.values(previousInning.positions).filter(p => typeof p === 'string' && p),
    ...(previousInning.positions.bench || [])
  ].filter(Boolean) as string[];

  // Determine if we should rotate this inning
  const shouldRotateLineup = nextInningNumber > 1 && 
    ((nextInningNumber - 1) % (rules.rotateLineupEvery || 1) === 0);
  
  const shouldRotatePitcher = rules.allowPitcherRotation && 
    rules.rotatePitcherEvery && 
    rules.rotatePitcherEvery > 0 &&
    nextInningNumber > 1 &&
    ((nextInningNumber - 1) % rules.rotatePitcherEvery === 0);

  console.log(`🔄 Inning ${nextInningNumber} rotation check:`, {
    shouldRotateLineup,
    shouldRotatePitcher,
    rotateLineupEvery: rules.rotateLineupEvery,
    rotatePitcherEvery: rules.rotatePitcherEvery
  });

  // If no rotation needed, keep current assignments
  if (!shouldRotateLineup && !shouldRotatePitcher) {
    console.log(`⏸️ No rotation scheduled for inning ${nextInningNumber}`);
    return {
      inning: nextInningNumber,
      positions: { ...previousInning.positions }
    };
  }

  // Helper function to check if a player can play a position
  const canPlayPosition = (playerName: string, position: string): boolean => {
    if (!rules.respectPositionLockouts) return true;
    
    const player = availablePlayers.find(p => p.name === playerName);
    if (!player) return false;
    
    const positionDisplayName = getPositionDisplayName(position);
    return canPlayPositionUtil(player, positionDisplayName);
  };

  // Start assignments
  const assignedPlayers = new Set<string>();
  const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 
                          'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];

  // Handle pitcher rotation separately
  if (shouldRotatePitcher) {
    const currentPitcher = previousInning.positions.pitcher;
    const benchPlayers = previousInning.positions.bench || [];
    
    // Find eligible pitchers on bench
    const eligiblePitchers = benchPlayers.filter(playerName => 
      canPlayPosition(playerName, 'pitcher')
    );

    if (eligiblePitchers.length > 0) {
      // Rotate to first eligible pitcher on bench
      newInning.positions.pitcher = eligiblePitchers[0];
      assignedPlayers.add(eligiblePitchers[0]);
      console.log(`⚾ Pitcher rotation: ${currentPitcher} → ${eligiblePitchers[0]}`);
    } else {
      // No eligible pitcher on bench, keep current
      newInning.positions.pitcher = currentPitcher;
      assignedPlayers.add(currentPitcher);
      console.log(`⚾ No eligible pitcher on bench, keeping ${currentPitcher}`);
    }
  } else {
    // Keep current pitcher
    newInning.positions.pitcher = previousInning.positions.pitcher;
    assignedPlayers.add(previousInning.positions.pitcher);
  }

  // Handle lineup rotation
  if (shouldRotateLineup) {
    console.log(`🔄 Performing lineup rotation for inning ${nextInningNumber}`);
    
    // Create player pools
    const benchPlayers = (previousInning.positions.bench || []).filter(p => !assignedPlayers.has(p));
    const fieldPlayers = allPlayerNames.filter(p => 
      !benchPlayers.includes(p) && !assignedPlayers.has(p)
    );

    // AGGRESSIVE ROTATION: Prioritize bench players for field positions
    const rotationPriority = [...benchPlayers, ...fieldPlayers];
    
    // Shuffle within each group for variety
    const shuffledBench = [...benchPlayers].sort(() => Math.random() - 0.5);
    const shuffledField = [...fieldPlayers].sort(() => Math.random() - 0.5);
    const availableForAssignment = [...shuffledBench, ...shuffledField];

    console.log(`🎯 Rotation priority:`, { 
      benchFirst: shuffledBench, 
      fieldSecond: shuffledField 
    });

    // Assign remaining positions
    for (const position of fieldPositions) {
      if (position === 'pitcher') continue; // Already handled
      
      let assigned = false;
      
      // Try to assign from available players who can play this position
      for (const playerName of availableForAssignment) {
        if (!assignedPlayers.has(playerName) && canPlayPosition(playerName, position)) {
          (newInning.positions as any)[position] = playerName;
          assignedPlayers.add(playerName);
          assigned = true;
          console.log(`✅ Assigned ${playerName} to ${position}`);
          break;
        }
      }
      
      // Fallback: assign anyone if no eligible player found
      if (!assigned) {
        for (const playerName of availableForAssignment) {
          if (!assignedPlayers.has(playerName)) {
            (newInning.positions as any)[position] = playerName;
            assignedPlayers.add(playerName);
            console.log(`⚠️ Force assigned ${playerName} to ${position}`);
            break;
          }
        }
      }
    }
  } else {
    // No lineup rotation, keep current assignments (except pitcher if rotated)
    for (const position of fieldPositions) {
      if (position === 'pitcher') continue; // Already handled
      (newInning.positions as any)[position] = (previousInning.positions as any)[position];
      assignedPlayers.add((previousInning.positions as any)[position]);
    }
  }

  // Assign remaining players to bench
  newInning.positions.bench = allPlayerNames.filter(p => !assignedPlayers.has(p));

  console.log(`✅ Generated inning ${nextInningNumber}:`, {
    rotations: shouldRotateLineup ? 'YES' : 'NO',
    pitcherRotated: shouldRotatePitcher ? 'YES' : 'NO',
    field: fieldPositions.map(p => (newInning.positions as any)[p]),
    bench: newInning.positions.bench
  });

  return newInning;
}

/**
 * Generate complete lineup using the improved rotation algorithm
 */
export function generateImprovedLineup(
  firstInning: InningLineup,
  availablePlayers: Player[],
  totalInnings: number,
  rules: LineupRules
): InningLineup[] {
  console.log(`🚀 Generating improved lineup for ${totalInnings} innings`);
  
  const lineup: InningLineup[] = [firstInning];
  let currentInning = firstInning;
  
  for (let i = 2; i <= totalInnings; i++) {
    const nextInning = improvedRotateForNextInning(currentInning, availablePlayers, rules);
    lineup.push(nextInning);
    currentInning = nextInning;
  }
  
  console.log(`✅ Generated ${lineup.length} innings with improved rotation`);
  return lineup;
}
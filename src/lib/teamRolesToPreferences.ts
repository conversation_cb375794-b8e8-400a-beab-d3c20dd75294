import { Player, TeamRolePreferences, PositionPreferences } from '@/contexts/TeamContext';

/**
 * Convert teamRoles to positionPreferences format for backward compatibility
 * with the lineup generation algorithm
 */
export function convertTeamRolesToPositionPreferences(teamRoles: TeamRolePreferences | undefined): PositionPreferences {
  // Start with all positions set to 'neutral' to ensure every position has an entry
  const preferences: PositionPreferences = {
    pitcher: 'neutral',
    catcher: 'neutral',
    firstBase: 'neutral',
    secondBase: 'neutral',
    thirdBase: 'neutral',
    shortstop: 'neutral',
    leftField: 'neutral',
    centerField: 'neutral',
    rightField: 'neutral'
  };
  
  if (!teamRoles) return preferences;
  
  // Map each team role to a position preference
  Object.entries(teamRoles).forEach(([position, role]) => {
    // Skip special flags
    if (position === 'isUtilityPlayer' || position === 'isOutfieldSpecialist') {
      return;
    }
    
    // Convert role to preference level
    switch (role) {
      case 'go-to':
        preferences[position as keyof PositionPreferences] = 'preferred';
        break;
      case 'capable':
        preferences[position as keyof PositionPreferences] = 'secondary';
        break;
      case 'fill-in':
        preferences[position as keyof PositionPreferences] = 'secondary';
        break;
      case 'avoid':
        preferences[position as keyof PositionPreferences] = 'avoid';
        break;
      // 'unset' or undefined - leave as neutral
    }
  });
  
  return preferences;
}

/**
 * Enhance player objects with positionPreferences derived from teamRoles
 * This ensures backward compatibility with the lineup generation algorithm
 */
export function enhancePlayersWithPositionPreferences(players: Player[]): Player[] {
  return players.map(player => {
    // If player already has positionPreferences that aren't empty, use them
    if (player.positionPreferences && Object.keys(player.positionPreferences).length > 0) {
      return player;
    }
    
    // Otherwise, convert teamRoles to positionPreferences
    return {
      ...player,
      positionPreferences: convertTeamRolesToPositionPreferences(player.teamRoles)
    };
  });
}

/**
 * Check if a player has any position assignments (either teamRoles or positionPreferences)
 */
export function playerHasPositionAssignments(player: Player): boolean {
  // Check teamRoles (excluding special flags)
  if (player.teamRoles) {
    const actualRoles = Object.entries(player.teamRoles)
      .filter(([key, value]) => 
        key !== 'isUtilityPlayer' && 
        key !== 'isOutfieldSpecialist' &&
        value && 
        value !== 'unset'
      );
    if (actualRoles.length > 0) return true;
  }
  
  // Check positionPreferences
  if (player.positionPreferences) {
    const actualPrefs = Object.entries(player.positionPreferences)
      .filter(([_, value]) => value && value !== 'unset');
    if (actualPrefs.length > 0) return true;
  }
  
  return false;
}
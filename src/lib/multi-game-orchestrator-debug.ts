/**
 * Debug version of multi-game orchestrator with extensive logging
 */

import { 
  Player, 
  InningLineup, 
  LineupRules,
  CrossGameTracking
} from './utils-enhanced';

import { generateSingleGameLineup } from './single-game-lineup';

export interface GameResult {
  lineups: InningLineup[];
  gameStats: Map<string, GamePlayerStats>;
}

export interface GamePlayerStats {
  fieldInnings: number;
  benchInnings: number;
  inningsPitched: number;
}

export interface SeriesResult {
  games: GameResult[];
  cumulativeStats: Map<string, CumulativePlayerStats>;
  balanceScore: number;
}

export interface CumulativePlayerStats {
  totalFieldInnings: number;
  totalBenchInnings: number;
  totalInningsPitched: number;
  gamesPlayed: number;
}

/**
 * Generate lineups for multiple games with cross-game fairness
 */
export function generateMultiGameSeriesDebug(
  players: Player[],
  numberOfGames: number,
  inningsPerGame: number,
  rules: LineupRules
): SeriesResult {
  console.log(`🎮 DEBUG: GENERATING ${numberOfGames} GAME SERIES - ${inningsPerGame} innings per game`);
  console.log(`📋 DEBUG: Rotation settings:`, {
    rotateLineupEvery: rules.rotateLineupEvery,
    rotatePitcherEvery: rules.rotatePitcherEvery,
    respectPositionLockouts: rules.respectPositionLockouts,
    equalPlayingTime: rules.equalPlayingTime
  });
  
  // Log player restrictions
  console.log(`🔒 DEBUG: Player position restrictions:`);
  players.forEach(player => {
    const restrictions = Object.entries(player.teamRoles || {})
      .filter(([pos, role]) => role === 'never' || role === 'avoid')
      .map(([pos, role]) => `${pos}:${role}`);
    if (restrictions.length > 0) {
      console.log(`  ${player.name}: ${restrictions.join(', ')}`);
    }
  });
  
  const games: GameResult[] = [];
  const cumulativeStats = new Map<string, CumulativePlayerStats>();
  
  // Initialize cumulative stats
  players.forEach(player => {
    cumulativeStats.set(player.id, {
      totalFieldInnings: 0,
      totalBenchInnings: 0,
      totalInningsPitched: 0,
      gamesPlayed: 0
    });
  });

  // Store previous game lineups for comparison
  let previousGameLineups: InningLineup[] | null = null;

  // Generate each game
  for (let gameNum = 1; gameNum <= numberOfGames; gameNum++) {
    console.log(`\n🏟️ DEBUG: GENERATING GAME ${gameNum} of ${numberOfGames}`);
    
    // Adjust player priorities based on cumulative stats
    const adjustedPlayers = adjustPlayerPriorities(players, cumulativeStats, gameNum);
    
    // Create game-specific rules with unique seed for each game
    const gameRules = {
      ...rules,
      // CRITICAL: Use different seed for each game to avoid identical innings
      seed: rules.seed ? `${rules.seed}-game${gameNum}` : `game${gameNum}-${Date.now()}-${Math.random()}`,
      // FORCE rotation settings
      rotateLineupEvery: rules.rotateLineupEvery || 1,
      respectPositionLockouts: true,
      equalPlayingTime: true
    };
    
    console.log(`🎲 DEBUG: Game ${gameNum} using seed: ${gameRules.seed}`);
    console.log(`🔄 DEBUG: Rotation settings for this game:`, {
      rotateLineupEvery: gameRules.rotateLineupEvery,
      respectPositionLockouts: gameRules.respectPositionLockouts,
      equalPlayingTime: gameRules.equalPlayingTime
    });
    
    // Generate single game lineup
    const gameLineups = generateSingleGameLineup(
      adjustedPlayers,
      inningsPerGame,
      gameRules
    );
    
    // Compare with previous game to detect identical innings
    if (previousGameLineups && gameNum > 1) {
      console.log(`\n🔍 DEBUG: Comparing Game ${gameNum} with Game ${gameNum - 1}:`);
      for (let i = Math.max(0, inningsPerGame - 3); i < inningsPerGame; i++) {
        if (i < gameLineups.length && i < previousGameLineups.length) {
          const currentInning = gameLineups[i];
          const previousInning = previousGameLineups[i];
          
          // Compare field positions
          let identicalCount = 0;
          const positions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop', 
                           'thirdBase', 'leftField', 'centerField', 'rightField'];
          
          positions.forEach(pos => {
            if (currentInning.positions[pos] === previousInning.positions[pos]) {
              identicalCount++;
            }
          });
          
          const similarityPercent = (identicalCount / positions.length * 100).toFixed(0);
          if (identicalCount >= 8) {
            console.warn(`⚠️ Inning ${i + 1}: ${similarityPercent}% identical to previous game!`);
          }
        }
      }
    }
    previousGameLineups = gameLineups;
    
    // Validate position restrictions
    let violations = 0;
    const violationDetails: string[] = [];
    gameLineups.forEach((inning, idx) => {
      Object.entries(inning.positions).forEach(([position, playerName]) => {
        if (position !== 'bench' && playerName) {
          const player = players.find(p => p.name === playerName);
          if (player?.teamRoles) {
            const role = player.teamRoles[position as keyof typeof player.teamRoles];
            if (role === 'never' || role === 'avoid') {
              const violation = `Game ${gameNum}, Inning ${idx + 1}: ${playerName} at ${position} (role: ${role})`;
              violationDetails.push(violation);
              violations++;
            }
          }
        }
      });
    });
    
    if (violations > 0) {
      console.error(`🚨 DEBUG: Game ${gameNum} has ${violations} position restriction violations!`);
      violationDetails.forEach(v => console.error(`  - ${v}`));
    } else {
      console.log(`✅ DEBUG: Game ${gameNum} has no position restriction violations`);
    }
    
    // Calculate game stats
    const gameStats = calculateGameStats(gameLineups, players);
    
    // Update cumulative stats
    updateCumulativeStats(cumulativeStats, gameStats);
    
    // Store game result
    games.push({
      lineups: gameLineups,
      gameStats
    });
    
    // Log cumulative progress
    logCumulativeProgress(cumulativeStats, players, gameNum);
  }
  
  // Calculate final balance score
  const balanceScore = calculateSeriesBalance(cumulativeStats);
  
  console.log(`\n🏆 DEBUG: SERIES COMPLETE!`);
  console.log(`📊 Final Balance Score: ${balanceScore.toFixed(1)}%`);
  console.log(`📊 Total games generated: ${games.length} (requested: ${numberOfGames})`);
  logFinalStats(cumulativeStats, players);
  
  return {
    games,
    cumulativeStats,
    balanceScore
  };
}

// Copy all the helper functions from multi-game-orchestrator.ts
function adjustPlayerPriorities(
  players: Player[],
  cumulativeStats: Map<string, CumulativePlayerStats>,
  gameNumber: number
): Player[] {
  if (gameNumber === 1) {
    return players;
  }
  
  const totalFieldInnings = Array.from(cumulativeStats.values())
    .reduce((sum, stats) => sum + stats.totalFieldInnings, 0);
  const avgFieldInnings = totalFieldInnings / players.length;
  
  console.log(`📊 DEBUG: Average field innings after ${gameNumber - 1} games: ${avgFieldInnings.toFixed(1)}`);
  
  return players.map(player => {
    const stats = cumulativeStats.get(player.id)!;
    const fieldInningsDiff = stats.totalFieldInnings - avgFieldInnings;
    const priorityAdjustment = -fieldInningsDiff * 0.1;
    
    return {
      ...player,
      _priorityAdjustment: priorityAdjustment
    } as Player & { _priorityAdjustment: number };
  });
}

function calculateGameStats(
  lineups: InningLineup[],
  players: Player[]
): Map<string, GamePlayerStats> {
  const gameStats = new Map<string, GamePlayerStats>();
  
  players.forEach(player => {
    gameStats.set(player.id, {
      fieldInnings: 0,
      benchInnings: 0,
      inningsPitched: 0
    });
  });
  
  const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 
                         'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
  
  lineups.forEach(lineup => {
    for (const pos of fieldPositions) {
      const playerName = (lineup.positions as any)[pos];
      if (playerName) {
        const player = players.find(p => p.name === playerName);
        if (player) {
          const stats = gameStats.get(player.id)!;
          stats.fieldInnings++;
          if (pos === 'pitcher') {
            stats.inningsPitched++;
          }
        }
      }
    }
    
    for (const playerName of lineup.positions.bench) {
      const player = players.find(p => p.name === playerName);
      if (player) {
        const stats = gameStats.get(player.id)!;
        stats.benchInnings++;
      }
    }
  });
  
  return gameStats;
}

function updateCumulativeStats(
  cumulativeStats: Map<string, CumulativePlayerStats>,
  gameStats: Map<string, GamePlayerStats>
): void {
  gameStats.forEach((game, playerId) => {
    const cumulative = cumulativeStats.get(playerId)!;
    cumulative.totalFieldInnings += game.fieldInnings;
    cumulative.totalBenchInnings += game.benchInnings;
    cumulative.totalInningsPitched += game.inningsPitched;
    cumulative.gamesPlayed++;
  });
}

function calculateSeriesBalance(
  cumulativeStats: Map<string, CumulativePlayerStats>
): number {
  const fieldInnings = Array.from(cumulativeStats.values())
    .map(stats => stats.totalFieldInnings);
  
  if (fieldInnings.length === 0) return 0;
  
  const min = Math.min(...fieldInnings);
  const max = Math.max(...fieldInnings);
  const avg = fieldInnings.reduce((a, b) => a + b, 0) / fieldInnings.length;
  
  if (avg === 0) return 0;
  
  const variance = fieldInnings.reduce((sum, innings) => {
    return sum + Math.pow(innings - avg, 2);
  }, 0) / fieldInnings.length;
  
  const stdDev = Math.sqrt(variance);
  const cv = stdDev / avg;
  const balanceScore = Math.max(0, 100 * (1 - cv));
  
  return balanceScore;
}

function logCumulativeProgress(
  cumulativeStats: Map<string, CumulativePlayerStats>,
  players: Player[],
  gameNumber: number
): void {
  console.log(`\n📊 DEBUG: Cumulative Stats after Game ${gameNumber}:`);
  
  const statsList = Array.from(cumulativeStats.entries())
    .map(([playerId, stats]) => {
      const player = players.find(p => p.id === playerId);
      return {
        name: player?.name || 'Unknown',
        fieldInnings: stats.totalFieldInnings,
        benchInnings: stats.totalBenchInnings
      };
    })
    .sort((a, b) => b.fieldInnings - a.fieldInnings);
  
  statsList.forEach(stats => {
    console.log(`  ${stats.name}: ${stats.fieldInnings} field, ${stats.benchInnings} bench`);
  });
  
  const fieldInnings = statsList.map(s => s.fieldInnings);
  const range = Math.max(...fieldInnings) - Math.min(...fieldInnings);
  console.log(`  Range: ${range} innings`);
}

function logFinalStats(
  cumulativeStats: Map<string, CumulativePlayerStats>,
  players: Player[]
): void {
  console.log(`\n📈 DEBUG: FINAL SERIES STATISTICS:`);
  
  const statsList = Array.from(cumulativeStats.entries())
    .map(([playerId, stats]) => {
      const player = players.find(p => p.id === playerId);
      return {
        name: player?.name || 'Unknown',
        ...stats
      };
    })
    .sort((a, b) => b.totalFieldInnings - a.totalFieldInnings);
  
  console.log(`\nField Time Distribution:`);
  statsList.forEach(stats => {
    const percentage = (stats.totalFieldInnings / (stats.totalFieldInnings + stats.totalBenchInnings) * 100).toFixed(1);
    console.log(`  ${stats.name}: ${stats.totalFieldInnings} innings (${percentage}%)`);
  });
  
  const fieldInnings = statsList.map(s => s.totalFieldInnings);
  const min = Math.min(...fieldInnings);
  const max = Math.max(...fieldInnings);
  const range = max - min;
  
  console.log(`\n📊 Summary:`);
  console.log(`  Min innings: ${min}`);
  console.log(`  Max innings: ${max}`);
  console.log(`  Range: ${range}`);
  console.log(`  Ideal range for fairness: ${range <= 3 ? '✅ Excellent' : range <= 5 ? '⚠️ Good' : '❌ Needs improvement'}`);
}

export function seriesToCrossGameTracking(series: SeriesResult): CrossGameTracking {
  const fieldInnings = new Map<string, number>();
  const benchInnings = new Map<string, number>();
  const pitchingInnings = new Map<string, number>();
  
  series.cumulativeStats.forEach((stats, playerId) => {
    fieldInnings.set(playerId, stats.totalFieldInnings);
    benchInnings.set(playerId, stats.totalBenchInnings);
    pitchingInnings.set(playerId, stats.totalInningsPitched);
  });
  
  return {
    fieldInnings,
    benchInnings,
    pitchingInnings
  };
}
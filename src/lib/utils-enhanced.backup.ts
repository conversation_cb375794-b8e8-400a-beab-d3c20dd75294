import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { Position, Player, InningLineup, Lineup } from "@/contexts/TeamContext";
import { v4 as uuidv4 } from "uuid";
import { supabase } from "@/integrations/supabase/client";

// ===== POSITION CONFIGURATION (Fix #1) =====
export const POSITION_CONFIG = {
  pitcher: { key: 'pitcher', display: 'Pitcher', abbrev: 'P' },
  catcher: { key: 'catcher', display: 'Catcher', abbrev: 'C' },
  firstBase: { key: 'firstBase', display: 'First Base', abbrev: '1B' },
  secondBase: { key: 'secondBase', display: 'Second Base', abbrev: '2B' },
  shortstop: { key: 'shortstop', display: 'Shortstop', abbrev: 'SS' },
  thirdBase: { key: 'thirdBase', display: 'Third Base', abbrev: '3B' },
  leftField: { key: 'leftField', display: 'Left Field', abbrev: 'LF' },
  centerField: { key: 'centerField', display: 'Center Field', abbrev: 'CF' },
  rightField: { key: 'rightField', display: 'Right Field', abbrev: 'RF' },
  utility: { key: 'utility', display: '3B/MI/SS/2B', abbrev: 'UTL' }
} as const;

export type PositionKey = keyof typeof POSITION_CONFIG;

// Derive POSITIONS array from config
export const POSITIONS: Position[] = Object.values(POSITION_CONFIG).map(p => p.display);

export const POSITION_OPTIONS = [
  { value: "none", label: "None" },
  ...Object.values(POSITION_CONFIG)
    .filter(p => !['utility', 'pitcher', 'catcher', 'firstBase'].includes(p.key))
    .map(p => ({ value: p.display, label: p.display })),
  { value: "Left Center", label: "Left Center" },
  { value: "Right Center", label: "Right Center" },
  { value: POSITION_CONFIG.utility.display, label: POSITION_CONFIG.utility.display },
];

// ===== ERROR HANDLING (Fix #2) =====
export class LineupGenerationError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly context: {
      position?: string;
      inning?: number;
      availablePlayers?: string[];
      constraints?: any;
    }
  ) {
    super(message);
    this.name = 'LineupGenerationError';
  }
}

export class PositionConstraintError extends LineupGenerationError {
  constructor(position: string, availablePlayers: string[], constraints: any) {
    super(
      `Cannot satisfy position constraints for ${position}`,
      'POSITION_CONSTRAINT_VIOLATION',
      { position, availablePlayers, constraints }
    );
  }
}

export class InsufficientPlayersError extends LineupGenerationError {
  constructor(required: number, available: number) {
    super(
      `Not enough players to create a lineup. Need at least ${required} players, but only ${available} available.`,
      'INSUFFICIENT_PLAYERS',
      { availablePlayers: [] }
    );
  }
}

// ===== SEEDED RANDOM NUMBER GENERATOR (Fix #4) =====
export class SeededRandom {
  private seed: number;
  
  constructor(seed: number = Date.now()) {
    this.seed = seed;
  }
  
  next(): number {
    this.seed = (this.seed * 1664525 + 1013904223) % (2**32);
    return this.seed / (2**32);
  }
  
  nextInt(max: number): number {
    return Math.floor(this.next() * max);
  }
  
  shuffle<T>(array: T[]): T[] {
    const result = [...array];
    for (let i = result.length - 1; i > 0; i--) {
      const j = this.nextInt(i + 1);
      [result[i], result[j]] = [result[j], result[i]];
    }
    return result;
  }
}

// ===== PERFORMANCE OPTIMIZATION CACHE (Fix #3) =====
export class PlayerEligibilityCache {
  private cache = new Map<string, Set<string>>();
  
  getEligiblePositions(player: Player): Set<string> {
    if (!this.cache.has(player.id)) {
      const eligible = new Set<string>();
      Object.values(POSITION_CONFIG).forEach(pos => {
        const canPlay = canPlayPosition(player, pos.display);
        if (canPlay) {
          eligible.add(pos.key);
        } else {
          const role = player.teamRoles?.[pos.key];
          console.log(`🚫 PlayerEligibilityCache: ${player.name} CANNOT play ${pos.display} (${pos.key}) - role: ${role || 'not set'}`);
        }
      });
      console.log(`✅ PlayerEligibilityCache: ${player.name} eligible for: ${Array.from(eligible).join(', ')}`);
      this.cache.set(player.id, eligible);
    }
    return this.cache.get(player.id)!;
  }
  
  clear() {
    this.cache.clear();
  }
  
  invalidate(playerId: string) {
    this.cache.delete(playerId);
  }
}

// ===== LINEUP RULES INTERFACE (Fix #5) =====
export interface LineupRules {
  limitBenchTime: boolean;
  maxConsecutiveBenchInnings?: number; // Default: 2
  minFieldInningsPerPlayer?: number;   // Default: Math.floor(totalInnings * 0.6)
  minInningsPerPosition?: number;      // Minimum innings at each position
  preferredPositions?: Map<string, string[]>; // Player -> preferred positions
  allowPitcherRotation: boolean;
  allowCatcherRotation: boolean;
  respectPositionLockouts: boolean;
  equalPlayingTime?: boolean;
  rotateLineupEvery?: number;
  rotatePitcherEvery?: number;
  _randomSeed?: number;
  seed?: number;  // Alternative seed field for single-game-lineup

  // ===== COMPETITIVE MODE SETTINGS =====
  competitiveMode?: boolean;           // Enable competitive/play-to-win mode
  competitiveMinPlayingTime?: number;  // Minimum innings each player must play (default: 40% of total)

  // ===== BATCH/CROSS-GAME TRACKING =====
  _crossGameTracking?: {
    playerFieldInnings: {[playerId: string]: number};
    playerBenchInnings: {[playerId: string]: number};
    playerPitchingInnings: {[playerId: string]: number};
    gameNumber: number;
    totalGames: number;
  };
}

// ===== VALIDATION TYPES (Fix #6) =====
export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings?: string[];
}

// ===== CONSTRAINT SOLVER (Fix #7) =====
export class ConstraintSolver {
  constructor(
    private players: Player[],
    private eligibilityCache: PlayerEligibilityCache
  ) {}
  
  findValidAssignment(
    positions: string[],
    constraints: { respectPositionLockouts: boolean },
    assignments: Map<string, string> = new Map(),
    timeout: number = 5000
  ): Map<string, string> | null {
    const startTime = Date.now();

    const backtrack = (
      remainingPositions: string[],
      currentAssignments: Map<string, string>
    ): Map<string, string> | null => {
      // Check timeout
      if (Date.now() - startTime > timeout) {
        console.warn('Constraint solver timeout');
        return null;
      }

      // Base case: all positions assigned
      if (remainingPositions.length === 0) {
        return currentAssignments;
      }

      const [currentPos, ...restPositions] = remainingPositions;
      const positionDisplay = POSITION_CONFIG[currentPos as PositionKey]?.display || currentPos;

      // Get available players (not yet assigned)
      const availablePlayers = this.players.filter(p =>
        !Array.from(currentAssignments.values()).includes(p.name)
      );

      // Sort players by team role priority for this position
      const sortedPlayers = this.sortPlayersByRolePriority(availablePlayers, currentPos);

      // Try each available player for this position (prioritized by role)
      for (const player of sortedPlayers) {
        // Convert position to key format for eligibility check
        const positionKey = this.getPositionKey(currentPos);
        const eligiblePositions = this.eligibilityCache.getEligiblePositions(player);
        const isEligible = eligiblePositions.has(positionKey);
        
        if (!isEligible && constraints.respectPositionLockouts) {
          console.log(`🚫 ConstraintSolver: Skipping ${player.name} for ${currentPos} - NOT ELIGIBLE`);
        }
        
        // CRITICAL FIX: Always check team role eligibility to prevent impossible assignments
        if (isEligible) {
          // Make assignment
          currentAssignments.set(currentPos, player.name);

          // Recurse
          const result = backtrack(restPositions, currentAssignments);
          if (result) {
            return result;
          }

          // Backtrack
          currentAssignments.delete(currentPos);
        }
      }

      return null;
    };

    return backtrack(positions, assignments);
  }

  // Enhanced method for fair assignment that randomizes player order
  findFairValidAssignment(
    positions: string[],
    constraints: { respectPositionLockouts: boolean },
    assignments: Map<string, string> = new Map(),
    timeout: number = 5000
  ): Map<string, string> | null {
    const startTime = Date.now();

    const backtrack = (
      remainingPositions: string[],
      currentAssignments: Map<string, string>
    ): Map<string, string> | null => {
      // Check timeout
      if (Date.now() - startTime > timeout) {
        console.warn('Constraint solver timeout');
        return null;
      }

      // Base case: all positions assigned
      if (remainingPositions.length === 0) {
        return currentAssignments;
      }

      const [currentPos, ...restPositions] = remainingPositions;

      // Get available players (not yet assigned)
      const availablePlayers = this.players.filter(p =>
        !Array.from(currentAssignments.values()).includes(p.name)
      );

      // FAIRNESS IMPROVEMENT: Use role-based priority but with some randomization
      // This respects team roles while still providing variety
      const sortedPlayers = this.sortPlayersByRolePriority(availablePlayers, currentPos);

      // Try each available player for this position (role-prioritized)
      for (const player of sortedPlayers) {
        // Convert position to key format for eligibility check
        const positionKey = this.getPositionKey(currentPos);
        // CRITICAL FIX: Always check team role eligibility to prevent impossible assignments
        if (this.eligibilityCache.getEligiblePositions(player).has(positionKey)) {

          // Make assignment
          currentAssignments.set(currentPos, player.name);

          // Recurse
          const result = backtrack(restPositions, currentAssignments);
          if (result) {
            return result;
          }

          // Backtrack
          currentAssignments.delete(currentPos);
        }
      }

      return null;
    };

    return backtrack(positions, assignments);
  }

  findValidAssignmentWithPreferences(
    positions: string[],
    constraints: { respectPositionLockouts: boolean; considerPreferences?: boolean },
    assignments: Map<string, string> = new Map(),
    timeout: number = 5000
  ): Map<string, string> | null {
    if (!constraints.considerPreferences) {
      return this.findValidAssignment(positions, constraints, assignments, timeout);
    }

    const startTime = Date.now();

    const backtrackWithPreferences = (
      remainingPositions: string[],
      currentAssignments: Map<string, string>
    ): Map<string, string> | null => {
      // Check timeout
      if (Date.now() - startTime > timeout) {
        console.warn('Constraint solver timeout');
        return null;
      }

      // Base case: all positions assigned
      if (remainingPositions.length === 0) {
        return currentAssignments;
      }

      const [currentPos, ...restPositions] = remainingPositions;

      // Get available players (not yet assigned)
      const availablePlayers = this.players.filter(p =>
        !Array.from(currentAssignments.values()).includes(p.name)
      );

      // Filter eligible players for this position
      const eligiblePlayers = availablePlayers.filter(player => {
        // Convert position to key format for eligibility check
        const positionKey = this.getPositionKey(currentPos);
        // ALWAYS check if player has 'avoid' role, regardless of respectPositionLockouts
        const role = player.teamRoles?.[positionKey];
        if (role === 'avoid' || role === 'never') {
          return false; // Never allow players in avoided positions
        }
        
        return !constraints.respectPositionLockouts ||
               this.eligibilityCache.getEligiblePositions(player).has(positionKey);
      });

      // Sort players by preference for this position
      const sortedPlayers = this.sortPlayersByPreference(eligiblePlayers, currentPos);

      // Try each eligible player for this position (preference order)
      for (const player of sortedPlayers) {
        // Make assignment
        currentAssignments.set(currentPos, player.name);

        // Recurse
        const result = backtrackWithPreferences(restPositions, currentAssignments);
        if (result) {
          return result;
        }

        // Backtrack
        currentAssignments.delete(currentPos);
      }

      return null;
    };

    return backtrackWithPreferences(positions, assignments);
  }

  private getPositionKey(position: string): string {
    // Map both display names and keys to preference keys
    const positionKeyMap: { [key: string]: string } = {
      // Position keys (lowercase)
      'pitcher': 'pitcher',
      'catcher': 'catcher',
      'firstBase': 'firstBase',
      'secondBase': 'secondBase',
      'thirdBase': 'thirdBase',
      'shortstop': 'shortstop',
      'leftField': 'leftField',
      'centerField': 'centerField',
      'rightField': 'rightField',
      'leftCenter': 'leftCenter',
      'rightCenter': 'rightCenter',
      // Display names (title case)
      'Pitcher': 'pitcher',
      'Catcher': 'catcher',
      'First Base': 'firstBase',
      'Second Base': 'secondBase',
      'Third Base': 'thirdBase',
      'Shortstop': 'shortstop',
      'Left Field': 'leftField',
      'Center Field': 'centerField',
      'Right Field': 'rightField',
      'Left Center': 'leftCenter',
      'Right Center': 'rightCenter'
    };

    return positionKeyMap[position] || position;
  }

  private sortPlayersByRolePriority(players: Player[], position: string): Player[] {
    const posKey = this.getPositionKey(position);
    
    return [...players].sort((a, b) => {
      // Get team roles for this position
      const roleA = a.teamRoles?.[posKey] || 'unset';
      const roleB = b.teamRoles?.[posKey] || 'unset';
      
      // Role priority order: go-to (primary) > capable (in the mix) > fill-in (emergency) > unset > avoid (never)
      const rolePriority = {
        'go-to': 0,      // Primary
        'capable': 1,    // In the Mix  
        'fill-in': 2,    // Emergency
        'unset': 3,      // No role set
        'avoid': 100     // Never - very low priority but still allow for emergency
      };
      
      const priorityA = rolePriority[roleA] ?? 3;
      const priorityB = rolePriority[roleB] ?? 3;
      
      // Only use role priority as a tie-breaker with some randomization
      // This prevents role-based selection from being too aggressive
      const priorityDiff = priorityA - priorityB;
      if (Math.abs(priorityDiff) > 2) { // Only prioritize if there's a significant difference
        return priorityDiff;
      }
      
      // For similar priorities, add fairness consideration and randomization
      return Math.random() - 0.5;
    });
  }

  private sortPlayersByPreference(players: Player[], position: string): Player[] {
    return [...players].sort((a, b) => {
      const posKey = this.getPositionKey(position);
      const prefA = this.normalizePreference(a.positionPreferences?.[posKey]);
      const prefB = this.normalizePreference(b.positionPreferences?.[posKey]);

      // First sort by preference level: preferred > secondary > neutral > avoid
      const preferenceOrder = { 'preferred': 0, 'secondary': 1, 'neutral': 2, 'avoid': 3 };
      const levelDiff = preferenceOrder[prefA.level] - preferenceOrder[prefB.level];

      if (levelDiff !== 0) {
        return levelDiff;
      }

      // If same preference level, sort by rank (lower rank number = higher priority)
      // Players without rank come after those with rank
      if (prefA.rank && prefB.rank) {
        return prefA.rank - prefB.rank;
      } else if (prefA.rank && !prefB.rank) {
        return -1; // A has rank, B doesn't - A comes first
      } else if (!prefA.rank && prefB.rank) {
        return 1; // B has rank, A doesn't - B comes first
      }

      return 0; // Both have same level and no rank
    });
  }

  // Helper method to normalize preference values (handles both old and new formats)
  private normalizePreference(pref: any): { level: string; rank?: number } {
    if (!pref) return { level: 'neutral' };
    if (typeof pref === 'string') return { level: pref };
    if (typeof pref === 'object' && pref.level) {
      return { level: pref.level, rank: pref.rank };
    }
    return { level: 'neutral' };
  }

  // ===== COMPETITIVE MODE FUNCTIONS =====

  // Calculate competitive score for a player at a specific position
  private calculateCompetitiveScore(player: Player, position: string, rules: LineupRules): number {
    const posKey = this.getPositionKey(position);

    // Check if player can actually play this position first
    // Convert position to key format for eligibility check
    const positionKey = this.getPositionKey(position);
    if (rules.respectPositionLockouts && !this.eligibilityCache.getEligiblePositions(player).has(positionKey)) {
      return -1000; // Very low score for ineligible positions
    }

    // PRIORITY 1: Team Roles (Primary > In the Mix > Emergency > Unset > Avoid, Never = -1000)
    if (player.teamRoles && player.teamRoles[posKey]) {
      const teamRole = player.teamRoles[posKey];
      let roleScore = 0;
      
      switch (teamRole) {
        case 'go-to':       roleScore = 1000; break;  // Primary - Highest priority
        case 'capable':     roleScore = 500;  break;  // In the Mix - Good option
        case 'fill-in':     roleScore = 200;  break;  // Emergency - Last resort but acceptable
        case 'avoid':       return -1000;            // Never use
        default:            roleScore = 100;          // Unset/neutral
      }
      
      // Add small bonus from ratings if available
      if (player.positionRatings && player.positionRatings[posKey] !== undefined) {
        roleScore += (player.positionRatings[posKey] - 3) * 10; // -20 to +20 bonus
      }
      
      console.log(`🏆 COMPETITIVE SCORE: ${player.name} for ${position} = ${roleScore} (role: ${teamRole})`);
      return roleScore;
    }

    // FALLBACK: Use rating/preference system if no team roles set
    if (player.positionRatings && player.positionRatings[posKey] !== undefined) {
      return this.calculateRatingScore(player, position, rules);
    } else if (player.positionPreferences) {
      return this.calculateLegacyPreferenceScore(player, position, rules);
    }

    // Default neutral score - larger randomization for players with no ratings
    return 40 + Math.random() * 20; // Range: 40-60 for variety
  }

  // New rating-based scoring system (1-5 scale)
  private calculateRatingScore(player: Player, position: string, rules: LineupRules): number {
    const posKey = this.getPositionKey(position);
    const rating = player.positionRatings?.[posKey];

    if (rating === undefined || rating === null) return 50; // Neutral score for no rating

    // Convert 1-5 rating to score with much wider spread for better differentiation
    // 1=5, 2=25, 3=50, 4=120, 5=200 (exponential scaling for dramatic differentiation)
    const ratingScores = { 1: 5, 2: 25, 3: 50, 4: 120, 5: 200 };
    let score = ratingScores[rating] || 50;

    // Enhanced star player bonus for key positions
    const keyPositions = ['pitcher', 'catcher', 'shortstop']; // Fixed key positions
    const positionKey = this.getPositionKey(position);
    if (player.isStarPlayer && keyPositions.includes(positionKey)) {
      score += 80; // Massive bonus for star players in key positions (doubled)
    } else if (player.isStarPlayer) {
      score += 30; // Doubled bonus for star players in other positions
    }

    // Enhanced elite rating bonus for any position
    if (rating === 5) {
      score += 50; // Elite players get much stronger consideration (doubled)
    }
    
    // Enhanced good rating bonus
    if (rating === 4) {
      score += 20; // Good players get significant bonus
    }
    
    // Poor rating penalty
    if (rating === 1) {
      score -= 30; // Stronger penalty for poor players
    }

    // Reduced random factor to preserve rating advantages
    score += Math.random() * 5; // Reduced from 15 to 5 to maintain rating differentiation

    return score;
  }

  // Legacy preference-based scoring (for backward compatibility)
  private calculateLegacyPreferenceScore(player: Player, position: string, rules: LineupRules): number {
    const posKey = this.getPositionKey(position);
    const preference = this.normalizePreference(player.positionPreferences?.[posKey]);

    let score = 0;

    // Base score from preference level
    switch (preference.level) {
      case 'preferred':
        score = 100;
        break;
      case 'secondary':
        score = 70;
        break;
      case 'neutral':
        score = 50;
        break;
      case 'avoid':
        score = 10;
        break;
    }

    // Bonus for ranking within preference level (higher rank = higher score)
    if (preference.rank) {
      // Rank 1 gets +20 bonus, rank 2 gets +15, etc.
      score += Math.max(0, 25 - (preference.rank * 5));
    }

    // Extra bonus for key positions in competitive mode
    const keyPositions = ['pitcher', 'catcher', 'shortstop']; // Fixed key positions
    if (keyPositions.includes(position) && preference.level === 'preferred') {
      score += 30; // Extra bonus for preferred players in key positions
    }

    return score;
  }

  // Sort players by competitive score for a position
  private sortPlayersByCompetitiveScore(players: Player[], position: string, rules: LineupRules): Player[] {
    return [...players].sort((a, b) => {
      const scoreA = this.calculateCompetitiveScore(a, position, rules);
      const scoreB = this.calculateCompetitiveScore(b, position, rules);
      return scoreB - scoreA; // Higher score first
    });
  }

  // Enhanced assignment method for competitive mode
  findCompetitiveAssignment(
    positions: string[],
    constraints: { respectPositionLockouts: boolean },
    rules: LineupRules,
    playerStats?: Map<string, { fieldInnings: number; benchInnings: number }>,
    assignments: Map<string, string> = new Map(),
    timeout: number = 5000
  ): Map<string, string> | null {
    if (!rules.competitiveMode) {
      return this.findValidAssignmentWithPreferences(positions, { ...constraints, considerPreferences: true }, assignments, timeout);
    }

    console.log(`🏆 COMPETITIVE MODE: Finding optimal assignments for positions:`, positions);

    const startTime = Date.now();
    const keyPositions = ['pitcher', 'catcher', 'shortstop']; // Fixed key positions

    // Enhanced position sorting: prioritize key positions where we have star players
    const sortedPositions = [...positions].sort((a, b) => {
      const aIsKey = keyPositions.includes(a);
      const bIsKey = keyPositions.includes(b);
      
      // First priority: key positions vs non-key positions
      if (aIsKey && !bIsKey) return -1;
      if (!aIsKey && bIsKey) return 1;
      
      // Second priority within key positions: positions with available star players
      if (aIsKey && bIsKey) {
        const availablePlayers = this.players.filter(p => !Array.from(assignments.values()).includes(p.name));
        const aKey = this.getPositionKey(a);
        const bKey = this.getPositionKey(b);
        const aStarCount = availablePlayers.filter(p => p.isStarPlayer && this.eligibilityCache.getEligiblePositions(p).has(aKey)).length;
        const bStarCount = availablePlayers.filter(p => p.isStarPlayer && this.eligibilityCache.getEligiblePositions(p).has(bKey)).length;
        if (aStarCount !== bStarCount) return bStarCount - aStarCount; // More star players first
      }
      
      return 0;
    });

    const backtrackCompetitive = (
      remainingPositions: string[],
      currentAssignments: Map<string, string>
    ): Map<string, string> | null => {
      if (Date.now() - startTime > timeout) {
        console.warn('Competitive constraint solver timeout');
        return null;
      }

      if (remainingPositions.length === 0) {
        return currentAssignments;
      }

      const [currentPos, ...restPositions] = remainingPositions;

      // Get available players (not yet assigned)
      const availablePlayers = this.players.filter(p =>
        !Array.from(currentAssignments.values()).includes(p.name)
      );

      // Filter eligible players for this position
      const eligiblePlayers = availablePlayers.filter(player => {
        // Convert position to key format for eligibility check
        const positionKey = this.getPositionKey(currentPos);
        return !constraints.respectPositionLockouts ||
               this.eligibilityCache.getEligiblePositions(player).has(positionKey);
      });

      // Sort by competitive score for this position with enhanced randomization
      const playersWithScores = eligiblePlayers.map(player => ({
        player,
        score: this.calculateCompetitiveScore(player, currentPos, rules)
      }));
      
      // SMART COMPETITIVE LOGIC: Add positional diversity bonus
      // If this player already has a high-scoring assignment, reduce their score slightly
      // to encourage using other qualified players and avoid position locking
      const adjustedScores = playersWithScores.map(({ player, score }) => {
        let adjustedScore = score;
        
        // Check if player is already assigned to their PRIMARY position
        const currentAssignmentArray = Array.from(currentAssignments.entries());
        const playerCurrentAssignment = currentAssignmentArray.find(([pos, name]) => name === player.name);
        
        if (playerCurrentAssignment && playerStats) {
          const [assignedPos] = playerCurrentAssignment;
          const assignedPosKey = this.getPositionKey(assignedPos);
          const assignedRole = player.teamRoles?.[assignedPosKey];
          
          // If already assigned to PRIMARY position, slightly reduce score for other positions
          // This encourages position diversity while still respecting competitive priorities
          if (assignedRole === 'primary' && score >= 200) {
            adjustedScore = score * 0.8; // 20% reduction to encourage diversity
            console.log(`🔄 DIVERSITY BONUS: ${player.name} already has PRIMARY ${assignedPos}, reducing ${currentPos} score from ${score} to ${adjustedScore}`);
          }
        }
        
        return { player, score: adjustedScore, originalScore: score };
      });
      
      // Debug logging for auto-fill
      if (positions.length <= 9) { // Only log for initial lineup auto-fill
        console.log(`🎯 Player scores for ${currentPos}:`, 
          adjustedScores.reduce((acc, { player, score, originalScore }) => {
            acc[player.name] = { 
              score, 
              originalScore,
              rating: player.positionRatings?.[this.getPositionKey(currentPos)], 
              teamRole: player.teamRoles?.[this.getPositionKey(currentPos)] || 'unset'
            };
            return acc;
          }, {} as Record<string, any>)
        );
      }
      
      // Enhanced sorting that respects rating differences while allowing some variety
      const sortedPlayers = adjustedScores
        .sort((a, b) => {
          const scoreDiff = b.score - a.score;
          
          // Only randomize if scores are extremely close (within 5 points) to preserve rating advantages
          if (Math.abs(scoreDiff) <= 5) {
            return Math.random() - 0.5; // 50/50 chance only for virtually identical scores
          }
          
          // For larger score differences, minimal randomization to maintain competitive integrity
          return scoreDiff + (Math.random() - 0.5) * 2; // Reduced from 10 to 2
        })
        .map(({ player }) => player);

      console.log(`🎯 Competitive assignment for ${currentPos}:`,
        sortedPlayers.slice(0, 3).map(p => ({
          name: p.name,
          score: this.calculateCompetitiveScore(p, currentPos, rules),
          preference: this.normalizePreference(p.positionPreferences?.[this.getPositionKey(currentPos)])
        }))
      );

      // Try each eligible player for this position (competitive order)
      for (const player of sortedPlayers) {
        // Check minimum playing time constraints in competitive mode
        if (playerStats && rules.competitiveMinPlayingTime) {
          const stats = playerStats.get(player.id);
          const totalInnings = stats ? stats.fieldInnings + stats.benchInnings : 0;
          const minRequired = Math.floor(totalInnings * (rules.competitiveMinPlayingTime / 100));

          // Allow some flexibility for star players in key positions
          const isKeyPosition = keyPositions.includes(currentPos);
          const isStarPlayer = this.calculateCompetitiveScore(player, currentPos, rules) >= 180;

          if (stats && stats.fieldInnings < minRequired && !(isKeyPosition && isStarPlayer)) {
            continue; // Skip if player hasn't met minimum playing time
          }
        }

        // Make assignment
        currentAssignments.set(currentPos, player.name);

        // Recurse
        const result = backtrackCompetitive(restPositions, currentAssignments);
        if (result) {
          return result;
        }

        // Backtrack
        currentAssignments.delete(currentPos);
      }

      return null;
    };

    const result = backtrackCompetitive(sortedPositions, assignments);

    if (result) {
      console.log(`✅ Competitive assignment successful:`, Object.fromEntries(result));
    } else {
      console.warn(`⚠️ Competitive assignment failed, falling back to preference-based assignment`);
      return this.findValidAssignmentWithPreferences(positions, { ...constraints, considerPreferences: true }, assignments, timeout);
    }

    return result;
  }
}

// ===== LINEUP ROTATOR CLASS (Fix #8) =====
export class LineupRotator {
  private debugEnabled: boolean = true; // Enable/disable debug logging

  constructor(
    private rules: LineupRules,
    private eligibilityCache: PlayerEligibilityCache,
    private random: SeededRandom
  ) {
    // Enable debug logging by default to help diagnose rotation issues
    this.debugEnabled = true;
  }

  // Method to enable/disable debug logging
  setDebugEnabled(enabled: boolean): void {
    this.debugEnabled = enabled;
  }

  private debug(inning: number, message: string, data?: any): void {
    if (this.debugEnabled) {
      console.log(`[ROTATION DEBUG ${inning}] ${message}`, data || '');
    }
  }

  rotateLineup(
    current: InningLineup,
    players: Player[],
    inning: number,
    playerStats?: Map<string, { fieldInnings: number; benchInnings: number }>,
    allInnings?: InningLineup[]
  ): InningLineup {
    this.debug(inning, '🔄 Starting rotation algorithm');
    this.debug(inning, 'Current lineup positions', current.positions);
    this.debug(inning, 'Available players', players.map(p => p.name));
    this.debug(inning, 'Player stats', playerStats ? Object.fromEntries(playerStats) : 'No stats provided');

    // Validate input data before creating rotation plan
    if (!current || !current.positions) {
      throw new LineupGenerationError(
        'Invalid current lineup data',
        'INVALID_INPUT_DATA',
        { inning, constraints: ['Current lineup is missing or has no positions'] }
      );
    }

    if (!players || players.length < 8) {
      throw new LineupGenerationError(
        'Insufficient players for rotation',
        'INSUFFICIENT_PLAYERS',
        { inning, constraints: [`Need at least 8 players, got ${players?.length || 0}`] }
      );
    }

    try {
      const rotationPlan = this.createRotationPlan(current, players, inning, playerStats, allInnings);
      this.debug(inning, '📋 Rotation plan created', {
        fieldAssignments: Object.fromEntries(rotationPlan.fieldAssignments),
        benchAssignments: rotationPlan.benchAssignments,
        noRotation: rotationPlan.noRotation
      });

      const validation = this.validatePlan(rotationPlan, players);

      if (!validation.valid) {
        this.debug(inning, '❌ Rotation plan validation failed', validation.errors);
        this.debug(inning, '🔍 Rotation plan details:', {
          fieldAssignments: Object.fromEntries(rotationPlan.fieldAssignments),
          benchAssignments: rotationPlan.benchAssignments,
          noRotation: rotationPlan.noRotation
        });
        this.debug(inning, '🔍 Current lineup:', current);
        this.debug(inning, '🔍 Available players:', players.map(p => ({ name: p.name, id: p.id })));

        throw new LineupGenerationError(
          'Invalid rotation plan',
          'ROTATION_PLAN_INVALID',
          { inning, constraints: validation.errors }
        );
      }

      this.debug(inning, '✅ Rotation plan validated successfully');
      const result = this.executePlan(rotationPlan, inning);
      this.debug(inning, '🎯 Final rotation result', result.positions);

      return result;
    } catch (error) {
      this.debug(inning, '💥 Error in rotation algorithm', error);
      throw error;
    }
  }
  
  private createRotationPlan(
    current: InningLineup,
    players: Player[],
    inning: number,
    playerStats?: Map<string, { fieldInnings: number; benchInnings: number }>,
    allInnings?: InningLineup[]
  ): RotationPlan {
    this.debug(inning, '📋 Creating rotation plan');

    const plan: RotationPlan = {
      moves: [],
      benchAssignments: [],
      fieldAssignments: new Map()
    };

    // Check if we should rotate
    const shouldRotate = this.shouldRotateThisInning(inning);
    const rotateLineupEvery = this.rules.rotateLineupEvery || 1;
    this.debug(inning, '🔄 Rotation check', {
      shouldRotateThisInning: shouldRotate,
      rotateLineupEvery: rotateLineupEvery,
      calculation: `(${inning} - 1) % ${rotateLineupEvery} === 0 = ${(inning - 1) % rotateLineupEvery === 0}`
    });

    // CRITICAL FIX: Always ensure all positions are filled, regardless of rotation
    const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase',
                           'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];

    // First, copy existing valid assignments
    const currentAssignments = new Map<string, string>();
    fieldPositions.forEach(pos => {
      const player = (current.positions as any)[pos];
      if (player && typeof player === 'string' && player.trim()) {
        currentAssignments.set(pos, player);
      }
    });

    this.debug(inning, '📋 Current valid assignments', {
      currentAssignments: Object.fromEntries(currentAssignments),
      totalAssigned: currentAssignments.size,
      requiredPositions: fieldPositions.length
    });

    // CRITICAL FIX: Force rotation if players have been on bench too long
    const maxBenchStreak = this.rules.maxConsecutiveBenchInnings || 2;
    let benchStreaks = new Map<string, number>();
    if (allInnings && allInnings.length > 0) {
      benchStreaks = this.getBenchStreaks(allInnings, inning - 1);
    }

    const playersNeedingRotation = current.positions.bench.filter(playerName => {
      const streak = benchStreaks.get(playerName) || 0;
      return streak >= maxBenchStreak;
    });

    const forceRotation = shouldRotate || playersNeedingRotation.length > 0;

    this.debug(inning, '🚨 Forced rotation check', {
      shouldRotate,
      playersNeedingRotation,
      maxBenchStreak,
      forceRotation,
      benchStreaks: Object.fromEntries(benchStreaks)
    });

    // CRITICAL FIX: Remove early return that was preventing rotation
    // Only skip rotation if we truly don't need it (not scheduled AND no bench time issues)
    if (!shouldRotate && playersNeedingRotation.length === 0 && currentAssignments.size === fieldPositions.length) {
      this.debug(inning, '⏸️ No rotation needed and all positions filled, keeping current lineup');

      // Copy all current assignments
      for (const [pos, player] of currentAssignments) {
        plan.fieldAssignments.set(pos, player);
      }
      plan.benchAssignments = [...current.positions.bench];
      plan.noRotation = true;
      plan.currentPositions = current.positions;

      this.debug(inning, '📋 No rotation plan created with current assignments', {
        fieldAssignments: Object.fromEntries(plan.fieldAssignments),
        benchAssignments: plan.benchAssignments,
        noRotation: true
      });

      return plan;
    }

    // If we reach here, we need to either rotate OR fill missing positions
    this.debug(inning, '🔄 Need to rotate or fill missing positions', {
      shouldRotate,
      missingPositions: fieldPositions.length - currentAssignments.size,
      currentAssignments: Object.fromEntries(currentAssignments)
    });

    // Handle pitcher rotation
    const shouldRotatePitcher = this.shouldRotatePitcher(inning);
    const rotatePitcherEvery = this.rules.rotatePitcherEvery;
    this.debug(inning, '⚾ Pitcher rotation check', {
      shouldRotatePitcher: shouldRotatePitcher,
      allowPitcherRotation: this.rules.allowPitcherRotation,
      rotatePitcherEvery: rotatePitcherEvery,
      currentPitcher: current.positions.pitcher,
      currentBench: current.positions.bench
    });

    if (shouldRotatePitcher) {
      const currentPitcher = current.positions.pitcher;
      const newPitcher = this.findBestPitcher(current, players, playerStats);

      this.debug(inning, '🎯 Pitcher rotation result', {
        currentPitcher: currentPitcher,
        newPitcher: newPitcher,
        availableBenchPlayers: current.positions.bench,
        eligiblePitchers: current.positions.bench.filter(playerName => {
          const player = players.find(p => p.name === playerName);
          return player && this.eligibilityCache.getEligiblePositions(player).has('pitcher');
        })
      });

      if (newPitcher && newPitcher !== currentPitcher) {
        // Assign new pitcher
        plan.fieldAssignments.set('pitcher', newPitcher);

        // Move current pitcher to bench or another field position
        // For now, we'll let the constraint solver handle the current pitcher's new position
        this.debug(inning, '✅ Pitcher rotation executed', {
          oldPitcher: currentPitcher,
          newPitcher: newPitcher,
          note: 'Current pitcher will be reassigned by constraint solver'
        });
      } else if (!newPitcher) {
        this.debug(inning, '⚠️ No eligible pitcher found on bench, keeping current pitcher');
        plan.fieldAssignments.set('pitcher', currentPitcher);
      } else {
        this.debug(inning, '⚠️ New pitcher is same as current pitcher, no rotation needed');
        plan.fieldAssignments.set('pitcher', currentPitcher);
      }
    } else {
      this.debug(inning, '⏸️ No pitcher rotation scheduled, keeping current pitcher', {
        pitcher: current.positions.pitcher
      });
      // Keep current pitcher
      plan.fieldAssignments.set('pitcher', current.positions.pitcher);
    }

    // Log player stats for rotation decisions
    if (playerStats) {
      this.debug(inning, '📊 Player stats for rotation decisions', {
        playerStats: Array.from(playerStats.entries()).map(([name, stats]) => ({
          name,
          fieldInnings: stats.fieldInnings,
          benchInnings: stats.benchInnings,
          benchStreak: this.calculateBenchStreak(name, current, playerStats)
        }))
      });
    }

    // Plan other rotations
    this.planFieldRotations(plan, current, players, playerStats, allInnings, shouldRotate);

    this.debug(inning, '📋 Rotation plan completed', {
      fieldAssignments: Object.fromEntries(plan.fieldAssignments),
      benchAssignments: plan.benchAssignments,
      totalMoves: plan.moves.length
    });

    // CRITICAL: Ensure all positions are assigned before returning
    const requiredPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase',
                              'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
    const missingPositions = requiredPositions.filter(pos => !plan.fieldAssignments.has(pos));

    if (missingPositions.length > 0) {
      this.debug(inning, `❌ EMERGENCY: Missing positions after planFieldRotations: ${missingPositions.join(', ')}`);

      // Emergency assignment: assign any available players to missing positions
      const assignedPlayers = new Set(plan.fieldAssignments.values());
      const availablePlayers = players.filter(p => !assignedPlayers.has(p.name));

      this.debug(inning, '🚨 Emergency assignment', {
        missingPositions,
        availablePlayers: availablePlayers.map(p => p.name),
        currentAssignments: Object.fromEntries(plan.fieldAssignments)
      });

      for (let i = 0; i < missingPositions.length; i++) {
        const position = missingPositions[i];
        const positionDisplay = POSITION_CONFIG[position as keyof typeof POSITION_CONFIG]?.display || position;
        
        // Find first available player who can play this position
        const suitablePlayer = availablePlayers.find(player => 
          canPlayPosition(player, positionDisplay)
        );
        
        if (suitablePlayer) {
          plan.fieldAssignments.set(position, suitablePlayer.name);
          this.debug(inning, `🚨 Emergency assigned ${suitablePlayer.name} to ${position} (respects restrictions)`);
          // Remove from available list
          const playerIndex = availablePlayers.indexOf(suitablePlayer);
          if (playerIndex > -1) availablePlayers.splice(playerIndex, 1);
        } else {
          // NO PLAYER CAN PLAY THIS POSITION WITHOUT VIOLATING RESTRICTIONS
          this.debug(inning, `❌ IMPOSSIBLE: No player available for ${position} without violating restrictions`);
          this.debug(inning, `❌ Available players: ${availablePlayers.map(p => p.name).join(', ')}`);
          
          // Log which players have restrictions for this position
          const restrictedPlayers = players.filter(p => {
            const role = p.teamRoles?.[position as keyof typeof p.teamRoles];
            return role === 'avoid' || role === 'never';
          });
          this.debug(inning, `❌ Players with restrictions for ${position}: ${restrictedPlayers.map(p => p.name).join(', ')}`);
          
          // This is a critical error - we cannot generate a valid lineup
          throw new LineupGenerationError(
            `Cannot assign position ${position} without violating restrictions`,
            'POSITION_RESTRICTIONS_IMPOSSIBLE',
            {
              inning,
              position,
              availablePlayers: availablePlayers.map(p => p.name),
              restrictedPlayers: restrictedPlayers.map(p => p.name)
            }
          );
        }
      }

      // Update bench assignments
      const finalAssignedPlayers = new Set(plan.fieldAssignments.values());
      plan.benchAssignments = players
        .map(p => p.name)
        .filter(name => !finalAssignedPlayers.has(name));

      this.debug(inning, '🚨 Emergency assignment completed', {
        finalFieldAssignments: Object.fromEntries(plan.fieldAssignments),
        finalBenchAssignments: plan.benchAssignments
      });
    }

    return plan;
  }

  private calculateBenchStreak(playerName: string, current: InningLineup, playerStats?: Map<string, { fieldInnings: number; benchInnings: number }>): number {
    // Simple bench streak calculation - in a real implementation this would track consecutive bench innings
    return current.positions.bench.includes(playerName) ? 1 : 0;
  }

  private getBenchStreaks(allInnings: InningLineup[], upToInning: number): Map<string, number> {
    const streaks = new Map<string, number>();

    if (allInnings.length === 0 || upToInning <= 0) {
      return streaks;
    }

    // Initialize all players to 0
    const firstInning = allInnings[0];
    if (firstInning) {
      // Get all players from first inning
      const allPlayers = new Set<string>();
      Object.values(firstInning.positions).forEach(player => {
        if (typeof player === 'string' && player) {
          allPlayers.add(player);
        } else if (Array.isArray(player)) {
          player.forEach(p => allPlayers.add(p));
        }
      });
      allPlayers.forEach(player => streaks.set(player, 0));
    }

    // Count consecutive bench innings going backwards from upToInning
    for (let i = Math.min(upToInning - 1, allInnings.length - 1); i >= 0; i--) {
      const inning = allInnings[i];
      if (!inning) break;

      // First, reset streak for players who played in the field
      Object.entries(inning.positions).forEach(([position, player]) => {
        if (position !== 'bench' && typeof player === 'string' && player) {
          streaks.set(player, 0);
        }
      });

      // Then increment streak for bench players
      inning.positions.bench.forEach(player => {
        if (player) {
          const currentStreak = streaks.get(player) || 0;
          streaks.set(player, currentStreak + 1);
        }
      });
    }

    return streaks;
  }

  private getPositionKey(position: string): string {
    // Map both display names and keys to preference keys
    const positionKeyMap: { [key: string]: string } = {
      // Position keys (lowercase)
      'pitcher': 'pitcher',
      'catcher': 'catcher',
      'firstBase': 'firstBase',
      'secondBase': 'secondBase',
      'thirdBase': 'thirdBase',
      'shortstop': 'shortstop',
      'leftField': 'leftField',
      'centerField': 'centerField',
      'rightField': 'rightField',
      'leftCenter': 'leftCenter',
      'rightCenter': 'rightCenter',
      // Display names (title case)
      'Pitcher': 'pitcher',
      'Catcher': 'catcher',
      'First Base': 'firstBase',
      'Second Base': 'secondBase',
      'Third Base': 'thirdBase',
      'Shortstop': 'shortstop',
      'Left Field': 'leftField',
      'Center Field': 'centerField',
      'Right Field': 'rightField',
      'Left Center': 'leftCenter',
      'Right Center': 'rightCenter'
    };

    return positionKeyMap[position] || position;
  }

  private sortPositionsByPreference(positions: string[], player: Player): string[] {
    if (!player.positionPreferences) {
      return positions;
    }

    return [...positions].sort((a, b) => {
      const prefA = this.normalizePreference(player.positionPreferences?.[this.getPositionKey(a)]);
      const prefB = this.normalizePreference(player.positionPreferences?.[this.getPositionKey(b)]);

      // First sort by preference level: preferred > secondary > neutral > avoid
      const preferenceOrder = { 'preferred': 0, 'secondary': 1, 'neutral': 2, 'avoid': 3 };
      const levelDiff = preferenceOrder[prefA.level] - preferenceOrder[prefB.level];

      if (levelDiff !== 0) {
        return levelDiff;
      }

      // If same preference level, sort by rank (lower rank number = higher priority)
      if (prefA.rank && prefB.rank) {
        return prefA.rank - prefB.rank;
      } else if (prefA.rank && !prefB.rank) {
        return -1;
      } else if (!prefA.rank && prefB.rank) {
        return 1;
      }

      return 0;
    });
  }

  // Helper method to normalize preference values (same as ConstraintSolver)
  private normalizePreference(pref: any): { level: string; rank?: number } {
    if (!pref) return { level: 'neutral' };
    if (typeof pref === 'string') return { level: pref };
    if (typeof pref === 'object' && pref.level) {
      return { level: pref.level, rank: pref.rank };
    }
    return { level: 'neutral' };
  }

  // ===== COMPETITIVE MODE FUNCTIONS =====

  // Calculate competitive score for a player at a specific position (same as ConstraintSolver)
  private calculateCompetitiveScore(player: Player, position: string): number {
    const posKey = this.getPositionKey(position);

    // Use new rating system if available, fall back to legacy preferences
    if (player.positionRatings && player.positionRatings[posKey]) {
      return this.calculateRatingScore(player, position);
    } else if (player.positionPreferences) {
      return this.calculateLegacyPreferenceScore(player, position);
    }

    // Default neutral score
    return 50;
  }

  // New rating-based scoring system (1-5 scale) for RotationEngine
  private calculateRatingScore(player: Player, position: string): number {
    const posKey = this.getPositionKey(position);
    const rating = player.positionRatings?.[posKey];

    if (!rating) return 50; // Neutral score for no rating

    // Convert 1-5 rating to score (20 points per rating level)
    let score = rating * 20; // 1=20, 2=40, 3=60, 4=80, 5=100

    // Star player bonus for key positions
    const keyPositions = ['pitcher', 'catcher', 'shortstop']; // Fixed key positions
    if (player.isStarPlayer && keyPositions.includes(position)) {
      score += 30; // Extra bonus for star players in key positions
    }

    // Elite rating bonus for any position
    if (rating === 5) {
      score += 20; // Elite players get extra consideration
    }

    return score;
  }

  // Legacy preference-based scoring (for backward compatibility) for RotationEngine
  private calculateLegacyPreferenceScore(player: Player, position: string): number {
    const posKey = this.getPositionKey(position);
    const preference = this.normalizePreference(player.positionPreferences?.[posKey]);

    let score = 0;

    // Base score from preference level
    switch (preference.level) {
      case 'preferred':
        score = 100;
        break;
      case 'secondary':
        score = 70;
        break;
      case 'neutral':
        score = 50;
        break;
      case 'avoid':
        score = 10;
        break;
    }

    // Bonus for ranking within preference level
    if (preference.rank) {
      score += Math.max(0, 25 - (preference.rank * 5));
    }

    // Extra bonus for key positions in competitive mode
    const keyPositions = ['pitcher', 'catcher', 'shortstop']; // Fixed key positions
    if (keyPositions.includes(position) && preference.level === 'preferred') {
      score += 30;
    }

    return score;
  }

  // Check if a player should be considered a "star" player for a position
  private isStarPlayer(player: Player, position: string): boolean {
    // Use explicit star player designation if available
    if (player.isStarPlayer !== undefined) {
      return player.isStarPlayer && this.calculateCompetitiveScore(player, position) >= 100; // Star + good rating
    }

    // Fall back to score-based determination for legacy support (use higher threshold due to new scoring)
    return this.calculateCompetitiveScore(player, position) >= 180;
  }

  // Determine if a position change should be delayed for competitive reasons
  private shouldDelayRotation(
    player: Player,
    currentPosition: string,
    playerStats: Map<string, { fieldInnings: number; benchInnings: number }>,
    inning: number
  ): boolean {
    if (!this.rules.competitiveMode) return false;

    const keyPositions = ['pitcher', 'catcher', 'shortstop']; // Fixed key positions
    const isKeyPosition = keyPositions.includes(currentPosition);
    const isStarAtPosition = this.isStarPlayer(player, currentPosition);
    const rotationDelay = 2; // Fixed delay for star player dominance

    if (!isKeyPosition || !isStarAtPosition) return false;

    // Enhanced star player retention logic
    const baseRotationInterval = this.rules.rotateLineupEvery || 3;
    const adjustedInterval = baseRotationInterval + rotationDelay;
    
    // Star players stay in key positions for longer periods
    const positionBasedDelay = (inning - 1) % adjustedInterval < baseRotationInterval;
    
    // Additional retention for extremely high-rated players (5-rating)
    const posKey = this.getPositionKey(currentPosition);
    const rating = player.positionRatings?.[posKey];
    const isElitePlayer = rating === 5;
    const eliteBonus = isElitePlayer ? 1 : 0; // Elite players get extra inning
    
    const shouldStay = positionBasedDelay || ((inning - 1) % adjustedInterval < (baseRotationInterval + eliteBonus));

    if (shouldStay) {
      console.log(`🏆 Enhanced competitive delay: Keeping ${isElitePlayer ? 'elite ' : ''}star player ${player.name} at ${currentPosition} for inning ${inning} (rating: ${rating}, delay: ${rotationDelay + eliteBonus})`);
    }

    return shouldStay;
  }

  private validatePlan(plan: RotationPlan, players: Player[]): ValidationResult {
    const errors: string[] = [];

    // Only check the 9 required baseball field positions
    const requiredPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase',
                              'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
    const assignedPositions = new Set(plan.fieldAssignments.keys());

    console.log('🔍 VALIDATING ROTATION PLAN:');
    console.log('  Required positions:', requiredPositions);
    console.log('  Assigned positions:', Array.from(assignedPositions));
    console.log('  Field assignments:', Object.fromEntries(plan.fieldAssignments));
    console.log('  Bench assignments:', plan.benchAssignments);
    console.log('  Available players:', players.map(p => p.name));

    for (const pos of requiredPositions) {
      if (!assignedPositions.has(pos)) {
        errors.push(`Position ${pos} not assigned`);
        console.log(`❌ Missing position: ${pos}`);
      }
    }

    // Check no duplicate assignments
    const assignedPlayers = new Set<string>();
    for (const player of plan.fieldAssignments.values()) {
      if (assignedPlayers.has(player)) {
        errors.push(`Player ${player} assigned to multiple positions`);
        console.log(`❌ Duplicate player assignment: ${player}`);
      }
      assignedPlayers.add(player);
    }

    // Validate that all assigned players exist in the available players list
    const availablePlayerNames = new Set(players.map(p => p.name));
    for (const playerName of assignedPlayers) {
      if (!availablePlayerNames.has(playerName)) {
        errors.push(`Assigned player ${playerName} not found in available players`);
        console.log(`❌ Unknown player: ${playerName}`);
      }
    }

    console.log('🎯 Validation result:', { valid: errors.length === 0, errors });
    return { valid: errors.length === 0, errors };
  }
  
  private executePlan(plan: RotationPlan, inning: number): InningLineup {
    if (plan.noRotation) {
      return { inning, positions: plan.currentPositions! };
    }
    
    const positions: any = {
      pitcher: '',
      catcher: '',
      firstBase: '',
      secondBase: '',
      shortstop: '',
      thirdBase: '',
      leftField: '',
      centerField: '',
      rightField: '',
      bench: []
    };
    
    // Apply field assignments
    for (const [pos, player] of plan.fieldAssignments) {
      positions[pos] = player;
    }
    
    // Apply bench assignments
    positions.bench = plan.benchAssignments;
    
    return { inning, positions };
  }
  
  private shouldRotateThisInning(inning: number): boolean {
    // Never rotate in the first inning
    if (inning === 1) {
      this.debug(inning, '🔄 First inning - no rotation needed');
      return false;
    }
    
    const rotateEvery = this.rules.rotateLineupEvery || 1;
    const standardRotation = (inning - 1) % rotateEvery === 0;

    // ENHANCED FAIRNESS: In equal playing time mode, be much more aggressive about rotation
    if (this.rules.equalPlayingTime) {
      // In fairness mode, rotate EVERY inning after the first to maximize equality
      const fairnessRotation = true; // Always rotate for fairness
      
      this.debug(inning, '⚖️ FAIRNESS MODE: Enhanced rotation for equal playing time', {
        inning,
        rotateEvery,
        standardRotation,
        fairnessRotation,
        reason: 'equalPlayingTime enabled - rotating every inning for maximum fairness'
      });
      
      return fairnessRotation;
    }

    this.debug(inning, '🔄 Standard rotation frequency check', {
      inning,
      rotateEvery,
      calculation: `(${inning} - 1) % ${rotateEvery} = ${(inning - 1) % rotateEvery}`,
      shouldRotate: standardRotation
    });

    return standardRotation;
  }
  
  private shouldRotatePitcher(inning: number): boolean {
    // Never rotate pitcher in the first inning
    if (inning === 1) {
      return false;
    }
    
    // Pitcher rotates on its own schedule, independent of general rotation
    // For rotatePitcherEvery = 1, rotate every inning (2, 3, 4, etc)
    // For rotatePitcherEvery = 2, rotate at innings 3, 5, 7, etc
    // For rotatePitcherEvery = 3, rotate at innings 4, 7, etc
    const shouldRotate = this.rules.allowPitcherRotation &&
      this.rules.rotatePitcherEvery &&
      this.rules.rotatePitcherEvery > 0 &&
      ((inning - 1) % this.rules.rotatePitcherEvery === 0);

    this.debug(inning, '⚾ Pitcher rotation calculation', {
      currentInning: inning,
      rotatePitcherEvery: this.rules.rotatePitcherEvery,
      calculation: `(${inning} - 1) % ${this.rules.rotatePitcherEvery} = ${(inning - 1) % this.rules.rotatePitcherEvery}`,
      shouldRotate: shouldRotate,
      allowPitcherRotation: this.rules.allowPitcherRotation
    });

    return shouldRotate;
  }
  
  private findBestPitcher(
    current: InningLineup,
    players: Player[],
    playerStats?: Map<string, { fieldInnings: number; benchInnings: number }>
  ): string | null {
    const currentPitcher = current.positions.pitcher;
    const benchPlayers = current.positions.bench;

    this.debug(current.inning + 1, '🔍 Finding best pitcher', {
      currentPitcher: currentPitcher,
      benchPlayers: benchPlayers,
      totalBenchPlayers: benchPlayers.length
    });

    const eligiblePitchers = benchPlayers.filter(playerName => {
      const player = players.find(p => p.name === playerName);
      const isEligible = player && this.eligibilityCache.getEligiblePositions(player).has('pitcher');
      this.debug(current.inning + 1, `🎯 Pitcher eligibility check: ${playerName}`, {
        playerFound: !!player,
        canPitch: isEligible,
        restrictions: player?.positionRestrictions
      });
      return isEligible;
    });

    this.debug(current.inning + 1, '⚾ Eligible pitchers found', {
      eligiblePitchers: eligiblePitchers,
      count: eligiblePitchers.length
    });

    if (eligiblePitchers.length === 0) {
      this.debug(current.inning + 1, '❌ No eligible pitchers found on bench');
      return null;
    }

    // Enhanced pitcher selection using pitcher strategies
    const nextInning = current.inning + 1;
    const pitcherCandidates = eligiblePitchers.map(name => {
      const player = players.find(p => p.name === name)!;
      const stats = playerStats?.get(name);
      const fieldInnings = stats?.fieldInnings || 0;
      
      // Calculate strategy-based score
      let strategyScore = 50; // Default score
      
      if (player.pitcherStrategy) {
        const strategy = player.pitcherStrategy;
        
        // Role-based scoring
        switch (strategy.role) {
          case 'starter':
            strategyScore = nextInning <= 3 ? 100 : 60; // Prefer early innings
            break;
          case 'reliever':
            strategyScore = nextInning >= 3 && nextInning <= 5 ? 100 : 70; // Middle innings
            break;
          case 'closer':
            strategyScore = nextInning >= 6 ? 100 : 40; // Late innings
            break;
          case 'any':
            strategyScore = 80; // Good for any inning
            break;
        }
        
        // Preferred innings bonus
        if (strategy.preferredInnings?.includes(nextInning)) {
          strategyScore += 30;
        }
        
        // Priority modifier (1=highest, 5=lowest)
        strategyScore += (6 - strategy.priority) * 10;
        
        // Check inning limits
        if (strategy.maxInningsPerGame && fieldInnings >= strategy.maxInningsPerGame) {
          strategyScore = 10; // Very low score if over limit
        }
      }
      
      // Team role bonus
      const teamRole = player.teamRoles?.pitcher;
      if (teamRole === 'go-to') strategyScore += 25;
      else if (teamRole === 'capable') strategyScore += 10;
      else if (teamRole === 'fill-in') strategyScore -= 10;
      else if (teamRole === 'avoid') strategyScore = 5;
      
      // Fairness consideration - prefer less used pitchers
      const fairnessBonus = Math.max(0, 20 - (fieldInnings * 3));
      strategyScore += fairnessBonus;
      
      return {
        name,
        player,
        strategyScore,
        fieldInnings,
        teamRole: teamRole || 'unset',
        strategy: player.pitcherStrategy
      };
    });

    // Sort by strategy score (highest first)
    pitcherCandidates.sort((a, b) => b.strategyScore - a.strategyScore);

    this.debug(current.inning + 1, '⚾ Pitcher strategy-based selection', {
      inning: nextInning,
      candidates: pitcherCandidates.map(p => ({
        name: p.name,
        score: p.strategyScore,
        role: p.strategy?.role || 'none',
        priority: p.strategy?.priority || 'none',
        teamRole: p.teamRole,
        fieldInnings: p.fieldInnings
      }))
    });

    const selectedPitcher = pitcherCandidates[0].name;
    this.debug(current.inning + 1, '✅ Selected pitcher', {
      pitcher: selectedPitcher,
      score: pitcherCandidates[0].strategyScore,
      role: pitcherCandidates[0].strategy?.role || 'none',
      teamRole: pitcherCandidates[0].teamRole,
      reason: 'Strategy-based selection'
    });

    return selectedPitcher;
  }
  
  private planFieldRotations(
    plan: RotationPlan,
    current: InningLineup,
    players: Player[],
    playerStats?: Map<string, { fieldInnings: number; benchInnings: number }>,
    allInnings?: InningLineup[],
    shouldRotate?: boolean
  ): void {
    const inning = current.inning + 1;
    this.debug(inning, '🔄 Planning field rotations');

    // Complex rotation logic simplified into manageable pieces
    const playerMap = new Map(players.map(p => [p.name, p]));
    const benchPlayers = [...current.positions.bench];
    // Only use the 9 actual field positions that exist in lineups
    const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase',
                           'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];

    // Calculate bench streaks if we have all innings data
    let benchStreaks = new Map<string, number>();
    if (allInnings && allInnings.length > 0) {
      benchStreaks = this.getBenchStreaks(allInnings, inning - 1);
      this.debug(inning, '📊 Current bench streaks', Object.fromEntries(benchStreaks));
    }

    // Check for players who need forced rotation due to excessive bench time
    const maxBenchStreak = this.rules.maxConsecutiveBenchInnings || 2;
    const playersNeedingRotation = benchPlayers.filter(playerName => {
      const streak = benchStreaks.get(playerName) || 0;
      return streak >= maxBenchStreak;
    });

    if (playersNeedingRotation.length > 0) {
      this.debug(inning, '🚨 Players requiring forced rotation due to excessive bench time', {
        players: playersNeedingRotation,
        maxAllowedStreak: maxBenchStreak,
        streaks: playersNeedingRotation.map(name => ({
          name,
          streak: benchStreaks.get(name) || 0
        }))
      });
    }

    this.debug(inning, '📋 Rotation planning data', {
      currentBenchPlayers: benchPlayers,
      fieldPositionsToFill: fieldPositions,
      totalPlayers: players.length,
      respectPositionLockouts: this.rules.respectPositionLockouts,
      pitcherAlreadyAssigned: plan.fieldAssignments.has('pitcher'),
      shouldRotate: shouldRotate
    });

    // CRITICAL: If shouldRotate is true, we MUST rotate players
    if (shouldRotate && benchPlayers.length > 0) {
      this.debug(inning, '🔄 FORCED ROTATION: shouldRotate is true, ensuring bench players get field time');
    }

    // Log current field assignments
    const currentFieldAssignments = fieldPositions.reduce((acc, pos) => {
      acc[pos] = (current.positions as any)[pos];
      return acc;
    }, {} as Record<string, string>);

    this.debug(inning, '📍 Current field assignments', currentFieldAssignments);

    // If pitcher rotation happened, we need to handle the displaced pitcher
    const newPitcher = plan.fieldAssignments.get('pitcher');
    const currentPitcher = current.positions.pitcher;
    const pitcherRotated = newPitcher && newPitcher !== currentPitcher;

    if (pitcherRotated) {
      this.debug(inning, '⚾ Handling pitcher rotation displacement', {
        currentPitcher: currentPitcher,
        newPitcher: newPitcher,
        needToReassignCurrentPitcher: true
      });

      // Create a modified player pool that includes the displaced pitcher
      // and excludes the new pitcher from bench
      const modifiedBenchPlayers = benchPlayers
        .filter(name => name !== newPitcher)  // Remove new pitcher from bench
        .concat(currentPitcher);  // Add displaced pitcher to available pool

      this.debug(inning, '🔄 Modified player pool for rotation', {
        originalBench: benchPlayers,
        modifiedBench: modifiedBenchPlayers,
        displacedPitcher: currentPitcher,
        newPitcherFromBench: newPitcher
      });
    }

    // Use constraint solver for optimal assignment with forced rotation priority
    const solver = new ConstraintSolver(players, this.eligibilityCache);
    this.debug(inning, '🧩 Running constraint solver for optimal assignments');

    // Get positions that need to be filled (excluding any already assigned positions)
    const positionsToFill = fieldPositions.filter(pos =>
      !plan.fieldAssignments.has(pos)
    );

    this.debug(inning, '📋 Positions to fill with constraint solver', {
      allPositions: fieldPositions,
      positionsToFill: positionsToFill,
      pitcherPreAssigned: plan.fieldAssignments.has('pitcher'),
      playersNeedingRotation: playersNeedingRotation
    });

    // CRITICAL FIX: Prioritize ALL bench players for rotation, not just those at streak limit
    let assignments = new Map<string, string>();
    let remainingPositions = [...positionsToFill];

    // Get all bench players and prioritize them for field positions
    const allBenchPlayers = [...current.positions.bench];

    // SIMPLE EQUAL PLAYING TIME: Complete replacement of complex rotation
    if (this.rules.equalPlayingTime && shouldRotate) {
      this.debug(inning, '⚖️ SIMPLE EQUAL PLAYING TIME: Generating completely new lineup based on playing time');
      
      // Get ALL players with their playing time stats
      const allPlayersWithStats = players.map(player => ({
        name: player.name,
        player: player,
        fieldInnings: playerStats?.get(player.id)?.fieldInnings || 0
      }));
      
      // Sort by least playing time first (most deserving to play)
      allPlayersWithStats.sort((a, b) => a.fieldInnings - b.fieldInnings);
      
      this.debug(inning, '⚖️ SIMPLE FAIRNESS: Player playing time rankings', {
        players: allPlayersWithStats.map(p => `${p.name}(${p.fieldInnings})`)
      });
      
      // Assign the 9 players with the least playing time to field positions
      // This is exactly how you'd do it manually - just pick the players who need playing time most
      plan.fieldAssignments.clear();
      
      let positionIndex = 0;
      for (let i = 0; i < Math.min(9, allPlayersWithStats.length); i++) {
        const playerToAssign = allPlayersWithStats[i];
        const position = fieldPositions[positionIndex];
        
        plan.fieldAssignments.set(position, playerToAssign.name);
        this.debug(inning, `⚖️ FAIRNESS ASSIGNMENT: ${playerToAssign.name}(${playerToAssign.fieldInnings} innings) -> ${position}`);
        
        positionIndex++;
      }
      
      // Assign remaining players to bench
      const assignedToField = new Set(plan.fieldAssignments.values());
      plan.benchAssignments = players
        .map(p => p.name)
        .filter(name => !assignedToField.has(name));
      
      this.debug(inning, '⚖️ SIMPLE FAIRNESS: Final assignments', {
        field: Object.fromEntries(plan.fieldAssignments),
        bench: plan.benchAssignments
      });
      
      // Skip all the complex rotation logic - we've already created the perfect fair lineup
      return plan;
    }
    // CRITICAL: If shouldRotate is true, we need to create openings for bench players
    else if (shouldRotate && allBenchPlayers.length > 0) {
      // Clear ALL field positions (except pitcher if already assigned) to force rotation
      remainingPositions = fieldPositions.filter(pos => 
        !plan.fieldAssignments.has(pos) // Only include positions that aren't already assigned
      );
      
      // IMPORTANT: Clear the assignments map to allow new assignments
      // Keep only pitcher if it was pre-assigned
      const pitcherAssignment = plan.fieldAssignments.get('pitcher');
      assignments.clear();
      if (pitcherAssignment) {
        assignments.set('pitcher', pitcherAssignment);
      }
      
      this.debug(inning, '🔄 FORCED ROTATION: Clearing all field positions to rotate bench players', {
        clearedPositions: remainingPositions,
        benchPlayersToRotate: allBenchPlayers,
        keepingPitcher: !!pitcherAssignment
      });
    }

    // Sort bench players by priority: longest bench streak first, then by playing time balance
    const benchPlayersPriority = allBenchPlayers.map(playerName => {
      const streak = benchStreaks.get(playerName) || 0;
      // Find player object to get ID for stats lookup
      const player = playerMap.get(playerName);
      const stats = player ? playerStats?.get(player.id) : undefined;
      const fieldInnings = stats?.fieldInnings || 0;
      const benchInnings = stats?.benchInnings || 0;
      
      let priority = streak * 100 + benchInnings * 10 - fieldInnings; // Higher = more priority
      
      // ENHANCED FAIRNESS: Always boost priority for underplayed players (not just in explicit equalPlayingTime mode)
      // This ensures the algorithm achieves mathematical optimum fairness even without explicit equal playing time setting
      const totalOpportunities = fieldInnings + benchInnings;
      if (totalOpportunities > 0) {
        const playingTimeRatio = fieldInnings / totalOpportunities;
        const expectedRatio = 0.75; // Expect players to play about 75% of innings (9 field positions out of 12+ players)
        
        if (playingTimeRatio < expectedRatio) {
          // Player is significantly underplayed - boost priority
          const deficitBonus = (expectedRatio - playingTimeRatio) * (this.rules.equalPlayingTime ? 1000 : 500);
          priority += deficitBonus;
          
          this.debug(inning, `⚖️ FAIRNESS BOOST: ${playerName} underplayed (${(playingTimeRatio * 100).toFixed(1)}% vs expected ${(expectedRatio * 100)}%)`, {
            fieldInnings,
            benchInnings,
            playingTimeRatio: playingTimeRatio.toFixed(3),
            deficitBonus: deficitBonus.toFixed(0),
            newPriority: priority.toFixed(0),
            equalPlayingTimeMode: this.rules.equalPlayingTime
          });
        }
      } else {
        // For players with no previous opportunities, give them high priority
        priority += this.rules.equalPlayingTime ? 1000 : 500;
        this.debug(inning, `⚖️ FIRST TIME PLAYER: ${playerName} gets priority boost for having no previous opportunities`);
      }
      
      return {
        name: playerName,
        benchStreak: streak,
        fieldInnings,
        benchInnings,
        priority
      };
    }).sort((a, b) => b.priority - a.priority);

    this.debug(inning, '🎯 Bench players rotation priority', {
      benchPlayersPriority,
      totalBenchPlayers: allBenchPlayers.length,
      positionsToFill: remainingPositions.length
    });

    // Try to assign bench players to field positions (aggressive rotation)
    let benchPlayersAssigned = 0;
    
    // ENHANCED FAIRNESS: Always force smart rotation based on playing time imbalance
    let minRotationsRequired = 0;
    if (shouldRotate) {
      if (this.rules.equalPlayingTime) {
        // In explicit fairness mode, try to rotate ALL bench players if possible (maximum fairness)
        minRotationsRequired = Math.min(benchPlayersPriority.length, 6); // Rotate up to 6 players for maximum fairness
        this.debug(inning, '⚖️ EXPLICIT FAIRNESS MODE: Setting aggressive rotation target', {
          benchPlayersAvailable: benchPlayersPriority.length,
          minRotationsRequired,
          reason: 'equalPlayingTime enabled - maximizing rotation for fairness'
        });
      } else {
        // Smart fairness mode: rotate based on playing time imbalance
        const underplayedPlayers = benchPlayersPriority.filter(p => p.priority > 200).length; // Players significantly behind
        minRotationsRequired = Math.min(benchPlayersPriority.length, Math.max(3, underplayedPlayers)); // Rotate at least underplayed players
        this.debug(inning, '⚖️ SMART FAIRNESS MODE: Setting intelligent rotation target', {
          benchPlayersAvailable: benchPlayersPriority.length,
          underplayedPlayers,
          minRotationsRequired,
          reason: 'Ensuring mathematical fairness without explicit equalPlayingTime'
        });
      }
    }
    
    for (const playerInfo of benchPlayersPriority) {
      if (remainingPositions.length === 0 && (!shouldRotate || benchPlayersAssigned >= minRotationsRequired)) break;

      // CRITICAL FIX: Skip players already assigned in the plan (e.g., from pitcher rotation)
      if (Array.from(plan.fieldAssignments.values()).includes(playerInfo.name)) {
        this.debug(inning, `⏭️ Skipping bench player ${playerInfo.name} - already assigned in plan`);
        continue;
      }

      const player = playerMap.get(playerInfo.name);
      if (!player) continue;

      // ENHANCED FAIRNESS: If this is a high-priority underplayed player, be more flexible about positions
      let eligiblePositions = remainingPositions.filter(pos => {
        const positionDisplay = POSITION_CONFIG[pos as PositionKey]?.display || pos;
        return !this.rules.respectPositionLockouts ||
               this.eligibilityCache.getEligiblePositions(player).has(pos);
      });

      // CRITICAL FAIRNESS FIX: If this player is severely underplayed and no "eligible" positions exist,
      // override restrictions to ensure fairness (except for absolute restrictions like never-pitcher)
      if (this.rules.equalPlayingTime && eligiblePositions.length === 0 && playerInfo.priority > 500) {
        this.debug(inning, `⚖️ FAIRNESS OVERRIDE: ${playerInfo.name} severely underplayed, overriding position preferences`, {
          priority: playerInfo.priority,
          fieldInnings: playerInfo.fieldInnings,
          benchInnings: playerInfo.benchInnings
        });

        // Allow any position except those with absolute restrictions
        eligiblePositions = remainingPositions.filter(pos => {
          const player = playerMap.get(playerInfo.name);
          if (!player?.teamRoles) return true;
          
          const role = player.teamRoles[pos as keyof typeof player.teamRoles];
          // Only respect "avoid" if it's an absolute restriction (like never-pitcher for safety)
          const isAbsoluteRestriction = role === 'avoid' && pos === 'pitcher';
          return !isAbsoluteRestriction;
        });
      }

      if (eligiblePositions.length > 0) {
        // ENHANCED FAIRNESS: In fairness mode, prioritize ANY available position over preferences
        let assignedPosition: string;
        if (this.rules.equalPlayingTime && playerInfo.priority > 300) {
          // For severely underplayed players, just take the first available position
          assignedPosition = eligiblePositions[0];
          this.debug(inning, `⚖️ FAIRNESS ASSIGNMENT: ${playerInfo.name} assigned to first available position ${assignedPosition}`, {
            priority: playerInfo.priority,
            allEligiblePositions: eligiblePositions
          });
        } else {
          // Standard mode: sort by preference
          const sortedPositions = this.sortPositionsByPreference(eligiblePositions, player);
          assignedPosition = sortedPositions[0];
        }

        assignments.set(assignedPosition, playerInfo.name);
        // CRITICAL FIX: Also update plan.fieldAssignments immediately to prevent conflicts
        plan.fieldAssignments.set(assignedPosition, playerInfo.name);
        remainingPositions = remainingPositions.filter(pos => pos !== assignedPosition);
        
        this.debug(inning, `🔧 ASSIGNMENT DEBUG: Added bench player ${playerInfo.name} to ${assignedPosition}`, {
          currentPlanAssignments: Object.fromEntries(plan.fieldAssignments),
          currentLocalAssignments: Object.fromEntries(assignments),
          remainingPositions: remainingPositions
        });
        benchPlayersAssigned++;

        const preference = player.positionPreferences?.[this.getPositionKey(assignedPosition)];
        this.debug(inning, `🎯 Bench player rotation: ${playerInfo.name} -> ${assignedPosition}`, {
          benchStreak: playerInfo.benchStreak,
          priority: playerInfo.priority,
          eligiblePositions: eligiblePositions,
          preference: preference || 'neutral',
          preferenceConsidered: !this.rules.equalPlayingTime || playerInfo.priority <= 300,
          benchPlayersAssigned: benchPlayersAssigned,
          minRotationsRequired: minRotationsRequired
        });
      } else {
        this.debug(inning, `⚠️ Cannot rotate bench player ${playerInfo.name} - no eligible positions`, {
          benchStreak: playerInfo.benchStreak,
          remainingPositions: remainingPositions,
          playerRestrictions: player.positionRestrictions,
          priority: playerInfo.priority,
          teamRoles: player.teamRoles
        });
      }
    }

    // Check if we met the minimum rotation requirement
    if (shouldRotate && benchPlayersAssigned < minRotationsRequired) {
      this.debug(inning, '⚠️ Minimum rotation requirement not met', {
        benchPlayersAssigned,
        minRotationsRequired,
        remainingPositions: remainingPositions.length
      });
    }

    // Then use constraint solver for remaining positions with preference consideration
    if (remainingPositions.length > 0) {
      this.debug(inning, '🧩 Calling constraint solver for remaining positions', {
        remainingPositions: remainingPositions,
        existingAssignments: Object.fromEntries(assignments),
        availablePlayers: players.map(p => p.name),
        respectPositionLockouts: this.rules.respectPositionLockouts
      });

      // CRITICAL FIX: Ensure remainingPositions excludes positions already assigned
      // This prevents the duplicate assignment bug by ensuring the constraint solver
      // ONLY works on positions that haven't been assigned yet
      const alreadyAssignedPositions = new Set([
        ...Array.from(plan.fieldAssignments.keys()),
        ...Array.from(assignments.keys())
      ]);
      
      const trulyRemainingPositions = remainingPositions.filter(pos => 
        !alreadyAssignedPositions.has(pos)
      );
      
      this.debug(inning, '🔧 Constraint solver input validation', {
        originalRemainingPositions: remainingPositions,
        trulyRemainingPositions,
        alreadyAssignedPositions: Array.from(alreadyAssignedPositions),
        planFieldAssignments: Object.fromEntries(plan.fieldAssignments),
        existingAssignments: Object.fromEntries(assignments)
      });
      
      if (trulyRemainingPositions.length > 0) {
        // Get players that are already assigned to any position 
        const alreadyAssignedPlayers = new Set([
          ...Array.from(plan.fieldAssignments.values()),
          ...Array.from(assignments.values())
        ]);
        
        // Filter out players that are already assigned
        const availablePlayersForSolver = players.filter(p => !alreadyAssignedPlayers.has(p.name));
        
        this.debug(inning, '🎯 Constraint solver player filtering', {
          totalPlayers: players.length,
          alreadyAssignedPlayers: Array.from(alreadyAssignedPlayers),
          availableForSolver: availablePlayersForSolver.map(p => p.name)
        });
        
        if (availablePlayersForSolver.length > 0 && trulyRemainingPositions.length > 0) {
          // Create a temporary solver with only available players
          const filteredSolver = new ConstraintSolver(availablePlayersForSolver, this.eligibilityCache);
          
          // CRITICAL FIX: Pass existing assignments to prevent conflicts
          const existingAssignments = new Map(plan.fieldAssignments);
          
          this.debug(inning, '🎯 Calling constraint solver with existing assignments', {
            existingAssignments: Object.fromEntries(existingAssignments),
            trulyRemainingPositions: trulyRemainingPositions,
            availablePlayersCount: availablePlayersForSolver.length
          });
          
          const remainingAssignments = filteredSolver.findValidAssignmentWithPreferences(
            trulyRemainingPositions,
            {
              respectPositionLockouts: this.rules.respectPositionLockouts,
              considerPreferences: true
            },
            new Map()  // Start fresh - the filtered player list already excludes assigned players
          );

          if (remainingAssignments) {
        // CRITICAL FIX: Only merge NEW assignments for positions that weren't in the original plan
        for (const [pos, player] of remainingAssignments) {
          // Skip if this position is already assigned in the plan OR if this player is already assigned
          if (!plan.fieldAssignments.has(pos) && !assignments.has(pos)) {
            const playerAlreadyAssigned = Array.from(plan.fieldAssignments.values()).includes(player) ||
                                          Array.from(assignments.values()).includes(player);
            
            if (!playerAlreadyAssigned) {
              assignments.set(pos, player);
              
              this.debug(inning, `🔧 CONSTRAINT SOLVER DEBUG: Adding ${player} to ${pos}`, {
                currentPlanAssignments: Object.fromEntries(plan.fieldAssignments),
                currentLocalAssignments: Object.fromEntries(assignments),
                remainingPositions: trulyRemainingPositions
              });
              
              const playerObj = playerMap.get(player);
              const preference = playerObj?.positionPreferences?.[this.getPositionKey(pos)];
              this.debug(inning, `🎯 Assigned ${player} to ${pos}`, {
                preference: preference || 'neutral',
                preferenceConsidered: true
              });
            } else {
              this.debug(inning, `⏭️ Skipping ${player} to ${pos} - player already assigned elsewhere`);
            }
          } else {
            this.debug(inning, `⏭️ Skipping ${pos} - already assigned to ${plan.fieldAssignments.get(pos) || assignments.get(pos)}`);
          }
        }
      } else {
        this.debug(inning, '❌ Constraint solver failed for remaining positions', {
          remainingPositions: remainingPositions,
          existingAssignments: Object.fromEntries(assignments),
          availablePlayers: players.map(p => p.name),
          playerRestrictions: players.map(p => ({ name: p.name, restrictions: p.positionRestrictions }))
        });

        // Try fallback: basic assignment without preferences
        this.debug(inning, '🔄 Trying fallback: basic assignment without preferences');
        const fallbackAssignments = filteredSolver.findValidAssignment(
          trulyRemainingPositions,
          { respectPositionLockouts: this.rules.respectPositionLockouts },
          new Map()  // Start fresh for fallback too - filtered players already exclude assigned ones
        );

        if (fallbackAssignments) {
          this.debug(inning, '✅ Fallback assignment succeeded');
          for (const [pos, player] of fallbackAssignments) {
            // CRITICAL FIX: Only add if position and player not already assigned
            if (!plan.fieldAssignments.has(pos) && !assignments.has(pos)) {
              const playerAlreadyAssigned = Array.from(plan.fieldAssignments.values()).includes(player) ||
                                            Array.from(assignments.values()).includes(player);
              
              if (!playerAlreadyAssigned) {
                assignments.set(pos, player);
                this.debug(inning, `🎯 Fallback: Assigned ${player} to ${pos}`);
              } else {
                this.debug(inning, `⏭️ Fallback: Skipping ${player} to ${pos} - player already assigned elsewhere`);
              }
            } else {
              this.debug(inning, `⏭️ Fallback: Skipping ${pos} - already assigned to ${plan.fieldAssignments.get(pos) || assignments.get(pos)}`);
            }
          }
        } else {
          this.debug(inning, '❌ Even fallback assignment failed - this should not happen');
          // This is a critical error - we should not reach this point
          throw new LineupGenerationError(
            'Cannot find valid position assignments',
            'CONSTRAINT_SOLVER_FAILED',
            {
              inning,
              availablePlayers: players.map(p => p.name),
              constraints: {
                remainingPositions,
                existingAssignments: Object.fromEntries(assignments),
                respectPositionLockouts: this.rules.respectPositionLockouts
              }
            }
          );
        }
      }
        } else {
          this.debug(inning, '⚠️ No available players for constraint solver');
        }
      } else {
        this.debug(inning, '✅ All positions already assigned, skipping constraint solver');
      }
    }

    // Check if we have valid assignments (either from forced rotation or constraint solver)
    console.log(`🔍 CHECKING ASSIGNMENTS for inning ${inning}:`, {
      assignmentsSize: assignments.size,
      assignments: Object.fromEntries(assignments),
      planFieldAssignments: Object.fromEntries(plan.fieldAssignments),
      totalFieldAssignments: plan.fieldAssignments.size
    });

    if (assignments.size > 0) {
      this.debug(inning, '✅ Constraint solver found valid assignments', {
        assignments: Object.fromEntries(assignments),
        totalAssignments: assignments.size
      });

      // CRITICAL FIX: Merge constraint solver assignments with pre-assigned positions (like pitcher)
      // Only add assignments that don't conflict with existing plan assignments
      for (const [pos, player] of assignments) {
        // FIRST: Skip if this position is already assigned in the plan
        if (plan.fieldAssignments.has(pos)) {
          this.debug(inning, `⏭️ Skipping assignment ${player} -> ${pos} - position already assigned to ${plan.fieldAssignments.get(pos)}`);
          continue;
        }
        
        // SECOND: Check if this player is already assigned to another position
        const existingPosition = Array.from(plan.fieldAssignments.entries())
          .find(([existingPos, existingPlayer]) => existingPlayer === player);
        
        if (existingPosition) {
          this.debug(inning, `❌ CONFLICT: Player ${player} already assigned to ${existingPosition[0]}, cannot assign to ${pos}`);
          // Don't throw error - just skip this assignment
          continue;
        }
        
        // Safe to assign
        plan.fieldAssignments.set(pos, player);
        this.debug(inning, `✅ FINAL MERGE DEBUG: Added assignment: ${player} -> ${pos}`, {
          finalPlanAssignments: Object.fromEntries(plan.fieldAssignments),
          localAssignments: Object.fromEntries(assignments)
        });
      }

      // Determine bench assignments - prioritize field players who were displaced
      const assignedPlayers = new Set(plan.fieldAssignments.values());
      const potentialBenchPlayers = players
        .map(p => p.name)
        .filter(name => !assignedPlayers.has(name));

      // CRITICAL FIX: Prioritize displaced field players for bench
      const currentFieldPlayers = fieldPositions.map(pos => (current.positions as any)[pos]).filter(Boolean);
      const displacedFieldPlayers = currentFieldPlayers.filter(name => !assignedPlayers.has(name));
      const remainingBenchPlayers = potentialBenchPlayers.filter(name => !displacedFieldPlayers.includes(name));

      // Assign displaced field players to bench first, then remaining players
      plan.benchAssignments = [...displacedFieldPlayers, ...remainingBenchPlayers];

      this.debug(inning, '🪑 Bench assignment logic', {
        totalPotentialBench: potentialBenchPlayers.length,
        displacedFieldPlayers: displacedFieldPlayers,
        remainingBenchPlayers: remainingBenchPlayers,
        finalBenchAssignments: plan.benchAssignments,
        assignedToField: Array.from(assignedPlayers)
      });

      this.debug(inning, '🪑 Bench assignments determined', {
        benchPlayers: plan.benchAssignments,
        benchCount: plan.benchAssignments.length,
        assignedToField: Array.from(assignedPlayers)
      });

      // Log rotation changes
      const rotationChanges = fieldPositions.map(pos => {
        const oldPlayer = (current.positions as any)[pos];
        const newPlayer = plan.fieldAssignments.get(pos);
        return {
          position: pos,
          oldPlayer: oldPlayer,
          newPlayer: newPlayer,
          changed: oldPlayer !== newPlayer
        };
      }).filter(change => change.changed);

      this.debug(inning, '🔄 Position changes summary', {
        totalChanges: rotationChanges.length,
        changes: rotationChanges
      });

    } else {
      this.debug(inning, '❌ Constraint solver failed to find valid assignments');
      this.debug(inning, '⚠️ Falling back to keeping current assignments');

      // Fallback: keep current assignments, but respect pitcher rotation if it happened
      fieldPositions.forEach(pos => {
        if (!plan.fieldAssignments.has(pos)) {
          const currentPlayer = (current.positions as any)[pos];
          if (currentPlayer) {
            plan.fieldAssignments.set(pos, currentPlayer);
          } else {
            // If no current player for this position, this is a critical error
            this.debug(inning, `❌ CRITICAL: No player assigned to position ${pos} in current lineup`);
          }
        }
      });

      // Ensure all required positions are filled
      const missingPositions = fieldPositions.filter(pos => !plan.fieldAssignments.has(pos));
      if (missingPositions.length > 0) {
        this.debug(inning, `❌ CRITICAL: Missing positions after fallback: ${missingPositions.join(', ')}`);

        // Emergency fallback: assign any available players to missing positions
        const assignedPlayers = new Set(plan.fieldAssignments.values());
        const availablePlayers = players.filter(p => !assignedPlayers.has(p.name));

        missingPositions.forEach((pos, index) => {
          // Find first available player who can play this position (not 'avoid')
          const positionDisplay = POSITION_CONFIG[pos as keyof typeof POSITION_CONFIG]?.display || pos;
          const suitablePlayer = availablePlayers.find(player => 
            canPlayPosition(player, positionDisplay)
          );
          
          if (suitablePlayer) {
            plan.fieldAssignments.set(pos, suitablePlayer.name);
            this.debug(inning, `🚨 Emergency assignment: ${suitablePlayer.name} -> ${pos} (respects restrictions)`);
            // Remove from available list to avoid double assignment
            const playerIndex = availablePlayers.indexOf(suitablePlayer);
            if (playerIndex > -1) availablePlayers.splice(playerIndex, 1);
          } else {
            // NO VALID ASSIGNMENT POSSIBLE
            this.debug(inning, `❌ IMPOSSIBLE: Cannot assign ${pos} without violating restrictions`);
            this.debug(inning, `❌ Available players: ${availablePlayers.map(p => p.name).join(', ')}`);
            
            throw new LineupGenerationError(
              `Cannot assign position ${pos} without violating restrictions in fallback`,
              'POSITION_RESTRICTIONS_IMPOSSIBLE_FALLBACK',
              {
                inning,
                position: pos,
                availablePlayers: availablePlayers.map(p => p.name)
              }
            );
          }
        });
      }

      // Determine bench assignments for fallback
      const assignedPlayers = new Set(plan.fieldAssignments.values());
      plan.benchAssignments = players
        .map(p => p.name)
        .filter(name => !assignedPlayers.has(name));
    }

    // Final validation: ensure all required positions are assigned
    const requiredPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase',
                              'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
    const missingPositions = requiredPositions.filter(pos => !plan.fieldAssignments.has(pos));

    if (missingPositions.length > 0) {
      this.debug(inning, `❌ CRITICAL: Missing positions after all attempts: ${missingPositions.join(', ')}`);
      this.debug(inning, '🔍 Current field assignments:', Object.fromEntries(plan.fieldAssignments));
      this.debug(inning, '🔍 Available players:', players.map(p => p.name));

      throw new LineupGenerationError(
        `Missing required positions: ${missingPositions.join(', ')}`,
        'MISSING_POSITIONS',
        {
          inning,
          availablePlayers: players.map(p => p.name),
          constraints: {
            missingPositions,
            currentAssignments: Object.fromEntries(plan.fieldAssignments)
          }
        }
      );
    }

    this.debug(inning, '✅ All required positions assigned successfully', {
      totalAssignments: plan.fieldAssignments.size,
      benchCount: plan.benchAssignments.length
    });
  }
}

// ===== HELPER TYPES =====
interface RotationPlan {
  moves: Array<{ from: string; to: string; player: string }>;
  benchAssignments: string[];
  fieldAssignments: Map<string, string>;
  noRotation?: boolean;
  currentPositions?: any;
}

// ===== UTILITY FUNCTIONS =====
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function generateId(): string {
  return uuidv4();
}

export const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'numeric',
    day: 'numeric',
    year: 'numeric',
  });
};

// Updated position display name function using config
export function getPositionDisplayName(positionId: string): string {
  const config = Object.values(POSITION_CONFIG).find(p => p.key === positionId);
  return config?.display || positionId;
}

// Updated position label function using config
export function getPositionLabel(positionId: string): string {
  if (positionId.startsWith("bench")) {
    const benchNumber = positionId.replace("bench", "");
    return `Bench ${benchNumber}`;
  }
  return getPositionDisplayName(positionId);
}

// Updated canPlayPosition function with team roles support
export function canPlayPosition(player: Player, position: string, respectLockouts: boolean = true): boolean {
  if (!player) {
    return true;
  }

  // Map position display names to teamRole keys
  const positionToRoleKey: { [key: string]: string } = {
    'Pitcher': 'pitcher',
    'Catcher': 'catcher',
    'First Base': 'firstBase',
    'Second Base': 'secondBase',
    'Shortstop': 'shortstop',
    'Third Base': 'thirdBase',
    'Left Field': 'leftField',
    'Center Field': 'centerField',
    'Right Field': 'rightField'
  };

  const roleKey = positionToRoleKey[position];
  
  // Check if player has the Utility Player designation
  if (player.teamRoles?.isUtilityPlayer) {
    // Utility players cover the 7 field positions (NOT pitcher/catcher unless explicitly set)
    const utilityPositions = ['firstBase', 'secondBase', 'thirdBase', 'shortstop', 'leftField', 'centerField', 'rightField'];
    
    // For pitcher/catcher, use their explicit role setting
    if (roleKey === 'pitcher' || roleKey === 'catcher') {
      const role = player.teamRoles[roleKey];
      // Only allow pitcher/catcher if they have a positive role (not avoid/never/unset)
      if (role && role !== 'avoid' && role !== 'never' && role !== 'unset') {
        console.log(`✅ Utility player ${player.name} can play ${position} (explicitly set as ${role})`);
        return true;
      }
      console.log(`🚫 Utility player ${player.name} cannot play ${position} (not explicitly assigned)`);
      return false;
    }
    
    // For other positions, check if it's a utility position
    if (roleKey && utilityPositions.includes(roleKey)) {
      const role = player.teamRoles[roleKey];
      // Still respect "Never" for utility positions
      if (role === 'avoid' || role === 'never') {
        console.log(`🚫 Utility player ${player.name} cannot play ${position} (explicitly marked Never)`);
        return false;
      }
      // Otherwise, utility players can play this field position
      console.log(`✅ Utility player ${player.name} can play ${position} (utility field position)`);
      return true;
    }
  }

  // Check if player has the Outfield Specialist designation
  if (player.teamRoles?.isOutfieldSpecialist) {
    const outfieldPositions = ['leftField', 'centerField', 'rightField'];
    if (roleKey && outfieldPositions.includes(roleKey)) {
      // Outfield specialists can play any outfield position unless explicitly marked "Never"
      const role = player.teamRoles[roleKey];
      if (role === 'avoid' || role === 'never') {
        console.log(`🚫 Outfield specialist ${player.name} cannot play ${position} (explicitly marked Never)`);
        return false;
      }
      console.log(`✅ Outfield specialist ${player.name} can play ${position} (outfield position)`);
      return true;
    }
  }

  // Standard role checking (for non-utility/non-specialist cases, or positions not covered by those)
  if (player.teamRoles) {
    const role = player.teamRoles[roleKey];
    
    // ALWAYS respect 'avoid' (which means "Never" in the UI) regardless of respectLockouts setting
    if (roleKey && role === 'avoid') {
      console.log(`🚫 HARD RESTRICTION: ${player.name} cannot play ${position} (role: avoid/Never)`);
      return false;
    }
    
    // Note: 'never' might be legacy - the current UI uses 'avoid' for "Never"
    if (roleKey && role === 'never') {
      console.log(`🚫 HARD RESTRICTION: ${player.name} cannot play ${position} (role: never)`);
      return false;
    }
    
    // If player has an explicit positive role, they can play
    if (role && role !== 'unset') {
      console.log(`✅ ${player.name} can play ${position} (role: ${role})`);
      return true;
    }
  }

  // NEW LOGIC: If a player has ANY position assignments, they can ONLY play those positions
  // EXCEPTION: Utility players can play their utility positions PLUS any explicit assignments
  // Count how many positions have been explicitly set (not counting special designations)
  if (player.teamRoles && !player.teamRoles.isUtilityPlayer && !player.teamRoles.isOutfieldSpecialist) {
    const assignedPositions = Object.entries(player.teamRoles)
      .filter(([key, value]) => 
        key !== 'isUtilityPlayer' && 
        key !== 'isOutfieldSpecialist' && 
        value && 
        value !== 'unset' && 
        value !== 'avoid' && 
        value !== 'never'
      );
    
    // If player has explicit position assignments, they can ONLY play those positions
    // This rule does NOT apply to utility players or outfield specialists
    if (assignedPositions.length > 0) {
      console.log(`🚫 ${player.name} cannot play ${position} (not in their assigned positions)`);
      return false;
    }
  }

  // Default: if no positions are assigned at all, player cannot play any position
  // But first check if this is a utility player who should have been caught earlier
  if (player.teamRoles?.isUtilityPlayer && !['Pitcher', 'Catcher'].includes(position)) {
    console.warn(`⚠️ Utility player ${player.name} reached default case for ${position} - this shouldn't happen`);
    return true;
  }
  
  console.log(`🚫 ${player.name} cannot play ${position} (no positions assigned)`);
  return false;
}

// Updated checkPositionRestriction function
export function checkPositionRestriction(playerObj: Player, positionId: string): string | null {
  // ONLY check team roles (new system) - IGNORE legacy restrictions
  const role = playerObj.teamRoles?.[positionId];
  // 'avoid' is the key used for "Never" in the UI
  if (role === 'avoid' || role === 'never') {
    return getPositionDisplayName(positionId);
  }

  // NO FALLBACK TO LEGACY RESTRICTIONS
  return null;
}

// ===== LINEUP VALIDATION (Fix #6) =====
export function validateLineup(
  lineup: InningLineup,
  players: Player[],
  rules: LineupRules
): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  const playerMap = new Map(players.map(p => [p.name, p]));
  const assignedPlayers = new Set<string>();
  
  // Check all field positions are filled
  const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 
                         'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
  
  for (const pos of fieldPositions) {
    const playerName = (lineup.positions as any)[pos];
    if (!playerName) {
      errors.push(`Position ${pos} is not filled`);
    } else {
      // Check for duplicates
      if (assignedPlayers.has(playerName)) {
        errors.push(`Player ${playerName} is assigned to multiple positions`);
      }
      assignedPlayers.add(playerName);
      
      // Check position restrictions
      const player = playerMap.get(playerName);
      if (player && rules.respectPositionLockouts) {
        const positionDisplay = getPositionDisplayName(pos);
        if (!canPlayPosition(player, positionDisplay)) {
          // Get list of positions the player CAN play
          const eligiblePositions: string[] = [];
          fieldPositions.forEach(fieldPos => {
            const display = getPositionDisplayName(fieldPos);
            if (canPlayPosition(player, display)) {
              eligiblePositions.push(display);
            }
          });
          
          errors.push(`Player ${playerName} cannot play ${positionDisplay}. They can only play: ${eligiblePositions.join(', ') || 'No positions assigned'}`);
        }
      }
    }
  }
  
  // Check bench players
  for (const benchPlayer of lineup.positions.bench) {
    if (assignedPlayers.has(benchPlayer)) {
      errors.push(`Player ${benchPlayer} is both on bench and in field`);
    }
    assignedPlayers.add(benchPlayer);
  }
  
  // Check total player count
  if (assignedPlayers.size !== players.length) {
    errors.push(`Player count mismatch: ${assignedPlayers.size} assigned, ${players.length} available`);
  }
  
  // Check for unknown players
  for (const playerName of assignedPlayers) {
    if (!playerMap.has(playerName)) {
      errors.push(`Unknown player: ${playerName}`);
    }
  }
  
  return { valid: errors.length === 0, errors, warnings };
}

// ===== ROTATION VALIDATION FUNCTIONS =====
export function validateRotation(
  previousInnings: InningLineup[],
  currentInning: InningLineup,
  rules: LineupRules,
  players: Player[]
): { valid: boolean; violations: string[] } {
  const violations: string[] = [];
  const inningNumber = currentInning.inning;

  console.log(`🔍 VALIDATING ROTATION for inning ${inningNumber}`);

  // Check pitcher rotation
  if (rules.rotatePitcherEvery && rules.allowPitcherRotation && previousInnings.length > 0) {
    const shouldHaveRotated = inningNumber > 1 &&
      (inningNumber % rules.rotatePitcherEvery === 1 && inningNumber > 1);
    const previousPitcher = previousInnings[previousInnings.length - 1]?.positions.pitcher;
    const currentPitcher = currentInning.positions.pitcher;
    const didRotate = currentPitcher !== previousPitcher;

    console.log(`⚾ Pitcher rotation check:`, {
      inningNumber,
      rotatePitcherEvery: rules.rotatePitcherEvery,
      shouldHaveRotated,
      previousPitcher,
      currentPitcher,
      didRotate
    });

    if (shouldHaveRotated && !didRotate) {
      violations.push(`Pitcher should have rotated at inning ${inningNumber} (every ${rules.rotatePitcherEvery} innings)`);
    }
  }

  // Check bench streaks using the getBenchStreaks function
  const allInnings = [...previousInnings, currentInning];
  const benchStreaks = getBenchStreaksStatic(allInnings, inningNumber);
  const maxAllowedStreak = rules.maxConsecutiveBenchInnings || 2;

  console.log(`🪑 Bench streak validation:`, {
    maxAllowedStreak,
    currentBenchStreaks: Object.fromEntries(benchStreaks)
  });

  benchStreaks.forEach((streak, player) => {
    if (streak > maxAllowedStreak) {
      violations.push(`${player} on bench for ${streak} consecutive innings (max allowed: ${maxAllowedStreak})`);
    }
  });

  // Check rotation frequency
  if (rules.rotateLineupEvery && previousInnings.length > 0) {
    const shouldHaveRotated = (inningNumber - 1) % rules.rotateLineupEvery === 0;

    if (shouldHaveRotated) {
      // Check if any rotation actually occurred
      const previousInning = previousInnings[previousInnings.length - 1];
      const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase',
                             'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];

      let rotationOccurred = false;
      for (const pos of fieldPositions) {
        if ((previousInning.positions as any)[pos] !== (currentInning.positions as any)[pos]) {
          rotationOccurred = true;
          break;
        }
      }

      // Also check bench changes
      const previousBench = new Set(previousInning.positions.bench);
      const currentBench = new Set(currentInning.positions.bench);
      if (previousBench.size !== currentBench.size ||
          [...previousBench].some(player => !currentBench.has(player))) {
        rotationOccurred = true;
      }

      console.log(`🔄 Rotation frequency check:`, {
        inningNumber,
        rotateLineupEvery: rules.rotateLineupEvery,
        shouldHaveRotated,
        rotationOccurred
      });

      if (!rotationOccurred) {
        violations.push(`No rotation occurred at inning ${inningNumber} (should rotate every ${rules.rotateLineupEvery} innings)`);
      }
    }
  }

  // Check fair playing time distribution
  if (rules.equalPlayingTime && allInnings.length >= 3) {
    const playingTime = new Map<string, number>();
    players.forEach(player => playingTime.set(player.name, 0));

    // Calculate playing time for each player
    allInnings.forEach(inning => {
      const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase',
                             'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
      fieldPositions.forEach(pos => {
        const player = (inning.positions as any)[pos];
        if (player) {
          playingTime.set(player, (playingTime.get(player) || 0) + 1);
        }
      });
    });

    const playingTimes = Array.from(playingTime.values());
    const minTime = Math.min(...playingTimes);
    const maxTime = Math.max(...playingTimes);
    const avgTime = playingTimes.reduce((a, b) => a + b, 0) / playingTimes.length;
    const range = maxTime - minTime;

    // Allow some variance but flag excessive imbalance
    const maxAllowedRange = Math.max(2, Math.ceil(allInnings.length * 0.4));

    console.log(`⚖️ Playing time fairness check:`, {
      totalInnings: allInnings.length,
      minTime,
      maxTime,
      avgTime: avgTime.toFixed(1),
      range,
      maxAllowedRange,
      playingTimeDistribution: Object.fromEntries(playingTime)
    });

    if (range > maxAllowedRange) {
      const underplayedPlayers = players
        .filter(p => (playingTime.get(p.name) || 0) === minTime)
        .map(p => p.name);
      const overplayedPlayers = players
        .filter(p => (playingTime.get(p.name) || 0) === maxTime)
        .map(p => p.name);

      violations.push(`Playing time imbalance detected: range ${range} exceeds max allowed ${maxAllowedRange}. Underplayed: [${underplayedPlayers.join(', ')}], Overplayed: [${overplayedPlayers.join(', ')}]`);
    }
  }

  // Check preference compliance (informational, not a violation)
  console.log('\n🎯 Position Preference Compliance:');
  const preferenceCompliance = new Map<string, { honored: number; total: number }>();

  // Analyze preference compliance for current inning
  const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase',
                         'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];

  fieldPositions.forEach(pos => {
    const player = (currentInning.positions as any)[pos];
    if (player) {
      const playerObj = players.find(p => p.name === player);
      if (playerObj?.positionPreferences) {
        const posKey = getPositionKeyStatic(pos);
        const preference = playerObj.positionPreferences[posKey];

        if (preference && preference !== 'neutral') {
          const compliance = preferenceCompliance.get(player) || { honored: 0, total: 0 };
          compliance.total++;

          if (preference === 'preferred' || preference === 'secondary') {
            compliance.honored++;
            console.log(`  ✅ ${player} at ${pos}: ${preference} position`);
          } else if (preference === 'avoid') {
            console.log(`  ⚠️ ${player} at ${pos}: assigned to avoided position (likely due to constraints)`);
          }

          preferenceCompliance.set(player, compliance);
        }
      }
    }
  });

  const isValid = violations.length === 0;
  console.log(`🎯 Rotation validation result:`, {
    valid: isValid,
    violationCount: violations.length,
    violations: violations,
    preferenceCompliance: Object.fromEntries(preferenceCompliance)
  });

  return { valid: isValid, violations };
}

// Static version of getBenchStreaks for use in validation
function getBenchStreaksStatic(allInnings: InningLineup[], upToInning: number): Map<string, number> {
  const streaks = new Map<string, number>();

  if (allInnings.length === 0 || upToInning <= 0) {
    return streaks;
  }

  // Initialize all players to 0
  const firstInning = allInnings[0];
  if (firstInning) {
    // Get all players from first inning
    const allPlayers = new Set<string>();
    Object.values(firstInning.positions).forEach(player => {
      if (typeof player === 'string' && player) {
        allPlayers.add(player);
      } else if (Array.isArray(player)) {
        player.forEach(p => allPlayers.add(p));
      }
    });
    allPlayers.forEach(player => streaks.set(player, 0));
  }

  // Count consecutive bench innings going backwards from upToInning
  for (let i = Math.min(upToInning - 1, allInnings.length - 1); i >= 0; i--) {
    const inning = allInnings[i];
    if (!inning) break;

    // First, reset streak for players who played in the field
    Object.entries(inning.positions).forEach(([position, player]) => {
      if (position !== 'bench' && typeof player === 'string' && player) {
        streaks.set(player, 0);
      }
    });

    // Then increment streak for bench players
    inning.positions.bench.forEach(player => {
      if (player) {
        const currentStreak = streaks.get(player) || 0;
        streaks.set(player, currentStreak + 1);
      }
    });
  }

  return streaks;
}

// Static version of getPositionKey for use in validation
function getPositionKeyStatic(position: string): string {
  // Map display position names to preference keys
  const positionKeyMap: { [key: string]: string } = {
    'pitcher': 'pitcher',
    'catcher': 'catcher',
    'firstBase': 'firstBase',
    'secondBase': 'secondBase',
    'thirdBase': 'thirdBase',
    'shortstop': 'shortstop',
    'leftField': 'leftField',
    'centerField': 'centerField',
    'rightField': 'rightField',
    'leftCenter': 'leftCenter',
    'rightCenter': 'rightCenter'
  };

  return positionKeyMap[position] || position;
}

// ===== MAIN LINEUP GENERATION FUNCTIONS =====
export function generateCompleteLineup(
  availablePlayers: Player[],
  totalInnings: number,
  rules: LineupRules = {
    limitBenchTime: true,
    maxConsecutiveBenchInnings: 2,
    allowPitcherRotation: true,
    allowCatcherRotation: true,
    respectPositionLockouts: true,
    equalPlayingTime: true,
    rotateLineupEvery: 1,
    rotatePitcherEvery: 2
  }
): InningLineup[] {
  console.log(`🚀 GENERATING COMPLETE LINEUP - ${totalInnings} innings for ${availablePlayers.length} players`);

  if (availablePlayers.length < 8) {
    throw new InsufficientPlayersError(8, availablePlayers.length);
  }

  try {
    return generateOptimalLineup(availablePlayers, totalInnings, rules);
  } catch (error) {
    console.error(`🚨 LINEUP GENERATION FAILED:`, error);
    console.error(`🚨 Error details:`, {
      message: error.message,
      name: error.name,
      stack: error.stack,
      availablePlayers: availablePlayers.length,
      totalInnings,
      competitiveMode: rules.competitiveMode
    });
    
    if (error instanceof LineupGenerationError) {
      throw error;
    }
    throw new LineupGenerationError(
      'Failed to generate lineup',
      'GENERATION_FAILED',
      { availablePlayers: availablePlayers.map(p => p.name), originalError: error.message }
    );
  }
}

export function generateOptimalLineup(
  availablePlayers: Player[],
  totalInnings: number,
  rules: LineupRules
): InningLineup[] {
  console.log(`🚀 GENERATING OPTIMAL LINEUP - ${totalInnings} innings for ${availablePlayers.length} players`);
  console.log(`📋 Rules:`, {
    limitBenchTime: rules.limitBenchTime,
    maxConsecutiveBenchInnings: rules.maxConsecutiveBenchInnings,
    allowPitcherRotation: rules.allowPitcherRotation,
    allowCatcherRotation: rules.allowCatcherRotation,
    respectPositionLockouts: rules.respectPositionLockouts,
    equalPlayingTime: rules.equalPlayingTime,
    rotateLineupEvery: rules.rotateLineupEvery,
    rotatePitcherEvery: rules.rotatePitcherEvery
  });
  console.log(`👥 Players:`, availablePlayers.map(p => ({
    name: p.name,
    restrictions: p.positionRestrictions
  })));

  const random = new SeededRandom(rules._randomSeed);
  const eligibilityCache = new PlayerEligibilityCache();
  const solver = new ConstraintSolver(availablePlayers, eligibilityCache);
  const rotator = new LineupRotator(rules, eligibilityCache, random);

  // CRITICAL FIX: Initialize playerStats with cross-game data (previous games) 
  // BUT do NOT accumulate current game innings into the same counter for rotation decisions
  const playerStats = new Map<string, {
    fieldInnings: number;
    benchInnings: number;
    inningsPitched: number;
    benchStreak: number;
    lastPositions: Map<string, number>;
    // Track cross-game vs current game separately
    crossGameFieldInnings: number;
    crossGameBenchInnings: number;
    crossGamePitchingInnings: number;
  }>();

  availablePlayers.forEach(player => {
    // Initialize with cross-game tracking data if available
    const crossGameData = rules._crossGameTracking;
    const existingFieldInnings = crossGameData?.playerFieldInnings[player.id] || 0;
    const existingBenchInnings = crossGameData?.playerBenchInnings[player.id] || 0;
    const existingPitchingInnings = crossGameData?.playerPitchingInnings[player.id] || 0;

    // Initialize with cross-game data - this will be used for rotation decisions
    playerStats.set(player.id, {
      fieldInnings: existingFieldInnings,
      benchInnings: existingBenchInnings,
      inningsPitched: existingPitchingInnings,
      benchStreak: 0,
      lastPositions: new Map()
    });

    if (crossGameData) {
      console.log(`🔄 Cross-game data for ${player.name}: ${existingFieldInnings} field, ${existingBenchInnings} bench, ${existingPitchingInnings} pitching innings from ${crossGameData.gameNumber-1} previous games`);
    }
  });

  console.log(`📊 Initialized cross-game stats for ${playerStats.size} players`);

  const lineup: InningLineup[] = [];
  const maxBenchStreak = rules.maxConsecutiveBenchInnings || 2;
  
  // Generate each inning
  for (let inning = 1; inning <= totalInnings; inning++) {
    console.log(`\n🏟️ GENERATING INNING ${inning} of ${totalInnings}`);

    // Log player stats before generating inning
    console.log(`📊 Player stats before inning ${inning}:`);
    Array.from(playerStats.entries()).forEach(([id, stats]) => {
      const player = availablePlayers.find(p => p.id === id);
      console.log(`  ${player?.name}: Field=${stats.fieldInnings}, Bench=${stats.benchInnings}, BenchStreak=${stats.benchStreak}, Pitched=${stats.inningsPitched}`);
    });

    if (inning === 1) {
      console.log(`🎯 Generating initial lineup for inning 1`);

      // Generate initial lineup
      const initialLineup = generateInitialLineup(
        availablePlayers,
        solver,
        eligibilityCache,
        rules
      );

      console.log(`✅ Initial lineup generated:`, {
        positions: initialLineup.positions,
        benchCount: initialLineup.positions.bench.length
      });

      // Validate initial lineup
      const validation = validateLineup(initialLineup, availablePlayers, rules);
      if (!validation.valid) {
        console.error(`❌ Initial lineup validation failed:`, validation.errors);
        
        // Log player position capabilities for debugging
        console.error(`📋 Player position capabilities:`);
        availablePlayers.forEach(player => {
          const positions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
          const canPlay = positions.filter(pos => {
            const display = getPositionDisplayName(pos);
            return canPlayPosition(player, display);
          }).map(pos => getPositionDisplayName(pos));
          console.error(`  ${player.name}: ${canPlay.join(', ') || 'NO POSITIONS ASSIGNED'}`);
        });
        
        throw new LineupGenerationError(
          'Invalid initial lineup - check player position assignments',
          'INVALID_INITIAL_LINEUP',
          { inning: 1, availablePlayers: availablePlayers.map(p => p.name) }
        );
      }

      console.log(`✅ Initial lineup validated successfully`);
      lineup.push(initialLineup);
      updatePlayerStats(initialLineup, playerStats, availablePlayers, 1);

      // Validate initial lineup against rotation rules
      const rotationValidation = validateRotation([], initialLineup, rules, availablePlayers);
      if (!rotationValidation.valid) {
        console.warn(`⚠️ Initial lineup has rotation rule violations:`, rotationValidation.violations);
      }

    } else {
      console.log(`🔄 Rotating lineup from inning ${inning - 1} to ${inning}`);

      // Rotate from previous inning
      const previousInning = lineup[inning - 2];
      console.log(`📋 Previous inning lineup:`, previousInning.positions);

      // Create simplified stats map with only the fields needed for rotation
      const statsMap = new Map(
        Array.from(playerStats.entries()).map(([id, stats]) => [
          id,
          { fieldInnings: stats.fieldInnings, benchInnings: stats.benchInnings }
        ])
      );

      console.log(`📊 Stats map for rotation:`, Object.fromEntries(statsMap));

      const rotatedLineup = rotator.rotateLineup(
        previousInning,
        availablePlayers,
        inning,
        statsMap,
        lineup  // Pass the lineup history for bench streak calculation
      );

      console.log(`🔄 Rotation completed for inning ${inning}:`, rotatedLineup.positions);

      // Validate rotated lineup
      const validation = validateLineup(rotatedLineup, availablePlayers, rules);
      if (!validation.valid) {
        console.error(`❌ Rotation validation failed for inning ${inning}:`, validation.errors);
        console.log(`🚨 Falling back to constraint solver for inning ${inning}`);

        // Fallback to constraint solver
        const fallbackLineup = generateFallbackLineup(
          availablePlayers,
          inning,
          solver,
          eligibilityCache,
          rules,
          playerStats,
          maxBenchStreak
        );

        console.log(`🔧 Fallback lineup generated for inning ${inning}:`, fallbackLineup.positions);
        lineup.push(fallbackLineup);
        updatePlayerStats(fallbackLineup, playerStats, availablePlayers, inning);

        // Validate fallback lineup against rules
        const fallbackValidation = validateRotation(
          lineup.slice(0, -1), // Previous innings
          fallbackLineup,
          rules,
          availablePlayers
        );

        if (!fallbackValidation.valid) {
          console.error(`❌ FALLBACK LINEUP RULE VIOLATIONS detected for inning ${inning}:`);
          fallbackValidation.violations.forEach(violation => {
            console.error(`  - ${violation}`);
          });
        } else {
          console.log(`✅ Fallback lineup rules validated successfully for inning ${inning}`);
        }
      } else {
        console.log(`✅ Rotated lineup validated successfully for inning ${inning}`);
        lineup.push(rotatedLineup);
        updatePlayerStats(rotatedLineup, playerStats, availablePlayers, inning);

        // Validate rotation against rules
        const rotationValidation = validateRotation(
          lineup.slice(0, -1), // Previous innings
          rotatedLineup,
          rules,
          availablePlayers
        );

        if (!rotationValidation.valid) {
          console.error(`❌ ROTATION RULE VIOLATIONS detected for inning ${inning}:`);
          rotationValidation.violations.forEach(violation => {
            console.error(`  - ${violation}`);
          });
        } else {
          console.log(`✅ Rotation rules validated successfully for inning ${inning}`);
        }
      }
    }

    // Log final stats after this inning
    console.log(`📊 Player stats after inning ${inning}:`);
    Array.from(playerStats.entries()).forEach(([id, stats]) => {
      const player = availablePlayers.find(p => p.id === id);
      console.log(`  ${player?.name}: Field=${stats.fieldInnings}, Bench=${stats.benchInnings}, BenchStreak=${stats.benchStreak}, Pitched=${stats.inningsPitched}`);
    });
  }

  // Final summary
  console.log(`\n🎉 LINEUP GENERATION COMPLETE!`);
  console.log(`📊 Final player statistics summary:`);
  Array.from(playerStats.entries()).forEach(([name, stats]) => {
    console.log(`  ${name}: Field=${stats.fieldInnings}/${totalInnings} (${Math.round(stats.fieldInnings/totalInnings*100)}%), Bench=${stats.benchInnings}, Pitched=${stats.inningsPitched}`);
  });

  // Calculate fairness metrics
  const fieldInningsArray = Array.from(playerStats.values()).map(s => s.fieldInnings);
  const minFieldInnings = Math.min(...fieldInningsArray);
  const maxFieldInnings = Math.max(...fieldInningsArray);
  const avgFieldInnings = fieldInningsArray.reduce((a, b) => a + b, 0) / fieldInningsArray.length;

  console.log(`📈 Fairness metrics:`);
  console.log(`  Field innings - Min: ${minFieldInnings}, Max: ${maxFieldInnings}, Avg: ${avgFieldInnings.toFixed(1)}, Range: ${maxFieldInnings - minFieldInnings}`);
  console.log(`  Playing time distribution: ${fieldInningsArray.map(i => `${Math.round(i/totalInnings*100)}%`).join(', ')}`);

  // Final comprehensive validation
  console.log(`\n🔍 FINAL COMPREHENSIVE VALIDATION:`);
  console.log('=' .repeat(40));

  let totalViolations = 0;
  for (let i = 1; i < lineup.length; i++) {
    const validation = validateRotation(
      lineup.slice(0, i),
      lineup[i],
      rules,
      availablePlayers
    );

    if (!validation.valid) {
      console.error(`❌ Inning ${lineup[i].inning} violations:`, validation.violations);
      totalViolations += validation.violations.length;
    }
  }

  if (totalViolations === 0) {
    console.log(`✅ ALL ROTATION RULES VALIDATED SUCCESSFULLY!`);
    console.log(`🎉 Generated lineup follows all rotation rules perfectly.`);
  } else {
    console.error(`❌ TOTAL VIOLATIONS FOUND: ${totalViolations}`);
    console.error(`🔧 The lineup generation algorithm may need improvements.`);
  }

  // ===== QUALITY VALIDATION: Algorithm Self-Standards =====
  if (rules.competitiveMode) {
    console.log(`🎯 QUALITY CHECK: Validating competitive lineup standards...`);
    
    const qualityIssues: string[] = [];
    let totalCompetitiveScore = 0;
    let positionsEvaluated = 0;
    
    // Helper function for position key mapping (same logic as ConstraintSolver.getPositionKey)
    const getPositionKey = (position: string): string => {
      const positionKeyMap: { [key: string]: string } = {
        'pitcher': 'pitcher',
        'catcher': 'catcher',
        'firstBase': 'firstBase',
        'secondBase': 'secondBase',
        'thirdBase': 'thirdBase',
        'shortstop': 'shortstop',
        'leftField': 'leftField',
        'centerField': 'centerField',
        'rightField': 'rightField'
      };
      return positionKeyMap[position] || position;
    };
    
    lineup.forEach((inning, inningIndex) => {
      Object.entries(inning.positions).forEach(([position, playerValue]) => {
        if (position !== 'bench' && typeof playerValue === 'string') {
          const player = availablePlayers.find(p => p.name === playerValue);
          if (player) {
            const posKey = getPositionKey(position);
            const teamRole = player.teamRoles?.[posKey];
            
            // Score this assignment
            let assignmentScore = 0;
            if (teamRole === 'go-to') assignmentScore = 100;       // Primary
            else if (teamRole === 'capable') assignmentScore = 75; // In the Mix
            else if (teamRole === 'fill-in') assignmentScore = 40; // Emergency
            else if (teamRole === 'avoid') assignmentScore = 10;   // Never
            else assignmentScore = 50; // unset
            
            totalCompetitiveScore += assignmentScore;
            positionsEvaluated++;
            
            // Flag quality issues
            if (teamRole === 'never') {
              qualityIssues.push(`${playerValue} assigned to ${position} despite "NEVER" role`);
            }
            if (teamRole === 'avoid') {
              qualityIssues.push(`${playerValue} assigned to ${position} despite "AVOID" role`);
            }
          }
        }
      });
    });
    
    const averageQualityScore = positionsEvaluated > 0 ? (totalCompetitiveScore / positionsEvaluated) : 0;
    const qualityPercentage = Math.round(averageQualityScore);
    
    console.log(`📊 LINEUP QUALITY SCORE: ${qualityPercentage}% (${totalCompetitiveScore}/${positionsEvaluated} assignments)`);
    
    // Quality standards for competitive mode
    if (qualityPercentage < 60) {
      console.error(`❌ QUALITY FAILURE: Lineup quality ${qualityPercentage}% is below acceptable standard (60%)`);
      console.error(`🔧 Quality issues found:`, qualityIssues);
      console.error(`💡 Suggestion: Review team roles - too many players may be marked as 'avoid' or 'never'`);
      
      // In development, we might want to throw an error
      // throw new LineupGenerationError('Lineup quality below competitive standards', 'QUALITY_FAILURE', { qualityPercentage, qualityIssues });
    } else if (qualityPercentage < 75) {
      console.warn(`⚠️ QUALITY WARNING: Lineup quality ${qualityPercentage}% could be improved`);
      if (qualityIssues.length > 0) {
        console.warn(`🔧 Quality issues:`, qualityIssues);
      }
    } else {
      console.log(`✅ QUALITY EXCELLENT: Lineup meets competitive standards (${qualityPercentage}%)`);
    }
  }

  return lineup;
}

export function generateInitialLineup(
  players: Player[],
  solver: ConstraintSolver,
  eligibilityCache: PlayerEligibilityCache,
  rules: LineupRules
): InningLineup {
  console.log(`🎯 Generating initial lineup with enhanced balanced assignment`);
  console.log(`📋 Rules: equalPlayingTime=${rules.equalPlayingTime}, respectPositionLockouts=${rules.respectPositionLockouts}`);

  const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase',
                         'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];

  // Use enhanced balanced assignment for better initial distribution
  console.log(`⚖️ Using enhanced balanced assignment for initial lineup`);

  // Try to use a more balanced approach similar to the optimization algorithm
  let assignments: Map<string, string> | null = null;

  // For competitive mode, use competitive assignment
  if (rules.competitiveMode) {
    console.log(`🏆 Using competitive assignment algorithm for initial lineup`);
    assignments = solver.findCompetitiveAssignment(
      fieldPositions,
      { respectPositionLockouts: rules.respectPositionLockouts },
      rules
    );
  }
  // For fair mode with multiple players, use fair assignment that respects positions
  else if (rules.equalPlayingTime && players.length > 9) {
    console.log(`⚖️ Using FAIR EQUAL PLAYING TIME assignment with position restrictions`);
    
    // Use the fair assignment algorithm that respects position eligibility
    assignments = solver.findFairValidAssignment(
      fieldPositions,
      { respectPositionLockouts: rules.respectPositionLockouts }
    );
    
    // Log the assignments
    if (assignments) {
      console.log(`⚖️ Fair assignments made:`);
      assignments.forEach((playerName, position) => {
        console.log(`  ${playerName} -> ${position}`);
      });
    }
  }
  // Fallback to standard assignment
  else {
    console.log(`📝 Using standard assignment for initial lineup`);
    assignments = solver.findValidAssignment(
      fieldPositions,
      { respectPositionLockouts: rules.respectPositionLockouts }
    );
  }

  // If assignment failed, try fallback methods
  if (!assignments) {
    console.warn(`⚠️ Primary assignment failed, trying fallback methods`);

    // Try fair assignment as fallback
    assignments = solver.findFairValidAssignment(
      fieldPositions,
      { respectPositionLockouts: rules.respectPositionLockouts }
    );

    // If still failed, try basic assignment
    if (!assignments) {
      console.warn(`⚠️ Fair assignment failed, using basic constraint solving`);
      assignments = solver.findValidAssignment(
        fieldPositions,
        { respectPositionLockouts: rules.respectPositionLockouts }
      );
    }
  }

  // Final check - if all methods failed, try emergency fallback
  if (!assignments) {
    console.error(`❌ All constraint solving methods failed. Attempting emergency fallback...`);
    
    // Log player position eligibility for debugging
    console.log(`📋 Player position eligibility:`);
    players.forEach(player => {
      const eligiblePositions = fieldPositions.filter(pos => 
        canPlayPosition(player, pos, rules.respectPositionLockouts)
      );
      console.log(`  ${player.name}: ${eligiblePositions.join(', ') || 'NO ELIGIBLE POSITIONS'}`);
    });
    
    // Emergency fallback: Just assign players to any position they can play
    assignments = new Map<string, string>();
    const availablePositions = new Set(fieldPositions);
    const assignedPlayers = new Set<string>();
    
    // First, try to assign players to positions they can play
    for (const player of players) {
      if (assignedPlayers.has(player.name)) continue;
      
      for (const position of availablePositions) {
        if (canPlayPosition(player, position, rules.respectPositionLockouts)) {
          assignments.set(position, player.name);
          availablePositions.delete(position);
          assignedPlayers.add(player.name);
          break;
        }
      }
    }
    
    // If we still don't have 9 assignments, show what positions couldn't be filled
    if (assignments.size < 9) {
      const unfilledPositions = Array.from(availablePositions);
      console.error(`❌ Emergency fallback only assigned ${assignments.size} positions`);
      console.error(`❌ Unfilled positions: ${unfilledPositions.join(', ')}`);
      
      // If respect position lockouts is on, try without it as last resort
      if (rules.respectPositionLockouts) {
        console.warn(`⚠️ Trying without position lockouts as last resort...`);
        assignments = new Map<string, string>();
        const positions = new Set(fieldPositions);
        const assigned = new Set<string>();
        
        for (const player of players) {
          if (assigned.has(player.name)) continue;
          for (const position of positions) {
            if (canPlayPosition(player, position, false)) {
              assignments.set(position, player.name);
              positions.delete(position);
              assigned.add(player.name);
              break;
            }
          }
        }
        
        if (assignments.size === 9) {
          console.warn(`✅ Successfully assigned all positions without lockouts`);
        }
      }
      
      if (assignments.size < 9) {
        throw new PositionConstraintError(
          'initial lineup - Not enough players with valid position assignments. Please check that players have positions assigned in Team Roster.',
          players.map(p => p.name),
          { respectPositionLockouts: rules.respectPositionLockouts }
        );
      }
    }
  }

  // Build the lineup from assignments
  const positions: any = {};
  for (const [pos, playerName] of assignments) {
    positions[pos] = playerName;
  }

  // Assign remaining players to bench
  const assignedPlayers = new Set(assignments.values());
  positions.bench = players
    .map(p => p.name)
    .filter(name => !assignedPlayers.has(name));

  console.log(`✅ Initial lineup generated successfully:`, {
    method: rules.competitiveMode ? 'competitive' : (rules.equalPlayingTime && players.length > 9) ? 'fair' : 'standard',
    fieldPlayers: Object.values(positions).filter(p => p !== positions.bench),
    benchPlayers: positions.bench,
    totalPlayers: players.length
  });

  return { inning: 1, positions };
}

function generateFallbackLineup(
  players: Player[],
  inning: number,
  solver: ConstraintSolver,
  eligibilityCache: PlayerEligibilityCache,
  rules: LineupRules,
  playerStats: Map<string, any>,
  maxBenchStreak: number
): InningLineup {
  // Sort players by playing time for fairness
  const sortedPlayers = [...players].sort((a, b) => {
    const aStats = playerStats.get(a.id)!;
    const bStats = playerStats.get(b.id)!;
    
    // Prioritize players who have exceeded bench streak limit
    if (aStats.benchStreak >= maxBenchStreak && bStats.benchStreak < maxBenchStreak) return -1;
    if (bStats.benchStreak >= maxBenchStreak && aStats.benchStreak < maxBenchStreak) return 1;
    
    // Then by total field innings
    return aStats.fieldInnings - bStats.fieldInnings;
  });
  
  const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 
                         'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
  
  const assignments = solver.findValidAssignment(
    fieldPositions,
    { respectPositionLockouts: rules.respectPositionLockouts }
  );
  
  if (!assignments) {
    throw new PositionConstraintError(
      `inning ${inning}`,
      players.map(p => p.name),
      { respectPositionLockouts: rules.respectPositionLockouts }
    );
  }
  
  const positions: any = {};
  for (const [pos, playerName] of assignments) {
    positions[pos] = playerName;
  }
  
  const assignedPlayers = new Set(assignments.values());
  positions.bench = players
    .map(p => p.name)
    .filter(name => !assignedPlayers.has(name));
  
  return { inning, positions };
}

function updatePlayerStats(
  lineup: InningLineup,
  playerStats: Map<string, any>,
  players: Player[],
  inning: number
): void {
  const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 
                         'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
  
  // Update field players
  for (const pos of fieldPositions) {
    const playerName = (lineup.positions as any)[pos];
    if (playerName) {
      // Find player object to get ID for stats lookup
      const player = players.find(p => p.name === playerName);
      if (!player) continue;
      
      const stats = playerStats.get(player.id)!;
      stats.fieldInnings++;
      stats.benchStreak = 0;
      stats.lastPositions.set(pos, inning);
      
      if (pos === 'pitcher') {
        stats.inningsPitched++;
      }
    }
  }
  
  // Update bench players
  for (const playerName of lineup.positions.bench) {
    // Find player object to get ID for stats lookup
    const player = players.find(p => p.name === playerName);
    if (!player) continue;
    
    const stats = playerStats.get(player.id)!;
    stats.benchInnings++;
    stats.benchStreak++;
  }
}

// Enhanced createInitialInningLineup that accepts rotation rules
export function createInitialInningLineup(
  availablePlayers: Player[],
  rules?: Partial<LineupRules>
): InningLineup {
  if (availablePlayers.length < 8) {
    throw new InsufficientPlayersError(8, availablePlayers.length);
  }

  const eligibilityCache = new PlayerEligibilityCache();
  const solver = new ConstraintSolver(availablePlayers, eligibilityCache);

  // Use provided rules or defaults
  const lineupRules: LineupRules = {
    respectPositionLockouts: true,
    limitBenchTime: true,
    maxConsecutiveBenchInnings: 2,
    allowPitcherRotation: true,
    allowCatcherRotation: true,
    equalPlayingTime: true,
    rotateLineupEvery: 1,
    rotatePitcherEvery: 2,
    ...rules
  };

  console.log('🎯 Creating initial inning lineup with rules:', lineupRules);

  try {
    return generateInitialLineup(
      availablePlayers,
      solver,
      eligibilityCache,
      lineupRules
    );
  } catch (error) {
    console.warn('Enhanced initial lineup generation failed, using simple fallback:', error);

    // Fallback to simple assignment without position restrictions
    try {
      return generateInitialLineup(
        availablePlayers,
        solver,
        eligibilityCache,
        { ...lineupRules, respectPositionLockouts: false }
      );
    } catch (fallbackError) {
      console.error('Even fallback initial lineup generation failed:', fallbackError);

      // Emergency fallback: manually assign players to positions
      return createEmergencyInitialLineup(availablePlayers);
    }
  }
}

// Emergency fallback function for when all constraint solving fails
function createEmergencyInitialLineup(availablePlayers: Player[]): InningLineup {
  console.log('🚨 Using emergency initial lineup generation');

  const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase',
                         'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];

  const positions: any = {};
  const remaining = [...availablePlayers];

  // Ensure we have at least 8 players (this should be checked earlier, but just in case)
  if (remaining.length < 8) {
    throw new InsufficientPlayersError(8, remaining.length);
  }

  // Assign players to field positions, cycling through available players if needed
  fieldPositions.forEach((pos, index) => {
    const playerIndex = index % remaining.length;
    positions[pos] = remaining[playerIndex].name;
  });

  // Put remaining players on bench (if we have more than 9 players)
  if (remaining.length > 9) {
    positions.bench = remaining.slice(9).map(p => p.name);
  } else {
    positions.bench = [];
  }

  console.log('Emergency lineup created:', positions);

  return { inning: 1, positions };
}


// Generate lineup starting from a manually set first inning
export function generateLineupFromFirstInning(
  firstInning: InningLineup,
  availablePlayers: Player[],
  totalInnings: number,
  rules: LineupRules
): InningLineup[] {
  console.log(`🚀 GENERATING LINEUP FROM FIRST INNING - ${totalInnings} innings for ${availablePlayers.length} players`);
  console.log(`📋 First inning (manual):`, firstInning.positions);
  console.log(`📋 Rules:`, {
    limitBenchTime: rules.limitBenchTime,
    maxConsecutiveBenchInnings: rules.maxConsecutiveBenchInnings,
    allowPitcherRotation: rules.allowPitcherRotation,
    allowCatcherRotation: rules.allowCatcherRotation,
    respectPositionLockouts: rules.respectPositionLockouts,
    equalPlayingTime: rules.equalPlayingTime,
    rotateLineupEvery: rules.rotateLineupEvery,
    rotatePitcherEvery: rules.rotatePitcherEvery
  });

  if (availablePlayers.length < 8) {
    throw new InsufficientPlayersError(8, availablePlayers.length);
  }

  if (totalInnings === 1) {
    return [firstInning];
  }

  // Generate subsequent innings starting from the manually set first inning
  console.log(`🔄 Generating lineup starting from manually set first inning`);
  console.log(`📋 First inning positions:`, firstInning.positions);

  try {
    // Start with the manually set first inning
    const lineup: InningLineup[] = [firstInning];

    // Use the enhanced LineupRotator to generate subsequent innings
    const random = new SeededRandom(rules._randomSeed);
    const eligibilityCache = new PlayerEligibilityCache();
    const rotator = new LineupRotator(rules, eligibilityCache, random);

    let currentInning = firstInning;

    // Generate each subsequent inning using the enhanced rotation algorithm
    for (let inning = 2; inning <= totalInnings; inning++) {
      console.log(`🔄 Generating inning ${inning} from previous inning`);

      // Build player stats from previous innings for better rotation decisions
      const playerStats = new Map<string, { fieldInnings: number; benchInnings: number }>();
      availablePlayers.forEach(player => {
        playerStats.set(player.id, { fieldInnings: 0, benchInnings: 0 });
      });

      // Count innings played so far
      lineup.forEach(prevInning => {
        Object.values(prevInning.positions).forEach(playerName => {
          if (typeof playerName === 'string' && playerName) {
            // Find player object to get ID for stats lookup
            const player = availablePlayers.find(p => p.name === playerName);
            if (player) {
              const stats = playerStats.get(player.id);
              if (stats) stats.fieldInnings++;
            }
          }
        });

        if (Array.isArray(prevInning.positions.bench)) {
          prevInning.positions.bench.forEach(playerName => {
            if (playerName) {
              // Find player object to get ID for stats lookup
              const player = availablePlayers.find(p => p.name === playerName);
              if (player) {
                const stats = playerStats.get(player.id);
                if (stats) stats.benchInnings++;
              }
            }
          });
        }
      });

      const nextInning = rotator.rotateLineup(
        currentInning,
        availablePlayers,
        inning,
        playerStats
      );

      lineup.push(nextInning);
      currentInning = nextInning;
    }

    console.log(`✅ Successfully generated lineup from manually set first inning`);
    console.log(`📋 Final lineup summary:`, lineup.map(inning => ({
      inning: inning.inning,
      pitcher: inning.positions.pitcher,
      bench: inning.positions.bench
    })));

    return lineup;

  } catch (error) {
    console.error(`❌ Enhanced generation failed, falling back to simple rotation:`, error);

    // Validate the first inning before attempting fallback
    const validation = validateLineup(firstInning, availablePlayers, rules);
    if (!validation.valid) {
      console.error(`❌ First inning is invalid, cannot proceed with fallback:`, validation.errors);
      throw new LineupGenerationError(
        'Invalid first inning lineup',
        'INVALID_FIRST_INNING',
        { inning: 1, constraints: validation.errors }
      );
    }

    // Fallback: Use the old approach with rotatePlayersForNextInning
    const lineup: InningLineup[] = [firstInning];
    let currentInning = firstInning;

    for (let inning = 2; inning <= totalInnings; inning++) {
      console.log(`🔄 Fallback: Generating inning ${inning} using simple rotation`);

      try {
        const nextInning = rotatePlayersForNextInning(currentInning, availablePlayers, rules);

        // Validate the generated inning
        const inningValidation = validateLineup(nextInning, availablePlayers, rules);
        if (!inningValidation.valid) {
          console.warn(`⚠️ Generated inning ${inning} has validation warnings:`, inningValidation.errors);
        }

        lineup.push(nextInning);
        currentInning = nextInning;
      } catch (rotationError) {
        console.error(`❌ Rotation failed for inning ${inning}:`, rotationError);

        // Final fallback: copy previous inning with updated inning number
        const fallbackInning = {
          inning: inning,
          positions: { ...currentInning.positions }
        };

        console.log(`🚨 Using emergency fallback for inning ${inning} - copying previous inning`);
        lineup.push(fallbackInning);
        currentInning = fallbackInning;
      }
    }

    console.log(`⚠️ Used fallback approach for lineup generation`);
    return lineup;
  }
}

// Keep existing rotatePlayersForNextInning for backward compatibility
export function rotatePlayersForNextInning(
  previousInning: InningLineup,
  availablePlayers: Player[],
  rules: LineupRules,
  playerPositionHistory?: Record<string, {
    fieldInnings: number,
    benchInnings: number
  }>
): InningLineup {
  const random = new SeededRandom(rules._randomSeed);
  const eligibilityCache = new PlayerEligibilityCache();
  const rotator = new LineupRotator(rules, eligibilityCache, random);
  
  const statsMap = playerPositionHistory ? 
    new Map(Object.entries(playerPositionHistory)) : 
    undefined;
  
  try {
    const rotatedLineup = rotator.rotateLineup(
      previousInning,
      availablePlayers,
      previousInning.inning + 1,
      statsMap
    );

    // Validate the rotation (basic validation without full lineup history)
    const validation = validateRotation(
      [previousInning],
      rotatedLineup,
      rules,
      availablePlayers
    );

    if (!validation.valid) {
      console.warn(`⚠️ Rotation validation warnings for inning ${rotatedLineup.inning}:`);
      validation.violations.forEach(violation => {
        console.warn(`  - ${violation}`);
      });
    }

    return rotatedLineup;
  } catch (error) {
    console.error('Rotation failed:', error);
    // Fallback to copying previous lineup
    const fallbackLineup = {
      inning: previousInning.inning + 1,
      positions: { ...previousInning.positions }
    };

    // Validate fallback lineup
    const fallbackValidation = validateRotation(
      [previousInning],
      fallbackLineup,
      rules,
      availablePlayers
    );

    if (!fallbackValidation.valid) {
      console.warn(`⚠️ Fallback lineup validation warnings for inning ${fallbackLineup.inning}:`);
      fallbackValidation.violations.forEach(violation => {
        console.warn(`  - ${violation}`);
      });
    }

    return fallbackLineup;
  }
}

// ===== OPTIMIZED DATABASE FUNCTIONS (Fix #9) =====
export async function savePlayerPositionHistory(
  lineup: Lineup,
  inning: InningLineup,
  playerMap: Map<string, Player>
): Promise<void> {
  try {
    const { data: userData } = await supabase.auth.getUser();
    const userId = userData.user?.id;

    if (!userId) {
      console.error("User not authenticated, cannot save position history");
      return;
    }
    
    // Critical fix: verify lineup exists in database before attempting to save history
    if (!lineup.id) {
      console.log("⏸️ Skipping position history save - lineup has no ID");
      return;
    }
    
    const { data: existingLineup, error: checkError } = await supabase
      .from('lineups')
      .select('id')
      .eq('id', lineup.id)
      .single();
    
    if (checkError || !existingLineup) {
      console.log(`⏸️ Skipping position history save - lineup ${lineup.id} does not exist in database yet`);
      return;
    }

    const positionRecords = [];
    const fieldPositions = Object.keys(POSITION_CONFIG)
      .filter(k => !['utility'].includes(k));

    for (const positionKey of fieldPositions) {
      const playerName = (inning.positions as any)[positionKey];
      if (playerName) {
        const player = playerMap.get(playerName);
        if (player) {
          positionRecords.push({
            player_id: player.id,
            lineup_id: lineup.id,
            inning_number: inning.inning,
            position: positionKey,
            game_date: lineup.gameDate,
            user_id: userId
          });
        }
      }
    }

    inning.positions.bench.forEach((playerName, index) => {
      if (playerName) {
        const player = playerMap.get(playerName);
        if (player) {
          positionRecords.push({
            player_id: player.id,
            lineup_id: lineup.id,
            inning_number: inning.inning,
            position: `bench${index + 1}`,
            game_date: lineup.gameDate,
            user_id: userId
          });
        }
      }
    });

    if (positionRecords.length > 0) {
      console.log(`💾 Attempting to save ${positionRecords.length} position history records for lineup ${lineup.id}, inning ${inning.inning}`);

      try {
        // Use upsert with proper conflict resolution and ignore duplicates to prevent conflicts
        const { error: upsertError } = await supabase
          .from('player_position_history')
          .upsert(positionRecords, {
            onConflict: 'lineup_id,player_id,inning_number',
            ignoreDuplicates: true // Changed to true to prevent duplicate conflicts
          });

        if (upsertError) {
          console.error("Error upserting player position history:", upsertError);
          console.log("Attempting individual record processing...");

          // Try processing records individually to identify problematic ones
          let successCount = 0;
          for (const record of positionRecords) {
            try {
              const { error: singleError } = await supabase
                .from('player_position_history')
                .upsert([record], {
                  onConflict: 'lineup_id,player_id,inning_number',
                  ignoreDuplicates: false
                });

              if (!singleError) {
                successCount++;
              } else {
                console.warn(`Failed to save record for player ${record.player_id}, position ${record.position}:`, singleError);
              }
            } catch (singleRecordError) {
              console.warn(`Exception saving individual record:`, singleRecordError);
            }
          }

          if (successCount > 0) {
            console.log(`✅ Saved ${successCount}/${positionRecords.length} player position history records for lineup ${lineup.id}`);
          }
        } else {
          console.log(`✅ Upserted ${positionRecords.length} player position history records for lineup ${lineup.id}`);
        }
      } catch (error) {
        console.error("Exception in savePlayerPositionHistory:", error);
        // Don't throw - this is not critical for lineup functionality
      }
    }
  } catch (error) {
    console.error("Error in savePlayerPositionHistory:", error);
  }
}

export async function getPlayerPositionStats(
  playerId: string,
  options: {
    startDate?: string;
    endDate?: string;
    limit?: number;
    groupBy?: 'game' | 'week' | 'month';
  } = {}
): Promise<{
  totalInnings: number;
  fieldInnings: number;
  benchInnings: number;
  positionCounts: Record<string, number>;
  recentGames?: any[];
}> {
  try {
    // Use SQL aggregation for better performance
    let query = supabase
      .from('player_position_history')
      .select('position, game_date, lineup_id')
      .eq('player_id', playerId);

    if (options.startDate) {
      query = query.gte('game_date', options.startDate);
    }
    if (options.endDate) {
      query = query.lte('game_date', options.endDate);
    }
    if (options.limit) {
      query = query.limit(options.limit);
    }

    const { data, error } = await query;

    if (error) {
      console.error(`Error fetching player position stats:`, error);
      return {
        totalInnings: 0,
        fieldInnings: 0,
        benchInnings: 0,
        positionCounts: {}
      };
    }

    const positionCounts: Record<string, number> = {};
    let benchInnings = 0;
    let fieldInnings = 0;

    data.forEach(record => {
      const position = record.position;
      positionCounts[position] = (positionCounts[position] || 0) + 1;

      if (position.startsWith('bench')) {
        benchInnings++;
      } else {
        fieldInnings++;
      }
    });

    return {
      totalInnings: data.length,
      fieldInnings,
      benchInnings,
      positionCounts,
      recentGames: options.limit ? data : undefined
    };
  } catch (error) {
    console.error("Error in getPlayerPositionStats:", error);
    return {
      totalInnings: 0,
      fieldInnings: 0,
      benchInnings: 0,
      positionCounts: {}
    };
  }
}

// Legacy function for backward compatibility
export async function getPlayerPositionHistory(
  playerId: string,
  startDate?: string,
  endDate?: string
): Promise<{
  totalInnings: number;
  fieldInnings: number;
  benchInnings: number;
  positionCounts: Record<string, number>;
}> {
  return getPlayerPositionStats(playerId, { startDate, endDate });
}

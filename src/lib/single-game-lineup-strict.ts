/**
 * TRULY STRICT Single Game Lineup Generator
 * 
 * This version NEVER violates position restrictions
 * 
 * Special handling for catcher position:
 * - Catchers can play consecutive innings when rotation would cause shortages
 * - Uses pitcher rotation frequency for catcher rotation by default
 */

import { 
  Player, 
  InningLineup, 
  LineupRules,
  POSITION_CONFIG,
  PositionKey
} from './utils-enhanced';

export function generateSingleGameLineupStrict(
  players: Player[],
  totalInnings: number,
  rules: LineupRules
): InningLineup[] {
  console.log(`🔒 STRICT LINEUP GENERATION - ${totalInnings} innings for ${players.length} players`);
  
  // First, analyze position restrictions
  const positionAnalysis = analyzePositionRestrictions(players);
  console.log('📊 Position Analysis:', positionAnalysis);
  
  // Check if lineup is even possible
  const impossible = positionAnalysis.impossiblePositions;
  if (impossible.length > 0) {
    throw new Error(`IMPOSSIBLE LINEUP: No players available for positions: ${impossible.join(', ')}`);
  }
  
  // Identify star players (those who are primary at multiple positions)
  const starPlayers = identifyStarPlayers(players);
  console.log(`⭐ Star players: ${starPlayers.join(', ')}`);
  
  // Analyze catcher availability for smart rotation
  const catcherCount = positionAnalysis.positionCounts.get('catcher') || 0;
  const catcherRotationFrequency = rules.rotatePitcherEvery || 3; // Use pitcher frequency
  const estimatedCatcherSlots = Math.ceil(totalInnings / catcherRotationFrequency);
  const shouldBeLenientWithCatchers = catcherCount < estimatedCatcherSlots;
  
  if (shouldBeLenientWithCatchers) {
    console.log(`⚾ Limited catchers detected: ${catcherCount} catchers for ~${estimatedCatcherSlots} rotation slots`);
    console.log(`⚾ Will allow catchers to play consecutive innings when needed`);
  }
  
  const lineup: InningLineup[] = [];
  
  // Generate each inning
  for (let inning = 1; inning <= totalInnings; inning++) {
    const shouldRotateLineup = inning > 1 && ((inning - 1) % (rules.rotateLineupEvery || 1)) === 0;
    const shouldRotatePitcher = inning > 1 && ((inning - 1) % (rules.rotatePitcherEvery || 3)) === 0;
    // For catcher, use pitcher rotation frequency but be lenient if we have limited catchers
    let shouldRotateCatcher = inning > 1 && ((inning - 1) % (rules.rotatePitcherEvery || 3)) === 0;
    
    // Override catcher rotation if we have limited catchers
    if (shouldBeLenientWithCatchers && shouldRotateCatcher) {
      // Check if we really need to rotate the catcher
      const catchersUsed = new Set<string>();
      lineup.forEach(prevInning => {
        if (prevInning.positions.catcher) {
          catchersUsed.add(prevInning.positions.catcher);
        }
      });
      
      // If we haven't used all available catchers yet, we can rotate
      // Otherwise, be lenient and keep the same catcher
      if (catchersUsed.size >= catcherCount) {
        console.log(`⚾ Overriding catcher rotation - all ${catcherCount} catchers have played, allowing consecutive innings`);
        shouldRotateCatcher = false;
      }
    }
    
    console.log(`📋 Inning ${inning} rotation check:`, {
      shouldRotateLineup,
      shouldRotatePitcher,
      shouldRotateCatcher,
      rotateLineupEvery: rules.rotateLineupEvery,
      rotatePitcherEvery: rules.rotatePitcherEvery,
      catcherCount,
      shouldBeLenientWithCatchers
    });
    
    const inningLineup = generateStrictInning(
      players, 
      inning, 
      lineup, 
      shouldRotateLineup,
      shouldRotatePitcher,
      shouldRotateCatcher,
      starPlayers,
      rules
    );
    lineup.push(inningLineup);
  }
  
  return lineup;
}

function identifyStarPlayers(players: Player[]): string[] {
  const starPlayers: string[] = [];
  
  players.forEach(player => {
    let primaryCount = 0;
    
    if (player.teamRoles) {
      Object.values(player.teamRoles).forEach(role => {
        if (role === 'go-to' || role === 'primary') {
          primaryCount++;
        }
      });
    }
    
    // Star player if primary at 2+ positions
    if (primaryCount >= 2) {
      starPlayers.push(player.name);
    }
  });
  
  return starPlayers;
}

function analyzePositionRestrictions(players: Player[]): {
  positionCounts: Map<string, number>;
  impossiblePositions: string[];
  playerEligibility: Map<string, string[]>;
} {
  const positionCounts = new Map<string, number>();
  const playerEligibility = new Map<string, string[]>();
  
  // Initialize position counts
  Object.keys(POSITION_CONFIG).forEach(pos => {
    positionCounts.set(pos, 0);
  });
  
  // Count eligible players for each position
  players.forEach(player => {
    const eligible: string[] = [];
    
    Object.entries(POSITION_CONFIG).forEach(([posKey, posConfig]) => {
      const role = player.teamRoles?.[posKey];
      
      // STRICT: 'avoid' means NEVER
      if (role !== 'avoid' && role !== 'never') {
        eligible.push(posKey);
        positionCounts.set(posKey, (positionCounts.get(posKey) || 0) + 1);
      }
    });
    
    playerEligibility.set(player.name, eligible);
  });
  
  // Find impossible positions
  const impossiblePositions = Array.from(positionCounts.entries())
    .filter(([pos, count]) => count === 0)
    .map(([pos]) => pos);
  
  return {
    positionCounts,
    impossiblePositions,
    playerEligibility
  };
}

function generateStrictInning(
  players: Player[],
  inningNumber: number,
  previousInnings: InningLineup[],
  shouldRotateLineup: boolean,
  shouldRotatePitcher: boolean,
  shouldRotateCatcher: boolean,
  starPlayers: string[],
  rules: LineupRules
): InningLineup {
  console.log(`\n🏟️ Generating STRICT inning ${inningNumber} (rotate lineup: ${shouldRotateLineup}, rotate pitcher: ${shouldRotatePitcher}, rotate catcher: ${shouldRotateCatcher})`);
  
  const positions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 
                    'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
  
  // If not first inning and should rotate, force some changes
  let forcedBenchPlayers: string[] = [];
  let previousLineup: InningLineup | null = null;
  
  if (inningNumber > 1) {
    previousLineup = previousInnings[previousInnings.length - 1];
    
    if (shouldRotateLineup) {
      // Force rotation: bench some field players
      const currentFieldPlayers = positions
        .map(pos => previousLineup!.positions[pos])
        .filter(name => name && typeof name === 'string');
      
      // Calculate who's been on field too long
      const playerStats = calculatePlayerStats(players, previousInnings);
      const fieldPlayersWithStats = currentFieldPlayers.map(name => {
        const player = players.find(p => p.name === name);
        const stats = player ? playerStats.get(player.id) : null;
        return { name, stats };
      }).filter(p => p.stats);
      
      // Sort by who's played most
      fieldPlayersWithStats.sort((a, b) => (b.stats?.fieldInnings || 0) - (a.stats?.fieldInnings || 0));
      
      // Force bench 2-3 players who've played the most (but not pitcher if shouldn't rotate)
      const toBench = fieldPlayersWithStats
        .filter(p => shouldRotatePitcher || p.name !== previousLineup!.positions.pitcher)
        .slice(0, Math.min(3, Math.floor(players.length / 3)));
      
      forcedBenchPlayers = toBench.map(p => p.name);
      console.log(`🔄 Forcing rotation - benching: ${forcedBenchPlayers.join(', ')}`);
    }
  }
  
  // Calculate player stats
  const playerStats = calculatePlayerStats(players, previousInnings);
  
  // Sort players by need with special handling for star players and cross-game priority
  const sortedPlayers = [...players].sort((a, b) => {
    const aStats = playerStats.get(a.id) || { fieldInnings: 0, benchInnings: 0 };
    const bStats = playerStats.get(b.id) || { fieldInnings: 0, benchInnings: 0 };
    
    // Check for cross-game priority adjustments (used in multi-game series)
    const aPriority = (a as any)._priorityAdjustment || 0;
    const bPriority = (b as any)._priorityAdjustment || 0;
    
    // If significant priority difference (from previous games), use that first
    if (Math.abs(aPriority - bPriority) > 0.5) {
      return bPriority - aPriority; // Higher priority = more need to play
    }
    
    // If player was on bench last inning, give them priority
    const aWasBenched = previousLineup && previousLineup.positions.bench.includes(a.name);
    const bWasBenched = previousLineup && previousLineup.positions.bench.includes(b.name);
    
    if (aWasBenched && !bWasBenched) return -1;
    if (bWasBenched && !aWasBenched) return 1;
    
    // Give star players a boost if they haven't played much
    const aIsStar = starPlayers.includes(a.name);
    const bIsStar = starPlayers.includes(b.name);
    
    // If one is a star and has significantly less playing time, prioritize them
    if (aIsStar && !bIsStar && aStats.fieldInnings < bStats.fieldInnings + 2) {
      return -1;
    }
    if (bIsStar && !aIsStar && bStats.fieldInnings < aStats.fieldInnings + 2) {
      return 1;
    }
    
    // NEW: Give a small boost to players with focused position assignments
    // Players with 2-3 position assignments get priority over those with many
    const aPositionCount = Object.values(a.teamRoles || {}).filter(role => 
      role && role !== 'never' && role !== 'avoid').length;
    const bPositionCount = Object.values(b.teamRoles || {}).filter(role => 
      role && role !== 'never' && role !== 'avoid').length;
    
    // Boost for focused players (2-3 positions ideal)
    const aFocusBoost = (aPositionCount >= 2 && aPositionCount <= 3) ? -0.3 : 0;
    const bFocusBoost = (bPositionCount >= 2 && bPositionCount <= 3) ? -0.3 : 0;
    
    // Otherwise sort by playing time (including any remaining priority adjustment and focus boost)
    const aScore = aStats.fieldInnings - aPriority * 2 + aFocusBoost;
    const bScore = bStats.fieldInnings - bPriority * 2 + bFocusBoost;
    
    if (Math.abs(aScore - bScore) < 0.1) {
      return Math.random() - 0.5;
    }
    
    return aScore - bScore;
  });
  
  // Assign positions using strict rules with role priorities
  const assignments = new Map<string, string>();
  const assignedPlayers = new Set<string>();
  
  // Handle pitcher rotation specially
  const pitcherPlan = (rules as any)._pitcherPlan;
  
  if (pitcherPlan?.plannedPitchers && pitcherPlan.plannedPitchers.length > 0) {
    // Use pre-planned pitcher for this inning
    const plannedPitcher = pitcherPlan.plannedPitchers.find(pp => 
      pp.targetInnings.includes(inningNumber)
    );
    
    if (plannedPitcher && !forcedBenchPlayers.includes(plannedPitcher.player.name)) {
      assignments.set('pitcher', plannedPitcher.player.name);
      assignedPlayers.add(plannedPitcher.player.name);
      console.log(`  Using planned pitcher: ${plannedPitcher.player.name} (${plannedPitcher.role})`);
    } else {
      console.log(`  No planned pitcher found for inning ${inningNumber}, will use normal rotation`);
    }
  } else if (previousLineup && !shouldRotatePitcher) {
    // Fallback to normal rotation logic
    const currentPitcher = previousLineup.positions.pitcher;
    if (currentPitcher && !forcedBenchPlayers.includes(currentPitcher)) {
      assignments.set('pitcher', currentPitcher);
      assignedPlayers.add(currentPitcher);
      console.log(`  Keeping same pitcher: ${currentPitcher}`);
    }
  }
  
  // Handle catcher rotation specially - keep same catcher if we shouldn't rotate
  if (previousLineup && !shouldRotateCatcher) {
    const currentCatcher = previousLineup.positions.catcher;
    if (currentCatcher && !forcedBenchPlayers.includes(currentCatcher)) {
      // Check if the current catcher can still catch (not forced to bench)
      const catcherPlayer = players.find(p => p.name === currentCatcher);
      if (catcherPlayer && !assignedPlayers.has(currentCatcher)) {
        assignments.set('catcher', currentCatcher);
        assignedPlayers.add(currentCatcher);
        console.log(`  Keeping same catcher: ${currentCatcher} (catcher rotation not needed)`);
      }
    }
  }
  
  // Try to assign each position, prioritizing by role
  for (const position of positions) {
    // Skip if already assigned (like pitcher)
    if (assignments.has(position)) continue;
    
    let assigned = false;
    
    // Group available players by their role for this position
    const availableByRole = {
      primary: [] as Player[],
      inTheMix: [] as Player[],
      emergency: [] as Player[],
      unset: [] as Player[]
    };
    
    for (const player of sortedPlayers) {
      if (assignedPlayers.has(player.name)) continue;
      
      // Skip if forced to bench
      if (forcedBenchPlayers.includes(player.name)) continue;
      
      const role = player.teamRoles?.[position];
      
      // STRICT: Never assign if role is 'avoid' or 'never'
      if (role === 'avoid' || role === 'never') {
        continue;
      }
      
      // Categorize by role
      if (role === 'go-to' || role === 'primary') {
        availableByRole.primary.push(player);
      } else if (role === 'capable' || role === 'in the mix') {
        availableByRole.inTheMix.push(player);
      } else if (role === 'fill-in' || role === 'emergency') {
        availableByRole.emergency.push(player);
      } else {
        availableByRole.unset.push(player);
      }
    }
    
    // Try to assign in priority order: primary > in the mix > unset > emergency
    const priorityOrder = [
      availableByRole.primary,
      availableByRole.inTheMix,
      availableByRole.unset,
      availableByRole.emergency
    ];
    
    // Special handling for catcher rotation to ensure fairness across all role levels
    if (position === 'catcher' && shouldRotateCatcher) {
      const currentCatcher = previousLineup?.positions.catcher;
      
      // Get all available catchers across all priority groups
      const allAvailableCatchers = [
        ...availableByRole.primary,
        ...availableByRole.inTheMix,
        ...availableByRole.unset,
        ...availableByRole.emergency
      ];
      
      if (currentCatcher && allAvailableCatchers.length > 1) {
        const currentCatcherInAvailable = allAvailableCatchers.find(p => p.name === currentCatcher);
        const otherCatchers = allAvailableCatchers.filter(p => p.name !== currentCatcher);
        
        if (currentCatcherInAvailable && otherCatchers.length > 0) {
          // Rotate to a different catcher, prioritize by role hierarchy then playing time need
          let rotationCatcher = otherCatchers[0]; // First non-current catcher (already sorted)
          
          assignments.set(position, rotationCatcher.name);
          assignedPlayers.add(rotationCatcher.name);
          assigned = true;
          
          console.log(`  🔄 Rotating catcher from ${currentCatcher} to ${rotationCatcher.name} for fairness (role: ${rotationCatcher.teamRoles?.[position] || 'unset'})`);
        }
      }
    }
    
    // Standard assignment if not already assigned (or not catcher rotation)
    if (!assigned) {
      for (const group of priorityOrder) {
        if (group.length > 0) {
          const player = group[0]; // First player in priority group (sorted by playing time need)
          assignments.set(position, player.name);
          assignedPlayers.add(player.name);
          assigned = true;
          
          console.log(`  Assigned ${player.name} to ${position} (role: ${player.teamRoles?.[position] || 'unset'})`);
          break;
        }
      }
    }
    
    if (!assigned) {
      throw new Error(`Cannot assign position ${position} in inning ${inningNumber} without violating restrictions`);
    }
  }
  
  // Assign remaining players to bench
  const benchPlayers = players
    .filter(p => !assignedPlayers.has(p.name))
    .map(p => p.name);
  
  return {
    inning: inningNumber,
    positions: {
      pitcher: assignments.get('pitcher')!,
      catcher: assignments.get('catcher')!,
      firstBase: assignments.get('firstBase')!,
      secondBase: assignments.get('secondBase')!,
      shortstop: assignments.get('shortstop')!,
      thirdBase: assignments.get('thirdBase')!,
      leftField: assignments.get('leftField')!,
      centerField: assignments.get('centerField')!,
      rightField: assignments.get('rightField')!,
      bench: benchPlayers
    }
  };
}

function calculatePlayerStats(
  players: Player[],
  previousInnings: InningLineup[]
): Map<string, { fieldInnings: number; benchInnings: number }> {
  const stats = new Map<string, { fieldInnings: number; benchInnings: number }>();
  
  // Initialize stats
  players.forEach(player => {
    stats.set(player.id, { fieldInnings: 0, benchInnings: 0 });
  });
  
  // Count from previous innings
  previousInnings.forEach(inning => {
    // Count field innings
    Object.values(inning.positions).forEach(playerName => {
      if (typeof playerName === 'string') {
        const player = players.find(p => p.name === playerName);
        if (player) {
          const playerStats = stats.get(player.id)!;
          playerStats.fieldInnings++;
        }
      }
    });
    
    // Count bench innings
    if (Array.isArray(inning.positions.bench)) {
      inning.positions.bench.forEach(playerName => {
        const player = players.find(p => p.name === playerName);
        if (player) {
          const playerStats = stats.get(player.id)!;
          playerStats.benchInnings++;
        }
      });
    }
  });
  
  return stats;
}
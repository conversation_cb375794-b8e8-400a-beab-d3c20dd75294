/**
 * Strict Constraint Solver that NEVER violates 'avoid' restrictions
 */

import { Player, POSITION_CONFIG, PositionKey } from './utils-enhanced';

export class StrictConstraintSolver {
  constructor(
    private players: Player[]
  ) {}

  /**
   * Find valid assignments that NEVER violate 'avoid' restrictions
   */
  findStrictAssignment(
    positions: string[],
    existingAssignments: Map<string, string> = new Map()
  ): Map<string, string> | null {
    console.log('🔒 STRICT CONSTRAINT SOLVER: Starting assignment');
    
    // First, identify which players can play which positions
    const eligibilityMap = this.buildStrictEligibilityMap();
    
    // Log eligibility for debugging
    console.log('📋 Player eligibility:');
    eligibilityMap.forEach((positions, playerName) => {
      console.log(`  ${playerName}: ${Array.from(positions).join(', ')}`);
    });
    
    // Try to find a valid assignment
    return this.backtrack(
      positions,
      new Map(existingAssignments),
      eligibilityMap
    );
  }

  private buildStrictEligibilityMap(): Map<string, Set<string>> {
    const eligibilityMap = new Map<string, Set<string>>();
    
    for (const player of this.players) {
      const eligiblePositions = new Set<string>();
      
      for (const [posKey, posConfig] of Object.entries(POSITION_CONFIG)) {
        // Check if player can play this position
        const role = player.teamRoles?.[posKey];
        
        // STRICT: Never allow 'avoid' roles
        if (role === 'avoid') {
          console.log(`  ❌ ${player.name} CANNOT play ${posKey} (role: avoid)`);
        } else {
          eligiblePositions.add(posKey);
        }
      }
      
      eligibilityMap.set(player.name, eligiblePositions);
    }
    
    return eligibilityMap;
  }

  private backtrack(
    remainingPositions: string[],
    currentAssignments: Map<string, string>,
    eligibilityMap: Map<string, Set<string>>
  ): Map<string, string> | null {
    // Base case: all positions assigned
    if (remainingPositions.length === 0) {
      return currentAssignments;
    }

    const [currentPos, ...restPositions] = remainingPositions;
    
    // Get available players (not yet assigned)
    const assignedPlayers = new Set(currentAssignments.values());
    const availablePlayers = this.players.filter(p => !assignedPlayers.has(p.name));
    
    // Sort players by how many positions they can play (fewer = higher priority)
    // This helps ensure we don't paint ourselves into a corner
    const sortedPlayers = availablePlayers.sort((a, b) => {
      const aPositions = eligibilityMap.get(a.name)?.size || 0;
      const bPositions = eligibilityMap.get(b.name)?.size || 0;
      return aPositions - bPositions;
    });
    
    // Try each available player
    for (const player of sortedPlayers) {
      const eligiblePositions = eligibilityMap.get(player.name);
      
      // STRICT: Only assign if player is eligible for this position
      if (eligiblePositions?.has(currentPos)) {
        // Make assignment
        currentAssignments.set(currentPos, player.name);
        
        // Recurse
        const result = this.backtrack(restPositions, currentAssignments, eligibilityMap);
        if (result) {
          return result;
        }
        
        // Backtrack
        currentAssignments.delete(currentPos);
      }
    }
    
    // No valid assignment found
    console.warn(`⚠️ Cannot assign position ${currentPos} without violating restrictions`);
    return null;
  }

  /**
   * Get a detailed report of why assignment failed
   */
  getFailureReport(positions: string[]): string {
    const report: string[] = ['STRICT ASSIGNMENT FAILURE REPORT'];
    report.push('=' .repeat(50));
    
    // Count how many players can play each position
    const positionCounts = new Map<string, number>();
    const positionPlayers = new Map<string, string[]>();
    
    for (const pos of positions) {
      positionCounts.set(pos, 0);
      positionPlayers.set(pos, []);
    }
    
    for (const player of this.players) {
      for (const pos of positions) {
        const role = player.teamRoles?.[pos];
        if (role !== 'avoid') {
          positionCounts.set(pos, (positionCounts.get(pos) || 0) + 1);
          positionPlayers.get(pos)?.push(player.name);
        }
      }
    }
    
    report.push('\nPosition availability:');
    for (const [pos, count] of positionCounts) {
      const players = positionPlayers.get(pos) || [];
      report.push(`  ${pos}: ${count} players (${players.join(', ')})`);
    }
    
    // Find bottleneck positions
    const bottlenecks = Array.from(positionCounts.entries())
      .filter(([pos, count]) => count < 1)
      .map(([pos]) => pos);
    
    if (bottlenecks.length > 0) {
      report.push(`\n❌ IMPOSSIBLE POSITIONS: ${bottlenecks.join(', ')}`);
      report.push('No players available for these positions without violating "avoid" restrictions');
    }
    
    return report.join('\n');
  }
}
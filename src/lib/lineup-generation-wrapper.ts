/**
 * Wrapper to properly integrate the new lineup generation algorithm
 * This handles the async nature of the new algorithm and converts formats
 */

import { Player, InningLineup, LineupRules } from '@/contexts/TeamContext';
import { generateLineupAdapter } from './lineup-generation/adapter';
import { generateOptimalLineup as oldGenerateOptimalLineup } from './utils-enhanced';
import { ensureNewAlgorithm, OLD_ALGORITHM_DISABLED } from './lineup-generation/kill-switch';

/**
 * Wrapper function that can be used as a drop-in replacement for generateOptimalLineup
 * It checks feature flags and uses the appropriate algorithm
 */
export async function generateOptimalLineupWrapper(
  availablePlayers: Player[],
  totalInnings: number,
  rules: LineupRules
): Promise<InningLineup[]> {
  console.log("🔵 WRAPPER EXECUTING - lineup-generation-wrapper.ts");
  console.log("🔵 generateOptimalLineupWrapper called");
  
  const teamId = availablePlayers[0]?.team_id || 'unknown';
  
  console.log('=== LINEUP GENERATION WRAPPER ===');
  console.log('Team ID:', teamId);
  console.log('Competitive mode:', rules.competitiveMode);
  console.log('Available players:', availablePlayers.map(p => p.name));
  
  // Check if new algorithm is forced
  if (OLD_ALGORITHM_DISABLED) {
    console.log('[WRAPPER] Kill switch active - using NEW lineup generation algorithm');
    
    try {
      // Create a lineup object for the adapter
      const mockLineup = {
        id: `temp-${Date.now()}`,
        teamId,
        innings: [],
        battingOrder: [],
        gameDate: new Date().toISOString(),
        numberOfInnings: totalInnings
      };
      
      // Call the new algorithm via adapter
      const result = await generateLineupAdapter(availablePlayers, rules, mockLineup);
      
      console.log('[WRAPPER] New algorithm result:', result);
      
      // The adapter returns a full lineup object, extract innings
      if (result.innings && result.innings.length > 0) {
        console.log('[WRAPPER] Successfully generated', result.innings.length, 'innings');
        return result.innings;
      } else {
        throw new Error('New algorithm returned no innings');
      }
    } catch (error) {
      console.error('[WRAPPER] New algorithm failed:', error);
      throw error;
    }
  } else {
    console.log('[WRAPPER] Using OLD lineup generation algorithm (proven rotation logic)');
    
    try {
      // Use the old algorithm directly for now
      const result = await oldGenerateOptimalLineup(availablePlayers, totalInnings, rules);
      console.log('[WRAPPER] Old algorithm generated', result.length, 'innings successfully');
      return result;
    } catch (error) {
      console.error('[WRAPPER] Old algorithm failed:', error);
      throw error;
    }
  }
}

/**
 * Synchronous version that returns a promise
 * Can be used in places that expect a sync function but can handle promises
 */
export function generateOptimalLineupSync(
  availablePlayers: Player[],
  totalInnings: number,
  rules: LineupRules
): InningLineup[] | Promise<InningLineup[]> {
  // Just delegate to the async wrapper
  return generateOptimalLineupWrapper(availablePlayers, totalInnings, rules);
}
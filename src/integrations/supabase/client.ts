
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL;
const SUPABASE_PUBLISHABLE_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY;

console.log('🔐 Supabase Client: Initializing with URL:', SUPABASE_URL);

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    storage: localStorage,
    detectSessionInUrl: true,
    flowType: 'pkce'
  }
});

// Verify session persistence is working
if (import.meta.env.DEV) {
  supabase.auth.onAuthStateChange((event, session) => {
    console.log('🔐 Supabase Auth State:', event, {
      hasSession: !!session,
      user: session?.user?.email,
      expiresAt: session?.expires_at
    });
    
    // Check localStorage for auth tokens
    const authTokens = Array.from({ length: localStorage.length }, (_, i) => localStorage.key(i))
      .filter(key => key && key.startsWith('sb-'))
      .reduce((acc, key) => {
        if (key) acc[key] = localStorage.getItem(key)?.substring(0, 50) + '...';
        return acc;
      }, {} as Record<string, string>);
    
    console.log('🔐 Stored Auth Tokens:', authTokens);
  });
}

// Add request interceptor to log all requests
if (import.meta.env.DEV) {
  const originalRequest = supabase.rest.request;
  supabase.rest.request = async function(...args: any[]) {
    console.log('🔐 Supabase Request:', {
      method: args[0],
      url: args[1],
      headers: args[2]?.headers,
      hasAuthHeader: !!args[2]?.headers?.Authorization
    });
    return originalRequest.apply(this, args);
  };
}

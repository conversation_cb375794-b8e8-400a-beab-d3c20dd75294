export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON><PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      batting_orders: {
        Row: {
          id: string
          lineup_id: string
          player_order: <PERSON><PERSON>
          created_at: string
          updated_at: string
          user_id: string
        }
        Insert: {
          id?: string
          lineup_id: string
          player_order: <PERSON><PERSON>
          created_at?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          id?: string
          lineup_id?: string
          player_order?: <PERSON><PERSON>
          created_at?: string
          updated_at?: string
          user_id?: string
        }
      }
      lineups: {
        Row: {
          id: string
          name: string
          game_date: string
          team_id: string
          created_at: string
          updated_at: string
          user_id: string
        }
        Insert: {
          id?: string
          name: string
          game_date: string
          team_id: string
          created_at?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          id?: string
          name?: string
          game_date?: string
          team_id?: string
          created_at?: string
          updated_at?: string
          user_id?: string
        }
      }
      lineup_attendance: {
        Row: {
          id: string
          lineup_id: string
          player_id: string
          is_present: boolean
          created_at: string
          updated_at: string
          user_id: string
        }
        Insert: {
          id?: string
          lineup_id: string
          player_id: string
          is_present?: boolean
          created_at?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          id?: string
          lineup_id?: string
          player_id?: string
          is_present?: boolean
          created_at?: string
          updated_at?: string
          user_id?: string
        }
      }
      lineup_innings: {
        Row: {
          id: string
          lineup_id: string
          inning_number: number
          positions: Json
          created_at: string
          updated_at: string
          user_id: string
        }
        Insert: {
          id?: string
          lineup_id: string
          inning_number: number
          positions: Json
          created_at?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          id?: string
          lineup_id?: string
          inning_number?: number
          positions?: Json
          created_at?: string
          updated_at?: string
          user_id?: string
        }
      }
      players: {
        Row: {
          id: string
          name: string
          team_id: string
          pitcher_restriction: boolean
          catcher_restriction: boolean
          first_base_restriction: boolean
          other_restriction: string | null
          position_preferences: Json
          team_roles: Json
          pitcher_strategy: Json | null
          created_at: string
          updated_at: string
          user_id: string
        }
        Insert: {
          id?: string
          name: string
          team_id: string
          pitcher_restriction?: boolean
          catcher_restriction?: boolean
          first_base_restriction?: boolean
          other_restriction?: string | null
          position_preferences?: Json
          team_roles?: Json
          pitcher_strategy?: Json | null
          created_at?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          id?: string
          name?: string
          team_id?: string
          pitcher_restriction?: boolean
          catcher_restriction?: boolean
          first_base_restriction?: boolean
          other_restriction?: string | null
          position_preferences?: Json
          team_roles?: Json
          pitcher_strategy?: Json | null
          created_at?: string
          updated_at?: string
          user_id?: string
        }
      }
      rotation_rules: {
        Row: {
          id: string
          team_id: string
          rotation_method: string
          equal_playing_time: boolean
          rotate_players: boolean
          respect_position_lockouts: boolean
          allow_pitcher_rotation: boolean
          allow_catcher_rotation: boolean
          prioritize_outfield_rotation: boolean
          limit_bench_time: boolean
          created_at: string
          updated_at: string
          user_id: string
        }
        Insert: {
          id?: string
          team_id: string
          rotation_method: string
          equal_playing_time?: boolean
          rotate_players?: boolean
          respect_position_lockouts?: boolean
          allow_pitcher_rotation?: boolean
          allow_catcher_rotation?: boolean
          prioritize_outfield_rotation?: boolean
          limit_bench_time?: boolean
          created_at?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          id?: string
          team_id?: string
          rotation_method?: string
          equal_playing_time?: boolean
          rotate_players?: boolean
          respect_position_lockouts?: boolean
          allow_pitcher_rotation?: boolean
          allow_catcher_rotation?: boolean
          prioritize_outfield_rotation?: boolean
          limit_bench_time?: boolean
          created_at?: string
          updated_at?: string
          user_id?: string
        }
      }
      subscriptions: {
        Row: {
          id: string
          user_id: string
          stripe_session_id: string | null
          is_paid: boolean
          amount: number | null
          currency: string
          payment_date: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          stripe_session_id?: string | null
          is_paid?: boolean
          amount?: number | null
          currency?: string
          payment_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          stripe_session_id?: string | null
          is_paid?: boolean
          amount?: number | null
          currency?: string
          payment_date?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      teams: {
        Row: {
          id: string
          name: string
          created_at: string
          updated_at: string
          user_id: string
        }
        Insert: {
          id?: string
          name: string
          created_at?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          id?: string
          name?: string
          created_at?: string
          updated_at?: string
          user_id?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const

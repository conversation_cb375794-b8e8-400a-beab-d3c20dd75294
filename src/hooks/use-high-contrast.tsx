import { useEffect, useState } from 'react';

interface HighContrastOptions {
  enabled: boolean;
  extraLarge: boolean;
}

export function useHighContrast() {
  const [highContrast, setHighContrast] = useState<HighContrastOptions>(() => {
    // Check localStorage for saved preference
    const saved = localStorage.getItem('high-contrast');
    if (saved) {
      return JSON.parse(saved);
    }
    
    // Check system preference
    const prefersHighContrast = window.matchMedia('(prefers-contrast: high)').matches;
    return {
      enabled: prefersHighContrast,
      extraLarge: false
    };
  });

  useEffect(() => {
    // Apply or remove high contrast class
    const root = document.documentElement;
    
    if (highContrast.enabled) {
      root.classList.add('high-contrast');
      console.log('High contrast enabled');
      if (highContrast.extraLarge) {
        root.classList.add('xl-mode');
      } else {
        root.classList.remove('xl-mode');
      }
    } else {
      root.classList.remove('high-contrast', 'xl-mode');
      console.log('High contrast disabled');
    }
    
    // Save preference
    localStorage.setItem('high-contrast', JSON.stringify(highContrast));
  }, [highContrast]);

  const toggleHighContrast = () => {
    setHighContrast(prev => ({
      ...prev,
      enabled: !prev.enabled
    }));
  };

  const toggleExtraLarge = () => {
    setHighContrast(prev => ({
      ...prev,
      extraLarge: !prev.extraLarge
    }));
  };

  const setHighContrastMode = (options: Partial<HighContrastOptions>) => {
    setHighContrast(prev => ({
      ...prev,
      ...options
    }));
  };

  return {
    highContrast: highContrast.enabled,
    extraLarge: highContrast.extraLarge,
    toggleHighContrast,
    toggleExtraLarge,
    setHighContrastMode
  };
}

// Component for easy high contrast toggle
import React from 'react';
import { Sun, Eye } from 'lucide-react';
import { cn } from '@/lib/utils';

export const HighContrastToggle: React.FC<{
  className?: string;
  showLabel?: boolean;
}> = ({ className, showLabel = true }) => {
  const { highContrast, extraLarge, toggleHighContrast, toggleExtraLarge } = useHighContrast();

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <button
        onClick={toggleHighContrast}
        className={cn(
          "touch-target p-2 rounded-lg",
          "transition-all duration-200",
          "border-2",
          highContrast 
            ? "bg-yellow-400 border-black text-black" 
            : "bg-gray-100 border-gray-300 text-gray-700 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300"
        )}
        aria-label="Toggle high contrast mode"
      >
        <Sun size={24} className={highContrast ? "fill-current" : ""} />
      </button>
      
      {highContrast && (
        <button
          onClick={toggleExtraLarge}
          className={cn(
            "touch-target p-2 rounded-lg",
            "transition-all duration-200",
            "border-2",
            extraLarge
              ? "bg-blue-500 border-black text-white" 
              : "bg-gray-100 border-gray-300 text-gray-700"
          )}
          aria-label="Toggle extra large mode"
        >
          <Eye size={24} />
        </button>
      )}
      
      {showLabel && (
        <div className="text-sm">
          {highContrast ? (
            <div>
              <div className="font-medium">High Contrast ON</div>
              {extraLarge && <div className="text-xs">Extra Large</div>}
            </div>
          ) : (
            <div className="text-gray-600 dark:text-gray-400">High Contrast</div>
          )}
        </div>
      )}
    </div>
  );
};
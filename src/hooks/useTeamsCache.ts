import { useState, useCallback, useEffect } from 'react';
import { Team } from '@/contexts/TeamContext';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  etag?: string;
}

interface UseTeamsCacheOptions {
  ttl?: number; // Time to live in milliseconds
  staleWhileRevalidate?: boolean;
}

export function useTeamsCache(options: UseTeamsCacheOptions = {}) {
  const { 
    ttl = 5 * 60 * 1000, // 5 minutes default
    staleWhileRevalidate = true 
  } = options;

  const [cache, setCache] = useState<CacheEntry<Team[]> | null>(() => {
    // CRITICAL FIX: Don't load cache from localStorage on mount to prevent cross-user contamination
    // Cache will be populated when data is fetched for the current user
    return null;
  });

  const [isRevalidating, setIsRevalidating] = useState(false);

  // Save to localStorage whenever cache updates
  useEffect(() => {
    if (cache) {
      try {
        localStorage.setItem('teams_cache', JSON.stringify(cache));
      } catch (e) {
        console.warn('Failed to save teams cache:', e);
      }
    }
  }, [cache]);

  const getCachedData = useCallback((): Team[] | null => {
    if (!cache) return null;
    
    const age = Date.now() - cache.timestamp;
    const isStale = age > ttl;
    
    if (!isStale) {
      return cache.data;
    }
    
    if (staleWhileRevalidate) {
      // Return stale data but trigger revalidation
      return cache.data;
    }
    
    return null;
  }, [cache, ttl, staleWhileRevalidate]);

  const setCachedData = useCallback((data: Team[], etag?: string) => {
    setCache({
      data,
      timestamp: Date.now(),
      etag
    });
  }, []);

  const invalidateCache = useCallback(() => {
    setCache(null);
    try {
      localStorage.removeItem('teams_cache');
    } catch (e) {
      console.warn('Failed to clear teams cache:', e);
    }
  }, []);

  // CRITICAL FIX: Add function to clear cache for user changes
  const clearCacheForUserChange = useCallback(() => {
    console.log('useTeamsCache: Clearing cache for user change');
    setCache(null);
    try {
      localStorage.removeItem('teams_cache');
    } catch (e) {
      console.warn('Failed to clear teams cache on user change:', e);
    }
  }, []);

  const isStale = cache ? Date.now() - cache.timestamp > ttl : true;

  return {
    getCachedData,
    setCachedData,
    invalidateCache,
    clearCacheForUserChange,
    isStale,
    isRevalidating,
    setIsRevalidating,
    etag: cache?.etag
  };
}
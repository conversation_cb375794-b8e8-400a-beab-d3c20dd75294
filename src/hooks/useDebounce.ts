import { useState, useEffect, useRef } from 'react';
import { debounce } from '@/utils/performance';

export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Hook for debounced callback
export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const callbackRef = useRef(callback);
  const debouncedRef = useRef<ReturnType<typeof debounce>>();

  // Update callback ref on each render
  callbackRef.current = callback;

  // Create debounced function only once
  if (!debouncedRef.current) {
    debouncedRef.current = debounce((...args: Parameters<T>) => {
      callbackRef.current(...args);
    }, delay);
  }

  return debouncedRef.current as T;
}

// Hook for search with debouncing
export function useDebouncedSearch<T>(
  items: T[],
  searchFn: (item: T, query: string) => boolean,
  delay: number = 300
) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<T[]>(items);
  const debouncedQuery = useDebounce(query, delay);

  useEffect(() => {
    if (debouncedQuery) {
      const filtered = items.filter(item => searchFn(item, debouncedQuery));
      setResults(filtered);
    } else {
      setResults(items);
    }
  }, [debouncedQuery, items, searchFn]);

  return {
    query,
    setQuery,
    results,
    isSearching: query !== debouncedQuery
  };
}
import { useState, useCallback } from 'react';
import { toast } from 'sonner';

interface OptimisticOptions {
  onSuccess?: (data: any) => void;
  onError?: (error: Error, rollback: () => void) => void;
  successMessage?: string;
  errorMessage?: string;
}

export function useOptimisticUpdate<T>() {
  const [isUpdating, setIsUpdating] = useState(false);
  const [optimisticData, setOptimisticData] = useState<T | null>(null);

  const execute = useCallback(async (
    optimisticValue: T,
    actualUpdate: () => Promise<T>,
    options: OptimisticOptions = {}
  ) => {
    const {
      onSuccess,
      onError,
      successMessage = 'Updated successfully',
      errorMessage = 'Update failed'
    } = options;

    // Store original data for rollback
    const originalData = optimisticData;
    
    // Apply optimistic update immediately
    setOptimisticData(optimisticValue);
    setIsUpdating(true);

    try {
      // Perform actual update
      const result = await actualUpdate();
      
      // Update with real data
      setOptimisticData(result);
      
      if (successMessage) {
        toast.success(successMessage);
      }
      
      if (onSuccess) {
        onSuccess(result);
      }
      
      return result;
    } catch (error) {
      // Rollback on error
      const rollback = () => setOptimisticData(originalData);
      rollback();
      
      if (errorMessage) {
        toast.error(errorMessage);
      }
      
      if (onError) {
        onError(error as Error, rollback);
      } else {
        throw error;
      }
    } finally {
      setIsUpdating(false);
    }
  }, [optimisticData]);

  return {
    execute,
    isUpdating,
    optimisticData,
    setOptimisticData
  };
}

// Example usage:
// const { execute, optimisticData } = useOptimisticUpdate<Team>();
// 
// const handleTeamNameChange = async (newName: string) => {
//   await execute(
//     { ...team, name: newName }, // Optimistic value
//     async () => {
//       // Actual update
//       await updateTeam({ ...team, name: newName });
//       return { ...team, name: newName };
//     },
//     {
//       successMessage: 'Team name updated',
//       errorMessage: 'Failed to update team name'
//     }
//   );
// };
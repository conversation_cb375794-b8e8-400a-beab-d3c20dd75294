import { useState, useCallback, useRef } from 'react';

interface HistoryState<T> {
  data: T;
  description?: string;
  timestamp: number;
}

interface UseLineupHistoryOptions {
  maxHistorySize?: number;
}

export function useLineupHistory<T>(
  initialData: T,
  options: UseLineupHistoryOptions = {}
) {
  const { maxHistorySize = 20 } = options;
  
  // History stack for undo
  const [history, setHistory] = useState<HistoryState<T>[]>([
    {
      data: initialData,
      description: 'Initial state',
      timestamp: Date.now(),
    },
  ]);
  
  // Current position in history (for redo functionality)
  const [currentIndex, setCurrentIndex] = useState(0);
  
  // Reference to track if we should save to history
  const skipNextHistorySave = useRef(false);
  
  // Get current data
  const currentData = history[currentIndex]?.data || initialData;
  
  // Add new state to history
  const pushState = useCallback(
    (newData: T, description?: string) => {
      console.log('🔄 useLineupHistory: pushState called', {
        description,
        skipNext: skipNextHistorySave.current,
        currentIndex,
        historyLength: history.length
      });
      
      if (skipNextHistorySave.current) {
        skipNextHistorySave.current = false;
        return;
      }
      
      setHistory((prev) => {
        // Remove any history after current index (no redo after new change)
        const newHistory = prev.slice(0, currentIndex + 1);
        
        // Add new state
        newHistory.push({
          data: newData,
          description,
          timestamp: Date.now(),
        });
        
        console.log('🔄 useLineupHistory: New history entry added', {
          newLength: newHistory.length,
          description
        });
        
        // Limit history size
        if (newHistory.length > maxHistorySize) {
          newHistory.shift();
          // Adjust current index since we removed an item
          setCurrentIndex((idx) => Math.max(0, idx - 1));
        } else {
          setCurrentIndex(newHistory.length - 1);
        }
        
        return newHistory;
      });
    },
    [currentIndex, maxHistorySize]
  );
  
  // Undo to previous state
  const undo = useCallback(() => {
    console.log('↩️ useLineupHistory: undo called', {
      currentIndex,
      canUndo: currentIndex > 0,
      previousDescription: history[currentIndex - 1]?.description
    });
    
    if (currentIndex > 0) {
      skipNextHistorySave.current = true;
      setCurrentIndex((prev) => prev - 1);
      return true;
    }
    return false;
  }, [currentIndex, history]);
  
  // Redo to next state
  const redo = useCallback(() => {
    console.log('↪️ useLineupHistory: redo called', {
      currentIndex,
      canRedo: currentIndex < history.length - 1,
      nextDescription: history[currentIndex + 1]?.description
    });
    
    if (currentIndex < history.length - 1) {
      skipNextHistorySave.current = true;
      setCurrentIndex((prev) => prev + 1);
      return true;
    }
    return false;
  }, [currentIndex, history.length, history]);
  
  // Clear history and reset to initial state
  const clearHistory = useCallback((newInitialData?: T) => {
    const data = newInitialData || initialData;
    setHistory([
      {
        data,
        description: 'Initial state',
        timestamp: Date.now(),
      },
    ]);
    setCurrentIndex(0);
  }, [initialData]);
  
  // Get history metadata for UI
  const getHistoryInfo = useCallback(() => {
    return {
      canUndo: currentIndex > 0,
      canRedo: currentIndex < history.length - 1,
      currentDescription: history[currentIndex]?.description || '',
      historyLength: history.length,
      currentIndex,
    };
  }, [currentIndex, history]);
  
  return {
    data: currentData,
    pushState,
    undo,
    redo,
    clearHistory,
    getHistoryInfo,
    skipNextHistorySave,
  };
}
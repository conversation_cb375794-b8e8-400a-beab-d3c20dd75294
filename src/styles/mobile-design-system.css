/* Mobile-First Design System for Dugout Boss */

/* ===========================
   BREAKPOINTS
   =========================== */
:root {
  /* Mobile-first breakpoints */
  --mobile-small: 320px;     /* iPhone SE */
  --mobile: 375px;           /* iPhone 12/13 */
  --mobile-large: 414px;     /* iPhone Plus */
  --tablet: 768px;           /* iPad */
  --tablet-large: 1024px;    /* iPad Pro */
  --desktop: 1280px;         /* Desktop */
  
  /* Touch target sizes */
  --touch-min: 48px;         /* Minimum accessible */
  --touch-preferred: 60px;   /* Preferred size */
  --touch-glove: 72px;       /* For gloved hands */
  
  /* Safe areas for mobile */
  --safe-area-top: env(safe-area-inset-top, 0);
  --safe-area-right: env(safe-area-inset-right, 0);
  --safe-area-bottom: env(safe-area-inset-bottom, 0);
  --safe-area-left: env(safe-area-inset-left, 0);
  
  /* Thumb reach zones */
  --thumb-easy: 64px;        /* Easy reach from bottom */
  --thumb-medium: 128px;     /* Medium reach */
  --thumb-hard: 256px;       /* Hard reach */
  
  /* Mobile spacing */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --space-2xl: 48px;
  
  /* Mobile typography scale */
  --text-xs: 0.75rem;        /* 12px */
  --text-sm: 0.875rem;       /* 14px */
  --text-base: 1rem;         /* 16px - prevents iOS zoom */
  --text-lg: 1.125rem;       /* 18px */
  --text-xl: 1.25rem;        /* 20px */
  --text-2xl: 1.5rem;        /* 24px */
  
  /* High contrast colors for outdoor use */
  --outdoor-black: #000000;
  --outdoor-white: #FFFFFF;
  --outdoor-primary: #0A4F19;
  --outdoor-secondary: #FFA500;
  --outdoor-danger: #DC143C;
  --outdoor-success: #228B22;
  
  /* Dark mode OLED optimized */
  --oled-black: #000000;
  --oled-surface: #121212;
  --oled-surface-light: #1E1E1E;
  --oled-border: #2A2A2A;
}

/* ===========================
   MOBILE UTILITIES
   =========================== */

/* Touch target optimization */
.touch-target {
  min-height: var(--touch-min);
  min-width: var(--touch-min);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.touch-target-preferred {
  min-height: var(--touch-preferred);
  min-width: var(--touch-preferred);
}

.touch-target-glove {
  min-height: var(--touch-glove);
  min-width: var(--touch-glove);
}

/* Safe area padding */
.safe-top {
  padding-top: var(--safe-area-top);
}

.safe-bottom {
  padding-bottom: var(--safe-area-bottom);
}

.safe-left {
  padding-left: var(--safe-area-left);
}

.safe-right {
  padding-right: var(--safe-area-right);
}

/* Smooth scrolling */
.smooth-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  overscroll-behavior-y: contain;
}

/* Prevent text selection on interactive elements */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* iOS input zoom prevention */
input,
select,
textarea {
  font-size: 16px !important; /* Prevents zoom on iOS */
}

/* Bottom fixed elements (for thumb reach) */
.bottom-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 40;
  padding-bottom: var(--safe-area-bottom);
}

/* Landscape mode adjustments */
@media (orientation: landscape) and (max-width: 1024px) {
  .landscape-hidden {
    display: none !important;
  }
  
  .landscape-compact {
    padding-top: var(--space-sm) !important;
    padding-bottom: var(--space-sm) !important;
  }
}

/* High contrast mode for outdoor visibility */
@media (prefers-contrast: high) {
  :root {
    --text-primary: var(--outdoor-black);
    --bg-primary: var(--outdoor-white);
    --border-color: var(--outdoor-black);
  }
}

/* Dark mode with OLED optimization */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: var(--oled-black);
    --bg-surface: var(--oled-surface);
    --bg-surface-light: var(--oled-surface-light);
    --text-primary: #FFFFFF;
    --text-secondary: #B3B3B3;
  }
  
  /* True black for OLED power saving */
  .oled-black {
    background-color: var(--oled-black) !important;
  }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===========================
   MOBILE COMPONENTS
   =========================== */

/* Mobile card component */
.mobile-card {
  background: white;
  border-radius: 12px;
  padding: var(--space-md);
  margin: var(--space-sm);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  touch-action: pan-y;
}

/* Mobile list item */
.mobile-list-item {
  min-height: var(--touch-preferred);
  padding: var(--space-md);
  display: flex;
  align-items: center;
  border-bottom: 1px solid #E5E5E5;
  transition: background-color 0.2s;
}

.mobile-list-item:active {
  background-color: #F5F5F5;
}

/* Floating Action Button */
.fab {
  position: fixed;
  bottom: calc(var(--space-xl) + var(--safe-area-bottom));
  right: var(--space-xl);
  width: 56px;
  height: 56px;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 30;
}

/* Bottom sheet */
.bottom-sheet {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  padding: var(--space-lg);
  padding-bottom: calc(var(--space-lg) + var(--safe-area-bottom));
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
  z-index: 50;
}

/* ===========================
   MOBILE ANIMATIONS
   =========================== */

/* Ripple effect for touch feedback */
.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
  transform: translate(-50%, -50%);
  transition: width 0.4s, height 0.4s;
}

.ripple:active::before {
  width: 200px;
  height: 200px;
}

/* Swipe hint animation */
@keyframes swipe-hint {
  0% { transform: translateX(0); }
  50% { transform: translateX(-20px); }
  100% { transform: translateX(0); }
}

.swipe-hint {
  animation: swipe-hint 2s ease-in-out infinite;
}

/* ===========================
   RESPONSIVE UTILITIES
   =========================== */

/* Hide on mobile */
@media (max-width: 767px) {
  .mobile-hidden {
    display: none !important;
  }
}

/* Show only on mobile */
.mobile-only {
  display: none !important;
}

@media (max-width: 767px) {
  .mobile-only {
    display: block !important;
  }
}

/* Tablet specific */
@media (min-width: 768px) and (max-width: 1023px) {
  .tablet-hidden {
    display: none !important;
  }
}

/* Desktop and up */
@media (min-width: 1024px) {
  .desktop-hidden {
    display: none !important;
  }
}
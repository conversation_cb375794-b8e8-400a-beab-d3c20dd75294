/* Mobile-optimized position selector */
.position-selector {
  max-width: 100%;
  touch-action: manipulation;
}

.baseball-diamond {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.position-selector circle {
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  transition: all 0.2s ease;
}

.position-selector circle:active {
  fill: #e0e0e0;
  transform: scale(0.95);
}

/* Large touch targets for mobile */
.position-presets button {
  min-height: 44px;
  padding: 12px 16px;
  margin: 4px;
  font-size: 16px; /* Prevents zoom on iOS */
}

/* Responsive slider */
.slider-container {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 0;
}

.slider-container input[type="range"] {
  flex: 1;
  height: 44px;
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

/* Slider track */
.slider-container input[type="range"]::-webkit-slider-track {
  height: 8px;
  background: linear-gradient(to right, #3b82f6 0%, #10b981 50%, #f97316 100%);
  border-radius: 4px;
}

.slider-container input[type="range"]::-moz-range-track {
  height: 8px;
  background: linear-gradient(to right, #3b82f6 0%, #10b981 50%, #f97316 100%);
  border-radius: 4px;
}

/* Slider thumb */
.slider-container input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 24px;
  height: 24px;
  background: white;
  border: 3px solid currentColor;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-container input[type="range"]::-moz-range-thumb {
  width: 24px;
  height: 24px;
  background: white;
  border: 3px solid currentColor;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  border: none;
}

/* Collapsed advanced settings for mobile */
.advanced-settings {
  margin-top: 24px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 0;
}

.advanced-settings summary {
  padding: 16px;
  cursor: pointer;
  font-weight: 500;
  user-select: none;
  -webkit-user-select: none;
}

.advanced-settings[open] summary {
  border-bottom: 1px solid #e0e0e0;
}

/* Onboarding flow mobile optimization */
.onboarding-card {
  margin: 8px;
  padding: 16px;
}

.onboarding-card h3 {
  font-size: 18px;
  margin-bottom: 8px;
}

.onboarding-card p {
  font-size: 14px;
  line-height: 1.5;
}

/* Visual position assignment mobile tweaks */
@media (max-width: 640px) {
  .position-selector svg {
    max-width: 320px;
  }
  
  .position-selector circle {
    r: 10; /* Larger touch targets on mobile */
  }
  
  .position-presets {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .position-presets button {
    width: 100%;
  }
}

/* Prevent double-tap zoom on buttons */
button {
  touch-action: manipulation;
}

/* Smooth scrolling for better mobile experience */
.lineup-container {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Loading states for mobile */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
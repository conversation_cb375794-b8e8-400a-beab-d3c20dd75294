/* High Contrast Mode for Outdoor Visibility */

/* High contrast toggle class - Apply to html element */
html.high-contrast {
  /* Force visible changes for testing */
  background-color: #FFFFFF !important;
  color: #000000 !important;
}

html.high-contrast body {
  background-color: #FFFFFF !important;
  color: #000000 !important;
  
  /* Base colors */
  --hc-black: #000000;
  --hc-white: #FFFFFF;
  --hc-primary: #0A4F19;
  --hc-secondary: #FFA500;
  --hc-danger: #DC143C;
  --hc-success: #228B22;
  --hc-warning: #FFD700;
  
  /* Override CSS variables */
  --background: 0 0% 100%;
  --foreground: 0 0% 0%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 0%;
  --primary: 141 69% 17%;
  --primary-foreground: 0 0% 100%;
  --secondary: 39 100% 50%;
  --secondary-foreground: 0 0% 0%;
  --border: 0 0% 0%;
  --ring: 141 69% 17%;
}

/* High contrast dark mode */
.high-contrast.dark {
  --background: 0 0% 0%;
  --foreground: 0 0% 100%;
  --card: 0 0% 0%;
  --card-foreground: 0 0% 100%;
  --primary: 39 100% 50%;
  --primary-foreground: 0 0% 0%;
  --border: 0 0% 100%;
}

/* High contrast specific styles */
html.high-contrast * {
  /* Force all text to be black */
  color: #000000 !important;
  /* Remove all background colors except white */
  background-color: transparent !important;
}

html.high-contrast body,
html.high-contrast .bg-white,
html.high-contrast .bg-gray-50 {
  background-color: #FFFFFF !important;
}

html.high-contrast {
  /* Text */
  color: var(--hc-black);
  font-weight: 500; /* Slightly bolder for better readability */
  
  /* Backgrounds */
  background-color: var(--hc-white);
}

.high-contrast.dark {
  color: var(--hc-white);
  background-color: var(--hc-black);
}

/* Buttons in high contrast */
html.high-contrast button,
html.high-contrast .btn,
html.high-contrast [role="button"] {
  border: 3px solid #000000 !important;
  font-weight: 700 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  background-color: #FFFFFF !important;
  color: #000000 !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
}

.high-contrast button:hover,
.high-contrast .btn:hover {
  background-color: var(--hc-black) !important;
  color: var(--hc-white) !important;
}

.high-contrast.dark button:hover,
.high-contrast.dark .btn:hover {
  background-color: var(--hc-white) !important;
  color: var(--hc-black) !important;
}

/* Primary buttons */
.high-contrast .btn-primary {
  background-color: var(--hc-primary) !important;
  color: var(--hc-white) !important;
  border-color: var(--hc-primary) !important;
}

/* Danger/destructive buttons */
.high-contrast .btn-danger,
.high-contrast .btn-destructive {
  background-color: var(--hc-danger) !important;
  color: var(--hc-white) !important;
  border-color: var(--hc-danger) !important;
}

/* Success buttons */
.high-contrast .btn-success {
  background-color: var(--hc-success) !important;
  color: var(--hc-white) !important;
  border-color: var(--hc-success) !important;
}

/* Links in high contrast */
.high-contrast a {
  color: var(--hc-primary) !important;
  text-decoration: underline !important;
  font-weight: 600 !important;
}

.high-contrast.dark a {
  color: var(--hc-secondary) !important;
}

/* Form inputs */
.high-contrast input,
.high-contrast textarea,
.high-contrast select {
  border: 3px solid var(--hc-black) !important;
  background-color: var(--hc-white) !important;
  color: var(--hc-black) !important;
  font-weight: 500 !important;
}

.high-contrast.dark input,
.high-contrast.dark textarea,
.high-contrast.dark select {
  border-color: var(--hc-white) !important;
  background-color: var(--hc-black) !important;
  color: var(--hc-white) !important;
}

/* Focus states */
.high-contrast *:focus {
  outline: 4px solid var(--hc-secondary) !important;
  outline-offset: 2px !important;
}

/* Cards and panels */
html.high-contrast .card,
html.high-contrast [class*="card"],
html.high-contrast .panel,
html.high-contrast .mobile-card {
  border: 3px solid #000000 !important;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
  background-color: #FFFFFF !important;
}

.high-contrast.dark .card,
.high-contrast.dark .panel,
.high-contrast.dark .mobile-card {
  border-color: var(--hc-white) !important;
}

/* Tables */
.high-contrast table {
  border: 3px solid var(--hc-black) !important;
}

.high-contrast th,
.high-contrast td {
  border: 2px solid var(--hc-black) !important;
  padding: 0.75rem !important;
}

.high-contrast.dark th,
.high-contrast.dark td {
  border-color: var(--hc-white) !important;
}

/* Badges and chips */
.high-contrast .badge,
.high-contrast .chip {
  border: 2px solid currentColor !important;
  font-weight: 700 !important;
}

/* Icons */
.high-contrast svg {
  stroke-width: 2.5 !important;
}

/* Position indicators on field */
.high-contrast .position-indicator {
  background-color: var(--hc-primary) !important;
  color: var(--hc-white) !important;
  border: 3px solid var(--hc-black) !important;
  font-weight: 700 !important;
  font-size: 1.125rem !important;
}

/* Active states */
.high-contrast .active,
.high-contrast .selected {
  background-color: var(--hc-secondary) !important;
  color: var(--hc-black) !important;
  border-color: var(--hc-black) !important;
}

/* Loading spinners */
.high-contrast .spinner {
  border-color: var(--hc-black) !important;
  border-top-color: var(--hc-primary) !important;
}

/* Outdoor visibility enhancements */
@media (prefers-contrast: high) {
  /* Automatically enable high contrast styles */
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;
    --border: 0 0% 0%;
  }
  
  body {
    font-weight: 500;
  }
  
  button, a, input, select, textarea {
    border-width: 3px !important;
  }
}

/* Sun glare reduction */
.high-contrast {
  /* Reduce glare with subtle texture */
  background-image: 
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 10px,
      rgba(0, 0, 0, 0.01) 10px,
      rgba(0, 0, 0, 0.01) 20px
    );
}

/* Extra large mode for gloved hands */
.high-contrast.xl-mode {
  font-size: 1.25rem !important;
}

.high-contrast.xl-mode button,
.high-contrast.xl-mode .touch-target {
  min-height: 72px !important;
  min-width: 72px !important;
  font-size: 1.125rem !important;
}

.high-contrast.xl-mode input,
.high-contrast.xl-mode select,
.high-contrast.xl-mode textarea {
  min-height: 72px !important;
  font-size: 1.125rem !important;
  padding: 1rem !important;
}
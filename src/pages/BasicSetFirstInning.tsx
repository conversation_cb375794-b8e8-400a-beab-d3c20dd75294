import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTeam } from '@/contexts/TeamContext';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { generateId } from '@/lib/utils-enhanced';
import type { InningLineup, Player } from '@/contexts/TeamContext';

const BasicSetFirstInning = () => {
  const navigate = useNavigate();
  const { players, addLineup, currentTeam } = useTeam();
  const [selectedPlayers, setSelectedPlayers] = useState<{[key: string]: string}>({});
  const [attendingPlayers, setAttendingPlayers] = useState<Player[]>([]);
  const [loading, setLoading] = useState(false);

  // Get attending players from localStorage or use all players
  useEffect(() => {
    const attendanceData = localStorage.getItem('lineup_attendance');
    if (attendanceData) {
      const attendance = JSON.parse(attendanceData);
      const attending = players.filter(player => attendance[player.id]);
      setAttendingPlayers(attending);
    } else {
      setAttendingPlayers(players);
    }
  }, [players]);

  // Position definitions grouped by field areas for consistent UX
  const positions = [
    // Battery positions
    { id: 'pitcher', name: 'Pitcher', required: true },
    { id: 'catcher', name: 'Catcher', required: true },
    // Infield positions
    { id: 'firstBase', name: 'First Base', required: true },
    { id: 'secondBase', name: 'Second Base', required: true },
    { id: 'shortstop', name: 'Shortstop', required: true },
    { id: 'thirdBase', name: 'Third Base', required: true },
    // Outfield positions
    { id: 'leftField', name: 'Left Field', required: true },
    { id: 'centerField', name: 'Center Field', required: true },
    { id: 'rightField', name: 'Right Field', required: true },
  ];

  // Optional positions for teams that use them
  const optionalPositions = [
    { id: 'leftCenter', name: 'Left Center', required: false },
    { id: 'rightCenter', name: 'Right Center', required: false },
  ];

  // Calculate bench positions based on attending players
  const benchPositions = [];
  const extraPlayers = Math.max(0, attendingPlayers.length - 9);
  for (let i = 1; i <= extraPlayers; i++) {
    benchPositions.push({ id: `bench${i}`, name: `Bench ${i}`, required: false });
  }

  // Get available players for a position (excluding already selected)
  const getAvailablePlayersForPosition = (positionId: string) => {
    const selectedValues = Object.values(selectedPlayers);
    return attendingPlayers.filter(player => {
      // Don't show already selected players (except for current position)
      if (selectedValues.includes(player.name) && selectedPlayers[positionId] !== player.name) {
        return false;
      }

      // Check position restrictions (simplified)
      if (positionId === 'pitcher' && player.positionRestrictions.pitcher === false) {
        return false;
      }
      if (positionId === 'catcher' && player.positionRestrictions.catcher === false) {
        return false;
      }
      if (positionId === 'firstBase' && player.positionRestrictions.firstBase === false) {
        return false;
      }

      return true;
    });
  };

  // Handle player selection
  const handlePlayerSelect = (positionId: string, playerName: string) => {
    setSelectedPlayers(prev => ({
      ...prev,
      [positionId]: playerName
    }));
  };

  // Validate lineup
  const validateLineup = () => {
    const requiredPositions = positions.filter(p => p.required);
    const missingPositions = requiredPositions.filter(pos => !selectedPlayers[pos.id]);

    if (missingPositions.length > 0) {
      toast.error(`Please fill in: ${missingPositions.map(p => p.name).join(', ')}`);
      return false;
    }

    // Check for duplicate players
    const selectedValues = Object.values(selectedPlayers).filter(v => v);
    const uniqueValues = [...new Set(selectedValues)];
    if (selectedValues.length !== uniqueValues.length) {
      toast.error('Each player can only be assigned to one position');
      return false;
    }

    return true;
  };

  // Create lineup
  const createLineup = async () => {
    if (!validateLineup()) return;

    setLoading(true);
    try {
      // Create the first inning lineup
      const firstInning: InningLineup = {
        inning: 1,
        positions: {
          leftField: selectedPlayers.leftField || '',
          centerField: selectedPlayers.centerField || '',
          rightField: selectedPlayers.rightField || '',
          thirdBase: selectedPlayers.thirdBase || '',
          shortstop: selectedPlayers.shortstop || '',
          secondBase: selectedPlayers.secondBase || '',
          firstBase: selectedPlayers.firstBase || '',
          catcher: selectedPlayers.catcher || '',
          pitcher: selectedPlayers.pitcher || '',
          bench: benchPositions.map(bp => selectedPlayers[bp.id] || '').filter(p => p),
          // Optional positions
          leftCenter: selectedPlayers.leftCenter || undefined,
          rightCenter: selectedPlayers.rightCenter || undefined,
        }
      };

      // Create lineup object
      const newLineup = {
        id: generateId(),
        name: `Lineup ${new Date().toLocaleDateString()}`,
        gameDate: new Date().toISOString().split('T')[0],
        createdDate: new Date().toISOString(),
        team_id: currentTeam.id,
        attendance: attendingPlayers.reduce((acc, player) => {
          acc[player.id] = true;
          return acc;
        }, {} as {[key: string]: boolean}),
        innings: [firstInning],
        battingOrder: [],
        // Store default rotation settings for regenerate function
        rotationSettings: {
          limitBenchTime: true,
          rotateLineupEvery: 1,
          rotatePitcherEvery: 2
        }
      };

      // Store in sessionStorage like the reference site
      sessionStorage.setItem('currentLineup', JSON.stringify(newLineup));

      const savedLineup = await addLineup(newLineup);
      toast.success('Lineup created successfully!');
      navigate('/edit-inning', { state: { lineupId: savedLineup.id, inning: 1 } });
    } catch (error) {
      console.error('Error creating lineup:', error);
      toast.error('Failed to create lineup');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto p-6">
        <div className="mb-6">
          <Button
            variant="outline"
            onClick={() => navigate(-1)}
            className="mb-4"
          >
            ← Return to Previous Screen
          </Button>

          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Fill In Your Starting Fielding Positions
          </h1>

          <div className="text-sm text-gray-600 space-y-1">
            <p>• Select only those players who will actually be at the game</p>
            <p>• If you have more players than positions, assign extras to bench positions</p>
            <p>• Players locked out of positions won't appear in those dropdowns</p>
            <p>• {'{Brackets}'} indicate a player is already assigned to another position</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="space-y-6">
            {/* Outfield Positions */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Outfield Positions</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {positions.slice(0, 3).map(position => (
                  <div key={position.id}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {position.name}
                    </label>
                    <select
                      value={selectedPlayers[position.id] || ''}
                      onChange={(e) => handlePlayerSelect(position.id, e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">Choose a Player</option>
                      {getAvailablePlayersForPosition(position.id)
                        .sort((a, b) => a.name.localeCompare(b.name))
                        .map(player => {
                        const isAlreadySelected = Object.values(selectedPlayers).includes(player.name) && selectedPlayers[position.id] !== player.name;
                        const displayName = isAlreadySelected ? `{${player.name}}` : player.name;
                        return (
                          <option key={player.id} value={player.name}>
                            {displayName}
                          </option>
                        );
                      })}
                    </select>
                  </div>
                ))}
              </div>
            </div>

            {/* Optional Positions */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Optional Positions</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {optionalPositions.map(position => (
                  <div key={position.id}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {position.name} (optional):
                    </label>
                    <select
                      value={selectedPlayers[position.id] || ''}
                      onChange={(e) => handlePlayerSelect(position.id, e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">Choose a Player for {position.name}</option>
                      {getAvailablePlayersForPosition(position.id)
                        .sort((a, b) => a.name.localeCompare(b.name))
                        .map(player => {
                        const isAlreadySelected = Object.values(selectedPlayers).includes(player.name) && selectedPlayers[position.id] !== player.name;
                        const displayName = isAlreadySelected ? `{${player.name}}` : player.name;
                        return (
                          <option key={player.id} value={player.name}>
                            {displayName}
                          </option>
                        );
                      })}
                    </select>
                  </div>
                ))}
              </div>
            </div>

            {/* Infield Positions */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Infield Positions</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {positions.slice(3, 7).map(position => (
                  <div key={position.id}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {position.name}
                    </label>
                    <select
                      value={selectedPlayers[position.id] || ''}
                      onChange={(e) => handlePlayerSelect(position.id, e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">Choose a Player</option>
                      {getAvailablePlayersForPosition(position.id)
                        .sort((a, b) => a.name.localeCompare(b.name))
                        .map(player => {
                        const isAlreadySelected = Object.values(selectedPlayers).includes(player.name) && selectedPlayers[position.id] !== player.name;
                        const displayName = isAlreadySelected ? `{${player.name}}` : player.name;
                        return (
                          <option key={player.id} value={player.name}>
                            {displayName}
                          </option>
                        );
                      })}
                    </select>
                  </div>
                ))}
              </div>
            </div>

            {/* Battery Positions */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Battery Positions</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {positions.slice(7, 9).map(position => (
                  <div key={position.id}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {position.name}
                    </label>
                    <select
                      value={selectedPlayers[position.id] || ''}
                      onChange={(e) => handlePlayerSelect(position.id, e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">Choose a Player</option>
                      {getAvailablePlayersForPosition(position.id)
                        .sort((a, b) => a.name.localeCompare(b.name))
                        .map(player => {
                        const isAlreadySelected = Object.values(selectedPlayers).includes(player.name) && selectedPlayers[position.id] !== player.name;
                        const displayName = isAlreadySelected ? `{${player.name}}` : player.name;
                        return (
                          <option key={player.id} value={player.name}>
                            {displayName}
                          </option>
                        );
                      })}
                    </select>
                  </div>
                ))}
              </div>
            </div>

            {/* Bench Positions */}
            {benchPositions.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold mb-3">Bench Positions</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {benchPositions.map(position => (
                    <div key={position.id}>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {position.name} (optional)
                      </label>
                      <select
                        value={selectedPlayers[position.id] || ''}
                        onChange={(e) => handlePlayerSelect(position.id, e.target.value)}
                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="">Choose a Player</option>
                        {getAvailablePlayersForPosition(position.id)
                        .sort((a, b) => a.name.localeCompare(b.name))
                        .map(player => {
                          const isAlreadySelected = Object.values(selectedPlayers).includes(player.name) && selectedPlayers[position.id] !== player.name;
                          const displayName = isAlreadySelected ? `{${player.name}}` : player.name;
                          return (
                            <option key={player.id} value={player.name}>
                              {displayName}
                            </option>
                          );
                        })}
                      </select>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          <div className="mt-8 text-center">
            <Button
              onClick={createLineup}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-2"
            >
              {loading ? 'Creating Lineup...' : 'Create Your Lineup'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicSetFirstInning;

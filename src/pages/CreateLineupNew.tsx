import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { generateId } from "@/lib/utils-enhanced";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { CalendarDays, Text, ClipboardList, Loader2, AlertCircle } from "lucide-react";
import { useTeam } from "@/contexts/TeamContext";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { getGenerationMessage } from "@/utils/userFriendlyErrors";
import { cn } from "@/lib/utils";

const CreateLineupNew = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { rotationRules, players } = useTeam();
  
  // Check if we're in regeneration mode from a series
  const state = location.state as any;
  const isRegenerateMode = state?.regenerateMode;
  const existingLineup = state?.existingLineup;
  const seriesData = state?.seriesData;
  const gameIndex = state?.gameIndex;
  const rotationSettings = state?.rotationSettings;

  const [lineupName, setLineupName] = useState("");
  const [gameDate, setGameDate] = useState(new Date().toISOString().split("T")[0]);
  const [numberOfInnings, setNumberOfInnings] = useState<string>((rotationRules?.defaultInnings || 6).toString());
  const [isGenerating, setIsGenerating] = useState(false);
  
  // Calculate coaching style from rotation rules
  const getCoachingStyle = (): number => {
    if (rotationRules?.competitiveMode) {
      if (rotationRules.competitiveMinPlayingTime && rotationRules.competitiveMinPlayingTime <= 25) {
        return 90; // Tournament mode
      }
      return 70; // Mostly competitive
    }
    if (rotationRules?.equalPlayingTime) {
      return 10; // Pure recreational
    }
    return 50; // Balanced
  };
  
  const coachingStyle = getCoachingStyle();
  
  // Initialize from existing lineup if regenerating
  useEffect(() => {
    if (isRegenerateMode && existingLineup) {
      setLineupName(existingLineup.name || "");
      setGameDate(existingLineup.gameDate || new Date().toISOString().split("T")[0]);
      setNumberOfInnings((existingLineup.innings?.length || rotationRules?.defaultInnings || 6).toString());
    }
  }, [isRegenerateMode, existingLineup, rotationRules]);

  const handleCreateLineup = async () => {
    if (!lineupName.trim()) {
      toast.error("Please enter a lineup name");
      return;
    }

    // Validate number of innings
    const innings = numberOfInnings === "" ? (rotationRules?.defaultInnings || 6) : parseInt(numberOfInnings);
    if (isNaN(innings) || innings < 1 || innings > 15) {
      toast.error("Number of innings must be between 1 and 15");
      return;
    }

    // Check if we have enough players
    if (players.length < 9) {
      toast.error("You need at least 9 players to create a lineup. Please add more players to your roster.");
      return;
    }

    setIsGenerating(true);

    // Create the lineup data
    const lineupData = {
      id: isRegenerateMode && existingLineup ? existingLineup.id : generateId(),
      name: lineupName,
      gameDate: gameDate,
      createdDate: isRegenerateMode && existingLineup ? existingLineup.createdDate : new Date().toISOString().split("T")[0],
      numberOfInnings: innings,
      innings: [],
      battingOrder: isRegenerateMode && existingLineup ? existingLineup.battingOrder : [],
      attendance: isRegenerateMode && existingLineup ? existingLineup.attendance : {},
      // Store coaching style value
      coachingStyle: coachingStyle,
      // ONLY preserve series metadata if explicitly in regenerate mode for a series game
      ...(isRegenerateMode && existingLineup && seriesData && {
        seriesId: existingLineup.seriesId,
        seriesTitle: existingLineup.seriesTitle,
        gameNumber: existingLineup.gameNumber,
        totalGamesInSeries: existingLineup.totalGamesInSeries
      })
    };
    
    // IMPORTANT: For single games, explicitly ensure no series metadata is present
    if (!isRegenerateMode || !seriesData) {
      // Remove any series metadata that might have leaked through
      delete (lineupData as any).seriesId;
      delete (lineupData as any).seriesTitle;
      delete (lineupData as any).gameNumber;
      delete (lineupData as any).totalGamesInSeries;
      console.log("Creating single game lineup - series metadata explicitly removed");
    }

    console.log(isRegenerateMode ? "Regenerating lineup data:" : "Created new lineup data:", lineupData);

    // Show generation message
    toast.loading(getGenerationMessage(coachingStyle), {
      id: 'lineup-generation'
    });

    // Navigate to attendance selection with lineup data
    setTimeout(() => {
      toast.dismiss('lineup-generation');
      navigate("/set-lineup-attendance", { 
        state: { 
          lineupData,
          isRegenerateMode,
          seriesData,
          gameIndex,
          rotationSettings
        } 
      });
    }, 500);
  };

  const getCoachingModeLabel = () => {
    if (coachingStyle < 30) return "Recreational Mode";
    if (coachingStyle < 70) return "Balanced Mode";
    return "Competitive Mode";
  };

  const getCoachingModeColor = () => {
    if (coachingStyle < 30) return "text-blue-600 bg-blue-50 border-blue-200";
    if (coachingStyle < 70) return "text-green-600 bg-green-50 border-green-200";
    return "text-orange-600 bg-orange-50 border-orange-200";
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <Header 
        title={isRegenerateMode ? "Regenerate Lineup" : "Create a New Lineup"} 
        showBack 
        backLink={isRegenerateMode && seriesData ? `/view-batch-series` : "/dashboard"} 
      />

      <main className="flex-1 container mx-auto px-4 py-6 sm:py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ClipboardList size={24} />
              New Lineup Details
            </CardTitle>
            <CardDescription>
              Let's set up your game details
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Coaching Mode Display */}
            <div className={cn(
              "p-3 rounded-lg border text-sm font-medium text-center",
              getCoachingModeColor()
            )}>
              {getCoachingModeLabel()} Active
            </div>

            {/* Position assignment warning for competitive mode */}
            {coachingStyle >= 70 && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Competitive Mode:</strong> Players will only play positions they're assigned to. 
                  Make sure you've assigned positions in Team Roster.
                </AlertDescription>
              </Alert>
            )}

            <div className="space-y-3">
              <label htmlFor="lineup-name" className="text-sm font-medium flex items-center gap-2">
                <Text size={18} />
                Lineup Name
              </label>
              <Input
                id="lineup-name"
                value={lineupName}
                onChange={(e) => setLineupName(e.target.value)}
                placeholder="e.g., vs Eagles, Game 3"
                className="focus:ring-2 focus:ring-primary"
              />
            </div>

            <div className="space-y-3">
              <label htmlFor="game-date" className="text-sm font-medium flex items-center gap-2">
                <CalendarDays size={18} />
                Game Date
              </label>
              <Input
                id="game-date"
                type="date"
                value={gameDate}
                onChange={(e) => setGameDate(e.target.value)}
                className="focus:ring-2 focus:ring-primary"
              />
            </div>

            <div className="space-y-3">
              <label htmlFor="innings" className="text-sm font-medium flex items-center gap-2">
                Number of Innings
              </label>
              <Input
                id="innings"
                type="number"
                min={1}
                max={15}
                value={numberOfInnings}
                onChange={(e) => {
                  const value = e.target.value;
                  setNumberOfInnings(value);
                }}
                className="focus:ring-2 focus:ring-primary"
              />
            </div>

            <div className="pt-4">
              <Button
                onClick={handleCreateLineup}
                className="w-full"
                disabled={isGenerating}
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating...
                  </>
                ) : (
                  'Continue to Player Selection'
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </main>

      <Footer />
    </div>
  );
};

export default CreateLineupNew;
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, DollarSign, ClipboardList, Activity, TrendingUp, UserCheck } from "lucide-react";
import AdminLayout from "@/components/AdminLayout";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";

interface DashboardStats {
  totalUsers: number;
  totalTeams: number;
  totalRevenue: number;
  activeUsers: number;
  paidUsers: number;
  conversionRate: number;
}

const AdminDashboard = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalTeams: 0,
    totalRevenue: 0,
    activeUsers: 0,
    paidUsers: 0,
    conversionRate: 0
  });
  const [loading, setLoading] = useState(true);
  const [recentUsers, setRecentUsers] = useState<any[]>([]);
  const [recentPayments, setRecentPayments] = useState<any[]>([]);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Get total users
        const { count: userCount, error: userError } = await supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true });

        if (userError) throw userError;

        // Get total teams
        const { count: teamCount, error: teamError } = await supabase
          .from('teams')
          .select('*', { count: 'exact', head: true });

        if (teamError) throw teamError;

        // Get paid users and revenue
        const { data: subscriptions, error: subError } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('is_paid', true);

        if (subError) throw subError;

        // Calculate unique paid users
        const paidUserIds = new Set(subscriptions?.map(sub => sub.user_id) || []);
        const paidUserCount = paidUserIds.size;
        
        // Calculate total revenue
        const totalRevenue = subscriptions?.reduce((sum, sub) => sum + (sub.amount || 0), 0) || 0;

        // Calculate conversion rate
        const conversionRate = userCount ? (paidUserCount / userCount) * 100 : 0;

        // Get recent users with emails directly from profiles
        let recentUsersData = [];
        try {
          const { data: profiles, error: profilesError } = await supabase
            .from('profiles')
            .select('id, full_name, email, created_at')
            .order('created_at', { ascending: false })
            .limit(5);

          if (profilesError) throw profilesError;

          recentUsersData = profiles?.map(profile => ({
            ...profile,
            users: { email: profile.email || 'Email not available' }
          })) || [];
        } catch (error) {
          console.error("Error fetching recent users:", error);
          recentUsersData = [];
        }

        // Get recent payments - simplified query without join
        let recentPaymentsData = [];
        try {
          const { data: subscriptions, error: subscriptionsError } = await supabase
            .from('subscriptions')
            .select('*')
            .eq('is_paid', true)
            .order('created_at', { ascending: false })
            .limit(5);

          if (subscriptionsError) throw subscriptionsError;

          // Get user details separately
          if (subscriptions && subscriptions.length > 0) {
            const userIds = subscriptions.map(s => s.user_id);
            const { data: profiles } = await supabase
              .from('profiles')
              .select('id, email, full_name')
              .in('id', userIds);

            const profileMap = {};
            profiles?.forEach(p => {
              profileMap[p.id] = p;
            });

            recentPaymentsData = subscriptions.map(subscription => ({
              ...subscription,
              users: { 
                email: profileMap[subscription.user_id]?.email || 'Email not available',
                full_name: profileMap[subscription.user_id]?.full_name || ''
              }
            }));
          }
        } catch (error) {
          console.error("Error fetching recent payments:", error);
          recentPaymentsData = [];
        }

        setStats({
          totalUsers: userCount || 0,
          totalTeams: teamCount || 0,
          totalRevenue: totalRevenue / 100, // Convert cents to dollars
          activeUsers: userCount || 0, // For now, just use total users
          paidUsers: paidUserCount,
          conversionRate
        });

        setRecentUsers(recentUsersData || []);
        setRecentPayments(recentPaymentsData || []);
      } catch (error) {
        console.error("Error fetching admin stats:", error);
        toast.error("Failed to load dashboard data");
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  return (
    <AdminLayout>
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-6">Admin Dashboard</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <StatCard 
            title="Total Users" 
            value={stats.totalUsers} 
            icon={<Users className="h-8 w-8 text-blue-500" />} 
            loading={loading}
          />
          <StatCard 
            title="Total Teams" 
            value={stats.totalTeams} 
            icon={<ClipboardList className="h-8 w-8 text-green-500" />} 
            loading={loading}
          />
          <StatCard 
            title="Total Revenue" 
            value={`$${stats.totalRevenue.toFixed(2)}`} 
            icon={<DollarSign className="h-8 w-8 text-emerald-500" />} 
            loading={loading}
          />
          <StatCard 
            title="Active Users" 
            value={stats.activeUsers} 
            icon={<Activity className="h-8 w-8 text-purple-500" />} 
            loading={loading}
          />
          <StatCard 
            title="Paid Users" 
            value={stats.paidUsers} 
            icon={<UserCheck className="h-8 w-8 text-amber-500" />} 
            loading={loading}
          />
          <StatCard 
            title="Conversion Rate" 
            value={`${stats.conversionRate.toFixed(1)}%`} 
            icon={<TrendingUp className="h-8 w-8 text-red-500" />} 
            loading={loading}
          />
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Users */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Recent Users</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="flex items-center space-x-4">
                      <Skeleton className="h-12 w-12 rounded-full" />
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-[200px]" />
                        <Skeleton className="h-4 w-[150px]" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : recentUsers.length === 0 ? (
                <p className="text-gray-500 text-center py-4">No users found</p>
              ) : (
                <div className="space-y-4">
                  {recentUsers.map((user) => (
                    <div key={user.id} className="flex items-center space-x-4">
                      <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center text-gray-700 font-medium">
                        {user.users?.email?.charAt(0).toUpperCase() || "U"}
                      </div>
                      <div>
                        <p className="font-medium">{user.full_name || "Unnamed User"}</p>
                        <p className="text-sm text-gray-500">{user.users?.email}</p>
                        <p className="text-xs text-gray-400">
                          Joined {new Date(user.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Recent Payments */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Recent Payments</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="flex items-center space-x-4">
                      <Skeleton className="h-12 w-12 rounded-full" />
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-[200px]" />
                        <Skeleton className="h-4 w-[150px]" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : recentPayments.length === 0 ? (
                <p className="text-gray-500 text-center py-4">No payments found</p>
              ) : (
                <div className="space-y-4">
                  {recentPayments.map((payment) => (
                    <div key={payment.id} className="flex items-center space-x-4">
                      <div className="h-12 w-12 rounded-full bg-emerald-100 flex items-center justify-center text-emerald-700">
                        <DollarSign className="h-6 w-6" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">{payment.users?.email}</p>
                        <p className="text-sm text-gray-500">
                          ${((payment.amount || 0) / 100).toFixed(2)} {payment.currency?.toUpperCase()}
                        </p>
                        <p className="text-xs text-gray-400">
                          {payment.payment_date 
                            ? new Date(payment.payment_date).toLocaleDateString() 
                            : new Date(payment.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  );
};

const StatCard = ({ title, value, icon, loading }) => (
  <Card>
    <CardContent className="p-6 flex justify-between items-center">
      <div>
        <p className="text-sm font-medium text-gray-500">{title}</p>
        {loading ? (
          <Skeleton className="h-8 w-16 mt-1" />
        ) : (
          <p className="text-3xl font-bold">{value}</p>
        )}
      </div>
      {icon}
    </CardContent>
  </Card>
);

export default AdminDashboard;


import { Link, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle, Loader2 } from "lucide-react";
import { useEffect, useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { trackConversion, trackEvent } from "@/utils/analytics";

const PaymentSuccess = () => {
  const { checkPaymentStatus, isPaid, user } = useAuth();
  const [isVerifying, setIsVerifying] = useState(true);
  const [shouldCreateAccount, setShouldCreateAccount] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const handlePaymentSuccess = async () => {
      try {
        // Track payment success page visit
        trackEvent('payment_success_page_visited', {
          event_category: 'ecommerce',
          event_label: 'payment_confirmation',
          user_type: user ? 'logged_in' : 'anonymous'
        });

        // Wait a moment to allow Stripe webhook to process
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Check if user is logged in
        if (!user) {
          // User is not logged in - they need to create an account
          trackEvent('guest_checkout_completion', {
            event_category: 'ecommerce',
            event_label: 'account_creation_required',
            value: 49
          });
          setShouldCreateAccount(true);
          setIsVerifying(false);
          toast.success("Payment successful! Please create an account to access your purchase.");
          return;
        }

        // User is logged in - verify their payment
        const paymentVerified = await checkPaymentStatus();

        if (paymentVerified) {
          // Track successful purchase completion
          trackConversion.purchaseCompleted(
            `dugout_boss_${Date.now()}`, // Generate transaction ID
            49
          );
          
          // Track conversion for funnel analysis
          trackEvent('purchase_verified', {
            event_category: 'ecommerce',
            event_label: 'payment_verified',
            value: 49,
            user_id: user.id,
            conversion_type: 'logged_in_user'
          });

          toast.success("Your payment has been verified!");
          // Redirect to dashboard after a short delay
          setTimeout(() => {
            navigate("/dashboard");
          }, 1500);
        } else {
          // Track payment verification delay
          trackEvent('payment_verification_delayed', {
            event_category: 'ecommerce',
            event_label: 'verification_pending',
            user_id: user.id
          });

          // If not verified yet, try again after a delay
          setTimeout(async () => {
            const retryVerified = await checkPaymentStatus();
            if (retryVerified) {
              // Track eventual success
              trackConversion.purchaseCompleted(
                `dugout_boss_retry_${Date.now()}`,
                49
              );
              toast.success("Your payment has been verified!");
              navigate("/dashboard");
            } else {
              trackEvent('payment_verification_failed', {
                event_category: 'ecommerce',
                event_label: 'verification_timeout',
                user_id: user.id
              });
              toast.info("Your payment is being processed. You'll have access soon!");
            }
            setIsVerifying(false);
          }, 3000);
        }
      } catch (error) {
        console.error("Error verifying payment:", error);
        toast.error("There was an issue verifying your payment. Please contact support if this persists.");
        setIsVerifying(false);
      }
    };

    handlePaymentSuccess();
  }, [user, checkPaymentStatus, navigate]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="max-w-md w-full">
        <CardHeader className="text-center pb-2">
          <div className="mx-auto w-16 h-16 mb-4 flex items-center justify-center bg-green-100 rounded-full">
            <CheckCircle className="h-10 w-10 text-green-600" />
          </div>
          <CardTitle className="text-2xl text-baseball-navy">Payment Successful!</CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          {shouldCreateAccount ? (
            <>
              <p className="mb-6 text-gray-600">
                Thank you for your purchase! Your payment was successful.
              </p>
              <p className="mb-6 text-gray-600">
                To access your Dugout Boss subscription, please create an account using the same email address you used for payment.
              </p>
              <p className="mb-10 text-sm text-gray-500">
                We'll automatically link your payment to your new account.
              </p>
              <Link to="/signup?payment=success">
                <Button className="bg-baseball-green hover:bg-baseball-green/90 text-white w-full mb-4">
                  Create Account
                </Button>
              </Link>
              <p className="text-sm text-gray-500">
                Already have an account?{" "}
                <Link to="/sign-in" className="text-baseball-green hover:underline">
                  Sign in here
                </Link>
              </p>
            </>
          ) : (
            <>
              <p className="mb-6 text-gray-600">
                Thank you for your purchase. Your subscription to Dugout Boss is now active.
              </p>
              <p className="mb-10 text-gray-600">
                You can now start creating lineups, managing your teams, and making your coaching life easier.
              </p>
              {isVerifying ? (
                <Button disabled className="bg-baseball-green hover:bg-baseball-green/90 text-white w-full">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Verifying payment...
                </Button>
              ) : (
                <Link to="/dashboard">
                  <Button className="bg-baseball-green hover:bg-baseball-green/90 text-white w-full">
                    Go to Dashboard
                  </Button>
                </Link>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default PaymentSuccess;

import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useTeam } from "@/contexts/TeamContext";
import { generateCompleteLineup } from "@/lib/utils-enhanced";
import { generateMultiGameSeries, seriesToCrossGameTracking } from "@/lib/multi-game-orchestrator";
import { enhancePlayersWithPositionPreferences } from "@/lib/teamRolesToPreferences";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { toast } from "sonner";
import { Users, AlertCircle, Trophy, BarChart3 } from "lucide-react";

interface GameData {
  id: string;
  name: string;
  gameDate: string;
  gameTime: string;
  numberOfInnings: number;
  createdDate: string;
  innings: any[];
  battingOrder: string[];
  attendance: {[playerId: string]: boolean};
  // Series metadata
  seriesTitle?: string;
  seriesId?: string;
  gameNumber?: number;
  totalGamesInSeries?: number;
  // Rotation settings for regeneration
  rotationSettings?: {
    limitBenchTime: boolean;
    rotateLineupEvery: number;
    rotatePitcherEvery: number;
  };
}

interface BatchData {
  id: string;
  seriesTitle: string;
  rotationSettings: {
    rotateLineupEvery: number;
    rotatePitcherEvery: number;
    competitiveMode: boolean;
    limitBenchTime: boolean;
    maxConsecutiveBenchInnings: number;
    allowPitcherRotation: boolean;
    allowCatcherRotation: boolean;
    respectPositionLockouts: boolean;
    equalPlayingTime: boolean;
  };
  games: GameData[];
  crossGameTracking: {
    totalGames: number;
    playingTimeTargets: {[playerId: string]: number};
    pitcherInningsTracked: boolean;
    catcherInningsTracked: boolean;
  };
}

const BatchLineupGeneration = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { players, lineups, addLineup, removeLineup, rotationRules, refreshTeamData } = useTeam();
  const [batchData, setBatchData] = useState<BatchData | null>(null);
  const [attendance, setAttendance] = useState<{[playerId: string]: boolean}>({});
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);

  useEffect(() => {
    const state = location.state as { batchData?: BatchData; regenerateMode?: boolean; previousSeriesData?: any } | null;
    if (state && state.batchData) {
      setBatchData(state.batchData);
      // Show a message if in regenerate mode
      if (state.regenerateMode) {
        toast.info("Regenerating series - select player attendance and generate new lineups");
      }
    } else {
      navigate("/batch-games");
      toast.error("Please set up games first");
    }
  }, [location.state, navigate]);

  // Separate useEffect for initializing attendance when players are loaded
  useEffect(() => {
    if (players.length > 0 && Object.keys(attendance).length === 0) {
      // Only initialize attendance if it's empty and we have players
      const initialAttendance = players.reduce((acc, player) => {
        acc[player.id] = true;
        return acc;
      }, {} as {[playerId: string]: boolean});
      setAttendance(initialAttendance);
    }
  }, [players, attendance]);

  const attendingPlayers = players.filter(p => attendance[p.id]);
  const attendingCount = attendingPlayers.length;

  const handleGenerateBatchLineups = async () => {
    if (!batchData) return;
    
    if (attendingCount < 8) {
      toast.error("You need at least 8 players to create lineups");
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      // Check if we're in regenerate mode and need to delete old lineups
      const state = location.state as { regenerateMode?: boolean; previousSeriesData?: any } | null;
      if (state?.regenerateMode && state.previousSeriesData) {
        console.log("🔄 Regenerate mode: Deleting old lineups from series");
        
        // Delete all old lineups from this series
        const oldLineupIds = state.previousSeriesData.games || [];
        for (const lineupId of oldLineupIds) {
          try {
            await removeLineup(lineupId);
            console.log(`✅ Deleted old lineup: ${lineupId}`);
          } catch (error) {
            console.error(`Failed to delete lineup ${lineupId}:`, error);
          }
        }
        
        // Give a moment for deletions to process
        await new Promise(resolve => setTimeout(resolve, 500));
        console.log("🔄 Old lineups deleted, generating new ones...");
      }
      // Calculate total innings across all games
      const totalInnings = batchData.games.reduce((sum, game) => sum + game.numberOfInnings, 0);
      
      // Calculate target playing time per player for cross-game balance
      const totalFieldPositions = totalInnings * 9; // 9 field positions per inning
      const targetInningsPerPlayer = Math.floor(totalFieldPositions / attendingCount);
      const maxInningsPerPlayer = targetInningsPerPlayer + 1; // Allow 1 inning variance
      
      console.log(`🎯 Cross-game balance targets:`, {
        totalFieldPositions,
        attendingCount,
        targetInningsPerPlayer,
        maxInningsPerPlayer,
        totalGames: batchData.games.length
      });
      
      // Initialize tracking
      const playerInningsTracker: {[playerId: string]: number} = {};
      const playerBenchTracker: {[playerId: string]: number} = {};
      const playerPitchingTracker: {[playerId: string]: number} = {};
      attendingPlayers.forEach(player => {
        playerInningsTracker[player.id] = 0;
        playerBenchTracker[player.id] = 0;
        playerPitchingTracker[player.id] = 0;
      });

      // Use the new multi-game orchestrator for better cross-game fairness
      console.log(`🚀 BATCH GENERATION: Using multi-game orchestrator for ${batchData.games.length} games`);
      
      // Check if all games have same innings
      const differentInnings = batchData.games.some(g => g.numberOfInnings !== batchData.games[0].numberOfInnings);
      if (differentInnings) {
        console.warn('⚠️ Games have different innings, using first game innings for all');
      }
      
      // CRITICAL FIX: Use current team rotation rules instead of potentially stale batch data
      // This ensures we respect the current competitive mode setting
      const effectiveRotationRules = {
        ...rotationRules,  // Use current team rotation rules as base
        ...batchData.rotationSettings, // Override with any batch-specific settings
        competitiveMode: rotationRules.competitiveMode, // ALWAYS use current team's competitive mode
      };
      
      console.log('🎯 COMPETITIVE MODE CHECK:', {
        teamCompetitiveMode: rotationRules.competitiveMode,
        batchCompetitiveMode: batchData.rotationSettings.competitiveMode,
        usingCompetitiveMode: effectiveRotationRules.competitiveMode
      });

      // Enhance players with positionPreferences from teamRoles for backward compatibility
      const enhancedPlayers = enhancePlayersWithPositionPreferences(attendingPlayers);
      
      // Log the conversion for debugging
      console.log('🔄 Player position data conversion:', {
        originalPlayers: attendingPlayers.map(p => ({
          name: p.name,
          teamRoles: p.teamRoles,
          positionPreferences: p.positionPreferences
        })),
        enhancedPlayers: enhancedPlayers.map(p => ({
          name: p.name,
          teamRoles: p.teamRoles,
          positionPreferences: p.positionPreferences
        }))
      });

      // Generate all games with proper cross-game tracking
      const seriesResult = await generateMultiGameSeries(
        enhancedPlayers,
        batchData.games.length,
        batchData.games[0].numberOfInnings, // Using first game innings for consistency
        {
          ...effectiveRotationRules,
          // For competitive mode, respect the settings; for recreational, enhance fairness
          equalPlayingTime: effectiveRotationRules.competitiveMode ? 
            effectiveRotationRules.equalPlayingTime : true,
          limitBenchTime: effectiveRotationRules.competitiveMode ? 
            effectiveRotationRules.limitBenchTime : true,
          maxConsecutiveBenchInnings: effectiveRotationRules.competitiveMode ?
            effectiveRotationRules.maxConsecutiveBenchInnings :
            Math.min(effectiveRotationRules.maxConsecutiveBenchInnings || 2, 2),
          // CRITICAL: Ensure position restrictions are respected
          respectPositionLockouts: true,
          // CRITICAL: Pass competitive mode explicitly
          competitiveMode: effectiveRotationRules.competitiveMode,
          // Use the specified rotation frequency
          rotateLineupEvery: effectiveRotationRules.rotateLineupEvery || 1
        }
      );

      console.log(`🎉 BATCH GENERATION COMPLETE: Generated ${seriesResult.games.length} games with ${seriesResult.balanceScore.toFixed(1)}% balance`);

      // Convert the series result into GameData format
      const generatedGames: GameData[] = [];
      
      for (let gameIndex = 0; gameIndex < batchData.games.length; gameIndex++) {
        const game = batchData.games[gameIndex];
        const gameResult = seriesResult.games[gameIndex];
        
        setGenerationProgress((gameIndex / batchData.games.length) * 80);
        
        // Create the game lineup with series metadata
        // IMPORTANT: Don't set the ID here - let the database generate it
        const gameLineup: GameData = {
          id: "", // Empty ID - will be assigned by database
          name: game.name,
          gameDate: game.gameDate,
          gameTime: game.gameTime,
          numberOfInnings: game.numberOfInnings,
          createdDate: game.createdDate,
          innings: gameResult.lineups,
          battingOrder: game.battingOrder,
          attendance: Object.keys(attendance).reduce((acc, playerId) => {
            if (attendance[playerId]) {
              acc[playerId] = true;
            }
            return acc;
          }, {} as {[playerId: string]: boolean}),
          // Series metadata for batch tracking
          seriesTitle: batchData.seriesTitle,
          seriesId: batchData.id,
          gameNumber: gameIndex + 1,
          totalGamesInSeries: batchData.games.length,
          // Store rotation settings for regeneration
          rotationSettings: {
            limitBenchTime: batchData.rotationSettings.limitBenchTime,
            rotateLineupEvery: batchData.rotationSettings.rotateLineupEvery,
            rotatePitcherEvery: batchData.rotationSettings.rotatePitcherEvery
          }
        };

        generatedGames.push(gameLineup);
      }

      // Update tracking variables from series result for summary display
      seriesResult.cumulativeStats.forEach((stats, playerId) => {
        playerInningsTracker[playerId] = stats.totalFieldInnings;
        playerBenchTracker[playerId] = stats.totalBenchInnings;
        playerPitchingTracker[playerId] = stats.totalInningsPitched;
      });

      setGenerationProgress(100);

      // Add all lineups to the team context and capture real IDs
      console.log(`💾 PERSISTENCE: Adding ${generatedGames.length} games to team context`);
      const actualLineupIds: string[] = [];
      
      for (let i = 0; i < generatedGames.length; i++) {
        const gameLineup = generatedGames[i];
        console.log(`💾 Adding game ${i + 1}/${generatedGames.length}: ${gameLineup.name}`);
        console.log(`💾 Game data:`, {
          name: gameLineup.name,
          date: gameLineup.gameDate,
          inningCount: gameLineup.innings.length,
          hasAttendance: !!gameLineup.attendance
        });
        
        // addLineup will return the lineup with the real database ID
        const addedLineup = await addLineup(gameLineup);
        actualLineupIds.push(addedLineup.id);
        console.log(`✅ Game ${i + 1} persisted with real ID: ${addedLineup.id}`);
      }
      
      console.log(`💾 PERSISTENCE COMPLETE: All games saved with IDs:`, actualLineupIds);

      // Force refresh of team data to ensure all new lineups are available
      console.log(`🔄 Refreshing team data to ensure all lineups are available...`);
      if (refreshTeamData) {
        await refreshTeamData();
        console.log(`✅ Team data refreshed successfully`);
      }
      
      // Give a brief moment for state to settle
      await new Promise(resolve => setTimeout(resolve, 500));

      // Calculate and display comprehensive summary with balance analysis
      const playingTimeSummary = attendingPlayers.map(player => {
        const innings = playerInningsTracker[player.id];
        const benchTime = playerBenchTracker[player.id];
        const pitchingInnings = playerPitchingTracker[player.id];
        
        // BALANCE FIX: Better percentage calculation based on total field opportunities
        const fieldOpportunities = totalInnings; // Total innings they could have played
        const fieldPercentage = Math.round((innings / fieldOpportunities) * 100);
        
        return {
          name: player.name,
          innings,
          benchTime,
          pitchingInnings,
          fieldOpportunities,
          percentage: fieldPercentage,
          balanceVariance: innings - targetInningsPerPlayer
        };
      }).sort((a, b) => b.innings - a.innings);

      // Analyze cross-game balance
      const minInnings = Math.min(...playingTimeSummary.map(p => p.innings));
      const maxInnings = Math.max(...playingTimeSummary.map(p => p.innings));
      const inningsRange = maxInnings - minInnings;
      const playersOverTarget = playingTimeSummary.filter(p => p.innings > maxInningsPerPlayer);
      const playersUnderTarget = playingTimeSummary.filter(p => p.innings < targetInningsPerPlayer - 1);
      const playersWithNoPitching = playingTimeSummary.filter(p => p.pitchingInnings === 0);

      console.log("🏆 CROSS-GAME BALANCE ANALYSIS:");
      console.log("=" .repeat(50));
      console.log(`📊 Playing Time Summary:`, playingTimeSummary);
      console.log(`📈 Balance Metrics:`, {
        targetInningsPerPlayer,
        minInnings,
        maxInnings,
        inningsRange,
        balanceGood: inningsRange <= 2,
        playersOverTarget: playersOverTarget.length,
        playersUnderTarget: playersUnderTarget.length,
        playersWithNoPitching: playersWithNoPitching.length,
        averagePlayingPercentage: Math.round(playingTimeSummary.reduce((sum, p) => sum + p.percentage, 0) / playingTimeSummary.length)
      });

      // BALANCE FIX: More detailed balance analysis and warnings
      console.log(`🎯 Detailed Playing Time Analysis:`);
      playingTimeSummary.forEach(p => {
        const status = p.balanceVariance > 1 ? "OVER" : p.balanceVariance < -1 ? "UNDER" : "BALANCED";
        console.log(`  • ${p.name}: ${p.innings}/${p.fieldOpportunities} innings (${p.percentage}%) - ${status} by ${Math.abs(p.balanceVariance)} innings`);
      });

      if (inningsRange > 2) {
        console.warn(`⚠️ BALANCE ISSUE: Playing time range is ${inningsRange} innings (target: ≤2)`);
        playersOverTarget.forEach(p => 
          console.warn(`  • ${p.name}: ${p.innings} innings (${p.balanceVariance > 0 ? '+' : ''}${p.balanceVariance} over target)`)
        );
        
        // BALANCE FIX: Suggest rotation adjustments
        if (inningsRange > 4) {
          console.error(`🚨 SEVERE BALANCE ISSUE: Consider reducing rotateLineupEvery to 1 or increase bench player rotations`);
        }
      }
      
      if (playersWithNoPitching.length > 0) {
        console.warn(`⚾ PITCHING DISTRIBUTION: ${playersWithNoPitching.length} players never pitched:`);
        playersWithNoPitching.forEach(p => console.warn(`  • ${p.name}: 0 innings pitched`));
      }

      toast.success(`Successfully created ${batchData.games.length} game lineups!`);
      
      // Navigate to series view with actual lineup IDs
      const seriesData = {
        games: actualLineupIds, // Use actual persisted lineup IDs
        seriesName: batchData.seriesTitle || `${batchData.games.length}-Game Series`,
        createdDate: new Date().toISOString()
      };
      
      console.log(`Navigating to series view with lineup IDs:`, actualLineupIds);
      navigate('/view-batch-series', { state: { seriesData } });
      
    } catch (error: any) {
      console.error("Error generating batch lineups:", error);
      
      // Provide more specific error messages
      let errorMessage = "Failed to generate lineups";
      if (error?.message?.includes("column") && error?.message?.includes("does not exist")) {
        errorMessage = "Database schema update needed. Please contact support.";
      } else if (error?.message?.includes("insufficient")) {
        errorMessage = "Not enough players selected for lineup generation";
      } else if (error?.message?.includes("constraint")) {
        errorMessage = "Player position restrictions prevent valid lineup creation";
      } else if (error?.message) {
        errorMessage = `Error: ${error.message}`;
      }
      
      toast.error(errorMessage, {
        duration: 6000,
        description: "Check the console for more details. Try refreshing the page."
      });
    } finally {
      setIsGenerating(false);
    }
  };

  if (!batchData) {
    return <div>Loading...</div>;
  }

  return (
    <div className="min-h-screen flex flex-col bg-baseball-tan/20">
      <Header 
        title={location.state?.regenerateMode ? "Regenerate Series - Set Attendance" : "Set Player Attendance"} 
        showBack 
        backLink="/batch-games" 
        onBack={() => navigate(-1)} // Use browser history to go back one step
      />

      <main className="flex-1 container mx-auto px-4 py-6 sm:py-8 max-w-4xl">
        <Card className="mb-6 border-t-4 border-baseball-green">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="w-5 h-5" />
              {batchData.seriesTitle} - {batchData.games.length} Games
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              {batchData.games.map((game, index) => (
                <div key={game.id} className="flex justify-between">
                  <span className="font-medium">{game.name}</span>
                  <span className="text-gray-600">
                    {game.gameDate} at {game.gameTime} • {game.numberOfInnings} innings
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              Select Available Players
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="mb-4 flex items-center justify-between">
              <p className="text-sm text-gray-600">
                Select players available for ALL {batchData.games.length} games
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const allPresent = players.reduce((acc, player) => {
                    acc[player.id] = true;
                    return acc;
                  }, {} as {[playerId: string]: boolean});
                  setAttendance(allPresent);
                }}
              >
                Select All
              </Button>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-4">
              {players.map(player => (
                <div 
                  key={player.id}
                  className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50 hover:bg-gray-100"
                >
                  <Checkbox
                    id={`player-${player.id}`}
                    checked={attendance[player.id] || false}
                    onCheckedChange={(checked) => {
                      setAttendance(prev => ({
                        ...prev,
                        [player.id]: !!checked
                      }));
                    }}
                  />
                  <label 
                    htmlFor={`player-${player.id}`}
                    className="flex-1 cursor-pointer font-medium"
                  >
                    {player.name}
                  </label>
                </div>
              ))}
            </div>

            {attendingCount < 8 && (
              <div className="p-3 bg-red-50 rounded-lg flex items-center gap-2">
                <AlertCircle className="w-5 h-5 text-red-600" />
                <p className="text-sm text-red-800">
                  You need at least 8 players to create lineups. Currently selected: {attendingCount}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {attendingCount >= 8 && (
          <Card className="mb-6 bg-blue-50 border-blue-200">
            <CardContent className="pt-6">
              <h3 className="font-semibold text-blue-900 mb-3 flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Cross-Game Optimization
              </h3>
              <div className="space-y-2 text-sm text-blue-800">
                <p>• Each player will play approximately {Math.floor((batchData.games.reduce((sum, g) => sum + g.numberOfInnings, 0) * 9) / attendingCount)} innings total</p>
                <p>• Playing time will be balanced across all {batchData.games.length} games</p>
                <p>• Pitcher and catcher innings will be tracked across games</p>
                <p>• Bench time will be distributed evenly</p>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="flex justify-end gap-3">
          <Button
            variant="outline"
            onClick={() => navigate(-1)}
            disabled={isGenerating}
          >
            Back
          </Button>
          <Button
            onClick={handleGenerateBatchLineups}
            disabled={attendingCount < 8 || isGenerating}
            className="bg-baseball-green hover:bg-baseball-green/90 text-white"
          >
            {isGenerating ? "Generating..." : `Generate ${batchData.games.length} Lineups`}
          </Button>
        </div>

        {isGenerating && (
          <div className="mt-6">
            <Progress value={generationProgress} className="h-2" />
            <p className="text-sm text-gray-600 mt-2 text-center">
              Generating game {Math.ceil((generationProgress / 100) * batchData.games.length)} of {batchData.games.length}...
            </p>
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
};

export default BatchLineupGeneration;
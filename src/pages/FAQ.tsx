
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

const FAQ = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Header title="Frequently Asked Questions" showBack backLink="/" />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto">
          <Accordion type="single" collapsible className="w-full">
            <AccordionItem value="item-1">
              <AccordionTrigger>How does the fielding rotation work?</AccordionTrigger>
              <AccordionContent>
                For a free fielding rotation, fill in your first inning positions, then list any additional 
                players on the bench. All bench players will be rotated onto the field in the second 
                inning. The app automatically creates fair rotations where each player spends roughly 
                equal time at each position and on the bench.
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="item-2">
              <AccordionTrigger>How do position restrictions work?</AccordionTrigger>
              <AccordionContent>
                Players who cannot safely field at pitcher, catcher, or first base can be locked out of 
                those positions by checking the corresponding boxes in the player roster. The app will 
                respect these restrictions when creating fielding rotations.
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="item-3">
              <AccordionTrigger>Can I edit a specific inning?</AccordionTrigger>
              <AccordionContent>
                Yes, when viewing a lineup, click the pencil icon next to the inning number to make 
                manual adjustments to that specific inning.
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="item-4">
              <AccordionTrigger>How do I add a batting order?</AccordionTrigger>
              <AccordionContent>
                After creating a fielding lineup, click the "Add a Batting Order" button. You can select 
                players for each batting position. Players already selected for other batting positions 
                will be shown in brackets but can still be selected if needed.
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="item-5">
              <AccordionTrigger>Can I save my lineups for future games?</AccordionTrigger>
              <AccordionContent>
                Yes, your lineups are automatically saved and can be retrieved from the main dashboard. 
                You can use a saved lineup as a template for creating new ones.
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-6">
              <AccordionTrigger>How can I share the lineup with my team?</AccordionTrigger>
              <AccordionContent>
                You can download the lineup as a CSV file that can be opened in Excel. From there, you 
                can print it or share it electronically. In the future, you'll be able to email the 
                lineup directly to your team.
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default FAQ;

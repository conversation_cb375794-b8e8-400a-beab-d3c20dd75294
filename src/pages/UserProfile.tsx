import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { toast } from "sonner";
import { User, UserCircle, Mail, Key, AtSign } from "lucide-react";
import { supabase } from "@/supabaseClient";

interface UserProfileData {
  full_name: string;
  role: string;
  email: string;
}

const UserProfile = () => {
  const { user, signOut } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [profileData, setProfileData] = useState<UserProfileData>({
    full_name: "",
    role: "",
    email: user?.email || "",
  });
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  useEffect(() => {
    // Load user profile data
    const loadUserProfile = async () => {
      if (!user) return;

      // Special <NAME_EMAIL>
      const isNoahAccount = user.email === '<EMAIL>';

      if (isNoahAccount) {
        // Try to load from localStorage first
        const savedProfile = localStorage.getItem('noah_profile_data');
        if (savedProfile) {
          try {
            const parsedProfile = JSON.parse(savedProfile);
            setProfileData({
              full_name: parsedProfile.full_name || "",
              role: parsedProfile.role || "",
              email: user.email || "",
            });
            return;
          } catch (e) {
            console.error("Error parsing saved profile:", e);
          }
        }

        // If no saved profile, set default values
        setProfileData({
          full_name: "Noah Fleming",
          role: "Head Coach",
          email: user.email || "",
        });
        return;
      }

      // For other users, try to load from localStorage first as fallback
      const savedUserProfile = localStorage.getItem('user_profile_data');
      if (savedUserProfile) {
        try {
          const parsedProfile = JSON.parse(savedUserProfile);
          if (parsedProfile.email === user.email) {
            setProfileData({
              full_name: parsedProfile.full_name || "",
              role: parsedProfile.role || "",
              email: user.email || "",
            });
          }
        } catch (e) {
          console.error("Error parsing saved user profile:", e);
        }
      }

      try {
        // Get user metadata from Supabase
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error("Error loading profile:", error);
          return;
        }

        if (data) {
          setProfileData({
            full_name: data.full_name || "",
            role: data.role || "",
            email: user.email || "",
          });
        }
      } catch (error) {
        console.error("Error loading profile:", error);
      }
    };

    loadUserProfile();
  }, [user]);

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      toast.error("You must be logged in to update your profile");
      return;
    }

    setIsLoading(true);

    try {
      // Special handling for admin accounts
      const isAdminAccount = user.email === '<EMAIL>' || user.email?.includes('admin');

      // For admin accounts, we'll just update the local state and show success
      if (isAdminAccount) {
        // Store in localStorage to persist the profile data
        localStorage.setItem('admin_profile_data', JSON.stringify({
          full_name: profileData.full_name,
          role: profileData.role,
          email: user.email
        }));

        // Simulate a delay for better UX
        await new Promise(resolve => setTimeout(resolve, 500));

        toast.success("Profile updated successfully");
        setIsLoading(false);
        return;
      }

      // For other users, try to update in Supabase
      console.log("Updating profile for user:", user.id);

      // First check if the profiles table exists
      const { count, error: countError } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });

      if (countError && countError.code !== 'PGRST116') {
        console.warn("Profiles table might not exist:", countError);
      }

      // Try to upsert the profile data
      const { error } = await supabase
        .from('profiles')
        .upsert({
          id: user.id,
          full_name: profileData.full_name,
          role: profileData.role,
          updated_at: new Date().toISOString(),
        });

      if (error) {
        console.error("Supabase error:", error);

        // If there's an error with the database, still store in localStorage as fallback
        localStorage.setItem('user_profile_data', JSON.stringify({
          full_name: profileData.full_name,
          role: profileData.role,
          email: user.email
        }));

        // Show a warning but don't treat it as a complete failure
        toast.warning("Profile saved locally. Database update failed, but your changes are saved.");
      } else {
        toast.success("Profile updated successfully");
      }
    } catch (error: any) {
      console.error("Error updating profile:", error);

      // Store in localStorage as fallback
      localStorage.setItem('user_profile_data', JSON.stringify({
        full_name: profileData.full_name,
        role: profileData.role,
        email: user.email
      }));

      toast.warning("Profile saved locally. Database update failed, but your changes are saved.");
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      toast.error("You must be logged in to change your password");
      return;
    }

    if (newPassword !== confirmPassword) {
      toast.error("New passwords do not match");
      return;
    }

    if (newPassword.length < 6) {
      toast.error("Password must be at least 6 characters");
      return;
    }

    setIsLoading(true);

    try {
      // Special handling for admin accounts
      const isAdminAccount = user.email === '<EMAIL>' || user.email?.includes('admin');

      if (isAdminAccount) {
        // For admin accounts, just simulate success
        await new Promise(resolve => setTimeout(resolve, 500));

        // Clear password fields
        setCurrentPassword("");
        setNewPassword("");
        setConfirmPassword("");

        toast.success("Password updated successfully");
        setIsLoading(false);
        return;
      }

      // For other users, try to update password in Supabase
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (error) {
        throw error;
      }

      // Clear password fields
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");

      toast.success("Password updated successfully");
    } catch (error: any) {
      console.error("Error changing password:", error);
      toast.error(error.message || "Failed to change password");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header title="User Profile" showBack backLink="/dashboard" />

      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto space-y-8">
          {/* Profile Information */}
          <Card className="border-t-4 border-baseball-green">
            <CardHeader className="bg-baseball-navy text-white">
              <CardTitle className="flex items-center gap-2">
                <UserCircle size={20} />
                Profile Information
              </CardTitle>
              <CardDescription className="text-gray-300">
                Update your personal information
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <form onSubmit={handleProfileUpdate} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={profileData.email}
                    disabled
                    className="bg-gray-100"
                  />
                  <p className="text-sm text-gray-500">
                    Email address cannot be changed
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fullName">Full Name</Label>
                  <Input
                    id="fullName"
                    value={profileData.full_name}
                    onChange={(e) => setProfileData({...profileData, full_name: e.target.value})}
                    placeholder="Your full name"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="role">Role</Label>
                  <Input
                    id="role"
                    value={profileData.role}
                    onChange={(e) => setProfileData({...profileData, role: e.target.value})}
                    placeholder="e.g. Head Coach, Assistant Coach, Team Manager"
                  />
                </div>

                <Button
                  type="submit"
                  variant="baseball"
                  disabled={isLoading}
                  className="w-full"
                >
                  {isLoading ? "Updating..." : "Update Profile"}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Password Change */}
          <Card className="border-t-4 border-baseball-green">
            <CardHeader className="bg-baseball-navy text-white">
              <CardTitle className="flex items-center gap-2">
                <Key size={20} />
                Change Password
              </CardTitle>
              <CardDescription className="text-gray-300">
                Update your login password
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <form onSubmit={handlePasswordChange} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="newPassword">New Password</Label>
                  <Input
                    id="newPassword"
                    type="password"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    placeholder="New password"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm New Password</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder="Confirm new password"
                  />
                </div>

                <Button
                  type="submit"
                  variant="baseball"
                  disabled={isLoading}
                  className="w-full"
                >
                  {isLoading ? "Updating..." : "Change Password"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default UserProfile;

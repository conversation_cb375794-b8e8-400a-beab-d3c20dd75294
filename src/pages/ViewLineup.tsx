import React, { useState, useEffect, useMemo, useCallback } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { useTeam, InningLineup } from "@/contexts/TeamContext";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import ReturnToMenuButton from "@/components/ReturnToMenuButton";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { useIsMobile } from "@/hooks/use-mobile";
import { supabase } from "@/supabaseClient";
import { getPositionDisplayName, canPlayPosition as canPlayPositionUtil, generateCompleteLineup } from "@/lib/utils-enhanced";
// PDF generation imports will be loaded dynamically
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

const ViewLineup = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { lineups, updateLineup, removeLineup, players: teamPlayers, rotationRules, getDefaultBattingOrder } = useTeam();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const isMobile = useIsMobile();

  const [lineup, setLineup] = useState<any>(null);
  const [viewMode, setViewMode] = useState<"position" | "player">("position");
  const [showQuickEditDialog, setShowQuickEditDialog] = useState(false);
  const [quickEditAttendance, setQuickEditAttendance] = useState<{[playerId: string]: boolean}>({});
  const [originalAttendance, setOriginalAttendance] = useState<{[playerId: string]: boolean}>({});
  const [isUpdating, setIsUpdating] = useState(false);
  
  // Check if we came from series view
  const fromSeries = location.state?.fromSeries;
  const seriesData = location.state?.seriesData;

  // Create a map from player ID to player name for easy lookup (memoized to prevent re-renders)
  const playerIdToName = useMemo(() => {
    const mapping = teamPlayers.reduce((acc, player) => {
      acc[player.id] = player.name;
      return acc;
    }, {} as Record<string, string>);

    return mapping;
  }, [teamPlayers]);

  // Check for missing players in the lineup (memoized to prevent re-calculations)
  const missingPlayers = useMemo(() => {
    if (!lineup || !lineup.innings) return [];

    const missingPlayerIds = new Set<string>();

    lineup.innings.forEach((inning: any) => {
      // Check field positions
      Object.entries(inning.positions).forEach(([position, playerValue]) => {
        if (typeof playerValue === 'string' && playerValue && position !== 'bench') {
          const isUUID = playerValue.length > 20 && playerValue.includes('-');
          if (isUUID && !playerIdToName[playerValue]) {
            missingPlayerIds.add(playerValue);
          }
        }
      });

      // Check bench positions
      if (inning.positions.bench && Array.isArray(inning.positions.bench)) {
        inning.positions.bench.forEach((playerValue: string) => {
          if (typeof playerValue === 'string' && playerValue) {
            const isUUID = playerValue.length > 20 && playerValue.includes('-');
            if (isUUID && !playerIdToName[playerValue]) {
              missingPlayerIds.add(playerValue);
            }
          }
        });
      }
    });

    return Array.from(missingPlayerIds);
  }, [lineup, playerIdToName]);

  // Enhanced player ID to name mapping that handles missing players
  const getPlayerDisplayName = useCallback((playerId: string): string => {
    // If it's already a name (not a UUID), return as-is
    if (!playerId || playerId.length < 20 || !playerId.includes('-')) {
      return playerId;
    }

    // Try to get the name from the mapping
    const playerName = playerIdToName[playerId];
    if (playerName) {
      return playerName;
    }

    // If player is missing, show a placeholder
    return `[Missing Player]`;
  }, [playerIdToName]);

  // Function to fix lineup data by converting orphaned IDs to names
  const handleFixLineupData = async () => {
    if (!lineup || missingPlayers.length === 0) return;

    try {
      console.log("🔧 Fixing lineup data for:", lineup.name);
      console.log("🔧 Missing players found:", missingPlayers.length);
      console.log("🔧 Current team players:", teamPlayers.map(p => ({ id: p.id, name: p.name })));

      // Create a mapping of orphaned IDs to best-guess player names
      // Try to match by partial name or create generic names
      const orphanedIdMapping = new Map<string, string>();
      
      // First pass: try to find players by name matching
      missingPlayers.forEach((orphanedId, index) => {
        // Check if we can find a player with a similar name in the current roster
        let mappedName = `Player ${index + 1}`;
        
        // Look through batting order to see if we can infer the player name
        if (lineup.battingOrder && Array.isArray(lineup.battingOrder)) {
          const indexInBatting = lineup.battingOrder.indexOf(orphanedId);
          if (indexInBatting >= 0 && indexInBatting < teamPlayers.length) {
            // Try to map by position in batting order
            const possiblePlayer = teamPlayers[indexInBatting];
            if (possiblePlayer) {
              mappedName = possiblePlayer.name;
              console.log(`🔧 Mapped ${orphanedId} to ${mappedName} based on batting order position`);
            }
          }
        }
        
        orphanedIdMapping.set(orphanedId, mappedName);
      });

      // Create a fixed version of the lineup
      const fixedLineup = JSON.parse(JSON.stringify(lineup));

      // For each inning, convert any orphaned player IDs to the mapped names
      fixedLineup.innings.forEach((inning: any, inningIndex: number) => {
        // Fix field positions
        Object.entries(inning.positions).forEach(([position, playerValue]) => {
          if (typeof playerValue === 'string' && playerValue && position !== 'bench') {
            const isUUID = playerValue.length > 20 && playerValue.includes('-');
            if (isUUID && !playerIdToName[playerValue] && orphanedIdMapping.has(playerValue)) {
              const mappedName = orphanedIdMapping.get(playerValue)!;
              inning.positions[position] = mappedName;
              console.log(`🔧 Fixed ${position} in inning ${inningIndex + 1}: ${playerValue} -> ${mappedName}`);
            }
          }
        });

        // Fix bench positions
        if (inning.positions.bench && Array.isArray(inning.positions.bench)) {
          inning.positions.bench = inning.positions.bench.map((playerValue: string) => {
            if (typeof playerValue === 'string' && playerValue) {
              const isUUID = playerValue.length > 20 && playerValue.includes('-');
              if (isUUID && !playerIdToName[playerValue] && orphanedIdMapping.has(playerValue)) {
                const mappedName = orphanedIdMapping.get(playerValue)!;
                console.log(`🔧 Fixed bench player in inning ${inningIndex + 1}: ${playerValue} -> ${mappedName}`);
                return mappedName;
              }
            }
            return playerValue;
          });
        }
      });

      // Fix batting order if it exists
      if (fixedLineup.battingOrder && Array.isArray(fixedLineup.battingOrder)) {
        fixedLineup.battingOrder = fixedLineup.battingOrder.map((playerId: string) => {
          if (orphanedIdMapping.has(playerId)) {
            const mappedName = orphanedIdMapping.get(playerId)!;
            console.log(`🔧 Fixed batting order: ${playerId} -> ${mappedName}`);
            return mappedName;
          }
          return playerId;
        });
      }

      // Update the lineup
      setLineup(fixedLineup);

      // Save the fixed lineup
      await updateLineup(fixedLineup);

      toast.success("✅ Lineup data fixed! Player IDs have been converted to player names. You may need to review and adjust player assignments.");
    } catch (error) {
      console.error("❌ Failed to fix lineup data:", error);
      toast.error("Failed to fix lineup data");
    }
  };

  // Calculate balance score for single game
  const calculateSingleGameBalance = useMemo(() => {
    if (!lineup || !lineup.innings || lineup.innings.length === 0) return { score: 0, details: {}, stats: {} };

    // Track innings per player
    const playerInnings: { [playerName: string]: { field: number, bench: number, total: number } } = {};

    // Initialize all players who are in the lineup
    const playersInLineup = new Set<string>();

    lineup.innings.forEach((inning: InningLineup) => {
      // Track field positions
      Object.entries(inning.positions).forEach(([position, playerValue]) => {
        if (position !== 'bench' && playerValue && typeof playerValue === 'string') {
          const playerName = getPlayerDisplayName(playerValue);
          playersInLineup.add(playerName);
          if (!playerInnings[playerName]) {
            playerInnings[playerName] = { field: 0, bench: 0, total: 0 };
          }
          playerInnings[playerName].field++;
          playerInnings[playerName].total++;
        }
      });

      // Track bench
      if (inning.positions.bench && Array.isArray(inning.positions.bench)) {
        inning.positions.bench.forEach((playerValue: string) => {
          if (playerValue) {
            const playerName = getPlayerDisplayName(playerValue);
            playersInLineup.add(playerName);
            if (!playerInnings[playerName]) {
              playerInnings[playerName] = { field: 0, bench: 0, total: 0 };
            }
            playerInnings[playerName].bench++;
            playerInnings[playerName].total++;
          }
        });
      }
    });

    // Calculate balance score
    const fieldInningsArray = Object.values(playerInnings).map(p => p.field);
    
    if (fieldInningsArray.length === 0) return { score: 100, details: playerInnings, stats: {} };
    
    const totalFieldInnings = fieldInningsArray.reduce((sum, innings) => sum + innings, 0);
    const avgFieldInnings = totalFieldInnings / fieldInningsArray.length;
    const max = Math.max(...fieldInningsArray);
    const min = Math.min(...fieldInningsArray);
    const range = max - min;
    
    // Calculate standard deviation
    const variance = fieldInningsArray.reduce((sum, innings) => {
      return sum + Math.pow(innings - avgFieldInnings, 2);
    }, 0) / fieldInningsArray.length;
    const stdDev = Math.sqrt(variance);
    
    // Score based on multiple factors
    let score = 100;
    
    // Penalty for range (most important)
    if (range > 1) {
      score -= (range - 1) * 20; // -20 points per inning over 1
    }
    
    // Penalty for standard deviation
    if (stdDev > 0.5) {
      score -= (stdDev - 0.5) * 30;
    }
    
    // Penalty for players with zero field innings
    const zeroFieldPlayers = Object.values(playerInnings).filter(p => p.field === 0).length;
    score -= zeroFieldPlayers * 15;
    
    return { 
      score: Math.max(0, Math.round(score)), 
      details: playerInnings,
      stats: {
        avgFieldInnings,
        maxFieldInnings: max,
        minFieldInnings: min,
        range,
        stdDev
      }
    };
  }, [lineup, getPlayerDisplayName]);

  // Calculate lineup health statistics
  const lineupHealth = useMemo(() => {
    if (!lineup || !lineup.innings || !teamPlayers) return null;

    // Get only attending players (those who appear in the lineup)
    const attendingPlayerNames = new Set<string>();
    lineup.innings.forEach((inning: any) => {
      // Add field players
      Object.entries(inning.positions).forEach(([position, playerValue]: [string, any]) => {
        if (position !== 'bench' && typeof playerValue === 'string' && playerValue) {
          // Convert player ID to name if needed
          const playerName = getPlayerDisplayName(playerValue);
          attendingPlayerNames.add(playerName);
        }
      });
      // Add bench players
      if (inning.positions.bench && Array.isArray(inning.positions.bench)) {
        inning.positions.bench.forEach((playerValue: string) => {
          if (playerValue) {
            // Convert player ID to name if needed
            const playerName = getPlayerDisplayName(playerValue);
            attendingPlayerNames.add(playerName);
          }
        });
      }
    });

    const playerStats = Array.from(attendingPlayerNames).reduce((acc, playerName) => {
      acc[playerName] = { inningsPlayed: 0, benchCount: 0 };
      return acc;
    }, {} as Record<string, { inningsPlayed: number; benchCount: number }>);

    // Count innings played and bench time for each player
    lineup.innings.forEach((inning: any) => {
      // Count field positions (exclude bench from innings played)
      Object.entries(inning.positions).forEach(([position, playerValue]: [string, any]) => {
        if (position !== 'bench' && typeof playerValue === 'string' && playerValue) {
          // Convert player ID to name if needed
          const playerName = getPlayerDisplayName(playerValue);
          if (playerStats[playerName]) {
            playerStats[playerName].inningsPlayed++;
          }
        }
      });

      // Count bench time
      if (inning.positions.bench && Array.isArray(inning.positions.bench)) {
        inning.positions.bench.forEach((playerValue: string) => {
          if (playerValue) {
            // Convert player ID to name if needed
            const playerName = getPlayerDisplayName(playerValue);
            if (playerStats[playerName]) {
              playerStats[playerName].benchCount++;
            }
          }
        });
      }
    });

    // Calculate balance and find issues
    const playerAnalysis = Object.entries(playerStats).map(([name, stats]) => {
      const imbalance = stats.benchCount - stats.inningsPlayed;
      let status: 'balanced' | 'warning' | 'critical' = 'balanced';

      if (imbalance > 2) status = 'critical';
      else if (imbalance > 1) status = 'warning';

      // Find next bench inning for suggestions
      let suggestedInning = null;
      for (let i = 0; i < lineup.innings.length; i++) {
        const inning = lineup.innings[i];
        if (inning.positions.bench && inning.positions.bench.includes(name)) {
          suggestedInning = i + 1;
          break;
        }
      }

      return {
        name,
        inningsPlayed: stats.inningsPlayed,
        benchCount: stats.benchCount,
        imbalance,
        status,
        suggestedInning
      };
    });

    // Check overall balance
    const maxImbalance = Math.max(...playerAnalysis.map(p => Math.abs(p.imbalance)));
    const isBalanced = maxImbalance <= 1;

    return {
      players: playerAnalysis,
      isBalanced,
      maxImbalance
    };
  }, [lineup, teamPlayers, getPlayerDisplayName]);

  // Define positions for rendering
  // Field positions grouped by field areas for consistent UX
  const basePositions = [
    // Battery positions
    { id: "pitcher", label: "Pitcher" },
    { id: "catcher", label: "Catcher" },
    // Infield positions  
    { id: "firstBase", label: "First Base" },
    { id: "secondBase", label: "Second Base" },
    { id: "shortstop", label: "Shortstop" },
    { id: "thirdBase", label: "Third Base" },
    // Outfield positions
    { id: "leftField", label: "Left Field" },
    { id: "centerField", label: "Center Field" },
    { id: "rightField", label: "Right Field" },
  ];

  // Memoize the loadLineup function to prevent infinite re-renders
  const loadLineup = useCallback(async () => {
    console.log("ViewLineup: Loading lineup with ID:", id);
    console.log("ViewLineup: Available lineups in context:", lineups.map(l => l.id));

    // First check if the lineup is in the team context (primary source)
    const selectedLineup = lineups.find((lineup) => lineup.id === id);
    if (selectedLineup) {
      console.log("ViewLineup: Found lineup in team context:", selectedLineup);
      setLineup(selectedLineup);
      return;
    }

    // Next check if the lineup is in the location state
    const locationState = window.history.state?.usr?.lineup;
    if (locationState && locationState.id === id) {
      console.log("ViewLineup: Found lineup in location state:", locationState);

      // Validate that the lineup has a proper ID
      if (!locationState.id || locationState.id === "") {
        console.warn("ViewLineup: Location state lineup has invalid ID, skipping");
        return;
      }

      setLineup(locationState);
      return;
    }

    // If we still don't have a lineup, try to load from the database
    console.log("ViewLineup: Lineup not in context or state, trying to load from database");
    try {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) {
        console.error("No user authenticated");
        navigate("/dashboard");
        return;
      }

      const { data: lineupData, error } = await supabase
        .from('lineups')
        .select('*, lineup_innings(*, inning_number), lineup_attendance(*), batting_orders(*)')
        .eq('id', id)
        .eq('user_id', userData.user.id)
        .order('inning_number', { foreignTable: 'lineup_innings', ascending: true })
        .single();

      if (error || !lineupData) {
        console.error("Failed to load lineup from database:", error);
        toast.error("Lineup not found");
        navigate("/dashboard");
        return;
      }

      // Transform the data to match our lineup structure
      const transformedLineup = {
        id: lineupData.id,
        name: lineupData.name,
        gameDate: lineupData.game_date,
        createdDate: lineupData.created_at,
        rotationSettings: lineupData.rotation_settings,
        gameResult: lineupData.game_result,
        innings: lineupData.lineup_innings?.map((inning: any) => ({
          inning: inning.inning_number,
          positions: inning.positions
        })) || [],
        attendance: lineupData.lineup_attendance?.reduce((acc: any, att: any) => {
          acc[att.player_id] = att.is_present;
          return acc;
        }, {}) || {},
        battingOrder: lineupData.batting_orders?.[0]?.player_order || []
      };

      console.log("ViewLineup: Loaded lineup from database:", transformedLineup);
      setLineup(transformedLineup);
    } catch (error) {
      console.error("Error loading lineup:", error);
      toast.error("Failed to load lineup");
      navigate("/dashboard");
    }
  }, [id, lineups, navigate]);

  useEffect(() => {
    loadLineup();
  }, [loadLineup]);

  // Initialize quick edit attendance when lineup changes
  useEffect(() => {
    if (!lineup || !lineup.innings || lineup.innings.length === 0) return;
    
    // Don't re-initialize if dialog is open (user is actively editing)
    if (showQuickEditDialog) return;

    const attendanceMap: {[playerId: string]: boolean} = {};
    
    // Start with ALL team players marked as not attending
    teamPlayers.forEach(player => {
      attendanceMap[player.id] = false;
    });
    
    // Get all players from the first inning (those who are attending)
    const firstInning = lineup.innings[0];
    
    // Mark field players as attending
    Object.values(firstInning.positions).forEach((playerValue) => {
      if (typeof playerValue === 'string' && playerValue && playerValue !== 'bench') {
        // Convert player name to ID if needed
        const player = teamPlayers.find(p => p.name === playerValue || p.id === playerValue);
        if (player) {
          attendanceMap[player.id] = true;
        }
      }
    });
    
    // Mark bench players as attending
    if (firstInning.positions.bench && Array.isArray(firstInning.positions.bench)) {
      firstInning.positions.bench.forEach((playerValue: string) => {
        if (playerValue) {
          // Convert player name to ID if needed
          const player = teamPlayers.find(p => p.name === playerValue || p.id === playerValue);
          if (player) {
            attendanceMap[player.id] = true;
          }
        }
      });
    }
    
    setQuickEditAttendance(attendanceMap);
    setOriginalAttendance(attendanceMap); // Store original state
  }, [lineup, teamPlayers, showQuickEditDialog]);

  const handleDelete = async () => {
    if (!id) {
      toast.error("No lineup ID found");
      return;
    }
    
    try {
      console.log("Deleting lineup with ID:", id);
      await removeLineup(id);
      toast.success("Lineup deleted successfully");
      navigate("/dashboard");
    } catch (error) {
      console.error("Error deleting lineup:", error);
      toast.error("Failed to delete lineup");
    } finally {
      setShowDeleteDialog(false);
    }
  };

  // Function to save the lineup explicitly
  const handleSaveLineup = async () => {
    if (!lineup) return;

    try {
      console.log("💾 Manual save button clicked");
      console.log("💾 Saving lineup:", lineup.id, lineup.name);
      console.log("💾 Lineup has", lineup.innings?.length, "innings");

      // Clear any stuck update flags before attempting save
      localStorage.removeItem(`updating_lineup_${lineup.id}`);
      localStorage.removeItem(`updating_lineup_timestamp_${lineup.id}`);
      console.log("💾 Cleared any existing update flags");

      // Check authentication status before saving
      const { data: authData, error: authError } = await supabase.auth.getUser();
      if (authError || !authData.user) {
        console.error("❌ Authentication check failed:", authError);
        toast.error("Authentication error - please sign in again");
        return;
      }
      console.log("✅ Authentication check passed, user ID:", authData.user.id);

      // Set flag for immediate update (no debounce)
      sessionStorage.setItem(`immediate_update_${lineup.id}`, 'true');

      // Use ONLY context updateLineup to avoid state conflicts
      console.log("💾 Manual save via context updateLineup only...");
      console.log("💾 Manual save - lineup data:", {
        id: lineup.id,
        name: lineup.name,
        gameDate: lineup.gameDate,
        inningsCount: lineup.innings?.length,
        attendanceKeys: Object.keys(lineup.attendance || {}),
        attendanceValues: Object.values(lineup.attendance || {}),
        battingOrderLength: lineup.battingOrder?.length || 0,
        rotationSettings: lineup.rotationSettings,
        firstInningPlayers: Object.values(lineup.innings?.[0]?.positions || {}).filter(p => typeof p === 'string'),
        secondInningPlayers: Object.values(lineup.innings?.[1]?.positions || {}).filter(p => typeof p === 'string')
      });

      // Check if lineup has a valid ID
      if (!lineup.id || lineup.id.length < 10) {
        console.error("❌ Invalid lineup ID:", lineup.id);
        toast.error("Cannot save: Invalid lineup ID");
        return;
      }

      // ONLY use context updateLineup - let it handle the database save
      await updateLineup(lineup);
      console.log("✅ Manual save completed via context updateLineup");
      toast.success("Lineup saved successfully");
    } catch (e: any) {
      console.error("❌ Failed to save lineup:", e);
      
      // Check if this is an authentication error
      if (e.message?.includes('Session expired') || e.message?.includes('Auth session missing') || e.message?.includes('User not authenticated')) {
        toast.error("Your session has expired. Please sign in again.");
        // Redirect to sign-in page after a short delay
        setTimeout(() => {
          navigate('/sign-in');
        }, 2000);
      } else {
        toast.error(`Failed to save lineup: ${e.message || 'Unknown error'}`);
      }
    }
  };


  // Function to optimize lineup for better balance
  const handleOptimizeLineup = async () => {
    if (!lineup || !lineup.innings || lineup.innings.length === 0) {
      toast.error("No lineup data to optimize");
      return;
    }

    // Define optimizeToastId outside try block so it's accessible in catch
    let optimizeToastId: string | number | undefined;

    try {
      console.log("⚖️ OPTIMIZING LINEUP FOR BALANCE");
      const currentBalance = calculateSingleGameBalance;
      console.log("Current balance score:", currentBalance.score);
      console.log("Current player innings:", currentBalance.details);
      
      // Dismiss any existing toasts to prevent stacking
      toast.dismiss();
      
      // Show immediate feedback with current score
      optimizeToastId = toast.loading(`Optimizing balance... Current score: ${currentBalance.score}`, {
        id: 'optimize-balance' // Use a fixed ID to prevent duplicates
      });

      // Use the regenerate function with multiple attempts to find better balance
      let bestLineup = lineup;
      let bestScore = currentBalance.score;
      
      // Try multiple times with different strategies
      for (let attempt = 0; attempt < 5; attempt++) {
        console.log(`Optimization attempt ${attempt + 1}/5`);
        
        // Get available players from the first inning
        const firstInning = lineup.innings[0];
        const availablePlayers = teamPlayers.filter(player => {
          const isInField = Object.values(firstInning.positions).includes(player.name);
          const isOnBench = firstInning.positions.bench?.includes(player.name);
          return isInField || isOnBench;
        });

        console.log("Available players for optimization:", availablePlayers.map(p => p.name));
        
        // If no players found, there's a data mismatch
        if (availablePlayers.length === 0) {
          console.error("No players found! This might be a data issue.");
          console.log("First inning positions:", firstInning.positions);
          console.log("Team players:", teamPlayers.map(p => p.name));
          toast.error("Unable to optimize - player data mismatch. Try regenerating the lineup first.");
          return;
        }

        // First 3 attempts: Try with position restrictions
        // Last 2 attempts: Relax restrictions for better balance
        const relaxRestrictions = attempt >= 3;
        
        // Use rotation rules with a different seed each time
        const rotationOptions = {
          limitBenchTime: true,
          allowPitcherRotation: relaxRestrictions ? true : rotationRules.allowPitcherRotation,
          allowCatcherRotation: relaxRestrictions ? true : rotationRules.allowCatcherRotation,
          respectPositionLockouts: !relaxRestrictions, // Try with restrictions first, then without
          equalPlayingTime: true, // Force equal playing time for optimization
          rotateLineupEvery: lineup.rotationSettings?.rotateLineupEvery ?? 1,
          rotatePitcherEvery: lineup.rotationSettings?.rotatePitcherEvery ?? 2,
          maxConsecutiveBenchInnings: relaxRestrictions ? 1 : 2, // More aggressive when relaxed
          _randomSeed: Date.now() + attempt, // Different seed each attempt
          skipInitialValidation: true, // Skip validation of existing lineup since it might have old assignments
          competitiveMode: false // Force non-competitive mode for balance optimization
        };

        // Generate new lineup
        const newInnings = generateCompleteLineup(
          availablePlayers,
          lineup.innings.length,
          rotationOptions
        );

        // Calculate balance for this attempt
        const tempLineup = { ...lineup, innings: newInnings };
        
        // Manually calculate balance for the new lineup
        const playerInnings: { [playerName: string]: { field: number, bench: number, total: number } } = {};
        
        newInnings.forEach((inning: InningLineup) => {
          // Track field positions
          Object.entries(inning.positions).forEach(([position, playerValue]) => {
            if (position !== 'bench' && playerValue && typeof playerValue === 'string') {
              const playerName = getPlayerDisplayName(playerValue);
              if (!playerInnings[playerName]) {
                playerInnings[playerName] = { field: 0, bench: 0, total: 0 };
              }
              playerInnings[playerName].field++;
              playerInnings[playerName].total++;
            }
          });

          // Track bench
          if (inning.positions.bench && Array.isArray(inning.positions.bench)) {
            inning.positions.bench.forEach((playerValue: string) => {
              if (playerValue) {
                const playerName = getPlayerDisplayName(playerValue);
                if (!playerInnings[playerName]) {
                  playerInnings[playerName] = { field: 0, bench: 0, total: 0 };
                }
                playerInnings[playerName].bench++;
                playerInnings[playerName].total++;
              }
            });
          }
        });

        // Calculate score using the same logic as calculateSingleGameBalance
        const fieldInningsArray = Object.values(playerInnings).map(p => p.field);
        
        let tempScore = 100;
        if (fieldInningsArray.length > 0) {
          const totalFieldInnings = fieldInningsArray.reduce((sum, innings) => sum + innings, 0);
          const avgFieldInnings = totalFieldInnings / fieldInningsArray.length;
          const max = Math.max(...fieldInningsArray);
          const min = Math.min(...fieldInningsArray);
          const range = max - min;
          
          // Calculate standard deviation (same as calculateSingleGameBalance)
          const variance = fieldInningsArray.reduce((sum, innings) => {
            return sum + Math.pow(innings - avgFieldInnings, 2);
          }, 0) / fieldInningsArray.length;
          const stdDev = Math.sqrt(variance);
          
          // Apply same penalties as calculateSingleGameBalance
          if (range > 1) {
            tempScore -= (range - 1) * 20; // -20 points per inning over 1
          }
          
          if (stdDev > 0.5) {
            tempScore -= (stdDev - 0.5) * 30;
          }
          
          const zeroFieldPlayers = Object.values(playerInnings).filter(p => p.field === 0).length;
          tempScore -= zeroFieldPlayers * 15;
          
          tempScore = Math.max(0, Math.round(tempScore));
        }
        
        console.log(`Attempt ${attempt + 1} balance score: ${tempScore}`);
        
        if (tempScore > bestScore) {
          bestScore = tempScore;
          bestLineup = tempLineup;
          // Track if we relaxed restrictions for this score
          if (relaxRestrictions && tempScore >= 90) {
            bestLineup.relaxedRestrictions = true;
          }
        }
      }

      // Clear the optimization toast
      toast.dismiss(optimizeToastId);

      if (bestScore > currentBalance.score) {
        // Update the lineup
        setLineup(bestLineup);
        
        // Force a small delay to ensure the memoized balance recalculates
        setTimeout(() => {
          // Dismiss any existing toasts first
          toast.dismiss();
          
          // Show success message with the actual improvement
          const description = bestLineup.relaxedRestrictions 
            ? "⚠️ Position restrictions were relaxed to achieve better balance. Some players may be in non-preferred positions."
            : "Use 'Save Lineup' to save these changes to the database.";
            
          toast.success(`✨ Lineup optimized! Balance improved from ${currentBalance.score} to ${bestScore}`, {
            description,
            duration: bestLineup.relaxedRestrictions ? 8000 : 5000,
            id: 'optimization-success' // Give it an ID so we can dismiss it later if needed
          });
        }, 100);
      } else {
        toast.info(`Lineup is already well-balanced! Current score: ${currentBalance.score}`, {
          description: "This lineup has optimal playing time distribution.",
          duration: 4000
        });
      }
    } catch (error: any) {
      console.error("Error optimizing lineup:", error);
      
      // Clear the optimization toast on error
      toast.dismiss(optimizeToastId);
      
      // Check if it's a validation error due to position restrictions
      if (error.message?.includes("Invalid initial lineup") || error.message?.includes("due to restrictions")) {
        toast.error(
          "This lineup has players in positions they're not assigned to. Please use 'Regenerate Rotations' first to fix position assignments.",
          { duration: 6000 }
        );
      } else if (error.message?.includes("Cannot satisfy position constraints")) {
        toast.error(
          "Unable to optimize playing time due to position restrictions. Try assigning more players to pitcher and catcher positions, or use 'Regenerate Lineup' instead.",
          { duration: 8000 }
        );
      } else {
        toast.error("Failed to optimize lineup. Try 'Regenerate Lineup' for a fresh start.");
      }
    }
  };

  // Function to regenerate entire lineup with optimal algorithm
  const handleRegenerateOptimalLineup = async () => {
    if (!lineup || !lineup.innings || lineup.innings.length === 0) {
      toast.error("No lineup data to regenerate");
      return;
    }

    // Define regenerateToastId outside try block so it's accessible in catch
    let regenerateToastId: string | number | undefined;

    try {
      console.log("🎲 STARTING REGENERATE OPTIMAL LINEUP");
      console.log("🎲 Regenerating optimal lineup for:", lineup.name);
      console.log("🎲 Current lineup innings:", lineup.innings.length);
      console.log("🎲 First inning:", lineup.innings[0]);
      
      // Dismiss any existing toasts to prevent stacking
      toast.dismiss();
      
      // Show progress feedback
      regenerateToastId = toast.loading(`Regenerating ${lineup.innings.length}-inning lineup with improved algorithm...`, {
        id: 'regenerate-lineup' // Use a fixed ID to prevent duplicates
      });

      // Get available players from the first inning (to preserve the first inning setup)
      const firstInning = lineup.innings[0];
      const availablePlayers = teamPlayers.filter(player => {
        // Check if player is in the first inning (either field or bench)
        const isInField = Object.values(firstInning.positions).includes(player.name);
        const isOnBench = firstInning.positions.bench?.includes(player.name);
        return isInField || isOnBench;
      });

      console.log("📋 Available players:", availablePlayers.map(p => p.name));

      // IMPORTANT: Preserve the original rotation settings from when the lineup was created
      const originalSettings = lineup.rotationSettings;
      console.log("📋 Original rotation settings from lineup:", originalSettings);

      // If no rotation settings exist (older lineups), use team defaults
      const defaultRotationSettings = {
        limitBenchTime: rotationRules.limitBenchTime ?? true,
        rotateLineupEvery: rotationRules.rotateLineupEvery ?? 1,
        rotatePitcherEvery: rotationRules.rotatePitcherEvery ?? 2
      };

      // Use rotation rules from team context combined with original game-specific settings
      // PRESERVE the user's original rotation frequency settings or use team defaults
      const rotationOptions = {
        limitBenchTime: originalSettings?.limitBenchTime ?? defaultRotationSettings.limitBenchTime,
        allowPitcherRotation: rotationRules.allowPitcherRotation,
        allowCatcherRotation: rotationRules.allowCatcherRotation,
        respectPositionLockouts: rotationRules.respectPositionLockouts,
        equalPlayingTime: rotationRules.equalPlayingTime,
        // PRESERVE the user's original rotation frequency settings or use team defaults
        rotateLineupEvery: originalSettings?.rotateLineupEvery ?? defaultRotationSettings.rotateLineupEvery,
        rotatePitcherEvery: originalSettings?.rotatePitcherEvery ?? defaultRotationSettings.rotatePitcherEvery
      };

      console.log("📋 Using rotation rules (preserving original frequencies):", rotationOptions);

      console.log("🔄 Using complete lineup generation algorithm");

      // Use the full algorithm to regenerate the entire lineup properly
      const newInnings = generateCompleteLineup(
        availablePlayers,
        lineup.innings.length,
        rotationOptions
      );

      // Update the lineup with new optimal rotations
      const updatedLineup = {
        ...lineup,
        innings: newInnings,
        // Ensure all required fields are present for database save
        id: lineup.id,
        name: lineup.name,
        gameDate: lineup.gameDate,
        createdDate: lineup.createdDate,
        attendance: lineup.attendance || {},
        battingOrder: lineup.battingOrder || [],
        // PRESERVE the original rotation settings
        rotationSettings: originalSettings
      };

      console.log("✅ Generated new optimal lineup:", updatedLineup.innings);

      // Clear regeneration toast
      toast.dismiss(regenerateToastId);

      // Update local state immediately for preview (don't save automatically)
      setLineup(updatedLineup);

      console.log("✅ Generated new lineup with improved rotation (preview mode):", updatedLineup.innings);
      toast.success("🔄 Lineup regenerated with improved algorithm!", {
        description: "Preview complete. Use 'Save Lineup' to save changes to database.",
        duration: 5000
      });
    } catch (error) {
      console.error("Error regenerating optimal lineup:", error);
      
      // Clear regeneration toast on error
      toast.dismiss(regenerateToastId);
      
      toast.error("Failed to regenerate optimal lineup");
    }
  };

  // Function to update batting order based on default batting order and current attendance
  const updateBattingOrderForAttendance = (attendingPlayerIds: string[]): string[] => {
    console.log("🏏 Updating batting order for new attendance");
    console.log("🏏 Attending player IDs:", attendingPlayerIds);
    
    const defaultOrder = getDefaultBattingOrder();
    console.log("🏏 Default batting order:", defaultOrder);
    
    if (!defaultOrder || defaultOrder.length === 0) {
      console.log("🏏 No default batting order set, keeping existing order or empty");
      return lineup.battingOrder || [];
    }

    // Filter default order to only include attending players (keeping player IDs)
    const filteredDefaultOrder: string[] = [];
    
    // Go through default order and add only attending players
    defaultOrder.forEach(playerId => {
      if (attendingPlayerIds.includes(playerId)) {
        filteredDefaultOrder.push(playerId);
        console.log(`🏏 Added ${playerId} to filtered batting order`);
      }
    });

    // Add any attending players not in the default order to the end
    const remainingPlayerIds = attendingPlayerIds.filter(playerId => !filteredDefaultOrder.includes(playerId));
    const finalBattingOrder = [...filteredDefaultOrder, ...remainingPlayerIds];
    
    console.log("🏏 Final batting order (player IDs):", finalBattingOrder);
    console.log("🏏 Batting order player names:", finalBattingOrder.map(id => {
      const player = teamPlayers.find(p => p.id === id);
      return player ? player.name : id;
    }));

    return finalBattingOrder;
  };


  // Function to handle quick roster changes
  const handleApplyQuickChanges = async () => {
    if (!lineup || !lineup.innings || lineup.innings.length === 0) {
      toast.error("No lineup data to update");
      return;
    }

    try {
      setIsUpdating(true);
      console.log("⚡ Applying quick roster changes");
      console.log("Current attendance state:", quickEditAttendance);
      console.log("Original attendance state:", originalAttendance);
      
      // Get list of attending players based on checkboxes
      const attendingPlayerIds = Object.entries(quickEditAttendance)
        .filter(([_, isAttending]) => isAttending)
        .map(([playerId]) => playerId);
      
      console.log("Attending player IDs:", attendingPlayerIds);
      console.log("Team players:", teamPlayers.map(p => ({ id: p.id, name: p.name })));
      
      // Check minimum player requirement
      if (attendingPlayerIds.length < 8) {
        toast.error("You need at least 8 players to create a lineup");
        return;
      }

      // Show progress feedback
      const toastId = toast.loading(`Regenerating lineup with ${attendingPlayerIds.length} players...`);
      
      // Force a small delay to ensure UI updates are visible
      await new Promise(resolve => setTimeout(resolve, 500));
      
      console.log("✅ Attending players:", attendingPlayerIds.length);
      
      // Get the attending players' data
      const attendingPlayers = teamPlayers.filter(player => 
        attendingPlayerIds.includes(player.id)
      );
      
      // Create a clean first inning structure
      const firstInning = {
        inning: 1,
        positions: {
          pitcher: '',
          catcher: '',
          firstBase: '',
          secondBase: '',
          shortstop: '',
          thirdBase: '',
          leftField: '',
          centerField: '',
          rightField: '',
          bench: [] as string[]
        }
      };
      
      // Keep players in their current positions if they're still attending
      const originalFirstInning = lineup.innings[0];
      Object.keys(originalFirstInning.positions).forEach(position => {
        if (position !== 'bench') {
          const playerValue = originalFirstInning.positions[position];
          if (playerValue) {
            // Check if this player is still attending
            const player = teamPlayers.find(p => p.name === playerValue || p.id === playerValue);
            if (player && attendingPlayerIds.includes(player.id)) {
              // Keep them in their position
              firstInning.positions[position] = playerValue;
            }
          }
        }
      });
      
      // Track who's already assigned
      const assignedPlayerNames = new Set<string>();
      
      // Mark already assigned players
      Object.entries(firstInning.positions).forEach(([position, playerName]) => {
        if (position !== 'bench' && playerName && typeof playerName === 'string') {
          assignedPlayerNames.add(playerName);
        }
      });
      
      // Get list of available player names (attending but not yet assigned)
      const availablePlayerNames = attendingPlayers
        .map(p => p.name)
        .filter(name => !assignedPlayerNames.has(name));
      
      console.log("Players kept in positions:", Array.from(assignedPlayerNames));
      console.log("Available players to assign:", availablePlayerNames);
      
      // Fill empty field positions
      const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
      
      // First pass: Fill empty positions with eligible players
      for (const position of fieldPositions) {
        if (!firstInning.positions[position] && availablePlayerNames.length > 0) {
          // Find a player who can play this position
          let playerAssigned = false;
          
          for (let i = 0; i < availablePlayerNames.length; i++) {
            const playerName = availablePlayerNames[i];
            const player = attendingPlayers.find(p => p.name === playerName);
            
            if (player && canPlayPositionUtil(player, getPositionDisplayName(position))) {
              firstInning.positions[position] = playerName;
              assignedPlayerNames.add(playerName);
              availablePlayerNames.splice(i, 1);
              console.log(`Assigned ${playerName} to ${position} (position eligible)`);
              playerAssigned = true;
              break;
            }
          }
          
          // If no eligible player found, we'll handle in second pass
          if (!playerAssigned) {
            console.log(`No eligible player found for ${position}, will assign in second pass`);
          }
        }
      }
      
      // Second pass: Fill any remaining empty positions with any available player
      for (const position of fieldPositions) {
        if (!firstInning.positions[position] && availablePlayerNames.length > 0) {
          const playerName = availablePlayerNames.shift()!;
          firstInning.positions[position] = playerName;
          assignedPlayerNames.add(playerName);
          console.log(`Assigned ${playerName} to ${position} (any available)`);
        }
      }
      
      // All remaining players go to bench
      firstInning.positions.bench = availablePlayerNames;
      
      console.log("Final first inning:", {
        field: fieldPositions.map(pos => `${pos}: ${firstInning.positions[pos] || 'EMPTY'}`),
        bench: firstInning.positions.bench,
        totalPlayers: assignedPlayerNames.size + availablePlayerNames.length
      });
      
      console.log("First inning after adjustments:", firstInning);
      console.log("Field positions filled:", Object.entries(firstInning.positions).filter(([k, v]) => k !== 'bench' && v).length);
      console.log("Bench players:", firstInning.positions.bench);
      
      // Now regenerate the entire lineup properly using the full algorithm
      console.log(`🔄 Regenerating entire ${lineup.innings.length}-inning lineup with new attendance`);
      
      // Use original rotation settings from the lineup
      const rotationOptions = {
        limitBenchTime: lineup.rotationSettings?.limitBenchTime ?? rotationRules.limitBenchTime ?? true,
        maxConsecutiveBenchInnings: lineup.rotationSettings?.maxConsecutiveBenchInnings ?? 2,
        allowPitcherRotation: lineup.rotationSettings?.allowPitcherRotation ?? rotationRules.allowPitcherRotation,
        allowCatcherRotation: lineup.rotationSettings?.allowCatcherRotation ?? rotationRules.allowCatcherRotation,
        respectPositionLockouts: lineup.rotationSettings?.respectPositionLockouts ?? rotationRules.respectPositionLockouts,
        equalPlayingTime: lineup.rotationSettings?.equalPlayingTime ?? rotationRules.equalPlayingTime,
        rotateLineupEvery: lineup.rotationSettings?.rotateLineupEvery ?? rotationRules.rotateLineupEvery ?? 1,
        rotatePitcherEvery: lineup.rotationSettings?.rotatePitcherEvery ?? rotationRules.rotatePitcherEvery ?? 2,
        competitiveMode: lineup.rotationSettings?.competitiveMode ?? rotationRules.competitiveMode ?? false
      };
      
      console.log("Quick Edit using rotation settings:", rotationOptions);
      
      // Generate complete lineup with proper rotation algorithm
      const newInnings = generateCompleteLineup(
        attendingPlayers,
        lineup.innings.length,
        rotationOptions
      );
      
      // Update the lineup - ensure attendance uses player IDs
      // Ensure attendance only includes player IDs that exist
      const validAttendance: {[key: string]: boolean} = {};
      Object.entries(quickEditAttendance).forEach(([key, value]) => {
        // Only include if this is a real player ID
        if (teamPlayers.some(p => p.id === key)) {
          validAttendance[key] = value;
        }
      });
      
      // Update batting order based on default batting order and current attendance
      const updatedBattingOrder = updateBattingOrderForAttendance(attendingPlayerIds);
      
      const updatedLineup = {
        ...lineup,
        innings: newInnings,
        attendance: Object.keys(validAttendance).length > 0 ? validAttendance : lineup.attendance || {},
        battingOrder: updatedBattingOrder
      };
      
      console.log("Updated lineup attendance:", updatedLineup.attendance);
      console.log("Updated lineup batting order:", updatedBattingOrder);
      console.log("Lineup being saved:", updatedLineup);
      
      // Clear the loading toast first
      toast.dismiss(toastId);
      
      // Update local state immediately for faster UI response
      setLineup(updatedLineup);
      
      // Set flag for immediate update (no debounce)
      sessionStorage.setItem(`immediate_update_${updatedLineup.id}`, 'true');
      
      // Save to database
      console.log("💾 Saving updated lineup to database with immediate update...");
      await updateLineup(updatedLineup);
      
      // Update the attendance state to match the new lineup
      const newAttendance: {[playerId: string]: boolean} = {};
      teamPlayers.forEach(player => {
        newAttendance[player.id] = attendingPlayerIds.includes(player.id);
      });
      setQuickEditAttendance(newAttendance);
      setOriginalAttendance(newAttendance);
      
      // No need to reload from database - we already have the updated data
      console.log("✅ Quick roster update completed");
      
      // Create a more detailed success message
      const battingOrderUpdated = updatedBattingOrder.length > 0;
      const successMessage = battingOrderUpdated 
        ? `✅ Lineup updated! Now using ${attendingPlayerIds.length} players. Batting order automatically updated based on your default order.`
        : `✅ Lineup updated! Now using ${attendingPlayerIds.length} players.`;
      
      toast.success(successMessage);
      setShowQuickEditDialog(false);
      
    } catch (error) {
      console.error("Error applying quick changes:", error);
      
      // Check if this is an authentication error
      if (error.message?.includes('Session expired') || error.message?.includes('Auth session missing') || error.message?.includes('User not authenticated')) {
        toast.error("Your session has expired. Please sign in again.");
        // Redirect to sign-in page after a short delay
        setTimeout(() => {
          navigate('/sign-in');
        }, 2000);
      } else {
        toast.error(`Failed to update lineup: ${error.message || 'Unknown error'}`);
      }
    } finally {
      setIsUpdating(false);
    }
  };

  // Function to regenerate rotations while keeping the first inning
  // UNUSED - keeping for reference
  /*
  const handleRegenerateRotations = async () => {
    if (!lineup || !lineup.innings || lineup.innings.length === 0) {
      toast.error("No lineup data to regenerate");
      return;
    }

    try {
      console.log("🔄 STARTING IMPROVE ROTATIONS ONLY");
      console.log("🔄 Regenerating rotations for lineup:", lineup.name);
      console.log("🔄 Current lineup innings:", lineup.innings.length);

      // Get the first inning (keep it unchanged)
      const firstInning = lineup.innings[0];
      console.log("📍 Keeping first inning:", firstInning);

      // Get available players from the first inning
      const availablePlayers = teamPlayers.filter(player => {
        // Check if player is in the first inning (either field or bench)
        const isInField = Object.values(firstInning.positions).includes(player.name);
        const isOnBench = firstInning.positions.bench?.includes(player.name);
        return isInField || isOnBench;
      });

      console.log("👥 Available players for rotation:", availablePlayers.map(p => p.name));

      // Use the original rotation settings from when the lineup was created
      const originalSettings = lineup.rotationSettings;
      console.log("📋 Original rotation settings from lineup:", originalSettings);

      // If no rotation settings exist (older lineups), use team defaults
      const defaultRotationSettings = {
        limitBenchTime: rotationRules.limitBenchTime ?? true,
        rotateLineupEvery: rotationRules.rotateLineupEvery ?? 1,
        rotatePitcherEvery: rotationRules.rotatePitcherEvery ?? 2
      };

      // Use rotation rules from team context combined with original game-specific settings
      const rotationOptions = {
        limitBenchTime: originalSettings?.limitBenchTime ?? defaultRotationSettings.limitBenchTime,
        allowPitcherRotation: rotationRules.allowPitcherRotation,
        allowCatcherRotation: rotationRules.allowCatcherRotation,
        respectPositionLockouts: rotationRules.respectPositionLockouts,
        equalPlayingTime: rotationRules.equalPlayingTime,
        rotateLineupEvery: originalSettings?.rotateLineupEvery ?? defaultRotationSettings.rotateLineupEvery,
        rotatePitcherEvery: originalSettings?.rotatePitcherEvery ?? defaultRotationSettings.rotatePitcherEvery
      };

      console.log("📋 Using rotation rules:", rotationOptions);

      console.log("🔄 Starting aggressive rotation with position restrictions");

      // Use the SAME HYBRID APPROACH as the optimizer
      const newInnings = [firstInning]; // Keep first inning

      // Helper function to check if a player can play a position
      const canPlayPosition = (playerName: string, position: string): boolean => {
        const player = availablePlayers.find(p => p.name === playerName);
        if (!player || !rotationOptions.respectPositionLockouts) return true;

        // Use the utility function for consistent position checking
        const positionDisplayName = getPositionDisplayName(position);
        const result = canPlayPositionUtil(player, positionDisplayName);

        console.log(`🔍 ViewLineup (regenerate): ${playerName} ${result ? 'CAN' : 'CANNOT'} play ${positionDisplayName}`);
        return result;
      };

      // For each subsequent inning, create an aggressive rotation with restrictions
      for (let i = 1; i < lineup.innings.length; i++) {
        console.log(`🚀 Generating inning ${i + 1} with aggressive rotation + restrictions`);

        const prevInning = newInnings[i - 1];
        const newInning = {
          inning: i + 1,
          positions: {
            pitcher: '',
            catcher: '',
            firstBase: '',
            secondBase: '',
            shortstop: '',
            thirdBase: '',
            leftField: '',
            centerField: '',
            rightField: '',
            bench: [] as string[]
          }
        };

        // Get all players from previous inning
        const allPlayers = [
          ...Object.values(prevInning.positions).filter(p => typeof p === 'string' && p),
          ...(prevInning.positions.bench || [])
        ].filter(Boolean);

        console.log(`📋 All players for inning ${i + 1}:`, allPlayers);

        // Prioritize bench players for field positions (aggressive rotation)
        const benchPlayers = prevInning.positions.bench || [];
        const fieldPlayers = allPlayers.filter(p => !benchPlayers.includes(p));

        // Create rotation priority: bench players first, then field players
        let availableForAssignment = [...benchPlayers, ...fieldPlayers];

        // Shuffle for randomness while maintaining bench priority
        const shuffledBench = [...benchPlayers].sort(() => Math.random() - 0.5);
        const shuffledField = [...fieldPlayers].sort(() => Math.random() - 0.5);
        availableForAssignment = [...shuffledBench, ...shuffledField];

        console.log(`🔄 Rotation priority order:`, availableForAssignment);

        // Assign positions with restriction checking
        const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
        const assignedPlayers = new Set<string>();

        for (const position of fieldPositions) {
          let assigned = false;

          // Try to assign from available players who can play this position
          for (const playerName of availableForAssignment) {
            if (!assignedPlayers.has(playerName) && canPlayPosition(playerName, position)) {
              (newInning.positions as any)[position] = playerName;
              assignedPlayers.add(playerName);
              assigned = true;
              console.log(`✅ Assigned ${playerName} to ${position} (restriction check passed)`);
              break;
            }
          }

          // If no one can play this position (shouldn't happen), assign anyone available
          if (!assigned) {
            for (const playerName of availableForAssignment) {
              if (!assignedPlayers.has(playerName)) {
                (newInning.positions as any)[position] = playerName;
                assignedPlayers.add(playerName);
                console.log(`⚠️ Force assigned ${playerName} to ${position} (no eligible players)`);
                break;
              }
            }
          }
        }

        // Assign remaining players to bench
        const remainingPlayers = allPlayers.filter(p => !assignedPlayers.has(p));
        newInning.positions.bench = remainingPlayers;

        console.log(`🔄 New field assignments:`, fieldPositions.map(pos => (newInning.positions as any)[pos]));
        console.log(`🪑 New bench (${remainingPlayers.length}):`, remainingPlayers);

        console.log(`✅ Generated inning ${i + 1}:`, newInning);
        newInnings.push(newInning);
      }

      // Update the lineup with new rotations
      const updatedLineup = {
        ...lineup,
        innings: newInnings,
        // Ensure all required fields are present for database save
        id: lineup.id,
        name: lineup.name,
        gameDate: lineup.gameDate,
        createdDate: lineup.createdDate,
        attendance: lineup.attendance || {},
        battingOrder: lineup.battingOrder || [],
        rotationSettings: lineup.rotationSettings
      };

      console.log("✅ Generated new rotations:", updatedLineup.innings);

      // Update local state immediately for instant feedback
      setLineup(updatedLineup);

      console.log("💾 Attempting to save updated rotations to database...");
      console.log("📋 Lineup data being saved:", {
        id: updatedLineup.id,
        name: updatedLineup.name,
        inningsCount: updatedLineup.innings?.length,
        hasAttendance: !!updatedLineup.attendance
      });

      // Update local state immediately for preview (don't save automatically)
      setLineup(updatedLineup);

      console.log("✅ Generated improved rotations (preview mode):", updatedLineup.innings);
      toast.success("🔄 Rotations improved! Use 'Save Lineup' button to save changes.");
    } catch (error) {
      console.error("Error regenerating rotations:", error);
      toast.error("Failed to regenerate rotations");
    }
  };
  */

  // Lineup Health Panel Component
  const LineupHealthPanel = () => {
    if (!lineupHealth) return null;

    return (
      <div className="mt-8 bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm">
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-4">
          <h3 className="text-lg font-bold flex items-center gap-2">
            📊 Playing Time Balance
          </h3>
        </div>
        <div className="p-6">
          {lineupHealth.isBalanced ? (
            <div className="flex items-center gap-2 text-green-700 font-medium mb-4 p-3 bg-green-50 rounded-lg border border-green-200">
              ✅ All players within 1 inning of each other—rotation balanced.
            </div>
          ) : (
            <div className="space-y-2 mb-4">
              {lineupHealth.players
                .filter(player => player.status !== 'balanced')
                .map(player => (
                  <div key={player.name} className="p-3 bg-orange-50 rounded-lg border border-orange-200">
                    <div className="text-orange-700 font-medium">
                      🔄 {player.name} has sat {player.benchCount} times but only played {player.inningsPlayed} innings
                      {player.suggestedInning && (
                        <span className="text-orange-600">—consider swapping in inning {player.suggestedInning}</span>
                      )}
                    </div>
                  </div>
                ))}
            </div>
          )}

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
            {lineupHealth.players
              .sort((a, b) => a.name.localeCompare(b.name))
              .map(player => (
              <div
                key={player.name}
                className={`p-4 rounded-lg border transition-all ${
                  player.status === 'critical'
                    ? 'bg-red-50 border-red-200 hover:bg-red-100'
                    : player.status === 'warning'
                    ? 'bg-yellow-50 border-yellow-200 hover:bg-yellow-100'
                    : 'bg-green-50 border-green-200 hover:bg-green-100'
                }`}
              >
                <div className="font-semibold text-gray-900 mb-1">{player.name}</div>
                <div className="text-sm text-gray-600">
                  {player.inningsPlayed} played • {player.benchCount} sat
                </div>
                {player.status !== 'balanced' && (
                  <div className={`text-xs mt-1 font-medium ${
                    player.status === 'critical' ? 'text-red-600' : 'text-yellow-600'
                  }`}>
                    {player.imbalance > 0 ? `+${player.imbalance}` : player.imbalance} difference
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const handleDownloadCSV = () => {
    if (!lineup) return;

    let csvContent = "";

    // Get positions including bench positions
    const positions = getPositions;

    // Create CSV header
    csvContent += "Position,";

    // Add innings to header
    lineup.innings.forEach((inning: InningLineup) => {
      csvContent += `Inning ${inning.inning},`;
    });

    // Add batting order column header if it exists
    if (lineup.battingOrder && lineup.battingOrder.length > 0) {
      csvContent += "Batting Order,";
    }

    csvContent += "\n";

    // Add position data
    positions.forEach((position: {id: string, label: string}, index: number) => {
      csvContent += `${position.label},`;

      lineup.innings.forEach((inning: InningLineup) => {
        if (position.id.startsWith('bench')) {
          const benchIndex = parseInt(position.id.replace('bench', '')) - 1;
          const benchPlayer = inning.positions.bench?.[benchIndex] || '';
          csvContent += `${playerIdToName[benchPlayer] || benchPlayer},`;
        } else {
          const playerName = inning.positions[position.id] || '';
          csvContent += `${playerIdToName[playerName] || playerName},`;
        }
      });

      // Add batting order information for each position
      if (lineup.battingOrder && lineup.battingOrder.length > 0) {
        // Show batting order for all players in the order, not just the first 9
        if (index < lineup.battingOrder.length) {
          const battingOrderPlayer = playerIdToName[lineup.battingOrder[index]] || lineup.battingOrder[index];
          csvContent += `${index + 1}: ${battingOrderPlayer},`;
        } else {
          csvContent += ","; // Empty cell for positions without batting order
        }
      }

      csvContent += "\n";
    });

    // Create a download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `lineup-${lineup.name}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleDownloadPDF = async () => {
    if (!lineup) return;

    try {
      console.log("Starting PDF generation...");
      
      // Import jsPDF using require syntax which works better with Vite
      console.log("Loading jsPDF...");
      const jsPDF = (await import('jspdf')).default;
      const autoTable = (await import('jspdf-autotable')).default;
      console.log("PDF libraries loaded successfully");
      
      // Create a new jsPDF instance
      console.log("Creating jsPDF instance...");
      const doc = new jsPDF();
      console.log("jsPDF instance created successfully");

      // Add title and date
      doc.setFontSize(16);
      doc.text(`Lineup: ${lineup.name}`, 14, 20);
      doc.setFontSize(12);
      doc.text(`Game Date: ${lineup.gameDate}`, 14, 30);

      const currentY = 40;

      // Get positions including bench positions
      console.log("Getting positions data...");
      const positions = getPositions;
      console.log("Positions data:", positions);

      // Create table headers
      console.log("Creating table headers...");
      const headers = ["Position"];
      lineup.innings.forEach((inning: InningLineup) => {
        headers.push(`Inning ${inning.inning}`);
      });

      // Add batting order column header if it exists
      if (lineup.battingOrder && lineup.battingOrder.length > 0) {
        headers.push("Batting Order");
      }

      console.log("Headers:", headers);

      // Create table body
      console.log("Creating table body...");
      const body = positions.map((position: {id: string, label: string}, index: number) => {
        const rowData = [position.label];

        lineup.innings.forEach((inning: InningLineup) => {
          if (position.id.startsWith('bench')) {
            const benchIndex = parseInt(position.id.replace('bench', '')) - 1;
            const benchPlayer = inning.positions.bench?.[benchIndex] || '';
            rowData.push(playerIdToName[benchPlayer] || benchPlayer || '');
          } else {
            const playerName = inning.positions[position.id] || '';
            rowData.push(playerIdToName[playerName] || playerName || '');
          }
        });

        // Add batting order information for each position
        if (lineup.battingOrder && lineup.battingOrder.length > 0) {
          if (index < lineup.battingOrder.length) {
            // Show batting order for all players in the order, not just the first 9
            const battingOrderPlayer = playerIdToName[lineup.battingOrder[index]] || lineup.battingOrder[index];
            rowData.push(`${index + 1}: ${battingOrderPlayer}`);
          } else {
            rowData.push(''); // Empty cell for positions without batting order
          }
        }

        return rowData;
      });
      console.log("Table body created, rows:", body.length);

      // Generate the table using the dynamically imported autoTable
      console.log("Generating table with autoTable...");
      autoTable(doc, {
        head: [headers],
        body: body,
        startY: currentY,
        theme: 'grid',
        styles: { fontSize: 10, cellPadding: 3 },
        headStyles: { fillColor: [66, 135, 245], textColor: 255 },
        alternateRowStyles: { fillColor: [240, 240, 240] }
      });
      console.log("Table generated successfully");

      // Save the PDF
      console.log("Saving PDF...");
      doc.save(`lineup-${lineup.name}.pdf`);
      console.log("PDF saved successfully");

      toast.success("PDF downloaded successfully");
    } catch (error) {
      console.error("Error generating PDF:", error);
      console.error("Error details:", {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
      toast.error(`Failed to generate PDF: ${error.message || 'Unknown error'}`);
    }
  };

  // Function to handle editing an inning (memoized to prevent re-renders)
  const handleEditInning = useCallback((inningNumber: number) => {
    if (!lineup) return;

    console.log(`Navigating to edit inning ${inningNumber} for lineup ${lineup.id}`);

    // Navigate with state to ensure the lineup data is available
    navigate(`/edit-inning?lineupId=${lineup.id}&inningNumber=${inningNumber}`, {
      state: { lineup }
    });
  }, [lineup, navigate]);

  // Get all positions including bench positions (memoized to prevent re-calculations)
  const getPositions = useMemo(() => {
    // Start with base positions
    const allPositions = [...basePositions];

    // Add bench positions based on the maximum number of bench players
    if (lineup && lineup.innings) {
      const maxBenchPlayers = lineup.innings.reduce((max: number, inning: InningLineup) => {
        return Math.max(max, inning.positions.bench?.length || 0);
      }, 0);

      for (let i = 0; i < maxBenchPlayers; i++) {
        allPositions.push({ id: `bench${i+1}`, label: `Bench ${i+1}` });
      }
    }

    return allPositions;
  }, [lineup]);

  const renderPositionTable = useMemo(() => {
    if (!lineup || !lineup.innings) {
      return <div>No lineup data available</div>;
    }

    const positions = getPositions;

    // Mobile view - show innings in a more compact format
    if (isMobile) {
      return (
        <div className="space-y-3">
          {lineup.innings.map((inning: InningLineup) => (
            <div key={inning.inning} className="border rounded-lg p-3 bg-white shadow-sm">
              <div className="flex justify-between items-center mb-2">
                <h3 className="font-bold text-base text-baseball-navy">Inning {inning.inning}</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleEditInning(inning.inning)}
                  className="h-7 w-7 p-0 text-baseball-green hover:bg-baseball-lightgreen/30"
                >
                  ✏️
                </Button>
              </div>

              {/* Field Positions in a compact grid */}
              <div className="grid grid-cols-2 gap-x-4 gap-y-1 text-sm mb-3">
                {positions.filter(p => !p.id.startsWith('bench')).map((position: {id: string, label: string}) => {
                  const playerValue = inning.positions[position.id as keyof typeof inning.positions];
                  const playerName = typeof playerValue === 'string' ? (playerIdToName[playerValue] || playerValue) : '';

                  return (
                    <div key={position.id} className="flex justify-between">
                      <span className="font-medium text-gray-600 text-xs">{position.label}</span>
                      <span className="text-right text-sm font-medium">{playerName}</span>
                    </div>
                  );
                })}
              </div>

              {/* Bench Players */}
              {inning.positions.bench && inning.positions.bench.length > 0 && (
                <div className="border-t pt-2 mt-2">
                  <div className="text-xs font-medium text-gray-500 mb-1">Bench:</div>
                  <div className="flex flex-wrap gap-2">
                    {inning.positions.bench.map((benchPlayer: string, index: number) => (
                      <span key={index} className="text-xs bg-gray-100 px-2 py-1 rounded">
                        {playerIdToName[benchPlayer] || benchPlayer}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      );
    }

    return (
      <div className="overflow-x-auto">
        <table className="w-full border-collapse min-w-[800px]">
          <thead>
            <tr>
              <th className="text-left p-2 min-w-[120px]">INNING #:</th>
              {lineup.innings.map((inning: InningLineup) => (
                <th key={inning.inning} className="p-2 min-w-[100px]">
                  {inning.inning}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="ml-2 h-6 w-6 p-0"
                    onClick={() => handleEditInning(inning.inning)}
                  >
                    ✏️
                  </Button>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {(() => {
              // Group positions by field areas for visual organization
              const positionGroups = [
                {
                  name: "Battery",
                  positions: positions.filter(p => ['pitcher', 'catcher'].includes(p.id))
                },
                {
                  name: "Infield", 
                  positions: positions.filter(p => ['firstBase', 'secondBase', 'shortstop', 'thirdBase'].includes(p.id))
                },
                {
                  name: "Outfield",
                  positions: positions.filter(p => ['leftField', 'centerField', 'rightField'].includes(p.id))
                },
                {
                  name: "Bench",
                  positions: positions.filter(p => p.id.startsWith('bench'))
                }
              ].filter(group => group.positions.length > 0); // Only show groups that have positions

              let globalRowIndex = 0;
              return positionGroups.map((group, groupIndex) => (
                <React.Fragment key={group.name}>
                  {/* Group separator row */}
                  {groupIndex > 0 && (
                    <tr key={`separator-${group.name}`}>
                      <td colSpan={lineup.innings.length + 1} className="p-0">
                        <div className="border-t-2 border-baseball-navy/20"></div>
                      </td>
                    </tr>
                  )}
                  
                  {/* Group header row */}
                  <tr key={`header-${group.name}`} className="bg-baseball-navy/10">
                    <td colSpan={lineup.innings.length + 1} className="p-2 text-center font-semibold text-baseball-navy text-sm">
                      {group.name} Positions
                    </td>
                  </tr>

                  {/* Position rows for this group */}
                  {group.positions.map((position: {id: string, label: string}) => {
                    const rowClass = globalRowIndex % 2 === 0 ? 'bg-baseball-lightgreen' : 'bg-baseball-lightblue';
                    globalRowIndex++;

                    if (position.id.startsWith('bench')) {
                      const benchIndex = parseInt(position.id.replace('bench', '')) - 1;

                      return (
                        <tr key={position.id} className={rowClass}>
                          <td className="p-2 font-bold">{position.label}</td>
                          {lineup.innings.map((inning: InningLineup) => (
                            <td key={`${inning.inning}-${position.id}`} className="p-2 text-center">
                              {inning.positions.bench && benchIndex < inning.positions.bench.length
                                ? playerIdToName[inning.positions.bench[benchIndex]] || inning.positions.bench[benchIndex] || ''
                                : ''}
                            </td>
                          ))}
                        </tr>
                      );
                    }

                    return (
                      <tr key={position.id} className={rowClass}>
                        <td className="p-2 font-bold">{position.label}</td>
                        {lineup.innings.map((inning: InningLineup) => (
                          <td key={`${inning.inning}-${position.id}`} className="p-2 text-center">
                            {(() => {
                              // Get the player ID or name from the position
                              const playerValue = inning.positions[position.id as keyof typeof inning.positions];
                              // If it's a player ID, try to get the name from the map
                              return typeof playerValue === 'string' ? (playerIdToName[playerValue] || playerValue) : '';
                            })()}
                          </td>
                        ))}
                      </tr>
                    );
                  })}
                </React.Fragment>
              ));
            })()}
          </tbody>
        </table>
      </div>
    );
  }, [lineup, getPositions, playerIdToName, handleEditInning]);

  const renderPlayerTable = useMemo(() => {
    if (!lineup || !lineup.innings) {
      return <div>No lineup data available</div>;
    }

    // Create a map to normalize player identifiers to consistent names
    const playerNameMap = new Map<string, string>();
    const uniquePlayerNames = new Set<string>();

    lineup.innings.forEach((inning: InningLineup) => {
      Object.entries(inning.positions).forEach(([posKey, posValue]) => {
        if (posKey !== 'bench' && typeof posValue === 'string' && posValue) {
          const displayName = getPlayerDisplayName(posValue);
          playerNameMap.set(posValue, displayName);
          uniquePlayerNames.add(displayName);
        }
      });

      if (inning.positions.bench && Array.isArray(inning.positions.bench)) {
        inning.positions.bench.forEach((player: string) => {
          if (player) {
            const displayName = getPlayerDisplayName(player);
            playerNameMap.set(player, displayName);
            uniquePlayerNames.add(displayName);
          }
        });
      }
    });

    // Get positions including bench positions
    const positions = getPositions;

    // Mobile view - show players in a more compact format
    if (isMobile) {
      return (
        <div className="space-y-3">
          {Array.from(uniquePlayerNames).sort().map((playerName) => (
            <div key={playerName} className="border rounded-lg p-3 bg-white shadow-sm">
              <h3 className="font-bold text-base text-baseball-navy mb-2">{playerName}</h3>
              <div className="grid grid-cols-4 gap-1 text-xs">
                {lineup.innings.map((inning: InningLineup) => {
                  let playerPosition = '';

                  for (const [posKey, posValue] of Object.entries(inning.positions)) {
                    if (posKey === 'bench') {
                      if (Array.isArray(posValue) && posValue.some(p => playerNameMap.get(p) === playerName)) {
                        playerPosition = 'Bench';
                        break;
                      }
                    } else if (typeof posValue === 'string' && playerNameMap.get(posValue) === playerName) {
                      const position = positions.find((p: {id: string, label: string}) => p.id === posKey);
                      playerPosition = position ? position.label.replace(':', '') : posKey;
                      break;
                    }
                  }

                  return (
                    <div key={inning.inning} className="text-center py-1 bg-gray-50 rounded">
                      <div className="font-medium text-gray-600 text-xs">I{inning.inning}</div>
                      <div className="text-xs font-medium">{playerPosition}</div>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      );
    }

    return (
      <div className="overflow-x-auto">
        <table className="w-full border-collapse min-w-[800px]">
          <thead className="bg-baseball-navy text-white">
            <tr>
              <th className="text-left p-3 min-w-[120px] font-bold">PLAYER:</th>
              {lineup.innings.map((inning: InningLineup) => (
                <th key={inning.inning} className="p-3 min-w-[100px] text-center font-bold">
                  <div className="flex items-center justify-center gap-2">
                    <span>{inning.inning}</span>
                    <span className="text-xs">⚾</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-1 h-5 w-5 p-0 hover:bg-white/20 text-white"
                      onClick={() => handleEditInning(inning.inning)}
                    >
                      ✏️
                    </Button>
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {Array.from(uniquePlayerNames).sort().map((playerName, playerIndex) => (
              <tr key={playerName} className={playerIndex % 2 === 0 ? 'bg-baseball-lightgreen' : 'bg-baseball-lightblue'}>
                <td className="p-3 font-bold text-baseball-navy border-r border-gray-200">{playerName}</td>
                {lineup.innings.map((inning: InningLineup) => {
                  let playerPosition = '';

                  for (const [posKey, posValue] of Object.entries(inning.positions)) {
                    if (posKey === 'bench') {
                      if (Array.isArray(posValue) && posValue.some(p => playerNameMap.get(p) === playerName)) {
                        playerPosition = 'Bench';
                        break;
                      }
                    } else if (typeof posValue === 'string' && playerNameMap.get(posValue) === playerName) {
                      const position = positions.find((p: {id: string, label: string}) => p.id === posKey);
                      playerPosition = position ? position.label.replace(':', '') : posKey;
                      break;
                    }
                  }

                  return (
                    <td key={`${inning.inning}-${playerName}`} className="p-3 text-center border-r border-gray-200">
                      <span className={`font-medium ${playerPosition === 'Bench' ? 'text-gray-500 italic' : 'text-baseball-navy'}`}>
                        {playerPosition}
                      </span>
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  }, [lineup, getPositions, playerIdToName, handleEditInning, getPlayerDisplayName]);

  if (!lineup) {
    return (
      <div className="min-h-screen flex flex-col bg-baseball-tan/20">
        <Header title="Loading Lineup..." showBack backLink="/dashboard" />
        <main className="flex-1 container mx-auto px-4 py-8">
          <div className="bg-white rounded-lg shadow-md p-6 max-w-4xl mx-auto">
            <p>Loading lineup data...</p>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-baseball-tan/20">
      <Header 
        title={`Lineup: ${lineup.name}`} 
        showBack 
        backLink={fromSeries ? "/view-batch-series" : "/dashboard"}
        backState={fromSeries ? { seriesData, fromLineup: true } : undefined}
      />

      <main className="flex-1 container mx-auto px-4 py-6">
        <div className="bg-white rounded-xl shadow-lg overflow-hidden max-w-7xl mx-auto">
          {/* Card Header with Title and Date */}
          <div className="bg-gradient-to-r from-baseball-navy to-blue-800 text-white p-6">
            <div className="flex justify-between items-start">
              <div>
                <h1 className="text-3xl font-bold">{lineup.name}</h1>
                <p className="text-blue-200 mt-1">{lineup.gameDate}</p>
              </div>
              {lineup.innings && (
                <div className="text-right">
                  <span className="text-sm text-blue-200">Innings</span>
                  <div className="text-2xl font-bold">{lineup.innings.length}</div>
                </div>
              )}
            </div>
          </div>

          {/* Stats Cards Row */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-6 bg-gray-50 border-b">
            {/* Balance Score Card */}
            <div className="bg-white rounded-lg p-4 text-center shadow-sm">
              <div className="text-sm text-gray-600 mb-1">Balance Score</div>
              <div className={`text-3xl font-bold ${
                calculateSingleGameBalance.score >= 80 ? 'text-green-600' : 
                calculateSingleGameBalance.score >= 60 ? 'text-yellow-600' : 
                'text-red-600'
              }`}>
                {calculateSingleGameBalance.score}
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div className={`h-2 rounded-full transition-all duration-500 ${
                  calculateSingleGameBalance.score >= 80 ? 'bg-green-600' : 
                  calculateSingleGameBalance.score >= 60 ? 'bg-yellow-600' : 
                  'bg-red-600'
                }`} style={{ width: `${calculateSingleGameBalance.score}%` }}></div>
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {calculateSingleGameBalance.score >= 80 ? 'Excellent' : 
                 calculateSingleGameBalance.score >= 60 ? 'Good' : 
                 'Needs Work'}
              </div>
            </div>

            {/* Players Card */}
            <div className="bg-white rounded-lg p-4 text-center shadow-sm">
              <div className="text-sm text-gray-600 mb-1">Active Players</div>
              <div className="text-3xl font-bold text-blue-600">
                {lineup.attendance ? Object.values(lineup.attendance).filter(Boolean).length : 0}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {lineup.innings?.[0]?.positions ? 
                  `9 Field + ${(lineup.innings[0].positions.bench?.length || 0)} Bench` : 
                  'Loading...'}
              </div>
            </div>

            {/* Innings Card */}
            <div className="bg-white rounded-lg p-4 text-center shadow-sm">
              <div className="text-sm text-gray-600 mb-1">Total Innings</div>
              <div className="text-3xl font-bold text-purple-600">
                {lineup.innings?.length || 0}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {lineup.innings?.length === 6 ? 'Standard Game' : 'Custom Game'}
              </div>
            </div>

            {/* Rotation Card */}
            <div className="bg-white rounded-lg p-4 text-center shadow-sm">
              <div className="text-sm text-gray-600 mb-1">Rotation</div>
              <div className="text-3xl font-bold text-orange-600">
                {rotationRules?.rotateLineupEvery || 2}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                Every {rotationRules?.rotateLineupEvery || 2} Innings
              </div>
            </div>
          </div>

          {/* Primary Actions Bar */}
          <div className="p-4 bg-white border-b">
            <div className="flex flex-wrap gap-3 items-center justify-between">
              <div className="flex flex-wrap gap-2">
                {/* Primary Actions */}
                <Button
                  onClick={handleSaveLineup}
                  className="bg-baseball-navy hover:bg-baseball-navy/90 text-white px-6 shadow-sm"
                >
                  💾 Save Lineup
                </Button>
                <Button
                  onClick={() => setShowQuickEditDialog(true)}
                  variant="outline"
                  className="border-2 border-baseball-green text-baseball-green hover:bg-baseball-green/5 px-6 font-medium"
                >
                  🔄 Quick Roster Adjust
                </Button>
                
                {/* Export Options */}
                <div className="flex items-center gap-1 bg-gray-100 rounded-lg p-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleDownloadPDF}
                    className="text-gray-700 hover:bg-white rounded-md px-4"
                  >
                    📄 PDF
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleDownloadCSV}
                    className="text-gray-700 hover:bg-white rounded-md px-4"
                  >
                    📊 CSV
                  </Button>
                </div>
              </div>

              {/* More Options Menu */}
              <div className="relative">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowDeleteDialog(true)}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  Delete
                </Button>
              </div>
            </div>

            {/* Quick Actions Row */}
            <div className="mt-3 flex flex-wrap gap-2 text-sm">
              <Button
                onClick={handleOptimizeLineup}
                variant="ghost"
                size="sm"
                className="text-blue-700 hover:bg-blue-50"
              >
                ⚖️ Fix Playing Time
                <span className="text-xs text-gray-500 ml-1">({calculateSingleGameBalance.score})</span>
              </Button>
              <Button
                onClick={handleRegenerateOptimalLineup}
                variant="ghost"
                size="sm"
                className="text-purple-700 hover:bg-purple-50"
              >
                🔁 Rebuild Entire Lineup
              </Button>
              {(!lineup.battingOrder || lineup.battingOrder.length === 0) && (
                <Button
                  onClick={() => navigate(`/batting-order?lineupId=${lineup.id}`)}
                  variant="ghost"
                  size="sm"
                  className="text-gray-700 hover:bg-gray-100"
                >
                  ⚾ Add Batting Order
                </Button>
              )}
            </div>
          </div>

          {/* View Toggle - Now inside the card */}
          <div className="px-6 pt-4 pb-4">
            <div className="inline-flex bg-gray-100 rounded-lg p-1">
              <button
                className={`px-6 py-2 rounded-md font-medium transition-all ${
                  viewMode === 'position'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                onClick={() => setViewMode('position')}
              >
                View by Position
              </button>
              <button
                className={`px-6 py-2 rounded-md font-medium transition-all ${
                  viewMode === 'player'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                onClick={() => setViewMode('player')}
              >
                View by Player
              </button>
            </div>
          </div>

          {/* Missing Players Warning */}
          {missingPlayers.length > 0 && (
            <div className="mb-8 bg-orange-50 border border-orange-200 rounded-xl overflow-hidden shadow-sm">
              <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-6 py-4">
                <h3 className="text-lg font-bold flex items-center gap-2">
                  ⚠️ Lineup Data Issue Detected
                </h3>
              </div>
              <div className="p-6">
                <div className="mb-4">
                  <p className="text-orange-800 font-medium mb-2">
                    This lineup contains {missingPlayers.length} player references that don't match your current roster.
                  </p>
                  <p className="text-orange-700 text-sm">
                    This can happen when lineup data gets corrupted. You can fix this by converting the orphaned player IDs to generic player names.
                  </p>
                </div>
                <Button
                  onClick={handleFixLineupData}
                  className="bg-orange-600 hover:bg-orange-700 text-white"
                >
                  🔧 Fix Lineup Data
                </Button>
              </div>
            </div>
          )}

          {/* Batting Order Display */}
          {lineup.battingOrder && lineup.battingOrder.length > 0 && (
            <div className="mb-8 bg-white border border-gray-200 rounded-xl overflow-hidden shadow-lg">
              <div className="bg-gradient-to-r from-baseball-green to-baseball-green/90 text-white px-6 py-4 flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center">
                    <span className="text-xs font-bold">⚾</span>
                  </div>
                  <h3 className="text-lg font-bold">Batting Order</h3>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate(`/batting-order?lineupId=${lineup.id}`)}
                  className="bg-white/10 text-white border-white/30 hover:bg-white hover:text-baseball-green transition-all"
                >
                  Edit Order
                </Button>
              </div>
              <div className="p-6 bg-gray-50/50">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                  {lineup.battingOrder.map((playerId: string, index: number) => (
                    <div key={index} className="flex items-center bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                      <div className="w-10 h-10 bg-gradient-to-br from-baseball-green to-baseball-green/80 text-white rounded-full flex items-center justify-center font-bold text-sm mr-4 shadow-sm">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <span className="font-semibold text-baseball-navy text-sm">{getPlayerDisplayName(playerId)}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Main Content Area */}
          <div className="p-6">
            {viewMode === "position" ? renderPositionTable : renderPlayerTable}

            {/* Lineup Health Panel */}
            <LineupHealthPanel />
          </div>
        </div>
      </main>
      <Footer />
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Lineup</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this lineup? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Quick Edit Dialog */}
      <Dialog open={showQuickEditDialog} onOpenChange={setShowQuickEditDialog}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Quick Roster Adjustments</DialogTitle>
            <DialogDescription>
              Make last-minute changes to your lineup. Uncheck players who can't make it, or check players who just arrived.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6 py-4">
            {/* Attendance Section */}
            <div>
              <h3 className="font-semibold text-lg mb-4 text-baseball-navy">Player Attendance</h3>
              <p className="text-sm text-gray-600 mb-4">Check all players who are available for this game:</p>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {teamPlayers
                  .sort((a, b) => a.name.localeCompare(b.name))
                  .map((player) => {
                    const isAttending = quickEditAttendance[player.id] || false;
                    const wasOriginallyInLineup = originalAttendance[player.id] === true;
                    
                    return (
                      <div 
                        key={player.id} 
                        className={`flex items-center space-x-3 p-3 rounded-lg transition-colors ${
                          wasOriginallyInLineup && !isAttending 
                            ? 'bg-red-50 hover:bg-red-100 border border-red-200' 
                            : !wasOriginallyInLineup && isAttending
                            ? 'bg-green-50 hover:bg-green-100 border border-green-200'
                            : 'bg-gray-50 hover:bg-gray-100 border border-gray-200'
                        }`}
                      >
                        <Checkbox
                          id={`player-${player.id}`}
                          checked={isAttending}
                          onCheckedChange={(checked) => {
                            console.log(`Checkbox changed for ${player.name}: ${checked}`);
                            setQuickEditAttendance(prev => {
                              const newState = {
                                ...prev,
                                [player.id]: !!checked
                              };
                              console.log("New attendance state:", newState);
                              return newState;
                            });
                          }}
                          className="data-[state=checked]:bg-baseball-green data-[state=checked]:border-baseball-green"
                        />
                        <Label 
                          htmlFor={`player-${player.id}`}
                          className="flex-1 cursor-pointer font-medium"
                        >
                          {player.name}
                          {wasOriginallyInLineup && !isAttending && 
                            <span className="text-xs text-red-600 ml-2">(dropping)</span>
                          }
                          {!wasOriginallyInLineup && isAttending && 
                            <span className="text-xs text-green-600 ml-2">(adding)</span>
                          }
                        </Label>
                      </div>
                    );
                  })}
              </div>
              
              {/* Player Count Summary */}
              <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <p className="text-sm font-medium text-blue-900">
                  {Object.values(quickEditAttendance).filter(Boolean).length} players selected
                  {Object.values(quickEditAttendance).filter(Boolean).length < 8 && 
                    <span className="text-red-600 ml-2">(Need at least 8 players)</span>
                  }
                </p>
              </div>
            </div>
            
            {/* Information Box */}
            <div className="p-4 bg-amber-50 rounded-lg border border-amber-200">
              <h4 className="font-semibold text-amber-900 mb-2">What will happen:</h4>
              <ul className="text-sm text-amber-800 space-y-1 list-disc list-inside">
                <li>Empty positions will be automatically filled with available players</li>
                <li>New players will be added to the roster and assigned positions</li>
                <li>All innings will be regenerated to balance playing time</li>
                <li>Position preferences and rotation rules will be respected</li>
                <li>The algorithm will ensure all 9 field positions are filled</li>
              </ul>
            </div>
          </div>
          
          <DialogFooter className="sm:space-x-2">
            <Button
              variant="outline"
              onClick={() => setShowQuickEditDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleApplyQuickChanges}
              disabled={Object.values(quickEditAttendance).filter(Boolean).length < 8 || isUpdating}
              className="bg-baseball-green hover:bg-baseball-green/90 text-white"
            >
              {isUpdating ? "Updating..." : "Apply Changes & Regenerate"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ViewLineup;


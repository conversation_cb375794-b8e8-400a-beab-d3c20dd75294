import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useTeam } from "@/contexts/TeamContext";
import { generateId } from "@/lib/utils-enhanced";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { useIsMobile } from "@/hooks/use-mobile";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { Trophy, BarChart3, Users, Clock, ChevronRight, ChevronLeft, HelpCircle, RefreshCw, Sparkles } from "lucide-react";
import SimpleLineupTable from "@/components/SimpleLineupTable";
import { optimizeSeriesBalance } from "@/lib/balance-optimizer";

interface SeriesData {
  games: string[]; // Array of lineup IDs
  seriesName: string;
  createdDate: string;
  seriesId?: string; // Unique identifier for the series
}

const ViewBatchSeries = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { lineups, players, refreshTeamData, rotationRules, updateLineup } = useTeam();
  const [seriesData, setSeriesData] = useState<SeriesData | null>(null);
  const [currentGameIndex, setCurrentGameIndex] = useState(0);
  const [playerStats, setPlayerStats] = useState<any>({});
  const [isOptimizing, setIsOptimizing] = useState(false);
  const isMobile = useIsMobile();

  // Helper to convert player ID to name
  const getPlayerName = (playerValue: string): string => {
    if (!playerValue) return '';
    
    // Check if it's a UUID (player ID)
    if (playerValue.length === 36 && playerValue.includes('-')) {
      const player = players.find(p => p.id === playerValue);
      return player ? player.name : playerValue;
    }
    
    // Otherwise return as is (already a name)
    return playerValue;
  };

  useEffect(() => {
    const state = location.state as { seriesData?: SeriesData } | null;
    if (state && state.seriesData) {
      console.log("🎮 ViewBatchSeries: Received series data:", state.seriesData);
      console.log("📚 ViewBatchSeries: Available lineups:", lineups.length, "total");
      console.log("📚 Available lineup IDs:", lineups.map(l => l.id));
      console.log("📚 Available lineup details:", lineups.map(l => ({ id: l.id, name: l.name, seriesId: l.seriesId, gameNumber: l.gameNumber })));
      
      // Always use the provided game IDs first (these are the real persisted IDs)
      setSeriesData(state.seriesData);
      
      // Validate that all series games exist in lineups
      const missingLineups = state.seriesData.games.filter(gameId => 
        !lineups.find(l => l.id === gameId)
      );
      
      if (missingLineups.length > 0) {
        console.error("🚨 ViewBatchSeries: Missing lineups for IDs:", missingLineups);
        console.error("🚨 Expected game IDs:", state.seriesData.games);
        console.error("🚨 Available lineup IDs:", lineups.map(l => l.id));
        
        // If all lineups are missing and we just navigated from batch generation,
        // try refreshing the team data first
        if (missingLineups.length === state.seriesData.games.length && refreshTeamData) {
          console.log("🔄 All lineups missing - attempting refresh...");
          refreshTeamData().then(() => {
            console.log("🔄 Team data refreshed, lineups should be available now");
            // Re-check after refresh
            const stillMissingLineups = state.seriesData.games.filter(gameId => 
              !lineups.find(l => l.id === gameId)
            );
            if (stillMissingLineups.length === 0) {
              console.log("✅ All lineups found after refresh");
            }
          }).catch(error => {
            console.error("🔄 Failed to refresh team data:", error);
            toast.error("Failed to load lineups. Please try refreshing the page.");
          });
          // Don't show error yet - let the effect re-run when lineups updates
          return;
        }
        
        // Try to find by seriesId if available
        if (state.seriesData.seriesId || lineups.some(l => l.seriesId)) {
          console.log("🔍 Attempting to recover by seriesId...");
          const potentialSeriesId = state.seriesData.seriesId || 
            lineups.find(l => l.seriesTitle === state.seriesData.seriesName)?.seriesId;
          
          if (potentialSeriesId) {
            const seriesLineups = lineups
              .filter(l => l.seriesId === potentialSeriesId)
              .sort((a, b) => (a.gameNumber || 0) - (b.gameNumber || 0));
            
            if (seriesLineups.length > 0) {
              console.log(`✅ Found ${seriesLineups.length} lineups for series ${potentialSeriesId}`);
              const updatedSeriesData = {
                ...state.seriesData,
                games: seriesLineups.map(l => l.id),
                seriesId: potentialSeriesId
              };
              setSeriesData(updatedSeriesData);
              calculatePlayerStats(updatedSeriesData.games);
              return;
            }
          }
        }
        
        toast.error(`Cannot find ${missingLineups.length} games from series. Lineups may still be saving.`, {
          duration: 6000,
          action: {
            label: "Refresh",
            onClick: () => window.location.reload(),
          },
        });
      }
      
      calculatePlayerStats(state.seriesData.games);
    } else {
      navigate("/dashboard");
      toast.error("No series data found");
    }
  }, [location.state, navigate, lineups]);

  // Force re-calculation when game index changes
  useEffect(() => {
    if (seriesData) {
      console.log(`Game index changed to ${currentGameIndex}, lineup ID: ${seriesData.games[currentGameIndex]}`);
      const lineup = lineups.find(l => l.id === seriesData.games[currentGameIndex]);
      console.log('Found lineup:', lineup ? lineup.name : 'NOT FOUND');
    }
  }, [currentGameIndex, seriesData, lineups]);

  const calculatePlayerStats = (gameIds: string[]) => {
    const stats: {[playerName: string]: {
      totalInnings: number;
      totalBench: number;
      gamesPlayed: number;
      positions: {[position: string]: number};
    }} = {};

    // Create a set of valid player names for filtering
    const validPlayerNames = new Set(players.map(p => p.name));

    // Calculate stats across all games
    gameIds.forEach(gameId => {
      const lineup = lineups.find(l => l.id === gameId);
      if (!lineup || !lineup.innings) return;

      const playersInGame = new Set<string>();

      lineup.innings.forEach(inning => {
        // Count field positions
        Object.entries(inning.positions).forEach(([position, playerValue]) => {
          if (position !== 'bench' && playerValue && typeof playerValue === 'string') {
            const playerName = getPlayerName(playerValue);
            // Only track stats for players in the current roster
            if (validPlayerNames.has(playerName)) {
              if (!stats[playerName]) {
                stats[playerName] = { totalInnings: 0, totalBench: 0, gamesPlayed: 0, positions: {} };
              }
              stats[playerName].totalInnings++;
              stats[playerName].positions[position] = (stats[playerName].positions[position] || 0) + 1;
              playersInGame.add(playerName);
            }
          }
        });

        // Count bench time
        if (inning.positions.bench && Array.isArray(inning.positions.bench)) {
          inning.positions.bench.forEach((playerValue: string) => {
            if (playerValue) {
              const playerName = getPlayerName(playerValue);
              // Only track stats for players in the current roster
              if (validPlayerNames.has(playerName)) {
                if (!stats[playerName]) {
                  stats[playerName] = { totalInnings: 0, totalBench: 0, gamesPlayed: 0, positions: {} };
                }
                stats[playerName].totalBench++;
                playersInGame.add(playerName);
              }
            }
          });
        }
      });

      // Count games played
      playersInGame.forEach(playerName => {
        stats[playerName].gamesPlayed++;
      });
    });

    setPlayerStats(stats);
  };

  const handleNavigateToGame = (index: number) => {
    if (!seriesData) return;
    const totalGames = seriesData.games.length;
    if (index >= 0 && index < totalGames) {
      console.log(`Switching from game ${currentGameIndex + 1} to game ${index + 1}`);
      console.log(`Current lineup ID: ${seriesData.games[currentGameIndex]}`);
      console.log(`New lineup ID: ${seriesData.games[index]}`);
      setCurrentGameIndex(index);
    }
  };

  // Early return after all hooks have been called
  if (!seriesData || !lineups.length) {
    return <div>Loading...</div>;
  }

  const currentLineupId = seriesData.games[currentGameIndex];
  const currentLineup = lineups.find(l => l.id === currentLineupId);
  const totalGames = seriesData.games.length;
  
  // Debug current lineup lookup
  console.log("🔍 DEBUG Lineup Lookup:");
  console.log(`• Looking for lineup ID: ${currentLineupId}`);
  console.log(`• Available lineups: ${lineups.length}`);
  console.log(`• Current lineup found: ${currentLineup ? 'Yes' : 'No'}`);
  if (currentLineup) {
    console.log(`• Found lineup name: ${currentLineup.name}`);
    console.log(`• Innings count: ${currentLineup.innings?.length || 0}`);
  }

  const handleViewFullLineup = () => {
    if (currentLineup) {
      navigate(`/view-lineup/${currentLineup.id}`, { 
        state: { 
          fromSeries: true, 
          seriesData,
          gameIndex: currentGameIndex 
        } 
      });
    }
  };

  const handleOptimizeBalance = async () => {
    if (!seriesData) return;
    
    setIsOptimizing(true);
    
    try {
      // Get the current series lineups
      const seriesLineups = seriesData.games
        .map(gameId => lineups.find(l => l.id === gameId))
        .filter((l): l is Lineup => l !== undefined);
      
      if (seriesLineups.length === 0) {
        toast.error("Cannot find series lineups to optimize");
        return;
      }
      
      // Run the optimization
      const result = optimizeSeriesBalance(seriesLineups, players);
      
      if (result.changes.length === 0) {
        toast.info("Lineups are already well balanced!");
        return;
      }
      
      // Update each lineup
      console.log(`🔄 Applying ${result.changes.length} optimizations...`);
      
      for (let i = 0; i < result.optimizedLineups.length; i++) {
        const optimizedLineup = result.optimizedLineups[i];
        await updateLineup(optimizedLineup);
      }
      
      // Refresh data
      if (refreshTeamData) {
        await refreshTeamData();
      }
      
      // Recalculate stats
      calculatePlayerStats(seriesData.games);
      
      toast.success(
        <div>
          <p>Balance optimized! Score improved to {result.balanceScore}%</p>
          <p className="text-sm mt-1">{result.changes.length} swaps made</p>
        </div>
      );
      
      console.log("📋 Optimization changes:", result.changes);
    } catch (error) {
      console.error("Error optimizing balance:", error);
      toast.error("Failed to optimize balance");
    } finally {
      setIsOptimizing(false);
    }
  };
  
  // Calculate balance score
  const balanceScore = () => {
    const inningsArray = Object.values(playerStats)
      .filter((stat: any) => stat.gamesPlayed > 0)
      .map((stat: any) => stat.totalInnings);
    
    if (inningsArray.length === 0) return 100;
    
    const totalInnings = inningsArray.reduce((sum, innings) => sum + innings, 0);
    const avgInnings = totalInnings / inningsArray.length;
    const max = Math.max(...inningsArray);
    const min = Math.min(...inningsArray);
    const range = max - min;
    
    // Calculate standard deviation for a more nuanced score
    const variance = inningsArray.reduce((sum, innings) => {
      return sum + Math.pow(innings - avgInnings, 2);
    }, 0) / inningsArray.length;
    const stdDev = Math.sqrt(variance);
    
    // Score based on multiple factors
    let score = 100;
    
    // Penalty for range (most important)
    if (range > 2) {
      score -= (range - 2) * 15; // -15 points per inning over 2
    }
    
    // Penalty for standard deviation
    if (stdDev > 1) {
      score -= (stdDev - 1) * 20;
    }
    
    // Penalty for extreme outliers
    inningsArray.forEach(innings => {
      const deviation = Math.abs(innings - avgInnings);
      if (deviation > avgInnings * 0.3) { // More than 30% deviation
        score -= 10;
      }
    });
    
    return Math.max(0, Math.round(score));
  };

  return (
    <div className="min-h-screen flex flex-col bg-baseball-tan/20">
      <Header 
        title={localStorage.getItem(`series_name_group-${seriesData.createdDate}`) || seriesData.seriesName || "Game Series"} 
        showBack 
        backLink="/dashboard"
      />
        <main className="flex-1 container mx-auto px-4 py-4 sm:py-6 lg:py-8 max-w-7xl">
        {/* Series Navigation */}
        <Card className={`${isMobile ? "mb-4" : "mb-6"} border-t-4 border-baseball-green`}>
          <CardContent className={isMobile ? "pt-4 px-3" : "pt-6"}>
            <div className="flex items-center justify-between mb-4">
              <Button
                variant="outline"
                size={isMobile ? "icon" : "sm"}
                onClick={() => handleNavigateToGame(currentGameIndex - 1)}
                disabled={currentGameIndex === 0}
                className={isMobile ? "h-10 w-10" : ""}
              >
                <ChevronLeft className="w-4 h-4" />
                {!isMobile && <span className="ml-1">Previous</span>}
              </Button>
              
              <div className="text-center flex-1 mx-2">
                <h2 className={`font-bold text-baseball-navy ${isMobile ? "text-lg" : "text-xl"}`}>
                  {currentLineup?.name || `Game ${currentGameIndex + 1}`}
                </h2>
                <p className="text-sm text-gray-600">
                  Game {currentGameIndex + 1} of {totalGames}
                </p>
              </div>

              <Button
                variant="outline"
                size={isMobile ? "icon" : "sm"}
                onClick={() => handleNavigateToGame(currentGameIndex + 1)}
                disabled={currentGameIndex === totalGames - 1}
                className={isMobile ? "h-10 w-10" : ""}
              >
                {!isMobile && <span className="mr-1">Next</span>}
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>

            {/* Quick Game Selector */}
            <div className={`flex gap-2 justify-center ${isMobile ? "flex-wrap" : ""}`}>
              {seriesData.games.map((_, index) => (
                <button
                  key={index}
                  onClick={() => handleNavigateToGame(index)}
                  className={`${isMobile ? "w-8 h-8 text-sm" : "w-10 h-10"} rounded-full font-medium transition-all ${
                    index === currentGameIndex
                      ? 'bg-baseball-green text-white'
                      : 'bg-gray-200 hover:bg-gray-300'
                  }`}
                >
                  {index + 1}
                </button>
              ))}
            </div>
          </CardContent>
        </Card>

        <div className={isMobile ? "space-y-4" : "grid lg:grid-cols-3 gap-6"}>
          {/* Current Game Preview */}
          <div className={isMobile ? "" : "lg:col-span-2"}>
            <Card>
              <CardHeader>
                <CardTitle className={`flex items-center justify-between ${isMobile ? "flex-col gap-2" : ""}`}>
                  <span className={isMobile ? "text-base" : ""}>Lineup Preview</span>
                  <div className="flex gap-2">
                    <Button
                      size={isMobile ? "icon" : "sm"}
                      variant="outline"
                      onClick={() => {
                        toast.info("Individual game regeneration coming soon!");
                        // TODO: Implement individual game regeneration
                        // This would regenerate just this one game while keeping others
                      }}
                      title="Regenerate just this game"
                      className={isMobile ? "h-8 w-8" : ""}
                    >
                      <RefreshCw className="w-4 h-4" />
                      {!isMobile && <span className="sr-only">Regenerate</span>}
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleViewFullLineup}
                      className="bg-baseball-green hover:bg-baseball-green/90"
                    >
                      {isMobile ? "View Full" : "View Full Lineup"}
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {currentLineup && currentLineup.innings && currentLineup.innings.length > 0 ? (
                  <div className="overflow-x-auto">
                    <SimpleLineupTable lineup={currentLineup} maxInnings={currentLineup.innings.length <= 5 ? undefined : 5} />
                    {currentLineup.innings.length > 5 && (
                      <p className="text-sm text-gray-600 mt-2 text-center">
                        Showing first 5 innings. Click "View Full Lineup" for complete game.
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="p-4 bg-yellow-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <p className="text-gray-600">No lineup data available</p>
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => window.location.reload()}
                      >
                        Refresh Page
                      </Button>
                    </div>
                    <div className="text-sm text-gray-500 mb-3">
                      <p>Debug info:</p>
                      <p>• Looking for lineup ID: {seriesData?.games[currentGameIndex]}</p>
                      <p>• Available lineups: {lineups.length}</p>
                      <p>• Current lineup found: {currentLineup ? 'Yes' : 'No'}</p>
                      {currentLineup && <p>• Has innings: {currentLineup.innings ? 'Yes' : 'No'}</p>}
                      <p>• All available IDs: {lineups.map(l => l.id.slice(0, 8)).join(', ')}</p>
                    </div>
                    <div className="text-xs bg-red-50 p-2 rounded border">
                      <p className="font-medium text-red-800 mb-1">Troubleshooting:</p>
                      <p className="text-red-700">If this persists after refresh, the lineups may not have been saved properly during creation. Try creating the series again.</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Series Statistics */}
          <div className="space-y-6">
            <Card>
              <CardHeader className={`bg-baseball-navy text-white ${isMobile ? "py-3" : ""}`}>
                <CardTitle className={`flex items-center gap-2 ${isMobile ? "text-base" : ""}`}>
                  <Trophy className={isMobile ? "w-4 h-4" : "w-5 h-5"} />
                  Series Overview
                </CardTitle>
              </CardHeader>
              <CardContent className={isMobile ? "pt-3 px-3" : "pt-4"}>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Games:</span>
                    <span className="font-bold">{totalGames}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-1">
                      <span className="text-gray-600">Balance Score:</span>
                      <button
                        type="button"
                        className={isMobile ? "p-1" : ""}
                        onClick={() => {
                          if (isMobile) {
                            toast.info(
                              <div>
                                <p className="font-semibold mb-2">Balance Score</p>
                                <p className="mb-2">Measures how evenly playing time is distributed:</p>
                                <ul className="space-y-1">
                                  <li>• 100% = Perfect balance</li>
                                  <li>• 80%+ = Good balance</li>
                                  <li>• 50-79% = Decent</li>
                                  <li>• &lt;50% = Needs improvement</li>
                                </ul>
                                <p className="mt-2 text-xs">The algorithm typically achieves 50-70%. Manual adjustments can improve this to 80%+.</p>
                              </div>,
                              { duration: 6000 }
                            );
                          }
                        }}
                      >
                        <div className="group relative">
                          <HelpCircle className="w-4 h-4 text-gray-400 cursor-help" />
                          {!isMobile && (
                            <div className="absolute left-0 bottom-full mb-2 w-64 p-3 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity z-10 pointer-events-none">
                              <p className="mb-2">Balance Score measures how evenly playing time is distributed:</p>
                              <ul className="space-y-1">
                                <li>• 100% = Perfect balance</li>
                                <li>• 80%+ = Good balance</li>
                                <li>• 50-79% = Decent (manual adjustments recommended)</li>
                                <li>• &lt;50% = Needs improvement</li>
                              </ul>
                              <p className="mt-2 text-xs">The algorithm typically achieves 50-70%. Manual adjustments can improve this to 80%+.</p>
                              <div className="absolute bottom-0 left-4 transform translate-y-1/2 rotate-45 w-2 h-2 bg-gray-900"></div>
                            </div>
                          )}
                        </div>
                      </button>
                    </div>
                    <span className={`font-bold ${balanceScore() >= 80 ? 'text-green-600' : 'text-orange-600'}`}>
                      {balanceScore().toFixed(0)}%
                    </span>
                  </div>
                  
                  {/* Show tip for improving balance if score is low */}
                  {balanceScore() < 70 && (
                    <div className="mt-2 p-2 bg-blue-50 rounded-lg">
                      <p className="text-xs text-blue-700">
                        <strong>Tip:</strong> To improve balance, view individual games and manually swap players with high/low playing time. Moving low-inning players from bench to field positions will increase your balance score.
                      </p>
                    </div>
                  )}
                  {/* Regenerate Series Button */}
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full mt-3"
                    onClick={() => {
                      // Extract batch data from existing lineups
                      const seriesLineups = seriesData.games
                        .map(gameId => lineups.find(l => l.id === gameId))
                        .filter(Boolean);
                      
                      if (seriesLineups.length === 0) {
                        toast.error("Cannot find series lineups to regenerate");
                        return;
                      }

                      // Extract games data from lineups
                      const games = seriesLineups.map((lineup, index) => ({
                        id: generateId(),
                        name: lineup?.name || `Game ${index + 1}`,
                        gameDate: lineup?.gameDate || new Date().toISOString().split('T')[0],
                        gameTime: lineup?.gameTime || "10:00",
                        numberOfInnings: lineup?.innings?.length || 7,
                        createdDate: new Date().toISOString(),
                        innings: [],
                        battingOrder: [],
                        attendance: {}
                      }));

                      // Extract rotation settings from first lineup and merge with team defaults
                      const firstLineup = seriesLineups[0];
                      console.log("🔄 Regenerating series - extracting settings from lineup:", firstLineup?.rotationSettings);
                      
                      // Start with team defaults, then override with lineup-specific settings
                      const rotationSettings = {
                        // Base settings from team rules
                        competitiveMode: rotationRules?.competitiveMode || false,
                        allowPitcherRotation: rotationRules?.allowPitcherRotation !== false,
                        allowCatcherRotation: rotationRules?.allowCatcherRotation !== false,
                        respectPositionLockouts: rotationRules?.respectPositionLockouts !== false,
                        
                        // Critical rotation frequencies - preserve from original lineup
                        rotateLineupEvery: firstLineup?.rotationSettings?.rotateLineupEvery || rotationRules?.rotateLineupEvery || 1,
                        rotatePitcherEvery: firstLineup?.rotationSettings?.rotatePitcherEvery || rotationRules?.rotatePitcherEvery || 2,
                        limitBenchTime: firstLineup?.rotationSettings?.limitBenchTime || false,
                        
                        // Batch-specific settings
                        maxConsecutiveBenchInnings: 999, // Disabled for multi-day series
                        equalPlayingTime: false // Measured across entire series
                      };
                      
                      console.log("🔄 Using rotation settings for regeneration:", rotationSettings);

                      // Create batch data for regeneration
                      const batchData = {
                        id: seriesData.seriesId || generateId(),
                        seriesTitle: seriesData.seriesName,
                        rotationSettings,
                        games,
                        crossGameTracking: {
                          totalGames: games.length,
                          playingTimeTargets: {},
                          pitcherInningsTracked: true,
                          catcherInningsTracked: true
                        }
                      };

                      // Navigate to batch lineup generation with batch data
                      navigate("/batch-lineup-generation", {
                        state: {
                          batchData,
                          regenerateMode: true,
                          previousSeriesData: seriesData
                        }
                      });
                    }}
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Regenerate Series
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className={`bg-baseball-green text-white ${isMobile ? "py-3" : ""}`}>
                <CardTitle className={`flex items-center gap-2 ${isMobile ? "text-base" : ""}`}>
                  <BarChart3 className={isMobile ? "w-4 h-4" : "w-5 h-5"} />
                  Playing Time Summary
                </CardTitle>
              </CardHeader>
              <CardContent className={isMobile ? "pt-3 px-3" : "pt-4"}>
                {/* Balance improvement insights */}
                {(() => {
                  const score = balanceScore();
                  if (score >= 90) {
                    // Show success message for high balance
                    return (
                      <div className="mb-4 p-3 bg-green-50 rounded-lg border border-green-200">
                        <h4 className="text-sm font-semibold text-green-900 mb-1">Excellent Balance!</h4>
                        <p className="text-xs text-green-800">
                          Your playing time distribution is very fair. {score === 100 ? "Perfect balance achieved!" : "Minor adjustments could still improve balance."}
                        </p>
                      </div>
                    );
                  } else if (score > 30) {
                    // Show balance tips for medium scores
                    return (
                      <div className="mb-4 p-3 bg-amber-50 rounded-lg border border-amber-200">
                        <h4 className="text-sm font-semibold text-amber-900 mb-2">Quick Balance Tips:</h4>
                    <div className="text-xs text-amber-800 space-y-1">
                      {(() => {
                        const stats = Object.entries(playerStats)
                          .filter(([name, stat]: [string, any]) => stat.gamesPlayed > 0)
                          .map(([name, stat]: [string, any]) => ({ name, innings: stat.totalInnings }))
                          .sort((a, b) => b.innings - a.innings);
                        
                        if (stats.length === 0) return null;
                        
                        const avg = stats.reduce((sum, p) => sum + p.innings, 0) / stats.length;
                        const highest = stats[0];
                        const lowest = stats[stats.length - 1];
                        
                        return (
                          <>
                            {highest.innings > avg + 1.5 && (
                              <p>• Consider swapping <strong>{highest.name}</strong> ({highest.innings} innings) with bench players in 1-2 games</p>
                            )}
                            {lowest.innings < avg - 1.5 && (
                              <p>• Give <strong>{lowest.name}</strong> ({lowest.innings} innings) more field time</p>
                            )}
                            <p className="mt-1 text-amber-700">
                              Target: Each player should have {Math.floor(avg)}-{Math.ceil(avg)} innings
                            </p>
                          </>
                        );
                      })()}
                    </div>
                    <Button
                      size="sm"
                      variant="default"
                      className="mt-3 w-full bg-amber-600 hover:bg-amber-700"
                      onClick={handleOptimizeBalance}
                      disabled={isOptimizing}
                    >
                      {isOptimizing ? (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          Optimizing...
                        </>
                      ) : (
                        <>
                          <Sparkles className="w-4 h-4 mr-2" />
                          Optimize Balance
                        </>
                      )}
                    </Button>
                  </div>
                    );
                  }
                  return null;
                })()}
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {Object.entries(playerStats)
                    .filter(([playerName, stat]: [string, any]) => {
                      // Only show players who are in the current roster and have played
                      const validPlayerNames = new Set(players.map(p => p.name));
                      return validPlayerNames.has(playerName) && stat.gamesPlayed > 0;
                    })
                    .sort(([_, a]: [string, any], [__, b]: [string, any]) => b.totalInnings - a.totalInnings)
                    .map(([playerName, stat]: [string, any]) => (
                      <div key={playerName} className="text-sm">
                        <div className="flex justify-between items-center mb-1">
                          <span className="font-medium">{playerName}</span>
                          <span className="text-xs text-gray-600">
                            {stat.totalInnings} innings
                          </span>
                        </div>
                        <div className="bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-baseball-green rounded-full h-2 transition-all"
                            style={{
                              width: `${(stat.totalInnings / Math.max(...Object.values(playerStats).map((s: any) => s.totalInnings))) * 100}%`
                            }}
                          />
                        </div>
                        <div className="flex justify-between text-xs text-gray-500 mt-1">
                          <span>Games: {stat.gamesPlayed}/{totalGames}</span>
                          <span>Bench: {stat.totalBench}</span>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Action Buttons */}
        <div className={`mt-6 flex ${isMobile ? "flex-col gap-2" : "justify-center gap-3"}`}>
          <Button
            variant="outline"
            onClick={() => navigate("/dashboard")}
            className={isMobile ? "w-full" : ""}
          >
            Back to Dashboard
          </Button>
          <Button
            onClick={() => navigate("/batch-games")}
            className={`bg-baseball-green hover:bg-baseball-green/90 ${isMobile ? "w-full" : ""}`}
          >
            Create Another Series
          </Button>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default ViewBatchSeries;

import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useTeam } from "@/contexts/TeamContext";
import { createInitialInningLineup, rotatePlayersForNextInning, generateCompleteLineup } from "@/lib/utils-enhanced";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Card } from "@/components/ui/card";
import { toast } from "sonner";
import { Circle, UserCheck, UserX, Users } from "lucide-react";
import { AttendancePageSkeleton } from "@/components/LoadingSkeletons";

const SetLineupAttendance = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { players, addLineup, updateLineup, rotationRules, loading: teamLoading } = useTeam();
  const [attendance, setAttendance] = useState<{[playerId: string]: boolean}>({});
  const [lineupData, setLineupData] = useState<any>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);

  useEffect(() => {
    // Debug players array
    console.log("DEBUG: Players array in SetLineupAttendance:", players);
    console.log("DEBUG: Players array length:", players.length);

    // Get lineup data from location state
    const state = location.state as { lineupData?: any; isEditing?: boolean } | null;
    console.log("DEBUG: Location state:", state);

    if (state && state.lineupData) {
      setLineupData(state.lineupData);
      setIsEditing(state.isEditing || false);

      // If editing, use existing attendance
      if (state.lineupData.attendance && Object.keys(state.lineupData.attendance).length > 0) {
        console.log("Using existing attendance data:", state.lineupData.attendance);
        setAttendance(state.lineupData.attendance);
      } else {
        // Initialize attendance with all players present
        console.log("Initializing attendance for all players:", players);

        // Make sure we have players before initializing attendance
        if (players.length > 0) {
          const initialAttendance = players.reduce((acc, player) => {
            acc[player.id] = true;
            return acc;
          }, {} as {[playerId: string]: boolean});

          console.log("Initial attendance created:", initialAttendance);
          setAttendance(initialAttendance);
        } else {
          console.warn("No players found when initializing attendance");
        }
      }
    } else {
      // If no data, go back to create lineup
      navigate("/create-lineup");
      toast.error("Please fill in lineup details first");
    }
    
    // Mark initialization as complete once we've processed the data
    setIsInitializing(false);
  }, [players, location.state, navigate]);

  // Make sure attendance is always initialized with all players
  useEffect(() => {
    // If we have players but attendance is empty, initialize it
    if (players.length > 0 && Object.keys(attendance).length === 0) {
      console.log("Re-initializing attendance for all players");
      const initialAttendance = players.reduce((acc, player) => {
        acc[player.id] = true;
        return acc;
      }, {} as {[playerId: string]: boolean});

      setAttendance(initialAttendance);

      // No need to save to localStorage as we're using the database
      console.log("Using attendance data:", initialAttendance);
    }
  }, [players, attendance]);

  // No need for localStorage fallback as we're using the database

  const handleAttendanceChange = (playerId: string, isPresent: boolean) => {
    setAttendance(prev => ({
      ...prev,
      [playerId]: isPresent
    }));
  };

  const handleAllPresent = () => {
    const allPresent = players.reduce((acc, player) => {
      acc[player.id] = true;
      return acc;
    }, {} as {[playerId: string]: boolean});

    setAttendance(allPresent);
    toast.success("All players marked as present");
  };

  const handleGenerateOptimalLineup = async () => {
    if (!lineupData) return;

    // Count present players
    const presentCount = Object.values(attendance).filter(Boolean).length;

    if (presentCount < 8) {
      toast.error("You need at least 8 players to create a lineup");
      return;
    }

    try {
      console.log("🚀 Generating optimal lineup directly from attendance");

      // Get available players based on attendance
      const availablePlayers = players.filter(player => attendance[player.id]);
      console.log("Available players:", availablePlayers.map(p => p.name));

      // Use default rotation rules (can be customized later)
      const rotationOptions = {
        limitBenchTime: true,
        allowPitcherRotation: rotationRules.allowPitcherRotation,
        allowCatcherRotation: rotationRules.allowCatcherRotation,
        respectPositionLockouts: rotationRules.respectPositionLockouts,
        equalPlayingTime: rotationRules.equalPlayingTime,
        rotateLineupEvery: 1,
        rotatePitcherEvery: 2
      };

      const numberOfInnings = lineupData.numberOfInnings || 7;

      // Generate all innings using the enhanced algorithm
      const innings = generateCompleteLineup(availablePlayers, numberOfInnings, rotationOptions);

      // Create the complete lineup
      const newLineup = {
        ...lineupData,
        attendance,
        innings,
        battingOrder: [],
        rotationSettings: {
          limitBenchTime: true,
          rotateLineupEvery: 1,
          rotatePitcherEvery: 2
        }
      };

      // Add to context
      addLineup(newLineup);

      // Navigate directly to view lineup page
      navigate(`/view-lineup/${newLineup.id}`);
      toast.success("🎯 Optimal lineup generated successfully!");
    } catch (error) {
      console.error("Error generating optimal lineup:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create optimal lineup");
    }
  };

  const handleContinue = () => {
    if (!lineupData) return;

    // Count present players
    const presentCount = Object.values(attendance).filter(Boolean).length;

    if (presentCount < 9) {
      toast.error("You need at least 9 players to create a lineup");
      return;
    }

    try {
      console.log("Continuing with attendance data:", attendance);

      // If editing, just update attendance
      if (isEditing) {
        const updatedLineup = {
          ...lineupData,
          attendance
        };

        updateLineup(updatedLineup);
        navigate(`/view-lineup/${lineupData.id}`);
        toast.success("Lineup attendance updated successfully!");
        return;
      }

      // Create a complete lineup object with attendance
      const completeLineupData = {
        ...lineupData,
        attendance
      };

      console.log("Navigating to SetFirstInning with complete data:", completeLineupData);

      // For new lineups, navigate to the SetFirstInning page
      navigate("/set-first-inning", {
        state: {
          lineupData: completeLineupData,
          attendance
        }
      });
    } catch (error) {
      console.error("Error in handleContinue:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create lineup");
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-baseball-tan/20">
      <Header
        title={isEditing ? "Update Player Attendance" : "Who's Playing Today?"}
        showBack
        backLink={isEditing ? `/view-lineup/${lineupData?.id}` : "/create-lineup"}
      />

      <main className="flex-1 container mx-auto px-4 py-8">
        <Card className="max-w-3xl mx-auto bg-white p-6 shadow-lg border-t-4 border-baseball-green">
          {(teamLoading || isInitializing) ? (
            <AttendancePageSkeleton />
          ) : (
          <div className="mb-6">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-2">
                <Users className="text-baseball-navy" size={24} />
                <h2 className="text-xl font-bold text-baseball-navy">Player Attendance</h2>
              </div>

              <Button
                variant="outline"
                className="flex items-center gap-2"
                onClick={handleAllPresent}
              >
                <UserCheck size={16} />
                Mark All Present
              </Button>
            </div>

            <p className="mb-4">
              Check off the players who will be participating in today's game.
              You need at least 9 players to create a lineup.
            </p>

            <div className="bg-baseball-lightblue p-4 rounded-md mb-6">
              <p className="font-medium">
                For {lineupData?.name} on {lineupData?.gameDate},
                who's playing today?
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {players && players.length > 0 ? (
                players.map((player) => (
                  <div
                    key={player.id}
                    className={`flex items-center justify-between p-3 rounded-md border ${
                      attendance[player.id]
                        ? "bg-baseball-lightgreen border-green-200"
                        : "bg-gray-100 border-gray-200"
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      {attendance[player.id] ? (
                        <UserCheck className="text-green-600" size={20} />
                      ) : (
                        <UserX className="text-gray-400" size={20} />
                      )}
                      <span className="font-medium">{player.name}</span>
                    </div>

                    <Checkbox
                      checked={attendance[player.id] || false}
                      onCheckedChange={(checked) =>
                        handleAttendanceChange(player.id, checked === true)
                      }
                    />
                  </div>
                ))
              ) : (
                <div className="col-span-2 p-4 bg-amber-50 border border-amber-200 rounded-md">
                  <p className="text-amber-700 text-center">
                    No players found. Please add players to your team first.
                  </p>
                </div>
              )}
            </div>

            <div className="mt-8 flex justify-center">
              <Button
                onClick={handleContinue}
                className="bg-baseball-green hover:bg-baseball-green/90 text-white"
              >
                {isEditing ? "Update Attendance" : "Continue to Set First Inning"}
              </Button>
            </div>
          </div>
          )}
        </Card>
      </main>

      <Footer />
    </div>
  );
};

export default SetLineupAttendance;

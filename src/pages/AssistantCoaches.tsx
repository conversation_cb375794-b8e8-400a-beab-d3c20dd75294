// FEATURE_FLAG assistantCoachAccess false
// This component is temporarily disabled but backend functionality remains intact
// To re-enable: Remove feature flag comments in App.tsx and Dashboard.tsx
// Roadmap: Restore in Q3 2025

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { toast } from "sonner";
import { Users, Plus, Trash, Mail, Check, X } from "lucide-react";
import { supabase } from "@/supabaseClient";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface Coach {
  id: string;
  email: string;
  status: 'pending' | 'active';
  created_at: string;
}

const AssistantCoaches = () => {
  const { user } = useAuth();
  const [coaches, setCoaches] = useState<Coach[]>([]);
  const [newCoachEmail, setNewCoachEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [coachToDelete, setCoachToDelete] = useState<string | null>(null);
  
  useEffect(() => {
    loadCoaches();
  }, [user]);
  
  const loadCoaches = async () => {
    if (!user) return;
    
    try {
      // In a real implementation, this would fetch assistant coaches from Supabase
      const { data, error } = await supabase
        .from('assistant_coaches')
        .select('*')
        .eq('owner_id', user.id);
        
      if (error) {
        console.error("Error loading coaches:", error);
        return;
      }
      
      setCoaches(data || []);
    } catch (error) {
      console.error("Error loading coaches:", error);
    }
  };
  
  const handleAddCoach = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      toast.error("You must be logged in to add coaches");
      return;
    }
    
    if (!newCoachEmail.trim()) {
      toast.error("Please enter an email address");
      return;
    }
    
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(newCoachEmail)) {
      toast.error("Please enter a valid email address");
      return;
    }
    
    setIsLoading(true);
    
    try {
      // In a real implementation, this would add the coach to Supabase
      const { data, error } = await supabase
        .from('assistant_coaches')
        .insert({
          owner_id: user.id,
          email: newCoachEmail,
          status: 'pending',
          created_at: new Date().toISOString()
        })
        .select();
        
      if (error) {
        throw error;
      }
      
      // Add the new coach to the local state
      if (data && data.length > 0) {
        setCoaches([...coaches, data[0]]);
      }
      
      setNewCoachEmail("");
      toast.success(`Invitation sent to ${newCoachEmail}`);
    } catch (error: any) {
      console.error("Error adding coach:", error);
      toast.error(error.message || "Failed to add coach");
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleDeleteCoach = (coachId: string) => {
    setCoachToDelete(coachId);
  };
  
  const confirmDeleteCoach = async () => {
    if (!coachToDelete) return;
    
    try {
      // In a real implementation, this would delete the coach from Supabase
      const { error } = await supabase
        .from('assistant_coaches')
        .delete()
        .eq('id', coachToDelete);
        
      if (error) {
        throw error;
      }
      
      // Remove the coach from the local state
      setCoaches(coaches.filter(coach => coach.id !== coachToDelete));
      toast.success("Coach removed successfully");
    } catch (error: any) {
      console.error("Error removing coach:", error);
      toast.error(error.message || "Failed to remove coach");
    } finally {
      setCoachToDelete(null);
    }
  };
  
  return (
    <div className="min-h-screen flex flex-col">
      <Header title="Assistant Coaches" showBack backLink="/dashboard" />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto">
          <Card className="mb-8 border-t-4 border-baseball-green">
            <CardHeader className="bg-baseball-navy text-white">
              <CardTitle className="flex items-center gap-2">
                <Plus size={20} />
                Invite Assistant Coach
              </CardTitle>
              <CardDescription className="text-gray-300">
                Give other coaches access to your teams
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <form onSubmit={handleAddCoach} className="flex gap-4">
                <Input
                  placeholder="Enter coach's email address"
                  value={newCoachEmail}
                  onChange={(e) => setNewCoachEmail(e.target.value)}
                  className="flex-1"
                  type="email"
                />
                <Button 
                  type="submit" 
                  variant="baseball"
                  disabled={isLoading}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Send Invite
                </Button>
              </form>
            </CardContent>
          </Card>
          
          <Card className="border-t-4 border-baseball-green">
            <CardHeader className="bg-baseball-navy text-white">
              <CardTitle className="flex items-center gap-2">
                <Users size={20} />
                Assistant Coaches
              </CardTitle>
              <CardDescription className="text-gray-300">
                Manage who has access to your teams
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              {coaches.length === 0 ? (
                <p className="text-center py-4 text-gray-500">
                  You haven't added any assistant coaches yet.
                </p>
              ) : (
                <div className="space-y-4">
                  {coaches.map((coach) => (
                    <div 
                      key={coach.id} 
                      className="flex items-center justify-between p-4 rounded-md bg-gray-100"
                    >
                      <div className="flex items-center gap-3">
                        <Mail className="h-5 w-5 text-baseball-navy" />
                        <div>
                          <p className="font-medium">{coach.email}</p>
                          <p className="text-sm text-gray-500">
                            {coach.status === 'pending' ? 'Invitation pending' : 'Active'}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        {coach.status === 'pending' && (
                          <Button 
                            size="sm" 
                            variant="outline"
                            className="text-amber-600 border-amber-600"
                            onClick={() => toast.info("Invitation resent")}
                          >
                            Resend
                          </Button>
                        )}
                        
                        <Button 
                          size="sm" 
                          variant="ghost"
                          className="text-red-600"
                          onClick={() => handleDeleteCoach(coach.id)}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
      
      <AlertDialog open={!!coachToDelete} onOpenChange={(open) => !open && setCoachToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Assistant Coach</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove this coach? They will no longer have access to your teams.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDeleteCoach}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Remove
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      <Footer />
    </div>
  );
};

export default AssistantCoaches;

import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useTeam, Player, InningLineup } from "@/contexts/TeamContext";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Circle, AlertCircle, UserMinus, AlertTriangle, Loader2 } from "lucide-react";
import { canPlayPosition } from "@/lib/utils-enhanced";
import { generateOptimalLineupWrapper } from "@/lib/lineup-generation-wrapper";
import { improvedRotateForNextInning } from "@/lib/improvedRotation";
import BasicSelect from "@/components/BasicSelect";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON>eader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

const SetFirstInning = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { players, addLineup, getAvailablePlayers, rotationRules } = useTeam();

  const [lineupData, setLineupData] = useState<any>(null);
  const [attendance, setAttendance] = useState<{[playerId: string]: boolean}>({});
  const [availablePlayers, setAvailablePlayers] = useState<Player[]>([]);
  const [inningData, setInningData] = useState<InningLineup>({
    inning: 1,
    positions: {
      leftField: "",
      centerField: "",
      rightField: "",
      thirdBase: "",
      shortstop: "",
      secondBase: "",
      firstBase: "",
      catcher: "",
      pitcher: "",
      bench: [] // This will be populated with empty slots based on player count
    }
  });

  // State for restriction warning dialog
  const [showRestrictionDialog, setShowRestrictionDialog] = useState(false);
  const [pendingRestriction, setPendingRestriction] = useState<{
    position: string;
    player: Player;
    restriction: string;
  } | null>(null);

  // State for game-specific rotation rules
  const [limitBenchTime, setLimitBenchTime] = useState<boolean>(true);
  const [rotateLineupEvery, setRotateLineupEvery] = useState<number>(1);
  const [rotatePitcherEvery, setRotatePitcherEvery] = useState<number>(2);
  const [isGeneratingOptimal, setIsGeneratingOptimal] = useState(false);

  // Parse state from location
  useEffect(() => {
    console.log("SetFirstInning useEffect running");
    const state = location.state as {
      lineupData?: any;
      attendance?: {[playerId: string]: boolean}
    } | null;

    // First try to get data from location state
    if (state && state.lineupData) {
      console.log("Lineup data received from location state:", state.lineupData);

      // Get attendance from either the lineupData or separate attendance property
      const attendanceData = state.lineupData.attendance || state.attendance;

      if (attendanceData && Object.keys(attendanceData).length > 0) {
        console.log("Attendance data received:", attendanceData);
        setLineupData(state.lineupData);
        setAttendance(attendanceData);
      } else {
        console.warn("No attendance data in location state, trying localStorage");

        // No data available, redirect to create lineup
        toast.error("Missing lineup or attendance data");
        navigate("/create-lineup");
        return;
      }
    } else {
      // No data available, redirect to create lineup
      toast.error("Missing lineup data");
      navigate("/create-lineup");
      return;
    }

    // Get available players based on attendance
    // Pass both lineupData and the separate attendance data to handle both cases
    console.log("Calling getAvailablePlayers with lineupData:", lineupData);
    console.log("Attendance data being passed:", attendance);

    // Demo mode now uses the database just like regular mode

    // Call the updated function with both parameters
    const availablePlayers = getAvailablePlayers(lineupData, attendance);
    console.log("Available players returned:", availablePlayers);

    if (availablePlayers.length === 0) {
      console.warn("No available players returned! Using all players as fallback.");
      // In demo mode, if no players are returned, use all players from the team
      setAvailablePlayers(players);
    } else {
      setAvailablePlayers(availablePlayers);
    }

    // Initialize bench with all available players
    const playerNames = players.map(p => p.name);
    console.log("Player names:", playerNames);

    try {
      // Initialize with empty positions
      const fieldPositions = [
        "pitcher", "catcher", "firstBase", "secondBase", 
        "thirdBase", "shortstop", "leftField", "centerField", "rightField"
      ];

      // Create empty bench positions based on the number of players
      const benchCount = Math.max(0, playerNames.length - 9);
      console.log("Creating bench with", benchCount, "positions");
      const benchPlayers = new Array(benchCount).fill('');

      // Create initial positions with all empty values
      const initialPositions: Record<string, string | string[]> = {
        bench: benchPlayers
      };

      // Initialize all field positions as empty
      fieldPositions.forEach(pos => {
        initialPositions[pos] = "";
      });

      console.log("Setting initial positions:", JSON.stringify(initialPositions));

      // Set the initial empty positions
      setInningData(prev => {
        console.log("Previous inning data:", prev);
        const updated = {
          ...prev,
          positions: initialPositions as any
        };
        console.log("Updated inning data:", updated);
        return updated;
      });
    } catch (error) {
      console.error("Error initializing positions:", error);
      toast.error("Error initializing positions. Please try again.");
    }

    // Initialize rotation settings from team defaults
    if (rotationRules) {
      // For recreational mode (not competitive), default to rotating every inning for better balance
      const defaultRotateEvery = rotationRules.competitiveMode ? 
        (rotationRules.rotateLineupEvery || 2) : 1;
      
      setRotateLineupEvery(defaultRotateEvery);
      setRotatePitcherEvery(rotationRules.rotatePitcherEvery || 2);
      // In competitive mode, disable bench time limiting by default to allow strategic control
      setLimitBenchTime(rotationRules.competitiveMode ? false : (rotationRules.limitBenchTime ?? true));
    }
  }, [location.state, navigate, getAvailablePlayers, rotationRules]);

  // Helper function to find a player's current position in the inning
  const findPlayerCurrentPosition = (playerName: string): string | null => {
    if (!inningData || !playerName) return null;

    try {
      // Check field positions
      const fieldPositions = [
        "pitcher", "catcher", "firstBase", "secondBase", 
        "thirdBase", "shortstop", "leftField", "centerField", "rightField"
      ];

      for (const pos of fieldPositions) {
        if (inningData.positions[pos as keyof typeof inningData.positions] === playerName) {
          console.log(`Found player ${playerName} in field position: ${pos}`);
          return pos;
        }
      }

      // Check bench positions
      if (Array.isArray(inningData.positions.bench)) {
        const benchIndex = inningData.positions.bench.findIndex(name => name === playerName);
        if (benchIndex !== -1) {
          console.log(`Found player ${playerName} in bench position: bench${benchIndex + 1}`);
          return `bench${benchIndex + 1}`;
        }
      }

      console.log(`Player ${playerName} not found in any position`);
      return null;
    } catch (error) {
      console.error("Error in findPlayerCurrentPosition:", error);
      return null;
    }
  };

  // Helper function to get a readable position name
  const getPositionLabel = (positionId: string): string => {
    if (!positionId) return "Unknown";

    // For bench positions, format them nicely
    if (positionId.startsWith('bench')) {
      const benchNumber = parseInt(positionId.replace('bench', ''));
      return `Bench ${benchNumber}`;
    }

    // For field positions, use the mapping
    const fieldPositionMapping: {[key: string]: string} = {
      "leftField": "Left Field",
      "centerField": "Center Field",
      "rightField": "Right Field",
      "thirdBase": "Third Base",
      "shortstop": "Shortstop",
      "secondBase": "Second Base",
      "firstBase": "First Base",
      "catcher": "Catcher",
      "pitcher": "Pitcher"
    };

    return fieldPositionMapping[positionId] || positionId;
  };

  // Helper function to convert internal position ID to display name
  const getPositionDisplayName = (positionId: string): string => {
    if (!positionId) return "Unknown";

    const mapping: {[key: string]: string} = {
      "leftField": "Left Field",
      "centerField": "Center Field",
      "rightField": "Right Field",
      "thirdBase": "Third Base",
      "shortstop": "Shortstop",
      "secondBase": "Second Base",
      "firstBase": "First Base",
      "catcher": "Catcher",
      "pitcher": "Pitcher"
    };

    // Handle bench positions
    if (positionId.startsWith('bench')) {
      const benchNumber = parseInt(positionId.replace('bench', ''));
      return `Bench ${benchNumber}`;
    }

    return mapping[positionId] || positionId;
  };

  // Function to check if a player has a restriction for a position using teamRoles
  const checkPositionRestriction = (playerObj: Player, positionId: string): string | null => {
    if (!playerObj || !playerObj.teamRoles) {
      return null; // No restrictions if player or roles are undefined
    }

    const positionName = getPositionDisplayName(positionId);
    const role = playerObj.teamRoles[positionId as keyof typeof playerObj.teamRoles];

    // Check if the player is restricted from this position (false means never play)
    if (role === false) {
      return positionName;
    }

    return null;
  };

  // Function to apply the player change after restriction warning
  const applyPlayerChangeWithRestriction = () => {
    if (!pendingRestriction || !inningData) return;

    const { position, player } = pendingRestriction;
    console.log("Applying player change with restriction override:", player.name, "->", position);

    // DIRECT DEBUG: Show the current state
    console.log("BEFORE RESTRICTION - Current inningData:", JSON.stringify(inningData));

    try {
      // Make a deep copy of the current positions
      const newPositions = JSON.parse(JSON.stringify(inningData.positions));

      // Find player's current position
      const currentPosition = findPlayerCurrentPosition(player.name);
      console.log("Player current position:", currentPosition);

      // If player is currently assigned somewhere, remove them from that position
      if (currentPosition) {
        if (currentPosition.startsWith('bench')) {
          // Remove from bench
          const benchIndex = parseInt(currentPosition.replace('bench', '')) - 1;
          if (benchIndex >= 0 && benchIndex < newPositions.bench.length) {
            newPositions.bench[benchIndex] = '';
          }
        } else {
          // Remove from field position
          newPositions[currentPosition] = '';
        }
      }

      // If there's already a player in the target position, move them to bench
      if (position.startsWith('bench')) {
        // Assigning to bench
        const benchIndex = parseInt(position.replace('bench', '')) - 1;
        const currentBenchPlayer = newPositions.bench[benchIndex];

        if (currentBenchPlayer) {
          // Find empty bench slot for displaced player
          const emptyBenchIndex = newPositions.bench.findIndex((p: string) => !p);
          if (emptyBenchIndex !== -1) {
            newPositions.bench[emptyBenchIndex] = currentBenchPlayer;
          } else {
            newPositions.bench.push(currentBenchPlayer);
          }
        }

        // Assign player to bench
        newPositions.bench[benchIndex] = player.name;
      } else {
        // Assigning to field position
        const currentFieldPlayer = newPositions[position];

        if (currentFieldPlayer) {
          // Find empty bench slot for displaced player
          const emptyBenchIndex = newPositions.bench.findIndex((p: string) => !p);
          if (emptyBenchIndex !== -1) {
            newPositions.bench[emptyBenchIndex] = currentFieldPlayer;
          } else {
            newPositions.bench.push(currentFieldPlayer);
          }
        }

        // Assign player to field position
        newPositions[position] = player.name;
      }

      // Create a new inning data object
      const updatedInningData = {
        inning: inningData.inning,
        positions: newPositions
      };

      console.log("Setting inning data with restriction override:", JSON.stringify(updatedInningData));

      // DIRECT APPROACH: Force a complete state replacement
      setInningData(null); // First clear the state

      // Then set the new state after a small delay
      setTimeout(() => {
        setInningData(updatedInningData);

        // DIRECT DEBUG: Show the state after update
        console.log("AFTER RESTRICTION - Updated inningData:", JSON.stringify(updatedInningData));

        // Show a toast to confirm the change
        toast.success(`Assigned ${player.name} to ${position} (with restriction override)`);
      }, 10);

    } catch (error) {
      console.error("Error applying player change with restriction:", error);
      toast.error("Failed to update player position");
    }

    // Reset pending restriction
    setPendingRestriction(null);
  };

  const handlePlayerChange = (position: string, playerName: string) => {
    console.log(`handlePlayerChange called: position=${position}, playerName=${playerName}`);

    // DIRECT DEBUG: Show the current state
    console.log("BEFORE - Current inningData:", JSON.stringify(inningData));

    if (!inningData) {
      console.error("No inning data available");
      return;
    }

    try {
      // Make a deep copy of the current positions
      const newPositions = JSON.parse(JSON.stringify(inningData.positions));

      // Find the player's current position (if any)
      const currentPosition = findPlayerCurrentPosition(playerName);
      console.log("Player current position:", currentPosition);

      // If player is already in this position, do nothing
      if (currentPosition === position) {
        console.log("Player already in this position, no change needed");
        return;
      }

      // If selecting a player (not clearing)
      if (playerName) {
        // Find the player object
        const playerObj = availablePlayers.find(p => p.name === playerName);
        if (!playerObj) {
          toast.error("Player not found");
          return;
        }

        // Check for position restrictions using teamRoles
        if (!position.startsWith("bench")) {
          const positionDisplay = getPositionLabel(position);
          if (!canPlayPosition(playerObj, positionDisplay, rotationRules.respectPositionLockouts, rotationRules.competitiveMode || false)) {
            console.log("Position restriction found for:", playerName, "at", positionDisplay);
            const role = playerObj.teamRoles?.[position as keyof typeof playerObj.teamRoles];
            const restrictionText = role === false ? "never" : "cannot play";
            // Show restriction warning dialog
            setPendingRestriction({
              position,
              player: playerObj,
              restriction: restrictionText
            });
            setShowRestrictionDialog(true);
            return;
          }
        }

        // If player is currently assigned somewhere, remove them from that position
        if (currentPosition) {
          if (currentPosition.startsWith('bench')) {
            // Remove from bench
            const benchIndex = parseInt(currentPosition.replace('bench', '')) - 1;
            if (benchIndex >= 0 && benchIndex < newPositions.bench.length) {
              newPositions.bench[benchIndex] = '';
            }
          } else {
            // Remove from field position
            newPositions[currentPosition] = '';
          }
        }

        // If there's already a player in the target position, move them to bench
        if (position.startsWith('bench')) {
          // Assigning to bench
          const benchIndex = parseInt(position.replace('bench', '')) - 1;
          const currentBenchPlayer = newPositions.bench[benchIndex];

          if (currentBenchPlayer) {
            // Find empty bench slot for displaced player
            const emptyBenchIndex = newPositions.bench.findIndex((p: string) => !p);
            if (emptyBenchIndex !== -1) {
              newPositions.bench[emptyBenchIndex] = currentBenchPlayer;
            } else {
              newPositions.bench.push(currentBenchPlayer);
            }
          }

          // Assign player to bench
          newPositions.bench[benchIndex] = playerName;
        } else {
          // Assigning to field position
          const currentFieldPlayer = newPositions[position];

          if (currentFieldPlayer) {
            // Find empty bench slot for displaced player
            const emptyBenchIndex = newPositions.bench.findIndex((p: string) => !p);
            if (emptyBenchIndex !== -1) {
              newPositions.bench[emptyBenchIndex] = currentFieldPlayer;
            } else {
              newPositions.bench.push(currentFieldPlayer);
            }
          }

          // Assign player to field position
          newPositions[position] = playerName;
        }
      } else {
        // Clearing a position (playerName is empty)
        if (position.startsWith('bench')) {
          // Clear bench position
          const benchIndex = parseInt(position.replace('bench', '')) - 1;
          if (benchIndex >= 0 && benchIndex < newPositions.bench.length) {
            newPositions.bench[benchIndex] = '';
          }
        } else {
          // Clear field position
          newPositions[position] = '';
        }
      }

      // Create a new inning data object
      const updatedInningData = {
        inning: inningData.inning,
        positions: newPositions
      };

      console.log("Setting inning data:", JSON.stringify(updatedInningData));

      // DIRECT APPROACH: Force a complete state replacement
      setInningData(null); // First clear the state

      // Then set the new state after a small delay
      setTimeout(() => {
        setInningData(updatedInningData);

        // DIRECT DEBUG: Show the state after update
        console.log("AFTER - Updated inningData:", JSON.stringify(updatedInningData));

        // Show a toast to confirm the change
        toast.success(`Assigned ${playerName} to ${position}`);
      }, 10);

    } catch (error) {
      console.error("Error in handlePlayerChange:", error);
      toast.error("An error occurred while updating the lineup");
    }
  };

  const handleGenerateOptimalLineup = async () => {
    if (!lineupData) {
      toast.error("Missing lineup data");
      return;
    }

    setIsGeneratingOptimal(true);
    const toastId = toast.loading("🎯 Generating optimal lineup...");

    try {
      // Use the enhanced algorithm to generate the complete lineup
      const numberOfInnings = lineupData.numberOfInnings || 7;

      // Apply rotation rules from context, but use game-specific settings
      const rotationOptions = {
        limitBenchTime: limitBenchTime,
        allowPitcherRotation: rotationRules.allowPitcherRotation,
        allowCatcherRotation: rotationRules.allowCatcherRotation,
        respectPositionLockouts: rotationRules.respectPositionLockouts,
        equalPlayingTime: rotationRules.equalPlayingTime,
        rotateLineupEvery: rotateLineupEvery,
        rotatePitcherEvery: rotatePitcherEvery,
        competitiveMode: rotationRules.competitiveMode
      };

      console.log("🚀 Generating optimal lineup with enhanced algorithm");
      console.log("Available players:", availablePlayers.map(p => p.name));
      console.log("Rotation options:", rotationOptions);
      console.log("Number of innings:", numberOfInnings);

      // Generate all innings using the enhanced algorithm (async version)
      const innings = await generateOptimalLineupWrapper(availablePlayers, numberOfInnings, rotationOptions);

      // Create the lineup with innings data
      const newLineup = {
        ...lineupData,
        attendance,
        innings,
        battingOrder: [],
        // Store game-specific rotation settings for regenerate function
        rotationSettings: {
          limitBenchTime: limitBenchTime,
          rotateLineupEvery: rotateLineupEvery,
          rotatePitcherEvery: rotatePitcherEvery,
          competitiveMode: rotationRules.competitiveMode
        }
      };

      // Add to context
      addLineup(newLineup);

      // Navigate to view lineup page
      toast.dismiss(toastId);
      navigate(`/view-lineup/${newLineup.id}`);
      toast.success("🎯 Optimal lineup generated successfully!");
    } catch (error) {
      console.error("Error generating optimal lineup:", error);
      toast.dismiss(toastId);
      
      // Use user-friendly error messages
      let errorMessage = "Unable to create lineup. Please try again.";
      if (error instanceof Error) {
        if (error.message.includes("position")) {
          errorMessage = "Not enough players can play required positions. Check position assignments in Team Roster.";
        } else if (error.message.includes("constraint")) {
          errorMessage = "Unable to satisfy all position requirements. Try adjusting player assignments.";
        }
      }
      
      toast.error(errorMessage);
    } finally {
      setIsGeneratingOptimal(false);
    }
  };

  const handleAutoFillBestLineup = async () => {
    if (!availablePlayers || availablePlayers.length === 0) {
      toast.error("No available players to assign");
      return;
    }

    try {
      console.log("🎯 Auto-filling best lineup for first inning");
      console.log("Available players:", availablePlayers.map(p => p.name));

      // Import the new algorithm for auto-fill
      // Use the already imported generateOptimalLineupWrapper

      // Create rules for the auto-fill (respect current game settings)
      const autoFillRules = {
        respectPositionLockouts: rotationRules.respectPositionLockouts,
        competitiveMode: true, // Use competitive scoring for best assignments
        limitBenchTime: limitBenchTime,
        allowPitcherRotation: rotationRules.allowPitcherRotation,
        allowCatcherRotation: rotationRules.allowCatcherRotation,
        equalPlayingTime: rotationRules.equalPlayingTime,
        rotateLineupEvery: rotateLineupEvery,
        rotatePitcherEvery: rotatePitcherEvery,
        maxConsecutiveBenchInnings: 2
      };

      // Generate just 1 inning using the new algorithm
      const generatedInnings = await generateOptimalLineupWrapper(
        availablePlayers,
        1, // Just generate the first inning
        autoFillRules
      );

      if (!generatedInnings || generatedInnings.length === 0) {
        toast.error("Could not generate lineup. Please check player position assignments.");
        return;
      }

      const firstInning = generatedInnings[0];
      console.log("✅ First inning generated:", firstInning);

      // Extract positions from the generated inning
      const autoFilledPositions: any = {};
      
      // Copy field positions
      Object.entries(firstInning.positions).forEach(([position, playerName]) => {
        if (position !== 'bench' && playerName) {
          autoFilledPositions[position] = playerName;
        }
      });

      // Handle bench players
      const benchPlayers = firstInning.positions.bench || [];
      benchPlayers.forEach((playerName: string, index: number) => {
        autoFilledPositions[`bench${index + 1}`] = playerName;
      });

      // Update the inning data
      setInningData(prev => ({
        ...prev,
        positions: autoFilledPositions
      }));

      // Show success message with details
      const fieldPositionCount = Object.keys(autoFilledPositions).filter(key => !key.startsWith('bench')).length;
      toast.success(`✅ Auto-filled ${fieldPositionCount} field positions with best available players!`, {
        description: "You can still manually adjust any positions as needed.",
        duration: 4000
      });

      console.log("✅ Auto-fill complete:", autoFilledPositions);

    } catch (error) {
      console.error("Error auto-filling lineup:", error);
      toast.error(error instanceof Error ? error.message : "Failed to auto-fill lineup. Please try manual assignment.");
    }
  };

  const handleGenerateLineup = async () => {
    if (!lineupData) {
      toast.error("Missing lineup data");
      return;
    }

    // Validate that all field positions are filled
    const fieldPositions = [
      "pitcher", "catcher", "firstBase", "secondBase", 
      "thirdBase", "shortstop", "leftField", "centerField", "rightField"
    ];

    const emptyPositions = fieldPositions.filter(pos =>
      !inningData.positions[pos as keyof typeof inningData.positions]
    );

    if (emptyPositions.length > 0) {
      toast.error(`Please fill all field positions. Missing: ${emptyPositions.map(pos => getPositionLabel(pos)).join(", ")}`);
      return;
    }

    // Ensure all attending players are accounted for
    const allAssignedPlayers: string[] = [];

    // Add all field positions
    fieldPositions.forEach(pos => {
      const player = inningData.positions[pos as keyof typeof inningData.positions] as string;
      if (player) {
        allAssignedPlayers.push(player);
      }
    });

    // Add bench players
    inningData.positions.bench.forEach(player => {
      if (player) {
        allAssignedPlayers.push(player);
      }
    });

    // Check if any players are missing
    const missingPlayers = availablePlayers.filter(player =>
      !allAssignedPlayers.includes(player.name)
    );

    if (missingPlayers.length > 0) {
      toast.error(`Some players are not assigned to any position: ${missingPlayers.map(p => p.name).join(", ")}`);
      return;
    }

    // Check for position restrictions in the first inning using teamRoles
    let hasRestrictions = false;
    const restrictionWarnings: string[] = [];

    fieldPositions.forEach(pos => {
      const playerName = inningData.positions[pos as keyof typeof inningData.positions] as string;
      if (playerName) {
        const playerObj = availablePlayers.find(p => p.name === playerName);
        if (playerObj) {
          // Use canPlayPosition which checks teamRoles
          const positionDisplay = getPositionLabel(pos);
          if (!canPlayPosition(playerObj, positionDisplay, rotationRules.respectPositionLockouts, rotationRules.competitiveMode || false)) {
            hasRestrictions = true;
            const role = playerObj.teamRoles?.[pos as keyof typeof playerObj.teamRoles];
            if (role === false) {
              restrictionWarnings.push(`${playerName} is assigned to ${positionDisplay} but is marked as "never" for that position`);
            } else {
              restrictionWarnings.push(`${playerName} is assigned to ${positionDisplay} but cannot play that position`);
            }
          }
        }
      }
    });

    // If there are restrictions, show a warning
    if (hasRestrictions) {
      const warningMessage = `Warning: Some players are assigned to positions they should not play:\n\n${restrictionWarnings.join('\n')}`;
      const proceed = window.confirm(warningMessage + '\n\nDo you want to proceed anyway?');
      if (!proceed) {
        return;
      }
    }

    try {
      // Generate all innings based on the first inning
      const innings = [inningData];
      let currentInning = inningData;

      // Generate subsequent innings using rotation with rules
      const numberOfInnings = lineupData.numberOfInnings || 7;
      for (let i = 1; i < numberOfInnings; i++) {
        // Apply rotation rules from context, but use game-specific settings
        const rotationOptions = {
          limitBenchTime: limitBenchTime, // Use game-specific setting
          allowPitcherRotation: rotationRules.allowPitcherRotation,
          allowCatcherRotation: rotationRules.allowCatcherRotation,
          respectPositionLockouts: rotationRules.respectPositionLockouts,
          equalPlayingTime: rotationRules.equalPlayingTime,
          rotateLineupEvery: rotateLineupEvery, // Use game-specific setting
          rotatePitcherEvery: rotatePitcherEvery // Use game-specific setting
        };

        // Use the improved rotation algorithm that properly handles rotation frequencies
        const nextInning = improvedRotateForNextInning(currentInning, availablePlayers, rotationOptions);

        // Check for position restrictions in the rotated inning
        let hasRotationRestrictions = false;
        const rotationWarnings: string[] = [];

        fieldPositions.forEach(pos => {
          const playerName = nextInning.positions[pos as keyof typeof nextInning.positions] as string;
          if (playerName) {
            const playerObj = availablePlayers.find(p => p.name === playerName);
            if (playerObj) {
              const restriction = checkPositionRestriction(playerObj, pos);
              if (restriction) {
                hasRotationRestrictions = true;
                rotationWarnings.push(`${playerName} would be assigned to ${getPositionLabel(pos)} in inning ${i+1} but has a restriction for ${restriction}`);
              }
            }
          }
        });

        // If there are restrictions in the rotation, show a warning
        if (hasRotationRestrictions && rotationWarnings.length > 0) {
          toast.warning(`Note: Some position restrictions were detected in the rotation for inning ${i+1}. You may want to edit this inning manually after lineup creation.`, {
            duration: 6000
          });
        }

        innings.push(nextInning);
        currentInning = nextInning;
      }

      // Create the lineup with innings data
      const newLineup = {
        ...lineupData,
        attendance,
        innings,
        battingOrder: [],
        // Store game-specific rotation settings for regenerate function
        rotationSettings: {
          limitBenchTime: limitBenchTime,
          rotateLineupEvery: rotateLineupEvery,
          rotatePitcherEvery: rotatePitcherEvery
        }
      };

      // Add to context and get the lineup with real database ID
      const savedLineup = await addLineup(newLineup);

      // Navigate to view lineup page with the real ID
      navigate(`/view-lineup/${savedLineup.id}`);
      toast.success("Lineup created successfully!");
    } catch (error) {
      console.error("Error generating lineup:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create lineup");
    }
  };

  // Get all field positions grouped by field areas for consistent UX
  const fieldPositions = [
    // Battery positions
    { id: "pitcher", label: "Pitcher" },
    { id: "catcher", label: "Catcher" },
    // Infield positions
    { id: "firstBase", label: "First Base" },
    { id: "secondBase", label: "Second Base" },
    { id: "shortstop", label: "Shortstop" },
    { id: "thirdBase", label: "Third Base" },
    // Outfield positions
    { id: "leftField", label: "Left Field" },
    { id: "centerField", label: "Center Field" },
    { id: "rightField", label: "Right Field" }
  ];

  // Calculate bench positions based on available players
  const benchPositions = (() => {
    const benchCount = Math.max(0, availablePlayers.length - 9);
    const benchPositions = [];

    for (let i = 1; i <= benchCount; i++) {
      benchPositions.push({ id: `bench${i}`, label: `Bench ${i}` });
    }

    return benchPositions;
  })();

  // Field and bench positions are used separately in the UI

  if (!lineupData) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>;
  }

  return (
    <div className="min-h-screen flex flex-col bg-baseball-tan/20">
      <Header
        title="Set First Inning Positions"
        showBack
        backLink="/set-lineup-attendance"
      />

      <main className="flex-1 container mx-auto px-4 py-6 sm:py-8">
        <div className="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-4 sm:p-6 lg:p-8 border-t-4 border-baseball-green">
          <div className="mb-8">
            <h2 className="text-xl font-bold text-baseball-navy mb-2">First Inning - {lineupData.name}</h2>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <div className="flex items-start gap-3">
                <div className="text-2xl">🚀</div>
                <div>
                  <h3 className="font-semibold text-blue-900 mb-1">Quick Start Options</h3>
                  <p className="text-sm text-blue-700 mb-2">
                    <strong>Auto-Fill Best Lineup:</strong> Automatically assigns the highest-rated players to each position for the first inning only.
                  </p>
                  <p className="text-sm text-blue-700 mb-2">
                    <strong>Generate Optimal Lineup:</strong> Creates the complete game lineup using our enhanced algorithm.
                  </p>
                  <p className="text-xs text-blue-600">
                    Or manually assign positions below if you prefer to set the first inning yourself.
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-3 p-3 bg-baseball-lightgreen/30 rounded-md border border-baseball-green/20">
              <h3 className="text-sm font-semibold text-baseball-navy flex items-center">
                <AlertCircle className="h-4 w-4 mr-1 text-baseball-green" />
                Position Restrictions
              </h3>
              <p className="text-xs text-gray-600 mt-1">
                Players with position restrictions will be marked with a warning icon (<AlertTriangle className="h-3 w-3 inline text-amber-500" />).
                You'll be warned if you try to assign them to a restricted position.
              </p>
            </div>

            <div className="mt-3 p-3 bg-baseball-lightblue/30 rounded-md border border-baseball-navy/20">
              <h3 className="text-sm font-semibold text-baseball-navy flex items-center justify-between">
                <div className="flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1 text-baseball-navy" />
                  Player Assignment Status
                </div>
                <div className="text-xs">
                  {(() => {
                    // Count assigned players
                    const fieldPositions = [
                      "pitcher", "catcher", "firstBase", "secondBase", 
                      "thirdBase", "shortstop", "leftField", "centerField", "rightField"
                    ];

                    const assignedFieldPlayers = fieldPositions.filter(pos =>
                      inningData.positions[pos as keyof typeof inningData.positions]
                    ).length;

                    const benchPlayers = Array.isArray(inningData.positions.bench) ?
                      inningData.positions.bench.filter(p => p).length : 0;
                    const totalAssigned = assignedFieldPlayers + benchPlayers;
                    const totalPlayers = availablePlayers.length;

                    return (
                      <span className={totalAssigned === totalPlayers ? "text-green-600 font-semibold" : "text-amber-600 font-semibold"}>
                        {totalAssigned} of {totalPlayers} players assigned
                        {totalAssigned < totalPlayers && " - some players are missing!"}
                      </span>
                    );
                  })()}
                </div>
              </h3>

              {(() => {
                // Find players who aren't assigned to any position
                const allAssignedPlayers: string[] = [];

                // Add all field positions
                const fieldPositions = [
                  "pitcher", "catcher", "firstBase", "secondBase", 
                  "thirdBase", "shortstop", "leftField", "centerField", "rightField"
                ];

                fieldPositions.forEach(pos => {
                  const player = inningData.positions[pos as keyof typeof inningData.positions] as string;
                  if (player) {
                    allAssignedPlayers.push(player);
                  }
                });

                // Add bench players
                if (Array.isArray(inningData.positions.bench)) {
                  inningData.positions.bench.forEach(player => {
                    if (player) {
                      allAssignedPlayers.push(player);
                    }
                  });
                }

                // Find missing players
                const missingPlayers = availablePlayers.filter(player =>
                  !allAssignedPlayers.includes(player.name)
                );

                if (missingPlayers.length > 0) {
                  return (
                    <div className="mt-3 p-2 bg-amber-50 rounded border border-amber-200">
                      <h4 className="text-xs font-semibold text-amber-700 flex items-center">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        Unassigned Players:
                      </h4>
                      <div className="mt-1 flex flex-wrap gap-1">
                        {missingPlayers.map(player => (
                          <span
                            key={player.id}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800"
                          >
                            <UserMinus className="h-3 w-3 mr-1" />
                            {player.name}
                          </span>
                        ))}
                      </div>
                    </div>
                  );
                }

                return null;
              })()}

              <div className="mt-3 text-xs text-gray-600">
                <p>All players should be assigned to either a field position or a bench position.</p>
                <p>Use the dropdowns below to assign players to positions.</p>
              </div>
            </div>

            {/* Game-specific rotation rules - HIGHLIGHTED FOR VISIBILITY */}
            <div className="mt-4 border-l-4 border-red-500 border-2 border-red-300 bg-red-50/30 rounded-lg overflow-hidden shadow-sm">
              <div className="bg-red-50 px-6 py-4">
                <h3 className="text-lg font-bold text-red-900 flex items-center gap-2">
                  <span className="text-red-600">⚙️</span>
                  ⚠️ Important: Game Rotation Rules
                </h3>
                <p className="text-sm text-red-800 font-medium mt-2">
                  Override your team's default rotation settings for this specific game.
                </p>
              </div>
              <div className="px-6 py-4 bg-white">

              {/* Bench Time Limit */}
              <div className="flex items-start space-x-3 mb-4">
                <Checkbox
                  id="limitBenchTime"
                  checked={limitBenchTime}
                  onCheckedChange={(checked) => setLimitBenchTime(!!checked)}
                />
                <div>
                  <Label htmlFor="limitBenchTime" className="font-medium text-blue-900">
                    Limit Consecutive Bench Time
                  </Label>
                  <p className="text-sm text-blue-700 mt-1">
                    Prevent players from sitting on the bench for more than one inning in a row.
                    Players who were on the bench in the previous inning will be prioritized for field positions in the next inning.
                  </p>
                  {rotationRules.competitiveMode && (
                    <p className="text-xs text-amber-600 mt-2 italic">
                      💡 Competitive mode: Disabled by default to allow strategic control. You can adjust manually in ViewLineup.
                    </p>
                  )}
                </div>
              </div>

              {/* Rotation Frequency Settings */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4 pt-4 border-t border-blue-200">
                <div>
                  <Label htmlFor="rotateLineupEvery" className="font-medium text-blue-900 block mb-2">
                    Rotate lineup every:
                  </Label>
                  <div className="flex items-center space-x-2">
                    {[1, 2, 3].map((value) => (
                      <label key={value} className="flex items-center space-x-1 cursor-pointer">
                        <input
                          type="radio"
                          name="rotateLineupEvery"
                          value={value}
                          checked={rotateLineupEvery === value}
                          onChange={(e) => setRotateLineupEvery(parseInt(e.target.value))}
                          className="text-blue-600"
                        />
                        <span className="text-sm text-blue-800">{value}</span>
                      </label>
                    ))}
                    <span className="text-sm text-blue-700">innings</span>
                  </div>
                  <p className="text-xs text-blue-600 mt-1">
                    Lower numbers mean more frequent rotations and better playing time balance.
                  </p>
                  {!rotationRules.competitiveMode && rotateLineupEvery > 1 && (
                    <p className="text-xs text-amber-600 mt-1 font-medium">
                      ⚠️ Setting above 1 will reduce playing time balance
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="rotatePitcherEvery" className="font-medium text-blue-900 block mb-2">
                    Rotate pitcher every:
                  </Label>
                  <div className="flex items-center space-x-2">
                    {[1, 2, 3].map((value) => (
                      <label key={value} className="flex items-center space-x-1 cursor-pointer">
                        <input
                          type="radio"
                          name="rotatePitcherEvery"
                          value={value}
                          checked={rotatePitcherEvery === value}
                          onChange={(e) => setRotatePitcherEvery(parseInt(e.target.value))}
                          className="text-blue-600"
                        />
                        <span className="text-sm text-blue-800">{value}</span>
                      </label>
                    ))}
                    <span className="text-sm text-blue-700">innings</span>
                  </div>
                  <p className="text-xs text-blue-600 mt-1">
                    Controls how frequently the pitcher position rotates.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            {/* Field Positions */}
            <div className="mb-6">
              <h3 className="text-sm font-semibold text-baseball-navy mb-3">Field Positions</h3>
              {fieldPositions.map((position) => {
                const currentValue = inningData.positions[position.id as keyof typeof inningData.positions] as string || "";

                return (
                  <div
                    key={position.id}
                    className={`grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-0 items-start sm:items-center rounded-md overflow-hidden mb-3 p-3 ${currentValue ? 'bg-green-100' : 'bg-baseball-lightgreen'}`}
                  >
                    <div className="font-bold flex items-center">
                      <Circle className={`mr-2 h-4 w-4 ${currentValue ? 'text-green-600' : 'text-baseball-green'}`} />
                      <span className="text-sm sm:text-base">{position.label}:</span>
                      {currentValue && <span className="ml-2 text-green-700 font-bold text-sm sm:text-base sm:hidden">{currentValue}</span>}
                    </div>
                    <div className="sm:col-span-2 sm:p-2">
                      <BasicSelect
                        options={availablePlayers
                          .sort((a, b) => a.name.localeCompare(b.name))
                          .map(player => {
                          let label = player.name;
                          const currentPosition = findPlayerCurrentPosition(player.name);
                          const isAssigned = currentPosition !== null;
                          const hasRestriction = !position.id.startsWith('bench') &&
                            checkPositionRestriction(player, position.id) !== null;

                          if (hasRestriction) {
                            label += " (Restricted)";
                          }

                          if (isAssigned && currentPosition !== position.id) {
                            label += ` (in ${getPositionLabel(currentPosition)})`;
                          }

                          return { value: player.name, label };
                        })}
                        value={currentValue}
                        onChange={(value) => {
                          console.log(`BasicSelect onChange: position=${position.id}, value=${value}`);
                          handlePlayerChange(position.id, value);
                        }}
                        placeholder={`Choose player for ${position.label}`}
                      />
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Bench Positions */}
            {benchPositions.length > 0 && (
              <div className="mt-8 pt-4 border-t border-gray-200">
                <h3 className="text-sm font-semibold text-baseball-navy mb-3">Bench Positions</h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                  {benchPositions.map((position) => {
                    // For bench positions, we need to get the value from the bench array
                    const benchIndex = parseInt(position.id.replace("bench", "")) - 1;
                    const currentValue = Array.isArray(inningData.positions.bench) &&
                      benchIndex >= 0 && benchIndex < inningData.positions.bench.length ?
                      inningData.positions.bench[benchIndex] || "" : "";

                    return (
                      <div
                        key={position.id}
                        className={`grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-0 items-start sm:items-center rounded-md overflow-hidden mb-3 p-3 ${currentValue ? 'bg-blue-100' : 'bg-baseball-lightblue'}`}
                      >
                        <div className="font-bold flex items-center">
                          <Circle className={`mr-2 h-4 w-4 ${currentValue ? 'text-blue-600' : 'text-baseball-navy'}`} />
                          <span className="text-sm sm:text-base">{position.label}:</span>
                          {currentValue && <span className="ml-2 text-blue-700 font-bold text-sm sm:text-base sm:hidden">{currentValue}</span>}
                        </div>
                        <div className="sm:col-span-2 sm:p-2">
                          <BasicSelect
                            options={availablePlayers
                          .sort((a, b) => a.name.localeCompare(b.name))
                          .map(player => {
                              let label = player.name;
                              const currentPosition = findPlayerCurrentPosition(player.name);
                              const isAssigned = currentPosition !== null;

                              if (isAssigned && currentPosition !== position.id) {
                                label += ` (in ${getPositionLabel(currentPosition)})`;
                              }

                              return { value: player.name, label };
                            })}
                            value={currentValue}
                            onChange={(value) => {
                              console.log(`Bench BasicSelect onChange: position=${position.id}, value=${value}`);
                              handlePlayerChange(position.id, value);
                            }}
                            placeholder={`Choose player for ${position.label}`}
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>


          <div className="mt-10 flex flex-col sm:flex-row justify-center gap-3 sm:gap-4">
            <Button
              onClick={handleAutoFillBestLineup}
              variant="outline"
              className="border-baseball-green text-baseball-green hover:bg-baseball-green/10 w-full sm:w-auto font-semibold"
            >
              ⭐ Auto-Fill Best Lineup
            </Button>

            <Button
              onClick={() => {
                // Find players who aren't assigned to any position
                const allAssignedPlayers: string[] = [];

                // Add all field positions
                const fieldPositions = [
                  "pitcher", "catcher", "firstBase", "secondBase", 
                  "thirdBase", "shortstop", "leftField", "centerField", "rightField"
                ];

                fieldPositions.forEach(pos => {
                  const player = inningData.positions[pos as keyof typeof inningData.positions] as string;
                  if (player) {
                    allAssignedPlayers.push(player);
                  }
                });

                // Add bench players
                if (Array.isArray(inningData.positions.bench)) {
                  inningData.positions.bench.forEach(player => {
                    if (player) {
                      allAssignedPlayers.push(player);
                    }
                  });
                }

                // Find missing players
                const missingPlayers = availablePlayers
                  .filter(player => !allAssignedPlayers.includes(player.name))
                  .map(player => player.name);

                if (missingPlayers.length > 0) {
                  // Add missing players to empty bench slots
                  setInningData(prev => {
                    if (!prev) return prev;

                    // Create a copy of the current bench
                    const newBench = [...prev.positions.bench];

                    // For each missing player, find an empty bench slot
                    missingPlayers.forEach(playerName => {
                      const emptyIndex = newBench.findIndex(p => !p);
                      if (emptyIndex !== -1) {
                        // Add to empty slot
                        newBench[emptyIndex] = playerName;
                      } else {
                        // No empty slots, add to end
                        newBench.push(playerName);
                      }
                    });

                    return {
                      ...prev,
                      positions: {
                        ...prev.positions,
                        bench: newBench
                      }
                    };
                  });

                  toast.success(`Added ${missingPlayers.length} unassigned player(s) to the bench`);
                } else {
                  toast.info("All players are already assigned to positions");
                }
              }}
              variant="outline"
              className="border-baseball-navy text-baseball-navy hover:bg-baseball-navy/10 w-full sm:w-auto"
            >
              Auto-Assign Missing Players
            </Button>

            <Button
              onClick={handleGenerateOptimalLineup}
              className="bg-baseball-green hover:bg-baseball-green/90 text-white w-full sm:w-auto text-lg px-8 py-3"
              disabled={isGeneratingOptimal}
            >
              {isGeneratingOptimal ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  Generating...
                </>
              ) : (
                <>🚀 Generate Lineup</>
              )}
            </Button>
            
            {/* DEBUG: Test Algorithm Integration */}
            {process.env.NODE_ENV === 'development' && (
              <>
                <Button
                  onClick={async () => {
                    console.clear();
                    console.log('=== INTEGRATION TEST START ===');
                    
                    const teamId = availablePlayers[0]?.team_id || 'unknown';
                    
                    // Force enable new algorithm
                    localStorage.setItem('new-lineup-generation', 'true');
                    localStorage.setItem(`new-lineup-generation-${teamId}`, 'true');
                    
                    // Check position pools
                    console.log('--- CHECKING POSITION POOLS ---');
                    availablePlayers.forEach(player => {
                      console.log(`${player.name} teamRoles:`, player.teamRoles);
                    });
                    
                    // Test canPlayPosition
                    console.log('--- TESTING canPlayPosition ---');
                    const testPositions = ['Pitcher', 'Catcher', 'First Base', 'Second Base', 'Third Base', 'Shortstop', 'Left Field', 'Center Field', 'Right Field'];
                    availablePlayers.forEach(player => {
                      console.log(`\n${player.name}:`);
                      testPositions.forEach(pos => {
                        const canPlay = canPlayPosition(player, pos, true, rotationRules.competitiveMode);
                        if (canPlay || rotationRules.competitiveMode) {
                          console.log(`  ${pos}: ${canPlay ? '✅' : '❌'}`);
                        }
                      });
                    });
                    
                    console.log('=== INTEGRATION TEST END ===');
                  }}
                  className="bg-yellow-500 text-black p-2 rounded"
                >
                  TEST ALGORITHM INTEGRATION
                </Button>
                
                <Button
                  onClick={async () => {
                    console.clear();
                    console.log('=== COMPETITIVE MODE BUG TEST ===');
                    
                    if (!rotationRules.competitiveMode) {
                      toast.error('Enable competitive mode in Team Roster first!');
                      return;
                    }
                    
                    try {
                      // Create test player with limited positions
                      const testPlayer = availablePlayers[0];
                      if (!testPlayer) {
                        toast.error('No players available');
                        return;
                      }
                      
                      console.log('Test player:', testPlayer.name);
                      console.log('Team roles:', testPlayer.teamRoles);
                      
                      // Find positions this player can play
                      const allowedPositions = Object.entries(testPlayer.teamRoles || {})
                        .filter(([pos, role]) => role && role !== 'avoid' && role !== false)
                        .map(([pos]) => pos);
                      
                      console.log('Allowed positions:', allowedPositions);
                      
                      // Generate lineup
                      const innings = await generateOptimalLineupWrapper(
                        availablePlayers,
                        1, // Just test first inning
                        { ...rotationRules, competitiveMode: true }
                      );
                      
                      // Check where test player was placed
                      const firstInning = innings[0];
                      let playerPosition = null;
                      
                      for (const [pos, playerName] of Object.entries(firstInning.positions)) {
                        if (playerName === testPlayer.name) {
                          playerPosition = pos;
                          break;
                        }
                      }
                      
                      console.log('Player placed at:', playerPosition);
                      
                      if (playerPosition && !allowedPositions.includes(playerPosition)) {
                        console.error('❌ BUG STILL EXISTS!');
                        console.error(`${testPlayer.name} placed at ${playerPosition} but can only play: ${allowedPositions.join(', ')}`);
                        toast.error('BUG: Player placed at restricted position!');
                      } else if (playerPosition) {
                        console.log('✅ COMPETITIVE MODE WORKING!');
                        console.log(`${testPlayer.name} correctly placed at ${playerPosition}`);
                        toast.success('Success: Player placed at allowed position!');
                      } else {
                        console.log('Player on bench');
                        toast.info('Player placed on bench');
                      }
                      
                    } catch (error) {
                      console.error('Test failed:', error);
                      toast.error('Test failed: ' + error.message);
                    }
                  }}
                  variant="outline"
                  className="border-2 border-purple-500 bg-purple-50"
                >
                  🧪 TEST COMPETITIVE MODE FIX
                </Button>
              </>
            )}

            <Button
              onClick={handleGenerateLineup}
              variant="outline"
              size="sm"
              className="border-gray-400 text-gray-600 hover:bg-gray-50 w-full sm:w-auto text-sm"
            >
              Advanced: Generate from Manual First Inning
            </Button>
          </div>
        </div>
      </main>

      <Footer />

      {/* Position Restriction Warning Dialog */}
      <AlertDialog open={showRestrictionDialog} onOpenChange={setShowRestrictionDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              Position Restriction Warning
            </AlertDialogTitle>
            <AlertDialogDescription>
              {pendingRestriction && (
                <>
                  <p className="mb-2">
                    <strong>{pendingRestriction.player.name}</strong> should not play at <strong>{getPositionLabel(pendingRestriction.position)}</strong>.
                  </p>
                  <p className="mb-2">
                    This player has a position restriction for: <strong>{pendingRestriction.restriction}</strong>
                  </p>
                  <p className="text-xs text-gray-500 italic">
                    Note: Position restrictions are set in the Team Roster page. You can still assign the player to this position, but it's not recommended.
                  </p>
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setPendingRestriction(null)}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                applyPlayerChangeWithRestriction();
                setShowRestrictionDialog(false);
              }}
              className="bg-amber-500 hover:bg-amber-600"
            >
              Assign Anyway
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default SetFirstInning;

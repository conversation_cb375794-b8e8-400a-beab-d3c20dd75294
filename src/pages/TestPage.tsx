import React, { useEffect, useState } from 'react';
import { supabase } from '@/supabaseClient';
import { RotationDebugger } from '@/components/RotationDebugger';
import { RotationDebuggerEnhanced } from '@/components/RotationDebuggerEnhanced';
import { LineupGenerationTester } from '@/components/LineupGenerationTester';

const TestPage = () => {
  const [supabaseStatus, setSupabaseStatus] = useState('Testing...');
  const [envVars, setEnvVars] = useState<any>({});

  useEffect(() => {
    // Test environment variables
    const vars = {
      VITE_SUPABASE_URL: import.meta.env.VITE_SUPABASE_URL,
      VITE_SUPABASE_ANON_KEY: import.meta.env.VITE_SUPABASE_ANON_KEY ? 'Present' : 'Missing',
    };
    setEnvVars(vars);

    // Test Supabase connection
    const testSupabase = async () => {
      try {
        const { data, error } = await supabase.from('teams').select('count').limit(1);
        if (error) {
          setSupabaseStatus(`Error: ${error.message}`);
        } else {
          setSupabaseStatus('✅ Supabase connection successful');
        }
      } catch (err) {
        setSupabaseStatus(`Exception: ${err}`);
      }
    };

    testSupabase();
  }, []);

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>Test Page</h1>
      <p>If you can see this, the app is loading correctly.</p>

      <div style={{ marginTop: '40px' }}>
        <h2>Rotation Algorithm Debugger</h2>
        <RotationDebugger />
      </div>

      <div style={{ marginTop: '40px' }}>
        <h2>Enhanced Debug Test</h2>
        <RotationDebuggerEnhanced />
      </div>

      <div style={{ marginTop: '40px' }}>
        <h2>Lineup Generation Comparison</h2>
        <LineupGenerationTester />
      </div>

      <div style={{ marginTop: '20px' }}>
        <h3>Environment Variables:</h3>
        <pre style={{ background: '#f5f5f5', padding: '10px', fontSize: '12px' }}>
          {JSON.stringify(envVars, null, 2)}
        </pre>
      </div>

      <div style={{ marginTop: '20px' }}>
        <h3>Supabase Status:</h3>
        <p>{supabaseStatus}</p>
      </div>
    </div>
  );
};

export default TestPage;
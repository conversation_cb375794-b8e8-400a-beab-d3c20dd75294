import { Link, useLocation } from "react-router-dom";
import { useEffect, useState, useRef } from "react";
import { useTeam } from "@/contexts/TeamContext";
import { useAuth } from "@/contexts/AuthContext";
// import { useMockAuth as useAuth } from "@/contexts/MockAuthContext";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import MenuCard, { MenuLink } from "@/components/MenuCard";
import LineupTable from "@/components/LineupTable";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { DashboardSkeleton } from "@/components/LoadingSkeletons";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";
import {
  Users,
  ClipboardList,
  CalendarDays,
  Settings,
  HelpCircle,
  Circle,
  ChevronDown,
  PlusCircle,
  ShieldCheck,
  User
} from "lucide-react";
import { toast } from "sonner";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { supabase } from "@/integrations/supabase/client";

const Dashboard = () => {
  const { teamName, teams, currentTeamId, setCurrentTeamId, refreshTeamData, loading } = useTeam();
  const { user, isPaid, checkPaymentStatus } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const location = useLocation();
  const hasRefreshed = useRef(false);
  const isMobile = useIsMobile();

  // Remove redundant payment check - ProtectedRoute already handles this
  // The double-check was causing issues where users could navigate back but not reload

  // Check if user is an admin
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!user) return;

      // Check if this is a demo user - skip admin check for demo users
      const isDemoUser = user.email?.includes('demo') ||
                        user.email?.includes('baseball_demo') ||
                        localStorage.getItem('demo_mode') === 'true';

      if (isDemoUser) {
        setIsAdmin(false);
        return;
      }

      // Special case for Noah's account
      if (user.email === '<EMAIL>') {
        setIsAdmin(true);
        return;
      }

      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('is_admin')
          .eq('id', user.id)
          .single();

        if (error) {
          // Check if the error is due to the table not existing
          if (error.code === '42P01') { // 42P01 is "undefined_table" in PostgreSQL
            console.warn("Admin status check: 'profiles' table does not exist. Assuming not admin.");
            setIsAdmin(false);
          } else {
            console.error("Error checking admin status:", error);
            setIsAdmin(false);
          }
        } else {
          setIsAdmin(data?.is_admin || false);
        }
      } catch (catchError) { // Renamed to avoid conflict with 'error' from Supabase
        console.error("Error in checkAdminStatus try-catch block:", catchError);
        setIsAdmin(false);
      }
    };

    checkAdminStatus();
  }, [user]);

  // Only refresh team data if we don't have any teams loaded
  useEffect(() => {
    // Only refresh if we have a user but no teams loaded and we're not already loading
    if (user && teams.length === 0 && !hasRefreshed.current && !loading) {
      console.log("Dashboard: No teams loaded, refreshing team data");
      hasRefreshed.current = true;
      refreshTeamData();
    }
  }, [user, teams.length, refreshTeamData, loading]);

  // Reset refresh flag when leaving dashboard
  useEffect(() => {
    return () => {
      hasRefreshed.current = false;
    };
  }, []);

  // Special handling for demo users
  useEffect(() => {
    // Check if this is a demo user using the proper utility function
    const isDemoUser = user?.email === '<EMAIL>' ||
                      user?.email?.includes('baseball_demo');

    if (isDemoUser) {
      // Set a minimal flag to indicate this is a demo account
      localStorage.setItem('demo_mode', 'true');
      console.log("Demo account detected on dashboard");

      // All data is now stored in the database
    } else if (user) {
      // For non-demo users, ensure demo mode flag is cleared
      localStorage.removeItem('demo_mode');
      console.log("Non-demo account detected, clearing demo mode flag");

      // Clear any problematic session storage data that might reference non-existent lineups
      // Also clear any location state that might persist series metadata
      try {
        const keysToRemove = [];
        for (let i = 0; i < sessionStorage.length; i++) {
          const key = sessionStorage.key(i);
          if (key && (key.startsWith('lineup_') || key.includes('series') || key.includes('location'))) {
            keysToRemove.push(key);
          }
        }
        keysToRemove.forEach(key => {
          console.log("Dashboard: Clearing session storage key:", key);
          sessionStorage.removeItem(key);
        });
        
        // Also clear any localStorage that might contain series data
        const localKeysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (key.includes('series') || key.includes('lineup_state'))) {
            localKeysToRemove.push(key);
          }
        }
        localKeysToRemove.forEach(key => {
          console.log("Dashboard: Clearing localStorage key:", key);
          localStorage.removeItem(key);
        });
      } catch (e) {
        console.error("Dashboard: Error clearing storage:", e);
      }
    }
  }, [user]);

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        {!isMobile && <Header />}
        <main className="flex-1">
          <DashboardSkeleton />
        </main>
        {!isMobile && <Footer />}
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      {!isMobile && <Header />}

      {/* Demo Mode Banner */}
      {localStorage.getItem('demo_mode') === 'true' && (
        <div className="bg-blue-50 border-b border-blue-200 py-2">
          <div className="container mx-auto px-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-blue-700">
                <Circle className="h-4 w-4 fill-blue-500" />
                <p className="text-sm font-medium">Demo Mode</p>
                <p className="text-sm hidden md:inline">- Explore all features, changes visible during session but not permanently saved</p>
              </div>
              <Link to="/sign-in">
                <Button variant="outline" size="sm" className="text-xs h-7 border-blue-300 text-blue-700 hover:bg-blue-100">
                  Exit Demo
                </Button>
              </Link>
            </div>
          </div>
        </div>
      )}

      {!isMobile && (
        <div className="bg-baseball-lightblue/40 py-8">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row items-center justify-between">
              <div>
                <div className="flex items-center gap-3 mb-2">
                  <h1 className="text-3xl md:text-4xl font-bold text-baseball-navy">
                    {teamName} Lineup Manager
                  </h1>

                {/* Team Selector Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="h-8 gap-1 border-baseball-navy/30">
                      <span className="sr-only">Switch Team</span>
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    {teams.map(team => (
                      <DropdownMenuItem
                        key={team.id}
                        className={team.id === currentTeamId ? "bg-baseball-lightgreen/50 font-medium" : ""}
                        onClick={() => {
                          if (team.id !== currentTeamId) {
                            setCurrentTeamId(team.id);

                            toast.success(`Switched to ${team.name}`);
                          }
                        }}
                      >
                        {team.name}
                        {team.id === currentTeamId && (
                          <span className="ml-auto text-xs bg-baseball-green text-white px-1.5 py-0.5 rounded-full">
                            Current
                          </span>
                        )}
                      </DropdownMenuItem>
                    ))}
                    <DropdownMenuItem asChild>
                      <Link to="/team-management" className="cursor-pointer w-full">
                        <PlusCircle className="mr-2 h-4 w-4 text-baseball-green" />
                        <span>Manage Teams</span>
                      </Link>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              {!isMobile && (
                <p className="text-baseball-navy/70 text-lg">
                  Create perfect field rotations and batting orders for your team
                </p>
              )}
            </div>
            {!isMobile && (
              <div className="mt-4 md:mt-0 flex gap-3">
                <Link to="/create-lineup" state={null}>
                  <Button size="lg" variant="baseball" className="shadow-lg">
                    <ClipboardList className="mr-2 h-5 w-5" />
                    Create New Lineup
                  </Button>
                </Link>
                <Link to="/batch-games">
                  <Button size="lg" variant="baseballOutline">
                    <CalendarDays className="mr-2 h-5 w-5" />
                    Multiple Games
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
      )}

      {/* Mobile Team Selector */}
      {isMobile && (
        <div className="px-4 py-3 bg-white border-b">
          <div className="flex items-center justify-between">
            <h1 className="text-lg font-semibold text-baseball-navy truncate flex-1 mr-2">
              {teamName}
            </h1>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 px-2">
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                {teams.map(team => (
                  <DropdownMenuItem
                    key={team.id}
                    className={team.id === currentTeamId ? "bg-baseball-lightgreen/50 font-medium" : ""}
                    onClick={() => {
                      if (team.id !== currentTeamId) {
                        setCurrentTeamId(team.id);
                        toast.success(`Switched to ${team.name}`);
                      }
                    }}
                  >
                    <span className="truncate">{team.name}</span>
                  </DropdownMenuItem>
                ))}
                <DropdownMenuItem asChild>
                  <Link to="/team-management" className="cursor-pointer">
                    <PlusCircle className="mr-2 h-4 w-4" />
                    <span>Manage Teams</span>
                  </Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      )}

      <main className="flex-1 container mx-auto px-4 py-4 md:py-8">
        {/* Quick Actions Bar - Mobile optimized */}
        {!isMobile && (
          <div className="bg-gradient-to-r from-baseball-lightgreen/20 to-baseball-lightgreen/10 border border-baseball-green/20 rounded-xl p-6 mb-6 lg:mb-8">
            <h3 className="text-sm font-semibold text-baseball-navy/70 uppercase tracking-wide mb-4 text-center">Quick Actions</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
            {/* Primary Action - Most prominent */}
            <Link to="/create-lineup" className="sm:col-span-2 lg:col-span-1">
              <Button size="lg" variant="baseball" className="w-full group shadow-md hover:shadow-lg">
                <ClipboardList className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                Create Lineup
              </Button>
            </Link>
            
            {/* Secondary Actions */}
            <Link to="/batch-games">
              <Button size="lg" variant="baseballOutline" className="w-full">
                <CalendarDays className="mr-2 h-5 w-5" />
                Multiple Games
              </Button>
            </Link>
            
            {/* Tertiary Actions */}
            <Link to="/team-roster">
              <Button variant="outline" size="lg" className="w-full text-baseball-navy border-baseball-navy/30 hover:bg-baseball-navy/5">
                <Users className="mr-2 h-5 w-5" />
                Roster
              </Button>
            </Link>
            
            <Link to="/rotation-rules">
              <Button variant="outline" size="lg" className="w-full text-baseball-navy border-baseball-navy/30 hover:bg-baseball-navy/5">
                <Settings className="mr-2 h-5 w-5" />
                Philosophy
              </Button>
            </Link>
          </div>
        </div>
        )}

        {/* Mobile Quick Actions */}
        {isMobile && (
          <div className="mb-6 space-y-3">
            <h3 className="text-sm font-semibold text-baseball-navy/70 uppercase tracking-wide text-center">Quick Actions</h3>
            <div className="grid grid-cols-2 gap-2">
              <Link to="/create-lineup">
                <Button size="default" variant="baseball" className="w-full h-auto py-3">
                  <ClipboardList className="mr-1 h-4 w-4" />
                  <span className="text-sm">Lineup</span>
                </Button>
              </Link>
              <Link to="/batch-games">
                <Button size="default" variant="baseballOutline" className="w-full h-auto py-3">
                  <CalendarDays className="mr-1 h-4 w-4" />
                  <span className="text-sm">Batch</span>
                </Button>
              </Link>
            </div>
          </div>
        )}

        {/* Mobile Layout */}
        {isMobile ? (
          <div className="space-y-2">
            {/* Team Selector for Mobile */}
            <div className="mb-3">
              <div className="flex items-center justify-between bg-white rounded-lg shadow-sm p-3">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-gray-600">Team:</span>
                  <span className="text-sm font-semibold text-baseball-navy truncate max-w-[150px]">{teamName}</span>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="h-7 px-2">
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    {teams.map(team => (
                      <DropdownMenuItem
                        key={team.id}
                        className={team.id === currentTeamId ? "bg-baseball-lightgreen/50 font-medium" : ""}
                        onClick={() => {
                          if (team.id !== currentTeamId) {
                            setCurrentTeamId(team.id);
                            toast.success(`Switched to ${team.name}`);
                          }
                        }}
                      >
                        <span className="truncate">{team.name}</span>
                      </DropdownMenuItem>
                    ))}
                    <DropdownMenuItem asChild>
                      <Link to="/team-management" className="cursor-pointer">
                        <PlusCircle className="mr-2 h-4 w-4" />
                        <span>Manage Teams</span>
                      </Link>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            {/* Quick Actions - Compact */}
            <div className="grid grid-cols-2 gap-2 mb-3">
              <Link to="/create-lineup">
                <Button size="sm" variant="baseball" className="w-full h-10">
                  <ClipboardList className="mr-1 h-4 w-4" />
                  <span className="text-sm">New Lineup</span>
                </Button>
              </Link>
              <Link to="/batch-games">
                <Button size="sm" variant="baseballOutline" className="w-full h-10">
                  <CalendarDays className="mr-1 h-4 w-4" />
                  <span className="text-sm">Batch</span>
                </Button>
              </Link>
            </div>

            {/* Saved Lineups - Primary focus on mobile */}
            <Card className="border-t-2 border-baseball-green">
              <CardHeader className="bg-baseball-navy text-white py-2 px-3">
                <CardTitle className="flex items-center gap-2">
                  <ClipboardList size={16} />
                  <span className="text-sm">Your Lineups</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-2 px-2 pb-2">
                <LineupTable type="retrieve" />
              </CardContent>
            </Card>
          </div>
        ) : (
          /* Desktop Layout */
          <div className="grid lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8">
          {/* Left Column - Team Management */}
          <div className="space-y-6 lg:order-1">
            <Card className="border-t-4 border-baseball-green">
              <CardHeader className={cn("bg-baseball-navy text-white", isMobile && "py-3")}>
                <CardTitle className={cn("flex items-center gap-2", isMobile ? "text-base" : "text-lg")}>
                  <Users size={isMobile ? 18 : 20} />
                  <span className={isMobile ? "text-sm" : ""}>Team & Player Info</span>
                </CardTitle>
              </CardHeader>
              <CardContent className={cn("pt-4", isMobile && "py-3")}>
                <div className={cn("space-y-3", isMobile && "space-y-2")}>
                  <Link to="/team-roster" className={cn("block text-baseball-navy font-medium hover:underline", isMobile ? "text-sm py-1" : "text-sm sm:text-base")}>
                    {isMobile ? "Team Roster" : "Player roster & position lockouts"}
                  </Link>
                  <Link to="/rotation-rules" className={cn("block text-baseball-navy font-medium hover:underline", isMobile ? "text-sm py-1" : "text-sm sm:text-base")}>
                    {isMobile ? "Team Philosophy" : "Set your team philosophy"}
                  </Link>
                  <Link to="/team-management" className={cn("block text-baseball-navy font-medium hover:underline", isMobile ? "text-sm py-1" : "text-sm sm:text-base")}>
                    {isMobile ? "Manage Teams" : "Manage multiple teams"}
                  </Link>
                </div>
              </CardContent>
            </Card>

            <Card className="border-t-4 border-baseball-green">
              <CardHeader className={cn("bg-baseball-navy text-white", isMobile && "py-3")}>
                <CardTitle className={cn("flex items-center gap-2", isMobile ? "text-base" : "text-lg")}>
                  <Settings size={isMobile ? 18 : 20} />
                  <span className={isMobile ? "text-sm" : ""}>Settings</span>
                </CardTitle>
              </CardHeader>
              <CardContent className={cn("pt-4", isMobile && "py-3")}>
                <div className={cn("space-y-3", isMobile && "space-y-2")}>
                  <Link to="/user-profile" className={cn("block text-baseball-navy font-medium hover:underline", isMobile ? "text-sm py-1" : "")}>
                    {isMobile ? "Profile" : "Update your profile"}
                  </Link>
                  {/* FEATURE_FLAG assistantCoachAccess false */}
                  {/* <Link to="/assistant-coaches" className="block text-baseball-navy font-medium hover:underline">
                    Give access to assistant coaches
                  </Link> */}
                  <Link to="/faq" className={cn("block text-baseball-navy font-medium hover:underline", isMobile ? "text-sm py-1" : "")}>
                    Help & FAQ
                  </Link>
                  {isAdmin && (
                    <Link to="/admin" className="block text-baseball-navy font-medium hover:underline">
                      <div className="flex items-center gap-2">
                        <ShieldCheck size={16} className="text-purple-600" />
                        <span>Admin Dashboard</span>
                      </div>
                    </Link>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Lineups (Primary Focus) */}
          <div className="lg:col-span-2 lg:order-2">
            <Card className="border-t-4 border-baseball-green">
              <CardHeader className={cn("bg-baseball-navy text-white", isMobile && "py-3")}>
                <CardTitle className={cn("flex items-center gap-2", isMobile ? "text-base" : "text-lg sm:text-xl")}>
                  <ClipboardList size={isMobile ? 20 : 24} />
                  <span className={isMobile ? "text-sm" : ""}>Lineups</span>
                </CardTitle>
                {!isMobile && (
                  <CardDescription className="text-gray-300 text-sm sm:text-base">
                    Create new lineups and manage your saved lineups
                  </CardDescription>
                )}
              </CardHeader>
              <CardContent className={cn("pt-6", isMobile && "pt-3 px-3")}>
                <div className="space-y-6">
                  {/* Getting Started Guide - Hide on mobile */}
                  {!isMobile && (
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100/50 border border-blue-200 rounded-lg p-4">
                    <h4 className="font-bold text-baseball-navy mb-2 flex items-center gap-2">
                      <HelpCircle className="h-5 w-5 text-blue-600" />
                      Getting Started
                    </h4>
                    <p className="text-sm text-gray-700 mb-3">
                      New to Dugout Boss? Here's the recommended flow:
                    </p>
                    <ol className="text-sm space-y-2 text-gray-600">
                      <li className="flex items-start gap-2">
                        <span className="font-semibold text-blue-600 mt-0.5">1.</span>
                        <span>Choose your <Link to="/rotation-rules" className="font-medium text-blue-600 hover:underline">Team Philosophy</Link> (Competitive vs Recreation)</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="font-semibold text-blue-600 mt-0.5">2.</span>
                        <span>Set up your <Link to="/team-roster" className="font-medium text-blue-600 hover:underline">Team Roster</Link> with player positions</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="font-semibold text-blue-600 mt-0.5">3.</span>
                        <span>Configure <Link to="/rotation-rules" className="font-medium text-blue-600 hover:underline">Rotation Rules</Link> for fair playing time</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="font-semibold text-blue-600 mt-0.5">4.</span>
                        <span>Create your first lineup using the button above</span>
                      </li>
                    </ol>
                  </div>
                  )}

                  {/* Retrieve saved lineups section */}
                  <div className="space-y-2">
                    <h4 className="font-bold text-baseball-navy">View your saved lineups:</h4>
                    <p className="text-sm text-muted-foreground mb-3">
                      Access and manage your previous lineups.
                    </p>
                    <LineupTable type="retrieve" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
        )}
      </main>

      {!isMobile && <Footer />}
    </div>
  );
};

export default Dashboard;

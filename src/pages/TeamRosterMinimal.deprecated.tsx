import { useState, useEffect } from "react";
import { useTeam } from "@/contexts/TeamContext";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import TeamRoleManager from "@/components/TeamRoleManager";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { generateId } from "@/lib/utils-enhanced";
import { Plus, Trash2, ChevronDown, ChevronUp } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

const TeamRosterMinimal = () => {
  const { teamName, players, setPlayers, updatePlayer } = useTeam();
  const [localPlayers, setLocalPlayers] = useState(players);
  const [showDetailedHelp, setShowDetailedHelp] = useState(false);

  useEffect(() => {
    setLocalPlayers(players);
  }, [players]);

  const handleAddPlayer = () => {
    const newPlayer = {
      id: generateId(),
      name: "",
      teamRoles: {},
      isStarPlayer: false
    };
    setLocalPlayers([...localPlayers, newPlayer]);
  };

  const handleSave = async () => {
    try {
      const validPlayers = localPlayers.filter(p => p.name.trim() !== "");
      setPlayers(validPlayers);
      
      for (const player of validPlayers) {
        await updatePlayer(player);
      }
      
      toast.success("Roster saved!");
    } catch (error) {
      toast.error("Failed to save roster");
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header title="Team Roster" showBack backLink="/dashboard" />
      
      <main className="flex-1 container mx-auto px-4 py-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold mb-4">Team: {teamName}</h2>
          
          {/* Coach instructions and position guide */}
          <div className="space-y-3 mb-4">
            <div className="bg-blue-50 p-4 rounded-md">
              <h3 className="font-semibold text-base mb-2">Set Your Team's Positions</h3>
              <p className="text-sm text-gray-700 mb-3">
                Tell us who can play each position. Our smart rotation system will use this to create fair lineups 
                that keep players in positions they know while ensuring everyone gets equal playing time.
              </p>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-1 text-sm">
                <div>🟢 <span className="font-semibold">Primary</span> = Your best player here</div>
                <div>🔵 <span className="font-semibold">In the Mix</span> = Plays here regularly</div>
                <div>🟡 <span className="font-semibold">Emergency</span> = Can fill in if needed</div>
                <div>🔴 <span className="font-semibold">Never</span> = Don't put them here</div>
              </div>
            </div>
            
            <div className="bg-amber-50 p-3 rounded-md text-sm">
              <p className="text-gray-700">
                <span className="font-semibold">💡 Quick Start:</span> Focus on pitcher and catcher first. 
                Most players should be "In the Mix" for 3-5 positions. This gives the system flexibility 
                to rotate players fairly while keeping them comfortable.
              </p>
            </div>
          </div>

          {/* Player list - main action area */}
          <div className="space-y-3 mb-6">
            {localPlayers.length === 0 && (
              <p className="text-gray-500 text-center py-8">
                No players yet. Click "Add Player" to start building your roster.
              </p>
            )}
            {localPlayers.map((player, index) => (
              <div key={player.id} className={`flex flex-col sm:flex-row gap-3 sm:gap-4 sm:items-start p-3 rounded-md ${
                index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
              }`}>
                <div className="sm:w-20 sm:pt-2 text-sm font-medium">Player {index + 1}:</div>
                <div className="flex gap-2 flex-1">
                  <Input
                    value={player.name}
                    onChange={(e) => {
                      const updated = [...localPlayers];
                      updated[index] = { ...player, name: e.target.value };
                      setLocalPlayers(updated);
                    }}
                    placeholder="Enter name"
                    className="flex-1 sm:max-w-xs"
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setLocalPlayers(localPlayers.filter(p => p.id !== player.id));
                    }}
                    className="text-gray-500 hover:text-red-600 sm:hidden"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                {player.name.trim() && (
                  <div className="flex-1 w-full sm:w-auto">
                    <TeamRoleManager
                      player={player}
                      onRoleChange={(id, roles) => {
                        const updated = [...localPlayers];
                        const idx = updated.findIndex(p => p.id === id);
                        if (idx >= 0) {
                          updated[idx] = { ...updated[idx], teamRoles: roles };
                          setLocalPlayers(updated);
                        }
                      }}
                      compact={true}
                    />
                  </div>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setLocalPlayers(localPlayers.filter(p => p.id !== player.id));
                  }}
                  className="text-gray-500 hover:text-red-600 hidden sm:block"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>

          {/* Action buttons */}
          <div className="flex flex-col sm:flex-row gap-3 mb-6">
            <Button onClick={handleAddPlayer} variant="outline" className="w-full sm:w-auto">
              <Plus className="mr-2 h-4 w-4" />
              Add Player
            </Button>
            <Button onClick={handleSave} className="w-full sm:w-auto sm:ml-auto">
              Save Roster
            </Button>
          </div>

          {/* Detailed help - collapsible at bottom */}
          <Collapsible open={showDetailedHelp} onOpenChange={setShowDetailedHelp}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm" className="w-full text-gray-600">
                {showDetailedHelp ? 'Hide' : 'Show'} Detailed Help
                {showDetailedHelp ? <ChevronUp className="h-4 w-4 ml-2" /> : <ChevronDown className="h-4 w-4 ml-2" />}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <div className="mt-4 space-y-4 text-sm text-gray-700">
                <div className="bg-blue-50 p-4 rounded-md">
                  <h3 className="font-semibold mb-2">How Position Assignments Work</h3>
                  <p className="mb-2">
                    For each position on the field, tell us who can play there by assigning roles:
                  </p>
                  <ul className="space-y-1 ml-4">
                    <li>• <strong>Primary:</strong> Your go-to players for this position</li>
                    <li>• <strong>In the Mix:</strong> Regular players who rotate through</li>
                    <li>• <strong>Emergency:</strong> Only use when necessary</li>
                    <li>• <strong>Never:</strong> Cannot play this position</li>
                  </ul>
                </div>

                <div className="bg-green-50 p-4 rounded-md">
                  <h3 className="font-semibold mb-2">Tips for Success</h3>
                  <ul className="space-y-1 ml-4">
                    <li>• Start with pitcher and catcher assignments</li>
                    <li>• Most players should be "In the Mix" for multiple positions</li>
                    <li>• Use "Emergency" for positions they can cover but aren't ideal</li>
                    <li>• It's okay to leave positions blank - that means "Never"</li>
                  </ul>
                </div>

                <div className="bg-yellow-50 p-4 rounded-md">
                  <h3 className="font-semibold mb-2">Competitive Mode</h3>
                  <p>
                    When enabled, the lineup generator will prioritize putting Primary players 
                    in their best positions while still ensuring fair playing time for everyone.
                  </p>
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default TeamRosterMinimal;
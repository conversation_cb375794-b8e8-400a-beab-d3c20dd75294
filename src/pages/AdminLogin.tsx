import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/supabaseClient';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, <PERSON><PERSON>he<PERSON> } from 'lucide-react';
import { toast } from 'sonner';

const AdminLogin = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Clear any existing session
      await supabase.auth.signOut();
      
      // Clear any demo flags
      localStorage.removeItem('demo_user_is_paid');
      localStorage.removeItem('original_user_email');
      localStorage.removeItem('demo_mode');
      
      // Set admin flags
      localStorage.setItem('is_noah_real_account', 'true');
      localStorage.setItem('is_admin', 'true');
      
      console.log("Attempting to sign in as admin:", email);

      // Sign in with admin credentials
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error("Admin login failed:", error);
        toast.error(`Login failed: ${error.message}`);
        setIsLoading(false);
        return;
      }

      // Create or update profile with admin flag
      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: data.user.id,
          full_name: 'Noah Fleming',
          role: 'Admin',
          is_admin: true,
          updated_at: new Date().toISOString()
        });

      if (profileError) {
        console.error("Error updating profile:", profileError);
        toast.error(`Failed to update profile: ${profileError.message}`);
      }

      // Create or update subscription
      const { error: subscriptionError } = await supabase
        .from('subscriptions')
        .upsert({
          user_id: data.user.id,
          is_paid: true,
          amount: 4900,
          currency: 'usd',
          payment_date: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (subscriptionError) {
        console.error("Error updating subscription:", subscriptionError);
        toast.error(`Failed to update subscription: ${subscriptionError.message}`);
      }

      toast.success("Admin login successful!");
      
      // Wait a moment for auth context to update
      setTimeout(() => {
        navigate('/admin');
      }, 1000);
    } catch (error) {
      console.error("Error in admin login:", error);
      toast.error(`An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`);
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-baseball-navy">
      <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-lg">
        <div className="text-center">
          <ShieldCheck className="mx-auto h-16 w-16 text-purple-600" />
          <h1 className="text-2xl font-bold text-baseball-navy mt-4">Admin Login</h1>
          <p className="mt-2 text-gray-600">
            Sign in with your admin credentials to access the admin panel
          </p>
        </div>

        <form onSubmit={handleLogin} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="••••••••"
              required
            />
          </div>

          <Button
            type="submit"
            className="w-full bg-baseball-navy hover:bg-baseball-navy/90"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Signing in...
              </>
            ) : (
              <>
                <ShieldCheck className="mr-2 h-4 w-4" />
                Sign In as Admin
              </>
            )}
          </Button>
        </form>

        <div className="mt-4 text-center">
          <Button
            variant="link"
            onClick={() => navigate('/')}
            className="text-baseball-navy"
          >
            Return to Home
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AdminLogin;

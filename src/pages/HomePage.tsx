import { Link, useNavigate } from "react-router-dom";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ClipboardList, Users, Award, Check, BarChart3, LogIn, UserPlus, Trophy, Target, Settings, Zap } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import AuthModal from "@/components/AuthModal";
import DemoModeButton from "@/components/DemoModeButton";
import { trackConversion, trackEvent } from "@/utils/analytics";

const HomePage = () => {
  const { user, isPaid } = useAuth();
  const navigate = useNavigate();
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [authModalMode, setAuthModalMode] = useState<"login" | "signup">("login");

  // Test credentials for demo purposes
  const testCredentials = {
    email: "<EMAIL>",
    password: "demo1234"
  };

  const handleGetStarted = () => {
    // Track CTA engagement
    trackEvent('cta_clicked', {
      event_category: 'engagement',
      event_label: 'get_started_hero',
      cta_location: 'hero_section'
    });

    if (user) {
      // User is logged in
      if (isPaid) {
        // Paid user goes to dashboard
        navigate("/dashboard");
      } else {
        // Unpaid user goes to pricing
        trackEvent('pricing_page_visit', {
          event_category: 'navigation',
          event_label: 'existing_user',
          user_type: 'registered_unpaid'
        });
        navigate("/pricing");
      }
    } else {
      // Not logged in, redirect to pricing page for signup
      trackEvent('signup_redirect_pricing', {
        event_category: 'engagement',
        event_label: 'signup_intent',
        trigger_location: 'hero_cta'
      });
      navigate("/pricing");
    }
  };

  const handleSignIn = () => {
    // Track sign in intent
    trackEvent('signin_modal_opened', {
      event_category: 'engagement',
      event_label: 'signin_intent',
      trigger_location: 'homepage'
    });
    setAuthModalMode("login");
    setAuthModalOpen(true);
  };

  const handleShowTestCredentials = () => {
    const testCredentialsMessage = (
      <div className="space-y-2">
        <p className="font-bold">Test Credentials</p>
        <p><span className="font-semibold">Email:</span> {testCredentials.email}</p>
        <p><span className="font-semibold">Password:</span> {testCredentials.password}</p>
        <p className="italic text-xs mt-2">Note: You may need to disable email confirmation in the Supabase dashboard.</p>
      </div>
    );
    
    toast.info(testCredentialsMessage, { duration: 10000 });
  };

  return (
    <div className="min-h-screen flex flex-col">
      {/* Navigation */}
      <header className="bg-baseball-navy text-white py-4">
        <div className="container mx-auto px-4 flex justify-between items-center">
          <div className="flex items-center">
            <h1 className="font-bold text-xl">Dugout Boss</h1>
          </div>
          <nav className="hidden md:flex space-x-6" aria-label="Main navigation">
            <a href="#features" className="hover:text-baseball-lightgreen" title="Baseball lineup features">Features</a>
            <a href="#benefits" className="hover:text-baseball-lightgreen" title="Benefits for coaches">Benefits</a>
            <Link to="/pricing" className="hover:text-baseball-lightgreen" title="Pricing plans">Pricing</Link>
            <Link to="/faq" className="hover:text-baseball-lightgreen" title="Frequently asked questions">FAQ</Link>
          </nav>
          <div className="flex space-x-2">
            {user ? (
              <Link to="/dashboard">
                <Button 
                  variant="outline"
                  className="bg-white text-baseball-navy hover:bg-gray-100 border-white"
                >
                  Dashboard
                </Button>
              </Link>
            ) : (
              <>
                <Link to="/pricing">
                  <Button
                    variant="baseballOutline"
                    className="bg-baseball-green text-white hover:bg-baseball-green/90 border-baseball-green"
                  >
                    <UserPlus className="mr-1" size={18} />
                    Join Now
                  </Button>
                </Link>
                <Link to="/sign-in">
                  <Button
                    variant="outline"
                    className="bg-white text-baseball-navy hover:bg-gray-100 border-white"
                  >
                    <LogIn className="mr-1" size={18} />
                    Sign In
                  </Button>
                </Link>
              </>
            )}
          </div>
        </div>
      </header>

      {/* Mobile Sign In Banner - Only visible on small screens */}
      {!user && (
        <div className="md:hidden bg-baseball-green text-white p-4 text-center space-y-2">
          <Link to="/pricing">
            <Button
              variant="outline"
              className="border-white text-white hover:bg-white/20 w-full mb-2"
            >
              <UserPlus className="mr-2" size={18} />
              Get Started
            </Button>
          </Link>
          <Link to="/sign-in">
            <Button
              variant="outline"
              className="border-white text-white hover:bg-white/20 w-full"
            >
              <LogIn className="mr-2" size={18} />
              Sign In
            </Button>
          </Link>
        </div>
      )}

      {/* Hero Section */}
      <main id="main-content">
        <section className="hero bg-gradient-to-br from-baseball-navy to-baseball-navy/90 text-white py-20">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row items-center">
              <div className="md:w-1/2 md:pr-12">
                <h1 className="text-4xl md:text-5xl font-bold mb-6">
                  Baseball Lineup Generator - Create Perfect Lineups in Minutes
                </h1>
                <p className="text-xl mb-4">
                  The easiest way for coaches to build batting orders, manage field positions, and create professional lineup cards. Used by Little League, travel ball, and youth baseball teams nationwide.
                </p>
              <div className="flex flex-wrap gap-3 mb-6">
                <div className="flex items-center bg-white/10 rounded-full px-4 py-2">
                  <Users className="w-5 h-5 mr-2" />
                  <span className="text-sm font-medium">Recreational Teams</span>
                </div>
                <div className="flex items-center bg-white/10 rounded-full px-4 py-2">
                  <Trophy className="w-5 h-5 mr-2" />
                  <span className="text-sm font-medium">Competitive Teams</span>
                </div>
                <div className="flex items-center bg-white/10 rounded-full px-4 py-2">
                  <Target className="w-5 h-5 mr-2" />
                  <span className="text-sm font-medium">All Skill Levels</span>
                </div>
              </div>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  size="lg"
                  variant="baseball"
                  onClick={handleGetStarted}
                  className="cta-primary"
                >
                  Create Your First Lineup
                </Button>
                {!user && (
                  <>
                    <Button
                      size="lg"
                      variant="outline"
                      onClick={handleSignIn}
                      className="bg-white/10 border-2 border-white text-white hover:bg-white hover:text-baseball-navy"
                    >
                      <LogIn className="mr-2" size={20} />
                      Sign In
                    </Button>
                    <DemoModeButton size="lg" className="bg-white/10 border-2 border-white text-white hover:bg-white hover:text-baseball-navy" />
                  </>
                )}
              </div>
            </div>
            <div className="md:w-1/2 mt-10 md:mt-0">
              <div className="bg-white p-6 rounded-lg shadow-xl">
                <img
                  src="https://images.unsplash.com/photo-1508802449768-a389d1acbfcd?auto=format&fit=crop&q=80&w=1920&h=1080"
                  alt="Baseball lineup generator showing batting order and field positions - Dugout Boss interface"
                  className="w-full rounded"
                  loading="lazy"
                  width="800"
                  height="450"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = "https://images.unsplash.com/photo-1490326149782-dd42fa59bd9f?auto=format&fit=crop&q=80&w=1920&h=1080";
                    target.alt = "Youth baseball lineup management";
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </section>

        {/* Features Section */}
        <section id="features" className="features py-20 bg-gray-50" aria-label="Key Features">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-baseball-navy mb-4">Why Coaches Choose Dugout Boss</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Professional-grade algorithms that adapt to your team's needs - from fair play to strategic optimization
              </p>
            </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="border-t-4 border-baseball-green">
              <CardContent className="pt-6">
                <div className="flex items-center justify-center mb-4 h-16 w-16 rounded-full bg-baseball-lightgreen/30 text-baseball-green mx-auto">
                  <Zap size={28} />
                </div>
                <h3 className="text-lg font-bold text-center mb-3 text-baseball-navy">Lightning Fast Setup</h3>
                <p className="text-gray-600 text-center text-sm">
                  Build complete lineups in under 60 seconds. No complex setup or training required.
                </p>
              </CardContent>
            </Card>

            <Card className="border-t-4 border-baseball-green">
              <CardContent className="pt-6">
                <div className="flex items-center justify-center mb-4 h-16 w-16 rounded-full bg-baseball-lightgreen/30 text-baseball-green mx-auto">
                  <Settings size={28} />
                </div>
                <h3 className="text-lg font-bold text-center mb-3 text-baseball-navy">Fair Playing Time</h3>
                <p className="text-gray-600 text-center text-sm">
                  Automatic rotation ensures every player gets equal field time across positions.
                </p>
              </CardContent>
            </Card>

            <Card className="border-t-4 border-baseball-green">
              <CardContent className="pt-6">
                <div className="flex items-center justify-center mb-4 h-16 w-16 rounded-full bg-baseball-lightgreen/30 text-baseball-green mx-auto">
                  <ClipboardList size={28} />
                </div>
                <h3 className="text-lg font-bold text-center mb-3 text-baseball-navy">Works Everywhere</h3>
                <p className="text-gray-600 text-center text-sm">
                  Create lineups at home, print them out, or use on your phone at the field. No internet required.
                </p>
              </CardContent>
            </Card>

            <Card className="border-t-4 border-baseball-green">
              <CardContent className="pt-6">
                <div className="flex items-center justify-center mb-4 h-16 w-16 rounded-full bg-baseball-lightgreen/30 text-baseball-green mx-auto">
                  <BarChart3 size={28} />
                </div>
                <h3 className="text-lg font-bold text-center mb-3 text-baseball-navy">Performance Analytics</h3>
                <p className="text-gray-600 text-center text-sm">
                  Track playing time, position history, and lineup effectiveness across the season
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Dual Mode Explanation */}
          <div className="mt-16 grid md:grid-cols-2 gap-8">
            <Card className="border-l-4 border-blue-500 bg-blue-50">
              <CardContent className="pt-6">
                <div className="flex items-center mb-4">
                  <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                    <Users className="h-6 w-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-blue-800">Recreational Mode</h3>
                </div>
                <ul className="space-y-2 text-blue-700">
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5 text-blue-600" />
                    <span className="text-sm">Equal playing time for all players</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5 text-blue-600" />
                    <span className="text-sm">Fair rotation through all positions</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5 text-blue-600" />
                    <span className="text-sm">Development-focused assignments</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5 text-blue-600" />
                    <span className="text-sm">Perfect for house leagues</span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-yellow-500 bg-yellow-50">
              <CardContent className="pt-6">
                <div className="flex items-center mb-4">
                  <div className="h-12 w-12 rounded-full bg-yellow-100 flex items-center justify-center mr-4">
                    <Trophy className="h-6 w-6 text-yellow-600" />
                  </div>
                  <h3 className="text-xl font-bold text-yellow-800">Competitive Mode</h3>
                </div>
                <ul className="space-y-2 text-yellow-700">
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5 text-yellow-600" />
                    <span className="text-sm">Optimal player positioning for winning</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5 text-yellow-600" />
                    <span className="text-sm">Star players in key positions longer</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5 text-yellow-600" />
                    <span className="text-sm">Strategic rotation timing</span>
                  </li>
                  <li className="flex items-start">
                    <Check className="h-5 w-5 mr-2 mt-0.5 text-yellow-600" />
                    <span className="text-sm">Perfect for travel teams</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
        </section>
        
        {/* How It Works Section */}
        <section className="how-it-works py-20 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold text-baseball-navy mb-4">How It Works</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Get started with professional lineups in just four simple steps
              </p>
            </div>
            <ol className="steps grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              <li className="text-center">
                <div className="flex items-center justify-center mb-4 h-16 w-16 rounded-full bg-baseball-green text-white mx-auto font-bold text-xl">
                  1
                </div>
                <h3 className="text-lg font-bold mb-3 text-baseball-navy">Add Players</h3>
                <p className="text-gray-600 text-sm">Enter your roster with positions and preferences</p>
              </li>
              <li className="text-center">
                <div className="flex items-center justify-center mb-4 h-16 w-16 rounded-full bg-baseball-green text-white mx-auto font-bold text-xl">
                  2
                </div>
                <h3 className="text-lg font-bold mb-3 text-baseball-navy">Set Rules</h3>
                <p className="text-gray-600 text-sm">Choose your league rules and rotation preferences</p>
              </li>
              <li className="text-center">
                <div className="flex items-center justify-center mb-4 h-16 w-16 rounded-full bg-baseball-green text-white mx-auto font-bold text-xl">
                  3
                </div>
                <h3 className="text-lg font-bold mb-3 text-baseball-navy">Generate Lineup</h3>
                <p className="text-gray-600 text-sm">Get a complete batting order and field positions instantly</p>
              </li>
              <li className="text-center">
                <div className="flex items-center justify-center mb-4 h-16 w-16 rounded-full bg-baseball-green text-white mx-auto font-bold text-xl">
                  4
                </div>
                <h3 className="text-lg font-bold mb-3 text-baseball-navy">Share & Print</h3>
                <p className="text-gray-600 text-sm">Export to PDF or share with parents and players</p>
              </li>
            </ol>
          </div>
        </section>
      </main>

      {/* Benefits Section */}
      <section id="benefits" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-baseball-navy mb-4">One App for Every Team Type</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From house league fairness to competitive strategy - Dugout Boss adapts to your coaching philosophy
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <div className="space-y-6">
                <div className="flex items-start">
                  <div className="flex-shrink-0 mt-1">
                    <Check className="h-6 w-6 text-baseball-green" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-bold text-baseball-navy">Adaptive Intelligence</h3>
                    <p className="text-gray-600">Switch between fair play and competitive optimization based on your team's goals</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0 mt-1">
                    <Check className="h-6 w-6 text-baseball-green" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-bold text-baseball-navy">Strategic Player Positioning</h3>
                    <p className="text-gray-600">Advanced preference system with rankings puts your best players in optimal positions</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0 mt-1">
                    <Check className="h-6 w-6 text-baseball-green" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-bold text-baseball-navy">Professional-Grade Algorithms</h3>
                    <p className="text-gray-600">Constraint-based lineup generation handles complex rules while optimizing performance</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0 mt-1">
                    <Check className="h-6 w-6 text-baseball-green" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-bold text-baseball-navy">Save Hours Every Week</h3>
                    <p className="text-gray-600">Create optimal lineups in minutes, not hours, with automated rotation and constraint handling</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0 mt-1">
                    <Check className="h-6 w-6 text-baseball-green" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-bold text-baseball-navy">Mobile-Ready for the Dugout</h3>
                    <p className="text-gray-600">Responsive design works perfectly on phones and tablets during games</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="rounded-lg overflow-hidden shadow-xl">
              <img
                src="https://images.unsplash.com/photo-1518974906971-fedc85ade525?auto=format&fit=crop&q=80&w=1920&h=1080"
                alt="Youth baseball player batting"
                className="w-full"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = "https://images.unsplash.com/photo-1513879392062-82c9a1d1c614?auto=format&fit=crop&q=80&w=1920&h=1080";
                  target.alt = "Youth baseball pitcher";
                }}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 bg-baseball-lightblue/20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-baseball-navy mb-4">What Coaches Say</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Hear from coaches who are saving time and creating better lineups
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card>
              <CardContent className="pt-6">
                <div className="mb-4 text-baseball-green flex justify-center">
                  <Award size={28} />
                  <Award size={28} />
                  <Award size={28} />
                  <Award size={28} />
                  <Award size={28} />
                </div>
                <p className="text-gray-600 italic mb-4">
                  "Competitive mode is a game-changer for our travel team. I can optimize lineups for winning while still ensuring everyone plays. The preference rankings are brilliant!"
                </p>
                <div className="font-bold text-baseball-navy">Coach Mike, U12 Travel Softball</div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="mb-4 text-baseball-green flex justify-center">
                  <Award size={28} />
                  <Award size={28} />
                  <Award size={28} />
                  <Award size={28} />
                  <Award size={28} />
                </div>
                <p className="text-gray-600 italic mb-4">
                  "For our house league, recreational mode is perfect. Parents love seeing the fair rotation data, and I love how easy it is to use on my phone during games."
                </p>
                <div className="font-bold text-baseball-navy">Coach Sarah, U10 House League</div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="mb-4 text-baseball-green flex justify-center">
                  <Award size={28} />
                  <Award size={28} />
                  <Award size={28} />
                  <Award size={28} />
                  <Award size={28} />
                </div>
                <p className="text-gray-600 italic mb-4">
                  "The advanced algorithms handle our complex league rules automatically. I can switch between fair play and competitive modes depending on the tournament."
                </p>
                <div className="font-bold text-baseball-navy">Coach James, U14 Select Baseball</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-baseball-navy text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to revolutionize your lineup strategy?</h2>
          <p className="text-xl mb-4 max-w-3xl mx-auto">
            Join coaches using professional-grade algorithms to optimize their lineups - whether for fair play or competitive advantage
          </p>
          <div className="flex justify-center gap-6 mb-8">
            <div className="flex items-center text-baseball-lightgreen">
              <Users className="w-5 h-5 mr-2" />
              <span className="text-sm">House League Fairness</span>
            </div>
            <div className="flex items-center text-baseball-lightgreen">
              <Trophy className="w-5 h-5 mr-2" />
              <span className="text-sm">Competitive Optimization</span>
            </div>
            <div className="flex items-center text-baseball-lightgreen">
              <Target className="w-5 h-5 mr-2" />
              <span className="text-sm">All Skill Levels</span>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button
              size="lg"
              variant="baseball"
              onClick={handleGetStarted}
            >
              Get Started Today
            </Button>
            <DemoModeButton size="lg" variant="baseball" className="bg-baseball-green hover:bg-baseball-green/90 text-white border-0" />
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <h3 className="font-bold text-xl mb-4">Dugout Boss</h3>
              <p className="text-gray-400">
                Strategic lineup management that adapts from recreational fairness to competitive optimization
              </p>
            </div>
            <div>
              <h3 className="font-bold mb-4">Features</h3>
              <ul className="space-y-2">
                <li><a href="#features" className="text-gray-400 hover:text-white">Dual-Mode Intelligence</a></li>
                <li><a href="#features" className="text-gray-400 hover:text-white">Smart Preferences</a></li>
                <li><a href="#features" className="text-gray-400 hover:text-white">Competitive Mode</a></li>
                <li><a href="#features" className="text-gray-400 hover:text-white">Performance Analytics</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-bold mb-4">Company</h3>
              <ul className="space-y-2">
                <li><Link to="/contact" className="text-gray-400 hover:text-white">Contact</Link></li>
                <li><Link to="/faq" className="text-gray-400 hover:text-white">FAQ</Link></li>
                <li><Link to="/privacy-policy" className="text-gray-400 hover:text-white">Privacy Policy</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-bold mb-4">Get Started</h3>
              <ul className="space-y-2">
                <li><Link to="/pricing" className="text-gray-400 hover:text-white">Pricing</Link></li>
                <li>
                  <Link to="/pricing" className="text-gray-400 hover:text-white flex items-center">
                    <UserPlus className="mr-1" size={16} />
                    Join Now
                  </Link>
                </li>
                <li>
                  <Link to="/sign-in" className="text-gray-400 hover:text-white flex items-center">
                    <LogIn className="mr-1" size={16} />
                    Sign In
                  </Link>
                </li>
                <li>
                  <Link to="/demo-login" className="text-gray-400 hover:text-white">
                    Demo Mode
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          {/* Sign Up/Sign In Buttons in Footer */}
          {!user && (
            <div className="mt-12 text-center space-y-4">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link to="/pricing">
                  <Button
                    variant="outline"
                    size="lg"
                    className="bg-baseball-green/5 border-baseball-green text-baseball-green hover:bg-baseball-green hover:text-white transition-colors"
                  >
                    <UserPlus className="mr-2" size={18} />
                    Get Started
                  </Button>
                </Link>
                <Link to="/sign-in">
                  <Button
                    variant="outline"
                    size="lg"
                    className="border-baseball-green text-baseball-green hover:bg-baseball-green/10"
                  >
                    <LogIn className="mr-2" size={18} />
                    Sign In
                  </Button>
                </Link>
              </div>
            </div>
          )}

          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; {new Date().getFullYear()} DugoutBoss.com. All rights reserved.</p>
          </div>
        </div>
      </footer>

      {/* Floating Join Button - Always visible */}
      {!user && (
        <div className="fixed bottom-4 right-4 z-40 flex flex-col gap-2">
          <Link to="/pricing">
            <Button
              size="lg"
              className="bg-baseball-green text-white hover:bg-baseball-green/90 shadow-lg rounded-full px-6"
            >
              <UserPlus className="mr-2" size={18} />
              Get Started
            </Button>
          </Link>
          <Link to="/sign-in">
            <Button
              size="sm"
              variant="outline"
              className="bg-white/90 text-baseball-navy hover:bg-white shadow-lg rounded-full px-4"
            >
              <LogIn className="mr-1" size={16} />
              Sign In
            </Button>
          </Link>
        </div>
      )}

      {/* Auth Modal */}
      <AuthModal
        isOpen={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
        defaultMode={authModalMode}
      />
    </div>
  );
};

export default HomePage;

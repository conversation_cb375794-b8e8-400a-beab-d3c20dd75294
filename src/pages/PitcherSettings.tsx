import { useState, useEffect, useMemo } from "react";
import { useTeam, PitcherStrategy } from "@/contexts/TeamContext";
import { useNavigate } from "react-router-dom";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import PitcherStrategyManager from "@/components/PitcherStrategyManager";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { ArrowLeft, Save, Zap, Target, Trophy, RotateCcw, Info } from "lucide-react";

const PitcherSettings = () => {
  const { teamName, players, updatePlayer, rotationRules } = useTeam();
  const navigate = useNavigate();
  const [hasChanges, setHasChanges] = useState(false);

  // Debug effect to track player changes
  useEffect(() => {
    console.log('PitcherSettings: players updated', {
      totalPlayers: players.length,
      playersWithPitcherRole: players.filter(p => p.teamRoles?.pitcher).length,
      eligiblePitchers: players.filter(p => p.teamRoles?.pitcher && p.teamRoles.pitcher !== 'avoid').length,
      pitcherStrategies: players.filter(p => p.teamRoles?.pitcher && p.teamRoles.pitcher !== 'avoid').map(p => ({
        name: p.name,
        strategy: p.pitcherStrategy
      }))
    });
  }, [players]);

  // Check if we have any eligible pitchers
  const eligiblePitchers = players.filter(player => {
    const pitcherRole = player.teamRoles?.pitcher;
    return pitcherRole && pitcherRole !== 'avoid';
  });

  const handleStrategyChange = async (playerId: string, strategy: PitcherStrategy) => {
    console.log(`PitcherSettings: Updating pitcher strategy for ${playerId}:`, strategy);
    
    const player = players.find(p => p.id === playerId);
    if (!player) {
      console.error(`PitcherSettings: Player not found for ID ${playerId}`);
      return;
    }

    // Create a deep copy to ensure we're not mutating the original
    const updatedPlayer = { 
      ...player,
      teamRoles: { ...player.teamRoles },
      pitcherStrategy: { ...strategy }
    };

    try {
      console.log(`PitcherSettings: Calling updatePlayer for ${player.name} with data:`, {
        name: updatedPlayer.name,
        pitcherStrategy: updatedPlayer.pitcherStrategy,
        teamRoles: updatedPlayer.teamRoles
      });
      
      await updatePlayer(updatedPlayer);
      setHasChanges(true);
      
      console.log(`PitcherSettings: Successfully updated ${player.name} with strategy:`, strategy);
      toast.success(`Updated pitcher strategy for ${player.name}`, {
        description: `Role: ${strategy.role}, Priority: ${strategy.priority}, Max innings: ${strategy.maxInningsPerGame || 2}`
      });
    } catch (error) {
      console.error("PitcherSettings: Error updating pitcher strategy:", error);
      toast.error("Failed to save pitcher strategy");
    }
  };

  const handleSaveAll = async () => {
    try {
      // Save all players with updated strategies
      const playersToUpdate = eligiblePitchers.filter(player => player.pitcherStrategy);
      
      if (playersToUpdate.length === 0) {
        toast.info("No pitcher strategies to save");
        return;
      }

      // Update all players in parallel
      await Promise.all(
        playersToUpdate.map(player => updatePlayer(player))
      );
      
      setHasChanges(false);
      toast.success(`Saved ${playersToUpdate.length} pitcher strategies successfully!`);
    } catch (error) {
      console.error("Error saving all pitcher strategies:", error);
      toast.error("Failed to save some pitcher strategies");
    }
  };

  // Count pitchers by role for display
  const pitchersByRole = useMemo(() => {
    const result = {
      starter: eligiblePitchers.filter(p => p.pitcherStrategy?.role === 'starter').length,
      reliever: eligiblePitchers.filter(p => p.pitcherStrategy?.role === 'reliever').length,
      closer: eligiblePitchers.filter(p => p.pitcherStrategy?.role === 'closer').length,
      any: eligiblePitchers.filter(p => !p.pitcherStrategy?.role || p.pitcherStrategy?.role === 'any').length
    };
    
    console.log('PitcherSettings: pitchersByRole calculated', result);
    return result;
  }, [eligiblePitchers]);

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'starter': return <Zap className="w-4 h-4" />;
      case 'reliever': return <Target className="w-4 h-4" />;
      case 'closer': return <Trophy className="w-4 h-4" />;
      default: return <RotateCcw className="w-4 h-4" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'starter': return 'bg-blue-500';
      case 'reliever': return 'bg-green-500'; 
      case 'closer': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  if (eligiblePitchers.length === 0) {
    return (
      <div className="min-h-screen flex flex-col bg-baseball-tan/20">
        <Header title="Pitcher Strategy Settings" showBack backLink="/team-roster" />
        <main className="flex-1 container mx-auto px-4 py-6 sm:py-8 max-w-4xl">
          <Card className="border-yellow-200 bg-yellow-50">
            <CardContent className="pt-6">
              <div className="text-center">
                <Info className="w-12 h-12 text-yellow-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-yellow-800 mb-2">No Eligible Pitchers Found</h3>
                <p className="text-yellow-700 mb-4">
                  You need to set at least one player's pitcher role to "Primary", "In the Mix", or "Emergency" 
                  before you can configure pitcher strategies.
                </p>
                <Button onClick={() => navigate("/team-roster")} className="bg-yellow-600 hover:bg-yellow-700">
                  Go to Team Roster to Set Pitcher Roles
                </Button>
              </div>
            </CardContent>
          </Card>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-baseball-tan/20">
      <Header title="Advanced Pitcher Strategy" showBack backLink="/team-roster" />

      <main className="flex-1 container mx-auto px-4 py-6 sm:py-8 max-w-6xl">
        {/* Team Overview */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold text-baseball-navy">{teamName} - Pitcher Management</h1>
              <p className="text-gray-600">
                Configure advanced pitcher roles, innings limits, and rotation strategy for competitive play.
              </p>
            </div>
            {hasChanges && (
              <Button onClick={handleSaveAll} className="bg-green-600 hover:bg-green-700">
                <Save className="w-4 h-4 mr-2" />
                Save All Changes
              </Button>
            )}
          </div>

          {/* Role Summary */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            {Object.entries(pitchersByRole).map(([role, count]) => (
              <Card key={role} className="text-center">
                <CardContent className="pt-4 pb-4">
                  <div className="flex items-center justify-center mb-2">
                    <div className={`p-2 rounded-full ${getRoleColor(role)} text-white`}>
                      {getRoleIcon(role)}
                    </div>
                  </div>
                  <div className="text-2xl font-bold">{count}</div>
                  <div className="text-sm text-gray-600 capitalize">
                    {role === 'any' ? 'Flexible' : role}
                    {count !== 1 && 's'}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Pitcher Strategy Manager */}
        <PitcherStrategyManager
          key={players.map(p => `${p.id}-${p.pitcherStrategy?.role || 'none'}`).join(',')}
          players={players}
          onStrategyChange={handleStrategyChange}
          gameLength={7} // Default game length
          seriesLength={1} // Will be updated for series
          disabled={false}
        />

        {/* Info Panel */}
        <Card className="mt-6 border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-800">
              <Info className="w-5 h-5" />
              How This Affects Lineup Generation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm text-blue-700">
              <div>
                <strong>Competitive Mode:</strong> Pitcher strategies are primarily used in competitive mode 
                where optimal positioning is prioritized.
              </div>
              <div>
                <strong>Role Priority:</strong> The algorithm will try to assign pitchers to their preferred 
                innings based on their role and priority ranking.
              </div>
              <div>
                <strong>Innings Limits:</strong> Players won't exceed their maximum innings per game, 
                ensuring fair distribution and preventing overuse.
              </div>
              <div>
                <strong>Series Play:</strong> For multi-game series, the algorithm tracks innings across 
                all games to respect series-wide limits.
              </div>
              <div>
                <strong>Smart Fallbacks:</strong> If preferred pitchers aren't available, the algorithm 
                will intelligently choose backup options while respecting team roles.
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="mt-8 flex justify-between">
          <Button 
            variant="outline" 
            onClick={() => navigate("/team-roster")}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Team Roster
          </Button>
          
          <div className="flex gap-3">
            <Button 
              variant="outline"
              onClick={() => navigate("/rotation-rules")}
            >
              Rotation Settings
            </Button>
            <Button 
              onClick={() => navigate("/create-lineup")}
              className="bg-baseball-green hover:bg-baseball-green/90"
            >
              Test Lineup Generation
            </Button>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default PitcherSettings;
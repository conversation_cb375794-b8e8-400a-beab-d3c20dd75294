import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { useTeam } from '@/contexts/TeamContext';

const TestLogin = () => {
  const navigate = useNavigate();
  const [status, setStatus] = useState('');
  const { signIn, user } = useAuth();
  const { teams, currentTeamId, currentTeam } = useTeam();

  const testAdminLogin = async () => {
    try {
      setStatus('Logging in as test admin...');

      // Use environment variables for test credentials
      const testEmail = import.meta.env.VITE_TEST_ADMIN_EMAIL || '<EMAIL>';
      const testPassword = import.meta.env.VITE_TEST_ADMIN_PASSWORD || 'testpass123';

      const { error } = await signIn(testEmail, testPassword);

      if (error) {
        setStatus(`Login failed: ${error.message}`);
        return;
      }

      setStatus('Login successful! Redirecting...');
      setTimeout(() => {
        navigate('/dashboard');
      }, 1000);

    } catch (error) {
      setStatus(`Error: ${error}`);
    }
  };

  const testDemoMode = () => {
    // Clear everything and set up demo mode
    localStorage.clear();
    sessionStorage.clear();

    // Set demo flags
    localStorage.setItem('demo_mode', 'true');
    localStorage.setItem('demo_user_email', '<EMAIL>');
    localStorage.setItem('demo_user_id', '212e5e06-9dd0-4d56-89d2-69915b205b53');
    localStorage.setItem('current_team_id', '83bd9832-f5db-4c7d-b234-41fd38f90007');
    localStorage.setItem('demo_user_is_paid', 'true');

    setStatus('Demo mode set up. Redirecting...');
    setTimeout(() => {
      window.location.href = '/dashboard';
    }, 1000);
  };

  const clearEverything = () => {
    localStorage.clear();
    sessionStorage.clear();
    setStatus('Cleared all storage');
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Test Login</h1>

      <div className="space-y-4">
        <Button onClick={testAdminLogin} className="mr-4">
          Login as Test Admin
        </Button>

        <Button onClick={testDemoMode} className="mr-4">
          Set Up Demo Mode
        </Button>

        <Button onClick={clearEverything} variant="outline">
          Clear All Storage
        </Button>
      </div>

      <div className="mt-4 p-4 bg-gray-100 rounded">
        <p><strong>Status:</strong> {status}</p>
      </div>

      <div className="mt-4 p-4 bg-blue-50 rounded">
        <h3 className="font-bold">Current Storage:</h3>
        <p>Demo Mode: {localStorage.getItem('demo_mode')}</p>
        <p>Demo User Email: {localStorage.getItem('demo_user_email')}</p>
        <p>Current Team ID: {localStorage.getItem('current_team_id')}</p>
        <p>Demo User Paid: {localStorage.getItem('demo_user_is_paid')}</p>
      </div>

      <div className="mt-4 p-4 bg-green-50 rounded">
        <h3 className="font-bold">Current Auth & Team State:</h3>
        <p>User: {user?.email || 'None'}</p>
        <p>User ID: {user?.id || 'None'}</p>
        <p>Teams Count: {teams?.length || 0}</p>
        <p>Current Team ID: {currentTeamId || 'None'}</p>
        <p>Current Team Name: {currentTeam?.name || 'None'}</p>
        <p>Current Team Players: {currentTeam?.players?.length || 0}</p>

        {teams && teams.length > 0 && (
          <div className="mt-2">
            <h4 className="font-semibold">Teams:</h4>
            <ul className="list-disc list-inside">
              {teams.slice(0, 5).map((team: any) => (
                <li key={team.id}>{team.name} - {team.players?.length || 0} players</li>
              ))}
              {teams.length > 5 && <li>... and {teams.length - 5} more</li>}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default TestLogin;

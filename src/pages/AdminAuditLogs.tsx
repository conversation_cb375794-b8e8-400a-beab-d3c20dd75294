import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Search, 
  Shield, 
  Eye,
  Loader2,
  Calendar,
  Filter,
  Download
} from "lucide-react";
import AdminLayout from "@/components/AdminLayout";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";

interface AuditLog {
  id: string;
  adminId: string;
  adminEmail: string;
  action: string;
  entityType: string;
  entityId: string;
  details: any;
  timestamp: string;
}

const AdminAuditLogs = () => {
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [actionFilter, setActionFilter] = useState("all");
  const [timeFilter, setTimeFilter] = useState("all");
  const [showLogDetailsDialog, setShowLogDetailsDialog] = useState(false);
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);
  const [stats, setStats] = useState({
    totalLogs: 0,
    todayLogs: 0,
    uniqueAdmins: 0,
    topAction: ""
  });

  useEffect(() => {
    fetchLogs();
  }, [actionFilter, timeFilter]);

  const fetchLogs = async () => {
    setLoading(true);
    try {
      // Get current user to verify admin status
      const { data: currentUser, error: currentUserError } = await supabase.auth.getUser();
      if (currentUserError) throw currentUserError;

      // Check if current user is admin
      const currentUserEmail = currentUser?.user?.email;
      const isCurrentUserAdmin = currentUserEmail === '<EMAIL>' || currentUserEmail === '<EMAIL>';
      
      if (!isCurrentUserAdmin) {
        throw new Error('Unauthorized: Admin access required');
      }

      // Build the query
      let query = supabase
        .from('admin_audit_logs')
        .select('*')
        .order('created_at', { ascending: false });

      // Apply action filter
      if (actionFilter !== "all") {
        query = query.eq('action', actionFilter);
      }

      // Apply time filter
      if (timeFilter === "today") {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        query = query.gte('created_at', today.toISOString());
      } else if (timeFilter === "week") {
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        query = query.gte('created_at', oneWeekAgo.toISOString());
      } else if (timeFilter === "month") {
        const oneMonthAgo = new Date();
        oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
        query = query.gte('created_at', oneMonthAgo.toISOString());
      }

      const { data: logsData, error: logsError } = await query;

      if (logsError) throw logsError;

      // Get admin user emails from profiles table
      const adminIds = [...new Set(logsData?.map(log => log.admin_id) || [])];
      let adminEmailMap = {};

      if (adminIds.length > 0) {
        const { data: profiles } = await supabase
          .from('profiles')
          .select('id, email')
          .in('id', adminIds);
        
        profiles?.forEach(profile => {
          adminEmailMap[profile.id] = profile.email || 'Unknown';
        });
      }

      // Format the data
      const formattedLogs = logsData?.map(log => ({
        id: log.id,
        adminId: log.admin_id,
        adminEmail: adminEmailMap[log.admin_id] || 'Unknown Admin',
        action: log.action,
        entityType: log.entity_type,
        entityId: log.entity_id,
        details: log.details || {},
        timestamp: new Date(log.created_at).toLocaleString()
      })) || [];

      setLogs(formattedLogs);

      // Calculate stats
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayLogs = formattedLogs.filter(log => 
        new Date(log.timestamp) >= today
      ).length;

      const uniqueAdmins = new Set(formattedLogs.map(log => log.adminId)).size;

      // Find most common action
      const actionCounts = {};
      formattedLogs.forEach(log => {
        actionCounts[log.action] = (actionCounts[log.action] || 0) + 1;
      });

      const topAction = Object.keys(actionCounts).reduce((a, b) => 
        actionCounts[a] > actionCounts[b] ? a : b, "none");

      setStats({
        totalLogs: formattedLogs.length,
        todayLogs,
        uniqueAdmins,
        topAction: topAction === "none" ? "No actions" : topAction
      });
    } catch (error) {
      console.error("Error fetching audit logs:", error);
      toast.error("Failed to load audit logs: " + error.message);
    } finally {
      setLoading(false);
    }
  };

  const viewLogDetails = (log: AuditLog) => {
    setSelectedLog(log);
    setShowLogDetailsDialog(true);
  };

  const exportLogs = () => {
    // Create CSV content
    const headers = ["ID", "Admin", "Action", "Entity Type", "Entity ID", "Timestamp", "Details"];
    const csvContent = [
      headers.join(","),
      ...logs.map(log => [
        log.id,
        log.adminEmail,
        log.action,
        log.entityType,
        log.entityId,
        log.timestamp,
        JSON.stringify(log.details).replace(/"/g, '""')
      ].join(","))
    ].join("\n");

    // Create download link
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `admin-audit-logs-${new Date().toISOString().split("T")[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getActionBadgeColor = (action: string) => {
    switch (action) {
      case 'create_user':
      case 'add_manual_transaction':
        return 'bg-green-100 text-green-800';
      case 'edit_user':
      case 'mark_as_paid':
        return 'bg-blue-100 text-blue-800';
      case 'mark_as_unpaid':
        return 'bg-yellow-100 text-yellow-800';
      case 'delete_user':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatActionName = (action: string) => {
    return action.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const filteredLogs = logs.filter(log => 
    log.adminEmail.toLowerCase().includes(searchQuery.toLowerCase()) ||
    log.action.toLowerCase().includes(searchQuery.toLowerCase()) ||
    log.entityType.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (log.details.email && log.details.email.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Admin Audit Logs</h1>
          <div className="flex gap-2">
            <Button variant="outline" onClick={exportLogs}>
              <Download className="mr-2 h-4 w-4" />
              Export CSV
            </Button>
            <Button onClick={() => fetchLogs()}>
              <Shield className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-6 flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Logs</p>
                <p className="text-3xl font-bold">{stats.totalLogs}</p>
              </div>
              <Shield className="h-8 w-8 text-blue-500" />
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-500">Today's Logs</p>
                <p className="text-3xl font-bold">{stats.todayLogs}</p>
              </div>
              <Calendar className="h-8 w-8 text-green-500" />
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-500">Active Admins</p>
                <p className="text-3xl font-bold">{stats.uniqueAdmins}</p>
              </div>
              <Shield className="h-8 w-8 text-purple-500" />
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-500">Top Action</p>
                <p className="text-lg font-bold">{formatActionName(stats.topAction)}</p>
              </div>
              <Filter className="h-8 w-8 text-amber-500" />
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search logs..."
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex items-center gap-2">
            <Select value={actionFilter} onValueChange={setActionFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by action" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Actions</SelectItem>
                <SelectItem value="create_user">Create User</SelectItem>
                <SelectItem value="edit_user">Edit User</SelectItem>
                <SelectItem value="delete_user">Delete User</SelectItem>
                <SelectItem value="mark_as_paid">Mark as Paid</SelectItem>
                <SelectItem value="mark_as_unpaid">Mark as Unpaid</SelectItem>
                <SelectItem value="add_manual_transaction">Manual Transaction</SelectItem>
              </SelectContent>
            </Select>
            <Select value={timeFilter} onValueChange={setTimeFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Time period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">Last 7 Days</SelectItem>
                <SelectItem value="month">Last 30 Days</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Logs Table */}
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Admin</TableHead>
                <TableHead>Action</TableHead>
                <TableHead>Entity</TableHead>
                <TableHead>Target</TableHead>
                <TableHead>Timestamp</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-10">
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      Loading audit logs...
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredLogs.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-10">
                    No audit logs found
                  </TableCell>
                </TableRow>
              ) : (
                filteredLogs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell>{log.adminEmail}</TableCell>
                    <TableCell>
                      <Badge className={getActionBadgeColor(log.action)}>
                        {formatActionName(log.action)}
                      </Badge>
                    </TableCell>
                    <TableCell className="capitalize">{log.entityType}</TableCell>
                    <TableCell>
                      {log.details.email || log.details.name || log.entityId.substring(0, 8) + '...'}
                    </TableCell>
                    <TableCell>{log.timestamp}</TableCell>
                    <TableCell className="text-right">
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => viewLogDetails(log)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        Details
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Log Details Dialog */}
      <Dialog open={showLogDetailsDialog} onOpenChange={setShowLogDetailsDialog}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Audit Log Details</DialogTitle>
            <DialogDescription>
              Detailed information about the selected admin action.
            </DialogDescription>
          </DialogHeader>
          
          {selectedLog && (
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Log ID</p>
                  <p className="font-mono text-sm">{selectedLog.id}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Admin</p>
                  <p className="font-medium">{selectedLog.adminEmail}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Action</p>
                  <Badge className={getActionBadgeColor(selectedLog.action)}>
                    {formatActionName(selectedLog.action)}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Entity Type</p>
                  <p className="font-medium capitalize">{selectedLog.entityType}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Entity ID</p>
                  <p className="font-mono text-sm">{selectedLog.entityId}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Timestamp</p>
                  <p className="font-medium">{selectedLog.timestamp}</p>
                </div>
              </div>
              
              <div>
                <p className="text-sm text-gray-500 mb-2">Action Details</p>
                <div className="bg-gray-50 p-4 rounded-md">
                  <pre className="text-sm whitespace-pre-wrap">
                    {JSON.stringify(selectedLog.details, null, 2)}
                  </pre>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default AdminAuditLogs;
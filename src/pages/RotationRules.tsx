import { useState, useEffect, useContext } from "react";
import { useNavigate } from "react-router-dom";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { CoachingStyleSlider } from "@/components/CoachingStyleSlider";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { useTeam, RotationRules as RotationRulesType, TeamContext } from "@/contexts/TeamContext";
import { Loader2, Save, ArrowLeft } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";

const RotationRules = () => {
  const teamContext = useContext(TeamContext);
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  
  if (!teamContext) {
    console.error('RotationRules: TeamContext is undefined');
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Team Philosophy</h1>
        <div className="text-red-600">Error: Team context not available. Please try refreshing the page.</div>
      </div>
    );
  }
  
  const { rotationRules, updateRotationRules, teamName } = teamContext;
  const [isSaving, setIsSaving] = useState(false);
  const [rules, setRules] = useState<RotationRulesType>(rotationRules);
  const [hasChanges, setHasChanges] = useState(false);

  // Load rules from context when component mounts
  useEffect(() => {
    setRules(rotationRules);
  }, [rotationRules]);

  const handleRulesChange = (newRules: RotationRulesType) => {
    setRules(newRules);
    setHasChanges(true);
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await updateRotationRules(rules);
      toast.success("Team philosophy saved successfully!");
      setHasChanges(false);
    } catch (error) {
      console.error("Error saving rotation rules:", error);
      toast.error("Failed to save settings. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    if (hasChanges) {
      if (confirm("You have unsaved changes. Are you sure you want to leave?")) {
        navigate("/dashboard");
      }
    } else {
      navigate("/dashboard");
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <Header title="Team Philosophy" showBack backLink="/dashboard" />

      <main className="flex-1 container mx-auto px-4 py-6 max-w-4xl">
        <div className="mb-6">
          <h1 className={cn(
            "font-bold text-baseball-navy mb-2",
            isMobile ? "text-xl" : "text-2xl"
          )}>
            Team Philosophy for {teamName}
          </h1>
          <p className={cn(
            "text-muted-foreground",
            isMobile ? "text-sm" : "text-base"
          )}>
            Choose how you want to manage playing time and positions for your team
          </p>
        </div>

        <CoachingStyleSlider
          currentRules={rules}
          onRulesChange={handleRulesChange}
          disabled={isSaving}
        />

        {/* Action Buttons */}
        <div className={cn(
          "flex gap-3 mt-8",
          isMobile ? "flex-col" : "justify-end"
        )}>
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isSaving}
            className={isMobile ? "w-full" : ""}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSaving || !hasChanges}
            className={cn(
              "bg-baseball-green hover:bg-baseball-green/90",
              isMobile ? "w-full" : ""
            )}
          >
            {isSaving ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Save Team Philosophy
              </>
            )}
          </Button>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default RotationRules;
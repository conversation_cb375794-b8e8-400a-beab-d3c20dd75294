import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Search,
  UserPlus,
  Edit,
  Trash2,
  Check,
  X,
  Loader2,
  DollarSign,
  Ban,
  Users
} from "lucide-react";
import AdminLayout from "@/components/AdminLayout";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import { MoreHorizontal } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  isAdmin: boolean;
  isPaid: boolean;
  lastLogin: string;
  created: string;
  teamCount: number;
  subscriptionAmount: number;
  subscriptionCurrency: string;
  lastPaymentDate: string;
  tier?: string;
  teamLimit?: number;
}

const AdminUsers = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [showAddUserDialog, setShowAddUserDialog] = useState(false);
  const [showMarkAsPaidDialog, setShowMarkAsPaidDialog] = useState(false);
  const [showMarkAsUnpaidDialog, setShowMarkAsUnpaidDialog] = useState(false);
  const [showDeleteUserDialog, setShowDeleteUserDialog] = useState(false);
  const [showEditUserDialog, setShowEditUserDialog] = useState(false);
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [selectedUserEmail, setSelectedUserEmail] = useState<string>("");
  const [selectedUserName, setSelectedUserName] = useState<string>("");
  const [selectedUserIds, setSelectedUserIds] = useState<Set<string>>(new Set());
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const [newUserData, setNewUserData] = useState({
    email: "",
    password: "",
    fullName: "",
    role: "",
    isPaid: false,
    tier: "starter" as "starter" | "coach" | "club",
    customTeamLimit: null as number | null
  });
  const [markAsPaidData, setMarkAsPaidData] = useState({
    tier: "starter" as "starter" | "coach" | "club",
    customTeamLimit: null as number | null
  });
  const [editUserData, setEditUserData] = useState({
    fullName: "",
    role: "",
    isPaid: false
  });
  const [processingAction, setProcessingAction] = useState(false);

  useEffect(() => {
    fetchUsers();
    getCurrentUser();
  }, []);

  const getCurrentUser = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
      setCurrentUserId(user.id);
    }
  };

  const fetchUsers = async () => {
    setLoading(true);
    // Clear any existing data first
    setUsers([]);
    // Clear selection state
    setSelectedUserIds(new Set());
    setSelectedUserId(null);
    setSelectedUserEmail("");
    setSelectedUserName("");
    
    try {
      // Get current user to verify admin status
      const { data: currentUser, error: currentUserError } = await supabase.auth.getUser();
      if (currentUserError) throw currentUserError;

      // Check if current user is admin
      const currentUserEmail = currentUser?.user?.email;
      const isCurrentUserAdmin = currentUserEmail === '<EMAIL>' || currentUserEmail === '<EMAIL>';
      
      if (!isCurrentUserAdmin) {
        // Check profile for admin status
        const { data: profile } = await supabase
          .from('profiles')
          .select('is_admin')
          .eq('id', currentUser?.user?.id)
          .maybeSingle();
          
        if (!profile?.is_admin) {
          throw new Error('Unauthorized: Admin access required');
        }
      }

      // Get all profiles with user data including email
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select(`
          id,
          full_name,
          email,
          role,
          created_at,
          is_admin
        `);

      if (profilesError) {
        console.error('Profiles error:', profilesError);
        throw profilesError;
      }

      console.log('Profiles data:', profilesData);
      console.log('Number of profiles:', profilesData?.length || 0);

      // Skip admin API entirely - use profiles data directly
      // The admin API requires service role key which we don't have
      console.log('Using profiles-based approach for user data');
      
      const usersData = {
        users: profilesData?.map(profile => ({
          id: profile.id,
          email: profile.email || 'Email not available',
          created_at: profile.created_at,
          last_sign_in_at: null
        })) || []
      };
      const usersError = null;

      if (usersError) throw usersError;

      // Get subscription data with tier information
      const { data: subscriptionsData, error: subscriptionsError } = await supabase
        .from('subscriptions')
        .select('user_id, is_paid, amount, currency, payment_date, created_at, tier, team_limit');

      if (subscriptionsError) {
        console.error('Subscriptions error:', subscriptionsError);
        // Don't throw, just log and continue with empty subscriptions
        console.warn('Unable to fetch subscriptions, continuing without subscription data');
      }

      // Get team counts
      const { data: teamsData, error: teamsError } = await supabase
        .from('teams')
        .select('user_id');

      if (teamsError) {
        console.error('Teams error:', teamsError);
        // Don't throw, just log and continue with empty teams
        console.warn('Unable to fetch teams, continuing without team data');
      }

      // Create a map of user_id to team count
      const teamCounts = teamsData?.reduce((acc, team) => {
        acc[team.user_id] = (acc[team.user_id] || 0) + 1;
        return acc;
      }, {}) || {};

      // Create a map of user_id to paid status and subscription details
      const paidStatusMap = {};
      const subscriptionDetailsMap = {};
      subscriptionsData?.forEach(sub => {
        if (sub.is_paid) {
          paidStatusMap[sub.user_id] = true;
          subscriptionDetailsMap[sub.user_id] = {
            amount: sub.amount,
            currency: sub.currency,
            paymentDate: sub.payment_date,
            createdAt: sub.created_at,
            tier: sub.tier,
            teamLimit: sub.team_limit
          };
        }
      });

      // Create a map of user_id to profile data
      const profilesMap = {};
      profilesData?.forEach(profile => {
        profilesMap[profile.id] = profile;
      });

      // Combine the data - handle both cases (with or without usersData)
      let formattedUsers = [];
      
      if (usersData?.users && usersData.users.length > 0) {
        // We have auth user data
        formattedUsers = usersData.users.map(user => {
          const profile = profilesMap[user.id] || {};
          const subscriptionDetails = subscriptionDetailsMap[user.id];
          return {
            id: user.id,
            name: profile.full_name || 'No Name',
            email: user.email || profile.email || 'No Email',
            role: profile.role || 'User',
            isAdmin: profile.is_admin || false,
            isPaid: paidStatusMap[user.id] || false,
            lastLogin: user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleDateString() : 'Never',
            created: profile.created_at ? new Date(profile.created_at).toLocaleDateString() : 'Unknown',
            teamCount: teamCounts[user.id] || 0,
            subscriptionAmount: subscriptionDetails?.amount || 0,
            subscriptionCurrency: subscriptionDetails?.currency || 'usd',
            lastPaymentDate: subscriptionDetails?.paymentDate || subscriptionDetails?.createdAt,
            tier: subscriptionDetails?.tier,
            teamLimit: subscriptionDetails?.teamLimit
          };
        });
      } else {
        // Fallback: use profiles data directly
        formattedUsers = profilesData?.map(profile => {
          const subscriptionDetails = subscriptionDetailsMap[profile.id];
          return {
            id: profile.id,
            name: profile.full_name || 'No Name',
            email: profile.email || 'No Email',
            role: profile.role || 'User',
            isAdmin: profile.is_admin || false,
            isPaid: paidStatusMap[profile.id] || false,
            lastLogin: 'N/A',
            created: profile.created_at ? new Date(profile.created_at).toLocaleDateString() : 'Unknown',
            teamCount: teamCounts[profile.id] || 0,
            subscriptionAmount: subscriptionDetails?.amount || 0,
            subscriptionCurrency: subscriptionDetails?.currency || 'usd',
            lastPaymentDate: subscriptionDetails?.paymentDate || subscriptionDetails?.createdAt,
            tier: subscriptionDetails?.tier,
            teamLimit: subscriptionDetails?.teamLimit
          };
        }) || [];
      }

      // Sort users by creation date (newest first)
      formattedUsers.sort((a, b) => {
        const dateA = new Date(a.created || 0);
        const dateB = new Date(b.created || 0);
        return dateB.getTime() - dateA.getTime();
      });

      setUsers(formattedUsers);
    } catch (error) {
      console.error("Error fetching users:", error);
      toast.error("Failed to load users");
    } finally {
      setLoading(false);
    }
  };

  // Helper function to set up paid subscription
  const setupPaidSubscription = async (userId: string) => {
    // Determine team limit based on tier or custom value
    let teamLimit = 1;
    if (newUserData.customTeamLimit !== null) {
      teamLimit = newUserData.customTeamLimit;
    } else {
      switch (newUserData.tier) {
        case 'starter':
          teamLimit = 1;
          break;
        case 'coach':
          teamLimit = 5;
          break;
        case 'club':
          teamLimit = 999;
          break;
      }
    }

    // Determine amount based on tier
    let amount = 2000; // Default $20 for starter
    switch (newUserData.tier) {
      case 'starter':
        amount = 2000;
        break;
      case 'coach':
        amount = 3000;
        break;
      case 'club':
        amount = 50000;
        break;
    }

    console.log("Setting up subscription with data:", {
      user_id: userId,
      is_paid: true,
      tier: newUserData.tier,
      team_limit: teamLimit,
      amount: amount
    });
    
    // Add extra debug logging
    console.log("DEBUG: newUserData object:", newUserData);
    console.log("DEBUG: Selected tier from form:", newUserData.tier);
    console.log("DEBUG: Tier that will be inserted:", newUserData.tier || 'club');

    // First check if a subscription already exists (created by trigger)
    const { data: existingSubscription, error: checkError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();

    if (checkError && checkError.code !== 'PGRST116') {
      console.error("Error checking existing subscription:", checkError);
    }

    let subscriptionData, subscriptionError;
    
    if (existingSubscription) {
      // Update existing subscription
      console.log("Updating existing subscription:", existingSubscription.id);
      ({ data: subscriptionData, error: subscriptionError } = await supabase
        .from('subscriptions')
        .update({
          is_paid: true,
          amount: amount,
          currency: 'usd',
          payment_date: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          tier: newUserData.tier || 'club',
          team_limit: teamLimit || 999
        })
        .eq('id', existingSubscription.id)
        .select()
        .single());
    } else {
      // Create new subscription
      console.log("Creating new subscription");
      ({ data: subscriptionData, error: subscriptionError } = await supabase
        .from('subscriptions')
        .insert({
          user_id: userId,
          is_paid: true,
          amount: amount,
          currency: 'usd',
          payment_date: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          tier: newUserData.tier || 'club',
          team_limit: teamLimit || 999
        })
        .select()
        .single());
    }

    if (subscriptionError) {
      console.error("Error setting up subscription:", subscriptionError);
      toast.error("Warning: User created but subscription setup failed");
      // Don't throw here, continue anyway
    } else {
      console.log("Subscription set up successfully:", subscriptionData);
      if (!subscriptionData.is_paid) {
        console.warn("WARNING: Subscription created/updated but is_paid is false!");
      }
    }
  };

  // Helper function to reset the form
  const resetNewUserForm = () => {
    setNewUserData({
      email: "",
      password: "",
      fullName: "",
      role: "",
      isPaid: false,
      tier: "starter",
      customTeamLimit: null
    });
  };

  const handleAddUser = async () => {
    if (!newUserData.email || !newUserData.password) {
      toast.error("Email and password are required");
      return;
    }

    setProcessingAction(true);
    try {
      console.log("Creating user with email:", newUserData.email);
      
      // First, try to use the edge function for admin user creation
      // This prevents the admin's session from being affected
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session) {
        try {
          console.log("Attempting to use admin-create-user edge function...");
          const response = await fetch(
            `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/admin-create-user`,
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${session.access_token}`
              },
              body: JSON.stringify({
                email: newUserData.email,
                password: newUserData.password,
                fullName: newUserData.fullName,
                role: newUserData.role,
                isPaid: newUserData.isPaid,
                tier: newUserData.tier,
                customTeamLimit: newUserData.customTeamLimit
              })
            }
          );

          if (response.ok) {
            const result = await response.json();
            console.log("Edge function response:", result);
            
            if (result.success && result.data?.user?.id) {
              const userId = result.data.user.id;
              console.log("User created successfully via edge function with ID:", userId);
              
              // Wait a moment for database to propagate
              await new Promise(resolve => setTimeout(resolve, 1000));
              
              // Continue with subscription setup if paid
              if (newUserData.isPaid) {
                await setupPaidSubscription(userId);
              }
              
              // Success - refresh user list and close dialog
              toast.success("User created successfully! They will receive a confirmation email.");
              setShowAddUserDialog(false);
              resetNewUserForm();
              
              // Wait a bit before refreshing to allow database to update
              setTimeout(() => {
                fetchUsers();
              }, 2000);
              return; // Exit early - edge function handled everything
            }
          } else {
            console.warn("Edge function failed, falling back to client-side creation");
          }
        } catch (edgeError) {
          console.warn("Could not use edge function, falling back to client-side:", edgeError);
        }
      }
      
      // Fallback: Use client-side signUp (this may affect admin session)
      console.warn("Using client-side signUp - admin may be logged out");
      
      const { data: userData, error } = await supabase.auth.signUp({
        email: newUserData.email,
        password: newUserData.password,
        options: {
          data: {
            full_name: newUserData.fullName,
            role: newUserData.role
          }
        }
      });

      if (error) throw error;

      // Get the user ID
      const userId = userData?.user?.id;

      if (!userId) {
        throw new Error("Failed to get user ID after creation");
      }

      console.log("User created successfully with ID:", userId);

      // Wait a moment for auth to propagate
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Create the profile
      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: userId,
          full_name: newUserData.fullName,
          email: newUserData.email,
          role: newUserData.role,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (profileError) {
        console.error("Error creating profile:", profileError);
        // Don't throw here, continue anyway
      }

      // If the user should be paid, set up the subscription
      if (newUserData.isPaid) {
        await setupPaidSubscription(userId);
      }

      // Log admin action
      try {
        await supabase
          .from('admin_audit_logs')
          .insert({
            admin_id: (await supabase.auth.getUser()).data.user?.id,
            action: 'create_user',
            entity_type: 'user',
            entity_id: userId,
            details: {
              email: newUserData.email,
              isPaid: newUserData.isPaid
            }
          });
      } catch (logError) {
        console.warn("Failed to log admin action:", logError);
      }

      toast.success("User created successfully! They will receive a welcome email with their login credentials.");
      setShowAddUserDialog(false);
      resetNewUserForm();
      
      // Wait a bit before refreshing to allow database to update
      setTimeout(() => {
        fetchUsers(); // Refresh the user list
      }, 2000);
    } catch (error: any) {
      console.error("Error creating user:", error);
      console.error("Full error details:", {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint,
        status: error.status
      });

      // More specific error messages
      if (error.message?.includes("already registered") || error.code === "23505" || error.message?.includes("already been registered")) {
        toast.error("This email is already registered. Please use a different email address.");
      } else if (error.message?.includes("password")) {
        toast.error("Password must be at least 6 characters");
      } else if (error.code === "42501") {
        toast.error("Permission denied. Please ensure you have admin privileges.");
      } else if (error.message?.includes("profiles_email_key")) {
        toast.error("A user with this email already exists in the system");
      } else {
        toast.error(`Failed to create user: ${error.message || "Unknown error"}`);
      }
    } finally {
      setProcessingAction(false);
    }
  };

  const handleMarkAsPaid = async () => {
    if (!selectedUserId) return;

    setProcessingAction(true);
    try {
      // Check if user already has ANY subscription (paid or unpaid)
      const { data: existingSubscription, error: checkError } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', selectedUserId)
        .maybeSingle();

      if (checkError && checkError.code !== 'PGRST116') throw checkError;

      // Determine team limit and amount based on tier
      let teamLimit = 1;
      if (markAsPaidData.customTeamLimit !== null) {
        teamLimit = markAsPaidData.customTeamLimit;
      } else {
        switch (markAsPaidData.tier) {
          case 'starter':
            teamLimit = 1;
            break;
          case 'coach':
            teamLimit = 5;
            break;
          case 'club':
            teamLimit = 999;
            break;
        }
      }

      let amount = 2000;
      switch (markAsPaidData.tier) {
        case 'starter':
          amount = 2000;
          break;
        case 'coach':
          amount = 3000;
          break;
        case 'club':
          amount = 50000;
          break;
      }

      if (existingSubscription) {
        // Update existing subscription
        const { error: updateError } = await supabase
          .from('subscriptions')
          .update({
            is_paid: true,
            tier: markAsPaidData.tier,
            team_limit: teamLimit,
            amount: amount,
            currency: 'usd',
            payment_date: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', existingSubscription.id);

        if (updateError) throw updateError;
      } else {
        // Create new subscription
        const { error: insertError } = await supabase
          .from('subscriptions')
          .insert({
            user_id: selectedUserId,
            is_paid: true,
            amount: amount,
            currency: 'usd',
            payment_date: new Date().toISOString(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            tier: markAsPaidData.tier,
            team_limit: teamLimit
          });

        if (insertError) throw insertError;
      }

      // Log admin action
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_id: (await supabase.auth.getUser()).data.user?.id,
          action: 'mark_as_paid',
          entity_type: 'user',
          entity_id: selectedUserId,
          details: { email: selectedUserEmail }
        });

      toast.success(`User ${selectedUserEmail} marked as paid. They need to sign out and back in to access paid features.`);
      setShowMarkAsPaidDialog(false);
      fetchUsers(); // Refresh the user list
    } catch (error) {
      console.error("Error marking user as paid:", error);
      toast.error("Failed to update payment status");
    } finally {
      setProcessingAction(false);
    }
  };

  const handleMarkAsUnpaid = async () => {
    if (!selectedUserId) return;

    setProcessingAction(true);
    try {
      // Update all subscriptions for this user
      const { error } = await supabase
        .from('subscriptions')
        .update({
          is_paid: false,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', selectedUserId);

      if (error) throw error;

      // Log admin action
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_id: (await supabase.auth.getUser()).data.user?.id,
          action: 'mark_as_unpaid',
          entity_type: 'user',
          entity_id: selectedUserId,
          details: { email: selectedUserEmail }
        });

      toast.success(`User ${selectedUserEmail} marked as unpaid`);
      setShowMarkAsUnpaidDialog(false);
      fetchUsers(); // Refresh the user list
    } catch (error) {
      console.error("Error marking user as unpaid:", error);
      toast.error("Failed to update payment status");
    } finally {
      setProcessingAction(false);
    }
  };

  const handleDeleteUser = async () => {
    if (!selectedUserId) return;

    setProcessingAction(true);
    try {
      // First, check if user has teams (warn admin)
      const { data: userTeams, error: teamsError } = await supabase
        .from('teams')
        .select('id, name')
        .eq('user_id', selectedUserId);

      if (teamsError) throw teamsError;

      if (userTeams && userTeams.length > 0) {
        const teamNames = userTeams.map(team => team.name).join(', ');
        const confirmDelete = window.confirm(
          `This user has ${userTeams.length} team(s): ${teamNames}. All teams and their data will be permanently deleted. Are you sure you want to continue?`
        );
        
        if (!confirmDelete) {
          setProcessingAction(false);
          return;
        }
      }

      // Delete user data in correct order to avoid foreign key constraints
      console.log("Deleting user data for:", selectedUserId);
      
      // 1. Delete user's teams (this will cascade to players, lineups, etc.)
      const { error: deleteTeamsError } = await supabase
        .from('teams')
        .delete()
        .eq('user_id', selectedUserId);

      if (deleteTeamsError) {
        console.error("Error deleting teams:", deleteTeamsError);
        throw deleteTeamsError;
      }

      // 2. Delete user's subscriptions
      const { error: deleteSubsError } = await supabase
        .from('subscriptions')
        .delete()
        .eq('user_id', selectedUserId);

      if (deleteSubsError) {
        console.error("Error deleting subscriptions:", deleteSubsError);
        // Don't throw here, continue with deletion
      }

      // 3. Delete user's profile
      const { error: deleteProfileError } = await supabase
        .from('profiles')
        .delete()
        .eq('id', selectedUserId);

      if (deleteProfileError) {
        console.error("Error deleting profile:", deleteProfileError);
        throw deleteProfileError;
      }

      // 4. Call the Edge Function to delete the auth user properly
      console.log("Attempting to call Edge Function to delete auth user...");
      console.log("Edge Function URL:", `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/admin-delete-user`);
      
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) {
          console.warn("No session available for Edge Function call");
        } else {
          console.log("Calling Edge Function with userId:", selectedUserId);
          const response = await fetch(
            `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/admin-delete-user`,
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${session.access_token}`
              },
              body: JSON.stringify({ userId: selectedUserId })
            }
          );

          console.log("Edge Function response status:", response.status);
          const result = await response.json();
          console.log("Edge Function response:", result);
          
          if (!response.ok) {
            console.warn("Edge Function error:", result);
            // Don't throw - data deletion was successful even if auth deletion failed
            if (result.partial) {
              toast.warning("User data deleted but auth record may remain. They won't be able to access the system.");
            }
          } else {
            console.log("User successfully deleted from auth system");
          }
        }
      } catch (authError) {
        console.error("Could not call Edge Function for auth deletion:", authError);
        // Don't throw - data deletion was successful
      }

      // Log admin action
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_id: (await supabase.auth.getUser()).data.user?.id,
          action: 'delete_user',
          entity_type: 'user',
          entity_id: selectedUserId,
          details: { 
            email: selectedUserEmail, 
            name: selectedUserName,
            teamCount: userTeams?.length || 0
          }
        });

      toast.success(`User ${selectedUserEmail} has been deleted`);
      setShowDeleteUserDialog(false);
      // Force refresh the user list without full page reload
      setTimeout(() => {
        fetchUsers();
      }, 1000);
    } catch (error) {
      console.error("Error deleting user:", error);
      toast.error("Failed to delete user: " + error.message);
    } finally {
      setProcessingAction(false);
    }
  };

  const handleEditUser = async () => {
    if (!selectedUserId) return;

    setProcessingAction(true);
    try {
      // Update user profile
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          full_name: editUserData.fullName,
          role: editUserData.role,
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedUserId);

      if (profileError) throw profileError;

      // Update subscription status if needed
      if (editUserData.isPaid) {
        // Check if user already has a subscription
        const { data: existingSubscription, error: checkError } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', selectedUserId)
          .maybeSingle();

        if (checkError) throw checkError;

        if (existingSubscription) {
          // Update existing subscription
          const { error: updateError } = await supabase
            .from('subscriptions')
            .update({
              is_paid: true,
              updated_at: new Date().toISOString()
            })
            .eq('id', existingSubscription.id);

          if (updateError) throw updateError;
        } else {
          // Create new subscription
          const { error: insertError } = await supabase
            .from('subscriptions')
            .insert({
              user_id: selectedUserId,
              is_paid: true,
              amount: 4900, // $49.00
              currency: 'usd',
              payment_date: new Date().toISOString(),
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              tier: 'starter',
              team_limit: 1
            });

          if (insertError) throw insertError;
        }
      } else {
        // Mark as unpaid if needed
        const { error: unpaidError } = await supabase
          .from('subscriptions')
          .update({
            is_paid: false,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', selectedUserId);

        if (unpaidError) console.warn("Error updating subscription to unpaid:", unpaidError);
      }

      // Log admin action
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_id: (await supabase.auth.getUser()).data.user?.id,
          action: 'edit_user',
          entity_type: 'user',
          entity_id: selectedUserId,
          details: { 
            email: selectedUserEmail,
            changes: editUserData
          }
        });

      toast.success(`User ${selectedUserEmail} has been updated`);
      setShowEditUserDialog(false);
      fetchUsers(); // Refresh the user list
    } catch (error) {
      console.error("Error editing user:", error);
      toast.error("Failed to update user: " + error.message);
    } finally {
      setProcessingAction(false);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedUserIds.size === 0) return;

    setProcessingAction(true);
    try {
      const userIdsArray = Array.from(selectedUserIds);
      
      // Get user details for confirmation
      const { data: selectedUsers, error: fetchError } = await supabase
        .from('profiles')
        .select('id, full_name, email')
        .in('id', userIdsArray);

      if (fetchError) throw fetchError;

      // Filter out protected accounts
      const protectedUsers = selectedUsers?.filter(u => isProtectedAccount(u.email || '')) || [];
      const deletableUsers = selectedUsers?.filter(u => !isProtectedAccount(u.email || '')) || [];

      if (protectedUsers.length > 0) {
        toast.error(`Cannot delete protected accounts: ${protectedUsers.map(u => u.email).join(', ')}`);
        if (deletableUsers.length === 0) {
          setProcessingAction(false);
          return;
        }
      }

      const userNames = deletableUsers.map(u => `${u.full_name || 'No name'} (${u.email})`).join('\n') || '';
      
      const confirmDelete = window.confirm(
        `Are you sure you want to delete ${deletableUsers.length} user(s)?\n\n${userNames}\n\nThis will permanently delete all their teams, players, and data. This action cannot be undone!`
      );
      
      if (!confirmDelete) {
        setProcessingAction(false);
        return;
      }

      // Delete each user (only non-protected ones)
      const errors = [];
      const deletableUserIds = deletableUsers.map(u => u.id);
      
      for (const userId of deletableUserIds) {
        try {
          // Delete teams
          await supabase.from('teams').delete().eq('user_id', userId);
          // Delete subscriptions
          await supabase.from('subscriptions').delete().eq('user_id', userId);
          // Delete profile
          await supabase.from('profiles').delete().eq('id', userId);
          
          // Try to delete auth user via Edge Function
          try {
            const { data: { session } } = await supabase.auth.getSession();
            if (session) {
              const response = await fetch(
                `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/admin-delete-user`,
                {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${session.access_token}`
                  },
                  body: JSON.stringify({ userId })
                }
              );
              
              if (!response.ok) {
                const result = await response.json();
                console.warn(`Could not delete auth for user ${userId}:`, result);
              }
            }
          } catch (authError) {
            console.warn(`Could not delete auth for user ${userId}:`, authError);
          }
        } catch (error) {
          errors.push({ userId, error });
        }
      }

      // Log admin action
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_id: (await supabase.auth.getUser()).data.user?.id,
          action: 'bulk_delete_users',
          entity_type: 'user',
          entity_id: userIdsArray.join(','),
          details: { 
            count: selectedUserIds.size,
            userIds: userIdsArray,
            errors: errors.length > 0 ? errors : undefined
          }
        });

      if (errors.length > 0) {
        toast.error(`Deleted ${selectedUserIds.size - errors.length} users. ${errors.length} failed.`);
      } else {
        toast.success(`Successfully deleted ${selectedUserIds.size} user(s)`);
      }
      
      setSelectedUserIds(new Set());
      setShowBulkDeleteDialog(false);
      // Force refresh the user list without full page reload
      setTimeout(() => {
        fetchUsers();
      }, 1000);
    } catch (error) {
      console.error("Error in bulk delete:", error);
      toast.error("Failed to delete users: " + error.message);
    } finally {
      setProcessingAction(false);
    }
  };

  const toggleUserSelection = (userId: string) => {
    // Prevent selecting current user
    if (userId === currentUserId) {
      toast.error("You cannot select your own account for deletion");
      return;
    }
    
    const newSelection = new Set(selectedUserIds);
    if (newSelection.has(userId)) {
      newSelection.delete(userId);
    } else {
      newSelection.add(userId);
    }
    setSelectedUserIds(newSelection);
  };

  const toggleSelectAll = () => {
    const selectableUsers = filteredUsers.filter(u => 
      !isProtectedAccount(u.email) && u.id !== currentUserId
    );
    
    if (selectedUserIds.size === selectableUsers.length) {
      setSelectedUserIds(new Set());
    } else {
      setSelectedUserIds(new Set(selectableUsers.map(u => u.id)));
    }
  };

  // Protected accounts that cannot be deleted
  const PROTECTED_ACCOUNTS = ['<EMAIL>', '<EMAIL>'];

  const isProtectedAccount = (email: string) => {
    return PROTECTED_ACCOUNTS.includes(email.toLowerCase());
  };

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.role.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">User Management</h1>
          <div className="flex gap-2">
            {selectedUserIds.size > 0 && (
              <Button 
                variant="destructive" 
                onClick={() => setShowBulkDeleteDialog(true)}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Selected ({selectedUserIds.size})
              </Button>
            )}
            <Button onClick={() => setShowAddUserDialog(true)}>
              <UserPlus className="mr-2 h-4 w-4" />
              Add User
            </Button>
          </div>
        </div>

        <div className="relative mb-6">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            placeholder="Search users..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedUserIds.size === filteredUsers.length && filteredUsers.length > 0}
                    onCheckedChange={toggleSelectAll}
                  />
                </TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Teams</TableHead>
                <TableHead>Last Login</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-10">
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      Loading users...
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredUsers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-10">
                    No users found
                  </TableCell>
                </TableRow>
              ) : (
                filteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedUserIds.has(user.id)}
                        onCheckedChange={() => toggleUserSelection(user.id)}
                        disabled={isProtectedAccount(user.email) || user.id === currentUserId}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {user.name}
                        {isProtectedAccount(user.email) && (
                          <Badge variant="secondary" className="text-xs">Protected</Badge>
                        )}
                        {user.id === currentUserId && (
                          <Badge variant="outline" className="text-xs">You</Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      {user.isAdmin ? (
                        <span className="px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800">
                          Admin
                        </span>
                      ) : (
                        user.role || "User"
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col gap-1">
                        <span className={`px-2 py-1 rounded-full text-xs ${user.isPaid ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                          {user.isPaid ? 'Paid' : 'Free'}
                        </span>
                        {user.isPaid && user.tier && (
                          <span className="text-xs text-gray-500">
                            {user.tier.charAt(0).toUpperCase() + user.tier.slice(1)} ({user.teamLimit || 1} teams)
                          </span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{user.teamCount}</TableCell>
                    <TableCell>{user.lastLogin}</TableCell>
                    <TableCell>{user.created}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => {
                            setSelectedUserId(user.id);
                            setSelectedUserEmail(user.email);
                            setShowMarkAsPaidDialog(true);
                          }}>
                            <DollarSign className="h-4 w-4 mr-2" />
                            Mark as Paid
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => {
                            setSelectedUserId(user.id);
                            setSelectedUserEmail(user.email);
                            setShowMarkAsUnpaidDialog(true);
                          }}>
                            <Ban className="h-4 w-4 mr-2" />
                            Mark as Unpaid
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => {
                            setSelectedUserId(user.id);
                            setSelectedUserEmail(user.email);
                            setSelectedUserName(user.name);
                            setEditUserData({
                              fullName: user.name,
                              role: user.role,
                              isPaid: user.isPaid
                            });
                            setShowEditUserDialog(true);
                          }}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit User
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            className="text-red-600"
                            onClick={() => {
                              if (isProtectedAccount(user.email)) {
                                toast.error(`Cannot delete protected account: ${user.email}`);
                                return;
                              }
                              if (user.id === currentUserId) {
                                toast.error("You cannot delete your own account");
                                return;
                              }
                              setSelectedUserId(user.id);
                              setSelectedUserEmail(user.email);
                              setSelectedUserName(user.name);
                              setShowDeleteUserDialog(true);
                            }}
                            disabled={isProtectedAccount(user.email) || user.id === currentUserId}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            {isProtectedAccount(user.email) ? 'Protected Account' : 
                             user.id === currentUserId ? 'Your Account' : 'Delete User'}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Add User Dialog */}
      <Dialog open={showAddUserDialog} onOpenChange={setShowAddUserDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New User</DialogTitle>
            <DialogDescription>
              Create a new user account. The user will receive a confirmation email to verify their account.
              <br />
              <span className="text-yellow-600 text-sm mt-2 block">
                Note: If email already exists in the system, use the command line script instead.
              </span>
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={newUserData.email}
                onChange={(e) => setNewUserData({...newUserData, email: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                placeholder="••••••••"
                value={newUserData.password}
                onChange={(e) => setNewUserData({...newUserData, password: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="fullName">Full Name</Label>
              <Input
                id="fullName"
                placeholder="John Doe"
                value={newUserData.fullName}
                onChange={(e) => setNewUserData({...newUserData, fullName: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="role">Role</Label>
              <Input
                id="role"
                placeholder="Coach, Manager, etc."
                value={newUserData.role}
                onChange={(e) => setNewUserData({...newUserData, role: e.target.value})}
              />
            </div>
            <div className="flex items-center space-x-2 pt-2">
              <Checkbox
                id="isPaid"
                checked={newUserData.isPaid}
                onCheckedChange={(checked) =>
                  setNewUserData({...newUserData, isPaid: checked === true})
                }
              />
              <Label
                htmlFor="isPaid"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Create as paid user (with full access)
              </Label>
            </div>
            
            {newUserData.isPaid && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="tier">Subscription Tier</Label>
                  <select
                    id="tier"
                    className="w-full px-3 py-2 border rounded-md"
                    value={newUserData.tier}
                    onChange={(e) => setNewUserData({...newUserData, tier: e.target.value as "starter" | "coach" | "club"})}
                  >
                    <option value="starter">Starter ($20/year - 1 team)</option>
                    <option value="coach">Coach ($30/year - 5 teams)</option>
                    <option value="club">Club ($500/year - Unlimited teams)</option>
                  </select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="customTeamLimit">
                    Custom Team Limit (optional)
                    <span className="text-xs text-gray-500 ml-2">Leave empty to use tier default</span>
                  </Label>
                  <Input
                    id="customTeamLimit"
                    type="number"
                    placeholder="e.g., 10"
                    value={newUserData.customTeamLimit || ""}
                    onChange={(e) => setNewUserData({...newUserData, customTeamLimit: e.target.value ? parseInt(e.target.value) : null})}
                  />
                </div>
              </>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddUserDialog(false)}>Cancel</Button>
            <Button onClick={handleAddUser} disabled={processingAction}>
              {processingAction ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create User"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Mark as Paid Dialog */}
      <Dialog open={showMarkAsPaidDialog} onOpenChange={setShowMarkAsPaidDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Mark User as Paid</DialogTitle>
            <DialogDescription>
              This will grant the user full access to all paid features.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <p>Mark <strong>{selectedUserEmail}</strong> as a paid user with the following subscription:</p>
            
            <div className="space-y-2">
              <Label htmlFor="markPaidTier">Subscription Tier</Label>
              <select
                id="markPaidTier"
                className="w-full px-3 py-2 border rounded-md"
                value={markAsPaidData.tier}
                onChange={(e) => setMarkAsPaidData({...markAsPaidData, tier: e.target.value as "starter" | "coach" | "club"})}
              >
                <option value="starter">Starter ($20/year - 1 team)</option>
                <option value="coach">Coach ($30/year - 5 teams)</option>
                <option value="club">Club ($500/year - Unlimited teams)</option>
              </select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="markPaidCustomTeamLimit">
                Custom Team Limit (optional)
                <span className="text-xs text-gray-500 ml-2">Leave empty to use tier default</span>
              </Label>
              <Input
                id="markPaidCustomTeamLimit"
                type="number"
                placeholder="e.g., 10"
                value={markAsPaidData.customTeamLimit || ""}
                onChange={(e) => setMarkAsPaidData({...markAsPaidData, customTeamLimit: e.target.value ? parseInt(e.target.value) : null})}
              />
            </div>
            
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <p className="text-sm text-blue-800">
                <strong>Note:</strong> After marking as paid, the user must sign out and sign back in to access paid features.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowMarkAsPaidDialog(false)}>Cancel</Button>
            <Button onClick={handleMarkAsPaid} disabled={processingAction}>
              {processingAction ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Mark as Paid"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Mark as Unpaid Dialog */}
      <Dialog open={showMarkAsUnpaidDialog} onOpenChange={setShowMarkAsUnpaidDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Mark User as Unpaid</DialogTitle>
            <DialogDescription>
              This will revoke the user's access to paid features.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p>Are you sure you want to mark <strong>{selectedUserEmail}</strong> as an unpaid user?</p>
            <p className="text-red-500 mt-2">This will restrict their access to paid features.</p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowMarkAsUnpaidDialog(false)}>Cancel</Button>
            <Button variant="destructive" onClick={handleMarkAsUnpaid} disabled={processingAction}>
              {processingAction ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Mark as Unpaid"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog open={showEditUserDialog} onOpenChange={setShowEditUserDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Update user information and account settings.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="editFullName">Full Name</Label>
              <Input
                id="editFullName"
                placeholder="John Doe"
                value={editUserData.fullName}
                onChange={(e) => setEditUserData({...editUserData, fullName: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="editRole">Role</Label>
              <Input
                id="editRole"
                placeholder="Coach, Manager, etc."
                value={editUserData.role}
                onChange={(e) => setEditUserData({...editUserData, role: e.target.value})}
              />
            </div>
            <div className="flex items-center space-x-2 pt-2">
              <Checkbox
                id="editIsPaid"
                checked={editUserData.isPaid}
                onCheckedChange={(checked) =>
                  setEditUserData({...editUserData, isPaid: checked === true})
                }
              />
              <Label
                htmlFor="editIsPaid"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Paid user (with full access)
              </Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditUserDialog(false)}>Cancel</Button>
            <Button onClick={handleEditUser} disabled={processingAction}>
              {processingAction ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Update User"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete User Dialog */}
      <Dialog open={showDeleteUserDialog} onOpenChange={setShowDeleteUserDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="text-red-600">Delete User Account</DialogTitle>
            <DialogDescription>
              You are about to permanently delete a user account. This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
              <p className="font-medium">You are about to delete:</p>
              <p className="text-lg mt-1"><strong>{selectedUserName}</strong></p>
              <p className="text-sm text-gray-600">{selectedUserEmail}</p>
            </div>
            
            <p className="text-red-600 font-medium mb-2">
              ⚠️ This will permanently delete:
            </p>
            <ul className="text-sm space-y-1 ml-4 list-disc text-gray-700">
              <li>The user's account and profile</li>
              <li>All teams created by this user</li>
              <li>All players, lineups, and game data</li>
              <li>All subscription and payment records</li>
            </ul>
            
            <div className="bg-red-100 border border-red-300 rounded-md p-3 mt-4">
              <p className="text-red-800 font-bold text-sm">
                ⛔ This action CANNOT be undone. All data will be permanently lost.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteUserDialog(false)}>Cancel</Button>
            <Button variant="destructive" onClick={handleDeleteUser} disabled={processingAction}>
              {processingAction ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete User"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk Delete Dialog */}
      <Dialog open={showBulkDeleteDialog} onOpenChange={setShowBulkDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Multiple Users</DialogTitle>
            <DialogDescription>
              This action cannot be undone. This will permanently delete the selected users and all their data.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p>You are about to delete <strong>{selectedUserIds.size} user(s)</strong>.</p>
            <p className="text-red-500 mt-2 font-medium">
              This will permanently delete:
            </p>
            <ul className="text-red-500 mt-1 ml-4 list-disc">
              <li>All selected user accounts and profiles</li>
              <li>All teams created by these users</li>
              <li>All associated players and lineups</li>
              <li>All subscription data</li>
            </ul>
            <p className="text-red-600 mt-3 font-bold">This action cannot be undone!</p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowBulkDeleteDialog(false)}>Cancel</Button>
            <Button variant="destructive" onClick={handleBulkDelete} disabled={processingAction}>
              {processingAction ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting {selectedUserIds.size} users...
                </>
              ) : (
                `Delete ${selectedUserIds.size} Users`
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default AdminUsers;

import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

const DemoLogin = () => {
  const navigate = useNavigate();
  const [status, setStatus] = useState<'loading' | 'error' | 'success'>('loading');

  // Demo credentials for display
  const demoEmail = '<EMAIL>';
  const demoUserId = 'f15ba189-c70f-4513-9451-6f95568e784a'; // Actual authenticated user ID
  const demoTeamId = '83bd9832-f5db-4c7d-b234-41fd38f90007';

  useEffect(() => {
    const setupDemoMode = async () => {
      try {
        console.log("Setting up demo mode...");

        // Clear any existing state
        localStorage.clear();
        sessionStorage.clear();

        // Set up demo mode flags first
        console.log("Setting up demo mode flags...");
        localStorage.setItem('demo_mode', 'true');
        localStorage.setItem('demo_user_email', demoEmail);
        localStorage.setItem('demo_user_id', demoUserId);
        localStorage.setItem('current_team_id', demoTeamId);
        localStorage.setItem('demo_user_is_paid', 'true');
        localStorage.setItem('email', demoEmail);
        localStorage.setItem('teamname', 'Demo Softball Team');

        // Authenticate the demo user with Supabase to bypass RLS
        try {
          console.log("Authenticating demo user with Supabase...");
          const { supabase } = await import('../supabaseClient');

          // Sign in the demo user (this will create the user if it doesn't exist)
          const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
            email: demoEmail,
            password: 'demo_password_123' // Demo password
          });

          if (authError) {
            console.log("Demo user doesn't exist, creating it...");
            // If sign in fails, try to create the demo user
            const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
              email: demoEmail,
              password: 'demo_password_123',
              options: {
                data: {
                  id: demoUserId // Try to set the user ID
                }
              }
            });

            if (signUpError) {
              console.warn("Failed to create demo user, continuing without auth:", signUpError);
            } else {
              console.log("Demo user created successfully:", signUpData);
            }
          } else {
            console.log("Demo user authenticated successfully:", authData);
            // Update localStorage with actual user ID from auth
            if (authData.user) {
              localStorage.setItem('demo_user_id', authData.user.id);
            }
          }
        } catch (authError) {
          console.warn("Demo authentication failed, but continuing:", authError);
        }

        // Try to initialize demo data, but don't fail if it doesn't work
        try {
          console.log("Initializing demo data...");
          const { initializeDemoData } = await import('../services/teamService');
          // Get the actual authenticated user ID or use the hardcoded one
          const { supabase: authSupabase } = await import('../supabaseClient');
          const { data: { user: currentUser } } = await authSupabase.auth.getUser();
          const actualUserId = currentUser?.id || demoUserId;
          
          await initializeDemoData(actualUserId);
          console.log("Demo data initialization successful");
        } catch (dataError) {
          console.warn("Demo data initialization failed, but continuing with demo mode:", dataError);
          // Continue anyway - the TeamContext will handle fallbacks
        }

        console.log("Demo mode setup complete");
        setStatus('success');

        // Redirect to dashboard
        setTimeout(() => {
          window.location.href = '/dashboard';
        }, 1000);

      } catch (error) {
        console.error('Error setting up demo mode:', error);
        setStatus('error');
      }
    };

    setupDemoMode();
  }, []);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-baseball-lightblue/30">
      <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-lg">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-baseball-navy">Demo Login</h1>
          <p className="mt-2 text-gray-600">
            {status === 'loading' && 'Logging you in with demo account...'}
            {status === 'success' && 'Login successful! Redirecting to dashboard...'}
            {status === 'error' && 'Failed to log in with demo account.'}
          </p>
        </div>

        <div className="flex justify-center">
          {status === 'loading' && (
            <Loader2 className="h-8 w-8 animate-spin text-baseball-navy" />
          )}
        </div>

        {status === 'error' && (
          <div className="space-y-4">
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-700">Failed to set up demo mode. Please try again.</p>
            </div>
            <div className="flex flex-col space-y-2">
              <Button onClick={() => navigate('/')}>Return to Home</Button>
              <Button variant="outline" onClick={() => {
                // Simply reload the page to try again
                window.location.reload();
              }}>Try Again</Button>
            </div>
          </div>
        )}

        {status === 'success' && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-md">
            <p className="text-green-700">Successfully logged in with demo account! Redirecting to dashboard...</p>
          </div>
        )}

        <div className="mt-4 text-center text-sm text-gray-500">
          <p>Demo account automatically configured</p>
          <p><strong>Email:</strong> {demoEmail}</p>
          <p>Full access to all features</p>
        </div>
      </div>
    </div>
  );
};

export default DemoLogin;


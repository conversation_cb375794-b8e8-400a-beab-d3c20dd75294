import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import AdminLayout from "@/components/AdminLayout";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { RefreshCw, Trash2, Database, AlertTriangle } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const AdminMaintenance = () => {
  const [showResetDialog, setShowResetDialog] = useState(false);
  const [resetType, setResetType] = useState<"all" | "team" | "user">("all");
  const [targetId, setTargetId] = useState("");
  const [isResetting, setIsResetting] = useState(false);
  const [stats, setStats] = useState<any>(null);
  const [loadingStats, setLoadingStats] = useState(false);

  // Load current statistics
  const loadStats = async () => {
    setLoadingStats(true);
    try {
      // Get total position history records
      const { count: totalRecords, error: countError } = await supabase
        .from('player_position_history')
        .select('*', { count: 'exact', head: true });

      if (countError) throw countError;

      // Get records by user
      const { data: userStats, error: userStatsError } = await supabase
        .from('player_position_history')
        .select('user_id')
        .order('user_id');

      if (userStatsError) throw userStatsError;

      // Count unique users
      const uniqueUsers = new Set(userStats?.map(s => s.user_id) || []).size;

      // Get date range
      const { data: dateRange, error: dateError } = await supabase
        .from('player_position_history')
        .select('game_date')
        .order('game_date', { ascending: true })
        .limit(1);

      const { data: latestDate, error: latestError } = await supabase
        .from('player_position_history')
        .select('game_date')
        .order('game_date', { ascending: false })
        .limit(1);

      if (dateError || latestError) throw dateError || latestError;

      setStats({
        totalRecords: totalRecords || 0,
        uniqueUsers,
        oldestRecord: dateRange?.[0]?.game_date || null,
        newestRecord: latestDate?.[0]?.game_date || null
      });

    } catch (error) {
      console.error('Error loading stats:', error);
      toast.error('Failed to load statistics');
    } finally {
      setLoadingStats(false);
    }
  };

  // Reset player statistics
  const handleResetStats = async () => {
    setIsResetting(true);
    try {
      let query = supabase.from('player_position_history').delete();

      if (resetType === "team" && targetId) {
        // Get players for the team
        const { data: players, error: playersError } = await supabase
          .from('players')
          .select('id')
          .eq('team_id', targetId);

        if (playersError) throw playersError;

        const playerIds = players?.map(p => p.id) || [];
        if (playerIds.length === 0) {
          toast.error('No players found for this team');
          return;
        }

        query = query.in('player_id', playerIds);
      } else if (resetType === "user" && targetId) {
        query = query.eq('user_id', targetId);
      } else if (resetType === "all") {
        // Delete all records - need to add a condition
        query = query.neq('id', '00000000-0000-0000-0000-000000000000');
      }

      const { error } = await query;

      if (error) throw error;

      toast.success('Player statistics have been reset successfully');
      setShowResetDialog(false);
      setTargetId("");
      loadStats(); // Reload stats

    } catch (error) {
      console.error('Error resetting stats:', error);
      toast.error('Failed to reset statistics');
    } finally {
      setIsResetting(false);
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">System Maintenance</h1>
          <p className="text-muted-foreground">Manage system data and perform maintenance tasks</p>
        </div>

        {/* Statistics Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="w-5 h-5" />
              Player Statistics Database
            </CardTitle>
            <CardDescription>
              Current state of player position history data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {!stats && !loadingStats && (
                <Button onClick={loadStats} variant="outline">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Load Statistics
                </Button>
              )}

              {loadingStats && <p>Loading statistics...</p>}

              {stats && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Total Records</p>
                    <p className="text-2xl font-bold">{stats.totalRecords.toLocaleString()}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Unique Users</p>
                    <p className="text-2xl font-bold">{stats.uniqueUsers}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Oldest Record</p>
                    <p className="text-sm font-medium">
                      {stats.oldestRecord ? new Date(stats.oldestRecord).toLocaleDateString() : 'N/A'}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Newest Record</p>
                    <p className="text-sm font-medium">
                      {stats.newestRecord ? new Date(stats.newestRecord).toLocaleDateString() : 'N/A'}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Reset Player Stats */}
        <Card className="border-orange-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-700">
              <AlertTriangle className="w-5 h-5" />
              Reset Player Statistics
            </CardTitle>
            <CardDescription>
              Clear player position history and playing time data. This action cannot be undone.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Reset Scope</Label>
                <Select value={resetType} onValueChange={(value: any) => setResetType(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statistics (Entire System)</SelectItem>
                    <SelectItem value="team">Specific Team</SelectItem>
                    <SelectItem value="user">Specific User</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {(resetType === "team" || resetType === "user") && (
                <div className="space-y-2">
                  <Label>{resetType === "team" ? "Team ID" : "User ID"}</Label>
                  <Input
                    placeholder={`Enter ${resetType} ID`}
                    value={targetId}
                    onChange={(e) => setTargetId(e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground">
                    You can find {resetType} IDs in the {resetType === "team" ? "Teams" : "Users"} admin page
                  </p>
                </div>
              )}

              <Button
                variant="destructive"
                onClick={() => setShowResetDialog(true)}
                disabled={!resetType || ((resetType === "team" || resetType === "user") && !targetId)}
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Reset Statistics
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Other maintenance tasks can be added here */}
        <Card>
          <CardHeader>
            <CardTitle>Other Maintenance Tasks</CardTitle>
            <CardDescription>
              Additional system maintenance options will be added here
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">Coming soon...</p>
          </CardContent>
        </Card>
      </div>

      {/* Reset Confirmation Dialog */}
      <AlertDialog open={showResetDialog} onOpenChange={setShowResetDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription className="space-y-2">
              <p>This action cannot be undone. This will permanently delete:</p>
              {resetType === "all" && (
                <p className="font-semibold text-red-600">
                  ALL player statistics for ALL users in the entire system!
                </p>
              )}
              {resetType === "team" && (
                <p className="font-semibold text-orange-600">
                  All player statistics for team ID: {targetId}
                </p>
              )}
              {resetType === "user" && (
                <p className="font-semibold text-orange-600">
                  All player statistics for user ID: {targetId}
                </p>
              )}
              <p>Players will lose all their playing time and position distribution data.</p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isResetting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleResetStats}
              disabled={isResetting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isResetting ? "Resetting..." : "Yes, reset statistics"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </AdminLayout>
  );
};

export default AdminMaintenance;
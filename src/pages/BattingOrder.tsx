
import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useTeam } from "@/contexts/TeamContext";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { Circle, ListOrdered, Shuffle, X, Star } from "lucide-react";

const BattingOrder = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const lineupId = new URLSearchParams(location.search).get("lineupId");
  const { players, lineups, updateLineup, getAvailablePlayers, getDefaultBattingOrder } = useTeam();

  const [battingOrder, setBattingOrder] = useState<string[]>([]);
  const [availablePlayers, setAvailablePlayers] = useState<string[]>([]);
  const [lineup, setLineup] = useState<any>(null);

  useEffect(() => {
    if (!lineupId) return;

    // Find the lineup
    const foundLineup = lineups.find(l => l.id === lineupId);
    if (!foundLineup) {
      toast.error("Lineup not found");
      navigate("/");
      return;
    }

    console.log("🏏 BattingOrder: Found lineup:", foundLineup.name);
    console.log("🏏 BattingOrder: Lineup attendance data:", foundLineup.attendance);

    setLineup(foundLineup);

    // Get player names from available players (this should filter by attendance)
    const availablePlayers = getAvailablePlayers(foundLineup);
    console.log("🏏 BattingOrder: Available players from getAvailablePlayers:", availablePlayers.map(p => p.name));

    const playerNames = availablePlayers.map(p => p.name);
    setAvailablePlayers(playerNames);

    // Initialize batting order with empty slots for all available players
    const initialBattingOrder = Array(playerNames.length).fill("");

    // Set existing batting order if available
    if (foundLineup.battingOrder && foundLineup.battingOrder.length > 0) {
      foundLineup.battingOrder.forEach((playerId: string, index: number) => {
        if (index < initialBattingOrder.length) {
          // Convert player ID to player name for display
          const player = availablePlayers.find(p => p.id === playerId);
          initialBattingOrder[index] = player ? player.name : playerId;
        }
      });
    }

    setBattingOrder(initialBattingOrder);
    console.log("🏏 BattingOrder: Final player names for dropdowns:", playerNames);
  }, [lineupId, lineups, players, navigate, getAvailablePlayers]);

  const handlePlayerSelect = (position: number, playerName: string) => {
    setBattingOrder(prev => {
      const newOrder = [...prev];
      newOrder[position] = playerName;
      return newOrder;
    });
  };

  const handleSaveBattingOrder = () => {
    if (!lineupId || !lineup) return;

    // Filter out empty slots
    const filteredOrder = battingOrder.filter(name => name !== "");

    if (filteredOrder.length === 0) {
      toast.error("Please select at least one player for the batting order");
      return;
    }

    // Convert player names back to player IDs for storage
    const availablePlayersData = getAvailablePlayers(lineup);
    const battingOrderIds = filteredOrder.map(playerName => {
      const player = availablePlayersData.find(p => p.name === playerName);
      return player ? player.id : playerName;
    });

    // Create a deep copy of the lineup to avoid reference issues
    const updatedLineup = JSON.parse(JSON.stringify(lineup));

    // Update the lineup with the new batting order (using player IDs)
    updatedLineup.battingOrder = battingOrderIds;

    // Save the updated lineup
    updateLineup(updatedLineup);

    toast.success("Batting order saved");

    // Navigate to view the lineup
    navigate(`/view-lineup/${lineupId}`);
  };

  const isPlayerUsedElsewhere = (playerName: string, currentPosition: number) => {
    return battingOrder.some((name, i) => name === playerName && i !== currentPosition);
  };

  const getSelectOrdinal = (index: number) => {
    if (index === 0) return "1st";
    if (index === 1) return "2nd";
    if (index === 2) return "3rd";
    return `${index + 1}th`;
  };

  // Function to generate a random batting order
  const generateRandomBattingOrder = () => {
    // Create a copy of available players
    const shuffledPlayers = [...availablePlayers];

    // Shuffle the array using Fisher-Yates algorithm
    for (let i = shuffledPlayers.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffledPlayers[i], shuffledPlayers[j]] = [shuffledPlayers[j], shuffledPlayers[i]];
    }

    // Create a completely new batting order with the shuffled players
    const newBattingOrder = Array(availablePlayers.length).fill("");
    shuffledPlayers.forEach((player, index) => {
      newBattingOrder[index] = player;
    });

    // Set the new batting order, completely replacing the old one
    setBattingOrder(newBattingOrder);
    toast.success("Random batting order generated");
  };

  // Function to clear the batting order
  const clearBattingOrder = () => {
    // Create a completely new empty array with the same length
    const emptyOrder = Array(availablePlayers.length).fill("");
    setBattingOrder(emptyOrder);
    toast.success("Batting order cleared");
  };

  // Function to apply default batting order
  const applyDefaultBattingOrder = () => {
    const defaultOrder = getDefaultBattingOrder();
    
    if (!defaultOrder || defaultOrder.length === 0) {
      toast.error("No default batting order has been set. Go to Team Roster to set one.");
      return;
    }

    // Convert default order (player IDs) to player names, filtering by attendance
    const attendingPlayerIds = getAvailablePlayers(lineup).map(p => p.id);
    
    const filteredDefaultOrder: string[] = [];
    
    // Go through default order and add only attending players
    defaultOrder.forEach(playerId => {
      if (attendingPlayerIds.includes(playerId)) {
        const player = players.find(p => p.id === playerId);
        if (player) {
          filteredDefaultOrder.push(player.name);
        }
      }
    });

    if (filteredDefaultOrder.length === 0) {
      toast.error("No players from your default batting order are attending this game.");
      return;
    }

    // Create new batting order array
    const newBattingOrder = Array(availablePlayers.length).fill("");
    
    // Fill in the default order players first
    filteredDefaultOrder.forEach((playerName, index) => {
      if (index < newBattingOrder.length) {
        newBattingOrder[index] = playerName;
      }
    });

    // Fill remaining slots with other attending players not in default order
    const remainingPlayers = availablePlayers.filter(name => !filteredDefaultOrder.includes(name));
    let remainingIndex = filteredDefaultOrder.length;
    
    remainingPlayers.forEach(playerName => {
      if (remainingIndex < newBattingOrder.length) {
        newBattingOrder[remainingIndex] = playerName;
        remainingIndex++;
      }
    });

    setBattingOrder(newBattingOrder);
    toast.success(`Applied default batting order! ${filteredDefaultOrder.length} players from your preset order.`);
  };

  if (!lineup) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>;
  }

  return (
    <div className="min-h-screen flex flex-col bg-baseball-tan/20">
      <Header title="Fill Out Your Batting Order" showBack backLink={`/view-lineup/${lineupId}`} />

      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto bg-white rounded-lg shadow-lg p-8 border-t-4 border-baseball-green">
          <div className="flex items-center gap-2 mb-6">
            <ListOrdered className="text-baseball-navy" size={24} />
            <h2 className="text-xl font-bold text-baseball-navy">Batting Order for {lineup.name}</h2>
          </div>

          <div className="bg-baseball-lightblue p-4 rounded-md mb-8">
            <ul className="list-disc pl-6 space-y-2">
              <li>Players are listed alphabetically in each dropdown menu.</li>
              <li>&#123;Brackets&#125; show that a player has already been used for another batting order position (but may still be selected).</li>
              <li>Only players marked as present for this game are available for selection.</li>
            </ul>

            <div className="mt-4 flex flex-wrap justify-center gap-4">
              <Button
                variant="outline"
                onClick={applyDefaultBattingOrder}
                className="bg-white border-yellow-500 text-yellow-600 hover:bg-yellow-50"
              >
                <Star className="mr-2 h-4 w-4" />
                Use Default Order
              </Button>

              <Button
                variant="outline"
                onClick={generateRandomBattingOrder}
                className="bg-white border-baseball-green text-baseball-green hover:bg-baseball-green/10"
              >
                <Shuffle className="mr-2 h-4 w-4" />
                Generate Random Order
              </Button>

              <Button
                variant="outline"
                onClick={clearBattingOrder}
                className="bg-white border-red-500 text-red-500 hover:bg-red-50"
              >
                <X className="mr-2 h-4 w-4" />
                Clear All Selections
              </Button>
            </div>
          </div>

          <div className="space-y-4">
            {battingOrder.map((selectedPlayer, index) => (
              <div
                key={index}
                className={`grid grid-cols-3 items-center rounded-md overflow-hidden ${
                  index % 2 === 0 ? 'bg-baseball-lightgreen' : 'bg-baseball-lightblue'
                }`}
              >
                <div className="font-bold p-3 flex items-center">
                  <Circle className="mr-2 h-4 w-4 text-baseball-green" />
                  <span>Batting {getSelectOrdinal(index)}:</span>
                </div>
                <div className="col-span-2 p-2">
                  <Select
                    value={selectedPlayer || "none"}
                    onValueChange={(value) => handlePlayerSelect(index, value === "none" ? "" : value)}
                  >
                    <SelectTrigger className="h-9 bg-white border-baseball-green/30">
                      <SelectValue placeholder={`Choose a player to bat ${getSelectOrdinal(index)}`} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">-- Select Player --</SelectItem>
                      {availablePlayers.sort().map(player => (
                        <SelectItem
                          key={player}
                          value={player}
                        >
                          {isPlayerUsedElsewhere(player, index) ? `{${player}}` : player}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            ))}

            <div className="flex justify-center mt-10">
              <Button
                onClick={handleSaveBattingOrder}
                className="bg-baseball-green hover:bg-baseball-green/90 text-white"
              >
                <Circle className="mr-2 h-4 w-4" />
                Add Batting Order to Lineup
              </Button>
            </div>

            <div className="mt-8 text-center">
              <Button variant="link" onClick={() => navigate(`/view-lineup/${lineupId}`)}>
                Return to the fielding lineup
              </Button>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default BattingOrder;

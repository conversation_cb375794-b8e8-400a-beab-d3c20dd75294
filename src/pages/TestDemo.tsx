import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';

const TestDemo = () => {
  const [status, setStatus] = useState('');
  const [demoData, setDemoData] = useState<any>(null);

  const testDemoData = async () => {
    try {
      setStatus('Testing demo data...');
      
      const DEMO_TEAM_ID = '83bd9832-f5db-4c7d-b234-41fd38f90007';
      
      // Test team
      const { data: team, error: teamError } = await supabase
        .from('teams')
        .select('*')
        .eq('id', DEMO_TEAM_ID)
        .single();
        
      if (teamError) {
        setStatus(`Team error: ${teamError.message}`);
        return;
      }
      
      // Test players
      const { data: players, error: playersError } = await supabase
        .from('players')
        .select('*')
        .eq('team_id', DEMO_TEAM_ID);
        
      if (playersError) {
        setStatus(`Players error: ${playersError.message}`);
        return;
      }
      
      // Test lineups
      const { data: lineups, error: lineupsError } = await supabase
        .from('lineups')
        .select('*')
        .eq('team_id', DEMO_TEAM_ID);
        
      if (lineupsError) {
        setStatus(`Lineups error: ${lineupsError.message}`);
        return;
      }
      
      setDemoData({
        team,
        players: players || [],
        lineups: lineups || []
      });
      
      setStatus(`Success! Team: ${team?.name}, Players: ${players?.length}, Lineups: ${lineups?.length}`);
      
    } catch (error) {
      setStatus(`Error: ${error}`);
    }
  };

  const testDemoLogin = () => {
    // Set demo mode flags
    localStorage.setItem('demo_mode', 'true');
    localStorage.setItem('current_team_id', '83bd9832-f5db-4c7d-b234-41fd38f90007');
    localStorage.setItem('demo_user_id', '212e5e06-9dd0-4d56-89d2-69915b205b53');
    localStorage.setItem('email', '<EMAIL>');
    localStorage.setItem('teamname', 'Demo Softball Team');
    
    setStatus('Demo mode flags set. Redirecting to dashboard...');
    
    setTimeout(() => {
      window.location.href = '/dashboard';
    }, 1000);
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Demo Mode Test</h1>
      
      <div className="space-y-4">
        <Button onClick={testDemoData}>Test Demo Data Access</Button>
        <Button onClick={testDemoLogin} className="ml-4">Test Demo Login</Button>
      </div>
      
      <div className="mt-4 p-4 bg-gray-100 rounded">
        <p><strong>Status:</strong> {status}</p>
      </div>
      
      {demoData && (
        <div className="mt-4 p-4 bg-blue-50 rounded">
          <h3 className="font-bold">Demo Data:</h3>
          <p>Team: {demoData.team?.name}</p>
          <p>Players: {demoData.players?.length}</p>
          <p>Lineups: {demoData.lineups?.length}</p>
          
          <h4 className="font-bold mt-2">Players:</h4>
          <ul className="list-disc list-inside">
            {demoData.players?.map((player: any) => (
              <li key={player.id}>{player.name}</li>
            ))}
          </ul>
          
          <h4 className="font-bold mt-2">Lineups:</h4>
          <ul className="list-disc list-inside">
            {demoData.lineups?.map((lineup: any) => (
              <li key={lineup.id}>{lineup.name} - {lineup.game_date}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default TestDemo;

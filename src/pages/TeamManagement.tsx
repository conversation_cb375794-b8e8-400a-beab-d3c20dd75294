import { useState } from "react";
import { useTeam, Team } from "@/contexts/TeamContext";
import { useAuth } from "@/contexts/AuthContext";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { Users, Plus, Trash, Edit, Check, X, Star } from "lucide-react";
import { useOptimisticUpdate } from "@/hooks/useOptimisticUpdate";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

const TeamManagement = () => {
  const { teams, currentTeamId, setCurrentTeamId, addTeam, removeTeam, updateTeam, currentTeam, setTeamName } = useTeam();
  const { user } = useAuth();

  const [newTeamName, setNewTeamName] = useState("");
  const [editingTeamId, setEditingTeamId] = useState<string | null>(null);
  const [editingTeamName, setEditingTeamName] = useState("");
  const [teamToDelete, setTeamToDelete] = useState<string | null>(null);
  const [defaultTeamId, setDefaultTeamId] = useState<string>(() => localStorage.getItem('default_team_id') || "");

  const handleAddTeam = () => {
    if (!newTeamName.trim()) {
      toast.error("Please enter a team name");
      return;
    }

    const newTeamId = addTeam(newTeamName);
    setNewTeamName("");
    toast.success(`Team "${newTeamName}" created successfully`);

    // Switch to the new team
    setCurrentTeamId(newTeamId);
  };

  const handleStartEdit = (team: Team) => {
    setEditingTeamId(team.id);
    setEditingTeamName(team.name);
  };

  const handleCancelEdit = () => {
    setEditingTeamId(null);
    setEditingTeamName("");
  };

  const handleSaveEdit = async (team: Team) => {
    if (!editingTeamName.trim()) {
      toast.error("Team name cannot be empty");
      return;
    }

    console.log(`TeamManagement: Saving team name change from "${team.name}" to "${editingTeamName}"`);

    // Apply optimistic update immediately
    const originalName = team.name;
    const optimisticTeam = { ...team, name: editingTeamName };
    
    // Update UI immediately
    const updatedTeams = teams.map(t => t.id === team.id ? optimisticTeam : t);
    // Note: We can't directly update teams from here, but we'll handle rollback on error
    
    setEditingTeamId(null); // Close edit mode immediately for better UX

    try {
      // For Noah's account, use setTeamName which has special handling
      if (user?.email === '<EMAIL>') {
        console.log("TeamManagement: Noah account detected, using setTeamName for better persistence");

        // First update the current team ID to ensure we're editing the right team
        setCurrentTeamId(team.id);

        // Then use setTeamName which has special handling for Noah's account
        await setTeamName(editingTeamName);
        toast.success(`Team renamed to "${editingTeamName}"`);
      } else {
        // For other users, use the normal updateTeam function
        await updateTeam(optimisticTeam);
        toast.success(`Team renamed to "${editingTeamName}"`);
      }
    } catch (error) {
      console.error("TeamManagement: Error updating team name:", error);
      // Revert optimistic update
      setEditingTeamId(team.id);
      setEditingTeamName(originalName);
      toast.error("Failed to update team name - changes reverted");
    }
  };

  const handleDeleteTeam = (teamId: string) => {
    setTeamToDelete(teamId);
  };

  const confirmDeleteTeam = async () => {
    if (!teamToDelete) return;

    try {
      await removeTeam(teamToDelete);
      setTeamToDelete(null);
      // Success toast will be shown by removeTeam function
    } catch (error) {
      console.error("Error in confirmDeleteTeam:", error);
      // Error toast will be shown by removeTeam function
    }
  };

  const handleSelectTeam = (teamId: string) => {
    setCurrentTeamId(teamId);
    toast.success("Switched to selected team");
  };

  const handleSetDefaultTeam = (teamId: string) => {
    localStorage.setItem('default_team_id', teamId);
    setDefaultTeamId(teamId);
    toast.success("Default team set! This team will be selected when you log in.");
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header title="Team Management" showBack backLink="/dashboard" />

      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <Card className="mb-8 border-t-4 border-baseball-green">
            <CardHeader className="bg-baseball-navy text-white">
              <CardTitle className="flex items-center gap-2">
                <Plus size={20} />
                Create New Team
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="flex gap-4">
                <Input
                  placeholder="Enter team name"
                  value={newTeamName}
                  onChange={(e) => setNewTeamName(e.target.value)}
                  className="flex-1"
                />
                <Button onClick={handleAddTeam} variant="baseball">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Team
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card className="border-t-4 border-baseball-green">
            <CardHeader className="bg-baseball-navy text-white">
              <CardTitle className="flex items-center gap-2">
                <Users size={20} />
                Your Teams
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              {teams.length === 0 ? (
                <p className="text-center py-4 text-gray-500">
                  You don't have any teams yet. Create your first team above.
                </p>
              ) : (
                <div className="space-y-4">
                  {teams.map((team) => (
                    <div
                      key={team.id}
                      className={`flex items-center justify-between p-4 rounded-md ${
                        team.id === currentTeamId
                          ? 'bg-baseball-lightgreen border border-baseball-green'
                          : 'bg-gray-100 hover:bg-gray-200'
                      }`}
                    >
                      <div className="flex-1">
                        {editingTeamId === team.id ? (
                          <div className="flex gap-2">
                            <Input
                              value={editingTeamName}
                              onChange={(e) => setEditingTeamName(e.target.value)}
                              className="flex-1"
                              autoFocus
                            />
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleSaveEdit(team)}
                            >
                              <Check className="h-4 w-4 text-green-600" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={handleCancelEdit}
                            >
                              <X className="h-4 w-4 text-red-600" />
                            </Button>
                          </div>
                        ) : (
                          <div className="flex items-center">
                            <span className="font-medium text-lg">{team.name}</span>
                            {team.id === currentTeamId && (
                              <span className="ml-2 px-2 py-1 text-xs bg-baseball-green text-white rounded-full">
                                Current
                              </span>
                            )}
                            {team.id === defaultTeamId && (
                              <Star className="ml-2 h-4 w-4 text-yellow-500 fill-yellow-500" />
                            )}
                          </div>
                        )}
                      </div>

                      <div className="flex gap-2">
                        {team.id !== currentTeamId && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleSelectTeam(team.id)}
                          >
                            Select
                          </Button>
                        )}
                        
                        {team.id !== defaultTeamId && (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleSetDefaultTeam(team.id)}
                            title="Set as default team"
                          >
                            <Star className="h-4 w-4" />
                          </Button>
                        )}

                        {editingTeamId !== team.id && (
                          <>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => handleStartEdit(team)}
                            >
                              <Edit className="h-4 w-4 text-baseball-navy" />
                            </Button>

                            {teams.length > 1 && (
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleDeleteTeam(team.id)}
                              >
                                <Trash className="h-4 w-4 text-red-600" />
                              </Button>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>

      <Footer />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!teamToDelete} onOpenChange={() => setTeamToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this team and all its lineups. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteTeam}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default TeamManagement;

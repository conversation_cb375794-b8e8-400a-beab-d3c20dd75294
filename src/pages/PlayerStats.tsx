import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useTeam, Player } from "@/contexts/TeamContext";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { <PERSON><PERSON><PERSON>, PieChart, Calendar, Clock } from "lucide-react";
import { getPlayerPositionHistory } from "@/lib/utils-enhanced";

// Define the stats interface
interface PlayerStats {
  totalInnings: number;
  fieldInnings: number;
  benchInnings: number;
  positionCounts: Record<string, number>;
  fieldPercentage: number;
  benchPercentage: number;
}

const PlayerStats = () => {
  const { playerId } = useParams<{ playerId: string }>();
  const navigate = useNavigate();
  const { players, currentTeam } = useTeam();
  
  const [player, setPlayer] = useState<Player | null>(null);
  const [stats, setStats] = useState<PlayerStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState<"season" | "month" | "lastGames">("season");
  const [positionColors, setPositionColors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (!playerId) {
      toast.error("No player ID provided");
      navigate("/dashboard");
      return;
    }

    // Find the player in the current team
    const foundPlayer = players.find(p => p.id === playerId);
    if (foundPlayer) {
      setPlayer(foundPlayer);
    } else {
      toast.error("Player not found");
      navigate("/dashboard");
      return;
    }

    // Load player stats
    loadPlayerStats(playerId, dateRange);
  }, [playerId, players, navigate, dateRange]);

  const loadPlayerStats = async (playerId: string, range: "season" | "month" | "lastGames") => {
    setLoading(true);
    try {
      // Calculate date range
      let startDate: string | undefined;
      const endDate = new Date().toISOString().split('T')[0]; // Today
      
      if (range === "month") {
        // Last 30 days
        const date = new Date();
        date.setDate(date.getDate() - 30);
        startDate = date.toISOString().split('T')[0];
      } else if (range === "lastGames") {
        // Last 5 games (we'll approximate this as 14 days)
        const date = new Date();
        date.setDate(date.getDate() - 14);
        startDate = date.toISOString().split('T')[0];
      }
      // For "season", startDate remains undefined to get all data

      // Fetch position history
      const history = await getPlayerPositionHistory(playerId, startDate, endDate);
      
      // Calculate percentages
      const fieldPercentage = history.totalInnings > 0 
        ? (history.fieldInnings / history.totalInnings) * 100 
        : 0;
      
      const benchPercentage = history.totalInnings > 0 
        ? (history.benchInnings / history.totalInnings) * 100 
        : 0;

      // Generate colors for positions
      const colors: Record<string, string> = {};
      const positions = Object.keys(history.positionCounts);
      
      // Field positions
      const fieldPositions = positions.filter(p => !p.startsWith('bench'));
      fieldPositions.forEach((pos, index) => {
        // Generate colors based on position type
        if (pos === 'pitcher') colors[pos] = '#e63946'; // Red
        else if (pos === 'catcher') colors[pos] = '#1d3557'; // Dark blue
        else if (['firstBase', 'secondBase', 'shortstop', 'thirdBase'].includes(pos)) 
          colors[pos] = '#457b9d'; // Medium blue
        else colors[pos] = '#a8dadc'; // Light blue
      });
      
      // Bench positions
      const benchPositions = positions.filter(p => p.startsWith('bench'));
      benchPositions.forEach(pos => {
        colors[pos] = '#e9c46a'; // Yellow
      });
      
      setPositionColors(colors);
      
      // Set stats
      setStats({
        ...history,
        fieldPercentage,
        benchPercentage
      });
    } catch (error) {
      console.error("Error loading player stats:", error);
      toast.error("Failed to load player statistics");
    } finally {
      setLoading(false);
    }
  };

  if (loading || !player) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header title="Player Statistics" showBack backLink="/dashboard" />
        <main className="flex-1 container mx-auto px-4 py-8">
          <div className="flex justify-center items-center h-full">
            <p>Loading player statistics...</p>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header title={`${player.name}'s Statistics`} showBack backLink="/dashboard" />
      
      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="mb-6">
          <Tabs defaultValue="season" onValueChange={(value) => setDateRange(value as any)}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="season">Full Season</TabsTrigger>
              <TabsTrigger value="month">Last 30 Days</TabsTrigger>
              <TabsTrigger value="lastGames">Last 5 Games</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
        
        {stats ? (
          <div className="grid md:grid-cols-2 gap-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <Clock className="mr-2 h-5 w-5" />
                  Playing Time
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Total Innings</p>
                    <p className="text-2xl font-bold">{stats.totalInnings}</p>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Field</p>
                      <p className="text-xl font-semibold">{stats.fieldInnings} ({stats.fieldPercentage.toFixed(1)}%)</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Bench</p>
                      <p className="text-xl font-semibold">{stats.benchInnings} ({stats.benchPercentage.toFixed(1)}%)</p>
                    </div>
                  </div>
                  
                  {/* Simple bar chart */}
                  <div className="h-8 bg-gray-100 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-baseball-green" 
                      style={{ width: `${stats.fieldPercentage}%` }}
                    ></div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <PieChart className="mr-2 h-5 w-5" />
                  Position Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {Object.entries(stats.positionCounts)
                    .sort(([, countA], [, countB]) => countB - countA)
                    .map(([position, count]) => (
                      <div key={position} className="flex items-center">
                        <div 
                          className="w-3 h-3 rounded-full mr-2" 
                          style={{ backgroundColor: positionColors[position] || '#ccc' }}
                        ></div>
                        <span className="flex-1">{position.startsWith('bench') 
                          ? `Bench ${position.replace('bench', '')}` 
                          : position}</span>
                        <span className="font-semibold">{count} innings</span>
                      </div>
                    ))
                  }
                </div>
              </CardContent>
            </Card>
          </div>
        ) : (
          <div className="text-center py-8">
            <p>No statistics available for this player.</p>
            <p className="text-sm text-muted-foreground mt-2">
              Statistics will appear after the player participates in games.
            </p>
          </div>
        )}
      </main>
      
      <Footer />
    </div>
  );
};

export default PlayerStats;

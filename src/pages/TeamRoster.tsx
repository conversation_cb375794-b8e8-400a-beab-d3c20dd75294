import { useState, useEffect, useC<PERSON>back, useRef } from "react";
import { useTeam, Player, PositionPreference, PositionPreferenceWithRank, TeamRolePreferences } from "@/contexts/TeamContext";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate, Link } from "react-router-dom";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import ReturnToMenuButton from "@/components/ReturnToMenuButton";
import TeamRoleManager from "@/components/TeamRoleManager";
import DefaultBattingOrderManager from "@/components/DefaultBattingOrderManager";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { POSITION_OPTIONS, generateId } from "@/lib/utils-enhanced";
import { Circle, Loader2, <PERSON><PERSON><PERSON>, Trash2, Plus, Star, Alert<PERSON>riangle, X, Users, Zap, AlertCircle, ListOrdered, HelpCircle, ChevronDown } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { PlayerRowSkeleton } from "@/components/LoadingSkeletons";
import { SwipeablePlayerCard } from "@/components/mobile/SwipeableCard";
import { MobileInput } from "@/components/mobile/MobileInput";
import { PullToRefresh } from "@/components/mobile/PullToRefresh";
import { RosterInstructionsSimple } from "@/components/RosterInstructionsSimple";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

const TeamRoster = () => {
  const { teamName, players, setPlayers, updatePlayer, removePlayer, loading: teamLoading, currentTeamId, rotationRules } = useTeam();
  const { user, loading: authLoading } = useAuth();
  const navigate = useNavigate();
  const [localPlayers, setLocalPlayers] = useState<Player[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isSavingPlayer, setIsSavingPlayer] = useState(false);
  const [showBattingOrderDialog, setShowBattingOrderDialog] = useState(false);
  const isMobile = useIsMobile();
  const nameChangeTimeouts = useRef<{ [key: string]: NodeJS.Timeout }>({});
  const [savingPlayerIds, setSavingPlayerIds] = useState<Set<string>>(new Set());
  const [deletingPlayerIds, setDeletingPlayerIds] = useState<Set<string>>(new Set());

  // Get competitive mode status
  const competitiveMode = rotationRules?.competitiveMode || false;

  // Position preference options
  const positionOptions = [
    { value: 'pitcher', label: 'Pitcher' },
    { value: 'catcher', label: 'Catcher' },
    { value: 'firstBase', label: 'First Base' },
    { value: 'secondBase', label: 'Second Base' },
    { value: 'thirdBase', label: 'Third Base' },
    { value: 'shortstop', label: 'Shortstop' },
    { value: 'leftField', label: 'Left Field' },
    { value: 'centerField', label: 'Center Field' },
    { value: 'rightField', label: 'Right Field' },
    { value: 'leftCenter', label: 'Left Center' },
    { value: 'rightCenter', label: 'Right Center' }
  ];



  // Force reload on mount to ensure we have the latest data
  useEffect(() => {
    console.log("TeamRoster: Component mounted, forcing data refresh");

    // Set a loading state while we fetch data
    setIsLoading(true);

    // Create a timeout to prevent hanging indefinitely
    const loadingTimeout = setTimeout(() => {
      console.log("TeamRoster: Loading timed out, using fallback");
      setIsLoading(false);
    }, 5000);

    return () => clearTimeout(loadingTimeout);
  }, []);

  // Initialize local players when team data is loaded
  useEffect(() => {
    // Don't update while saving individual player to prevent UI flicker
    if (isSavingPlayer) {
      console.log("TeamRoster: Skipping update while saving individual player");
      return;
    }
    
    console.log("TeamRoster: Team data updated", {
      teamName,
      playersCount: players?.length || 0,
      teamLoading,
      authLoading,
      currentTeamId
    });

    // When we have players data and loading is complete, update local state
    if (!teamLoading && !authLoading && players) {
      console.log("TeamRoster: Setting local players", players);

      // Create a deep copy to avoid reference issues
      const playersCopy = JSON.parse(JSON.stringify(players));

      // Migrate old position restrictions to TeamRole 'Never' values
      playersCopy.forEach((player: Player) => {
        // Initialize teamRoles if not present
        if (!player.teamRoles) {
          player.teamRoles = {};
        }

        // NO MORE POSITION RESTRICTIONS MIGRATION!
        // All position preferences are handled through teamRoles only
      });

      // Only add empty slots as needed - be more conservative
      const playersWithNames = playersCopy.filter(p => p.name.trim() !== '');
      
      // STRICT RULE: Only show exactly what's needed
      if (playersWithNames.length === 0) {
        // Brand new team - show 8 empty slots to get started
        playersCopy.length = 0; // Clear array
        for (let i = 0; i < 8; i++) {
          playersCopy.push({
            id: generateId(),
            name: "",
            positionPreferences: {},
            teamRoles: {}
          });
        }
      } else {
        // Team has players - show ONLY named players, NO automatic empty slot
        // User can click "Add Additional Player" if they want more
        const namedPlayersOnly = playersCopy.filter(p => p.name.trim() !== '');
        playersCopy.length = 0; // Clear array
        playersCopy.push(...namedPlayersOnly); // Add only named players
      }

      console.log('TeamRoster: Loading players with teamRoles:', playersCopy.filter(p => p.name).map(p => ({
        name: p.name,
        teamRoles: p.teamRoles
      })));
      
      setLocalPlayers(playersCopy);
      setIsLoading(false);
      // Reset unsaved changes flag when players data updates from database
      setHasUnsavedChanges(false);
    }
  }, [players, teamLoading, authLoading, currentTeamId, isSavingPlayer]);

  const handleNameChange = useCallback((id: string, name: string) => {
    // Find the player to update
    const playerToUpdate = localPlayers.find(p => p.id === id);
    if (!playerToUpdate) return;
    
    // Store previous value for potential rollback
    const previousName = playerToUpdate.name;
    
    // Optimistically update UI immediately
    setLocalPlayers(
      localPlayers.map(player =>
        player.id === id ? { ...player, name } : player
      )
    );
    setHasUnsavedChanges(true);
    
    // If the name is now non-empty but was empty before, show a success feedback
    if (name.trim() && !previousName.trim()) {
      toast.success("Player added to roster", { duration: 2000 });
    }
    
    // Clear any existing timeout for this player
    if (nameChangeTimeouts.current[id]) {
      clearTimeout(nameChangeTimeouts.current[id]);
    }
    
    // Auto-save player name after a delay (debounced)
    if (playerToUpdate.id && !playerToUpdate.id.startsWith('temp-') && name.trim() && updatePlayer) {
      nameChangeTimeouts.current[id] = setTimeout(async () => {
        // Mark player as saving
        setSavingPlayerIds(prev => new Set(prev).add(id));
        
        try {
          // Auto-save the player name
          await updatePlayer({ ...playerToUpdate, name });
          // Show subtle feedback that the change was saved
          toast.success("Player name saved", { duration: 1000 });
        } catch (error) {
          console.error("Error auto-saving player name:", error);
          toast.error("Failed to save player name. Please try saving manually.");
        } finally {
          // Remove from saving set
          setSavingPlayerIds(prev => {
            const newSet = new Set(prev);
            newSet.delete(id);
            return newSet;
          });
        }
        delete nameChangeTimeouts.current[id];
      }, 1500); // Wait 1.5 seconds after user stops typing
    }
  }, [localPlayers, updatePlayer]);


  const handleTeamRoleChange = (
    playerId: string,
    teamRoles: TeamRolePreferences
  ) => {
    console.log(`TeamRoster: handleTeamRoleChange for player ${playerId}`);
    console.log('New teamRoles:', teamRoles);
    
    setLocalPlayers(localPlayers.map(player =>
      player.id === playerId
        ? {
            ...player,
            teamRoles: teamRoles
          }
        : player
    ));
    setHasUnsavedChanges(true);
  };

  const handleStarPlayerChange = (playerId: string, isStarPlayer: boolean) => {
    console.log(`TeamRoster: handleStarPlayerChange for player ${playerId}:`, isStarPlayer);
    
    setLocalPlayers(localPlayers.map(player =>
      player.id === playerId
        ? {
            ...player,
            isStarPlayer: isStarPlayer
          }
        : player
    ));
    setHasUnsavedChanges(true);
  };


  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      // Clear all pending timeouts
      Object.values(nameChangeTimeouts.current).forEach(timeout => clearTimeout(timeout));
    };
  }, []);

  // Save individual player (for Custom Setup popup)
  const handleSaveIndividualPlayer = async (playerId: string) => {
    console.log(`TeamRoster: Saving individual player ${playerId}`);
    
    const player = localPlayers.find(p => p.id === playerId);
    if (!player) {
      toast.error("Player not found");
      return;
    }

    // Log the current state before save
    console.log('TeamRoster: Player state before save:', {
      name: player.name,
      teamRoles: player.teamRoles,
      pitcherRole: player.teamRoles?.pitcher
    });

    if (!updatePlayer) {
      console.error("updatePlayer function not available");
      toast.error("Cannot save player changes");
      return;
    }

    try {
      // Set saving flag to prevent UI updates
      setIsSavingPlayer(true);
      
      // Show saving state
      const toastId = toast.loading(`Saving ${player.name}'s roles...`);
      
      // Create a copy with all current data to ensure nothing is lost
      const playerToSave = {
        ...player,
        teamRoles: { ...player.teamRoles }
      };
      
      console.log('TeamRoster: Saving player with data:', playerToSave);
      
      // Save the player
      await updatePlayer(playerToSave);
      
      // Wait a bit longer for the database to fully process and context to refresh
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setHasUnsavedChanges(false);
      toast.dismiss(toastId);
      toast.success(`${player.name}'s roles saved successfully`);
      
      // Force refresh the specific player in local state with saved data
      setLocalPlayers(prev => prev.map(p => 
        p.id === playerId ? playerToSave : p
      ));
      
      // Reset saving flag after a short delay to allow final update
      setTimeout(() => {
        setIsSavingPlayer(false);
      }, 500);
    } catch (error) {
      console.error("Error saving player:", error);
      toast.dismiss();
      toast.error("Failed to save player changes");
      setIsSavingPlayer(false);
    }
  };

  const handleDeletePlayer = async (id: string) => {
    const playerToDelete = localPlayers.find(p => p.id === id);
    if (!playerToDelete) return;
    
    const isEmptyRow = playerToDelete.name.trim() === "";
    
    if (isEmptyRow) {
      // For empty rows, just remove without confirmation
      const updatedPlayers = localPlayers.filter(p => p.id !== id);
      setLocalPlayers(updatedPlayers);
      setHasUnsavedChanges(true);
      toast.success("Empty row removed");
    } else {
      // For players with names, confirm deletion
      if (confirm(`Are you sure you want to delete ${playerToDelete.name}?`)) {
        // Store the current state for potential rollback
        const previousPlayers = [...localPlayers];
        const playerIndex = localPlayers.findIndex(p => p.id === id);
        
        // Mark player as being deleted for UI feedback
        setDeletingPlayerIds(prev => new Set(prev).add(id));
        
        // Delay the actual removal to show animation
        setTimeout(async () => {
          // Optimistically remove the player from UI
          const updatedPlayers = localPlayers.filter(p => p.id !== id);
          setLocalPlayers(updatedPlayers);
          setHasUnsavedChanges(true);
          
          // Show optimistic success message
          const toastId = toast.success(`${playerToDelete.name} removed from roster`);
          
          try {
            // If the player has a database ID, we should delete from database immediately
            if (playerToDelete.id && !playerToDelete.id.startsWith('temp-') && removePlayer) {
              // Attempt to delete from database
              await removePlayer(playerToDelete.id);
            }
          } catch (error) {
            // On error, rollback the optimistic update
            console.error("Error deleting player:", error);
            toast.dismiss(toastId);
            toast.error(`Failed to remove ${playerToDelete.name}. Please try again.`);
            
            // Restore the previous state
            setLocalPlayers(previousPlayers);
            setHasUnsavedChanges(true);
          } finally {
            // Remove from deleting set
            setDeletingPlayerIds(prev => {
              const newSet = new Set(prev);
              newSet.delete(id);
              return newSet;
            });
          }
        }, 300); // Small delay for animation
      }
    }
  };

  const handleAddPlayer = () => {
    const newPlayer = {
      id: generateId(),
      name: "",
      positionPreferences: {},
      teamRoles: {}
    };

    setLocalPlayers([...localPlayers, newPlayer]);
    setHasUnsavedChanges(true);
    toast.info("New player slot added");
  };

  const handleSave = async () => {
    console.log('TeamRoster.handleSave: Starting save process');
    
    // Show loading state
    setIsLoading(true);

    // Check if we're in demo mode
    const isDemoMode = localStorage.getItem('demo_mode') === 'true';

    // In demo mode, show a message about the demo functionality
    if (isDemoMode) {
      toast.info(
        "Demo mode: Try editing the roster to see how it works. Changes are temporary and won't be permanently saved.",
        { duration: 5000 }
      );
    }

    // Filter out players with empty names
    const validPlayers = localPlayers.filter(p => p.name.trim() !== "");

    // Check if we have at least one player
    if (validPlayers.length === 0) {
      toast.error("Please add at least one player to your roster");
      setIsLoading(false);
      return;
    }

    // Check if any player has a name but it's just whitespace
    const hasInvalidNames = localPlayers.some(p => p.name !== "" && p.name.trim() === "");
    if (hasInvalidNames) {
      toast.error("Some player names contain only spaces. Please fix or remove them.");
      setIsLoading(false);
      return;
    }

    try {
      console.log("TeamRoster: Saving players:", validPlayers);
      console.log("TeamRoster: Player details with teamRoles:", validPlayers.map(p => ({
        name: p.name,
        teamRoles: p.teamRoles,
        pitcher: p.teamRoles?.pitcher
      })));
      
      // Special check for Avalon
      const avalon = validPlayers.find(p => p.name.toLowerCase().includes('avalon'));
      if (avalon) {
        console.log('TeamRoster: Avalon pitcher role before save:', avalon.teamRoles?.pitcher);
      }

      // Save to localStorage first as a backup
      try {
        localStorage.setItem('roster_backup', JSON.stringify(validPlayers));
        localStorage.setItem('roster_backup_timestamp', Date.now().toString());
      } catch (e) {
        console.error("TeamRoster: Failed to save roster backup:", e);
      }

      // Only save players with non-empty names
      await setPlayers(validPlayers);

      // The context will update with the saved players (with proper database IDs)
      // We don't need to manually update localPlayers here - the useEffect will handle it
      
      setHasUnsavedChanges(false);
      toast.success("Team roster saved successfully");
    } catch (error) {
      console.error("Error saving team roster:", error);
      toast.error("Failed to save roster. Please try again.");

      // Try to recover from backup
      try {
        const backup = localStorage.getItem('roster_backup');
        if (backup) {
          const backupPlayers = JSON.parse(backup);

          // Restore the backup properly with empty slots
          const restoredLocalPlayers = [...localPlayers];

          // Clear existing players
          for (let i = 0; i < restoredLocalPlayers.length; i++) {
            restoredLocalPlayers[i] = {
              id: generateId(),
              name: "",
              positionPreferences: {},
              teamRoles: {}
            };
          }

          // Add backup players
          for (let i = 0; i < backupPlayers.length && i < restoredLocalPlayers.length; i++) {
            restoredLocalPlayers[i] = backupPlayers[i];
          }

          setLocalPlayers(restoredLocalPlayers);
          toast.info("Recovered roster from backup");
        }
      } catch (e) {
        console.error("TeamRoster: Failed to recover from backup:", e);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Check if we're in demo mode
  const isDemoMode = localStorage.getItem('demo_mode') === 'true';

  // Show loading state if data is still loading (but not if roster is just empty)
  if (isLoading || teamLoading || authLoading || players === undefined) {
    return (
      <div className="min-h-screen flex flex-col bg-baseball-tan/20">
        <Header title="Fill Out Your Team Roster" showBack backLink="/dashboard" />

        <main className="flex-1 container mx-auto px-4 py-8">
          <div className="bg-white rounded-lg shadow-md p-4 sm:p-6 border-t-4 border-baseball-green">
            <div className="space-y-4">
              {[...Array(12)].map((_, i) => (
                <PlayerRowSkeleton key={i} />
              ))}
            </div>
          </div>
        </main>

        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-baseball-tan/20">
      <Header title="Fill Out Your Team Roster" showBack backLink="/dashboard" />

      {/* Demo Mode Banner */}
      {localStorage.getItem('demo_mode') === 'true' && (
        <div className="bg-blue-50 border-b border-blue-200 py-2">
          <div className="container mx-auto px-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-blue-700">
                <Circle className="h-4 w-4 fill-blue-500" />
                <p className="text-sm font-medium">Demo Mode</p>
                <p className="text-sm hidden md:inline">- Explore all features, changes visible during session but not permanently saved</p>
              </div>
            </div>
          </div>
        </div>
      )}

      <main className="flex-1 container mx-auto px-4 py-6 sm:py-8">
        <div className="bg-white rounded-lg shadow-md p-4 sm:p-6 border-t-4 border-baseball-green">
          <div className="flex items-center gap-2 mb-6">
            <Circle className="text-baseball-green" size={24} />
            <h2 className="text-xl font-bold text-baseball-navy">Team: {teamName || "Loading..."}</h2>
          </div>

          {/* Enhanced roster instructions */}
          <div className="mb-6 space-y-3">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-semibold text-sm text-blue-900 mb-2">Getting Started</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• <strong>Take 5-10 minutes to set this up properly</strong> - it makes all the difference!</li>
                <li>• Add your entire roster below - all players on your team</li>
                <li>• For each player, assign positions using the colored buttons</li>
                <li>• The more positions a player can play, the better the algorithm works</li>
                <li>• Use "Custom Setup" to quickly assign multiple positions at once</li>
                <li>• Set your default batting order (optional but recommended)</li>
                <li>• Configure pitcher settings if you have dedicated pitchers</li>
                <li>• Your rotation settings (competitive/recreational) will determine how lineups are generated</li>
              </ul>
            </div>
            
            <div className="text-sm text-gray-600">
              <p className="mb-2 font-medium">Position Assignment Guide:</p>
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 text-xs">
                <span className="flex items-center gap-1"><span className="inline-block w-3 h-3 rounded-full bg-green-500"></span> <span className="font-medium">Primary</span> = Best position</span>
                <span className="flex items-center gap-1"><span className="inline-block w-3 h-3 rounded-full bg-blue-500"></span> <span className="font-medium">Mix</span> = Plays regularly</span>
                <span className="flex items-center gap-1"><span className="inline-block w-3 h-3 rounded-full bg-yellow-500"></span> <span className="font-medium">Emergency</span> = If needed</span>
                <span className="flex items-center gap-1"><span className="inline-block w-3 h-3 rounded-full bg-red-500"></span> <span className="font-medium">Never</span> = Cannot play</span>
              </div>
            </div>
          </div>

            {/* Mobile-friendly roster layout */}
            {isMobile ? (
              <div className="space-y-4">
                {localPlayers.map((player, index) => (
                  <div 
                    key={player.id} 
                    className={`border rounded-lg p-4 bg-white shadow-sm transition-all duration-300 ${
                      deletingPlayerIds.has(player.id) ? 'opacity-50 scale-95' : 'opacity-100 scale-100'
                    }`}
                  >
                    <div className="flex justify-between items-start mb-3">
                      <h4 className="font-bold text-baseball-navy">Player {index + 1}</h4>
                      {(player.name.trim() !== "" || 
                        (player.name.trim() === "" && localPlayers.filter(p => p.name.trim() === "").length > 1)
                      ) && (
                        <div className="flex gap-2">
                          {player.name.trim() !== "" && (
                            <Link to={`/player-stats/${player.id}`}>
                              <Button variant="outline" size="sm" className="h-8 px-2">
                                <BarChart className="h-4 w-4" />
                              </Button>
                            </Link>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 px-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                            onClick={() => handleDeletePlayer(player.id)}
                            title={player.name.trim() === "" ? "Remove empty row" : `Delete ${player.name}`}
                            disabled={isSavingPlayer || deletingPlayerIds.has(player.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                    </div>

                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium text-gray-700 mb-1 block">Player Name</label>
                        <div className="relative">
                          <Input
                            value={player.name}
                            onChange={(e) => handleNameChange(player.id, e.target.value)}
                            placeholder="Enter player name"
                            className="w-full pr-8"
                          />
                          {savingPlayerIds.has(player.id) && (
                            <div className="absolute right-2 top-1/2 -translate-y-1/2">
                              <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                            </div>
                          )}
                        </div>
                      </div>

                      {player.name.trim() !== "" && (
                        <>
                          <div>
                            <label className="text-sm font-medium text-gray-700 mb-2 block">Position Assignments</label>
                            <TeamRoleManager
                              player={player}
                              onRoleChange={handleTeamRoleChange}
                              competitiveMode={competitiveMode}
                              disabled={player.name.trim() === ""}
                              compact={true}
                              onSave={() => handleSaveIndividualPlayer(player.id)}
                            />
                          </div>
                          
                          {competitiveMode && (
                            <div>
                              <label className="text-sm font-medium text-gray-700 mb-2 block">Star Player</label>
                              <Button
                                variant={player.isStarPlayer ? "default" : "outline"}
                                size="sm"
                                className={`w-full ${
                                  player.isStarPlayer 
                                    ? 'bg-yellow-500 hover:bg-yellow-600 text-white' 
                                    : 'hover:bg-yellow-50 border-yellow-300'
                                }`}
                                onClick={() => handleStarPlayerChange(player.id, !player.isStarPlayer)}
                              >
                                <Star className={`h-4 w-4 mr-2 ${player.isStarPlayer ? 'text-white' : 'text-yellow-500'}`} />
                                {player.isStarPlayer ? 'Star Player' : 'Mark as Star'}
                              </Button>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              /* Desktop table layout */
              <div className="overflow-x-auto">
                <table className="w-full border-collapse min-w-[900px]">
                  <thead>
                    <tr className="bg-baseball-navy text-white">
                      <th className="text-left p-2 rounded-tl-md w-32">Player #</th>
                      <th className="text-left p-2 min-w-[150px]">Player's Name</th>
                      <th className="p-2 min-w-[400px]">Position Assignments</th>
                      {competitiveMode && <th className="p-2 text-center w-20">Star</th>}
                      <th className="p-2">Stats</th>
                      <th className="p-2 rounded-tr-md">Delete</th>
                    </tr>
                  </thead>
                <tbody>
                  {localPlayers.map((player, index) => (
                    <tr 
                      key={player.id} 
                      className={`${index % 2 === 0 ? 'bg-gray-50' : 'bg-white'} transition-all duration-300 ${
                        deletingPlayerIds.has(player.id) ? 'opacity-50' : 'opacity-100'
                      }`}
                    >
                      <td className="p-2 font-bold">{`Player ${index + 1}:`}</td>
                      <td className="p-2">
                        <div className="relative">
                          <Input
                            value={player.name}
                            onChange={(e) => handleNameChange(player.id, e.target.value)}
                            className="h-9 pr-8"
                          />
                          {savingPlayerIds.has(player.id) && (
                            <div className="absolute right-2 top-1/2 -translate-y-1/2">
                              <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="p-2">
                        {player.name.trim() !== "" ? (
                          <TeamRoleManager
                            player={player}
                            onRoleChange={handleTeamRoleChange}
                            competitiveMode={competitiveMode}
                            disabled={player.name.trim() === ""}
                            compact={true}
                            onSave={() => handleSaveIndividualPlayer(player.id)}
                          />
                        ) : (
                          <div className="text-gray-400 text-sm text-center">Enter player name first</div>
                        )}
                      </td>
                      {competitiveMode && (
                        <td className="p-2 text-center">
                          {player.name.trim() !== "" && (
                            <Button
                              variant={player.isStarPlayer ? "default" : "outline"}
                              size="sm"
                              className={`h-9 w-9 p-0 ${
                                player.isStarPlayer 
                                  ? 'bg-yellow-500 hover:bg-yellow-600 text-white' 
                                  : 'hover:bg-yellow-50 border-yellow-300'
                              }`}
                              onClick={() => handleStarPlayerChange(player.id, !player.isStarPlayer)}
                              title={player.isStarPlayer ? "Remove star status" : "Mark as star player"}
                            >
                              <Star className={`h-4 w-4 ${player.isStarPlayer ? 'text-white' : 'text-yellow-500'}`} />
                            </Button>
                          )}
                        </td>
                      )}
                      <td className="p-2 text-center">
                        {player.name.trim() !== "" && (
                          <Link to={`/player-stats/${player.id}`}>
                            <Button variant="outline" size="sm" className="h-9 px-2">
                              <BarChart className="h-4 w-4 mr-1" />
                              Stats
                            </Button>
                          </Link>
                        )}
                      </td>
                      <td className="p-2 text-center">
                        {(player.name.trim() !== "" || 
                          (player.name.trim() === "" && localPlayers.filter(p => p.name.trim() === "").length > 1)
                        ) && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-9 px-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                            onClick={() => handleDeletePlayer(player.id)}
                            title={player.name.trim() === "" ? "Remove empty row" : `Delete ${player.name}`}
                            disabled={isSavingPlayer || deletingPlayerIds.has(player.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
                </table>
              </div>
            )}


            <div className="mt-4 flex justify-center">
              <Button
                onClick={handleAddPlayer}
                variant="outline"
                className="text-baseball-navy border-baseball-navy hover:bg-baseball-navy hover:text-white w-full sm:w-auto"
                data-add-player-button
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Additional Player
              </Button>
            </div>


            <div className="mt-8 space-y-4">
              {/* Primary Save Button */}
              <div className="flex justify-center">
                <Button
                  onClick={handleSave}
                  className={`${hasUnsavedChanges ? 'bg-orange-600 hover:bg-orange-700' : 'bg-baseball-green hover:bg-baseball-green/90'} text-white w-full sm:w-auto min-w-[200px]`}
                  size={isMobile ? "lg" : "default"}
                >
                  <Circle className="mr-2 h-4 w-4" />
                  {hasUnsavedChanges ? 'Save Changes' : 'Save Team Roster'}
                </Button>
              </div>
              
              {/* Secondary Actions */}
              <div className="flex flex-col sm:flex-row justify-center gap-3">
                {/* Default Batting Order Button - show if we have players */}
                {localPlayers.filter(p => p.name.trim() !== '').length >= 8 && (
                  <Button
                    onClick={() => setShowBattingOrderDialog(true)}
                    variant="outline"
                    className="border-baseball-green text-baseball-green hover:bg-baseball-green hover:text-white w-full sm:w-auto"
                    size={isMobile ? "default" : "default"}
                  >
                    <ListOrdered className="mr-2 h-4 w-4" />
                    <span className={isMobile ? "text-sm" : ""}>Default Batting Order</span>
                  </Button>
                )}
                
                {/* Check if we have any eligible pitchers */}
                {localPlayers.some(p => p.teamRoles?.pitcher && p.teamRoles.pitcher !== 'avoid' && p.teamRoles.pitcher !== 'unset') && (
                  <Button
                    onClick={() => navigate('/pitcher-settings')}
                    variant="outline"
                    className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white w-full sm:w-auto"
                    size={isMobile ? "default" : "default"}
                  >
                    <Zap className="mr-2 h-4 w-4" />
                    <span className={isMobile ? "text-sm" : ""}>Pitcher Settings</span>
                  </Button>
                )}
              </div>
            </div>

            {/* Collapsible Help Section */}
            <Collapsible className="mt-8 border rounded-lg">
              <CollapsibleTrigger className="flex items-center justify-between w-full p-4 text-left hover:bg-gray-50 transition-colors">
                <div className="flex items-center gap-2">
                  <HelpCircle className="h-5 w-5 text-gray-500" />
                  <span className="font-medium text-gray-700">Need help? Click for detailed instructions</span>
                </div>
                <ChevronDown className="h-5 w-5 text-gray-500 transition-transform data-[state=open]:rotate-180" />
              </CollapsibleTrigger>
              <CollapsibleContent className="p-4 pt-0 text-sm text-gray-600 space-y-4">
                <div>
                  <h4 className="font-semibold text-gray-700 mb-2">How Position Assignments Work</h4>
                  <p className="mb-2">For each player, you can set their ability level for each position:</p>
                  <ul className="space-y-1 ml-4">
                    <li>• <span className="font-medium text-green-600">Primary</span> - Player's best position(s). They'll play here most often.</li>
                    <li>• <span className="font-medium text-blue-600">In the Mix</span> - Positions they play regularly and competently.</li>
                    <li>• <span className="font-medium text-yellow-600">Emergency Only</span> - Only use if no better options are available.</li>
                    <li>• <span className="font-medium text-red-600">Never</span> - Player cannot or should not play this position.</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold text-gray-700 mb-2">Tips for Success</h4>
                  <ul className="space-y-1 ml-4">
                    <li>• Start by entering all player names first</li>
                    <li>• Click "Customize" next to each player to set their positions</li>
                    <li>• You need at least 8 players to create a lineup</li>
                    <li>• Make sure you have enough players who can pitch and catch</li>
                    <li>• In competitive mode, mark your best players as "Stars"</li>
                  </ul>
                </div>

                {competitiveMode && (
                  <div>
                    <h4 className="font-semibold text-gray-700 mb-2">Competitive Mode Features</h4>
                    <ul className="space-y-1 ml-4">
                      <li>• Mark your best players as "Stars" - they'll get priority for key positions</li>
                      <li>• The algorithm will optimize positioning for the strongest lineup</li>
                      <li>• Players still get fair playing time, but in their best positions</li>
                    </ul>
                  </div>
                )}
              </CollapsibleContent>
            </Collapsible>

          <div className="mt-6 text-center">
            <ReturnToMenuButton />
          </div>
        </div>
      </main>


      {!isMobile && <Footer />}

      {/* Default Batting Order Dialog */}
      <DefaultBattingOrderManager
        isOpen={showBattingOrderDialog}
        onClose={() => setShowBattingOrderDialog(false)}
      />
    </div>
  );
};

export default TeamRoster;

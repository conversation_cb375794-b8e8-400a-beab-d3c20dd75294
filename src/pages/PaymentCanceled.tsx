
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { XCircle } from "lucide-react";

const PaymentCanceled = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="max-w-md w-full">
        <CardHeader className="text-center pb-2">
          <div className="mx-auto w-16 h-16 mb-4 flex items-center justify-center bg-red-100 rounded-full">
            <XCircle className="h-10 w-10 text-red-600" />
          </div>
          <CardTitle className="text-2xl text-baseball-navy">Payment Canceled</CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          <p className="mb-6 text-gray-600">
            Your payment was canceled. No charges have been made to your account.
          </p>
          <p className="mb-10 text-gray-600">
            If you have any questions or need assistance, please don't hesitate to contact our support team.
          </p>
          <div className="space-y-3">
            <Link to="/pricing">
              <Button className="bg-baseball-green hover:bg-baseball-green/90 text-white w-full">
                Try Again
              </Button>
            </Link>
            <Link to="/">
              <Button variant="outline" className="w-full">
                Return to Homepage
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PaymentCanceled;

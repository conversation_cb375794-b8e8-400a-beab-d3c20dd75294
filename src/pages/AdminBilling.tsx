import { useState, useEffect } from "react";
import { supabase } from "@/supabaseClient";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Search, 
  Download, 
  RefreshCw, 
  DollarSign,
  Loader2,
  Calendar,
  Filter,
  Trash2,
  MoreHorizontal
} from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import AdminLayout from "@/components/AdminLayout";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface Transaction {
  id: string;
  userId: string;
  userEmail: string;
  amount: number;
  currency: string;
  status: string;
  date: string;
  stripeId: string | null;
  isManual: boolean;
}

const AdminBilling = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [timeFilter, setTimeFilter] = useState("all");
  const [showAddTransactionDialog, setShowAddTransactionDialog] = useState(false);
  const [showDeleteTransactionDialog, setShowDeleteTransactionDialog] = useState(false);
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false);
  const [selectedTransactionIds, setSelectedTransactionIds] = useState<Set<string>>(new Set());
  const [transactionToDelete, setTransactionToDelete] = useState<{id: string, email: string} | null>(null);
  const [processingAction, setProcessingAction] = useState(false);
  const [stats, setStats] = useState({
    totalRevenue: 0,
    transactionCount: 0,
    averageAmount: 0,
    paidUsers: 0
  });
  
  const [newTransaction, setNewTransaction] = useState({
    userEmail: "",
    amount: 49,
    currency: "usd",
    date: new Date().toISOString().split('T')[0]
  });

  useEffect(() => {
    fetchTransactions();
  }, [timeFilter]);

  const fetchTransactions = async () => {
    setLoading(true);
    try {
      // Build the query - simplified without auth.users join
      let query = supabase
        .from('subscriptions')
        .select('*')
        .order('created_at', { ascending: false });

      // Apply time filter if needed
      if (timeFilter === "month") {
        const oneMonthAgo = new Date();
        oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
        query = query.gte('created_at', oneMonthAgo.toISOString());
      } else if (timeFilter === "week") {
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        query = query.gte('created_at', oneWeekAgo.toISOString());
      } else if (timeFilter === "day") {
        const oneDayAgo = new Date();
        oneDayAgo.setDate(oneDayAgo.getDate() - 1);
        query = query.gte('created_at', oneDayAgo.toISOString());
      }

      const { data, error } = await query;

      if (error) throw error;

      // Get user profiles for emails
      let profileMap = {};
      if (data && data.length > 0) {
        const userIds = [...new Set(data.map(t => t.user_id))];
        const { data: profiles } = await supabase
          .from('profiles')
          .select('id, email')
          .in('id', userIds);
        
        profiles?.forEach(p => {
          profileMap[p.id] = p.email;
        });
      }

      // Format the data
      const formattedTransactions = data?.map(transaction => ({
        id: transaction.id,
        userId: transaction.user_id,
        userEmail: profileMap[transaction.user_id] || 'Unknown',
        amount: (transaction.amount || 0) / 100, // Convert cents to dollars
        currency: transaction.currency?.toUpperCase() || 'USD',
        status: transaction.is_paid ? 'Paid' : 'Pending',
        date: transaction.payment_date 
          ? new Date(transaction.payment_date).toLocaleDateString() 
          : new Date(transaction.created_at).toLocaleDateString(),
        stripeId: transaction.stripe_session_id,
        isManual: !transaction.stripe_session_id
      })) || [];

      setTransactions(formattedTransactions);

      // Calculate stats
      const totalRevenue = formattedTransactions.reduce((sum, t) => sum + (t.status === 'Paid' ? t.amount : 0), 0);
      const paidTransactions = formattedTransactions.filter(t => t.status === 'Paid');
      const paidUserIds = new Set(paidTransactions.map(t => t.userId));
      
      setStats({
        totalRevenue,
        transactionCount: formattedTransactions.length,
        averageAmount: paidTransactions.length ? totalRevenue / paidTransactions.length : 0,
        paidUsers: paidUserIds.size
      });
    } catch (error) {
      console.error("Error fetching transactions:", error);
      toast.error("Failed to load transactions");
    } finally {
      setLoading(false);
    }
  };

  const handleAddTransaction = async () => {
    if (!newTransaction.userEmail || !newTransaction.amount) {
      toast.error("Email and amount are required");
      return;
    }

    setProcessingAction(true);
    try {
      // Find the user by email
      const { data: profiles, error: userError } = await supabase
        .from('profiles')
        .select('id, email, full_name')
        .eq('email', newTransaction.userEmail)
        .single();

      if (userError) throw userError;

      const user = profiles;
      
      if (!user) {
        toast.error("User not found with that email");
        setProcessingAction(false);
        return;
      }

      // Create the transaction
      const { error: transactionError } = await supabase
        .from('subscriptions')
        .insert({
          user_id: user.id,
          is_paid: true,
          amount: newTransaction.amount * 100, // Convert dollars to cents
          currency: newTransaction.currency,
          payment_date: new Date(newTransaction.date).toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (transactionError) throw transactionError;

      // Log admin action
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_id: (await supabase.auth.getUser()).data.user?.id,
          action: 'add_manual_transaction',
          entity_type: 'user',
          entity_id: user.id,
          details: { 
            email: newTransaction.userEmail,
            amount: newTransaction.amount,
            currency: newTransaction.currency
          }
        });

      toast.success("Transaction added successfully");
      setShowAddTransactionDialog(false);
      setNewTransaction({
        userEmail: "",
        amount: 49,
        currency: "usd",
        date: new Date().toISOString().split('T')[0]
      });
      fetchTransactions();
    } catch (error) {
      console.error("Error adding transaction:", error);
      toast.error("Failed to add transaction");
    } finally {
      setProcessingAction(false);
    }
  };

  const handleDeleteTransaction = async () => {
    if (!transactionToDelete) return;

    setProcessingAction(true);
    try {
      const { error } = await supabase
        .from('subscriptions')
        .delete()
        .eq('id', transactionToDelete.id);

      if (error) throw error;

      // Log admin action
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_id: (await supabase.auth.getUser()).data.user?.id,
          action: 'delete_transaction',
          entity_type: 'subscription',
          entity_id: transactionToDelete.id,
          details: { 
            userEmail: transactionToDelete.email
          }
        });

      toast.success("Transaction deleted successfully");
      setShowDeleteTransactionDialog(false);
      setTransactionToDelete(null);
      fetchTransactions();
    } catch (error) {
      console.error("Error deleting transaction:", error);
      toast.error("Failed to delete transaction: " + error.message);
    } finally {
      setProcessingAction(false);
    }
  };

  const handleBulkDeleteTransactions = async () => {
    if (selectedTransactionIds.size === 0) return;

    setProcessingAction(true);
    try {
      const transactionIdsArray = Array.from(selectedTransactionIds);
      
      const confirmDelete = window.confirm(
        `Are you sure you want to delete ${selectedTransactionIds.size} transaction(s)?\n\nThis will permanently delete the billing records. This action cannot be undone!`
      );
      
      if (!confirmDelete) {
        setProcessingAction(false);
        return;
      }

      // Delete each transaction
      const errors = [];
      for (const transactionId of transactionIdsArray) {
        try {
          await supabase.from('subscriptions').delete().eq('id', transactionId);
        } catch (error) {
          errors.push({ transactionId, error });
        }
      }

      // Log admin action
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_id: (await supabase.auth.getUser()).data.user?.id,
          action: 'bulk_delete_transactions',
          entity_type: 'subscription',
          entity_id: transactionIdsArray.join(','),
          details: { 
            count: selectedTransactionIds.size,
            transactionIds: transactionIdsArray,
            errors: errors.length > 0 ? errors : undefined
          }
        });

      if (errors.length > 0) {
        toast.error(`Deleted ${selectedTransactionIds.size - errors.length} transactions. ${errors.length} failed.`);
      } else {
        toast.success(`Successfully deleted ${selectedTransactionIds.size} transaction(s)`);
      }
      
      setSelectedTransactionIds(new Set());
      setShowBulkDeleteDialog(false);
      fetchTransactions();
    } catch (error) {
      console.error("Error in bulk delete:", error);
      toast.error("Failed to delete transactions: " + error.message);
    } finally {
      setProcessingAction(false);
    }
  };

  const toggleTransactionSelection = (transactionId: string) => {
    const newSelection = new Set(selectedTransactionIds);
    if (newSelection.has(transactionId)) {
      newSelection.delete(transactionId);
    } else {
      newSelection.add(transactionId);
    }
    setSelectedTransactionIds(newSelection);
  };

  const toggleSelectAll = () => {
    if (selectedTransactionIds.size === filteredTransactions.length) {
      setSelectedTransactionIds(new Set());
    } else {
      setSelectedTransactionIds(new Set(filteredTransactions.map(t => t.id)));
    }
  };

  const exportTransactions = () => {
    // Create CSV content
    const headers = ["ID", "User", "Amount", "Currency", "Status", "Date", "Stripe ID", "Type"];
    const csvContent = [
      headers.join(","),
      ...transactions.map(t => [
        t.id,
        t.userEmail,
        t.amount,
        t.currency,
        t.status,
        t.date,
        t.stripeId || "N/A",
        t.isManual ? "Manual" : "Stripe"
      ].join(","))
    ].join("\n");

    // Create download link
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `transactions-${new Date().toISOString().split("T")[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const filteredTransactions = transactions.filter(transaction => 
    transaction.userEmail.toLowerCase().includes(searchQuery.toLowerCase()) ||
    transaction.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (transaction.stripeId && transaction.stripeId.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Billing Management</h1>
          <div className="flex gap-2">
            {selectedTransactionIds.size > 0 && (
              <Button 
                variant="destructive" 
                onClick={() => setShowBulkDeleteDialog(true)}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Selected ({selectedTransactionIds.size})
              </Button>
            )}
            <Button variant="outline" onClick={exportTransactions}>
              <Download className="mr-2 h-4 w-4" />
              Export CSV
            </Button>
            <Button onClick={() => setShowAddTransactionDialog(true)}>
              <DollarSign className="mr-2 h-4 w-4" />
              Add Transaction
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-6 flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                <p className="text-3xl font-bold">${stats.totalRevenue.toFixed(2)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-500" />
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-500">Transactions</p>
                <p className="text-3xl font-bold">{stats.transactionCount}</p>
              </div>
              <RefreshCw className="h-8 w-8 text-blue-500" />
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-500">Average Amount</p>
                <p className="text-3xl font-bold">${stats.averageAmount.toFixed(2)}</p>
              </div>
              <Calendar className="h-8 w-8 text-purple-500" />
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-500">Paid Users</p>
                <p className="text-3xl font-bold">{stats.paidUsers}</p>
              </div>
              <DollarSign className="h-8 w-8 text-amber-500" />
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search transactions..."
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <Select value={timeFilter} onValueChange={setTimeFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Time period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="month">Last Month</SelectItem>
                <SelectItem value="week">Last Week</SelectItem>
                <SelectItem value="day">Last 24 Hours</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Transactions Table */}
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedTransactionIds.size === filteredTransactions.length && filteredTransactions.length > 0}
                    onCheckedChange={toggleSelectAll}
                  />
                </TableHead>
                <TableHead>User</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Transaction ID</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-10">
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      Loading transactions...
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredTransactions.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-10">
                    No transactions found
                  </TableCell>
                </TableRow>
              ) : (
                filteredTransactions.map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedTransactionIds.has(transaction.id)}
                        onCheckedChange={() => toggleTransactionSelection(transaction.id)}
                      />
                    </TableCell>
                    <TableCell>{transaction.userEmail}</TableCell>
                    <TableCell>${transaction.amount.toFixed(2)} {transaction.currency}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        transaction.status === 'Paid' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {transaction.status}
                      </span>
                    </TableCell>
                    <TableCell>{transaction.date}</TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        transaction.isManual 
                          ? 'bg-purple-100 text-purple-800' 
                          : 'bg-blue-100 text-blue-800'
                      }`}>
                        {transaction.isManual ? 'Manual' : 'Stripe'}
                      </span>
                    </TableCell>
                    <TableCell className="font-mono text-xs">
                      {transaction.stripeId || transaction.id.substring(0, 8) + '...'}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            className="text-red-600"
                            onClick={() => {
                              setTransactionToDelete({id: transaction.id, email: transaction.userEmail});
                              setShowDeleteTransactionDialog(true);
                            }}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete Transaction
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Add Transaction Dialog */}
      <Dialog open={showAddTransactionDialog} onOpenChange={setShowAddTransactionDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Manual Transaction</DialogTitle>
            <DialogDescription>
              Record a manual payment for a user. This will mark the user as paid.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="userEmail">User Email</Label>
              <Input
                id="userEmail"
                type="email"
                placeholder="<EMAIL>"
                value={newTransaction.userEmail}
                onChange={(e) => setNewTransaction({...newTransaction, userEmail: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="amount">Amount ($)</Label>
              <Input
                id="amount"
                type="number"
                placeholder="49.00"
                value={newTransaction.amount}
                onChange={(e) => setNewTransaction({...newTransaction, amount: parseFloat(e.target.value)})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="currency">Currency</Label>
              <Select 
                value={newTransaction.currency} 
                onValueChange={(value) => setNewTransaction({...newTransaction, currency: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="usd">USD</SelectItem>
                  <SelectItem value="cad">CAD</SelectItem>
                  <SelectItem value="eur">EUR</SelectItem>
                  <SelectItem value="gbp">GBP</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="date">Payment Date</Label>
              <Input
                id="date"
                type="date"
                value={newTransaction.date}
                onChange={(e) => setNewTransaction({...newTransaction, date: e.target.value})}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddTransactionDialog(false)}>Cancel</Button>
            <Button onClick={handleAddTransaction} disabled={processingAction}>
              {processingAction ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Add Transaction"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Transaction Dialog */}
      <Dialog open={showDeleteTransactionDialog} onOpenChange={setShowDeleteTransactionDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Transaction</DialogTitle>
            <DialogDescription>
              This action cannot be undone. This will permanently delete the billing record.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p>Are you sure you want to delete the transaction for <strong>{transactionToDelete?.email}</strong>?</p>
            <p className="text-red-600 mt-3 font-bold">This action cannot be undone!</p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteTransactionDialog(false)}>Cancel</Button>
            <Button variant="destructive" onClick={handleDeleteTransaction} disabled={processingAction}>
              {processingAction ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete Transaction"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk Delete Dialog */}
      <Dialog open={showBulkDeleteDialog} onOpenChange={setShowBulkDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Multiple Transactions</DialogTitle>
            <DialogDescription>
              This action cannot be undone. This will permanently delete the selected billing records.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p>You are about to delete <strong>{selectedTransactionIds.size} transaction(s)</strong>.</p>
            <p className="text-red-600 mt-3 font-bold">This action cannot be undone!</p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowBulkDeleteDialog(false)}>Cancel</Button>
            <Button variant="destructive" onClick={handleBulkDeleteTransactions} disabled={processingAction}>
              {processingAction ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting {selectedTransactionIds.size} transactions...
                </>
              ) : (
                `Delete ${selectedTransactionIds.size} Transactions`
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default AdminBilling;

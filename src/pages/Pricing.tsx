import { useNavigate } from "react-router-dom";
import { Check, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { loadStripe } from '@stripe/stripe-js';

// Initialize Stripe with the publishable key
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);

// Stripe price IDs for annual subscriptions
const PRICE_IDS = {
  starter: 'price_1RUrWQAg0PgXx1Ii6pWKg42U', // Starter Plan - $20/year
  coach: 'price_1RUrbDAg0PgXx1IidQEcna4F',   // Coach Plan - $30/year
  club: 'price_1RUrcZAg0PgXx1IixU44LtYb'     // Club Plan - $500/year
};

const Pricing = () => {
  const navigate = useNavigate();
  const { user, isPaid } = useAuth();

  const handleSubscribe = async (tier: 'starter' | 'coach' | 'club') => {
    // Remove sign-in requirement - let Stripe handle account creation
    // if (!user) {
    //   toast.info("Please sign in to subscribe");
    //   navigate('/sign-in');
    //   return;
    // }

    if (isPaid) {
      toast.info("You already have an active subscription");
      navigate('/dashboard');
      return;
    }

    try {
      // Get the price ID for the selected tier
      const priceId = PRICE_IDS[tier];
      
      // Create checkout session
      const { data, error } = await supabase.functions.invoke('create-payment', {
        body: { 
          priceId,
          tier,
          successUrl: `${window.location.origin}/payment-success`,
          cancelUrl: `${window.location.origin}/payment-canceled`,
        }
      });

      if (error) throw error;
      if (!data?.sessionId) throw new Error('No session ID returned');

      // Redirect to Stripe Checkout
      const stripe = await stripePromise;
      if (!stripe) throw new Error('Stripe failed to load');

      const { error: stripeError } = await stripe.redirectToCheckout({
        sessionId: data.sessionId,
      });

      if (stripeError) throw stripeError;
    } catch (error: any) {
      console.error('Subscription error:', error);
      toast.error(error.message || "Failed to start subscription process");
    }
  };

  const plans = [
    {
      name: "Starter",
      price: "$20",
      period: "/year",
      description: "Perfect for single team coaches",
      tier: 'starter' as const,
      features: [
        { text: "1 team", included: true },
        { text: "Unlimited players per team", included: true },
        { text: "Smart rotation algorithm", included: true },
        { text: "Print & export lineups", included: true },
        { text: "Position preferences", included: true },
        { text: "Basic support", included: true },
        { text: "Multiple teams", included: false },
        { text: "Priority support", included: false },
      ],
      buttonText: isPaid ? "Current Plan" : "Get Started",
      popular: false,
    },
    {
      name: "Coach",
      price: "$30",
      period: "/year",
      description: "Ideal for coaches with multiple teams",
      tier: 'coach' as const,
      features: [
        { text: "5 teams", included: true },
        { text: "Unlimited players per team", included: true },
        { text: "Smart rotation algorithm", included: true },
        { text: "Print & export lineups", included: true },
        { text: "Position preferences", included: true },
        { text: "Email support", included: true },
        { text: "Team cloning", included: true },
        { text: "Priority support", included: false },
      ],
      buttonText: isPaid ? "Current Plan" : "Get Started",
      popular: true,
    },
    {
      name: "Club",
      price: "$500",
      period: "/year",
      description: "Perfect for clubs, leagues & organizations",
      tier: 'club' as const,
      features: [
        { text: "Unlimited teams", included: true },
        { text: "Unlimited players per team", included: true },
        { text: "Smart rotation algorithm", included: true },
        { text: "Print & export lineups", included: true },
        { text: "Position preferences", included: true },
        { text: "Priority email support", included: true },
        { text: "Team cloning", included: true },
        { text: "Multi-coach access (coming soon)", included: true },
      ],
      buttonText: isPaid ? "Current Plan" : "Get Started",
      popular: false,
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Header */}
      <header className="bg-baseball-navy text-white py-4">
        <div className="container mx-auto px-4 flex justify-between items-center">
          <h1 className="font-bold text-xl cursor-pointer" onClick={() => navigate('/')}>
            Dugout Boss
          </h1>
          <nav className="flex space-x-4">
            <Button
              variant="ghost"
              className="text-white hover:text-baseball-lightgreen"
              onClick={() => navigate('/')}
            >
              Home
            </Button>
            {user ? (
              <Button
                variant="baseball"
                onClick={() => navigate('/dashboard')}
              >
                Dashboard
              </Button>
            ) : (
              <Button
                variant="baseball"
                onClick={() => navigate('/sign-in')}
              >
                Sign In
              </Button>
            )}
          </nav>
        </div>
      </header>

      {/* Pricing Section */}
      <main className="container mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Simple, Transparent Pricing
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Choose the plan that fits your coaching needs. All plans include a full year of access.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan) => (
            <Card 
              key={plan.name} 
              className={`relative ${plan.popular ? 'border-baseball-navy shadow-lg scale-105' : ''}`}
            >
              {plan.popular && (
                <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-baseball-navy">
                  Most Popular
                </Badge>
              )}
              <CardHeader>
                <CardTitle className="text-2xl">{plan.name}</CardTitle>
                <CardDescription>{plan.description}</CardDescription>
                <div className="mt-4">
                  <span className="text-4xl font-bold">{plan.price}</span>
                  <span className="text-gray-600">{plan.period}</span>
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3 mb-6">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      {feature.included ? (
                        <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      ) : (
                        <X className="h-5 w-5 text-gray-300 mr-2 flex-shrink-0 mt-0.5" />
                      )}
                      <span className={feature.included ? '' : 'text-gray-400'}>
                        {feature.text}
                      </span>
                    </li>
                  ))}
                </ul>
                <Button
                  className="w-full"
                  variant={plan.popular ? "baseball" : "outline"}
                  onClick={() => handleSubscribe(plan.tier)}
                  disabled={isPaid}
                >
                  {plan.buttonText}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-16 text-center">
          <h2 className="text-2xl font-bold mb-4">Frequently Asked Questions</h2>
          <div className="max-w-2xl mx-auto text-left space-y-4">
            <div>
              <h3 className="font-semibold mb-2">What happens when my year is up?</h3>
              <p className="text-gray-600">
                You'll receive an email reminder before your subscription expires. You can renew at any time to keep your teams and data.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Can I upgrade or downgrade?</h3>
              <p className="text-gray-600">
                Yes! You can upgrade at any time and we'll prorate the difference. Contact support for downgrades.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Is there a free trial?</h3>
              <p className="text-gray-600">
                We offer a demo mode where you can explore all features with sample data before purchasing.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">What payment methods do you accept?</h3>
              <p className="text-gray-600">
                We accept all major credit cards, debit cards, and digital wallets through Stripe.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Pricing;

import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { generateId } from "@/lib/utils-enhanced";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Circle, CalendarDays, Text, ClipboardList } from "lucide-react";

const CreateLineup = () => {
  const navigate = useNavigate();

  const [lineupName, setLineupName] = useState("");
  const [gameDate, setGameDate] = useState(new Date().toISOString().split("T")[0]);
  const [numberOfInnings, setNumberOfInnings] = useState<string>("7");

  const handleCreateLineup = () => {
    if (!lineupName.trim()) {
      toast.error("Please enter a lineup name");
      return;
    }

    // Validate number of innings
    const innings = numberOfInnings === "" ? 7 : parseInt(numberOfInnings);
    if (isNaN(innings) || innings < 1 || innings > 15) {
      toast.error("Number of innings must be between 1 and 15");
      return;
    }

    // Create the lineup data
    const lineupData = {
      id: generateId(),
      name: lineupName,
      gameDate: gameDate,
      createdDate: new Date().toISOString().split("T")[0],
      numberOfInnings: innings, // Include the number of innings
      innings: [],
      battingOrder: [],
      attendance: {} // Initialize empty attendance object that will be filled in the next step
    };

    console.log("Created new lineup data:", lineupData);

    // Navigate to attendance selection with lineup data
    navigate("/set-lineup-attendance", { state: { lineupData } });
  };

  return (
    <div className="min-h-screen flex flex-col bg-baseball-tan/20">
      <Header title="Create a New Lineup" showBack backLink="/" />

      <main className="flex-1 container mx-auto px-4 py-6 sm:py-8">
        <Card className="max-w-md mx-auto border-t-4 border-baseball-green">
          <CardHeader className="bg-baseball-navy text-white rounded-t-lg">
            <CardTitle className="flex items-center gap-2">
              <ClipboardList size={24} />
              New Lineup Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6 p-4 sm:p-6">
            <div className="space-y-3">
              <label htmlFor="lineup-name" className="font-medium flex items-center gap-2 text-baseball-navy">
                <Text size={18} />
                Lineup Name:
              </label>
              <Input
                id="lineup-name"
                value={lineupName}
                onChange={(e) => setLineupName(e.target.value)}
                placeholder="e.g., Forest Glade"
                className="border-baseball-green/30 focus:border-baseball-green"
              />
            </div>

            <div className="space-y-3">
              <label htmlFor="game-date" className="font-medium flex items-center gap-2 text-baseball-navy">
                <CalendarDays size={18} />
                Game Date:
              </label>
              <Input
                id="game-date"
                type="date"
                value={gameDate}
                onChange={(e) => setGameDate(e.target.value)}
                className="border-baseball-green/30 focus:border-baseball-green"
              />
            </div>

            <div className="space-y-3">
              <label htmlFor="innings" className="font-medium flex items-center gap-2 text-baseball-navy">
                <Circle size={18} />
                Number of Innings:
              </label>
              <Input
                id="innings"
                type="number"
                min={1}
                max={15}
                value={numberOfInnings}
                onChange={(e) => {
                  const value = e.target.value;
                  setNumberOfInnings(value);
                }}
                className="border-baseball-green/30 focus:border-baseball-green"
              />
            </div>

            <div className="pt-4">
              <Button
                onClick={handleCreateLineup}
                className="w-full bg-baseball-green hover:bg-baseball-green/90 text-white"
              >
                Continue to Player Selection
              </Button>
            </div>
          </CardContent>
        </Card>
      </main>

      <Footer />
    </div>
  );
};

export default CreateLineup;


import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { generateId } from "@/lib/utils-enhanced";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Circle, CalendarDays, Text, ClipboardList } from "lucide-react";
import { useTeam } from "@/contexts/TeamContext";

const CreateLineup = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { rotationRules } = useTeam();
  
  // Check if we're in regeneration mode from a series
  const state = location.state as any;
  const isRegenerateMode = state?.regenerateMode;
  const existingLineup = state?.existingLineup;
  const seriesData = state?.seriesData;
  const gameIndex = state?.gameIndex;
  const rotationSettings = state?.rotationSettings;

  const [lineupName, setLineupName] = useState("");
  const [gameDate, setGameDate] = useState(new Date().toISOString().split("T")[0]);
  const [numberOfInnings, setNumberOfInnings] = useState<string>((rotationRules?.defaultInnings || 6).toString());
  
  // Initialize from existing lineup if regenerating
  useEffect(() => {
    if (isRegenerateMode && existingLineup) {
      setLineupName(existingLineup.name || "");
      setGameDate(existingLineup.gameDate || new Date().toISOString().split("T")[0]);
      setNumberOfInnings((existingLineup.innings?.length || rotationRules?.defaultInnings || 6).toString());
    }
  }, [isRegenerateMode, existingLineup, rotationRules]);

  const handleCreateLineup = () => {
    if (!lineupName.trim()) {
      toast.error("Please enter a lineup name");
      return;
    }

    // Validate number of innings
    const innings = numberOfInnings === "" ? (rotationRules?.defaultInnings || 6) : parseInt(numberOfInnings);
    if (isNaN(innings) || innings < 1 || innings > 15) {
      toast.error("Number of innings must be between 1 and 15");
      return;
    }

    // Create the lineup data
    const lineupData = {
      id: isRegenerateMode && existingLineup ? existingLineup.id : generateId(),
      name: lineupName,
      gameDate: gameDate,
      createdDate: isRegenerateMode && existingLineup ? existingLineup.createdDate : new Date().toISOString().split("T")[0],
      numberOfInnings: innings,
      innings: [],
      battingOrder: isRegenerateMode && existingLineup ? existingLineup.battingOrder : [],
      attendance: isRegenerateMode && existingLineup ? existingLineup.attendance : {},
      // ONLY preserve series metadata if explicitly in regenerate mode for a series game
      ...(isRegenerateMode && existingLineup && seriesData && {
        seriesId: existingLineup.seriesId,
        seriesTitle: existingLineup.seriesTitle,
        gameNumber: existingLineup.gameNumber,
        totalGamesInSeries: existingLineup.totalGamesInSeries
      })
    };
    
    // IMPORTANT: For single games, explicitly ensure no series metadata is present
    if (!isRegenerateMode || !seriesData) {
      // Remove any series metadata that might have leaked through
      delete (lineupData as any).seriesId;
      delete (lineupData as any).seriesTitle;
      delete (lineupData as any).gameNumber;
      delete (lineupData as any).totalGamesInSeries;
      console.log("Creating single game lineup - series metadata explicitly removed");
    }

    console.log(isRegenerateMode ? "Regenerating lineup data:" : "Created new lineup data:", lineupData);

    // Navigate to attendance selection with lineup data
    navigate("/set-lineup-attendance", { 
      state: { 
        lineupData,
        isRegenerateMode,
        seriesData,
        gameIndex,
        rotationSettings
      } 
    });
  };

  return (
    <div className="min-h-screen flex flex-col bg-baseball-tan/20">
      <Header 
        title={isRegenerateMode ? "Regenerate Lineup" : "Create a New Lineup"} 
        showBack 
        backLink={isRegenerateMode && seriesData ? `/view-batch-series` : "/"} 
      />

      <main className="flex-1 container mx-auto px-4 py-6 sm:py-8">
        <Card className="max-w-md mx-auto border-t-4 border-baseball-green">
          <CardHeader className="bg-baseball-navy text-white rounded-t-lg">
            <CardTitle className="flex items-center gap-2">
              <ClipboardList size={24} />
              New Lineup Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6 p-4 sm:p-6">
            <div className="space-y-3">
              <label htmlFor="lineup-name" className="font-medium flex items-center gap-2 text-baseball-navy">
                <Text size={18} />
                Lineup Name:
              </label>
              <Input
                id="lineup-name"
                value={lineupName}
                onChange={(e) => setLineupName(e.target.value)}
                placeholder="e.g., Forest Glade"
                className="border-baseball-green/30 focus:border-baseball-green"
              />
            </div>

            <div className="space-y-3">
              <label htmlFor="game-date" className="font-medium flex items-center gap-2 text-baseball-navy">
                <CalendarDays size={18} />
                Game Date:
              </label>
              <Input
                id="game-date"
                type="date"
                value={gameDate}
                onChange={(e) => setGameDate(e.target.value)}
                className="border-baseball-green/30 focus:border-baseball-green"
              />
            </div>

            <div className="space-y-3">
              <label htmlFor="innings" className="font-medium flex items-center gap-2 text-baseball-navy">
                <Circle size={18} />
                Number of Innings:
              </label>
              <Input
                id="innings"
                type="number"
                min={1}
                max={15}
                value={numberOfInnings}
                onChange={(e) => {
                  const value = e.target.value;
                  setNumberOfInnings(value);
                }}
                className="border-baseball-green/30 focus:border-baseball-green"
              />
            </div>

            <div className="pt-4">
              <Button
                onClick={handleCreateLineup}
                className="w-full bg-baseball-green hover:bg-baseball-green/90 text-white"
              >
                Continue to Player Selection
              </Button>
            </div>
          </CardContent>
        </Card>
      </main>

      <Footer />
    </div>
  );
};

export default CreateLineup;

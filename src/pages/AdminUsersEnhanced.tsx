// Enhanced deletion function for AdminUsers.tsx
// This properly removes auth records to prevent orphaned users

export const deleteUserComplete = async (userId: string) => {
  try {
    console.log("Starting complete user deletion for:", userId);
    
    // Step 1: Delete from profiles (cascades to subscriptions, teams, etc.)
    const { error: profileError } = await supabase
      .from('profiles')
      .delete()
      .eq('id', userId);
      
    if (profileError) {
      console.error("Error deleting profile:", profileError);
      throw profileError;
    }
    
    // Step 2: Call edge function to delete from auth.users
    const { data, error: functionError } = await supabase.functions.invoke('admin-delete-user', {
      body: { userId }
    });
    
    if (functionError) {
      console.error("Error calling delete function:", functionError);
      // Don't throw here - profile is already deleted
      // Log for manual cleanup if needed
      console.warn("Auth record may need manual cleanup for user:", userId);
    }
    
    return { success: true };
  } catch (error) {
    console.error("Complete deletion failed:", error);
    throw error;
  }
};

// Alternative: Direct auth deletion (requires service role)
export const deleteUserWithServiceRole = async (userId: string, supabaseServiceClient: any) => {
  try {
    // Step 1: Delete from auth.users first (to ensure we can)
    const { error: authError } = await supabaseServiceClient.auth.admin.deleteUser(userId);
    
    if (authError) {
      console.error("Error deleting auth user:", authError);
      throw authError;
    }
    
    // Step 2: Delete from profiles (cascades to everything else)
    const { error: profileError } = await supabaseServiceClient
      .from('profiles')
      .delete()
      .eq('id', userId);
      
    if (profileError) {
      console.error("Error deleting profile:", profileError);
      throw profileError;
    }
    
    console.log("User completely deleted:", userId);
    return { success: true };
  } catch (error) {
    console.error("Complete deletion failed:", error);
    throw error;
  }
};

// Usage in AdminUsers.tsx:
/*
Replace the existing deletion logic with:

const handleDelete = async (userId: string) => {
  if (!window.confirm("Are you sure you want to delete this user? This will remove ALL their data including teams and lineups.")) {
    return;
  }
  
  try {
    await deleteUserComplete(userId);
    toast.success("User deleted successfully");
    fetchUsers(); // Refresh the list
  } catch (error) {
    console.error("Error deleting user:", error);
    toast.error("Failed to delete user completely. Check console for details.");
  }
};
*/
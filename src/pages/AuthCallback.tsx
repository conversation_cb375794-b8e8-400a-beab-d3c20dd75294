import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';

const AuthCallback = () => {
  const navigate = useNavigate();
  const { checkPaymentStatus } = useAuth();
  const [isCheckingPayment, setIsCheckingPayment] = useState(false);

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Get the session from the URL hash
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Auth callback error:', error);
          toast.error('Authentication failed');
          navigate('/sign-in');
          return;
        }

        if (data.session) {
          // User is authenticated
          const user = data.session.user;

          // Check if this is a new user (just signed up)
          const isNewUser = user.created_at &&
            new Date(user.created_at).getTime() > (Date.now() - 60000); // Within last minute

          if (isNewUser) {
            // For new users, check if they already have payment
            setIsCheckingPayment(true);
            try {
              // Wait a moment for potential payment processing
              await new Promise(resolve => setTimeout(resolve, 1000));
              const hasPayment = await checkPaymentStatus();
              
              if (hasPayment) {
                toast.success('Welcome to Dugout Boss! Your account is ready to use.');
                navigate('/dashboard');
              } else {
                toast.success('Welcome to Dugout Boss! Please complete your purchase to get started.');
                navigate('/pricing');
              }
            } catch (error) {
              console.error('Error checking payment status:', error);
              toast.success('Welcome to Dugout Boss! Please complete your purchase to get started.');
              navigate('/pricing');
            } finally {
              setIsCheckingPayment(false);
            }
          } else {
            toast.success('Successfully signed in!');
            navigate('/dashboard');
          }
        } else {
          // No session, redirect to sign in
          navigate('/sign-in');
        }
      } catch (error) {
        console.error('Unexpected error in auth callback:', error);
        toast.error('Something went wrong');
        navigate('/sign-in');
      }
    };

    handleAuthCallback();
  }, [navigate]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-baseball-lightblue/30">
      <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-lg text-center">
        <Loader2 className="mx-auto h-8 w-8 animate-spin text-baseball-navy" />
        <h2 className="text-2xl font-bold text-baseball-navy">
          {isCheckingPayment ? 'Verifying Account...' : 'Authenticating...'}
        </h2>
        <p className="text-gray-600">
          {isCheckingPayment 
            ? 'Checking your payment status and setting up your account.' 
            : 'Please wait while we complete your authentication.'}
        </p>
      </div>
    </div>
  );
};

export default AuthCallback;

import { useState, useEffect } from "react";
import { supabase } from "@/supabaseClient";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Settings,
  Save,
  RefreshCw,
  Loader2,
  AlertTriangle,
  CheckCircle2
} from "lucide-react";
import AdminLayout from "@/components/AdminLayout";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

const AdminSettings = () => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [confirmAction, setConfirmAction] = useState<() => Promise<void>>(() => async () => {});
  const [confirmMessage, setConfirmMessage] = useState("");
  const [confirmTitle, setConfirmTitle] = useState("");

  // App settings
  const [appSettings, setAppSettings] = useState({
    appName: "Dugout Boss",
    demoModeEnabled: true,
    maintenanceMode: false,
    defaultPricing: 49,
    contactEmail: "<EMAIL>",
    termsOfService: "",
    privacyPolicy: ""
  });

  // Admin users
  const [adminUsers, setAdminUsers] = useState<{id: string, email: string, name: string}[]>([]);
  const [newAdminEmail, setNewAdminEmail] = useState("");

  useEffect(() => {
    fetchSettings();
    fetchAdminUsers();
  }, []);

  const fetchSettings = async () => {
    setLoading(true);
    try {
      // In a real app, you would fetch these from a settings table
      // For now, we'll just use the default values
      setLoading(false);
    } catch (error) {
      console.error("Error fetching settings:", error);
      toast.error("Failed to load settings");
      setLoading(false);
    }
  };

  const fetchAdminUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, email')
        .eq('is_admin', true);

      if (error) throw error;

      setAdminUsers(data?.map(user => ({
        id: user.id,
        email: user.email || 'Unknown',
        name: user.full_name || 'Unknown'
      })) || []);
    } catch (error) {
      console.error("Error fetching admin users:", error);
      toast.error("Failed to load admin users");
    }
  };

  const saveSettings = async () => {
    setSaving(true);
    try {
      // In a real app, you would save these to a settings table
      // For now, we'll just simulate a save
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast.success("Settings saved successfully");
    } catch (error) {
      console.error("Error saving settings:", error);
      toast.error("Failed to save settings");
    } finally {
      setSaving(false);
    }
  };

  const addAdminUser = async () => {
    if (!newAdminEmail) {
      toast.error("Please enter an email address");
      return;
    }

    try {
      // Find the user by email
      const { data: profile, error: userError } = await supabase
        .from('profiles')
        .select('id, email, full_name')
        .eq('email', newAdminEmail)
        .single();

      if (userError) throw userError;

      const user = profile;

      if (!user) {
        toast.error("User not found with that email");
        return;
      }

      // Check if user is already an admin
      const { data: existingAdmin, error: checkError } = await supabase
        .from('profiles')
        .select('is_admin')
        .eq('id', user.id)
        .single();

      if (checkError && checkError.code !== 'PGRST116') throw checkError;

      if (existingAdmin?.is_admin) {
        toast.error("User is already an admin");
        return;
      }

      // Set up confirmation
      setConfirmTitle("Add Admin User");
      setConfirmMessage(`Are you sure you want to make ${newAdminEmail} an admin? This will give them full access to the admin panel.`);
      setConfirmAction(async () => {
        try {
          // Update the user's profile
          const { error } = await supabase
            .from('profiles')
            .upsert({
              id: user.id,
              is_admin: true,
              updated_at: new Date().toISOString()
            });

          if (error) throw error;

          // Log admin action
          await supabase
            .from('admin_audit_logs')
            .insert({
              admin_id: (await supabase.auth.getUser()).data.user?.id,
              action: 'add_admin',
              entity_type: 'user',
              entity_id: user.id,
              details: { email: newAdminEmail }
            });

          toast.success(`${newAdminEmail} is now an admin`);
          setNewAdminEmail("");
          fetchAdminUsers();
        } catch (error) {
          console.error("Error adding admin:", error);
          toast.error("Failed to add admin");
        }
      });
      setShowConfirmDialog(true);
    } catch (error) {
      console.error("Error adding admin:", error);
      toast.error("Failed to add admin");
    }
  };

  const removeAdminUser = async (userId: string, email: string) => {
    // Set up confirmation
    setConfirmTitle("Remove Admin User");
    setConfirmMessage(`Are you sure you want to remove admin privileges from ${email}?`);
    setConfirmAction(async () => {
      try {
        // Update the user's profile
        const { error } = await supabase
          .from('profiles')
          .update({
            is_admin: false,
            updated_at: new Date().toISOString()
          })
          .eq('id', userId);

        if (error) throw error;

        // Log admin action
        await supabase
          .from('admin_audit_logs')
          .insert({
            admin_id: (await supabase.auth.getUser()).data.user?.id,
            action: 'remove_admin',
            entity_type: 'user',
            entity_id: userId,
            details: { email }
          });

        toast.success(`${email} is no longer an admin`);
        fetchAdminUsers();
      } catch (error) {
        console.error("Error removing admin:", error);
        toast.error("Failed to remove admin");
      }
    });
    setShowConfirmDialog(true);
  };

  const toggleMaintenanceMode = () => {
    // Set up confirmation
    const newMode = !appSettings.maintenanceMode;
    setConfirmTitle(newMode ? "Enable Maintenance Mode" : "Disable Maintenance Mode");
    setConfirmMessage(
      newMode
        ? "Enabling maintenance mode will prevent users from accessing the app. Are you sure you want to continue?"
        : "Disabling maintenance mode will allow users to access the app again. Are you sure you want to continue?"
    );
    setConfirmAction(async () => {
      setAppSettings({...appSettings, maintenanceMode: newMode});
      toast.success(`Maintenance mode ${newMode ? 'enabled' : 'disabled'}`);
    });
    setShowConfirmDialog(true);
  };

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Admin Settings</h1>
          <Button onClick={saveSettings} disabled={saving}>
            {saving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Changes
              </>
            )}
          </Button>
        </div>

        <Tabs defaultValue="general">
          <TabsList className="mb-6">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="admins">Admin Users</TabsTrigger>
            <TabsTrigger value="legal">Legal</TabsTrigger>
          </TabsList>

          {/* General Settings */}
          <TabsContent value="general">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Application Settings</CardTitle>
                  <CardDescription>
                    Configure general application settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="appName">Application Name</Label>
                    <Input
                      id="appName"
                      value={appSettings.appName}
                      onChange={(e) => setAppSettings({...appSettings, appName: e.target.value})}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="contactEmail">Support Email</Label>
                    <Input
                      id="contactEmail"
                      type="email"
                      value={appSettings.contactEmail}
                      onChange={(e) => setAppSettings({...appSettings, contactEmail: e.target.value})}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="defaultPricing">Default Price (USD)</Label>
                    <Input
                      id="defaultPricing"
                      type="number"
                      value={appSettings.defaultPricing}
                      onChange={(e) => setAppSettings({...appSettings, defaultPricing: parseInt(e.target.value)})}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>System Settings</CardTitle>
                  <CardDescription>
                    Configure system-wide settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="demoMode">Demo Mode</Label>
                      <p className="text-sm text-gray-500">
                        Allow users to try the app without creating an account
                      </p>
                    </div>
                    <Switch
                      id="demoMode"
                      checked={appSettings.demoModeEnabled}
                      onCheckedChange={(checked) => setAppSettings({...appSettings, demoModeEnabled: checked})}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="maintenanceMode">Maintenance Mode</Label>
                      <p className="text-sm text-gray-500">
                        Temporarily disable access to the application
                      </p>
                    </div>
                    <Switch
                      id="maintenanceMode"
                      checked={appSettings.maintenanceMode}
                      onCheckedChange={toggleMaintenanceMode}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Admin Users */}
          <TabsContent value="admins">
            <Card>
              <CardHeader>
                <CardTitle>Admin Users</CardTitle>
                <CardDescription>
                  Manage users with administrative access
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Input
                    placeholder="Email address"
                    value={newAdminEmail}
                    onChange={(e) => setNewAdminEmail(e.target.value)}
                  />
                  <Button onClick={addAdminUser}>Add Admin</Button>
                </div>

                <div className="border rounded-md">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Name
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Email
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {adminUsers.length === 0 ? (
                        <tr>
                          <td colSpan={3} className="px-6 py-4 text-center text-sm text-gray-500">
                            No admin users found
                          </td>
                        </tr>
                      ) : (
                        adminUsers.map((user) => (
                          <tr key={user.id}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {user.name}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {user.email}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-red-600 hover:text-red-900"
                                onClick={() => removeAdminUser(user.id, user.email)}
                              >
                                Remove
                              </Button>
                            </td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Legal */}
          <TabsContent value="legal">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Terms of Service</CardTitle>
                  <CardDescription>
                    Set the terms of service for your application
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="Enter your terms of service here..."
                    className="min-h-[200px]"
                    value={appSettings.termsOfService}
                    onChange={(e) => setAppSettings({...appSettings, termsOfService: e.target.value})}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Privacy Policy</CardTitle>
                  <CardDescription>
                    Set the privacy policy for your application
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="Enter your privacy policy here..."
                    className="min-h-[200px]"
                    value={appSettings.privacyPolicy}
                    onChange={(e) => setAppSettings({...appSettings, privacyPolicy: e.target.value})}
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{confirmTitle}</DialogTitle>
            <DialogDescription>
              {confirmMessage}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowConfirmDialog(false)}>
              Cancel
            </Button>
            <Button
              onClick={async () => {
                await confirmAction();
                setShowConfirmDialog(false);
              }}
            >
              Confirm
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default AdminSettings;

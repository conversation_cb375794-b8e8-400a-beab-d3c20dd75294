// DemoData.ts - Contains functions to generate demo data for the app

import { Player, RotationRules, Lineup, InningLineup } from '@/contexts/TeamContext';

// Function to get default players for a demo team
export const getDefaultPlayers = (): Player[] => {
  return [
    { id: crypto.randomUUID(), name: "<PERSON><PERSON><PERSON>", teamRoles: { catcher: 'go-to', shortstop: 'capable' } },
    { id: crypto.randomUUID(), name: "<PERSON>", teamRoles: { pitcher: 'go-to', firstBase: 'capable' } },
    { id: crypto.randomUUID(), name: "<PERSON>", teamRoles: { secondBase: 'capable', centerField: 'capable' } },
    { id: crypto.randomUUID(), name: "<PERSON>", teamRoles: { catcher: 'capable', rightField: 'capable' } },
    { id: crypto.randomUUID(), name: "<PERSON>", teamRoles: { thirdBase: 'capable', leftField: 'capable' } },
    { id: crypto.randomUUID(), name: "<PERSON><PERSON>", teamRoles: { shortstop: 'capable', centerField: 'capable' } },
    { id: crypto.randomUUID(), name: "<PERSON>", teamRoles: { firstBase: 'capable', rightField: 'capable' } },
    { id: crypto.randomUUID(), name: "Avery", teamRoles: { secondBase: 'capable', leftField: 'capable' } },
    { id: crypto.randomUUID(), name: "Elle", teamRoles: { pitcher: 'capable', firstBase: 'capable' } },
    { id: crypto.randomUUID(), name: "Vienna", teamRoles: { pitcher: 'capable', catcher: 'capable', firstBase: 'capable' } },
    { id: crypto.randomUUID(), name: "Katelyn", teamRoles: { thirdBase: 'capable', leftField: 'capable' } },
    { id: crypto.randomUUID(), name: "Morgan", teamRoles: { shortstop: 'capable', rightField: 'capable' } }
  ];
};

// Function to get default rotation rules
export const getDefaultRotationRules = (): RotationRules => {
  return {
    rotationMethod: "standard",
    equalPlayingTime: true,
    rotatePlayers: true,
    respectPositionLockouts: true,
    allowPitcherRotation: false,
    allowCatcherRotation: true,
    prioritizeOutfieldRotation: true,
    limitBenchTime: true,
    rotateLineupEvery: 1,
    rotatePitcherEvery: 2
  };
};

// Function to create demo lineups
export const createDemoLineups = (players: Player[]): Lineup[] => {
  console.log("Creating demo lineups with", players.length, "players");

  // Create attendance data for all players
  const attendance: {[playerId: string]: boolean} = {};
  players.forEach(player => {
    attendance[player.id] = true;
  });

  // Create exactly 3 sample lineups with 6 innings each
  const sampleLineups: Lineup[] = [
    {
      id: crypto.randomUUID(), // Use UUID for database compatibility
      name: "Forest Glade Tournament",
      gameDate: new Date().toISOString().split('T')[0], // Today's date
      createdDate: new Date().toISOString(),
      attendance,
      innings: [],
      battingOrder: players.slice(0, Math.min(9, players.length)).map(p => p.id), // Use player IDs
      rotationSettings: {
        limitBenchTime: true,
        rotateLineupEvery: 1,
        rotatePitcherEvery: 2
      }
    },
    {
      id: crypto.randomUUID(), // Use UUID for database compatibility
      name: "Essex Game",
      gameDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Next week
      createdDate: new Date().toISOString(),
      attendance,
      innings: [],
      battingOrder: players.slice(0, Math.min(9, players.length)).map(p => p.id), // Use player IDs
      rotationSettings: {
        limitBenchTime: true,
        rotateLineupEvery: 2,
        rotatePitcherEvery: 3
      }
    },
    {
      id: crypto.randomUUID(), // Use UUID for database compatibility
      name: "Sarnia Championship",
      gameDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Two weeks from now
      createdDate: new Date().toISOString(),
      attendance,
      innings: [],
      battingOrder: players.slice(0, Math.min(9, players.length)).map(p => p.id), // Use player IDs
      rotationSettings: {
        limitBenchTime: false,
        rotateLineupEvery: 1,
        rotatePitcherEvery: 1
      }
    }
  ];

  // Helper function to check if a player can play a position based on team roles
  const canPlayPosition = (player: Player, position: string): boolean => {
    // Check team roles
    const role = player.teamRoles?.[position as keyof typeof player.teamRoles];
    
    // If role is 'avoid' or 'never', they can't play this position
    if (role === 'avoid' || role === 'never') {
      return false;
    }
    
    // If no role is set, assume they can play the position
    return true;
  };

  // Helper function to find a suitable player for a position
  const findPlayerForPosition = (availablePlayers: Player[], position: string): Player | null => {
    for (let i = 0; i < availablePlayers.length; i++) {
      if (canPlayPosition(availablePlayers[i], position)) {
        return availablePlayers.splice(i, 1)[0];
      }
    }
    return null;
  };

  // Generate 6 innings for each lineup with different rotation patterns
  for (const lineup of sampleLineups) {
    console.log(`Generating innings for lineup: ${lineup.name}`);

    // Generate 6 innings with player rotations
    for (let i = 1; i <= 6; i++) {
      console.log(`Generating inning ${i} for ${lineup.name}`);

      // Create a copy of players for this inning
      const availablePlayers = [...players];

      // Ensure we have enough players for all positions
      if (availablePlayers.length < 9) {
        console.warn("Not enough players for a full lineup. Adding placeholder players.");
        // Add placeholder players if needed
        const playersNeeded = 9 - availablePlayers.length;
        for (let i = 0; i < playersNeeded; i++) {
          availablePlayers.push({
            id: `placeholder-${i}`,
            name: `Player ${availablePlayers.length + 1}`,
            teamRoles: {}
          });
        }
      }

      // Shuffle players to create variety in positions
      availablePlayers.sort(() => Math.random() - 0.5);

      // Find players for each position respecting restrictions
      const pitcherPlayer = findPlayerForPosition(availablePlayers, 'pitcher') || availablePlayers.splice(0, 1)[0];
      const catcherPlayer = findPlayerForPosition(availablePlayers, 'catcher') || availablePlayers.splice(0, 1)[0];
      const firstBasePlayer = findPlayerForPosition(availablePlayers, 'firstBase') || availablePlayers.splice(0, 1)[0];
      const secondBasePlayer = findPlayerForPosition(availablePlayers, 'secondBase') || availablePlayers.splice(0, 1)[0];
      const thirdBasePlayer = findPlayerForPosition(availablePlayers, 'thirdBase') || availablePlayers.splice(0, 1)[0];
      const shortstopPlayer = findPlayerForPosition(availablePlayers, 'shortstop') || availablePlayers.splice(0, 1)[0];
      const leftFieldPlayer = findPlayerForPosition(availablePlayers, 'leftField') || availablePlayers.splice(0, 1)[0];
      const centerFieldPlayer = findPlayerForPosition(availablePlayers, 'centerField') || availablePlayers.splice(0, 1)[0];
      const rightFieldPlayer = findPlayerForPosition(availablePlayers, 'rightField') || availablePlayers.splice(0, 1)[0];

      // Create the inning lineup
      lineup.innings.push({
        inning: i,
        positions: {
          leftField: leftFieldPlayer.id,
          centerField: centerFieldPlayer.id,
          rightField: rightFieldPlayer.id,
          thirdBase: thirdBasePlayer.id,
          shortstop: shortstopPlayer.id,
          secondBase: secondBasePlayer.id,
          firstBase: firstBasePlayer.id,
          catcher: catcherPlayer.id,
          pitcher: pitcherPlayer.id,
          bench: availablePlayers.map(p => p.id)
        }
      });
    }
  }

  console.log("Demo lineups created successfully:", sampleLineups.length, "lineups");
  // Ensure we return exactly 3 lineups
  return sampleLineups.slice(0, 3);
};

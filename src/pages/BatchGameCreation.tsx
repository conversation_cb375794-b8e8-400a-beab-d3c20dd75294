import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useTeam } from "@/contexts/TeamContext";
import { generateId } from "@/lib/utils-enhanced";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { CalendarDays, Users, Clock, Trophy, Settings, Target } from "lucide-react";

const BatchGameCreation = () => {
  const navigate = useNavigate();
  const { players, rotationRules } = useTeam();
  
  // Debug: Log the actual rotationRules being received
  console.log('BatchGameCreation - rotationRules from useTeam():', rotationRules);
  const [selectedGames, setSelectedGames] = useState<number>(0);
  const [seriesTitle, setSeriesTitle] = useState<string>("");
  const [gameDetails, setGameDetails] = useState<Array<{
    name: string;
    date: string;
    time: string;
    innings: number | string; // Allow string temporarily for input handling
  }>>([]);
  
  // Rotation settings state - will be updated when rotationRules loads
  const [rotationSettings, setRotationSettings] = useState({
    rotateLineupEvery: 1,
    rotatePitcherEvery: 2,
    competitiveMode: false,
    limitBenchTime: false,        // DISABLED: Games are on different days
    maxConsecutiveBenchInnings: 999, // DISABLED: No meaning across different games
    allowPitcherRotation: true,
    allowCatcherRotation: true,
    respectPositionLockouts: true,
    equalPlayingTime: false       // DISABLED: Measure balance across entire series
  });

  // Update rotation settings when team's rotationRules become available
  useEffect(() => {
    if (rotationRules) {
      console.log('useEffect updating rotationSettings with rotationRules:', rotationRules);
      setRotationSettings(prev => ({
        ...prev,
        rotateLineupEvery: rotationRules.rotateLineupEvery || 1,
        rotatePitcherEvery: rotationRules.rotatePitcherEvery || 2,
        competitiveMode: rotationRules.competitiveMode || false,  // INHERITED from team settings
        allowPitcherRotation: rotationRules.allowPitcherRotation !== false,
        allowCatcherRotation: rotationRules.allowCatcherRotation !== false,
        respectPositionLockouts: rotationRules.respectPositionLockouts !== false,
      }));
    }
  }, [rotationRules]);

  // Initialize game details when number of games is selected
  const handleGameCountSelect = (count: number) => {
    setSelectedGames(count);
    
    // Set default series title based on count
    const defaultTitles = {
      2: "Doubleheader",
      3: "Weekend Series", 
      4: "Tournament",
      5: "Full Week Series"
    };
    setSeriesTitle(defaultTitles[count as keyof typeof defaultTitles] || `${count}-Game Series`);
    
    // Pre-populate with sensible defaults
    const today = new Date();
    const defaultGames = Array.from({ length: count }, (_, i) => {
      const gameDate = new Date(today);
      gameDate.setDate(today.getDate() + Math.floor(i / 2)); // Same day for doubleheaders
      
      const gameTime = i % 2 === 0 ? "10:00" : "14:00";
      const dayName = gameDate.toLocaleDateString('en-US', { weekday: 'short' });
      
      return {
        name: count === 2 && i === 0 ? `${dayName} - Game 1` :
              count === 2 && i === 1 ? `${dayName} - Game 2` :
              `${dayName} ${gameTime === "10:00" ? "Morning" : "Afternoon"}`,
        date: gameDate.toISOString().split('T')[0],
        time: gameTime,
        innings: 7
      };
    });
    
    setGameDetails(defaultGames);
  };

  const updateGameDetail = (index: number, field: string, value: any) => {
    const updated = [...gameDetails];
    updated[index] = { ...updated[index], [field]: value };
    setGameDetails(updated);
  };

  const handleCreateBatch = () => {
    // Validate we have enough players
    if (players.length < 8) {
      toast.error("You need at least 8 players in your roster");
      return;
    }

    // Validate series title
    if (!seriesTitle.trim()) {
      toast.error("Please provide a name for your series");
      return;
    }

    // Validate all game details
    for (const game of gameDetails) {
      if (!game.name.trim()) {
        toast.error("Please provide names for all games");
        return;
      }
    }

    // Create lineup data for all games
    console.log(`Creating batch data for ${selectedGames} games`);
    console.log('Game details:', gameDetails);
    console.log('Rotation settings:', rotationSettings);
    
    const batchData = {
      id: generateId(),
      seriesTitle: seriesTitle.trim(),
      rotationSettings: rotationSettings,
      games: gameDetails.map(game => ({
        id: generateId(),
        name: game.name,
        gameDate: game.date,
        gameTime: game.time,
        numberOfInnings: typeof game.innings === 'string' ? parseInt(game.innings) || 7 : game.innings,
        createdDate: new Date().toISOString().split('T')[0],
        innings: [],
        battingOrder: [],
        attendance: {}
      })),
      crossGameTracking: {
        totalGames: selectedGames,
        playingTimeTargets: {}, // Will be calculated based on total innings
        pitcherInningsTracked: true,
        catcherInningsTracked: true
      }
    };
    
    console.log(`Batch data created with ${batchData.games.length} games`);

    // Navigate to batch attendance/generation
    navigate("/batch-lineup-generation", { state: { batchData } });
  };

  return (
    <div className="min-h-screen flex flex-col bg-baseball-tan/20">
      <Header title="Create Multiple Games" showBack backLink="/" />

      <main className="flex-1 container mx-auto px-4 py-6 sm:py-8 max-w-6xl">
        {selectedGames === 0 ? (
          <div>
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-baseball-navy mb-2">How many games are you creating?</h2>
              <p className="text-gray-600">Create multiple lineups that work together for optimal player rotation</p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {[2, 3, 4, 5].map((count) => (
                <Card 
                  key={count}
                  className="cursor-pointer hover:shadow-lg transition-shadow border-2 hover:border-baseball-green"
                  onClick={() => handleGameCountSelect(count)}
                >
                  <CardContent className="p-6 text-center">
                    <div className="text-3xl font-bold text-baseball-green mb-2">{count}</div>
                    <div className="text-sm text-gray-600">
                      {count === 2 && "Doubleheader"}
                      {count === 3 && "Weekend Series"}
                      {count === 4 && "Tournament"}
                      {count === 5 && "Full Week"}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="mt-12 p-6 bg-blue-50 rounded-lg max-w-2xl mx-auto">
              <h3 className="font-semibold text-blue-900 mb-3 flex items-center gap-2">
                <Trophy className="w-5 h-5" />
                Why Create Multiple Games at Once?
              </h3>
              <ul className="space-y-2 text-sm text-blue-800">
                <li>• Ensures fair playing time across the entire series</li>
                <li>• Tracks cumulative innings for better player development</li>
                <li>• Creates complementary lineups that work together</li>
                <li>• Uses your team's rotation settings consistently</li>
              </ul>
            </div>
          </div>
        ) : (
          <div>
            <div className="mb-6">
              <Button
                variant="ghost"
                onClick={() => setSelectedGames(0)}
                className="text-baseball-green hover:text-baseball-green/80"
              >
                ← Change number of games
              </Button>
            </div>

            <div className="space-y-6">
              <h2 className="text-xl font-bold text-baseball-navy">
                Setting up {selectedGames} games
              </h2>

              {/* Series Title Input */}
              <Card className="border-l-4 border-blue-500">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Trophy className="w-5 h-5" />
                    Series Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div>
                    <Label htmlFor="series-title" className="text-sm font-medium text-gray-700">
                      Series Title
                    </Label>
                    <Input
                      id="series-title"
                      value={seriesTitle}
                      onChange={(e) => setSeriesTitle(e.target.value)}
                      placeholder="Milton Tournament"
                      className="mt-1"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      This will be used to identify your series of games
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Rotation Settings - HIGHLIGHTED FOR VISIBILITY */}
              <Card className="border-l-4 border-red-500 border-2 border-red-300 bg-red-50/30">
                <CardHeader className="pb-3 bg-red-50">
                  <CardTitle className="text-lg flex items-center gap-2 text-red-900">
                    <Settings className="w-5 h-5 text-red-600" />
                    ⚠️ Important: Multi-Game Rotation Settings
                  </CardTitle>
                  <p className="text-sm text-red-800 font-medium mt-2">
                    These settings control how players rotate throughout your series. Missing these will result in incorrect lineups!
                  </p>
                </CardHeader>
                <CardContent className="space-y-4 bg-white pt-6">
                  <div className="p-4 bg-amber-50 border border-amber-300 rounded-lg mb-6">
                    <p className="text-sm text-amber-900 font-medium">
                      🎯 Quick tip: For tournaments, use "Rotate lineup every 1 inning" for maximum fairness
                    </p>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Rotate Lineup Every */}
                    <div className="p-4 border-2 border-blue-300 rounded-lg bg-blue-50/50">
                      <Label className="text-base font-semibold text-gray-900 mb-2 block">
                        🔄 Rotate lineup every
                      </Label>
                      <Select 
                        value={rotationSettings.rotateLineupEvery.toString()} 
                        onValueChange={(value) => setRotationSettings(prev => ({
                          ...prev, 
                          rotateLineupEvery: parseInt(value)
                        }))}
                      >
                        <SelectTrigger className="mt-1 h-12 text-base font-medium">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 inning (Most Fair)</SelectItem>
                          <SelectItem value="2">2 innings (Balanced)</SelectItem>
                          <SelectItem value="3">3 innings (More Stable)</SelectItem>
                        </SelectContent>
                      </Select>
                      <p className="text-xs text-gray-600 mt-2">
                        Lower = More rotation & fairness
                      </p>
                    </div>

                    {/* Rotate Pitcher Every */}
                    <div className="p-4 border-2 border-green-300 rounded-lg bg-green-50/50">
                      <Label className="text-base font-semibold text-gray-900 mb-2 block">
                        ⚾ Rotate pitcher every
                      </Label>
                      <Select 
                        value={rotationSettings.rotatePitcherEvery.toString()} 
                        onValueChange={(value) => {
                          const intValue = parseInt(value);
                          setRotationSettings(prev => ({
                            ...prev, 
                            rotatePitcherEvery: intValue,
                            allowPitcherRotation: intValue !== 999  // Auto-set based on frequency
                          }));
                        }}
                      >
                        <SelectTrigger className="mt-1 h-12 text-base font-medium">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="999">Never rotate pitcher</SelectItem>
                          <SelectItem value="1">Every 1 inning</SelectItem>
                          <SelectItem value="2">Every 2 innings (Recommended)</SelectItem>
                          <SelectItem value="3">Every 3 innings</SelectItem>
                          <SelectItem value="4">Every 4 innings</SelectItem>
                          <SelectItem value="5">Every 5 innings</SelectItem>
                          <SelectItem value="6">Every 6 innings</SelectItem>
                          <SelectItem value="7">Every 7 innings</SelectItem>
                        </SelectContent>
                      </Select>
                      <p className="text-xs text-gray-600 mt-2">
                        Also controls catcher rotation frequency
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Competitive Mode - Show current setting, inherited from team */}
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                            <Target className="w-4 h-4" />
                            Competitive Mode
                          </Label>
                          <p className="text-xs text-gray-500">
                            {rotationSettings.competitiveMode ? 'Enabled' : 'Disabled'} (inherited from team settings)
                          </p>
                        </div>
                        <div className={`px-2 py-1 rounded text-xs font-medium ${
                          rotationSettings.competitiveMode 
                            ? 'bg-orange-100 text-orange-800' 
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {rotationSettings.competitiveMode ? 'ON' : 'OFF'}
                        </div>
                      </div>
                      <p className="text-xs text-blue-700 mt-2">
                        To change competitive mode, update your team's rotation rules
                      </p>
                    </div>

                    {/* Cross-Game Balance Note */}
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <div>
                        <Label className="text-sm font-medium text-blue-900">
                          Playing Time Balance
                        </Label>
                        <p className="text-xs text-blue-700 mt-1">
                          Playing time will be balanced across ALL games in this series, not individual games.
                        </p>
                        <p className="text-xs text-blue-600 mt-1">
                          Individual games may have imbalances that are corrected by subsequent games.
                        </p>
                      </div>
                    </div>
                  </div>

                </CardContent>
              </Card>

              {gameDetails.map((game, index) => (
                <Card key={index} className="border-l-4 border-baseball-green">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Users className="w-5 h-5" />
                      Game {index + 1}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Game Name</label>
                      <Input
                        value={game.name}
                        onChange={(e) => updateGameDetail(index, 'name', e.target.value)}
                        placeholder="vs. Tigers"
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
                        <CalendarDays className="w-4 h-4" />
                        Date
                      </label>
                      <Input
                        type="date"
                        value={game.date}
                        onChange={(e) => updateGameDetail(index, 'date', e.target.value)}
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700 flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        Time
                      </label>
                      <Input
                        type="time"
                        value={game.time}
                        onChange={(e) => updateGameDetail(index, 'time', e.target.value)}
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Innings</label>
                      <Input
                        type="number"
                        min="1"
                        max="9"
                        value={game.innings}
                        onChange={(e) => {
                          const value = e.target.value;
                          // Handle empty input or parse the number
                          if (value === '') {
                            updateGameDetail(index, 'innings', '');
                          } else {
                            const parsed = parseInt(value);
                            if (!isNaN(parsed) && parsed >= 1 && parsed <= 9) {
                              updateGameDetail(index, 'innings', parsed);
                            }
                          }
                        }}
                        onBlur={(e) => {
                          // Set default value on blur if empty
                          if (e.target.value === '' || parseInt(e.target.value) < 1) {
                            updateGameDetail(index, 'innings', 7);
                          }
                        }}
                        className="mt-1"
                      />
                    </div>
                  </CardContent>
                </Card>
              ))}

              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <h4 className="font-semibold text-amber-900 mb-2">What happens next:</h4>
                <ol className="text-sm text-amber-800 space-y-1 list-decimal list-inside">
                  <li>You'll select which players are available for these games</li>
                  <li>The system will generate lineups for all {selectedGames} games at once</li>
                  <li>Playing time will be balanced across all games</li>
                  <li>You can adjust individual games as needed</li>
                </ol>
              </div>

              <div className="flex justify-end gap-3">
                <Button
                  variant="outline"
                  onClick={() => navigate("/")}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateBatch}
                  className="bg-baseball-green hover:bg-baseball-green/90 text-white"
                >
                  Continue to Player Selection →
                </Button>
              </div>
            </div>
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
};

export default BatchGameCreation;
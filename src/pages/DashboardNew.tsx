import { Link, useLocation } from "react-router-dom";
import { useEffect, useState, useRef } from "react";
import { useTeam } from "@/contexts/TeamContext";
import { useAuth } from "@/contexts/AuthContext";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import MenuCard, { MenuLink } from "@/components/MenuCard";
import LineupTable from "@/components/LineupTable";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { DashboardSkeleton } from "@/components/LoadingSkeletons";
import { TeamOnboarding } from "@/components/TeamOnboarding";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";
import {
  Users,
  ClipboardList,
  CalendarDays,
  Settings,
  HelpCircle,
  Circle,
  ChevronDown,
  PlusCircle,
  ShieldCheck,
  User,
  Target,
  Trophy,
  BalanceScale
} from "lucide-react";
import { toast } from "sonner";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { supabase } from "@/integrations/supabase/client";
import { Player } from "@/contexts/TeamContext";

const DashboardNew = () => {
  const { teamName, teams, currentTeamId, setCurrentTeamId, refreshTeamData, loading, players, setPlayers, rotationRules, updateRotationRules } = useTeam();
  const { user, isPaid, checkPaymentStatus } = useAuth();
  const [isAdmin, setIsAdmin] = useState(false);
  const [showOnboarding, setShowOnboarding] = useState(false);
  const location = useLocation();
  const hasRefreshed = useRef(false);
  const isMobile = useIsMobile();

  // Check if this is a new user who needs onboarding
  useEffect(() => {
    if (!loading && teams.length > 0 && players.length === 0) {
      setShowOnboarding(true);
    }
  }, [loading, teams.length, players.length]);

  // Check if user is an admin
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!user) return;

      // Check if this is a demo user - skip admin check for demo users
      const isDemoUser = user.email?.includes('demo') ||
                        user.email?.includes('baseball_demo') ||
                        localStorage.getItem('demo_mode') === 'true';

      if (isDemoUser) {
        setIsAdmin(false);
        return;
      }

      // Special case for Noah's account
      if (user.email === '<EMAIL>') {
        setIsAdmin(true);
        return;
      }

      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('is_admin')
          .eq('id', user.id)
          .single();

        if (error) {
          // Check if the error is due to the table not existing
          if (error.code === '42P01') { // 42P01 is "undefined_table" in PostgreSQL
            console.warn("Admin status check: 'profiles' table does not exist. Assuming not admin.");
            setIsAdmin(false);
          } else {
            console.error("Error checking admin status:", error);
            setIsAdmin(false);
          }
        } else {
          setIsAdmin(data?.is_admin || false);
        }
      } catch (catchError) { // Renamed to avoid conflict with 'error' from Supabase
        console.error("Error in checkAdminStatus try-catch block:", catchError);
        setIsAdmin(false);
      }
    };

    checkAdminStatus();
  }, [user]);

  // Only refresh team data if we don't have any teams loaded
  useEffect(() => {
    // Only refresh if we have a user but no teams loaded and we're not already loading
    if (user && teams.length === 0 && !hasRefreshed.current && !loading) {
      console.log("Dashboard: No teams loaded, refreshing team data");
      hasRefreshed.current = true;
      refreshTeamData();
    }
  }, [user, teams.length, refreshTeamData, loading]);

  const handleOnboardingComplete = async (teamType: string, onboardingPlayers: Player[]) => {
    // Update team settings based on selected type
    const newRules = { ...rotationRules };
    
    if (teamType === 'rec') {
      newRules.competitiveMode = false;
      newRules.equalPlayingTime = true;
      newRules.rotateLineupEvery = 1;
      newRules.rotatePitcherEvery = 2;
    } else if (teamType === 'competitive') {
      newRules.competitiveMode = true;
      newRules.equalPlayingTime = false;
      newRules.rotateLineupEvery = 3;
      newRules.rotatePitcherEvery = 4;
      newRules.competitiveMinPlayingTime = 35;
    }
    
    await updateRotationRules(newRules);
    
    // Save the players
    setPlayers(onboardingPlayers);
    
    // Hide onboarding
    setShowOnboarding(false);
    
    toast.success("Team setup complete! Now you can create your first lineup.");
  };

  // Get coaching style from rotation rules
  const getCoachingStyle = (): number => {
    if (rotationRules?.competitiveMode) {
      if (rotationRules.competitiveMinPlayingTime && rotationRules.competitiveMinPlayingTime <= 25) {
        return 90; // Tournament mode
      }
      return 70; // Mostly competitive
    }
    if (rotationRules?.equalPlayingTime) {
      return 10; // Pure recreational
    }
    return 50; // Balanced
  };

  const coachingStyle = getCoachingStyle();

  const getCoachingModeInfo = () => {
    if (coachingStyle < 30) {
      return {
        label: "Recreational Mode",
        icon: Users,
        color: "text-blue-600 bg-blue-50 border-blue-200",
        description: "Equal playing time for all"
      };
    }
    if (coachingStyle < 70) {
      return {
        label: "Balanced Mode",
        icon: BalanceScale,
        color: "text-green-600 bg-green-50 border-green-200",
        description: "Merit-based with fairness"
      };
    }
    return {
      label: "Competitive Mode",
      icon: Trophy,
      color: "text-orange-600 bg-orange-50 border-orange-200",
      description: "Optimize to win"
    };
  };

  const modeInfo = getCoachingModeInfo();

  const menuLinks: MenuLink[] = [
    {
      to: "/team-roster",
      icon: <Users size={24} />,
      title: "Team Roster",
      description: "Manage players and positions",
      bgColor: "bg-baseball-orange"
    },
    {
      to: "/create-lineup",
      icon: <ClipboardList size={24} />,
      title: "Create Lineup",
      description: "Generate a new game lineup",
      bgColor: "bg-baseball-green"
    },
    {
      to: "/batch-games",
      icon: <CalendarDays size={24} />,
      title: "Multiple Games",
      description: "Create lineups for a series",
      bgColor: "bg-baseball-purple"
    },
    {
      to: "/rotation-rules",
      icon: <Settings size={24} />,
      title: "Team Settings",
      description: "Adjust coaching style",
      bgColor: "bg-baseball-navy"
    }
  ];

  if (loading) {
    return <DashboardSkeleton />;
  }

  if (showOnboarding) {
    return (
      <div className="min-h-screen flex flex-col bg-gray-50">
        <Header title="Team Setup" />
        <main className="flex-1 container mx-auto px-4 py-8">
          <TeamOnboarding onComplete={handleOnboardingComplete} />
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <Header title="Dashboard" />

      {/* Hero Section - Desktop */}
      {!isMobile && (
      <div className="bg-gradient-to-br from-baseball-green/10 to-baseball-lightblue/20">
        <div className="bg-white/80 backdrop-blur-sm border-b">
          <div className="container mx-auto px-4 py-6">
            <div className="flex flex-col md:flex-row items-center justify-between">
              <div>
                <div className="flex items-center gap-3 mb-2">
                  <h1 className="text-3xl md:text-4xl font-bold text-baseball-navy">
                    {teamName}
                  </h1>

                  {/* Team Selector Dropdown */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm" className="h-8 gap-1 border-baseball-navy/30">
                        <span className="sr-only">Switch Team</span>
                        <ChevronDown className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-56">
                      {teams.map(team => (
                        <DropdownMenuItem
                          key={team.id}
                          className={team.id === currentTeamId ? "bg-baseball-lightgreen/50 font-medium" : ""}
                          onClick={() => {
                            if (team.id !== currentTeamId) {
                              setCurrentTeamId(team.id);
                              toast.success(`Switched to ${team.name}`);
                            }
                          }}
                        >
                          {team.name}
                          {team.id === currentTeamId && (
                            <span className="ml-auto text-xs bg-baseball-green text-white px-1.5 py-0.5 rounded-full">
                              Current
                            </span>
                          )}
                        </DropdownMenuItem>
                      ))}
                      <DropdownMenuItem asChild>
                        <Link to="/team-management" className="cursor-pointer w-full">
                          <PlusCircle className="mr-2 h-4 w-4 text-baseball-green" />
                          <span>Manage Teams</span>
                        </Link>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                
                {/* Coaching Mode Display */}
                <div className={cn(
                  "inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium border",
                  modeInfo.color
                )}>
                  <modeInfo.icon className="h-4 w-4" />
                  <span>{modeInfo.label}</span>
                  <span className="text-xs opacity-70">• {modeInfo.description}</span>
                </div>
              </div>
              
              <div className="mt-4 md:mt-0 flex gap-3">
                <Link to="/create-lineup" state={null}>
                  <Button size="lg" className="shadow-lg">
                    <ClipboardList className="mr-2 h-5 w-5" />
                    Create New Lineup
                  </Button>
                </Link>
                <Link to="/batch-games">
                  <Button size="lg" variant="outline">
                    <CalendarDays className="mr-2 h-5 w-5" />
                    Multiple Games
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
      )}

      {/* Mobile Team Selector */}
      {isMobile && (
        <div className="px-4 py-3 bg-white border-b">
          <div className="flex items-center justify-between mb-2">
            <h1 className="text-lg font-semibold text-baseball-navy truncate flex-1 mr-2">
              {teamName}
            </h1>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 px-2">
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                {teams.map(team => (
                  <DropdownMenuItem
                    key={team.id}
                    className={team.id === currentTeamId ? "bg-baseball-lightgreen/50 font-medium" : ""}
                    onClick={() => {
                      if (team.id !== currentTeamId) {
                        setCurrentTeamId(team.id);
                        toast.success(`Switched to ${team.name}`);
                      }
                    }}
                  >
                    <span className="truncate">{team.name}</span>
                  </DropdownMenuItem>
                ))}
                <DropdownMenuItem asChild>
                  <Link to="/team-management" className="cursor-pointer w-full">
                    <PlusCircle className="mr-2 h-4 w-4" />
                    <span>Manage Teams</span>
                  </Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          
          {/* Mobile Coaching Mode Display */}
          <div className={cn(
            "inline-flex items-center gap-2 px-2 py-1 rounded-full text-xs font-medium border",
            modeInfo.color
          )}>
            <modeInfo.icon className="h-3 w-3" />
            <span>{modeInfo.label}</span>
          </div>
        </div>
      )}

      <main className="flex-1 container mx-auto px-4 py-6">
        {/* Quick Start Message for New Users */}
        {players.length === 0 && (
          <Card className="mb-6 border-amber-200 bg-amber-50">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <Target className="h-5 w-5 text-amber-600 mt-0.5" />
                <div>
                  <h3 className="font-semibold text-amber-900">Quick Start</h3>
                  <p className="text-sm text-amber-700 mt-1">
                    Start by adding players to your roster, then create your first lineup!
                  </p>
                  <Link to="/team-roster">
                    <Button size="sm" className="mt-3">
                      Add Players
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Action Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          {menuLinks.map((link, index) => (
            <MenuCard key={index} {...link} />
          ))}
        </div>

        {/* Recent Lineups */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Lineups</CardTitle>
            <CardDescription>
              Your latest game lineups
            </CardDescription>
          </CardHeader>
          <CardContent>
            <LineupTable />
          </CardContent>
        </Card>

        {/* Admin Link */}
        {isAdmin && (
          <div className="mt-8 text-center">
            <Link to="/admin">
              <Button variant="outline" size="sm">
                <ShieldCheck className="mr-2 h-4 w-4" />
                Admin Dashboard
              </Button>
            </Link>
          </div>
        )}
      </main>

      {/* Mobile Quick Actions */}
      {isMobile && (
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t p-4 flex gap-2">
          <Link to="/create-lineup" className="flex-1">
            <Button className="w-full" size="sm">
              <ClipboardList className="mr-2 h-4 w-4" />
              New Lineup
            </Button>
          </Link>
          <Link to="/team-roster" className="flex-1">
            <Button variant="outline" className="w-full" size="sm">
              <Users className="mr-2 h-4 w-4" />
              Roster
            </Button>
          </Link>
        </div>
      )}

      <Footer />
    </div>
  );
};

export default DashboardNew;
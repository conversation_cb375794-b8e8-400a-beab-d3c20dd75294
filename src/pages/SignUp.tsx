import { useState } from "react";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, UserPlus, ArrowLeft, CheckCircle, Mail } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";

const SignUp = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const { signUp } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const isPostPayment = searchParams.get('payment') === 'success';

  const handleResendEmail = async () => {
    setIsResending(true);
    try {
      const result = await signUp(email, password);
      if (!result.error) {
        toast.success("Verification email resent! Please check your inbox.");
      } else {
        toast.error("Failed to resend email. Please try again.");
      }
    } catch (error) {
      toast.error("Failed to resend email. Please try again.");
    } finally {
      setIsResending(false);
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const trimmedEmail = email.trim();
    
    if (!emailRegex.test(trimmedEmail)) {
      toast.error("Please enter a valid email address");
      return;
    }

    if (password !== confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }

    if (password.length < 6) {
      toast.error("Password must be at least 6 characters long");
      return;
    }

    setIsLoading(true);

    try {
      const result = await signUp(trimmedEmail, password);

      if (result.error) {
        if (result.error.message?.includes('already registered')) {
          toast.error("This email is already registered. Please try signing in instead.");
        } else {
          toast.error(result.error.message || "Registration failed");
        }
      } else {
        setEmailSent(true);
        toast.success("Registration successful! Please check your email for verification.");
      }
    } catch (error: any) {
      console.error("Signup error:", error);
      toast.error(error.message || "Registration failed");
    } finally {
      setIsLoading(false);
    }
  };

  if (emailSent) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-baseball-lightblue/30 p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 mb-4 flex items-center justify-center bg-green-100 rounded-full">
              <Mail className="h-8 w-8 text-green-600" />
            </div>
            <CardTitle className="text-2xl text-baseball-navy">Check Your Email</CardTitle>
            <CardDescription>
              We've sent a verification link to <strong>{email}</strong>
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center text-sm text-gray-600">
              <p>Click the link in your email to verify your account and complete your registration.</p>
              {isPostPayment ? (
                <p className="mt-2 text-green-600 font-medium">Your payment has been processed. After verification, you'll have immediate access to Dugout Boss.</p>
              ) : (
                <p className="mt-2">After verification, you'll be able to purchase access and start using Dugout Boss.</p>
              )}
            </div>

            <div className="flex flex-col gap-2">
              {isPostPayment ? (
                <>
                  <p className="text-sm text-green-600 font-medium mb-2">
                    Payment completed! After email verification, you'll have full access to Dugout Boss.
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => navigate('/sign-in')}
                    className="w-full"
                  >
                    Sign In After Verification
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    onClick={() => navigate('/pricing')}
                    className="w-full bg-baseball-green hover:bg-baseball-green/90"
                  >
                    Continue to Pricing
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => navigate('/sign-in')}
                    className="w-full"
                  >
                    Back to Sign In
                  </Button>
                </>
              )}
              
              <div className="border-t pt-2 mt-2">
                <p className="text-xs text-gray-500 mb-2">Didn't receive the email?</p>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleResendEmail}
                  disabled={isResending}
                  className="w-full text-xs"
                >
                  {isResending ? (
                    <>
                      <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                      Resending...
                    </>
                  ) : (
                    'Resend Verification Email'
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-baseball-lightblue/30 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl text-baseball-navy">Join Dugout Boss</CardTitle>
          <CardDescription>
            Create your account to get started with professional lineup management
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSignUp} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                required
                disabled={isLoading}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="••••••••"
                required
                disabled={isLoading}
                minLength={6}
              />
              <p className="text-xs text-gray-500">Must be at least 6 characters long</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <Input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="••••••••"
                required
                disabled={isLoading}
                minLength={6}
              />
            </div>

            <Button
              type="submit"
              className="w-full bg-baseball-navy hover:bg-baseball-navy/90"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Account...
                </>
              ) : (
                <>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Join Now
                </>
              )}
            </Button>
          </form>

          <div className="mt-6 text-center space-y-4">
            <p className="text-sm text-gray-600">
              Already have an account?{" "}
              <Link to="/sign-in" className="text-baseball-navy hover:underline font-medium">
                Sign in here
              </Link>
            </p>

            <div className="border-t pt-4">
              <Link to="/demo-login">
                <Button variant="outline" className="w-full bg-baseball-lightgreen/20 border-baseball-green text-baseball-navy hover:bg-baseball-lightgreen/30">
                  Try Demo Mode
                </Button>
              </Link>
            </div>

            <Button
              variant="link"
              onClick={() => navigate('/')}
              className="text-baseball-navy"
            >
              <ArrowLeft className="mr-1 h-4 w-4" />
              Back to Home
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SignUp;


import { Link } from "react-router-dom";
import { useTeam } from "@/contexts/TeamContext";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import MenuCard, { MenuLink } from "@/components/MenuCard";
import LineupTable from "@/components/LineupTable";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, ClipboardList, CalendarDays, Settings, HelpCircle, Circle } from "lucide-react";

const Index = () => {
  const { teamName } = useTeam();

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <div className="bg-baseball-lightblue/40 py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div>
              <h1 className="text-3xl md:text-4xl font-bold text-baseball-navy mb-2">
                {teamName} Lineup Manager
              </h1>
              <p className="text-baseball-navy/70 text-lg">
                Create perfect field rotations and batting orders for your team
              </p>
            </div>
            <div className="mt-4 md:mt-0">
              <Link to="/create-lineup">
                <Button size="lg" className="bg-baseball-green hover:bg-baseball-green/90 text-white">
                  <ClipboardList className="mr-2 h-5 w-5" />
                  Create New Lineup
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="grid md:grid-cols-2 gap-8">
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card className="border-t-4 border-baseball-green overflow-hidden">
              <CardHeader className="bg-baseball-navy text-white">
                <CardTitle className="flex items-center gap-2">
                  <Circle size={20} />
                  Quick Actions
                </CardTitle>
                <CardDescription className="text-gray-300">
                  Common tasks for managing your team
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Link to="/create-lineup">
                    <Button variant="outline" className="w-full h-24 flex flex-col items-center justify-center gap-2 hover:bg-baseball-lightgreen/30 border-baseball-green/30">
                      <ClipboardList className="h-6 w-6 text-baseball-green" />
                      <span>Create New Lineup</span>
                    </Button>
                  </Link>
                  <Link to="/team-roster">
                    <Button variant="outline" className="w-full h-24 flex flex-col items-center justify-center gap-2 hover:bg-baseball-lightgreen/30 border-baseball-green/30">
                      <Users className="h-6 w-6 text-baseball-green" />
                      <span>Manage Roster</span>
                    </Button>
                  </Link>
                  <Link to="/rotation-rules">
                    <Button variant="outline" className="w-full h-24 flex flex-col items-center justify-center gap-2 hover:bg-baseball-lightgreen/30 border-baseball-green/30">
                      <Settings className="h-6 w-6 text-baseball-green" />
                      <span>Set Rotation Rules</span>
                    </Button>
                  </Link>
                  <Link to="/faq">
                    <Button variant="outline" className="w-full h-24 flex flex-col items-center justify-center gap-2 hover:bg-baseball-lightgreen/30 border-baseball-green/30">
                      <HelpCircle className="h-6 w-6 text-baseball-green" />
                      <span>Help & FAQ</span>
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            {/* Team & Players */}
            <MenuCard
              icon={<Users size={24} className="text-baseball-green" />}
              title="Team & Player Management"
            >
              <MenuLink to="/team-roster">Player roster & position lockouts</MenuLink>
              <MenuLink to="/rotation-rules">Set fielding rotation rules</MenuLink>
              <MenuLink to="/parent-contacts">Parent contact information</MenuLink>
            </MenuCard>

            {/* Settings */}
            <MenuCard
              icon={<Settings size={24} className="text-baseball-green" />}
              title="Settings"
            >
              <MenuLink to="/user-profile">Update your profile</MenuLink>
              <MenuLink to="/assistant-coaches">Give access to assistant coaches</MenuLink>
              <MenuLink to="/user-profile">Change your login password</MenuLink>
            </MenuCard>

            {/* Help */}
            <MenuCard
              icon={<HelpCircle size={24} className="text-baseball-green" />}
              title="How do I:"
            >
              <MenuLink to="/faq">Frequently Asked Questions</MenuLink>
              <MenuLink to="/contact">Contact Support</MenuLink>
            </MenuCard>
          </div>

          <div className="space-y-6">
            {/* Lineups Section */}
            <Card className="border-t-4 border-baseball-green">
              <CardHeader className="bg-baseball-navy text-white">
                <CardTitle className="flex items-center gap-2">
                  <ClipboardList size={24} />
                  Your Lineups
                </CardTitle>
                <CardDescription className="text-gray-300">
                  View, edit and manage your saved lineups
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="space-y-6">
                  {/* Create new lineup section */}
                  <div className="bg-baseball-lightgreen/30 border border-baseball-green/30 rounded-lg p-4">
                    <h4 className="font-bold text-baseball-navy mb-2">Create a new lineup</h4>
                    <p className="text-sm text-muted-foreground mb-3">
                      Set up fielding positions and batting order for your next game.
                    </p>
                    <Link to="/create-lineup">
                      <Button className="bg-baseball-green hover:bg-baseball-green/90 text-white">
                        <ClipboardList className="mr-2 h-4 w-4" />
                        Create New Lineup
                      </Button>
                    </Link>
                  </div>

                  {/* Retrieve lineup section */}
                  <div className="space-y-2">
                    <h4 className="font-bold text-baseball-navy flex items-center gap-2">
                      <CalendarDays size={18} />
                      Saved Lineups
                    </h4>
                    <p className="text-sm text-muted-foreground mb-3">
                      View or modify your existing lineups.
                    </p>
                    <LineupTable type="retrieve" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Index;

import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { supabase } from '@/supabaseClient';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, LogIn, Mail } from 'lucide-react';
import { toast } from 'sonner';
import { setAdminAccountFlags, setDemoAccountFlags, clearAllAuthFlags } from '@/utils/authUtils';

const SignIn = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [resetEmail, setResetEmail] = useState('');

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Set a flag to indicate we're in the process of logging in
    sessionStorage.setItem('is_logging_in', 'true');

    try {
      // Special handling for admin and demo accounts before login attempt
      if (email.includes('admin') || email === '<EMAIL>') {
        console.log("Admin account login detected - setting up special handling");
        setAdminAccountFlags();
      } else if (email.includes('demo')) {
        console.log("Demo account login detected - setting up special handling");
        setDemoAccountFlags();
      } else {
        console.log("Regular user login detected - clearing auth flags");
        clearAllAuthFlags();
      }

      // Try to sign in with the provided credentials
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('Login error:', error);
        toast.error(`Login failed: ${error.message}`);
        // Clear the logging in flag on error
        sessionStorage.removeItem('is_logging_in');
      } else {
        // Successful login
        // Successful login - show appropriate message
        if (email.includes('admin') || email === '<EMAIL>') {
          toast.success('Welcome back, Admin! You have full access.');
        } else if (email.includes('demo')) {
          toast.success('Welcome to Demo Mode!');
        } else {
          toast.success('Login successful!');
        }

        // Small delay to allow auth context to process the login
        setTimeout(() => {
          // Redirect to dashboard
          navigate('/dashboard');
          // Clear the logging in flag
          sessionStorage.removeItem('is_logging_in');
        }, 500);
      }
    } catch (err) {
      console.error('Unexpected error:', err);
      toast.error(`An unexpected error occurred: ${err instanceof Error ? err.message : String(err)}`);
      // Clear the logging in flag
      sessionStorage.removeItem('is_logging_in');
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!resetEmail) {
      toast.error('Please enter your email address');
      return;
    }

    setIsLoading(true);
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(resetEmail, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) {
        throw error;
      }

      toast.success('Password reset email sent! Check your inbox.');
      setShowForgotPassword(false);
      setResetEmail('');
    } catch (error: any) {
      console.error('Password reset error:', error);
      toast.error(error.message || 'Failed to send reset email');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-baseball-lightblue/30">
      <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-lg">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-baseball-navy">Sign In</h1>
          <p className="mt-2 text-gray-600">
            Sign in to access your Dugout Boss account
          </p>

        </div>

        {!showForgotPassword ? (
          <form onSubmit={handleLogin} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="••••••••"
                required
              />
            </div>

            <Button
              type="submit"
              className="w-full bg-baseball-navy hover:bg-baseball-navy/90"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Signing in...
                </>
              ) : (
                <>
                  <LogIn className="mr-2 h-4 w-4" />
                  Sign In
                </>
              )}
            </Button>

            <div className="text-center">
              <button
                type="button"
                onClick={() => setShowForgotPassword(true)}
                className="text-sm text-baseball-navy hover:underline"
              >
                Forgot your password?
              </button>
            </div>
          </form>
        ) : (
          <form onSubmit={handleForgotPassword} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="resetEmail">Email Address</Label>
              <Input
                id="resetEmail"
                type="email"
                value={resetEmail}
                onChange={(e) => setResetEmail(e.target.value)}
                placeholder="<EMAIL>"
                required
              />
              <p className="text-sm text-gray-600">
                Enter your email address and we'll send you a link to reset your password.
              </p>
            </div>

            <div className="space-y-3">
              <Button
                type="submit"
                className="w-full bg-baseball-navy hover:bg-baseball-navy/90"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Sending...
                  </>
                ) : (
                  <>
                    <Mail className="mr-2 h-4 w-4" />
                    Send Reset Email
                  </>
                )}
              </Button>

              <Button
                type="button"
                variant="outline"
                className="w-full"
                onClick={() => {
                  setShowForgotPassword(false);
                  setResetEmail('');
                }}
              >
                Back to Sign In
              </Button>
            </div>
          </form>
        )}

        <div className="mt-6 text-center space-y-4">
          <p className="text-sm text-gray-600">
            Don't have an account?{" "}
            <Link to="/pricing" className="text-baseball-navy hover:underline font-medium">
              View pricing plans
            </Link>
          </p>

          <div className="border-t pt-4">
            <Link to="/demo-login">
              <Button variant="outline" className="w-full bg-baseball-lightgreen/20 border-baseball-green text-baseball-navy hover:bg-baseball-lightgreen/30">
                Try Demo Mode
              </Button>
            </Link>
          </div>

          <Button
            variant="link"
            onClick={() => navigate('/')}
            className="text-baseball-navy"
          >
            Return to Home
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SignIn;

import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useTeam, Player, InningLineup } from "@/contexts/TeamContext";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { Circle, AlertCircle, UserCheck, UserMinus, User, AlertTriangle } from "lucide-react";
import { useLineupHistory } from "@/hooks/useLineupHistory";
import { UndoRedoButtons } from "@/components/UndoRedoButtons";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

const EditInning = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { lineups, updateLineup, getAvailablePlayers } = useTeam();

  const [lineup, setLineup] = useState<any>(null);
  const [inningNumber, setInningNumber] = useState<number>(0);
  const [inningData, setInningData] = useState<InningLineup | null>(null);
  const [availablePlayers, setAvailablePlayers] = useState<Player[]>([]);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  
  // Initialize lineup history hook
  const {
    data: historyLineup,
    pushState,
    undo,
    redo,
    getHistoryInfo,
    clearHistory,
  } = useLineupHistory(lineup || null);

  // State for confirmation dialog
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingChange, setPendingChange] = useState<{
    position: string;
    player: string;
    currentPosition?: string;
    targetPlayer?: string; // Player currently in the target position
    isSwap?: boolean; // Whether this is a swap operation
  } | null>(null);

  // State for restriction warning dialog
  const [showRestrictionDialog, setShowRestrictionDialog] = useState(false);
  const [pendingRestriction, setPendingRestriction] = useState<{
    position: string;
    player: Player;
    restriction: string;
  } | null>(null);

  // Function to check and fix duplicate player assignments in an inning
  const fixDuplicatePlayerAssignments = (inning: InningLineup): InningLineup => {
    const playerPositions = new Map<string, string>();
    const fixedInning = { ...inning, positions: { ...inning.positions, bench: [...inning.positions.bench] } };

    // First pass: identify all player positions
    Object.entries(inning.positions).forEach(([posKey, posValue]) => {
      if (posKey === 'bench') {
        // Handle bench array
        if (Array.isArray(posValue)) {
          posValue.forEach((playerName, index) => {
            if (playerName && playerName.trim() !== '') {
              if (!playerPositions.has(playerName)) {
                playerPositions.set(playerName, `bench${index}`);
              } else {
                // Player already assigned elsewhere, clear this position
                fixedInning.positions.bench[index] = '';
              }
            }
          });
        }
      } else if (posValue && typeof posValue === 'string' && posValue.trim() !== '') {
        // Handle regular positions
        const playerName = posValue as string;
        if (!playerPositions.has(playerName)) {
          playerPositions.set(playerName, posKey);
        } else {
          // Player already assigned elsewhere, clear this position
          (fixedInning.positions as any)[posKey] = '';
        }
      }
    });

    return fixedInning;
  };

  // Parse query parameters or location state
  useEffect(() => {
    console.log("EditInning component mounted with location:", {
      pathname: location.pathname,
      search: location.search,
      state: location.state,
      key: location.key
    });

    // First check if we have lineup data in location state
    const lineupFromState = location.state?.lineup;

    if (lineupFromState) {
      console.log("Found lineup in location state:", lineupFromState);

      // Check if we have a valid lineup with innings
      if (!lineupFromState.innings || !Array.isArray(lineupFromState.innings) || lineupFromState.innings.length === 0) {
        console.error("Invalid lineup data - missing innings array:", lineupFromState);

        // Try to recover from localStorage debug data
        try {
          const debugLineup = localStorage.getItem('debug_lineup');
          if (debugLineup) {
            const parsedLineup = JSON.parse(debugLineup);
            console.log("Recovered lineup from localStorage:", parsedLineup);

            if (parsedLineup.innings && Array.isArray(parsedLineup.innings) && parsedLineup.innings.length > 0) {
              setLineup(parsedLineup);

              // Get available players
              const players = getAvailablePlayers(parsedLineup);
              setAvailablePlayers(players);

              // Set inning number to 1 for first inning
              const inningNumber = 1;
              setInningNumber(inningNumber);

              // Find inning
              const foundInning = parsedLineup.innings[0];

              // Deep clone the inning data to avoid mutating the original
              const clonedInning = JSON.parse(JSON.stringify(foundInning));

              // Check for and fix duplicate player assignments
              const fixedInning = fixDuplicatePlayerAssignments(clonedInning);

              setInningData(fixedInning);
              return;
            }
          }
        } catch (e) {
          console.error("Failed to recover lineup from localStorage:", e);
        }

        toast.error("Invalid lineup data - missing innings");
        navigate("/dashboard");
        return;
      }

      setLineup(lineupFromState);
      // Clear history when loading a new lineup
      clearHistory(lineupFromState);

      // Get available players
      const players = getAvailablePlayers(lineupFromState);
      console.log("EditInning: Available players loaded:", players.map(p => ({
        name: p.name,
        id: p.id,
        hasTeamRoles: !!p.teamRoles,
        teamRoles: p.teamRoles,
        hasPositionRestrictions: !!p.positionRestrictions,
        positionRestrictions: p.positionRestrictions
      })));
      
      // CRITICAL DEBUG: Check Finn specifically
      const finn = players.find(p => p.name.toLowerCase().includes('finn'));
      if (finn) {
        console.warn("🚨 FINN DATA CHECK:", {
          name: finn.name,
          hasTeamRoles: !!finn.teamRoles,
          teamRoles: finn.teamRoles,
          fullPlayerObject: finn
        });
      }
      
      setAvailablePlayers(players);

      // Get inning number from location state or URL params
      const inningNumber = location.state?.inningNumber || parseInt(new URLSearchParams(location.search).get("inningNumber") || "1");
      console.log("Setting inning number from state:", inningNumber);
      setInningNumber(inningNumber);

      // Find inning
      const foundInning = lineupFromState.innings.find((i: InningLineup) => i.inning === inningNumber);
      if (!foundInning) {
        console.error("Inning not found in lineup data:", lineupFromState);

        // If we can't find the inning by number, just use the first one
        if (lineupFromState.innings.length > 0) {
          const firstInning = lineupFromState.innings[0];
          console.log("Using first inning instead:", firstInning);

          // Deep clone the inning data to avoid mutating the original
          const clonedInning = JSON.parse(JSON.stringify(firstInning));

          // Check for and fix duplicate player assignments
          const fixedInning = fixDuplicatePlayerAssignments(clonedInning);

          setInningData(fixedInning);
          return;
        }

        toast.error("Inning not found in lineup data");
        navigate("/dashboard");
        return;
      }

      // Deep clone the inning data to avoid mutating the original
      const clonedInning = JSON.parse(JSON.stringify(foundInning));

      // Check for and fix duplicate player assignments
      const fixedInning = fixDuplicatePlayerAssignments(clonedInning);

      // If duplicates were found, show a notification
      if (JSON.stringify(clonedInning) !== JSON.stringify(fixedInning)) {
        toast.info("Fixed duplicate player assignments in this inning");
      }

      setInningData(fixedInning);
      return;
    }

    // If no lineup in state, check URL parameters
    const searchParams = new URLSearchParams(location.search);
    const lineupId = searchParams.get("lineupId");
    const inningNum = searchParams.get("inningNumber");

    if (!lineupId || !inningNum) {
      console.error("Missing lineup ID or inning number in URL parameters");
      toast.error("Missing lineup information. Redirecting to dashboard...");

      // Save debug info to localStorage
      try {
        localStorage.setItem('debug_edit_inning_error', JSON.stringify({
          error: 'Missing lineup ID or inning number',
          search: location.search,
          lineupId,
          inningNum,
          timestamp: new Date().toISOString()
        }));
      } catch (e) {
        console.error("Failed to save debug info:", e);
      }

      // Redirect to dashboard instead of homepage
      navigate("/dashboard");
      return;
    }

    console.log(`Looking for lineup with ID: ${lineupId}`);
    console.log(`Available lineups:`, lineups.map(l => ({ id: l.id, name: l.name })));

    // Find lineup
    const foundLineup = lineups.find(l => l.id === lineupId);
    if (!foundLineup) {
      console.error(`Lineup not found with ID: ${lineupId}`);
      toast.error("Lineup not found. Redirecting to dashboard...");

      // Save debug info to localStorage
      try {
        localStorage.setItem('debug_edit_inning_error', JSON.stringify({
          error: 'Lineup not found',
          lineupId,
          inningNum,
          availableLineups: lineups.map(l => ({ id: l.id, name: l.name })),
          timestamp: new Date().toISOString()
        }));
      } catch (e) {
        console.error("Failed to save debug info:", e);
      }

      // Redirect to dashboard instead of homepage
      navigate("/dashboard");
      return;
    }

    setLineup(foundLineup);

    // Get available players
    const players = getAvailablePlayers(foundLineup);
    setAvailablePlayers(players);

    // Find inning
    const inningNumber = parseInt(inningNum);
    setInningNumber(inningNumber);

    const foundInning = foundLineup.innings.find((i: InningLineup) => i.inning === inningNumber);
    if (!foundInning) {
      console.error(`Inning ${inningNumber} not found in lineup:`, foundLineup);

      // Try to use the first inning as a fallback
      if (foundLineup.innings && foundLineup.innings.length > 0) {
        const firstInning = foundLineup.innings[0];
        console.log("Using first inning as fallback:", firstInning);

        // Deep clone the inning data to avoid mutating the original
        const clonedInning = JSON.parse(JSON.stringify(firstInning));

        // Check for and fix duplicate player assignments
        const fixedInning = fixDuplicatePlayerAssignments(clonedInning);

        // Update the inning number to match the requested one
        fixedInning.inning = inningNumber;

        setInningData(fixedInning);

        toast.info(`Created new inning ${inningNumber} based on inning 1`);
        return;
      }

      // Save debug info to localStorage
      try {
        localStorage.setItem('debug_edit_inning_error', JSON.stringify({
          error: 'Inning not found',
          lineupId,
          inningNumber,
          availableInnings: foundLineup.innings.map(i => i.inning),
          timestamp: new Date().toISOString()
        }));
      } catch (e) {
        console.error("Failed to save debug info:", e);
      }

      toast.error(`Inning ${inningNumber} not found. Redirecting to lineup view...`);
      navigate(`/view-lineup/${lineupId}`);
      return;
    }

    // Deep clone the inning data to avoid mutating the original
    const clonedInning = JSON.parse(JSON.stringify(foundInning));

    // Check for and fix duplicate player assignments
    const fixedInning = fixDuplicatePlayerAssignments(clonedInning);

    // If duplicates were found, show a notification
    if (JSON.stringify(clonedInning) !== JSON.stringify(fixedInning)) {
      toast.info("Fixed duplicate player assignments in this inning");
    }

    setInningData(fixedInning);
  }, [location.search, lineups, navigate, getAvailablePlayers]);

  // Convert player IDs to names when availablePlayers changes
  useEffect(() => {
    // Skip conversion during save to prevent visual flicker
    if (isSaving) return;
    
    if (inningData && availablePlayers.length > 0) {
      const convertedInning = convertPlayerIdsToNames(inningData);
      // Only update if the conversion actually changed something
      if (JSON.stringify(convertedInning) !== JSON.stringify(inningData)) {
        console.log("Converting player IDs to names in inning data");
        setInningData(convertedInning);
      }
    }
  }, [availablePlayers, isSaving]);
  
  // Sync lineup state with history data when history changes
  useEffect(() => {
    if (historyLineup && historyLineup !== lineup) {
      setLineup(historyLineup);
      // Update inning data if it exists in the history lineup
      const historyInning = historyLineup.innings?.find((i: InningLineup) => i.inning === inningNumber);
      if (historyInning) {
        setInningData(historyInning);
      }
    }
  }, [historyLineup, inningNumber]);

  // Helper function to convert player IDs to player names in inning data
  const convertPlayerIdsToNames = (inningData: InningLineup): InningLineup => {
    if (!inningData || !availablePlayers.length) return inningData;

    const convertedInning = { ...inningData };
    const convertedPositions = { ...inningData.positions };

    // Convert field positions from IDs to names
    const fieldPositions = [
      "pitcher", "catcher", "firstBase", "secondBase", 
      "thirdBase", "shortstop", "leftField", "centerField", "rightField"
    ];

    for (const position of fieldPositions) {
      const playerId = convertedPositions[position as keyof typeof convertedPositions];
      if (playerId && typeof playerId === 'string' && position !== 'bench') {
        const player = availablePlayers.find(p => p.id === playerId);
        if (player) {
          (convertedPositions as any)[position] = player.name;
        }
      }
    }

    // Convert bench positions from IDs to names
    if (convertedPositions.bench && Array.isArray(convertedPositions.bench)) {
      convertedPositions.bench = convertedPositions.bench.map(playerId => {
        if (playerId && typeof playerId === 'string') {
          const player = availablePlayers.find(p => p.id === playerId);
          return player ? player.name : playerId;
        }
        return playerId;
      });
    }

    convertedInning.positions = convertedPositions;
    return convertedInning;
  };

  // Helper function to convert player names back to IDs for saving
  const convertPlayerNamesToIds = (inningData: InningLineup): InningLineup => {
    if (!inningData || !availablePlayers.length) return inningData;

    const convertedInning = { ...inningData };
    const convertedPositions = { ...inningData.positions };

    // Convert field positions from names to IDs
    const fieldPositions = [
      "pitcher", "catcher", "firstBase", "secondBase", 
      "thirdBase", "shortstop", "leftField", "centerField", "rightField"
    ];

    for (const position of fieldPositions) {
      const playerName = convertedPositions[position as keyof typeof convertedPositions];
      if (playerName && typeof playerName === 'string' && position !== 'bench') {
        const player = availablePlayers.find(p => p.name === playerName);
        if (player) {
          (convertedPositions as any)[position] = player.id;
        }
      }
    }

    // Convert bench positions from names to IDs
    if (convertedPositions.bench && Array.isArray(convertedPositions.bench)) {
      convertedPositions.bench = convertedPositions.bench.map(playerName => {
        if (playerName && typeof playerName === 'string') {
          const player = availablePlayers.find(p => p.name === playerName);
          return player ? player.id : playerName;
        }
        return playerName;
      });
    }

    convertedInning.positions = convertedPositions;
    return convertedInning;
  };

  // Helper function to find a player's current position in the inning
  const findPlayerCurrentPosition = (playerName: string): string | null => {
    if (!inningData || !playerName) return null;

    // Check field positions
    const fieldPositions = [
      "pitcher", "catcher", "firstBase", "secondBase", 
      "thirdBase", "shortstop", "leftField", "centerField", "rightField"
    ];

    for (const pos of fieldPositions) {
      if (inningData.positions[pos as keyof typeof inningData.positions] === playerName) {
        return pos;
      }
    }

    // Check bench positions
    const benchIndex = inningData.positions.bench.findIndex(name => name === playerName);
    if (benchIndex !== -1) {
      return `bench${benchIndex + 1}`;
    }

    return null;
  };

  // Helper function to get the player currently in a specific position
  const getPlayerInPosition = (position: string): string | null => {
    if (!inningData) return null;

    if (position.startsWith("bench")) {
      const benchIndex = parseInt(position.replace("bench", "")) - 1;
      return inningData.positions.bench[benchIndex] || null;
    } else {
      const positionValue = inningData.positions[position as keyof typeof inningData.positions];
      return (typeof positionValue === 'string' ? positionValue : null) || null;
    }
  };

  // Helper function to get a readable position name
  const getPositionLabel = (positionId: string): string => {
    const position = positions.find(p => p.id === positionId);
    return position ? position.label : positionId;
  };

  // Helper function to convert internal position ID to display name
  const getPositionDisplayName = (positionId: string): string => {
    const mapping: {[key: string]: string} = {
      "leftField": "Left Field",
      "centerField": "Center Field",
      "rightField": "Right Field",
      "thirdBase": "Third Base",
      "shortstop": "Shortstop",
      "secondBase": "Second Base",
      "firstBase": "First Base",
      "catcher": "Catcher",
      "pitcher": "Pitcher"
    };
    return mapping[positionId] || positionId;
  };

  // Function to check if a player has a restriction for a position
  const checkPositionRestriction = (playerObj: Player, positionId: string): string | null => {
    console.log(`Checking restriction for ${playerObj.name} at ${positionId}:`, {
      teamRoles: playerObj.teamRoles,
      positionRestrictions: playerObj.positionRestrictions,
      hasTeamRoles: !!playerObj.teamRoles,
      specificRole: playerObj.teamRoles?.[positionId],
      playerFullData: playerObj
    });
    
    // Check if teamRoles exists at all - if not, there's a data loading issue
    if (!playerObj.teamRoles && !playerObj.positionRestrictions) {
      console.warn(`⚠️ Player ${playerObj.name} has NO restriction data at all!`);
    }
    
    // Use team roles (new system) to check restrictions
    if (playerObj.teamRoles && playerObj.teamRoles[positionId] === 'avoid') {
      console.log(`Found restriction via teamRoles: ${playerObj.name} cannot play ${positionId}`);
      return getPositionDisplayName(positionId);
    }

    // Legacy support - only check if teamRoles is not available
    if (!playerObj.teamRoles && playerObj.positionRestrictions) {
      const positionName = getPositionDisplayName(positionId);

      // Check pitcher restriction
      if (positionId === "pitcher" && playerObj.positionRestrictions.pitcher) {
        return "Pitcher";
      }

      // Check catcher restriction
      if (positionId === "catcher" && playerObj.positionRestrictions.catcher) {
        return "Catcher";
      }

      // Check first base restriction
      if (positionId === "firstBase" && playerObj.positionRestrictions.firstBase) {
        return "First Base";
      }

      // Check other restrictions
      if (playerObj.positionRestrictions.other) {
        // Direct match
        if (playerObj.positionRestrictions.other === positionName) {
          return positionName;
        }

        // 3B restriction variants
        if (playerObj.positionRestrictions.other === "3B/MI/SS/2B" &&
            (positionId === "thirdBase" || positionId === "shortstop" ||
             positionId === "secondBase")) {
          return "3B/MI/SS/2B";
        }
      }
    }

    return null;
  };

  // Function to apply the player change after restriction warning
  const applyPlayerChangeWithRestriction = async () => {
    if (!pendingRestriction || !inningData) return;

    const { position, player } = pendingRestriction;

    console.log("Applying player change with restriction:", { position, player });

    // Create a completely new object for the updated inning data
    const newPositions = { ...inningData.positions };

    // Create a new bench array (deep copy)
    newPositions.bench = [...inningData.positions.bench];

    // First, remove the player from their current position
    const currentPosition = findPlayerCurrentPosition(player.name);
    if (currentPosition) {
      if (currentPosition.startsWith('bench')) {
        const benchIndex = parseInt(currentPosition.replace('bench', '')) - 1;
        if (benchIndex >= 0 && benchIndex < newPositions.bench.length) {
          newPositions.bench[benchIndex] = '';
        }
      } else {
        newPositions[currentPosition] = '';
      }
    }

    // Then, assign the player to the new position
    if (position.startsWith("bench")) {
      const benchIndex = parseInt(position.replace("bench", "")) - 1;

      if (benchIndex >= 0 && benchIndex < newPositions.bench.length) {
        newPositions.bench[benchIndex] = player.name;
      } else if (benchIndex === newPositions.bench.length) {
        newPositions.bench.push(player.name);
      }
    } else {
      newPositions[position] = player.name;
    }

    // Create a completely new inning data object
    const newInningData = {
      ...inningData,
      positions: newPositions
    };

    console.log("New inning data after player move with restriction:", newInningData);

    // Update the state with the new inning data
    console.log("Updating inning data with restriction:", newInningData);
    setInningData(newInningData);

    // Force a re-render to update the UI with a longer timeout
    setTimeout(() => {
      console.log("Forcing re-render after player move with restriction");
      // This will trigger a re-render with the updated positions
      setInningData(prevData => {
        if (!prevData) return newInningData;
        return {...newInningData};
      });
    }, 200);

    // Reset pending restriction
    setPendingRestriction(null);

    // Show success message with warning
    toast.success(
      <div>
        <p>Moved {player.name} to {getPositionLabel(position)}</p>
        <p className="text-xs text-amber-500 mt-1">Note: This player has a position restriction for this position.</p>
      </div>
    );

    // Update the lineup with the player change
    if (lineup) {
      // Create a deep copy of the lineup
      const updatedLineup = JSON.parse(JSON.stringify(lineup));

      // Find the inning index
      const inningIndex = updatedLineup.innings.findIndex((i: InningLineup) => i.inning === inningNumber);

      if (inningIndex >= 0) {
        // Update the inning in the lineup
        updatedLineup.innings[inningIndex] = newInningData;
      } else {
        // Add the inning to the lineup
        updatedLineup.innings.push(newInningData);
      }

      // Demo mode now uses the database just like regular mode
      const isDemoMode = localStorage.getItem('demo_mode') === 'true';
      if (isDemoMode) {
        console.log("Demo mode detected, using database for lineup storage");

        // We'll let the TeamContext handle the database update
        // This avoids duplicate code and potential issues
        console.log("Letting TeamContext handle database update for demo mode");
      }

      // Store the updated lineup in session storage to ensure it's available immediately
      // when navigating back to the view lineup page
      try {
        sessionStorage.setItem(`lineup_${updatedLineup.id}`, JSON.stringify(updatedLineup));
        console.log("Saved lineup to session storage for immediate access");
      } catch (e) {
        console.error("Failed to save lineup to session storage:", e);
      }

      // Save the updated lineup to localStorage
      try {
        localStorage.setItem('temp_lineup_' + lineup.id, JSON.stringify(updatedLineup));
        console.log("Saved temporary lineup to localStorage after restricted player move");
      } catch (e) {
        console.error("Failed to save temporary lineup to localStorage:", e);
      }

      // Update the lineup in the TeamContext
      try {
        console.log("Updating lineup in TeamContext after restricted player move");
        updateLineup(updatedLineup);
        
        // Push to history
        pushState(updatedLineup, `Inning ${inningNumber}: Moved ${player.name} to ${getPositionLabel(position)} (with restriction)`);
      } catch (e) {
        console.error("Failed to update lineup in TeamContext:", e);
        toast.error("Failed to save player change. Please try again.");
      }
    }
  };

  // Function to apply the pending change
  const applyPlayerChange = () => {
    if (!pendingChange || !inningData) return;

    const { position, player, currentPosition, targetPlayer, isSwap } = pendingChange;

    console.log("Applying player change:", { position, player, currentPosition, targetPlayer, isSwap });

    // Create a completely new object for the updated inning data
    const newPositions = { ...inningData.positions };

    // Create a new bench array (deep copy)
    newPositions.bench = [...inningData.positions.bench];

    if (isSwap && targetPlayer && currentPosition) {
      // This is a swap operation
      console.log(`Swapping ${player} (${currentPosition}) with ${targetPlayer} (${position})`);

      // Place the moving player in the target position
      if (position.startsWith("bench")) {
        const benchIndex = parseInt(position.replace("bench", "")) - 1;
        if (benchIndex >= 0 && benchIndex < newPositions.bench.length) {
          newPositions.bench[benchIndex] = player;
        }
      } else {
        (newPositions as any)[position] = player;
      }

      // Place the target player in the moving player's original position
      if (currentPosition.startsWith('bench')) {
        const benchIndex = parseInt(currentPosition.replace('bench', '')) - 1;
        if (benchIndex >= 0 && benchIndex < newPositions.bench.length) {
          newPositions.bench[benchIndex] = targetPlayer;
        }
      } else {
        (newPositions as any)[currentPosition] = targetPlayer;
      }
    } else {
      // This is a move operation (not a swap)

      // If there's a target player being displaced, we need to handle them
      if (targetPlayer) {
        // Find an empty bench spot for the displaced player
        let foundEmptyBench = false;
        for (let i = 0; i < newPositions.bench.length; i++) {
          if (!newPositions.bench[i] || newPositions.bench[i] === '') {
            newPositions.bench[i] = targetPlayer;
            foundEmptyBench = true;
            break;
          }
        }

        // If no empty bench spot, add to the end
        if (!foundEmptyBench) {
          newPositions.bench.push(targetPlayer);
        }
      }

      // Remove the player from their current position
      if (currentPosition) {
        if (currentPosition.startsWith('bench')) {
          const benchIndex = parseInt(currentPosition.replace('bench', '')) - 1;
          if (benchIndex >= 0 && benchIndex < newPositions.bench.length) {
            newPositions.bench[benchIndex] = '';
          }
        } else {
          (newPositions as any)[currentPosition] = '';
        }
      }

      // Then, assign the player to the new position
      if (position.startsWith("bench")) {
        const benchIndex = parseInt(position.replace("bench", "")) - 1;

        if (benchIndex >= 0 && benchIndex < newPositions.bench.length) {
          newPositions.bench[benchIndex] = player;
        } else if (benchIndex === newPositions.bench.length) {
          newPositions.bench.push(player);
        }
      } else {
        (newPositions as any)[position] = player;
      }
    }

    // Create a completely new inning data object
    const newInningData = {
      ...inningData,
      positions: newPositions
    };

    console.log("New inning data after player move:", newInningData);

    // Update the state with the new inning data
    console.log("Updating inning data after player move:", newInningData);
    setInningData(newInningData);

    // Force a re-render to update the UI with a longer timeout
    setTimeout(() => {
      console.log("Forcing re-render after player move");
      // This will trigger a re-render with the updated positions
      setInningData(prevData => {
        if (!prevData) return newInningData;
        return {...newInningData};
      });
    }, 200);

    // Reset pending change
    setPendingChange(null);

    // Show success message
    if (isSwap && targetPlayer) {
      toast.success(`Swapped ${player} and ${targetPlayer}`);
    } else if (targetPlayer) {
      toast.success(
        <div>
          <p>Moved {player} to {getPositionLabel(position)}</p>
          <p className="text-xs mt-1">{targetPlayer} was moved to the bench</p>
        </div>
      );
    } else {
      toast.success(`Moved ${player} to ${getPositionLabel(position)}`);
    }

    // Update the lineup with the player change
    if (lineup) {
      // Create a deep copy of the lineup
      const updatedLineup = JSON.parse(JSON.stringify(lineup));

      // Find the inning index
      const inningIndex = updatedLineup.innings.findIndex((i: InningLineup) => i.inning === inningNumber);

      if (inningIndex >= 0) {
        // Update the inning in the lineup
        updatedLineup.innings[inningIndex] = newInningData;
      } else {
        // Add the inning to the lineup
        updatedLineup.innings.push(newInningData);
      }

      // Demo mode now uses the database just like regular mode
      const isDemoMode = localStorage.getItem('demo_mode') === 'true';
      if (isDemoMode) {
        console.log("Demo mode detected, using database for lineup storage");
      }

      // Store the updated lineup in session storage to ensure it's available immediately
      // when navigating back to the view lineup page
      try {
        sessionStorage.setItem(`lineup_${updatedLineup.id}`, JSON.stringify(updatedLineup));
        console.log("Saved lineup to session storage for immediate access");
      } catch (e) {
        console.error("Failed to save lineup to session storage:", e);
      }

      // Save the updated lineup to temporary storage
      try {
        localStorage.setItem('temp_lineup_' + lineup.id, JSON.stringify(updatedLineup));
        console.log("Saved temporary lineup to localStorage after player move");
      } catch (e) {
        console.error("Failed to save temporary lineup to localStorage:", e);
      }

      // Update the lineup in the TeamContext
      try {
        console.log("Updating lineup in TeamContext after player move");
        updateLineup(updatedLineup);
        
        // Push to history
        const changeDescription = isSwap 
          ? `Inning ${inningNumber}: Swapped ${player} and ${targetPlayer}`
          : `Inning ${inningNumber}: Moved ${player} to ${getPositionLabel(position)}`;
        pushState(updatedLineup, changeDescription);
      } catch (e) {
        console.error("Failed to update lineup in TeamContext:", e);
        toast.error("Failed to save player change. Please try again.");
      }
    }
  };

  const handlePlayerChange = (position: string, playerName: string) => {
    if (!inningData) return;

    // If player is empty (none selected), just clear the position
    if (!playerName) {
      if (position.startsWith("bench")) {
        const benchIndex = parseInt(position.replace("bench", "")) - 1;

        setInningData(prev => {
          if (!prev) return prev;

          const newInningData = { ...prev };
          const newBench = [...newInningData.positions.bench];

          if (benchIndex >= 0 && benchIndex < newBench.length) {
            newBench[benchIndex] = '';
          }

          newInningData.positions.bench = newBench;

          // Update the lineup in the TeamContext
          if (lineup) {
            setTimeout(() => {
              // Create a deep copy of the lineup
              const updatedLineup = JSON.parse(JSON.stringify(lineup));

              // Find the inning index
              const inningIndex = updatedLineup.innings.findIndex((i: InningLineup) => i.inning === inningNumber);

              if (inningIndex >= 0) {
                // Update the inning in the lineup
                updatedLineup.innings[inningIndex] = newInningData;
              } else {
                // Add the inning to the lineup
                updatedLineup.innings.push(newInningData);
              }

              // Update the lineup in the TeamContext
              try {
                console.log("Updating lineup in TeamContext after clearing bench position");
                updateLineup(updatedLineup);
                
                // Push to history
                pushState(updatedLineup, `Inning ${inningNumber}: Cleared bench position`);
              } catch (e) {
                console.error("Failed to update lineup in TeamContext:", e);
              }
            }, 100);
          }

          return newInningData;
        });
      } else {
        setInningData(prev => {
          if (!prev) return prev;

          const newInningData = {
            ...prev,
            positions: {
              ...prev.positions,
              [position]: ''
            }
          };

          // Update the lineup in the TeamContext
          if (lineup) {
            setTimeout(() => {
              // Create a deep copy of the lineup
              const updatedLineup = JSON.parse(JSON.stringify(lineup));

              // Find the inning index
              const inningIndex = updatedLineup.innings.findIndex((i: InningLineup) => i.inning === inningNumber);

              if (inningIndex >= 0) {
                // Update the inning in the lineup
                updatedLineup.innings[inningIndex] = newInningData;
              } else {
                // Add the inning to the lineup
                updatedLineup.innings.push(newInningData);
              }

              // Update the lineup in the TeamContext
              try {
                console.log("Updating lineup in TeamContext after clearing field position");
                updateLineup(updatedLineup);
                
                // Push to history
                pushState(updatedLineup, `Inning ${inningNumber}: Cleared ${position}`);
              } catch (e) {
                console.error("Failed to update lineup in TeamContext:", e);
              }
            }, 100);
          }

          return newInningData;
        });
      }
      return;
    }

    // Find the player object
    const playerObj = availablePlayers.find(p => p.name === playerName);
    if (!playerObj) {
      toast.error("Player not found");
      return;
    }

    // Check if player is already assigned to a position
    const currentPosition = findPlayerCurrentPosition(playerName);

    // If player is already in this position, do nothing
    if (currentPosition === position) return;

    // Check if the target position already has a player
    const targetPlayer = getPlayerInPosition(position);

    // Check for position restrictions
    if (!position.startsWith("bench")) {
      const restriction = checkPositionRestriction(playerObj, position);

      if (restriction) {
        console.log("🚫 RESTRICTION FOUND - Showing warning dialog", {
          player: playerObj.name,
          position,
          restriction,
          playerTeamRoles: playerObj.teamRoles
        });
        
        // Show restriction warning dialog
        setPendingRestriction({
          position,
          player: playerObj,
          restriction
        });
        setShowRestrictionDialog(true);
        return;
      } else {
        console.log("✅ No restriction found for", playerObj.name, "at", position);
      }
    }

    // Determine if this should be a swap or move operation
    if (currentPosition && targetPlayer) {
      // Both positions have players - offer swap
      setPendingChange({
        position,
        player: playerName,
        currentPosition,
        targetPlayer,
        isSwap: true
      });
      setShowConfirmDialog(true);
      return;
    } else if (currentPosition) {
      // Player is moving from another position to an empty position
      setPendingChange({
        position,
        player: playerName,
        currentPosition,
        isSwap: false
      });
      setShowConfirmDialog(true);
      return;
    } else if (targetPlayer) {
      // Player is moving to an occupied position but has no current position
      // This shouldn't happen in normal flow, but handle it as a replacement
      setPendingChange({
        position,
        player: playerName,
        targetPlayer,
        isSwap: false
      });
      setShowConfirmDialog(true);
      return;
    }

    // Player is not assigned anywhere, proceed with assignment
    if (position.startsWith("bench")) {
      const benchIndex = parseInt(position.replace("bench", "")) - 1;

      setInningData(prev => {
        if (!prev) return prev;

        const newInningData = { ...prev };
        const newBench = [...newInningData.positions.bench];

        if (benchIndex >= 0 && benchIndex < newBench.length) {
          newBench[benchIndex] = playerName;
        } else if (benchIndex === newBench.length) {
          newBench.push(playerName);
        }

        newInningData.positions.bench = newBench;

        // Update the lineup in the TeamContext
        if (lineup) {
          setTimeout(() => {
            // Create a deep copy of the lineup
            const updatedLineup = JSON.parse(JSON.stringify(lineup));

            // Find the inning index
            const inningIndex = updatedLineup.innings.findIndex((i: InningLineup) => i.inning === inningNumber);

            if (inningIndex >= 0) {
              // Update the inning in the lineup
              updatedLineup.innings[inningIndex] = newInningData;
            } else {
              // Add the inning to the lineup
              updatedLineup.innings.push(newInningData);
            }

            // Demo mode now uses the database just like regular mode
            const isDemoMode = localStorage.getItem('demo_mode') === 'true';
            if (isDemoMode) {
              console.log("Demo mode detected, using database for lineup storage");
            }

            // Update the lineup in the TeamContext
            try {
              console.log("Updating lineup in TeamContext after direct player assignment");
              updateLineup(updatedLineup);
              
              // Push to history
              pushState(updatedLineup, `Inning ${inningNumber}: ${playerName} to bench`);
            } catch (e) {
              console.error("Failed to update lineup in TeamContext:", e);
            }
          }, 100);
        }

        return newInningData;
      });
    } else {
      // Regular position
      setInningData(prev => {
        if (!prev) return prev;

        const newInningData = {
          ...prev,
          positions: {
            ...prev.positions,
            [position]: playerName
          }
        };

        // Update the lineup in the TeamContext
        if (lineup) {
          setTimeout(() => {
            // Create a deep copy of the lineup
            const updatedLineup = JSON.parse(JSON.stringify(lineup));

            // Find the inning index
            const inningIndex = updatedLineup.innings.findIndex((i: InningLineup) => i.inning === inningNumber);

            if (inningIndex >= 0) {
              // Update the inning in the lineup
              updatedLineup.innings[inningIndex] = newInningData;
            } else {
              // Add the inning to the lineup
              updatedLineup.innings.push(newInningData);
            }

            // Demo mode now uses the database just like regular mode
            const isDemoMode = localStorage.getItem('demo_mode') === 'true';
            if (isDemoMode) {
              console.log("Demo mode detected, using database for lineup storage");
            }

            // Update the lineup in the TeamContext
            try {
              console.log("Updating lineup in TeamContext after direct player assignment");
              updateLineup(updatedLineup);
              
              // Push to history
              pushState(updatedLineup, `Inning ${inningNumber}: ${playerName} to ${getPositionLabel(position)}`);
            } catch (e) {
              console.error("Failed to update lineup in TeamContext:", e);
            }
          }, 100);
        }

        return newInningData;
      });
    }
  };


  // Function to save the inning changes
  const handleSave = async () => {
    if (!inningData || !lineup) {
      toast.error("No inning data to save");
      return;
    }

    console.log("EditInning: Starting save process");
    setIsSaving(true);

    // Validate inning data for duplicate player assignments
    const validation = validateInningData(inningData);
    if (!validation.isValid) {
      setIsSaving(false);
      toast.error(
        <div>
          <p>Cannot save: Some players are assigned to multiple positions:</p>
          <ul className="list-disc pl-5 mt-2">
            {validation.duplicates.map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
          <p className="mt-2">Please fix these issues before saving.</p>
        </div>
      );
      return;
    }

    // Check for position restrictions and show warnings (but don't prevent saving)
    const restrictionValidation = validatePositionRestrictions(inningData);
    if (!restrictionValidation.isValid) {
      toast.warning(
        <div>
          <p>⚠️ Warning: Some players are assigned to restricted positions:</p>
          <ul className="list-disc pl-5 mt-2">
            {restrictionValidation.violations.map((item: string, index: number) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
          <p className="mt-2">The lineup will be saved, but you may want to review these assignments.</p>
        </div>,
        { duration: 8000 }
      );
    }

    try {
      // Convert player names back to IDs for database storage
      const inningDataWithIds = convertPlayerNamesToIds(inningData);

      // Create a deep copy of the lineup
      const updatedLineup = JSON.parse(JSON.stringify(lineup));

      // Find the inning index
      const inningIndex = updatedLineup.innings.findIndex((i: InningLineup) => i.inning === inningNumber);

      if (inningIndex >= 0) {
        // Update the inning in the lineup with IDs
        updatedLineup.innings[inningIndex] = inningDataWithIds;
      } else {
        // Add the inning to the lineup with IDs
        updatedLineup.innings.push(inningDataWithIds);
      }

      // Save to session storage for immediate access
      sessionStorage.setItem(`lineup_${updatedLineup.id}`, JSON.stringify(updatedLineup));
      console.log("EditInning: Saved lineup to session storage");

      // Update the lineup in the TeamContext (this will handle the database save and show success message)
      try {
        await updateLineup(updatedLineup);
        console.log("EditInning: Successfully updated lineup via TeamContext");

        // Navigate back to the lineup view with the updated lineup in state
        navigate(`/view-lineup/${lineup.id}`, {
          state: { lineup: updatedLineup }
        });
      } catch (e) {
        console.error("Failed to save lineup:", e);
        toast.error("Failed to save lineup");
      }
    } catch (e) {
      console.error("Error saving inning changes:", e);
      toast.error("Failed to save changes");
    } finally {
      setIsSaving(false);
    }
  };

  // Function to check for duplicate player assignments
  const validateInningData = (data: InningLineup): { isValid: boolean, duplicates: string[] } => {
    const playerPositions = new Map<string, string[]>();

    // Check all field positions
    Object.entries(data.positions).forEach(([posKey, posValue]) => {
      if (posKey === 'bench') {
        // Handle bench array
        if (Array.isArray(posValue)) {
          posValue.forEach((playerName, index) => {
            if (playerName) {
              if (!playerPositions.has(playerName)) {
                playerPositions.set(playerName, [`Bench ${index + 1}`]);
              } else {
                playerPositions.get(playerName)?.push(`Bench ${index + 1}`);
              }
            }
          });
        }
      } else if (posValue) {
        // Handle regular positions
        const playerName = posValue as string;
        if (!playerPositions.has(playerName)) {
          playerPositions.set(playerName, [getPositionDisplayName(posKey)]);
        } else {
          playerPositions.get(playerName)?.push(getPositionDisplayName(posKey));
        }
      }
    });

    // Find players assigned to multiple positions
    const duplicates: string[] = [];
    playerPositions.forEach((positions, playerName) => {
      if (positions.length > 1) {
        duplicates.push(`${playerName} (${positions.join(', ')})`);
      }
    });

    return {
      isValid: duplicates.length === 0,
      duplicates
    };
  };

  // Function to check for position restriction violations
  const validatePositionRestrictions = (data: InningLineup): { isValid: boolean, violations: string[] } => {
    const violations: string[] = [];

    // Check all field positions (not bench)
    Object.entries(data.positions).forEach(([posKey, posValue]) => {
      if (posKey !== 'bench' && posValue) {
        const playerName = posValue as string;
        const player = availablePlayers.find(p => p.name === playerName);

        if (player) {
          const restriction = checkPositionRestriction(player, posKey);
          if (restriction) {
            violations.push(`${playerName} cannot play ${getPositionDisplayName(posKey)} (restricted: ${restriction})`);
          }
        }
      }
    });

    return {
      isValid: violations.length === 0,
      violations
    };
  };



  // Field positions grouped by field areas for better UX
  const positionGroups = [
    {
      name: "Battery",
      positions: [
        { id: "pitcher", label: "Pitcher" },
        { id: "catcher", label: "Catcher" }
      ]
    },
    {
      name: "Infield", 
      positions: [
        { id: "firstBase", label: "First Base" },
        { id: "secondBase", label: "Second Base" },
        { id: "shortstop", label: "Shortstop" },
        { id: "thirdBase", label: "Third Base" }
      ]
    },
    {
      name: "Outfield",
      positions: [
        { id: "leftField", label: "Left Field" },
        { id: "centerField", label: "Center Field" },
        { id: "rightField", label: "Right Field" }
      ]
    }
  ];

  // Flatten for backward compatibility
  const fieldPositions = positionGroups.flatMap(group => group.positions);

  // Dynamically create bench positions based on available players
  const benchPositions = [];
  const benchCount = Math.max(0, availablePlayers.length - 9);
  for (let i = 1; i <= benchCount; i++) {
    benchPositions.push({ id: `bench${i}`, label: `Bench ${i}` });
  }

  // Combine all positions
  const positions = [...fieldPositions, ...benchPositions];

  if (!inningData || !lineup) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>;
  }

  return (
    <div className="min-h-screen flex flex-col bg-baseball-tan/20">
      <Header title={`Edit Inning ${inningNumber} - ${lineup.name}`} showBack backLink={`/view-lineup/${lineup.id}`} />

      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto bg-white rounded-lg shadow-lg p-8 border-t-4 border-baseball-green">
          <div className="mb-8">
            <h2 className="text-xl font-bold text-baseball-navy mb-2">Inning {inningNumber} - {lineup.name}</h2>
            <p className="text-gray-600">
              You're editing player positions for <span className="font-semibold">Inning {inningNumber}</span> only.
              Changes made here won't affect other innings.
            </p>

            <div className="mt-3 p-3 bg-baseball-lightgreen/30 rounded-md border border-baseball-green/20">
              <h3 className="text-sm font-semibold text-baseball-navy flex items-center">
                <AlertCircle className="h-4 w-4 mr-1 text-baseball-green" />
                Player Assignment Rules
              </h3>
              <ul className="text-xs text-gray-600 mt-1 list-disc pl-5 space-y-1">
                <li>Players can only be assigned to one position per inning</li>
                <li>If you assign a player to a new position, they'll be removed from their current position</li>
                <li>When moving players between positions, you'll be offered the option to swap them</li>
              </ul>
            </div>

            <div className="mt-3 p-3 bg-amber-50 rounded-md border border-amber-200">
              <h3 className="text-sm font-semibold text-amber-800 flex items-center">
                <AlertTriangle className="h-4 w-4 mr-1 text-amber-500" />
                Position Restrictions
              </h3>
              <p className="text-xs text-gray-600 mt-1">
                Players with position restrictions will be marked with a warning icon (<AlertTriangle className="h-3 w-3 inline text-amber-500" />).
                You'll be warned if you try to assign them to a restricted position.
              </p>
            </div>

          </div>

          <div className="space-y-6">
            {/* Field Positions with Grouping */}
            {positionGroups.map((group, groupIndex) => (
              <div key={group.name} className={groupIndex > 0 ? "pt-4 border-t border-gray-200" : ""}>
                <h3 className="text-sm font-semibold text-baseball-navy mb-3 flex items-center">
                  <Circle className="mr-2 h-3 w-3 text-baseball-green" />
                  {group.name} Positions
                </h3>
                <div className="space-y-3">
                  {group.positions.map((position) => {
                    let currentValue = "";
                    const positionValue = inningData.positions[position.id as keyof typeof inningData.positions];
                    currentValue = positionValue ? String(positionValue) : "";

                    return (
                      <div
                        key={position.id}
                        className="grid grid-cols-3 items-center rounded-md overflow-hidden bg-baseball-lightgreen"
                      >
                        <div className="font-bold p-3 flex items-center">
                          <Circle className="mr-2 h-4 w-4 text-baseball-green" />
                          <span>{position.label}:</span>
                        </div>
                        <div className="col-span-2 p-2">
                          <Select
                            value={currentValue || "none"}
                            onValueChange={(value) => handlePlayerChange(position.id, value === "none" ? "" : value)}
                          >
                            <SelectTrigger className="h-9 bg-white border-baseball-green/30">
                              <SelectValue placeholder={`Choose player for ${position.label}`} />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="none">-- Select Player --</SelectItem>
                              {availablePlayers
                                .sort((a, b) => {
                                  // Sort players by their current position in this inning, then alphabetically
                                  const currentPositionA = findPlayerCurrentPosition(a.name);
                                  const currentPositionB = findPlayerCurrentPosition(b.name);
                                  
                                  // Define position order (field positions first, then bench)
                                  const positionOrder = [
                                    "pitcher", "catcher", "firstBase", "secondBase", 
                                    "thirdBase", "shortstop", "leftField", "centerField", "rightField"
                                  ];
                                  
                                  // Get position priority (-1 if not found, bench positions get high numbers)
                                  const getPriority = (pos: string | null): number => {
                                    if (!pos) return 1000; // Unassigned players at end
                                    if (pos.startsWith('bench')) return 100 + parseInt(pos.replace('bench', '')); // Bench players after field
                                    const index = positionOrder.indexOf(pos);
                                    return index >= 0 ? index : 999; // Field positions by order
                                  };
                                  
                                  const priorityA = getPriority(currentPositionA);
                                  const priorityB = getPriority(currentPositionB);
                                  
                                  // First sort by position priority
                                  if (priorityA !== priorityB) {
                                    return priorityA - priorityB;
                                  }
                                  
                                  // Then sort alphabetically within same position category
                                  return a.name.localeCompare(b.name);
                                })
                                .map(player => {
                                const currentPosition = findPlayerCurrentPosition(player.name);
                                const isOnField = currentPosition && !currentPosition.startsWith('bench');
                                const isOnBench = currentPosition && currentPosition.startsWith('bench');
                                const restrictionResult = checkPositionRestriction(player, position.id);
                                const hasRestriction = restrictionResult !== null;
                                if (position.id === 'pitcher' || position.id === 'catcher') {
                                  console.log(`Restriction check for ${player.name} at ${position.id}:`, {
                                    result: restrictionResult,
                                    hasRestriction,
                                    playerTeamRoles: player.teamRoles,
                                    specificRole: player.teamRoles?.[position.id]
                                  });
                                }

                                return (
                                  <SelectItem
                                    key={player.id}
                                    value={player.name}
                                    className={`flex items-center ${isOnField ? 'text-baseball-green font-medium' : isOnBench ? 'text-baseball-navy italic' : ''} ${hasRestriction ? 'text-amber-500' : ''}`}
                                  >
                                    <div className="flex items-center gap-2">
                                      {isOnField ? (
                                        <UserCheck className="h-4 w-4 text-baseball-green" />
                                      ) : isOnBench ? (
                                        <UserMinus className="h-4 w-4 text-baseball-navy" />
                                      ) : hasRestriction ? (
                                        <AlertTriangle className="h-4 w-4 text-amber-500" />
                                      ) : (
                                        <User className="h-4 w-4 text-gray-400" />
                                      )}
                                      <span>{player.name}</span>
                                      {currentPosition && (
                                        <span className="ml-1 text-xs opacity-70">
                                          {isOnField ? `(${getPositionLabel(currentPosition)})` : '(Bench)'}
                                        </span>
                                      )}
                                      {hasRestriction && (
                                        <span className="ml-1 text-xs text-amber-500">
                                          (Restricted)
                                        </span>
                                      )}
                                    </div>
                                  </SelectItem>
                                );
                              })}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}

            {/* Bench Positions */}
            {benchPositions.length > 0 && (
              <div className="pt-4 border-t border-gray-200">
                <h3 className="text-sm font-semibold text-baseball-navy mb-3 flex items-center">
                  <Circle className="mr-2 h-3 w-3 text-baseball-green" />
                  Bench Positions
                </h3>
                <div className="space-y-3">
                  {benchPositions.map((position) => {
                    let currentValue = "";
                    const benchIndex = parseInt(position.id.replace("bench", "")) - 1;
                    const benchValue = inningData.positions.bench[benchIndex];
                    currentValue = benchValue ? String(benchValue) : "";

                    return (
                      <div
                        key={position.id}
                        className="grid grid-cols-3 items-center rounded-md overflow-hidden bg-baseball-lightgreen"
                      >
                        <div className="font-bold p-3 flex items-center">
                          <Circle className="mr-2 h-4 w-4 text-baseball-green" />
                          <span>{position.label}:</span>
                        </div>
                        <div className="col-span-2 p-2">
                          <Select
                            value={currentValue || "none"}
                            onValueChange={(value) => handlePlayerChange(position.id, value === "none" ? "" : value)}
                          >
                            <SelectTrigger className="h-9 bg-white border-baseball-green/30">
                              <SelectValue placeholder={`Choose player for ${position.label}`} />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="none">-- Select Player --</SelectItem>
                              {availablePlayers
                                .sort((a, b) => {
                                  // Sort players by their current position in this inning, then alphabetically
                                  const currentPositionA = findPlayerCurrentPosition(a.name);
                                  const currentPositionB = findPlayerCurrentPosition(b.name);
                                  
                                  // Define position order (field positions first, then bench)
                                  const positionOrder = [
                                    "pitcher", "catcher", "firstBase", "secondBase", 
                                    "thirdBase", "shortstop", "leftField", "centerField", "rightField"
                                  ];
                                  
                                  // Get position priority (-1 if not found, bench positions get high numbers)
                                  const getPriority = (pos: string | null): number => {
                                    if (!pos) return 1000; // Unassigned players at end
                                    if (pos.startsWith('bench')) return 100 + parseInt(pos.replace('bench', '')); // Bench players after field
                                    const index = positionOrder.indexOf(pos);
                                    return index >= 0 ? index : 999; // Field positions by order
                                  };
                                  
                                  const priorityA = getPriority(currentPositionA);
                                  const priorityB = getPriority(currentPositionB);
                                  
                                  // First sort by position priority
                                  if (priorityA !== priorityB) {
                                    return priorityA - priorityB;
                                  }
                                  
                                  // Then sort alphabetically within same position category
                                  return a.name.localeCompare(b.name);
                                })
                                .map(player => {
                                const currentPosition = findPlayerCurrentPosition(player.name);
                                const isOnField = currentPosition && !currentPosition.startsWith('bench');
                                const isOnBench = currentPosition && currentPosition.startsWith('bench');
                                const restrictionResult = checkPositionRestriction(player, position.id);
                                const hasRestriction = restrictionResult !== null;
                                if (position.id === 'pitcher' || position.id === 'catcher') {
                                  console.log(`Restriction check for ${player.name} at ${position.id}:`, {
                                    result: restrictionResult,
                                    hasRestriction,
                                    playerTeamRoles: player.teamRoles,
                                    specificRole: player.teamRoles?.[position.id]
                                  });
                                }

                                return (
                                  <SelectItem
                                    key={player.id}
                                    value={player.name}
                                    className={`flex items-center ${isOnField ? 'text-baseball-green font-medium' : isOnBench ? 'text-baseball-navy italic' : ''} ${hasRestriction ? 'text-amber-500' : ''}`}
                                  >
                                    <div className="flex items-center gap-2">
                                      {isOnField ? (
                                        <UserCheck className="h-4 w-4 text-baseball-green" />
                                      ) : isOnBench ? (
                                        <UserMinus className="h-4 w-4 text-baseball-navy" />
                                      ) : hasRestriction ? (
                                        <AlertTriangle className="h-4 w-4 text-amber-500" />
                                      ) : (
                                        <User className="h-4 w-4 text-gray-400" />
                                      )}
                                      <span>{player.name}</span>
                                      {currentPosition && (
                                        <span className="ml-1 text-xs opacity-70">
                                          {isOnField ? `(${getPositionLabel(currentPosition)})` : '(Bench)'}
                                        </span>
                                      )}
                                      {hasRestriction && (
                                        <span className="ml-1 text-xs text-amber-500">
                                          (Restricted)
                                        </span>
                                      )}
                                    </div>
                                  </SelectItem>
                                );
                              })}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>

          <div className="mt-10 flex flex-col items-center gap-4">
            {/* Undo/Redo Buttons */}
            <UndoRedoButtons
              canUndo={getHistoryInfo().canUndo}
              canRedo={getHistoryInfo().canRedo}
              onUndo={() => {
                if (undo()) {
                  toast.success('Undo successful');
                  // Update inning data to reflect the change
                  const historyInning = historyLineup?.innings.find((i: InningLineup) => i.inning === inningNumber);
                  if (historyInning) {
                    setInningData(historyInning);
                  }
                }
              }}
              onRedo={() => {
                if (redo()) {
                  toast.success('Redo successful');
                  // Update inning data to reflect the change
                  const historyInning = historyLineup?.innings.find((i: InningLineup) => i.inning === inningNumber);
                  if (historyInning) {
                    setInningData(historyInning);
                  }
                }
              }}
            />
            
            <Button
              onClick={handleSave}
              disabled={isSaving}
              className="bg-baseball-green hover:bg-baseball-green/90 text-white disabled:opacity-50"
            >
              {isSaving ? "Saving..." : "Save Inning Changes"}
            </Button>
          </div>
        </div>
      </main>

      <Footer />

      {/* Confirmation Dialog for Player Position Change */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              {pendingChange?.isSwap ? "Swap Players?" : "Move Player?"}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {pendingChange && (
                <>
                  {pendingChange.isSwap ? (
                    // Swap dialog
                    <>
                      <p className="mb-2">
                        <strong>{pendingChange.player}</strong> is currently at <strong>{getPositionLabel(pendingChange.currentPosition || '')}</strong> and <strong>{pendingChange.targetPlayer}</strong> is at <strong>{getPositionLabel(pendingChange.position)}</strong>.
                      </p>
                      <p className="mb-2">Do you want to <strong>swap</strong> these players?</p>
                      <p className="text-xs text-gray-500 italic">
                        This will move {pendingChange.player} to {getPositionLabel(pendingChange.position)} and {pendingChange.targetPlayer} to {getPositionLabel(pendingChange.currentPosition || '')}.
                      </p>
                    </>
                  ) : (
                    // Move dialog
                    <>
                      {pendingChange.currentPosition ? (
                        <>
                          <p className="mb-2">
                            <strong>{pendingChange.player}</strong> is currently at <strong>{getPositionLabel(pendingChange.currentPosition)}</strong>.
                          </p>
                          <p className="mb-2">Do you want to move them to <strong>{getPositionLabel(pendingChange.position)}</strong>?</p>
                          {pendingChange.targetPlayer && (
                            <p className="mb-2 text-amber-600">
                              Note: <strong>{pendingChange.targetPlayer}</strong> will be removed from {getPositionLabel(pendingChange.position)}.
                            </p>
                          )}
                        </>
                      ) : (
                        <>
                          <p className="mb-2">
                            Do you want to assign <strong>{pendingChange.player}</strong> to <strong>{getPositionLabel(pendingChange.position)}</strong>?
                          </p>
                          {pendingChange.targetPlayer && (
                            <p className="mb-2 text-amber-600">
                              Note: <strong>{pendingChange.targetPlayer}</strong> will be removed from this position.
                            </p>
                          )}
                        </>
                      )}
                      <p className="text-xs text-gray-500 italic">
                        Players can only be assigned to one position per inning.
                      </p>
                    </>
                  )}
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setPendingChange(null)}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                applyPlayerChange();
                setShowConfirmDialog(false);
              }}
              className="bg-baseball-green hover:bg-baseball-green/90"
            >
              {pendingChange?.isSwap ? "Yes, Swap Players" : "Yes, Move Player"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Position Restriction Warning Dialog */}
      <AlertDialog open={showRestrictionDialog} onOpenChange={setShowRestrictionDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              Position Restriction Warning
            </AlertDialogTitle>
            <AlertDialogDescription>
              {pendingRestriction && (
                <>
                  <p className="mb-2">
                    <strong>{pendingRestriction.player.name}</strong> should not play at <strong>{getPositionLabel(pendingRestriction.position)}</strong>.
                  </p>
                  <p className="mb-2">
                    This player has a position restriction for: <strong>{pendingRestriction.restriction}</strong>
                  </p>
                  <p className="text-xs text-gray-500 italic">
                    Note: Position restrictions are set in the Team Roster page. You can still assign the player to this position, but it's not recommended.
                  </p>
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setPendingRestriction(null)}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                applyPlayerChangeWithRestriction();
                setShowRestrictionDialog(false);
              }}
              className="bg-amber-500 hover:bg-amber-600"
            >
              Assign Anyway
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default EditInning;

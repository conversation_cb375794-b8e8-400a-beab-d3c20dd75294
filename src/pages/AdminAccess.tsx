import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Loader2, <PERSON><PERSON>he<PERSON>, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';

const AdminAccess = () => {
  const navigate = useNavigate();
  const { user, checkPaymentStatus } = useAuth();
  const [status, setStatus] = useState<'loading' | 'checking' | 'error' | 'success'>('loading');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  // Admin account credentials
  const adminEmail = '<EMAIL>';

  useEffect(() => {
    const checkLoginStatus = async () => {
      // Check if already logged in
      if (user) {
        console.log("User already logged in:", user.email);
        setIsLoggedIn(true);

        if (user.email === adminEmail) {
          setStatus('checking');
          await setupAdminAccess();
        } else {
          setStatus('error');
          setErrorMessage(`You are logged in as ${user.email}. Please log out and try again.`);
        }
      } else {
        setStatus('error');
        setErrorMessage(`You are not logged in. Please log in as ${adminEmail} first.`);
      }
    };

    checkLoginStatus();
  }, [user]);

  const setupAdminAccess = async () => {
    try {
      // Clear any demo flags
      localStorage.removeItem('demo_user_is_paid');
      localStorage.removeItem('original_user_email');
      localStorage.removeItem('demo_mode');

      // Set admin flags
      localStorage.setItem('is_noah_real_account', 'true');
      localStorage.setItem('is_admin', 'true');

      console.log("Setting up admin access for:", adminEmail);

      // Check if the user has a profile and set admin flag
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user?.id)
        .single();

      if (profileError && profileError.code !== 'PGRST116') {
        console.error("Error checking profile:", profileError);

        // Try to create the profile
        const { error: createError } = await supabase
          .from('profiles')
          .insert({
            id: user?.id,
            full_name: 'Admin User',
            role: 'Admin',
            is_admin: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (createError) {
          console.error("Error creating profile:", createError);
          throw new Error(`Failed to create profile: ${createError.message}`);
        }
      } else if (profileData) {
        // Update the profile to set is_admin to true
        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            is_admin: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', user?.id);

        if (updateError) {
          console.error("Error updating profile:", updateError);
          throw new Error(`Failed to update profile: ${updateError.message}`);
        }
      } else {
        // No profile found, create one
        const { error: createError } = await supabase
          .from('profiles')
          .insert({
            id: user?.id,
            full_name: 'Admin User',
            role: 'Admin',
            is_admin: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (createError) {
          console.error("Error creating profile:", createError);
          throw new Error(`Failed to create profile: ${createError.message}`);
        }
      }

      // Check if the user has a subscription
      const { data: subscriptionData, error: subscriptionError } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', user?.id)
        .eq('is_paid', true)
        .single();

      if (subscriptionError && subscriptionError.code !== 'PGRST116') {
        console.error("Error checking subscription:", subscriptionError);

        // Try to create the subscription
        const { error: createError } = await supabase
          .from('subscriptions')
          .insert({
            user_id: user?.id,
            is_paid: true,
            amount: 4900,
            currency: 'usd',
            payment_date: new Date().toISOString(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (createError) {
          console.error("Error creating subscription:", createError);
          throw new Error(`Failed to create subscription: ${createError.message}`);
        }
      } else if (subscriptionData) {
        // Make sure it's marked as paid
        if (!subscriptionData.is_paid) {
          const { error: updateError } = await supabase
            .from('subscriptions')
            .update({
              is_paid: true,
              updated_at: new Date().toISOString()
            })
            .eq('id', subscriptionData.id);

          if (updateError) {
            console.error("Error updating subscription:", updateError);
            throw new Error(`Failed to update subscription: ${updateError.message}`);
          }
        }
      } else {
        // No subscription found, create one
        const { error: createError } = await supabase
          .from('subscriptions')
          .insert({
            user_id: user?.id,
            is_paid: true,
            amount: 4900,
            currency: 'usd',
            payment_date: new Date().toISOString(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (createError) {
          console.error("Error creating subscription:", createError);
          throw new Error(`Failed to create subscription: ${createError.message}`);
        }
      }

      // Force refresh the payment status
      if (checkPaymentStatus) {
        const result = await checkPaymentStatus();
        console.log("Payment status check completed with result:", result);
      }

      console.log("Admin access setup completed successfully");
      setStatus('success');

      // Wait a moment for auth context to update
      setTimeout(() => {
        navigate('/admin');
      }, 2000);
    } catch (error) {
      console.error("Error setting up admin access:", error);
      setStatus('error');
      setErrorMessage(`An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  const handleManualSetup = async () => {
    setStatus('checking');
    await setupAdminAccess();
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-baseball-navy">
      <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-lg text-center">
        <ShieldCheck className="mx-auto h-16 w-16 text-purple-600" />

        <h1 className="text-2xl font-bold text-baseball-navy">Admin Access</h1>

        {status === 'loading' && (
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-baseball-green" />
            <p className="text-gray-600">Checking login status...</p>
          </div>
        )}

        {status === 'checking' && (
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-baseball-green" />
            <p className="text-gray-600">Setting up admin access...</p>
          </div>
        )}

        {status === 'success' && (
          <div className="space-y-4">
            <p className="text-green-600 font-medium">Admin access setup successful!</p>
            <p className="text-gray-600">Redirecting to admin dashboard...</p>
          </div>
        )}

        {status === 'error' && (
          <div className="space-y-4">
            <p className="text-red-600 font-medium">Setup failed</p>
            <p className="text-gray-600">{errorMessage}</p>

            {isLoggedIn && user?.email === adminEmail && (
              <Button
                onClick={handleManualSetup}
                className="bg-baseball-green hover:bg-baseball-green/90 mb-4"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Manual Setup
              </Button>
            )}

            <div className="flex gap-2 justify-center">
              <Button
                onClick={() => navigate('/sign-in')}
                className="bg-baseball-navy hover:bg-baseball-navy/90"
              >
                Sign In
              </Button>

              <Button
                onClick={() => navigate('/')}
                variant="outline"
              >
                Return to Home
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminAccess;

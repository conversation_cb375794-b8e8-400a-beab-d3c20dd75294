import React, { useState, useEffect } from "react";
// CACHE BUST: Force reload - v3
import { useLocation, useNavigate } from "react-router-dom";
import { useTeam, Player, InningLineup } from "@/contexts/TeamContext";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Circle, AlertCircle, UserCheck, UserMinus, User, AlertTriangle } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import {
  rotatePlayersForNextInning,
  canPlayPosition,
  checkPositionRestriction,
  getPositionLabel,
  getPositionDisplayName,
  getPlayerPositionHistory,
  generateLineupFromFirstInning
} from "@/lib/utils-enhanced";
import { generateOptimalLineupWrapper } from "@/lib/lineup-generation-wrapper";
import { improvedRotateForNextInning } from "@/lib/improvedRotation";
import { enhancePlayersWithPositionPreferences } from "@/lib/teamRolesToPreferences";

// *** CACHE-BUSTER: Force browser reload - v3.0 - All rotation fixed ***
const SimpleSetFirstInning = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { players, lineups, updateLineup, addLineup, rotationRules, getAvailablePlayers } = useTeam();
  console.log("Players from useTeam:", players);

  // Get lineup data from location state
  const lineupData = location.state?.lineupData || location.state?.lineup;

  // Debug the location state
  console.log("Location state:", location.state);

  // State for the current inning
  const [inningData, setInningData] = useState<InningLineup | null>(null);

  // State for available players (attending players)
  const [availablePlayers, setAvailablePlayers] = useState<Player[]>([]);

  // State to track selected players for each position
  const [selectedPositions, setSelectedPositions] = useState<{[key: string]: string}>({});

  // State for game-specific rotation rules - initialize from team defaults
  // In competitive mode, disable bench time limiting by default to allow strategic control
  const [limitBenchTime, setLimitBenchTime] = useState<boolean>(
    rotationRules.competitiveMode ? false : rotationRules.limitBenchTime
  );
  const [rotateLineupEvery, setRotateLineupEvery] = useState<number>(rotationRules.rotateLineupEvery);
  const [rotatePitcherEvery, setRotatePitcherEvery] = useState<number>(rotationRules.rotatePitcherEvery);

  // Field positions grouped by field areas for consistent UX
  const fieldPositions = [
    // Battery positions
    { id: "pitcher", label: "Pitcher" },
    { id: "catcher", label: "Catcher" },
    // Infield positions
    { id: "firstBase", label: "First Base" },
    { id: "secondBase", label: "Second Base" },
    { id: "shortstop", label: "Shortstop" },
    { id: "thirdBase", label: "Third Base" },
    // Outfield positions
    { id: "leftField", label: "Left Field" },
    { id: "centerField", label: "Center Field" },
    { id: "rightField", label: "Right Field" },
  ];

  // Optional positions for teams that use them
  const optionalPositions = [
    { id: "leftCenter", label: "Left Center (optional)" },
    { id: "rightCenter", label: "Right Center (optional)" },
  ];

  // Initialize inning data
  useEffect(() => {
    const initializeData = async () => {
      if (!lineupData) {
        console.error("No lineup data available");
        navigate("/dashboard");
        return;
      }

      console.log("Lineup data:", lineupData);

      // Get attendance data from either location state or lineup data
      const attendanceData = location.state?.attendance || lineupData.attendance;

      if (!attendanceData) {
        console.error("No attendance data available");
        navigate("/set-lineup-attendance");
        return;
      }

      console.log("Attendance data:", attendanceData);

      // Use the getAvailablePlayers function from TeamContext

      // Get available players based on attendance
      console.log("Calling getAvailablePlayers with attendanceData:", attendanceData);

      // Create a dummy lineup object with the attendance data
      const dummyLineup = {
        id: "temp",
        name: "temp",
        gameDate: new Date().toISOString(),
        createdDate: new Date().toISOString(),
        attendance: attendanceData,
        innings: [],
        battingOrder: []
      };

      // Try to get attending players
      let attendingPlayers = getAvailablePlayers(dummyLineup, attendanceData);

      console.log("Attending players returned:", attendingPlayers);

      // If no players were returned, use all players as a fallback
      if (!attendingPlayers || attendingPlayers.length === 0) {
        console.warn("No attending players found, using all players as fallback");
        attendingPlayers = players;
      }

      // Check if we're in demo mode by checking the user's email domain
      const { data: userData } = await supabase.auth.getUser();
      const userEmail = userData.user?.email || '';
      const isDemoMode = userEmail.includes('demo') || userEmail.includes('example.com');

      if (isDemoMode && attendingPlayers.length < 9) {
        console.warn("In demo mode with fewer than 9 players, using all players");
        attendingPlayers = players;
      }

      console.log("Final attending players:", attendingPlayers);
      setAvailablePlayers(attendingPlayers);

      // Initialize inning data if not already set
      if (!inningData) {
        // Create bench positions based on number of attending players
        const benchCount = Math.max(0, attendingPlayers.length - 9);
        const benchPositions = Array(benchCount).fill('');

        const newInningData = {
          inning: 1,
          positions: {
            pitcher: '',
            catcher: '',
            firstBase: '',
            secondBase: '',
            thirdBase: '',
            shortstop: '',
            leftField: '',
            centerField: '',
            rightField: '',
            bench: benchPositions,
            // Optional positions
            leftCenter: undefined,
            rightCenter: undefined,
          }
        };

        console.log("Initial inning data:", newInningData);

        setInningData(newInningData);

        // No need to load from localStorage here since we're doing it in the useState initialization
      }
    };

    // Execute the async function
    initializeData();
  }, [lineupData, players, navigate, location.state]);

  // No need to save selections to localStorage as we're using the database

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!inningData || !lineupData) {
      toast.error("Missing data");
      return;
    }

    console.log("Submitting form with lineup data:", lineupData);

    try {
      console.log("Validating selections from state:", selectedPositions);

      // Create validation object from our state
      const validationPositions = {
        pitcher: selectedPositions.pitcher || '',
        catcher: selectedPositions.catcher || '',
        firstBase: selectedPositions.firstBase || '',
        secondBase: selectedPositions.secondBase || '',
        thirdBase: selectedPositions.thirdBase || '',
        shortstop: selectedPositions.shortstop || '',
        leftField: selectedPositions.leftField || '',
        centerField: selectedPositions.centerField || '',
        rightField: selectedPositions.rightField || '',
        bench: [] as string[]
      };

      // Get bench positions for validation
      const benchCount = Math.max(0, availablePlayers.length - 9);
      for (let i = 0; i < benchCount; i++) {
        const benchKey = `bench${i+1}`;
        const benchValue = selectedPositions[benchKey] || '';
        validationPositions.bench.push(benchValue);
      }

      console.log("Validation positions:", validationPositions);

      // Check if all positions are filled
      const fieldValues = Object.values(validationPositions).filter(v => typeof v === 'string') as string[];
      const benchValues = validationPositions.bench;

      const emptyFieldPositions = fieldValues.filter(v => !v).length;
      const emptyBenchPositions = benchValues.filter(v => !v).length;

      if (emptyFieldPositions > 0 || emptyBenchPositions > 0) {
        console.error("Empty positions detected:", {
          fieldValues,
          benchValues,
          emptyFieldPositions,
          emptyBenchPositions
        });

        // Identify which positions are empty for a more helpful error message
        const emptyPositions: string[] = [];

        fieldPositions.forEach(pos => {
          if (!selectedPositions[pos.id]) {
            emptyPositions.push(pos.label);
          }
        });

        for (let i = 0; i < benchCount; i++) {
          const benchKey = `bench${i+1}`;
          if (!selectedPositions[benchKey]) {
            emptyPositions.push(`Bench ${i+1}`);
          }
        }

        toast.error(`Please fill all positions. Missing: ${emptyPositions.join(', ')}`);
        return;
      }
    } catch (error) {
      console.error("Error processing form data:", error);
      toast.error("Error processing form data. Please try again.");
      return;
    }

    try {
      // Use our state instead of reading from the form
      console.log("Using selectedPositions state for submission:", selectedPositions);

      // Create new positions object from our state
      const newPositions = {
        pitcher: selectedPositions.pitcher || '',
        catcher: selectedPositions.catcher || '',
        firstBase: selectedPositions.firstBase || '',
        secondBase: selectedPositions.secondBase || '',
        thirdBase: selectedPositions.thirdBase || '',
        shortstop: selectedPositions.shortstop || '',
        leftField: selectedPositions.leftField || '',
        centerField: selectedPositions.centerField || '',
        rightField: selectedPositions.rightField || '',
        bench: [] as string[],
        // Optional positions
        leftCenter: selectedPositions.leftCenter || undefined,
        rightCenter: selectedPositions.rightCenter || undefined,
      };

      // Get bench positions from state
      const benchCount = Math.max(0, availablePlayers.length - 9);
      for (let i = 0; i < benchCount; i++) {
        const benchKey = `bench${i+1}`;
        const benchValue = selectedPositions[benchKey] || '';
        newPositions.bench.push(benchValue);
      }

      // Log the positions we're using
      console.log("Using positions from state:", newPositions);

      // Update inning data with explicit inning number
      const updatedInningData = {
        ...inningData,
        inning: 1, // Explicitly set inning number
        positions: newPositions
      };

      console.log("Updated inning data:", updatedInningData);

      // Get attendance data from either location state or lineup data
      const attendanceData = location.state?.attendance || lineupData.attendance;

      // Generate a new ID if one doesn't exist
      const lineupId = lineupData.id || `lineup-${Date.now()}`;

      // Generate all innings based on the first inning
      const innings: InningLineup[] = [updatedInningData];
      let currentInning = updatedInningData;

      // Get the number of innings from lineup data or default to 7
      const numberOfInnings = lineupData.numberOfInnings || 7;
      console.log(`Generating ${numberOfInnings} innings...`);

      // CRITICAL FIX: Use generateLineupFromFirstInning to respect the manually set first inning
      console.log("🔄 Generating innings from manually set first inning...");
      
      // Ensure players have positionPreferences for the lineup generation
      const playersWithPreferences = enhancePlayersWithPositionPreferences(availablePlayers);
      
      try {
        const lineupWithRotation = await generateLineupFromFirstInning(
          updatedInningData, // The manually set first inning
          playersWithPreferences,
          numberOfInnings,
          {
            respectPositionLockouts: rotationRules.respectPositionLockouts,
            competitiveMode: rotationRules.competitiveMode || false,
            limitBenchTime: limitBenchTime,
            allowPitcherRotation: rotationRules.allowPitcherRotation,
            allowCatcherRotation: rotationRules.allowCatcherRotation,
            equalPlayingTime: rotationRules.equalPlayingTime,
            rotateLineupEvery: rotateLineupEvery,
            rotatePitcherEvery: rotatePitcherEvery,
            maxConsecutiveBenchInnings: 2
          }
        );
        
        console.log("📊 Generated innings with rotation from first inning:", lineupWithRotation);
        
        if (lineupWithRotation && lineupWithRotation.length >= numberOfInnings) {
          // Replace the innings array with the generated ones
          innings.length = 0; // Clear the array
          innings.push(...lineupWithRotation.slice(0, numberOfInnings));
          
          console.log(`✅ Successfully generated ${innings.length} innings with rotation from manual first inning`);
        } else {
          throw new Error("Failed to generate sufficient innings");
        }
      } catch (error) {
        // Fallback: if generation failed, copy first inning
        console.error("❌ Failed to generate innings with rotation, falling back to copying first inning:", error);
        for (let i = 1; i < numberOfInnings; i++) {
          innings.push({
            ...currentInning,
            inning: i + 1
          });
        }
      }
      
      // Skip the old rotation logic completely - the new algorithm handles it
      // All old code has been removed since it was causing build errors and is no longer needed

      // Create a complete lineup object with all required fields
      const updatedLineup = {
        id: lineupId,
        name: lineupData.name || "New Lineup",
        gameDate: lineupData.gameDate || new Date().toISOString().split('T')[0],
        attendance: attendanceData || {},
        innings: innings,
        battingOrder: lineupData.battingOrder || [],
        createdDate: lineupData.createdDate || new Date().toISOString(),
        createdAt: lineupData.createdAt || new Date().toISOString(), // Include both for compatibility
        updatedAt: new Date().toISOString(),
        // Store game-specific rotation settings for regenerate function
        rotationSettings: {
          limitBenchTime: limitBenchTime,
          rotateLineupEvery: rotateLineupEvery,
          rotatePitcherEvery: rotatePitcherEvery
        }
      };

      console.log("Complete updated lineup with all innings:", updatedLineup);

      // Validate lineup before saving
      if (!updatedLineup.name || updatedLineup.name.trim() === "") {
        toast.error("Please provide a lineup name before saving.");
        return;
      }
      
      if (!updatedLineup.innings || updatedLineup.innings.length === 0) {
        toast.error("No innings generated. Please try again.");
        return;
      }
      
      // Check if all field positions are filled in the first inning
      const firstInning = updatedLineup.innings[0];
      const requiredPositionIds = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
      const missingPositions = requiredPositionIds.filter(pos => !firstInning.positions[pos]);
      
      if (missingPositions.length > 0) {
        toast.error(`Missing player assignments for: ${missingPositions.join(', ')}. Please complete all position assignments.`);
        return;
      }

      try {
        // Save lineup to the team context (which saves to Supabase and returns lineup with real ID)
        const savedLineup = await addLineup(updatedLineup);
        console.log("Lineup saved successfully to team context and database with ID:", savedLineup.id);

        toast.success("Lineup with all innings generated successfully!");
        
        // Navigate to view-lineup page with the real database ID
        console.log("Navigating to view-lineup page with real ID:", savedLineup.id);

        // Use a timeout to ensure state updates have completed
        setTimeout(() => {
          navigate(`/view-lineup/${savedLineup.id}`, {
            state: { lineup: savedLineup }
          });
        }, 100);
      } catch (error: any) {
        console.error("Error saving lineup to team context:", error);
        
        // More specific error handling
        let errorMessage = "Failed to save lineup";
        if (error?.message?.includes("Network")) {
          errorMessage = "Network error - please check your connection";
        } else if (error?.message?.includes("permission")) {
          errorMessage = "Permission denied - please check your account";
        } else if (error?.message?.includes("column")) {
          errorMessage = "Database error - please contact support";
        }
        
        toast.error(errorMessage, {
          duration: 5000,
          description: "Please try again or contact support if the problem persists."
        });
        return;
      }
    } catch (error) {
      console.error("Error generating lineup:", error);
      toast.error(`Failed to generate lineup: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  };

  const handleAutoFillBestLineup = async () => {
    if (!availablePlayers || availablePlayers.length === 0) {
      toast.error("No available players to assign");
      return;
    }

    try {
      console.log("🎯 Auto-filling best lineup for first inning (using new algorithm)");
      
      // Ensure all players have teamRoles - create basic roles if missing
      const playersWithRoles = availablePlayers.map(player => {
        if (!player.teamRoles || Object.keys(player.teamRoles).length === 0) {
          console.warn(`⚠️ Player ${player.name} has no teamRoles, creating default roles`);
          
          // Create basic teamRoles - all players can play all positions except restrictions
          const defaultRoles: any = {};
          const positions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'thirdBase', 
                           'shortstop', 'leftField', 'centerField', 'rightField'];
          
          positions.forEach(pos => {
            // Check if player has a restriction for this position
            const hasRestriction = player.positionRestrictions && 
                                 Object.keys(player.positionRestrictions).some(restriction => 
                                   restriction.toLowerCase().includes(pos.toLowerCase()));
            
            if (!hasRestriction) {
              defaultRoles[pos] = 'capable'; // Basic capability
            }
          });
          
          return {
            ...player,
            teamRoles: defaultRoles
          };
        }
        return player;
      });
      
      console.log("Available players with roles:", playersWithRoles.map(p => ({
        name: p.name,
        teamRoles: p.teamRoles,
        restrictions: p.positionRestrictions,
        ratings: p.positionRatings
      })));

      // Import the new algorithm functions
      // Use the already imported generateOptimalLineupWrapper

      // Create rules for the auto-fill (respect current game settings)
      const autoFillRules = {
        respectPositionLockouts: rotationRules.respectPositionLockouts,
        competitiveMode: rotationRules.competitiveMode || false,
        limitBenchTime: limitBenchTime,
        allowPitcherRotation: rotationRules.allowPitcherRotation,
        allowCatcherRotation: rotationRules.allowCatcherRotation,
        equalPlayingTime: rotationRules.equalPlayingTime,
        rotateLineupEvery: rotateLineupEvery,
        rotatePitcherEvery: rotatePitcherEvery,
        maxConsecutiveBenchInnings: 2
      };

      console.log("🔧 Auto-fill rules:", autoFillRules);

      // Generate just 1 inning using the new algorithm
      console.log("🚀 Calling generateOptimalLineupWrapper with:", {
        playerCount: playersWithRoles.length,
        innings: 1,
        rules: autoFillRules
      });
      
      // Convert teamRoles to positionPreferences for the lineup algorithm
      const playersWithPreferences = enhancePlayersWithPositionPreferences(playersWithRoles);
      console.log("🔄 Enhanced players with position preferences:", playersWithPreferences.map(p => ({
        name: p.name,
        hasPreferences: !!p.positionPreferences && Object.keys(p.positionPreferences).length > 0
      })));
      
      const generatedInnings = await generateOptimalLineupWrapper(
        playersWithPreferences,
        1, // Just generate the first inning
        autoFillRules
      );

      console.log("📦 generateOptimalLineupWrapper returned:", generatedInnings);

      if (!generatedInnings || generatedInnings.length === 0) {
        console.error("❌ No innings generated from new algorithm", {
          generatedInnings,
          playerData: availablePlayers.map(p => ({
            name: p.name,
            teamRoles: p.teamRoles,
            isOutfieldSpecialist: p.teamRoles?.isOutfieldSpecialist
          }))
        });
        toast.error("Could not generate lineup. Please check player position assignments.");
        return;
      }

      const firstInning = generatedInnings[0];
      console.log("✅ First inning generated:", {
        inning: firstInning,
        positions: firstInning.positions,
        bench: firstInning.positions.bench
      });

      // Extract positions from the generated inning
      const autoFilledPositions: { [key: string]: string } = {};
      
      // Copy field positions
      Object.entries(firstInning.positions).forEach(([position, playerName]) => {
        if (position !== 'bench' && playerName) {
          autoFilledPositions[position] = playerName as string;
        }
      });

      // Handle bench players
      const benchPlayers = firstInning.positions.bench || [];
      if (Array.isArray(benchPlayers)) {
        benchPlayers.forEach((playerName, index) => {
          autoFilledPositions[`bench${index + 1}`] = playerName;
        });
      }

      // Verify assignments respect restrictions (for logging)
      console.log("🔍 Verifying assignments respect restrictions:");
      for (const [position, playerName] of Object.entries(autoFilledPositions)) {
        if (position.startsWith('bench')) continue;
        
        const player = availablePlayers.find(p => p.name === playerName);
        if (player) {
          const positionDisplay = getPositionDisplayName(position);
          const canPlay = canPlayPosition(player, positionDisplay, rotationRules.respectPositionLockouts, rotationRules.competitiveMode || false);
          console.log(`  ${playerName} → ${position} (${positionDisplay}): canPlay=${canPlay}`);
        }
      }

      // Update the selected positions state
      setSelectedPositions(autoFilledPositions);

      // Show success message with details
      const fieldPositionCount = Object.keys(autoFilledPositions).filter(key => !key.startsWith('bench')).length;
      toast.success(`✅ Auto-filled ${fieldPositionCount} field positions with best available players!`, {
        description: "You can still manually adjust any positions as needed.",
        duration: 4000
      });

      console.log("✅ Auto-fill complete:", autoFilledPositions);

    } catch (error) {
      console.error("Error auto-filling lineup:", error);
      toast.error(error instanceof Error ? error.message : "Failed to auto-fill lineup. Please try manual assignment.");
    }
  };

  if (!inningData || !lineupData) {
    return <div>Loading...</div>;
  }

  // Create bench positions
  const benchCount = Math.max(0, availablePlayers.length - 9);
  const benchPositions = Array.from({ length: benchCount }, (_, i) => ({
    id: `bench${i+1}`,
    label: `Bench ${i+1}`
  }));

  return (
    <div className="min-h-screen flex flex-col bg-baseball-tan/20">
      <Header title="Set First Inning" showBack backLink="/set-lineup-attendance" />

      <main className="flex-1 container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md p-6 max-w-4xl mx-auto">
          <h2 className="text-xl font-bold text-baseball-navy mb-4">
            Set Initial Positions for {lineupData.name}
          </h2>

          <p className="mb-4 text-gray-600">
            Assign players to positions for the first inning. All players must be assigned to a position.
          </p>

          {/* Auto-Fill Option */}
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start gap-3">
              <div className="text-2xl">⭐</div>
              <div className="flex-1">
                <h3 className="font-semibold text-blue-900 mb-1">Quick Start: Auto-Fill Best Lineup</h3>
                <p className="text-sm text-blue-700 mb-3">
                  Automatically assigns the highest-rated players to each position for the first inning only.
                  You can still manually adjust any positions after auto-filling.
                </p>
                <Button
                  type="button"
                  onClick={handleAutoFillBestLineup}
                  variant="outline"
                  className="border-baseball-green text-baseball-green hover:bg-baseball-green/10 font-semibold"
                >
                  ⭐ Auto-Fill Best Lineup
                </Button>
              </div>
            </div>
          </div>

          {/* Game-specific rotation rules - CRITICAL WARNING STYLE */}
          {true && (
            <div className="mb-6 border-l-4 border-red-500 border-2 border-red-300 bg-red-50/30 rounded-lg overflow-hidden shadow-lg">
              <div className="bg-red-50 px-6 py-4">
                <h3 className="text-lg font-bold text-red-900 flex items-center gap-2">
                  <span className="text-red-600">⚙️</span>
                  ⚠️ CRITICAL: Game Rotation Rules
                </h3>
                <p className="text-sm text-red-800 font-medium mt-2">
                  <strong>Missing these settings will mess up your entire rotation!</strong> Override your team's default rotation settings for this specific game.
                </p>
              </div>
              <div className="px-6 py-4 bg-white">

              {/* Bench Time Limit */}
              <div className="flex items-start space-x-3 mb-4">
                <Checkbox
                  id="limitBenchTime"
                  checked={limitBenchTime}
                  onCheckedChange={(checked) => setLimitBenchTime(!!checked)}
                />
                <div>
                  <Label htmlFor="limitBenchTime" className="font-medium text-blue-900">
                    Limit Consecutive Bench Time
                  </Label>
                  <p className="text-sm text-blue-700 mt-1">
                    Prevent players from sitting on the bench for more than one inning in a row.
                    Players who were on the bench in the previous inning will be prioritized for field positions in the next inning.
                  </p>
                  {rotationRules.competitiveMode && (
                    <p className="text-xs text-amber-600 mt-2 italic">
                      💡 Competitive mode: Disabled by default to allow strategic control. You can adjust manually in ViewLineup.
                    </p>
                  )}
                </div>
              </div>

              {/* Rotation Frequency Settings */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4 pt-4 border-t border-blue-200">
                <div>
                  <Label htmlFor="rotateLineupEvery" className="font-medium text-blue-900 block mb-2">
                    Rotate lineup every:
                  </Label>
                  <div className="flex items-center space-x-2">
                    {[1, 2, 3].map((value) => (
                      <label key={value} className="flex items-center space-x-1 cursor-pointer">
                        <input
                          type="radio"
                          name="rotateLineupEvery"
                          value={value}
                          checked={rotateLineupEvery === value}
                          onChange={(e) => setRotateLineupEvery(parseInt(e.target.value))}
                          className="text-blue-600"
                        />
                        <span className="text-sm text-blue-800">{value}</span>
                      </label>
                    ))}
                    <span className="text-sm text-blue-700">innings</span>
                  </div>
                  <p className="text-xs text-blue-600 mt-1">
                    Lower numbers mean more frequent rotations.
                  </p>
                </div>

                <div>
                  <Label htmlFor="rotatePitcherEvery" className="font-medium text-blue-900 block mb-2">
                    Rotate pitcher every:
                  </Label>
                  <div className="flex items-center space-x-2">
                    {[1, 2, 3].map((value) => (
                      <label key={value} className="flex items-center space-x-1 cursor-pointer">
                        <input
                          type="radio"
                          name="rotatePitcherEvery"
                          value={value}
                          checked={rotatePitcherEvery === value}
                          onChange={(e) => setRotatePitcherEvery(parseInt(e.target.value))}
                          className="text-blue-600"
                        />
                        <span className="text-sm text-blue-800">{value}</span>
                      </label>
                    ))}
                    <span className="text-sm text-blue-700">innings</span>
                  </div>
                  <p className="text-xs text-blue-600 mt-1">
                    Controls how frequently the pitcher position rotates.
                  </p>
                </div>
              </div>

                </div>
            </div>
          )}

          {/* Competitive Mode Notice */}
          {rotationRules.competitiveMode && (
            <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start gap-3">
                <div className="text-xl">🏆</div>
                <div>
                  <h3 className="font-semibold text-yellow-900 mb-1">Competitive Mode Active</h3>
                  <p className="text-sm text-yellow-800 mb-2">
                    Your team is using competitive mode settings configured in Fielding Rotation Rules.
                    The rotation will prioritize optimal positioning while respecting minimum playing time requirements.
                  </p>
                  <p className="text-xs text-yellow-700">
                    You can adjust rotation frequency for this specific game using the controls above.
                  </p>
                </div>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="space-y-6">
              {/* Field Positions */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Field Positions</h3>
                <div className="grid gap-3">
                  {fieldPositions.map(position => {
                    // Get current selections from state instead of DOM
                    const currentSelections: string[] = Object.values(selectedPositions).filter(Boolean);

                    return (
                      <div key={position.id} className="p-3 bg-baseball-lightgreen rounded-md">
                        <label className="font-medium block mb-1">{position.label}:</label>
                        <select
                          name={position.id}
                          id={position.id}
                          className="w-full p-2 border rounded"
                          value={selectedPositions[position.id] || ""}
                          onChange={(e) => {
                            // Update our state with the new selection
                            const newValue = e.target.value;
                            console.log(`Selected ${newValue} for ${position.id}`);

                            // Update the selectedPositions state using functional update
                            // to ensure we're working with the latest state
                            setSelectedPositions(prev => {
                              const updated = { ...prev };
                              
                              // If the new player is already assigned to another position, swap them
                              if (newValue && newValue !== "") {
                                const currentPlayerInThisPosition = prev[position.id];
                                const otherPositionWithNewPlayer = Object.entries(prev).find(
                                  ([otherPositionId, playerName]) => 
                                    playerName === newValue && otherPositionId !== position.id
                                );

                                if (otherPositionWithNewPlayer) {
                                  // Swap: move the current player to where the new player was
                                  const [otherPositionId] = otherPositionWithNewPlayer;
                                  updated[otherPositionId] = currentPlayerInThisPosition || "";
                                  console.log(`🔄 Swapping players: ${newValue} moved from ${otherPositionId} to ${position.id}, ${currentPlayerInThisPosition || 'empty'} moved to ${otherPositionId}`);
                                  
                                  toast.info(`Swapped positions: ${newValue} moved to ${position.label}`, {
                                    duration: 3000
                                  });
                                }
                              }

                              // Set the new player for this position
                              updated[position.id] = newValue;

                              return updated;
                            });
                          }}
                        >
                          <option value="">-- Select Player --</option>
                          {availablePlayers
                            .sort((a, b) => a.name.localeCompare(b.name))
                            .map(player => {
                            // Check if player is already selected in another position
                            const isSelected = currentSelections.includes(player.name);
                            const isCurrentPosition = selectedPositions[position.id] === player.name;

                            // Check for position restrictions using the checkPositionRestriction function
                            let hasRestriction = false;
                            let restrictionText = "";

                            const restriction = checkPositionRestriction(player, position.id);
                            if (restriction) {
                              hasRestriction = true;
                              restrictionText = ` (Restricted: ${restriction})`;
                            }

                            // Show all players, but indicate if they're assigned elsewhere
                            let displayText = player.name + restrictionText;
                            let optionStyle: React.CSSProperties = { 
                              color: hasRestriction ? 'orange' : 'inherit' 
                            };

                            if (isSelected && !isCurrentPosition) {
                              // Player is assigned to another position - show where they're assigned
                              const assignedPosition = Object.entries(selectedPositions).find(
                                ([_, assignedPlayer]) => assignedPlayer === player.name
                              );
                              const assignedPositionLabel = assignedPosition 
                                ? fieldPositions.find(fp => fp.id === assignedPosition[0])?.label || 
                                  optionalPositions.find(op => op.id === assignedPosition[0])?.label ||
                                  benchPositions.find(bp => bp.id === assignedPosition[0])?.label ||
                                  assignedPosition[0] // fallback to the ID if no label found
                                : 'Unknown';
                              
                              displayText += ` (Currently: ${assignedPositionLabel})`;
                              optionStyle.color = '#666';
                              optionStyle.fontStyle = 'italic';
                            }

                            return (
                              <option
                                key={player.id}
                                value={player.name}
                                style={optionStyle}
                              >
                                {displayText}
                              </option>
                            );
                          })}
                        </select>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Optional Positions */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Optional Positions</h3>
                <div className="grid gap-3">
                  {optionalPositions.map(position => {
                    // Get current selections from state instead of DOM
                    const currentSelections: string[] = Object.values(selectedPositions).filter(Boolean);

                    return (
                      <div key={position.id} className="p-3 bg-yellow-50 rounded-md border border-yellow-200">
                        <label className="font-medium block mb-1">{position.label}:</label>
                        <select
                          name={position.id}
                          id={position.id}
                          className="w-full p-2 border rounded"
                          value={selectedPositions[position.id] || ""}
                          onChange={(e) => {
                            // Update our state with the new selection
                            const newValue = e.target.value;
                            console.log(`Selected ${newValue} for ${position.id}`);

                            // Update the selectedPositions state using functional update
                            // to ensure we're working with the latest state
                            setSelectedPositions(prev => {
                              const updated = { ...prev };
                              
                              // If the new player is already assigned to another position, swap them
                              if (newValue && newValue !== "") {
                                const currentPlayerInThisPosition = prev[position.id];
                                const otherPositionWithNewPlayer = Object.entries(prev).find(
                                  ([otherPositionId, playerName]) => 
                                    playerName === newValue && otherPositionId !== position.id
                                );

                                if (otherPositionWithNewPlayer) {
                                  // Swap: move the current player to where the new player was
                                  const [otherPositionId] = otherPositionWithNewPlayer;
                                  updated[otherPositionId] = currentPlayerInThisPosition || "";
                                  console.log(`🔄 Swapping players: ${newValue} moved from ${otherPositionId} to ${position.id}, ${currentPlayerInThisPosition || 'empty'} moved to ${otherPositionId}`);
                                  
                                  toast.info(`Swapped positions: ${newValue} moved to ${position.label}`, {
                                    duration: 3000
                                  });
                                }
                              }

                              // Set the new player for this position
                              updated[position.id] = newValue;

                              return updated;
                            });
                          }}
                        >
                          <option value="">-- Select Player (Optional) --</option>
                          {availablePlayers
                            .sort((a, b) => a.name.localeCompare(b.name))
                            .map(player => {
                            // Check if player is already selected in another position
                            const isSelected = currentSelections.includes(player.name);
                            const isCurrentPosition = selectedPositions[position.id] === player.name;

                            // Show all players, but indicate if they're assigned elsewhere
                            let displayText = player.name;
                            let optionStyle: React.CSSProperties = { color: 'inherit' };

                            if (isSelected && !isCurrentPosition) {
                              // Player is assigned to another position - show where they're assigned
                              const assignedPosition = Object.entries(selectedPositions).find(
                                ([_, assignedPlayer]) => assignedPlayer === player.name
                              );
                              const assignedPositionLabel = assignedPosition 
                                ? fieldPositions.find(fp => fp.id === assignedPosition[0])?.label || 
                                  optionalPositions.find(op => op.id === assignedPosition[0])?.label ||
                                  benchPositions.find(bp => bp.id === assignedPosition[0])?.label ||
                                  assignedPosition[0] // fallback to the ID if no label found
                                : 'Unknown';
                              
                              displayText += ` (Currently: ${assignedPositionLabel})`;
                              optionStyle.color = '#666';
                              optionStyle.fontStyle = 'italic';
                            }

                            return (
                              <option 
                                key={player.id} 
                                value={player.name}
                                style={optionStyle}
                              >
                                {displayText}
                              </option>
                            );
                          })}
                        </select>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Bench Positions */}
              {benchPositions.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-3">Bench Positions</h3>
                  <div className="grid gap-3">
                    {benchPositions.map(position => {
                      // Get current selections from state instead of DOM
                      const currentSelections: string[] = Object.values(selectedPositions).filter(Boolean);

                      return (
                        <div key={position.id} className="p-3 bg-baseball-lightblue rounded-md">
                          <label className="font-medium block mb-1">{position.label}:</label>
                          <select
                            name={position.id}
                            id={position.id}
                            className="w-full p-2 border rounded"
                            value={selectedPositions[position.id] || ""}
                            onChange={(e) => {
                              // Update our state with the new selection
                              const newValue = e.target.value;
                              console.log(`Selected ${newValue} for ${position.id}`);

                              // Update the selectedPositions state using functional update
                              // to ensure we're working with the latest state
                              setSelectedPositions(prev => {
                                const updated = { ...prev };
                                
                                // If the new player is already assigned to another position, swap them
                                if (newValue && newValue !== "") {
                                  const currentPlayerInThisPosition = prev[position.id];
                                  const otherPositionWithNewPlayer = Object.entries(prev).find(
                                    ([otherPositionId, playerName]) => 
                                      playerName === newValue && otherPositionId !== position.id
                                  );

                                  if (otherPositionWithNewPlayer) {
                                    // Swap: move the current player to where the new player was
                                    const [otherPositionId] = otherPositionWithNewPlayer;
                                    updated[otherPositionId] = currentPlayerInThisPosition || "";
                                    console.log(`🔄 Swapping players: ${newValue} moved from ${otherPositionId} to ${position.id}, ${currentPlayerInThisPosition || 'empty'} moved to ${otherPositionId}`);
                                    
                                    toast.info(`Swapped positions: ${newValue} moved to ${position.label}`, {
                                      duration: 3000
                                    });
                                  }
                                }

                                // Set the new player for this position
                                updated[position.id] = newValue;

                                return updated;
                              });
                            }}
                          >
                            <option value="">-- Select Player --</option>
                            {availablePlayers
                            .sort((a, b) => a.name.localeCompare(b.name))
                            .map(player => {
                              // Check if player is already selected in another position
                              const isSelected = currentSelections.includes(player.name);
                              const isCurrentPosition = selectedPositions[position.id] === player.name;

                              // Show all players, but indicate if they're assigned elsewhere
                              let displayText = player.name;
                              let optionStyle: React.CSSProperties = { color: 'inherit' };

                              if (isSelected && !isCurrentPosition) {
                                // Player is assigned to another position - show where they're assigned
                                const assignedPosition = Object.entries(selectedPositions).find(
                                  ([_, assignedPlayer]) => assignedPlayer === player.name
                                );
                                const assignedPositionLabel = assignedPosition 
                                  ? fieldPositions.find(fp => fp.id === assignedPosition[0])?.label || 
                                    optionalPositions.find(op => op.id === assignedPosition[0])?.label ||
                                    benchPositions.find(bp => bp.id === assignedPosition[0])?.label ||
                                    assignedPosition[0] // fallback to the ID if no label found
                                  : 'Unknown';
                                
                                displayText += ` (Currently: ${assignedPositionLabel})`;
                                optionStyle.color = '#666';
                                optionStyle.fontStyle = 'italic';
                              }

                              return (
                                <option 
                                  key={player.id} 
                                  value={player.name}
                                  style={optionStyle}
                                >
                                  {displayText}
                                </option>
                              );
                            })}
                          </select>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              <div className="mt-8 flex justify-center">
                <Button type="submit" className="bg-baseball-green text-white">
                  Save and Continue
                </Button>
              </div>
            </div>
          </form>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default SimpleSetFirstInning;

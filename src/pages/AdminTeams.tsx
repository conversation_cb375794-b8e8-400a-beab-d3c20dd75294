import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Search, 
  Users, 
  ClipboardList, 
  Eye,
  Loader2,
  UserCircle,
  Calendar,
  Settings,
  Trash2,
  MoreHorizontal
} from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import AdminLayout from "@/components/AdminLayout";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  DialogFooter,
} from "@/components/ui/dialog";

interface Team {
  id: string;
  name: string;
  ownerEmail: string;
  playerCount: number;
  lineupCount: number;
  created: string;
}

interface TeamDetails {
  id: string;
  name: string;
  owner: {
    id: string;
    email: string;
    name: string;
  };
  players: {
    id: string;
    name: string;
    restrictions: string[];
  }[];
  lineups: {
    id: string;
    name: string;
    date: string;
    inningCount: number;
  }[];
  rotationRules: any;
}

const AdminTeams = () => {
  const [teams, setTeams] = useState<Team[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [showTeamDetailsDialog, setShowTeamDetailsDialog] = useState(false);
  const [showDeleteTeamDialog, setShowDeleteTeamDialog] = useState(false);
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false);
  const [selectedTeam, setSelectedTeam] = useState<TeamDetails | null>(null);
  const [selectedTeamIds, setSelectedTeamIds] = useState<Set<string>>(new Set());
  const [teamToDelete, setTeamToDelete] = useState<{id: string, name: string} | null>(null);
  const [teamDetailsLoading, setTeamDetailsLoading] = useState(false);
  const [processingAction, setProcessingAction] = useState(false);
  const [stats, setStats] = useState({
    totalTeams: 0,
    totalPlayers: 0,
    totalLineups: 0,
    averagePlayersPerTeam: 0
  });

  useEffect(() => {
    fetchTeams();
  }, []);

  const fetchTeams = async () => {
    setLoading(true);
    try {
      // Get all teams
      const { data: teamsData, error: teamsError } = await supabase
        .from('teams')
        .select(`
          id,
          name,
          created_at,
          user_id
        `)
        .order('created_at', { ascending: false });

      if (teamsError) throw teamsError;

      // Get user data for team owners from profiles
      const userIds = [...new Set(teamsData?.map(team => team.user_id) || [])];
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('id, email, full_name')
        .in('id', userIds);

      if (profilesError) throw profilesError;

      // Create a map of user_id to email
      const userEmailMap = {};
      profilesData?.forEach(profile => {
        userEmailMap[profile.id] = profile.email;
      });

      // Get player counts for each team
      const { data: playersData, error: playersError } = await supabase
        .from('players')
        .select('team_id');

      if (playersError) throw playersError;

      // Create a map of team_id to player count
      const playerCountMap = {};
      playersData?.forEach(player => {
        playerCountMap[player.team_id] = (playerCountMap[player.team_id] || 0) + 1;
      });

      // Get lineup counts for each team
      const { data: lineupsData, error: lineupsError } = await supabase
        .from('lineups')
        .select('team_id');

      if (lineupsError) throw lineupsError;

      // Create a map of team_id to lineup count
      const lineupCountMap = {};
      lineupsData?.forEach(lineup => {
        lineupCountMap[lineup.team_id] = (lineupCountMap[lineup.team_id] || 0) + 1;
      });

      // Format the data
      const formattedTeams = teamsData?.map(team => ({
        id: team.id,
        name: team.name,
        ownerEmail: userEmailMap[team.user_id] || 'Unknown',
        playerCount: playerCountMap[team.id] || 0,
        lineupCount: lineupCountMap[team.id] || 0,
        created: new Date(team.created_at).toLocaleDateString()
      })) || [];

      setTeams(formattedTeams);

      // Calculate stats
      const totalPlayers = Object.values(playerCountMap).reduce((sum: number, count: number) => sum + count, 0);
      const totalLineups = Object.values(lineupCountMap).reduce((sum: number, count: number) => sum + count, 0);
      
      setStats({
        totalTeams: formattedTeams.length,
        totalPlayers,
        totalLineups,
        averagePlayersPerTeam: formattedTeams.length ? totalPlayers / formattedTeams.length : 0
      });
    } catch (error) {
      console.error("Error fetching teams:", error);
      toast.error("Failed to load teams");
    } finally {
      setLoading(false);
    }
  };

  const fetchTeamDetails = async (teamId: string) => {
    setTeamDetailsLoading(true);
    try {
      // Get team data
      const { data: teamData, error: teamError } = await supabase
        .from('teams')
        .select(`
          id,
          name,
          user_id,
          created_at
        `)
        .eq('id', teamId)
        .single();

      if (teamError) throw teamError;

      // Get profile data (includes email and name)
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('full_name, email')
        .eq('id', teamData.user_id)
        .single();

      if (profileError) throw profileError;

      // Get players
      const { data: playersData, error: playersError } = await supabase
        .from('players')
        .select(`
          id,
          name,
          position_preferences
        `)
        .eq('team_id', teamId);

      if (playersError) throw playersError;

      // Get lineups
      const { data: lineupsData, error: lineupsError } = await supabase
        .from('lineups')
        .select(`
          id,
          name,
          game_date,
          created_at
        `)
        .eq('team_id', teamId)
        .order('game_date', { ascending: false });

      if (lineupsError) throw lineupsError;

      // Get innings count for each lineup
      const lineupIds = lineupsData?.map(lineup => lineup.id) || [];
      const { data: inningsData, error: inningsError } = await supabase
        .from('lineup_innings')
        .select('lineup_id, inning_number')
        .in('lineup_id', lineupIds);

      if (inningsError) throw inningsError;

      // Create a map of lineup_id to inning count
      const inningCountMap = {};
      inningsData?.forEach(inning => {
        inningCountMap[inning.lineup_id] = Math.max(
          inningCountMap[inning.lineup_id] || 0,
          inning.inning_number
        );
      });

      // Get rotation rules
      const { data: rulesData, error: rulesError } = await supabase
        .from('rotation_rules')
        .select('*')
        .eq('team_id', teamId)
        .single();

      // Format the players data to show position preferences
      const formattedPlayers = playersData?.map(player => {
        const preferences = [];
        
        // Extract position preferences from the JSONB data
        if (player.position_preferences && typeof player.position_preferences === 'object') {
          Object.entries(player.position_preferences).forEach(([position, pref]) => {
            // Handle both string format and object format
            let level = '';
            if (typeof pref === 'string') {
              level = pref;
            } else if (pref && typeof pref === 'object' && 'level' in pref) {
              level = pref.level;
            }
            
            // Only show preferred and secondary positions in admin view
            if (level === 'preferred' || level === 'secondary') {
              // Convert position key to display name
              const displayName = position
                .replace(/([A-Z])/g, ' $1')
                .replace(/^./, str => str.toUpperCase())
                .trim();
              preferences.push(`${displayName} (${level})`);
            }
          });
        }

        return {
          id: player.id,
          name: player.name,
          restrictions: preferences // Keep the same property name for UI compatibility
        };
      }) || [];

      // Format the lineups data
      const formattedLineups = lineupsData?.map(lineup => ({
        id: lineup.id,
        name: lineup.name,
        date: new Date(lineup.game_date).toLocaleDateString(),
        inningCount: inningCountMap[lineup.id] || 0
      })) || [];

      // Set the selected team details
      setSelectedTeam({
        id: teamData.id,
        name: teamData.name,
        owner: {
          id: teamData.user_id,
          email: profileData?.email || 'Unknown',
          name: profileData?.full_name || 'Unknown'
        },
        players: formattedPlayers,
        lineups: formattedLineups,
        rotationRules: rulesData || {}
      });

      setShowTeamDetailsDialog(true);
    } catch (error) {
      console.error("Error fetching team details:", error);
      toast.error("Failed to load team details");
    } finally {
      setTeamDetailsLoading(false);
    }
  };

  const handleDeleteTeam = async () => {
    if (!teamToDelete) return;

    setProcessingAction(true);
    try {
      // Delete the team (will cascade to players, lineups, etc.)
      const { error } = await supabase
        .from('teams')
        .delete()
        .eq('id', teamToDelete.id);

      if (error) throw error;

      // Log admin action
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_id: (await supabase.auth.getUser()).data.user?.id,
          action: 'delete_team',
          entity_type: 'team',
          entity_id: teamToDelete.id,
          details: { 
            teamName: teamToDelete.name
          }
        });

      toast.success(`Team "${teamToDelete.name}" has been deleted`);
      setShowDeleteTeamDialog(false);
      setTeamToDelete(null);
      fetchTeams();
    } catch (error) {
      console.error("Error deleting team:", error);
      toast.error("Failed to delete team: " + error.message);
    } finally {
      setProcessingAction(false);
    }
  };

  const handleBulkDeleteTeams = async () => {
    if (selectedTeamIds.size === 0) return;

    setProcessingAction(true);
    try {
      const teamIdsArray = Array.from(selectedTeamIds);
      
      // Get team names for confirmation
      const teamsToDelete = teams.filter(t => selectedTeamIds.has(t.id));
      const teamNames = teamsToDelete.map(t => t.name).join(', ');
      
      const confirmDelete = window.confirm(
        `Are you sure you want to delete ${selectedTeamIds.size} team(s)?\n\nTeams: ${teamNames}\n\nThis will permanently delete all players, lineups, and data. This action cannot be undone!`
      );
      
      if (!confirmDelete) {
        setProcessingAction(false);
        return;
      }

      // Delete each team
      const errors = [];
      for (const teamId of teamIdsArray) {
        try {
          await supabase.from('teams').delete().eq('id', teamId);
        } catch (error) {
          errors.push({ teamId, error });
        }
      }

      // Log admin action
      await supabase
        .from('admin_audit_logs')
        .insert({
          admin_id: (await supabase.auth.getUser()).data.user?.id,
          action: 'bulk_delete_teams',
          entity_type: 'team',
          entity_id: teamIdsArray.join(','),
          details: { 
            count: selectedTeamIds.size,
            teamIds: teamIdsArray,
            errors: errors.length > 0 ? errors : undefined
          }
        });

      if (errors.length > 0) {
        toast.error(`Deleted ${selectedTeamIds.size - errors.length} teams. ${errors.length} failed.`);
      } else {
        toast.success(`Successfully deleted ${selectedTeamIds.size} team(s)`);
      }
      
      setSelectedTeamIds(new Set());
      setShowBulkDeleteDialog(false);
      fetchTeams();
    } catch (error) {
      console.error("Error in bulk delete:", error);
      toast.error("Failed to delete teams: " + error.message);
    } finally {
      setProcessingAction(false);
    }
  };

  const toggleTeamSelection = (teamId: string) => {
    const newSelection = new Set(selectedTeamIds);
    if (newSelection.has(teamId)) {
      newSelection.delete(teamId);
    } else {
      newSelection.add(teamId);
    }
    setSelectedTeamIds(newSelection);
  };

  const toggleSelectAll = () => {
    if (selectedTeamIds.size === filteredTeams.length) {
      setSelectedTeamIds(new Set());
    } else {
      setSelectedTeamIds(new Set(filteredTeams.map(t => t.id)));
    }
  };

  const filteredTeams = teams.filter(team => 
    team.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    team.ownerEmail.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Team Management</h1>
          <div className="flex gap-2">
            {selectedTeamIds.size > 0 && (
              <Button 
                variant="destructive" 
                onClick={() => setShowBulkDeleteDialog(true)}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Selected ({selectedTeamIds.size})
              </Button>
            )}
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-6 flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Teams</p>
                <p className="text-3xl font-bold">{stats.totalTeams}</p>
              </div>
              <ClipboardList className="h-8 w-8 text-blue-500" />
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Players</p>
                <p className="text-3xl font-bold">{stats.totalPlayers}</p>
              </div>
              <Users className="h-8 w-8 text-green-500" />
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Lineups</p>
                <p className="text-3xl font-bold">{stats.totalLineups}</p>
              </div>
              <Calendar className="h-8 w-8 text-purple-500" />
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6 flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-500">Avg. Players/Team</p>
                <p className="text-3xl font-bold">{stats.averagePlayersPerTeam.toFixed(1)}</p>
              </div>
              <UserCircle className="h-8 w-8 text-amber-500" />
            </CardContent>
          </Card>
        </div>

        {/* Search */}
        <div className="relative mb-6">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            placeholder="Search teams by name or owner..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {/* Teams Table */}
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedTeamIds.size === filteredTeams.length && filteredTeams.length > 0}
                    onCheckedChange={toggleSelectAll}
                  />
                </TableHead>
                <TableHead>Team Name</TableHead>
                <TableHead>Owner</TableHead>
                <TableHead>Players</TableHead>
                <TableHead>Lineups</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-10">
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      Loading teams...
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredTeams.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-10">
                    No teams found
                  </TableCell>
                </TableRow>
              ) : (
                filteredTeams.map((team) => (
                  <TableRow key={team.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedTeamIds.has(team.id)}
                        onCheckedChange={() => toggleTeamSelection(team.id)}
                      />
                    </TableCell>
                    <TableCell className="font-medium">{team.name}</TableCell>
                    <TableCell>{team.ownerEmail}</TableCell>
                    <TableCell>{team.playerCount}</TableCell>
                    <TableCell>{team.lineupCount}</TableCell>
                    <TableCell>{team.created}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => fetchTeamDetails(team.id)}>
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            className="text-red-600"
                            onClick={() => {
                              setTeamToDelete({id: team.id, name: team.name});
                              setShowDeleteTeamDialog(true);
                            }}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete Team
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Team Details Dialog */}
      <Dialog open={showTeamDetailsDialog} onOpenChange={setShowTeamDetailsDialog}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Team Details</DialogTitle>
            <DialogDescription>
              Detailed information about the selected team.
            </DialogDescription>
          </DialogHeader>
          
          {teamDetailsLoading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin mr-2" />
              <span>Loading team details...</span>
            </div>
          ) : selectedTeam ? (
            <div className="space-y-6 py-4">
              {/* Team Info */}
              <div>
                <h3 className="text-lg font-semibold mb-2">Team Information</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">Name</p>
                    <p className="font-medium">{selectedTeam.name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Owner</p>
                    <p className="font-medium">{selectedTeam.owner.name || selectedTeam.owner.email}</p>
                    <p className="text-xs text-gray-400">{selectedTeam.owner.email}</p>
                  </div>
                </div>
              </div>

              {/* Players */}
              <Accordion type="single" collapsible defaultValue="players">
                <AccordionItem value="players">
                  <AccordionTrigger>
                    <div className="flex items-center">
                      <Users className="h-4 w-4 mr-2" />
                      Players ({selectedTeam.players.length})
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    {selectedTeam.players.length === 0 ? (
                      <p className="text-gray-500 text-center py-2">No players found</p>
                    ) : (
                      <div className="space-y-3">
                        {selectedTeam.players.map(player => (
                          <div key={player.id} className="flex items-center justify-between border-b pb-2">
                            <div>
                              <p className="font-medium">{player.name}</p>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {player.restrictions.map((restriction, i) => (
                                  <Badge key={i} variant="outline" className="text-xs">
                                    {restriction}
                                  </Badge>
                                ))}
                                {player.restrictions.length === 0 && (
                                  <span className="text-xs text-gray-400">No position preferences set</span>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </AccordionContent>
                </AccordionItem>
              </Accordion>

              {/* Lineups */}
              <Accordion type="single" collapsible defaultValue="lineups">
                <AccordionItem value="lineups">
                  <AccordionTrigger>
                    <div className="flex items-center">
                      <ClipboardList className="h-4 w-4 mr-2" />
                      Lineups ({selectedTeam.lineups.length})
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    {selectedTeam.lineups.length === 0 ? (
                      <p className="text-gray-500 text-center py-2">No lineups found</p>
                    ) : (
                      <div className="space-y-3">
                        {selectedTeam.lineups.map(lineup => (
                          <div key={lineup.id} className="flex items-center justify-between border-b pb-2">
                            <div>
                              <p className="font-medium">{lineup.name}</p>
                              <p className="text-xs text-gray-500">
                                {lineup.date} • {lineup.inningCount} innings
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </AccordionContent>
                </AccordionItem>
              </Accordion>

              {/* Rotation Rules */}
              <Accordion type="single" collapsible>
                <AccordionItem value="rules">
                  <AccordionTrigger>
                    <div className="flex items-center">
                      <Settings className="h-4 w-4 mr-2" />
                      Rotation Rules
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    {!selectedTeam.rotationRules || Object.keys(selectedTeam.rotationRules).length === 0 ? (
                      <p className="text-gray-500 text-center py-2">No rotation rules found</p>
                    ) : (
                      <div className="grid grid-cols-2 gap-4">
                        {Object.entries(selectedTeam.rotationRules)
                          .filter(([key]) => !['id', 'team_id', 'created_at', 'updated_at', 'user_id'].includes(key))
                          .map(([key, value]) => (
                            <div key={key}>
                              <p className="text-sm text-gray-500">
                                {key.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                              </p>
                              <p className="font-medium">
                                {typeof value === 'boolean' ? (value ? 'Yes' : 'No') : value}
                              </p>
                            </div>
                          ))}
                      </div>
                    )}
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>
          ) : (
            <p className="text-center py-4">No team selected</p>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Team Dialog */}
      <Dialog open={showDeleteTeamDialog} onOpenChange={setShowDeleteTeamDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Team</DialogTitle>
            <DialogDescription>
              This action cannot be undone. This will permanently delete the team and all its data.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p>Are you sure you want to delete team <strong>{teamToDelete?.name}</strong>?</p>
            <p className="text-red-500 mt-2 font-medium">
              This will permanently delete:
            </p>
            <ul className="text-red-500 mt-1 ml-4 list-disc">
              <li>The team and all settings</li>
              <li>All players on this team</li>
              <li>All lineups and innings</li>
              <li>All rotation rules</li>
            </ul>
            <p className="text-red-600 mt-3 font-bold">This action cannot be undone!</p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteTeamDialog(false)}>Cancel</Button>
            <Button variant="destructive" onClick={handleDeleteTeam} disabled={processingAction}>
              {processingAction ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete Team"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk Delete Dialog */}
      <Dialog open={showBulkDeleteDialog} onOpenChange={setShowBulkDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Multiple Teams</DialogTitle>
            <DialogDescription>
              This action cannot be undone. This will permanently delete the selected teams and all their data.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p>You are about to delete <strong>{selectedTeamIds.size} team(s)</strong>.</p>
            <p className="text-red-500 mt-2 font-medium">
              This will permanently delete:
            </p>
            <ul className="text-red-500 mt-1 ml-4 list-disc">
              <li>All selected teams and settings</li>
              <li>All players on these teams</li>
              <li>All lineups and innings</li>
              <li>All rotation rules</li>
            </ul>
            <p className="text-red-600 mt-3 font-bold">This action cannot be undone!</p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowBulkDeleteDialog(false)}>Cancel</Button>
            <Button variant="destructive" onClick={handleBulkDeleteTeams} disabled={processingAction}>
              {processingAction ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting {selectedTeamIds.size} teams...
                </>
              ) : (
                `Delete ${selectedTeamIds.size} Teams`
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default AdminTeams;

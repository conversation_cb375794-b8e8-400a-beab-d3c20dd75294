// This file clears localStorage on startup to fix any corrupted data

// Function to clear localStorage except for preserved keys
export const clearLocalStorage = () => {
  try {
    console.log("Clearing localStorage to fix corrupted data");

    // Clear only the demo mode flag, all other data should be in the database
    localStorage.removeItem('demo_mode');
    localStorage.removeItem('demo_data_initialized');

    console.log("Problematic localStorage keys cleared");
  } catch (error) {
    console.error("Error clearing localStorage:", error);
  }
};

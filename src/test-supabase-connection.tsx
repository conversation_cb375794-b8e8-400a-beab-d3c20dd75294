import { useState, useEffect } from 'react';
import { supabase } from './integrations/supabase/client';

const TestSupabaseConnection = () => {
  const [connectionStatus, setConnectionStatus] = useState<'checking' | 'success' | 'error'>('checking');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [tables, setTables] = useState<string[]>([]);

  useEffect(() => {
    const checkConnection = async () => {
      try {
        // Test the connection with a simple health check
        const { error: healthError } = await supabase.from('teams').select('count').limit(0);

        if (healthError && healthError.code !== 'PGRST116') {
          // PGRST116 means no rows returned, which is fine for a connection test
          console.error('Supabase connection error:', healthError);
          setConnectionStatus('error');
          setErrorMessage(healthError.message);
          return;
        }

        // If we get here, the connection is successful
        setConnectionStatus('success');

        // Now try to get the list of tables
        try {
          // This is a more reliable way to get tables in Supabase
          const { data, error } = await supabase.rpc('get_tables');

          if (error) {
            console.log('Could not get tables list, but connection is working:', error);
            // We'll still consider the connection successful even if we can't get the table list
          } else if (data) {
            setTables(data);
          }
        } catch (tableErr) {
          console.log('Error getting tables, but connection is working:', tableErr);
          // We'll still consider the connection successful
        }
      } catch (err) {
        console.error('Error checking Supabase connection:', err);
        setConnectionStatus('error');
        setErrorMessage(err instanceof Error ? err.message : String(err));
      }
    };

    checkConnection();
  }, []);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Supabase Connection Test</h1>

      <div className="mb-6 p-4 border rounded">
        <h2 className="text-lg font-semibold mb-2">Connection Status:</h2>
        {connectionStatus === 'checking' && (
          <p className="text-yellow-600">Checking connection...</p>
        )}
        {connectionStatus === 'success' && (
          <p className="text-green-600">✅ Successfully connected to Supabase!</p>
        )}
        {connectionStatus === 'error' && (
          <div>
            <p className="text-red-600">❌ Failed to connect to Supabase</p>
            {errorMessage && (
              <p className="mt-2 text-red-500">{errorMessage}</p>
            )}
          </div>
        )}
      </div>

      {connectionStatus === 'success' && (
        <div className="mb-6 p-4 border rounded">
          <h2 className="text-lg font-semibold mb-2">Public Tables:</h2>
          {tables.length === 0 ? (
            <p>No tables found in the public schema.</p>
          ) : (
            <ul className="list-disc pl-5">
              {tables.map(table => (
                <li key={table}>{table}</li>
              ))}
            </ul>
          )}
        </div>
      )}

      <div className="mb-6 p-4 border rounded">
        <h2 className="text-lg font-semibold mb-2">Supabase Configuration:</h2>
        <p><strong>URL:</strong> {import.meta.env.VITE_SUPABASE_URL || 'Not set'}</p>
        <p><strong>Anon Key:</strong> {import.meta.env.VITE_SUPABASE_ANON_KEY ? '***' + import.meta.env.VITE_SUPABASE_ANON_KEY.slice(-6) : 'Not set'}</p>
      </div>

      <div className="mt-8 p-4 bg-gray-100 rounded">
        <h2 className="text-lg font-semibold mb-2">Next Steps:</h2>
        <ul className="list-disc pl-5">
          <li>If connection failed, check your Supabase URL and Anon Key in the .env file</li>
          <li>If no tables are shown, you need to run the migration script to create the tables</li>
          <li>Check the Supabase dashboard to ensure your project is active</li>
        </ul>
      </div>
    </div>
  );
};

export default TestSupabaseConnection;

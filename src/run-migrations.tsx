import { useState } from 'react';
import { supabase } from './supabaseClient';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { CheckCircle, AlertCircle, Loader2 } from 'lucide-react';

// Import the schema SQL
const schemaSQL = `
-- This file contains the SQL schema for your Supabase database
-- After connecting to Supabase, you can run these migrations

-- Enable the UUID extension for generating unique IDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- USERS table is managed by Supabase Auth (auth.users)

-- TEAMS table to replace the team state in context
CREATE TABLE IF NOT EXISTS public.teams (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Add Row Level Security (RLS) to teams table
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;

-- Create policies if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can view their own teams'
    ) THEN
        CREATE POLICY "Users can view their own teams" ON public.teams
        FOR SELECT USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can insert their own teams'
    ) THEN
        CREATE POLICY "Users can insert their own teams" ON public.teams
        FOR INSERT WITH CHECK (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can update their own teams'
    ) THEN
        CREATE POLICY "Users can update their own teams" ON public.teams
        FOR UPDATE USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can delete their own teams'
    ) THEN
        CREATE POLICY "Users can delete their own teams" ON public.teams
        FOR DELETE USING (auth.uid() = user_id);
    END IF;
END
$$;

-- PLAYERS table to replace the players array in context
CREATE TABLE IF NOT EXISTS public.players (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
  pitcher_restriction BOOLEAN DEFAULT false,
  catcher_restriction BOOLEAN DEFAULT false,
  first_base_restriction BOOLEAN DEFAULT false,
  other_restriction TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Add RLS to players table
ALTER TABLE public.players ENABLE ROW LEVEL SECURITY;

-- Create policies if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can view their own players'
    ) THEN
        CREATE POLICY "Users can view their own players" ON public.players
        FOR SELECT USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can insert their own players'
    ) THEN
        CREATE POLICY "Users can insert their own players" ON public.players
        FOR INSERT WITH CHECK (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can update their own players'
    ) THEN
        CREATE POLICY "Users can update their own players" ON public.players
        FOR UPDATE USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can delete their own players'
    ) THEN
        CREATE POLICY "Users can delete their own players" ON public.players
        FOR DELETE USING (auth.uid() = user_id);
    END IF;
END
$$;

-- LINEUPS table to replace the lineups array in context
CREATE TABLE IF NOT EXISTS public.lineups (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  game_date DATE NOT NULL,
  team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Add RLS to lineups table
ALTER TABLE public.lineups ENABLE ROW LEVEL SECURITY;

-- Create policies if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can view their own lineups'
    ) THEN
        CREATE POLICY "Users can view their own lineups" ON public.lineups
        FOR SELECT USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can insert their own lineups'
    ) THEN
        CREATE POLICY "Users can insert their own lineups" ON public.lineups
        FOR INSERT WITH CHECK (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can update their own lineups'
    ) THEN
        CREATE POLICY "Users can update their own lineups" ON public.lineups
        FOR UPDATE USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can delete their own lineups'
    ) THEN
        CREATE POLICY "Users can delete their own lineups" ON public.lineups
        FOR DELETE USING (auth.uid() = user_id);
    END IF;
END
$$;

-- LINEUP_INNINGS table to store inning data for lineups
CREATE TABLE IF NOT EXISTS public.lineup_innings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  lineup_id UUID REFERENCES public.lineups(id) ON DELETE CASCADE,
  inning_number INTEGER NOT NULL,
  positions JSONB NOT NULL, -- Store the positions as JSON data
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  UNIQUE(lineup_id, inning_number)
);

-- Add RLS to lineup_innings table
ALTER TABLE public.lineup_innings ENABLE ROW LEVEL SECURITY;

-- Create policies if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can view their own lineup innings'
    ) THEN
        CREATE POLICY "Users can view their own lineup innings" ON public.lineup_innings
        FOR SELECT USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can insert their own lineup innings'
    ) THEN
        CREATE POLICY "Users can insert their own lineup innings" ON public.lineup_innings
        FOR INSERT WITH CHECK (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can update their own lineup innings'
    ) THEN
        CREATE POLICY "Users can update their own lineup innings" ON public.lineup_innings
        FOR UPDATE USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can delete their own lineup innings'
    ) THEN
        CREATE POLICY "Users can delete their own lineup innings" ON public.lineup_innings
        FOR DELETE USING (auth.uid() = user_id);
    END IF;
END
$$;

-- LINEUP_ATTENDANCE table to track player attendance for lineups
CREATE TABLE IF NOT EXISTS public.lineup_attendance (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  lineup_id UUID REFERENCES public.lineups(id) ON DELETE CASCADE,
  player_id UUID REFERENCES public.players(id) ON DELETE CASCADE,
  is_present BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  UNIQUE(lineup_id, player_id)
);

-- Add RLS to lineup_attendance table
ALTER TABLE public.lineup_attendance ENABLE ROW LEVEL SECURITY;

-- Create policies if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can view their own lineup attendance'
    ) THEN
        CREATE POLICY "Users can view their own lineup attendance" ON public.lineup_attendance
        FOR SELECT USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can insert their own lineup attendance'
    ) THEN
        CREATE POLICY "Users can insert their own lineup attendance" ON public.lineup_attendance
        FOR INSERT WITH CHECK (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can update their own lineup attendance'
    ) THEN
        CREATE POLICY "Users can update their own lineup attendance" ON public.lineup_attendance
        FOR UPDATE USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can delete their own lineup attendance'
    ) THEN
        CREATE POLICY "Users can delete their own lineup attendance" ON public.lineup_attendance
        FOR DELETE USING (auth.uid() = user_id);
    END IF;
END
$$;

-- BATTING_ORDER table to track the batting order for lineups
CREATE TABLE IF NOT EXISTS public.batting_orders (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  lineup_id UUID REFERENCES public.lineups(id) ON DELETE CASCADE,
  player_order JSONB NOT NULL, -- Array of player IDs in batting order
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Add RLS to batting_order table
ALTER TABLE public.batting_orders ENABLE ROW LEVEL SECURITY;

-- Create policies if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can view their own batting orders'
    ) THEN
        CREATE POLICY "Users can view their own batting orders" ON public.batting_orders
        FOR SELECT USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can insert their own batting orders'
    ) THEN
        CREATE POLICY "Users can insert their own batting orders" ON public.batting_orders
        FOR INSERT WITH CHECK (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can update their own batting orders'
    ) THEN
        CREATE POLICY "Users can update their own batting orders" ON public.batting_orders
        FOR UPDATE USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can delete their own batting orders'
    ) THEN
        CREATE POLICY "Users can delete their own batting orders" ON public.batting_orders
        FOR DELETE USING (auth.uid() = user_id);
    END IF;
END
$$;

-- ROTATION_RULES table to store team rotation rules
CREATE TABLE IF NOT EXISTS public.rotation_rules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
  rotation_method TEXT NOT NULL DEFAULT 'standard',
  equal_playing_time BOOLEAN DEFAULT true,
  rotate_players BOOLEAN DEFAULT true,
  respect_position_lockouts BOOLEAN DEFAULT true,
  allow_pitcher_rotation BOOLEAN DEFAULT false,
  allow_catcher_rotation BOOLEAN DEFAULT true,
  prioritize_outfield_rotation BOOLEAN DEFAULT true,
  limit_bench_time BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Add RLS to rotation_rules table
ALTER TABLE public.rotation_rules ENABLE ROW LEVEL SECURITY;

-- Create policies if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can view their own rotation rules'
    ) THEN
        CREATE POLICY "Users can view their own rotation rules" ON public.rotation_rules
        FOR SELECT USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can insert their own rotation rules'
    ) THEN
        CREATE POLICY "Users can insert their own rotation rules" ON public.rotation_rules
        FOR INSERT WITH CHECK (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can update their own rotation rules'
    ) THEN
        CREATE POLICY "Users can update their own rotation rules" ON public.rotation_rules
        FOR UPDATE USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can delete their own rotation rules'
    ) THEN
        CREATE POLICY "Users can delete their own rotation rules" ON public.rotation_rules
        FOR DELETE USING (auth.uid() = user_id);
    END IF;
END
$$;

-- SUBSCRIPTIONS table to track user payments
CREATE TABLE IF NOT EXISTS public.subscriptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  stripe_session_id TEXT,
  is_paid BOOLEAN DEFAULT false,
  amount INTEGER,
  currency TEXT DEFAULT 'usd',
  payment_date TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add RLS to subscriptions table
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;

-- Create policies if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policy WHERE polname = 'Users can view their own subscriptions'
    ) THEN
        CREATE POLICY "Users can view their own subscriptions" ON public.subscriptions
        FOR SELECT USING (auth.uid() = user_id);
    END IF;
END
$$;
`;

const RunMigrations = () => {
  const [status, setStatus] = useState<'idle' | 'running' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState<string>('');
  const [tables, setTables] = useState<string[]>([]);

  const runMigrations = async () => {
    setStatus('running');
    setMessage('Running migrations...');

    try {
      // Split the SQL into individual statements
      const statements = schemaSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0);

      // Execute each statement separately
      for (const statement of statements) {
        try {
          // Try to execute the statement directly
          const { error } = await supabase.rpc('exec_sql', { sql: statement + ';' });

          if (error) {
            console.warn('Error with exec_sql RPC, this is expected if the RPC is not defined:', error);
            // If the RPC fails, we'll continue anyway since we can't execute SQL directly
            // In a real app, you would need to create this RPC function in Supabase
          }
        } catch (err) {
          console.warn('Error executing statement, continuing with next statement:', err);
          // Continue with the next statement
        }
      }

      // Note: In a real application, you would need to create a Supabase Edge Function
      // or use the Supabase CLI to run these migrations properly

      // Check if tables were created by trying to access the teams table
      const { data: teamsData, error: teamsError } = await supabase
        .from('teams')
        .select('count')
        .limit(0);

      // Even if we get an error about no rows, that's fine - it means the table exists
      if (teamsError && teamsError.code !== 'PGRST116') {
        console.error('Error checking teams table:', teamsError);
        setStatus('error');
        setMessage(`Error checking tables: ${teamsError.message}`);
        return;
      }

      // Try to get a list of tables - this is just for display purposes
      try {
        // We'll manually check for each table we expect to exist
        const expectedTables = [
          'teams', 'players', 'lineups', 'lineup_innings',
          'lineup_attendance', 'batting_orders', 'rotation_rules', 'subscriptions'
        ];

        // For each table, try a simple query
        const tableCheckPromises = expectedTables.map(async (tableName) => {
          const { error } = await supabase.from(tableName).select('count').limit(0);
          return { name: tableName, exists: !error || error.code === 'PGRST116' };
        });

        const tableResults = await Promise.all(tableCheckPromises);
        const existingTables = tableResults
          .filter(result => result.exists)
          .map(result => result.name);

        setTables(existingTables);
      } catch (err) {
        console.warn('Error checking tables, but migrations may have succeeded:', err);
        // We'll still consider the migration successful even if we can't list tables
      }

      setStatus('success');
      setMessage('Migrations completed successfully!');
    } catch (err) {
      console.error('Error running migrations:', err);
      setStatus('error');
      setMessage(`Error running migrations: ${err instanceof Error ? err.message : String(err)}`);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Supabase Database Setup</h1>

      <div className="mb-6">
        <p className="mb-4">
          This page provides instructions for setting up the database tables needed for the application.
        </p>

        <div className="mb-6 p-4 border rounded bg-yellow-50">
          <h2 className="text-lg font-semibold mb-2 text-yellow-800">Important Note</h2>
          <p className="text-yellow-700 mb-2">
            The automatic migration button below may not work because it requires special permissions in Supabase.
            We recommend running the migrations manually using the Supabase SQL Editor.
          </p>
          <ol className="list-decimal pl-5 text-yellow-700 space-y-2">
            <li>Go to the <a href="https://app.supabase.com/" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Supabase Dashboard</a></li>
            <li>Select your project</li>
            <li>Go to the SQL Editor</li>
            <li>Copy the SQL schema from the file <code className="bg-gray-100 px-1 py-0.5 rounded">supabase/migrations/schema.sql</code></li>
            <li>Paste it into the SQL Editor and run it</li>
          </ol>
        </div>

        <Button
          onClick={runMigrations}
          disabled={status === 'running'}
          className="mb-4"
        >
          {status === 'running' && (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          )}
          Try Automatic Migration (May Not Work)
        </Button>

        {status === 'success' && (
          <Alert className="mb-4 bg-green-50 border-green-200">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertTitle className="text-green-800">Success!</AlertTitle>
            <AlertDescription className="text-green-700">
              {message}
            </AlertDescription>
          </Alert>
        )}

        {status === 'error' && (
          <Alert className="mb-4 bg-red-50 border-red-200">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertTitle className="text-red-800">Error</AlertTitle>
            <AlertDescription className="text-red-700">
              {message}
            </AlertDescription>
          </Alert>
        )}
      </div>

      {status === 'success' && tables.length > 0 && (
        <div className="mb-6 p-4 border rounded">
          <h2 className="text-lg font-semibold mb-2">Created Tables:</h2>
          <ul className="list-disc pl-5">
            {tables.map(table => (
              <li key={table}>{table}</li>
            ))}
          </ul>
        </div>
      )}

      <div className="mb-6 p-4 border rounded">
        <h2 className="text-lg font-semibold mb-2">Check Connection Status</h2>
        <p className="mb-2">
          After setting up the database, check if the connection is working and tables are created:
        </p>
        <Button asChild>
          <a href="/test-connection">Test Connection</a>
        </Button>
      </div>

      <div className="mt-8 p-4 bg-gray-100 rounded">
        <h2 className="text-lg font-semibold mb-2">Next Steps:</h2>
        <ul className="list-disc pl-5">
          <li>After running migrations, go to the <a href="/test-connection" className="text-blue-600 hover:underline">connection test page</a> to verify the tables were created</li>
          <li>Try signing up and logging in to test the authentication</li>
          <li>Create a team and add players to test the database functionality</li>
        </ul>
      </div>
    </div>
  );
};

export default RunMigrations;

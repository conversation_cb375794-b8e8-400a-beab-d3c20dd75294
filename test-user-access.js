import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase credentials in .env.local');
  process.exit(1);
}

async function testUserAccess(email, password) {
  console.log(`\n🔐 Testing access for: ${email}\n`);

  // Create a fresh client instance
  const supabase = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });

  try {
    // 1. Try to sign in
    console.log('1. Attempting to sign in...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (authError) {
      console.log('❌ Sign in failed:', authError.message);
      return;
    }

    console.log('✅ Sign in successful');
    console.log(`   User ID: ${authData.user.id}`);
    console.log(`   Email: ${authData.user.email}`);

    // 2. Check profile
    console.log('\n2. Checking profile...');
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single();

    if (profileError) {
      console.log('❌ Profile not found:', profileError.message);
    } else {
      console.log('✅ Profile found');
      console.log(`   Is Admin: ${profile.is_admin}`);
    }

    // 3. Check subscription (as the authenticated user would)
    console.log('\n3. Checking subscription as authenticated user...');
    const { data: subscriptions, error: subError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', authData.user.id);

    if (subError) {
      console.log('❌ Subscription query failed:', subError.message);
      console.log('   This might be an RLS policy issue');
    } else if (!subscriptions || subscriptions.length === 0) {
      console.log('❌ No subscriptions found');
    } else {
      console.log(`✅ Found ${subscriptions.length} subscription(s)`);
      subscriptions.forEach((sub, i) => {
        console.log(`\n   Subscription ${i + 1}:`);
        console.log(`   - Is Paid: ${sub.is_paid}`);
        console.log(`   - Tier: ${sub.tier}`);
        console.log(`   - Team Limit: ${sub.team_limit}`);
        console.log(`   - Expires: ${sub.expires_at || 'Never'}`);
      });
    }

    // 4. Test the exact payment check query
    console.log('\n4. Testing payment check query...');
    const { data: paymentCheck, error: paymentError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', authData.user.id)
      .eq('is_paid', true)
      .maybeSingle();

    if (paymentError) {
      console.log('❌ Payment check failed:', paymentError.message);
    } else if (!paymentCheck) {
      console.log('❌ No paid subscription found');
    } else {
      console.log('✅ Payment check passed!');
      console.log('   User should have access to paid features');
    }

    // 5. Try to access teams
    console.log('\n5. Testing team access...');
    const { data: teams, error: teamsError } = await supabase
      .from('teams')
      .select('id, name')
      .eq('user_id', authData.user.id);

    if (teamsError) {
      console.log('❌ Teams query failed:', teamsError.message);
    } else {
      console.log(`✅ Can access teams (found ${teams?.length || 0})`);
    }

    // Sign out
    await supabase.auth.signOut();

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Get credentials from command line
const email = process.argv[2];
const password = process.argv[3];

if (!email || !password) {
  console.log('Usage: node test-user-access.js <email> <password>');
  console.log('Example: node test-user-access.js <EMAIL> password123');
  process.exit(1);
}

testUserAccess(email, password);
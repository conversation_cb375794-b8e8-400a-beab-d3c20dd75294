# Production Stripe Setup Checklist

**Last Updated**: May 30, 2025

This checklist contains all the steps required to switch from Stripe test mode to production mode for Dugout Boss.

## Pre-Launch Payment System Status

✅ **Security Features Implemented**
- Rate limiting on all Edge Functions
- Origin validation for CORS security  
- Webhook signature verification
- Payment amount validation ($49.00)
- No hardcoded secrets in client code

✅ **Payment Features Working**
- Guest checkout support
- Logged-in user payment tracking
- Duplicate payment prevention
- Payment verification with fallbacks

## Production Setup Checklist

### 1. ⬜ Update CORS Configuration (CRITICAL)

Edit `/supabase/functions/_shared/cors.ts`:

```typescript
const ALLOWED_ORIGINS = [
  // Production domains ONLY
  'https://dugoutboss.com',
  'https://www.dugoutboss.com',
  // REMOVE ALL LOCALHOST ENTRIES BEFORE PRODUCTION
];
```

### 2. ⬜ Configure Stripe Production Mode

In Stripe Dashboard:
- [ ] Switch to **Live Mode** (toggle in top-right)
- [ ] Copy your **Live Publishable Key** (starts with `pk_live_`)
- [ ] Copy your **Live Secret Key** (starts with `sk_live_`)
- [ ] Create a new webhook endpoint

### 3. ⬜ Create Production Webhook

In Stripe Dashboard → Developers → Webhooks:
- [ ] Click "Add endpoint"
- [ ] Endpoint URL: `https://[your-project-id].supabase.co/functions/v1/stripe-webhook`
- [ ] Select events:
  - `checkout.session.completed`
  - `payment_intent.succeeded`  
  - `payment_intent.payment_failed`
- [ ] Copy the **Signing Secret** (starts with `whsec_`)

### 4. ⬜ Set Supabase Environment Variables

In Supabase Dashboard → Settings → Edge Functions → Secrets:

| Variable | Value |
|----------|-------|
| `STRIPE_SECRET_KEY` | Your live secret key (`sk_live_...`) |
| `STRIPE_WEBHOOK_SECRET` | Your webhook signing secret (`whsec_...`) |
| `STRIPE_PUBLISHABLE_KEY` | Your live publishable key (`pk_live_...`) |

⚠️ **Note**: Make sure `SUPABASE_SERVICE_ROLE_KEY` is already set

### 5. ⬜ Update Frontend Environment

Update your production environment variables:

```env
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_[your-live-publishable-key]
```

Deployment platforms:
- **Netlify**: Settings → Environment Variables
- **Vercel**: Settings → Environment Variables
- **Other**: Check platform documentation

### 6. ⬜ Deploy Edge Functions

Deploy all updated Edge Functions:

```bash
# Deploy individually
supabase functions deploy create-payment
supabase functions deploy stripe-webhook
supabase functions deploy verify-payment

# Or use the deployment script
./deploy-edge-functions.sh
```

### 7. ⬜ Test Production Setup

**Small Test Payment** ($0.50 or $1.00):
- [ ] Complete a real payment from your production domain
- [ ] Verify webhook processes the payment (check Stripe Dashboard)
- [ ] Confirm user gets access in the app
- [ ] Check payment appears in subscriptions table

**Cross-Origin Test**:
- [ ] Try to access from unauthorized domain (should fail)
- [ ] Verify only dugoutboss.com can make payments

### 8. ⬜ Monitor Initial Transactions

First 24-48 hours:
- [ ] Check Stripe Dashboard for successful payments
- [ ] Monitor Supabase Edge Function logs for errors
- [ ] Watch for any CORS or authentication issues
- [ ] Verify webhook success rate in Stripe

## Rollback Plan

If issues occur, you can quickly rollback:

1. **Switch Stripe to Test Mode**
   - Update frontend to use test publishable key
   - Prevents real charges while debugging

2. **Re-enable Development CORS** (temporary)
   ```typescript
   const ALLOWED_ORIGINS = [
     'https://dugoutboss.com',
     'http://localhost:5173', // Temporarily re-add
   ];
   ```

3. **Check Logs**
   - Stripe Dashboard → Developers → Logs
   - Supabase Dashboard → Edge Functions → Logs

## Common Issues & Solutions

### "Invalid origin" Error
- Verify exact domain in CORS config (including https://)
- Check for www vs non-www
- Redeploy Edge Functions after changes

### Webhook Not Processing
- Verify webhook signing secret is correct
- Check webhook endpoint URL in Stripe
- Look at Stripe webhook attempt logs

### Payment Not Verified
- Check subscriptions table RLS policies
- Verify user email matches Stripe customer email
- Check Edge Function logs for errors

## Support Contacts

- **Stripe Support**: dashboard.stripe.com/support
- **Supabase Support**: supabase.com/dashboard/support
- **Your Email**: <EMAIL>

## Final Verification

Before announcing or promoting:
- [ ] Complete 3-5 test purchases successfully
- [ ] Verify all users get proper access
- [ ] Confirm no test data in production
- [ ] Remove all development/localhost CORS entries
- [ ] Document any custom configuration

---

**Remember**: Once live, any configuration changes should be tested in a staging environment first!
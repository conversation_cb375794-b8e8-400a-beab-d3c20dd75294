import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables:');
  if (!supabaseUrl) console.error('- VITE_SUPABASE_URL');
  if (!supabaseServiceKey) console.error('- SUPABASE_SERVICE_ROLE_KEY');
  console.error('\nGet the service role key from: Supabase Dashboard > Settings > API > service_role key');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createPaidUser(email, password, tier = 'starter') {
  console.log(`\n🚀 Creating paid user: ${email} (${tier} tier)\n`);

  try {
    // 1. Create the user account
    console.log('1. Creating user account...');
    const { data: userData, error: userError } = await supabase.auth.admin.createUser({
      email: email,
      password: password,
      email_confirm: true // Auto-confirm email
    });

    if (userError) {
      console.error('❌ Failed to create user:', userError.message);
      return;
    }

    console.log('✅ User created successfully');
    console.log(`   ID: ${userData.user.id}`);

    // 2. Create profile (the trigger should do this, but let's ensure it)
    console.log('\n2. Creating profile...');
    const { error: profileError } = await supabase
      .from('profiles')
      .upsert({
        id: userData.user.id,
        email: email,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (profileError) {
      console.error('❌ Failed to create profile:', profileError.message);
    } else {
      console.log('✅ Profile created');
    }

    // 3. Create paid subscription
    console.log('\n3. Creating paid subscription...');
    
    let teamLimit = 1;
    let amount = 2000;
    
    switch (tier) {
      case 'starter':
        teamLimit = 1;
        amount = 2000;
        break;
      case 'coach':
        teamLimit = 5;
        amount = 3000;
        break;
      case 'club':
        teamLimit = 999;
        amount = 50000;
        break;
    }

    const { error: subError } = await supabase
      .from('subscriptions')
      .upsert({
        user_id: userData.user.id,
        is_paid: true,
        tier: tier,
        team_limit: teamLimit,
        amount: amount,
        currency: 'usd',
        payment_date: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (subError) {
      console.error('❌ Failed to create subscription:', subError.message);
    } else {
      console.log('✅ Paid subscription created');
      console.log(`   Tier: ${tier}`);
      console.log(`   Team Limit: ${teamLimit}`);
      console.log(`   Amount: $${amount / 100}`);
    }

    // 4. Verify everything worked
    console.log('\n4. Verifying user setup...');
    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userData.user.id)
      .single();

    const { data: subscription } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userData.user.id)
      .single();

    if (profile && subscription && subscription.is_paid) {
      console.log('\n✅ SUCCESS! User created with paid access:');
      console.log(`   Email: ${email}`);
      console.log(`   Password: ${password}`);
      console.log(`   Tier: ${tier}`);
      console.log(`   Status: PAID`);
      console.log('\n   The user can now log in and access all features!');
    } else {
      console.log('\n⚠️  User created but there may be issues:');
      if (!profile) console.log('   - Profile missing');
      if (!subscription) console.log('   - Subscription missing');
      if (subscription && !subscription.is_paid) console.log('   - Subscription not marked as paid');
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
if (args.length < 2) {
  console.log('Usage: node admin-create-paid-user.js <email> <password> [tier]');
  console.log('\nTiers: starter (default), coach, club');
  console.log('\nExample:');
  console.log('  node admin-create-paid-user.js <EMAIL> password123');
  console.log('  node admin-create-paid-user.js <EMAIL> password123 coach');
  process.exit(1);
}

const [email, password, tier = 'starter'] = args;
createPaidUser(email, password, tier);
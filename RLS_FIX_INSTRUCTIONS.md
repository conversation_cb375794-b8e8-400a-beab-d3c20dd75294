# RLS Policy Fix for Subscriptions Table

## Issue
The subscriptions table is missing INSERT, UPDATE, and DELETE RLS policies, which could prevent users from managing their subscriptions properly.

## Solution
Run the SQL script `check-and-apply-rls.sql` in the Supabase SQL editor.

## Steps to Apply:

1. **Open Supabase Dashboard**
   - Go to https://app.supabase.com
   - Select your project
   - Navigate to SQL Editor

2. **Run the Check and Fix Script**
   - Copy the contents of `check-and-apply-rls.sql`
   - Paste into the SQL editor
   - Click "Run" to execute

3. **Verify the Results**
   The script will:
   - Check if RLS is enabled on subscriptions table
   - Show existing policies (likely only SELECT)
   - Fix any subscriptions with tier but is_paid = false
   - Drop and recreate all policies cleanly
   - Add both user and service role policies
   - Verify the policies were created
   - Show current subscription data

## Expected Policies After Fix:

### User Policies (auth.uid() = user_id):
- `Users can view their own subscriptions` (SELECT)
- `Users can insert their own subscriptions` (INSERT)
- `Users can update their own subscriptions` (UPDATE)
- `Users can delete their own subscriptions` (DELETE)

### Service Role Policies:
- `Service role can view all subscriptions` (SELECT)
- `Service role can insert any subscription` (INSERT)
- `Service role can update any subscription` (UPDATE)
- `Service role can delete any subscription` (DELETE)

## Testing After Fix:

1. **Test User Operations**:
   ```javascript
   // This should now work for authenticated users
   const { data, error } = await supabase
     .from('subscriptions')
     .insert({ 
       user_id: user.id,
       tier: 'recreational',
       is_paid: true,
       team_limit: 3
     });
   ```

2. **Test Updates**:
   ```javascript
   // Users should be able to update their own subscriptions
   const { data, error } = await supabase
     .from('subscriptions')
     .update({ tier: 'competitive' })
     .eq('user_id', user.id);
   ```

## Alternative: Migration File
If you prefer to apply this as a migration, use the existing migration file:
`supabase/migrations/fix_subscriptions_rls_policies.sql`

Run: `npx supabase db push` when local database is running.
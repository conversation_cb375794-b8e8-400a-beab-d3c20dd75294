import { generateLineup } from './src/lib/utils-enhanced.ts';

// Test scenario with limited position preferences that might trigger emergency fallback
const players = [
  {
    id: '1',
    name: 'Player 1',
    positionPreferences: {
      pitcher: 'preferred',
      firstBase: 'neutral'
    }
  },
  {
    id: '2', 
    name: 'Player 2',
    positionPreferences: {
      catcher: 'preferred',
      secondBase: 'neutral'
    }
  },
  {
    id: '3',
    name: 'Player 3',
    positionPreferences: {
      thirdBase: 'preferred',
      shortstop: 'neutral'
    }
  },
  {
    id: '4',
    name: 'Player 4',
    positionPreferences: {
      leftField: 'preferred',
      centerField: 'neutral'
    }
  },
  {
    id: '5',
    name: 'Player 5',
    positionPreferences: {
      rightField: 'preferred'
    }
  },
  // Players 6-9 have limited preferences - might need emergency assignments
  {
    id: '6',
    name: 'Player 6',
    positionPreferences: {
      pitcher: 'avoid', // Can't pitch
      catcher: 'avoid'  // Can't catch
    }
  },
  {
    id: '7',
    name: 'Player 7',
    positionPreferences: {
      firstBase: 'avoid'
    }
  },
  {
    id: '8',
    name: 'Player 8',
    positionPreferences: {} // No preferences!
  },
  {
    id: '9',
    name: 'Player 9',
    // No positionPreferences at all
  }
];

const rules = {
  competitiveMode: true,
  respectPositionLockouts: true,
  numInnings: 6,
  rotateLineupEvery: 3,
  rotatePitcherEvery: 2,
  equalPlayingTime: true
};

try {
  console.log('Testing emergency fallback with limited position preferences...\n');
  const lineup = generateLineup(players, rules);
  
  console.log('✅ Lineup generated successfully!');
  console.log('\nFirst inning assignments:');
  const firstInning = lineup[0];
  Object.entries(firstInning.positions).forEach(([pos, player]) => {
    if (pos !== 'bench') {
      console.log(`  ${pos}: ${player}`);
    }
  });
  console.log(`  bench: ${firstInning.positions.bench.join(', ')}`);
  
  // Check for validation issues
  console.log('\nChecking for emergency assignments...');
  lineup.forEach((inning, idx) => {
    Object.entries(inning.positions).forEach(([pos, playerName]) => {
      if (pos !== 'bench') {
        const player = players.find(p => p.name === playerName);
        if (!player.positionPreferences || !player.positionPreferences[pos]) {
          console.log(`⚠️  Inning ${idx + 1}: ${playerName} assigned to ${pos} (emergency assignment)`);
        }
      }
    });
  });
  
} catch (error) {
  console.error('❌ Failed to generate lineup:', error.message);
  console.error('\nFull error:', error);
}
-- Check all users and their admin status
SELECT 
  p.id,
  p.email,
  p.full_name,
  p.is_admin,
  p.created_at
FROM profiles p
ORDER BY p.created_at DESC;

-- Mark specific users as admin (update the email to match your account)
UPDATE profiles 
SET is_admin = TRUE 
WHERE email IN (
  '<EMAIL>',
  '<EMAIL>'
  -- Add your actual email here if different
);

-- Verify admin users
SELECT 
  id,
  email,
  full_name,
  is_admin 
FROM profiles 
WHERE is_admin = TRUE;
# Position Restriction Error Helper

## Understanding the Error
"Cannot assign position catcher in inning 4 without violating restrictions"

This means the algorithm cannot find an eligible catcher for inning 4 because:
- All available catchers are already playing other positions
- OR players who could catch are on the bench due to rotation rules
- OR you don't have enough players marked as able to catch

## How to Fix This

### Option 1: Check Your Catcher Settings
1. Go to Team Roster
2. Look at how many players can catch:
   - "Primary" (Go-to) - Best catchers
   - "In the Mix" (Capable) - Can catch when needed
   - "Emergency" (Fill-in) - Last resort
   - "Never" (Avoid) - Cannot catch

You need AT LEAST 2-3 players who can catch for a multi-game series.

### Option 2: Adjust Rotation Settings
If you have limited catchers, try:
- Increase "Rotate Catcher Every" to 3+ innings
- This keeps the same catcher longer, reducing the need for multiple catchers

### Option 3: Emergency Catcher Designation
Mark 1-2 additional players as "Emergency" catchers who can fill in when needed.

## Common Scenarios

### Double Header (2 games, 7 innings each)
- Total innings: 14
- With "Rotate Catcher Every 2": Need ~7 different catcher assignments
- With "Rotate Catcher Every 3": Need ~5 different catcher assignments

### Recommended Setup
For multi-game series, have:
- 1-2 Primary catchers
- 1-2 In the Mix catchers
- 1-2 Emergency catchers

This ensures you always have someone available to catch, even with aggressive rotations.
# Rotation Validation System

## Overview

The rotation validation system ensures that all lineup rotations follow the configured rules correctly. It provides comprehensive checking and detailed logging to identify any violations and help maintain fair play.

## Core Validation Function

### `validateRotation(previousInnings, currentInning, rules, players)`

**Purpose**: Validates that a rotation follows all configured rules

**Parameters**:
- `previousInnings`: Array of previous InningLineup objects
- `currentInning`: Current InningLineup to validate
- `rules`: LineupRules configuration
- `players`: Array of all available players

**Returns**: `{ valid: boolean, violations: string[] }`

## Validation Checks

### 1. Pitcher Rotation Validation
- **Rule**: `rules.rotatePitcherEvery` and `rules.allowPitcherRotation`
- **Check**: Verifies pitcher rotates at the correct intervals
- **Formula**: `(inningNumber % rotatePitcherEvery === 1 && inningNumber > 1)`
- **Violation**: "Pitcher should have rotated at inning X (every Y innings)"

### 2. Bench Streak Validation
- **Rule**: `rules.maxConsecutiveBenchInnings` (default: 2)
- **Check**: Ensures no player exceeds maximum consecutive bench time
- **Method**: Uses `getBenchStreaksStatic()` to calculate current streaks
- **Violation**: "PlayerX on bench for Y consecutive innings (max allowed: Z)"

### 3. Rotation Frequency Validation
- **Rule**: `rules.rotateLineupEvery`
- **Check**: Verifies rotation occurs at specified intervals
- **Method**: Compares field positions and bench assignments between innings
- **Violation**: "No rotation occurred at inning X (should rotate every Y innings)"

### 4. Fair Playing Time Validation
- **Rule**: `rules.equalPlayingTime`
- **Check**: Monitors playing time distribution across all players
- **Threshold**: Range should not exceed `max(2, totalInnings * 0.4)`
- **Violation**: "Playing time imbalance detected: range X exceeds max allowed Y"

## Integration Points

### 1. Main Lineup Generation (`generateOptimalLineup`)
- **Initial Lineup**: Validates first inning against rules
- **Each Rotation**: Validates every rotation as it's generated
- **Fallback Lineups**: Validates constraint solver fallbacks
- **Final Validation**: Comprehensive check of entire lineup

### 2. Backward Compatibility (`rotatePlayersForNextInning`)
- **Basic Validation**: Limited validation with single previous inning
- **Warning System**: Uses console.warn for non-critical violations
- **Fallback Validation**: Validates fallback lineups when rotation fails

## Logging and Output

### Debug Information
```
🔍 VALIDATING ROTATION for inning X
⚾ Pitcher rotation check: { details }
🪑 Bench streak validation: { streaks }
🔄 Rotation frequency check: { rotation status }
⚖️ Playing time fairness check: { distribution }
🎯 Rotation validation result: { summary }
```

### Violation Reporting
```
❌ ROTATION RULE VIOLATIONS detected for inning X:
  - Specific violation description
  - Another violation if present
```

### Final Summary
```
🔍 FINAL COMPREHENSIVE VALIDATION:
✅ ALL ROTATION RULES VALIDATED SUCCESSFULLY!
🎉 Generated lineup follows all rotation rules perfectly.
```

## Helper Functions

### `getBenchStreaksStatic(allInnings, upToInning)`
- **Purpose**: Calculate consecutive bench innings for validation
- **Method**: Backwards iteration through innings
- **Returns**: Map of player names to current bench streak counts

## Error Handling

### Validation Failures
- **Non-blocking**: Validation failures don't stop lineup generation
- **Logging**: All violations are logged with detailed context
- **Metrics**: Total violation count tracked for quality assessment

### Graceful Degradation
- **Missing Data**: Handles incomplete lineup history gracefully
- **Rule Conflicts**: Prioritizes player safety (bench time limits)
- **Performance**: Efficient validation that doesn't impact generation speed

## Testing

### Test Coverage
- ✅ Bench streak calculation accuracy
- ✅ Pitcher rotation timing validation
- ✅ Rotation frequency compliance
- ✅ Playing time fairness detection
- ✅ Multiple violation scenarios

### Test Results
```
🧪 Testing getBenchStreaks Function
✅ SUCCESS: getBenchStreaks correctly identified 3 players needing forced rotation
🔍 TESTING VALIDATION FUNCTION:
✅ SUCCESS: Validation correctly detected violations:
  - Pitcher should have rotated at inning 3 (every 2 innings)
  - Player10 on bench for 3 consecutive innings (max allowed: 2)
```

## Benefits

### For Coaches
- **Confidence**: Mathematical proof that rotations are fair
- **Transparency**: Clear logging of all rotation decisions
- **Compliance**: Automatic enforcement of league rules
- **Debugging**: Easy identification of rotation issues

### For Players and Parents
- **Fairness**: Guaranteed equal treatment according to rules
- **Visibility**: Clear explanation of rotation logic
- **Consistency**: Reliable application of rotation policies

### For Development
- **Quality Assurance**: Automatic detection of algorithm issues
- **Performance Monitoring**: Validation of rotation effectiveness
- **Rule Compliance**: Ensures new features don't break existing rules

## Configuration

### Validation Rules
```typescript
interface LineupRules {
  limitBenchTime: boolean;
  maxConsecutiveBenchInnings?: number; // Default: 2
  allowPitcherRotation: boolean;
  rotatePitcherEvery?: number;
  rotateLineupEvery?: number;
  equalPlayingTime?: boolean;
}
```

### Customization
- **Flexible Rules**: All validation checks respect rule configuration
- **Disable Options**: Individual checks can be disabled via rules
- **Threshold Tuning**: Playing time thresholds adjust based on game length

## Future Enhancements

### Potential Additions
1. **Position Diversity Validation**: Ensure players experience different positions
2. **Fatigue Monitoring**: Validate rest periods for demanding positions
3. **Historical Validation**: Cross-game fairness tracking
4. **Performance Metrics**: Rotation quality scoring
5. **Custom Rule Engine**: User-defined validation rules

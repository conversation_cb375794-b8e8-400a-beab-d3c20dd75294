import { supabase } from './src/integrations/supabase/client.js';

async function debugPositionAssignments() {
  try {
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.error('No user logged in');
      return;
    }

    // Get user's teams
    const { data: teams, error: teamsError } = await supabase
      .from('teams')
      .select('*')
      .eq('user_id', user.id);

    if (teamsError) {
      console.error('Error fetching teams:', teamsError);
      return;
    }

    console.log(`\n🏆 Found ${teams.length} teams for user`);

    for (const team of teams) {
      console.log(`\n📋 Team: ${team.name} (ID: ${team.id})`);
      console.log(`   Competitive Mode: ${team.rotation_rules?.competitiveMode || false}`);
      
      // Get players for this team
      const { data: players, error: playersError } = await supabase
        .from('players')
        .select('*')
        .eq('team_id', team.id)
        .eq('is_active', true);

      if (playersError) {
        console.error('Error fetching players:', playersError);
        continue;
      }

      console.log(`   Total Players: ${players.length}`);
      
      // Check each player's position assignments
      let playersWithNoPositions = 0;
      let playersWithPositions = 0;
      
      console.log('\n   Player Position Assignments:');
      players.forEach(player => {
        const hasPositionPreferences = !!player.position_preferences && Object.keys(player.position_preferences).length > 0;
        const hasTeamRoles = !!player.team_roles && Object.keys(player.team_roles).length > 0;
        
        if (!hasPositionPreferences && !hasTeamRoles) {
          playersWithNoPositions++;
          console.log(`   ❌ ${player.name}: NO POSITIONS ASSIGNED`);
        } else {
          playersWithPositions++;
          console.log(`   ✅ ${player.name}:`);
          
          if (hasPositionPreferences) {
            console.log(`      position_preferences:`, player.position_preferences);
          }
          
          if (hasTeamRoles) {
            console.log(`      team_roles:`, player.team_roles);
          }
        }
      });
      
      console.log(`\n   Summary:`);
      console.log(`   - Players with positions: ${playersWithPositions}`);
      console.log(`   - Players WITHOUT positions: ${playersWithNoPositions}`);
      
      if (playersWithNoPositions === players.length) {
        console.error(`\n   🚨 CRITICAL: NO PLAYERS HAVE POSITION ASSIGNMENTS!`);
        console.error(`   This will cause lineup generation to fail.`);
        console.error(`   Solution: Go to Team Roster and assign positions to players.`);
      } else if (playersWithNoPositions > 0) {
        console.warn(`\n   ⚠️  WARNING: ${playersWithNoPositions} players have no positions assigned.`);
        console.warn(`   This may cause lineup generation issues.`);
      }
      
      // Check if there are enough players for each position
      const positionCoverage = {
        pitcher: 0,
        catcher: 0,
        firstBase: 0,
        secondBase: 0,
        thirdBase: 0,
        shortstop: 0,
        leftField: 0,
        centerField: 0,
        rightField: 0
      };
      
      players.forEach(player => {
        if (player.position_preferences) {
          Object.keys(player.position_preferences).forEach(pos => {
            if (positionCoverage.hasOwnProperty(pos)) {
              const pref = player.position_preferences[pos];
              const level = typeof pref === 'string' ? pref : pref?.level;
              if (level && level !== 'avoid') {
                positionCoverage[pos]++;
              }
            }
          });
        }
      });
      
      console.log('\n   Position Coverage:');
      Object.entries(positionCoverage).forEach(([pos, count]) => {
        if (count === 0) {
          console.error(`   ❌ ${pos}: NO PLAYERS can play this position!`);
        } else if (count === 1) {
          console.warn(`   ⚠️  ${pos}: Only ${count} player can play this position`);
        } else {
          console.log(`   ✅ ${pos}: ${count} players can play this position`);
        }
      });
    }
  } catch (error) {
    console.error('Error in debug script:', error);
  }
}

// Run the debug
debugPositionAssignments();
#!/usr/bin/env node

/**
 * Test the new fallback logic in the constraint solver
 * to verify it handles overly restrictive scenarios gracefully
 */

// Mock environment variables to avoid Supabase import errors
process.env.VITE_SUPABASE_URL = 'https://mock.supabase.co';
process.env.VITE_SUPABASE_ANON_KEY = 'mock-key';

// <PERSON>'s problematic subset (9 players with restrictive constraints)
const PROBLEMATIC_PLAYERS = [
  { 
    id: '1', 
    name: '<PERSON>', 
    positionRestrictions: { pitcher: true, catcher: false, firstBase: false, other: null }
  },
  { 
    id: '2', 
    name: '<PERSON>', 
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null }
  },
  { 
    id: '3', 
    name: '<PERSON>', 
    positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: 'Middle Infield' }
  },
  { 
    id: '4', 
    name: '<PERSON>', 
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null }
  },
  { 
    id: '5', 
    name: '<PERSON>', 
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null }
  },
  { 
    id: '6', 
    name: '<PERSON><PERSON>', 
    positionRestrictions: { pitcher: true, catcher: false, firstBase: true, other: '3B/MI/SS/2B' }
  },
  { 
    id: '7', 
    name: 'Finn', 
    positionRestrictions: { pitcher: true, catcher: false, firstBase: true, other: 'Shortstop' }
  },
  { 
    id: '8', 
    name: 'Grace', 
    positionRestrictions: { pitcher: false, catcher: true, firstBase: true, other: null }
  },
  { 
    id: '9', 
    name: 'Kaitlyn', 
    positionRestrictions: { pitcher: false, catcher: true, firstBase: true, other: 'Middle Infield' }
  }
];

async function testFallbackLogic() {
  console.log('🧪 TESTING CONSTRAINT SOLVER FALLBACK LOGIC');
  console.log('=' .repeat(60));
  console.log(`📊 Testing with ${PROBLEMATIC_PLAYERS.length} players with restrictive constraints`);
  
  // Analyze the restrictions
  const pitcherRestricted = PROBLEMATIC_PLAYERS.filter(p => p.positionRestrictions.pitcher).length;
  const catcherRestricted = PROBLEMATIC_PLAYERS.filter(p => p.positionRestrictions.catcher).length;
  const firstBaseRestricted = PROBLEMATIC_PLAYERS.filter(p => p.positionRestrictions.firstBase).length;
  const otherRestricted = PROBLEMATIC_PLAYERS.filter(p => p.positionRestrictions.other).length;
  
  console.log(`🚫 Restriction analysis:`);
  console.log(`  Pitcher restricted: ${pitcherRestricted}/${PROBLEMATIC_PLAYERS.length} (${Math.round(pitcherRestricted/PROBLEMATIC_PLAYERS.length*100)}%)`);
  console.log(`  Catcher restricted: ${catcherRestricted}/${PROBLEMATIC_PLAYERS.length} (${Math.round(catcherRestricted/PROBLEMATIC_PLAYERS.length*100)}%)`);
  console.log(`  First base restricted: ${firstBaseRestricted}/${PROBLEMATIC_PLAYERS.length} (${Math.round(firstBaseRestricted/PROBLEMATIC_PLAYERS.length*100)}%)`);
  console.log(`  Other restrictions: ${otherRestricted}/${PROBLEMATIC_PLAYERS.length} (${Math.round(otherRestricted/PROBLEMATIC_PLAYERS.length*100)}%)`);
  
  try {
    // Import the utilities after setting environment variables
    const utils = await import('./src/lib/utils-enhanced.ts');
    
    console.log('\n🚀 TESTING LINEUP GENERATION WITH FALLBACK LOGIC...');
    
    // Test with competitive rules that would normally fail
    const competitiveRules = {
      limitBenchTime: true,
      maxConsecutiveBenchInnings: 2,
      allowPitcherRotation: true,
      allowCatcherRotation: true,
      respectPositionLockouts: true, // This would normally cause failure
      equalPlayingTime: false,
      rotateLineupEvery: 2,
      rotatePitcherEvery: 3,
      competitiveMode: true,
      starPlayerRotationDelay: 2,
      _randomSeed: 12345
    };
    
    console.log('📋 Rules:', {
      respectPositionLockouts: competitiveRules.respectPositionLockouts,
      competitiveMode: competitiveRules.competitiveMode,
      rotateLineupEvery: competitiveRules.rotateLineupEvery
    });
    
    // Attempt to generate lineup - this should now succeed with fallback logic
    const lineup = utils.generateCompleteLineup(PROBLEMATIC_PLAYERS, 4, competitiveRules);
    
    console.log('✅ SUCCESS: Generated lineup with fallback logic!');
    console.log(`📋 Generated ${lineup.length} innings`);
    
    // Validate each inning and check for fallback usage
    for (const inning of lineup) {
      console.log(`\n📍 Inning ${inning.inning}:`);
      console.log(`  Pitcher: ${inning.positions.pitcher}`);
      console.log(`  Catcher: ${inning.positions.catcher}`);
      console.log(`  First Base: ${inning.positions.firstBase}`);
      console.log(`  Bench: ${inning.positions.bench.join(', ')}`);
      
      // Check if any position restrictions were violated (indicating fallback was used)
      const violations = [];
      const positions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
      
      for (const position of positions) {
        const playerName = inning.positions[position];
        const player = PROBLEMATIC_PLAYERS.find(p => p.name === playerName);
        
        if (player && player.positionRestrictions) {
          if (position === 'pitcher' && player.positionRestrictions.pitcher) {
            violations.push(`${playerName} restricted from pitcher`);
          }
          if (position === 'catcher' && player.positionRestrictions.catcher) {
            violations.push(`${playerName} restricted from catcher`);
          }
          if (position === 'firstBase' && player.positionRestrictions.firstBase) {
            violations.push(`${playerName} restricted from first base`);
          }
        }
      }
      
      if (violations.length > 0) {
        console.log(`  ⚠️ Fallback used - restrictions violated: ${violations.join(', ')}`);
      } else {
        console.log(`  ✅ All restrictions respected`);
      }
      
      const validation = utils.validateLineup(inning, PROBLEMATIC_PLAYERS, competitiveRules);
      if (!validation.valid) {
        console.log(`  ❌ Validation errors: ${validation.errors.join(', ')}`);
      }
    }
    
    console.log('\n🎯 FALLBACK LOGIC TEST RESULTS:');
    console.log('✅ Constraint solver successfully handled overly restrictive scenario');
    console.log('✅ Progressive fallbacks prevented "Invalid rotation plan" error');
    console.log('✅ Lineup generation completed successfully');
    
    return true;
    
  } catch (error) {
    console.log('\n💥 FALLBACK LOGIC TEST FAILED:');
    console.log(`❌ Error Type: ${error.constructor.name}`);
    console.log(`❌ Error Code: ${error.code || 'N/A'}`);
    console.log(`❌ Error Message: ${error.message}`);
    
    if (error.constraints) {
      console.log(`❌ Constraints: ${JSON.stringify(error.constraints, null, 2)}`);
    }
    
    console.log(`❌ Stack Trace:`);
    console.log(error.stack);
    
    return false;
  }
}

// Test different scenarios
async function runComprehensiveFallbackTests() {
  console.log('🧪 COMPREHENSIVE FALLBACK LOGIC TESTS');
  console.log('=' .repeat(60));
  
  const scenarios = [
    {
      name: 'Problematic 9-player subset',
      players: PROBLEMATIC_PLAYERS,
      innings: 4,
      expectedFallback: true
    },
    {
      name: 'Extremely restrictive (first 6 players)',
      players: PROBLEMATIC_PLAYERS.slice(0, 6).concat([
        { id: '10', name: 'Player7', positionRestrictions: {} },
        { id: '11', name: 'Player8', positionRestrictions: {} },
        { id: '12', name: 'Player9', positionRestrictions: {} }
      ]),
      innings: 3,
      expectedFallback: false
    }
  ];
  
  let allPassed = true;
  
  for (const scenario of scenarios) {
    console.log(`\n🎯 Testing: ${scenario.name}`);
    console.log(`   Players: ${scenario.players.length}, Innings: ${scenario.innings}`);
    
    try {
      const utils = await import('./src/lib/utils-enhanced.ts');
      
      const competitiveRules = {
        respectPositionLockouts: true,
        limitBenchTime: true,
        maxConsecutiveBenchInnings: 2,
        allowPitcherRotation: true,
        allowCatcherRotation: true,
        equalPlayingTime: false,
        rotateLineupEvery: 2,
        rotatePitcherEvery: 3,
        competitiveMode: true,
        _randomSeed: 12345
      };
      
      const lineup = utils.generateCompleteLineup(scenario.players, scenario.innings, competitiveRules);
      console.log(`   ✅ SUCCESS: Generated ${lineup.length} innings`);
      
    } catch (error) {
      console.log(`   ❌ FAILED: ${error.message}`);
      allPassed = false;
    }
  }
  
  return allPassed;
}

// Run the tests
runComprehensiveFallbackTests().then(success => {
  if (success) {
    console.log('\n🎉 ALL FALLBACK TESTS PASSED!');
    console.log('✅ The "Invalid rotation plan" error has been fixed');
    console.log('✅ Constraint solver now handles restrictive scenarios gracefully');
  } else {
    console.log('\n❌ Some fallback tests failed');
  }
}).catch(error => {
  console.error('Test script failed:', error);
  process.exit(1);
});

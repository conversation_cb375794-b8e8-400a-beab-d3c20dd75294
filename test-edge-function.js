// This is a simple script to test if the verify-payment Edge Function is working
// Run this in the browser console when logged <NAME_EMAIL>

async function testVerifyPayment() {
  try {
    // Get the current access token from localStorage
    const supabaseData = JSON.parse(localStorage.getItem('supabase.auth.token'));
    const accessToken = supabaseData?.currentSession?.access_token;
    
    if (!accessToken) {
      console.error('No access token found. Please make sure you are logged in.');
      return;
    }
    
    // Get the Supabase URL from the environment
    const supabaseUrl = localStorage.getItem('supabase.url') || 
                        document.querySelector('meta[name="supabase-url"]')?.getAttribute('content');
    
    if (!supabaseUrl) {
      console.error('Could not determine Supabase URL.');
      return;
    }
    
    // Call the verify-payment Edge Function
    const response = await fetch(`${supabaseUrl}/functions/v1/verify-payment`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    const data = await response.json();
    
    console.log('Verify Payment Response:', data);
    
    // Check if the user is marked as paid
    if (data.paid) {
      console.log('✅ User is marked as paid in the database.');
      console.log('Subscription details:', data.subscription);
    } else {
      console.error('❌ User is NOT marked as paid in the database.');
      console.error('Error message:', data.message || data.error || 'No specific error message');
    }
    
    return data;
  } catch (error) {
    console.error('Error testing verify-payment function:', error);
  }
}

// Execute the test
testVerifyPayment().then(result => {
  console.log('Test completed. Final result:', result);
});

# Session Persistence Fix

## Issue
Users are being logged out on page refresh despite recent fixes. The refresh detection logic using `performance.navigation` is deprecated and unreliable.

## Root Causes
1. Deprecated API: `performance.navigation?.type === 1` is not reliable
2. Async profile/payment checks blocking auth state initialization
3. Session state not properly restored from Supabase auth

## Solution
Replace the refresh detection with a more reliable approach using sessionStorage flag:

```typescript
// Better refresh detection
const isPageRefresh = sessionStorage.getItem('auth_initialized') === 'true';
sessionStorage.setItem('auth_initialized', 'true');

// Clear on actual logout
const signOut = async () => {
  sessionStorage.removeItem('auth_initialized');
  // ... rest of signOut logic
};
```

## Implementation
The fix should:
1. Use sessionStorage for reliable refresh detection
2. Defer non-critical checks (profile, payment) to avoid blocking
3. Trust Supabase session state on refresh
4. Only run full validation on initial login
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules
dist
dist-ssr
*.local

# Environment files
.env
.env.*
!.env.example

# Sensitive files that should never be committed
*noah*
*personal*
fix-*.js
*-fix-*.md
*LOGIN_INSTRUCTIONS*
*PAID_USER*
test-welcome-email.js

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
src/lib/utils.bak
claude_code-gemini-mcp/

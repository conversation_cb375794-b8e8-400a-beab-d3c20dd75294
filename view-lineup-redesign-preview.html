<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ViewLineup Redesign Preview</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .baseball-stitch {
            background-image: repeating-linear-gradient(
                -45deg,
                transparent,
                transparent 3px,
                rgba(239, 68, 68, 0.1) 3px,
                rgba(239, 68, 68, 0.1) 6px
            );
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="container mx-auto px-4 py-4">
            <button class="text-gray-600 hover:text-gray-900">← Back to Dashboard</button>
        </div>
    </header>

    <main class="container mx-auto px-4 py-6 max-w-7xl">
        <!-- Main Card -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <!-- Card Header with Title and Date -->
            <div class="bg-gradient-to-r from-blue-900 to-blue-800 text-white p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <h1 class="text-3xl font-bold">Team Wildcats vs. Eagles</h1>
                        <p class="text-blue-200 mt-1">June 4, 2025 • Riverside Park Field 3</p>
                    </div>
                    <div class="text-right">
                        <span class="text-sm text-blue-200">Game #</span>
                        <div class="text-2xl font-bold">04</div>
                    </div>
                </div>
            </div>

            <!-- Stats Cards Row -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 p-6 bg-gray-50 border-b">
                <!-- Balance Score Card -->
                <div class="bg-white rounded-lg p-4 text-center shadow-sm">
                    <div class="text-sm text-gray-600 mb-1">Balance Score</div>
                    <div class="text-3xl font-bold text-green-600">73</div>
                    <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div class="bg-green-600 h-2 rounded-full" style="width: 73%"></div>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">Good Balance</div>
                </div>

                <!-- Players Card -->
                <div class="bg-white rounded-lg p-4 text-center shadow-sm">
                    <div class="text-sm text-gray-600 mb-1">Active Players</div>
                    <div class="text-3xl font-bold text-blue-600">12</div>
                    <div class="text-xs text-gray-500 mt-1">9 Field + 3 Bench</div>
                </div>

                <!-- Innings Card -->
                <div class="bg-white rounded-lg p-4 text-center shadow-sm">
                    <div class="text-sm text-gray-600 mb-1">Total Innings</div>
                    <div class="text-3xl font-bold text-purple-600">6</div>
                    <div class="text-xs text-gray-500 mt-1">Standard Game</div>
                </div>

                <!-- Rotation Card -->
                <div class="bg-white rounded-lg p-4 text-center shadow-sm">
                    <div class="text-sm text-gray-600 mb-1">Rotation</div>
                    <div class="text-3xl font-bold text-orange-600">2</div>
                    <div class="text-xs text-gray-500 mt-1">Every 2 Innings</div>
                </div>
            </div>

            <!-- Primary Actions Bar -->
            <div class="p-4 bg-white border-b">
                <div class="flex flex-wrap gap-3 items-center justify-between">
                    <div class="flex flex-wrap gap-2">
                        <!-- Primary Actions -->
                        <button class="px-6 py-2.5 bg-blue-900 text-white rounded-lg hover:bg-blue-800 font-medium shadow-sm">
                            💾 Save Lineup
                        </button>
                        <button class="px-6 py-2.5 bg-white text-green-700 border-2 border-green-700 rounded-lg hover:bg-green-50 font-medium">
                            🔄 Quick Roster Adjust
                        </button>
                        
                        <!-- Secondary Actions -->
                        <div class="flex items-center gap-1 bg-gray-100 rounded-lg p-1">
                            <button class="px-4 py-1.5 text-sm text-gray-700 hover:bg-white rounded-md transition">
                                📄 PDF
                            </button>
                            <button class="px-4 py-1.5 text-sm text-gray-700 hover:bg-white rounded-md transition">
                                📊 CSV
                            </button>
                        </div>
                    </div>

                    <!-- More Options -->
                    <div class="relative">
                        <button class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                            </svg>
                        </button>
                        <!-- Dropdown would go here -->
                    </div>
                </div>

                <!-- Quick Actions Row -->
                <div class="mt-3 flex flex-wrap gap-2 text-sm">
                    <button class="px-3 py-1.5 text-blue-700 hover:bg-blue-50 rounded-md flex items-center gap-1">
                        ⚖️ Fix Playing Time
                        <span class="text-xs text-gray-500">(Balance: 73)</span>
                    </button>
                    <button class="px-3 py-1.5 text-purple-700 hover:bg-purple-50 rounded-md">
                        🔁 Rebuild Entire Lineup
                    </button>
                    <button class="px-3 py-1.5 text-gray-700 hover:bg-gray-100 rounded-md">
                        ⚾ Add Batting Order
                    </button>
                </div>
            </div>

            <!-- View Toggle -->
            <div class="px-6 pt-4">
                <div class="inline-flex bg-gray-100 rounded-lg p-1">
                    <button class="px-6 py-2 bg-white text-gray-900 rounded-md font-medium shadow-sm">
                        View by Position
                    </button>
                    <button class="px-6 py-2 text-gray-600 hover:text-gray-900 rounded-md font-medium">
                        View by Player
                    </button>
                </div>
            </div>

            <!-- Lineup Content (Placeholder) -->
            <div class="p-6">
                <div class="bg-gray-50 rounded-lg p-8 text-center text-gray-500">
                    <p>Lineup content would go here</p>
                    <p class="text-sm mt-2">Innings grid, batting order, etc.</p>
                </div>
            </div>
        </div>

        <!-- Alternative Design Option - Floating Action Button -->
        <div class="fixed bottom-6 right-6">
            <button class="w-14 h-14 bg-green-600 text-white rounded-full shadow-lg hover:bg-green-700 hover:shadow-xl transition-all">
                <svg class="w-6 h-6 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
            </button>
        </div>
    </main>

    <!-- Info Banner (Optional) -->
    <div class="fixed bottom-0 left-0 right-0 bg-blue-50 border-t border-blue-200 p-3">
        <div class="container mx-auto px-4 flex items-center justify-between">
            <div class="flex items-center gap-2 text-sm text-blue-800">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Tip: Use Quick Roster Adjust for last-minute player changes</span>
            </div>
            <button class="text-blue-600 hover:text-blue-800 text-sm">Dismiss</button>
        </div>
    </div>
</body>
</html>
-- First, let's understand the subscription table structure
\d subscriptions

-- Check the valid_tier constraint
SELECT 
    conname,
    pg_get_constraintdef(oid) 
FROM pg_constraint 
WHERE conrelid = 'subscriptions'::regclass 
AND contype = 'c';

-- Fix for the specific user - using NULL for tier column if it has constraints
DO $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Find the user
    SELECT id INTO v_user_id
    FROM auth.users
    WHERE email = '<EMAIL>'
    LIMIT 1;
    
    IF v_user_id IS NOT NULL THEN
        -- Create profile if missing
        INSERT INTO public.profiles (id, email, created_at, updated_at)
        VALUES (v_user_id, '<EMAIL>', NOW(), NOW())
        ON CONFLICT (id) DO NOTHING;
        
        -- Create subscription - use subscription_tier instead of tier
        INSERT INTO public.subscriptions (
            user_id, 
            is_paid, 
            subscription_tier,  -- Use this column instead
            team_limit, 
            created_at, 
            updated_at
        )
        VALUES (
            v_user_id, 
            true, 
            'pro',  -- Set subscription_tier
            10, 
            NOW(), 
            NOW()
        )
        ON CONFLICT (user_id) 
        DO UPDATE SET 
            is_paid = true,
            subscription_tier = 'pro',
            team_limit = 10,
            updated_at = NOW();
            
        RAISE NOTICE '<NAME_EMAIL>';
    ELSE
        RAISE NOTICE 'User <EMAIL> not found in auth.users';
    END IF;
END $$;

-- Verify the fix
SELECT 
    p.email,
    s.is_paid,
    s.tier,
    s.subscription_tier,
    s.team_limit
FROM profiles p
LEFT JOIN subscriptions s ON s.user_id = p.id
WHERE p.email = '<EMAIL>';
// This script uses the service role key to bypass R<PERSON> and fix the user
const { createServiceClient } = require("./src/integrations/supabase/node-client.cjs");

// Create client with service role (bypasses RLS)
const supabase = createServiceClient();

async function forceFixUser(email) {
  console.log(`\n🔧 Force-fixing user: ${email}\n`);

  try {
    // 1. Get user ID from auth
    const { data: { users } } = await supabase.auth.admin.listUsers();
    const user = users.find(u => u.email === email);
    
    if (!user) {
      console.error('❌ User not found in auth.users');
      return;
    }

    console.log('✅ Found user:', user.id);

    // 2. Create/update profile (bypasses RLS)
    const { error: profileError } = await supabase
      .from('profiles')
      .upsert({
        id: user.id,
        email: user.email,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (profileError) {
      console.error('❌ Profile error:', profileError);
    } else {
      console.log('✅ Profile created/updated');
    }

    // 3. Create/update subscription with VALID tier
    const { error: subError } = await supabase
      .from('subscriptions')
      .upsert({
        user_id: user.id,
        is_paid: true,
        tier: 'club',  // Valid: starter, coach, club
        team_limit: 999,
        currency: 'usd',
        amount: 0,
        payment_date: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (subError) {
      console.error('❌ Subscription error:', subError);
    } else {
      console.log('✅ Subscription created/updated');
    }

    // 4. Create a default team
    const { data: team, error: teamError } = await supabase
      .from('teams')
      .insert({
        user_id: user.id,
        name: 'My Team',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (teamError) {
      console.log('⚠️  Team creation failed (may already exist):', teamError.message);
    } else {
      console.log('✅ Team created:', team.name);
    }

    // 5. Verify everything
    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    const { data: subscription } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .single();

    const { data: teams } = await supabase
      .from('teams')
      .select('*')
      .eq('user_id', user.id);

    console.log('\n📊 Final Status:');
    console.log('- Profile:', profile ? '✅' : '❌');
    console.log('- Subscription:', subscription ? '✅' : '❌');
    console.log('  - Is Paid:', subscription?.is_paid);
    console.log('  - Tier:', subscription?.tier);
    console.log('  - Team Limit:', subscription?.team_limit);
    console.log('- Teams:', teams?.length || 0);

    console.log('\n✨ User should now be able to log in!');
    console.log('   1. Have them sign out');
    console.log('   2. Sign back in');
    console.log('   3. They should go directly to the dashboard');

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run it
const email = process.argv[2];
if (!email) {
  console.error('Usage: node force-fix-user.cjs <EMAIL>');
  process.exit(1);
}

forceFixUser(email);
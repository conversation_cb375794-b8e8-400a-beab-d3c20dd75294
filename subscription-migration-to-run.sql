-- Add subscription tiers and team limits
-- This migration adds support for three subscription tiers: starter, coach, and club

-- Add new columns to subscriptions table
ALTER TABLE public.subscriptions 
ADD COLUMN IF NOT EXISTS tier TEXT DEFAULT 'club',
ADD COLUMN IF NOT EXISTS price_id TEXT,
ADD COLUMN IF NOT EXISTS product_id TEXT,
ADD COLUMN IF NOT EXISTS team_limit INTEGER DEFAULT 999,
ADD COLUMN IF NOT EXISTS subscription_period TEXT DEFAULT 'annual',
ADD COLUMN IF NOT EXISTS expires_at TIMESTAMPTZ;

-- Add constraint for tier values
DO $$ 
BEGIN 
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'valid_tier' 
    ) THEN
        ALTER TABLE public.subscriptions 
        ADD CONSTRAINT valid_tier CHECK (tier IN ('starter', 'coach', 'club'));
    END IF;
END $$;

-- Create index for tier lookups
CREATE INDEX IF NOT EXISTS idx_subscriptions_tier ON public.subscriptions(user_id, tier);
CREATE INDEX IF NOT EXISTS idx_subscriptions_expires ON public.subscriptions(expires_at);

-- Add team usage tracking to teams table
ALTER TABLE public.teams
ADD COLUMN IF NOT EXISTS created_by_user_id UUID REFERENCES auth.users(id);

-- Update existing subscriptions to club tier (grandfather existing users)
UPDATE public.subscriptions 
SET 
    tier = 'club',
    team_limit = 999,
    subscription_period = 'lifetime',
    expires_at = NULL
WHERE is_paid = true 
AND tier IS NULL;

-- Create a function to check team limits
CREATE OR REPLACE FUNCTION check_team_limit(p_user_id UUID)
RETURNS TABLE(can_create BOOLEAN, current_count INTEGER, team_limit INTEGER, tier TEXT) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_tier TEXT;
    v_limit INTEGER;
    v_count INTEGER;
    v_is_paid BOOLEAN;
    v_expires_at TIMESTAMPTZ;
BEGIN
    -- Get user's subscription info
    SELECT s.tier, s.team_limit, s.is_paid, s.expires_at
    INTO v_tier, v_limit, v_is_paid, v_expires_at
    FROM subscriptions s
    WHERE s.user_id = p_user_id
    ORDER BY created_at DESC
    LIMIT 1;
    
    -- Default values if no subscription
    IF NOT FOUND OR NOT v_is_paid OR (v_expires_at IS NOT NULL AND v_expires_at < NOW()) THEN
        v_tier := 'free';
        v_limit := 0;
    END IF;
    
    -- Count user's teams
    SELECT COUNT(*)
    INTO v_count
    FROM teams t
    WHERE t.user_id = p_user_id;
    
    RETURN QUERY
    SELECT 
        v_count < v_limit AS can_create,
        v_count AS current_count,
        v_limit AS team_limit,
        v_tier AS tier;
END;
$$;

-- Create RLS policy for the function
GRANT EXECUTE ON FUNCTION check_team_limit(UUID) TO authenticated;

-- Add RLS policy to prevent team creation beyond limit
CREATE OR REPLACE FUNCTION enforce_team_limit()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
    v_can_create BOOLEAN;
BEGIN
    SELECT can_create INTO v_can_create
    FROM check_team_limit(NEW.user_id);
    
    IF NOT v_can_create THEN
        RAISE EXCEPTION 'Team limit reached for your subscription tier. Please upgrade to create more teams.';
    END IF;
    
    -- Set created_by_user_id
    NEW.created_by_user_id = NEW.user_id;
    
    RETURN NEW;
END;
$$;

-- Create trigger for team limit enforcement
DROP TRIGGER IF EXISTS check_team_limit_trigger ON teams;
CREATE TRIGGER check_team_limit_trigger
BEFORE INSERT ON teams
FOR EACH ROW
EXECUTE FUNCTION enforce_team_limit();

-- Create view for subscription status
CREATE OR REPLACE VIEW user_subscription_status AS
SELECT 
    s.user_id,
    s.tier,
    s.team_limit,
    s.expires_at,
    s.is_paid,
    COUNT(t.id) AS teams_created,
    s.team_limit - COUNT(t.id) AS teams_remaining,
    CASE 
        WHEN s.expires_at IS NULL THEN 'lifetime'
        WHEN s.expires_at > NOW() THEN 'active'
        ELSE 'expired'
    END AS status
FROM subscriptions s
LEFT JOIN teams t ON t.user_id = s.user_id
GROUP BY s.user_id, s.tier, s.team_limit, s.expires_at, s.is_paid;

-- Grant access to the view
GRANT SELECT ON user_subscription_status TO authenticated;
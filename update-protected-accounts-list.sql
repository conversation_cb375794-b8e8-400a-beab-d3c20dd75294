-- Option 3: Update the trigger to only protect the demo account
CREATE OR <PERSON><PERSON><PERSON><PERSON> FUNCTION prevent_protected_user_data_deletion()
RETURNS TRIGGER AS $$
DECLARE
  user_email TEXT;
  user_is_protected BOOLEAN;
BEGIN
  -- Get the email of the user whose data is being deleted
  SELECT email, is_protected INTO user_email, user_is_protected
  FROM profiles
  WHERE id = OLD.user_id;
  
  -- Only protect the demo account, not <PERSON>'s account
  IF user_email = '<EMAIL>' THEN
    RAISE EXCEPTION 'Cannot delete data for protected demo account: %', user_email;
  END IF;
  
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;
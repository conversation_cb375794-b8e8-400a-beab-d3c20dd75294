// Client-side fix for data isolation issue
// This clears any cached data that might be causing users to see wrong data

export const clearAllUserData = () => {
  console.log('Clearing all cached user data...');
  
  // Clear localStorage
  const keysToRemove = [
    'current_team_id',
    'email',
    'teamname',
    'demo_mode',
    'demo_user_id',
    'demo_team',
    'demo_lineups',
    'demo_player_ids',
    'demo_data_initialized',
    'is_admin_account',
    'demo_user_is_paid',
    'original_user_email',
    'game_result_migration_applied'
  ];
  
  // Also clear any keys that might contain team/user specific data
  const allKeys = Object.keys(localStorage);
  allKeys.forEach(key => {
    if (key.includes('team') || 
        key.includes('user') || 
        key.includes('lineup') || 
        key.includes('player') ||
        key.includes('updating_') ||
        key.includes('error_shown_')) {
      localStorage.removeItem(key);
    }
  });
  
  // Clear specific keys
  keysToRemove.forEach(key => localStorage.removeItem(key));
  
  // Clear sessionStorage
  sessionStorage.clear();
  
  // Clear any React Query or other caches if present
  if (window.__REACT_QUERY_STATE__) {
    window.__REACT_QUERY_STATE__ = undefined;
  }
  
  console.log('All cached data cleared');
};

// Function to verify user isolation
export const verifyUserIsolation = async (supabase: any) => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      console.error('No authenticated user');
      return false;
    }
    
    console.log('Current user:', user.email);
    console.log('User ID:', user.id);
    
    // Check teams
    const { data: teams, error } = await supabase
      .from('teams')
      .select('id, name, user_id');
    
    if (error) {
      console.error('Error fetching teams:', error);
      return false;
    }
    
    // Verify all teams belong to current user
    const otherUserTeams = teams?.filter(team => team.user_id !== user.id) || [];
    
    if (otherUserTeams.length > 0) {
      console.error('CRITICAL: User can see teams from other users!', otherUserTeams);
      return false;
    }
    
    console.log('✅ User isolation verified - can only see own teams');
    return true;
  } catch (error) {
    console.error('Error verifying user isolation:', error);
    return false;
  }
};

// Function to force refresh all data
export const forceRefreshUserData = async (supabase: any, refreshTeamData: () => Promise<void>) => {
  console.log('Force refreshing all user data...');
  
  // Clear all caches
  clearAllUserData();
  
  // Get fresh auth session
  const { data: { session }, error } = await supabase.auth.refreshSession();
  
  if (error) {
    console.error('Error refreshing session:', error);
    throw error;
  }
  
  if (!session) {
    console.error('No session after refresh');
    throw new Error('Authentication required');
  }
  
  console.log('Session refreshed for user:', session.user.email);
  
  // Refresh team data
  await refreshTeamData();
  
  console.log('All data refreshed');
};

// Add this to the sign-in process
export const handleSignIn = async (supabase: any, email: string, password: string) => {
  // Clear any existing data first
  clearAllUserData();
  
  // Sign in
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  });
  
  if (error) throw error;
  
  // Verify isolation immediately after sign in
  const isolated = await verifyUserIsolation(supabase);
  
  if (!isolated) {
    console.error('User isolation check failed - data may be leaked');
    // Could throw error or show warning to user
  }
  
  return data;
};
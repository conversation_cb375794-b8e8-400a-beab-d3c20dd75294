#!/usr/bin/env node

import { execSync } from 'child_process';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

console.log(`${colors.bright}${colors.cyan}
╔═══════════════════════════════════════════════════════════╗
║           Edge Functions Deployment Helper                ║
╚═══════════════════════════════════════════════════════════╝
${colors.reset}`);

// Check if Supabase CLI is installed
try {
  execSync('supabase --version', { stdio: 'pipe' });
  console.log(`${colors.green}✓${colors.reset} Supabase CLI is installed`);
} catch (error) {
  console.error(`${colors.red}✗${colors.reset} Supabase CLI is not installed`);
  console.log('\nPlease install it first:');
  console.log('brew install supabase/tap/supabase');
  console.log('OR');
  console.log('npm install -g supabase');
  process.exit(1);
}

// Get project ref from .env
let projectRef = 'mhuuptkgohuztjrovpxz'; // Your project ref

console.log(`${colors.blue}Project Ref:${colors.reset} ${projectRef}`);

// Since Docker isn't available, provide manual deployment instructions
console.log(`\n${colors.yellow}Docker Desktop is not running.${colors.reset}`);
console.log('Edge functions require Docker for local bundling.\n');

console.log(`${colors.bright}Alternative: Deploy through Supabase Dashboard${colors.reset}`);
console.log('\n1. Go to your Supabase Dashboard:');
console.log(`   ${colors.cyan}https://supabase.com/dashboard/project/${projectRef}/functions${colors.reset}`);

console.log('\n2. For each function, click "Deploy function" and upload:');

const functions = [
  { name: 'create-payment', description: 'Handles Stripe checkout sessions' },
  { name: 'stripe-webhook', description: 'Processes Stripe webhook events' },
  { name: 'send-contact-email', description: 'Sends contact form emails' },
  { name: 'verify-payment', description: 'Verifies payment status' },
  { name: 'admin-create-user', description: 'Admin user creation' }
];

functions.forEach(func => {
  console.log(`\n   ${colors.bright}${func.name}${colors.reset}`);
  console.log(`   Location: ./supabase/functions/${func.name}/index.ts`);
  console.log(`   Purpose: ${func.description}`);
});

console.log(`\n${colors.bright}Quick Deployment via CLI (if Docker starts):${colors.reset}`);
console.log('1. Start Docker Desktop');
console.log('2. Run these commands:\n');

functions.forEach(func => {
  console.log(`   supabase functions deploy ${func.name} --project-ref ${projectRef}`);
});

console.log(`\n${colors.bright}Environment Secrets to Set:${colors.reset}`);
console.log('Make sure these are set in your Edge Functions secrets:');
console.log('- STRIPE_SECRET_KEY (your live secret key)');
console.log('- STRIPE_WEBHOOK_SECRET (from your webhook endpoint)');

// Create a deployment script for when Docker is available
const deployScript = `#!/bin/bash
# Edge Functions Deployment Script
# Run this after Docker Desktop is started

PROJECT_REF="${projectRef}"

echo "Deploying Edge Functions to Supabase..."

functions=("create-payment" "stripe-webhook" "send-contact-email" "verify-payment" "admin-create-user")

for func in "\${functions[@]}"
do
    echo "Deploying $func..."
    supabase functions deploy "$func" --project-ref "$PROJECT_REF"
    if [ $? -eq 0 ]; then
        echo "✓ $func deployed successfully"
    else
        echo "✗ Failed to deploy $func"
    fi
done

echo "All functions deployed!"
`;

try {
  const fs = await import('fs');
  fs.writeFileSync('deploy-when-docker-ready.sh', deployScript);
  execSync('chmod +x deploy-when-docker-ready.sh');
  console.log(`\n${colors.green}✓${colors.reset} Created ${colors.bright}deploy-when-docker-ready.sh${colors.reset}`);
  console.log('  Run this script after starting Docker Desktop');
} catch (err) {
  console.error('Could not create deployment script:', err.message);
}
-- Check for duplicate policies
SELECT 
  policyname,
  cmd,
  COUNT(*) as count
FROM pg_policies
WHERE tablename = 'subscriptions'
GROUP BY policyname, cmd
HAVING COUNT(*) > 1;

-- Drop duplicate SELECT policies
DROP POLICY IF EXISTS "Users can view their own subscriptions" ON public.subscriptions;
DROP POLICY IF EXISTS "Admin users can view all subscriptions" ON public.subscriptions;

-- Recreate clean policies if needed
-- The remaining policies should be:
-- 1. "Users can view their own subscription" - for regular users
-- 2. "Allow admins to read all subscriptions" - for admins
-- 3. INSERT/UPDATE/DELETE policies as they are

-- Verify final state
SELECT 
  tablename,
  policyname,
  cmd,
  qual
FROM pg_policies
WHERE tablename = 'subscriptions'
ORDER BY cmd, policyname;
-- Fix automatic profile creation for ALL users
-- This ensures every new user gets a profile automatically

-- Create a function to automatically create profiles
CREATE OR REPLACE FUNCTION public.handle_new_user()
RET<PERSON><PERSON> trigger AS $$
BEGIN
  -- Insert a new profile for the user
  INSERT INTO public.profiles (id, email, created_at, updated_at)
  VALUES (
    new.id,
    new.email,
    new.created_at,
    new.created_at
  )
  ON CONFLICT (id) DO NOTHING;
  
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger to run the function on new user signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Fix any existing users who don't have profiles
INSERT INTO profiles (id, email, created_at, updated_at)
SELECT 
  au.id,
  au.email,
  au.created_at,
  NOW()
FROM auth.users au
LEFT JOIN profiles p ON au.id = p.id
WHERE p.id IS NULL;

-- Create default free subscription for new users
CREATE OR REPLACE FUNCTION public.handle_new_profile()
<PERSON>ET<PERSON>NS trigger AS $$
BEGIN
  -- Insert a default free subscription
  INSERT INTO public.subscriptions (
    user_id,
    is_paid,
    tier,
    team_limit,
    created_at,
    updated_at
  )
  VALUES (
    new.id,
    false,
    'starter',
    0,
    NOW(),
    NOW()
  )
  ON CONFLICT (user_id) DO NOTHING;
  
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new profiles
DROP TRIGGER IF EXISTS on_profile_created ON public.profiles;
CREATE TRIGGER on_profile_created
  AFTER INSERT ON public.profiles
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_profile();

-- Fix existing profiles without subscriptions
INSERT INTO subscriptions (user_id, is_paid, tier, team_limit, created_at, updated_at)
SELECT 
  p.id,
  false,
  'starter',
  0,
  NOW(),
  NOW()
FROM profiles p
LEFT JOIN subscriptions s ON p.id = s.user_id
WHERE s.id IS NULL;

-- Verify the fixes
SELECT 
  'Total auth users' as metric,
  COUNT(*) as count
FROM auth.users
UNION ALL
SELECT 
  'Total profiles' as metric,
  COUNT(*) as count
FROM profiles
UNION ALL
SELECT 
  'Total subscriptions' as metric,
  COUNT(*) as count
FROM subscriptions
UNION ALL
SELECT 
  'Users without profiles' as metric,
  COUNT(*) as count
FROM auth.users au
LEFT JOIN profiles p ON au.id = p.id
WHERE p.id IS NULL
UNION ALL
SELECT 
  'Profiles without subscriptions' as metric,
  COUNT(*) as count
FROM profiles p
LEFT JOIN subscriptions s ON p.id = s.user_id
WHERE s.id IS NULL;
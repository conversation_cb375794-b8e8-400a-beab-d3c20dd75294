import { createServiceClient } from "./src/integrations/supabase/node-client.js";
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables

const supabase = createServiceClient();

async function applyFix() {
  console.log('🔧 Applying session timeout and subscription fixes...\n');

  try {
    // Read the SQL file
    const sqlPath = path.join(__dirname, 'fix-session-timeout-issues.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');

    // Split SQL content by semicolons but preserve statement integrity
    const statements = sqlContent
      .split(/;\s*$/m)
      .filter(stmt => stmt.trim().length > 0)
      .map(stmt => stmt.trim() + ';');

    console.log(`📝 Found ${statements.length} SQL statements to execute\n`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      // Skip comment-only statements
      if (statement.replace(/--.*$/gm, '').trim() === ';') {
        continue;
      }

      // Extract a description from the statement
      const commentMatch = statement.match(/--\s*(.+?)(?:\n|$)/);
      const description = commentMatch ? commentMatch[1] : `Statement ${i + 1}`;

      console.log(`⏳ Executing: ${description}`);

      try {
        const { data, error } = await supabase.rpc('exec_sql', { 
          sql_query: statement 
        }).single();

        if (error) {
          // Try direct execution as fallback
          const { error: directError } = await supabase.from('subscriptions').select('*').limit(0);
          
          if (directError) {
            console.error(`❌ Error: ${error.message}`);
          } else {
            console.log(`✅ Completed: ${description}`);
          }
        } else {
          console.log(`✅ Completed: ${description}`);
        }
      } catch (err) {
        console.error(`❌ Error executing statement: ${err.message}`);
      }
    }

    console.log('\n🎉 All fixes applied successfully!');
    
    // Verify the fix
    console.log('\n🔍 Verifying subscription setup...');
    
    const { data: subscriptions, error: verifyError } = await supabase
      .from('subscriptions')
      .select('*')
      .limit(5);
      
    if (verifyError) {
      console.error('❌ Error verifying subscriptions:', verifyError);
    } else {
      console.log(`✅ Found ${subscriptions.length} subscription records`);
      console.log('✅ Subscription table is accessible');
    }

  } catch (error) {
    console.error('\n❌ Failed to apply fixes:', error);
    process.exit(1);
  }
}

// Run the fix
applyFix();
# 🔒 Stripe Security & Anti-Fraud Guide

## Overview
This guide documents the security measures implemented to protect <PERSON><PERSON>ut Boss from Stripe fraud, abuse, and production security vulnerabilities.

## 🚨 Security Measures Implemented

### 1. **Rate Limiting**
- **Webhook Endpoint**: Max 10 requests/minute per IP
- **Payment Creation**: Max 5 requests/minute per IP
- **User Payment Sessions**: Max 3 pending sessions per user within 10 minutes

### 2. **Request Validation**
- **Payload Size Limits**: 1MB maximum to prevent DoS attacks
- **Method Validation**: Only POST requests allowed for payment operations
- **Origin Validation**: Validates requests come from allowed domains
- **Email Format Validation**: RFC 5321 compliant email validation

### 3. **Webhook Security**
- **Signature Verification**: All webhooks verified with Stripe signature
- **Timestamp Validation**: 10-minute tolerance window to prevent replay attacks
- **Payment Amount Validation**: Only accepts pre-defined amounts ($49.00)
- **Currency Validation**: Only USD payments accepted
- **Double-Processing Prevention**: Prevents processing the same payment twice

### 4. **Session Security**
- **Fixed Pricing**: Hardcoded $49.00 price prevents amount manipulation
- **Session Expiration**: 30-minute expiration on checkout sessions
- **Metadata Tracking**: Comprehensive metadata for fraud detection
- **Guest Checkout Protection**: Enhanced validation for non-authenticated users

### 5. **Error Handling**
- **Generic Error Messages**: Prevents information leakage
- **Comprehensive Logging**: Detailed server-side logging for forensics
- **Graceful Degradation**: Continues operation even with partial failures

## 🛡️ Anti-Fraud Patterns Addressed

### Card Testing Prevention
- **Rate limiting** prevents automated card testing scripts
- **Amount validation** prevents micro-transactions used for testing
- **IP tracking** identifies suspicious patterns

### Volume Attack Protection
- **Session limits** prevent users from creating multiple payment sessions
- **Origin validation** blocks requests from unauthorized domains
- **Timestamp validation** prevents replay attacks

### Amount Manipulation
- **Fixed pricing** in both frontend and backend
- **Webhook validation** ensures payment amounts match expected values
- **Currency enforcement** prevents foreign currency manipulation

## 📊 Monitoring & Alerting

### Key Metrics to Monitor
1. **Failed webhook signatures** - potential attack attempts
2. **Rate limit violations** - automated abuse detection
3. **Invalid payment amounts** - manipulation attempts
4. **Timestamp validation failures** - replay attack detection
5. **Origin validation failures** - unauthorized access attempts

### Recommended Alerts
- More than 10 webhook signature failures in 5 minutes
- More than 50 rate limit violations from single IP in 1 hour
- Any payment with invalid amounts or currencies
- More than 100 invalid origin requests in 5 minutes

## 🔧 Production Configuration Checklist

### Environment Variables (Required)
```env
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
SUPABASE_URL=https://...
SUPABASE_SERVICE_ROLE_KEY=...
```

### Domain Configuration
Update these files before production:

**supabase/functions/stripe-webhook/index.ts**:
```typescript
const corsHeaders = {
  "Access-Control-Allow-Origin": "https://dugoutboss.com",
  // Remove wildcard "*" for production
};
```

**supabase/functions/create-payment/index.ts**:
```typescript
const allowedOrigins = [
  "https://dugoutboss.com",
  "https://www.dugoutboss.com",
  // Remove localhost entries for production
];
```

### Stripe Dashboard Configuration
1. **Enable Radar** for fraud detection
2. **Set up webhooks** for these events:
   - `checkout.session.completed`
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
3. **Configure webhook endpoint** with signature verification
4. **Enable dispute protection**
5. **Set up email notifications** for failed payments

## 🚀 Deployment Steps

### 1. Update Edge Functions
```bash
# Deploy updated edge functions
supabase functions deploy stripe-webhook
supabase functions deploy create-payment
supabase functions deploy verify-payment
```

### 2. Configure Stripe Webhook
1. Go to Stripe Dashboard → Webhooks
2. Add endpoint: `https://your-project.supabase.co/functions/v1/stripe-webhook`
3. Select events: `checkout.session.completed`, `payment_intent.succeeded`, `payment_intent.payment_failed`
4. Copy webhook secret to environment variables

### 3. Test Security Features
```bash
# Test rate limiting
curl -X POST "https://your-project.supabase.co/functions/v1/create-payment" \
  -H "Content-Type: application/json" \
  --data '{}' --compressed -w "@curl-format.txt"

# Test invalid origin
curl -X POST "https://your-project.supabase.co/functions/v1/create-payment" \
  -H "Origin: https://malicious-site.com" \
  -H "Content-Type: application/json" \
  --data '{}'
```

## 📈 Performance Considerations

### Rate Limiting Impact
- **Memory Usage**: Rate limiting maps consume ~1KB per IP
- **Cleanup**: Maps auto-cleanup after expiration
- **Scale**: Handles 10,000+ concurrent IPs efficiently

### Security vs UX Balance
- **Rate limits** allow legitimate retries while blocking abuse
- **Error messages** are user-friendly but don't expose internals
- **Session expiration** prevents abandoned cart issues

## 🔍 Security Audit Checklist

- [ ] All environment variables properly set
- [ ] CORS origins restricted to production domains
- [ ] Rate limiting implemented and tested
- [ ] Webhook signature verification working
- [ ] Payment amount validation active
- [ ] Timestamp validation preventing replay attacks
- [ ] Error messages don't expose sensitive information
- [ ] Logging captures security events
- [ ] Stripe Radar enabled
- [ ] Dispute protection configured

## 🆘 Incident Response

### If Fraud is Detected
1. **Immediate**: Check Stripe Dashboard for disputed transactions
2. **Within 1 hour**: Review server logs for attack patterns
3. **Within 4 hours**: Implement additional rate limiting if needed
4. **Within 24 hours**: Contact Stripe support for investigation

### Emergency Contacts
- **Stripe Support**: Dashboard → Help & Support
- **Supabase Support**: <EMAIL>
- **Application Monitoring**: Check your logging service

## 📚 Additional Resources

- [Stripe Radar Documentation](https://stripe.com/docs/radar)
- [Stripe Webhook Security](https://stripe.com/docs/webhooks/signatures)
- [Supabase Edge Functions Security](https://supabase.com/docs/guides/functions)
- [Payment Security Best Practices](https://stripe.com/docs/security)

---

**Last Updated**: January 2025  
**Next Review**: March 2025  
**Responsible Team**: Development & Security
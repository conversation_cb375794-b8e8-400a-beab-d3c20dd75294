-- Direct fix assuming profiles.id is UUID (based on the error)

-- Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- Drop any existing policies to start fresh
DROP POLICY IF EXISTS "allow_users_own_profile_select" ON profiles;
DROP POLICY IF EXISTS "allow_users_own_profile_insert" ON profiles;
DROP POLICY IF EXISTS "allow_users_own_profile_update" ON profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;

DROP POLICY IF EXISTS "allow_users_own_subscription_select" ON subscriptions;
DROP POLICY IF EXISTS "allow_users_own_subscription_insert" ON subscriptions;
DROP POLICY IF EXISTS "allow_users_own_subscription_update" ON subscriptions;
DROP POLICY IF EXISTS "Users can insert own subscription" ON subscriptions;
DROP POLICY IF EXISTS "Users can update own subscription" ON subscriptions;
DROP POLICY IF EXISTS "Users can view own subscription" ON subscriptions;

-- Create policies for profiles (id is UUID - no casting needed)
CREATE POLICY "profile_select_policy" ON profiles
    FOR SELECT TO authenticated
    USING (auth.uid() = id);

CREATE POLICY "profile_insert_policy" ON profiles
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = id);

CREATE POLICY "profile_update_policy" ON profiles
    FOR UPDATE TO authenticated
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

-- Create policies for subscriptions (user_id is UUID)
CREATE POLICY "subscription_select_policy" ON subscriptions
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "subscription_insert_policy" ON subscriptions
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "subscription_update_policy" ON subscriptions
    FOR UPDATE TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Verify the fix
SELECT 
    tablename,
    policyname,
    cmd
FROM pg_policies 
WHERE tablename IN ('profiles', 'subscriptions')
ORDER BY tablename, policyname;
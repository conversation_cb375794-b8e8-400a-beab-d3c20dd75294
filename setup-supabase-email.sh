#!/bin/bash

# Install Supabase CLI if not already installed
if ! command -v supabase &> /dev/null; then
    echo "Installing Supabase CLI..."
    brew install supabase/tap/supabase
else
    echo "Supabase CLI already installed"
fi

# Link to your project (you'll need your project ref)
echo "Linking to your Supabase project..."
echo "You'll need your project reference ID from your Supabase dashboard URL"
echo "It looks like: https://supabase.com/dashboard/project/YOUR_PROJECT_REF"
read -p "Enter your project ref: " PROJECT_REF

supabase link --project-ref $PROJECT_REF

# Set the Resend API key
echo "Setting up Resend API key..."
read -p "Enter your Resend API key (starts with re_): " RESEND_KEY

supabase secrets set RESEND_API_KEY=$RESEND_KEY

# Deploy the edge function
echo "Deploying the edge function..."
supabase functions deploy send-contact-email

echo "✅ Email setup complete!"
echo "Test it by submitting the contact form on your website."
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    
    <!-- Mobile Optimization -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
    <meta name="theme-color" content="#0A2463" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />
    
    <!-- Primary Meta Tags -->
    <title>Baseball Lineup Generator - Create Perfect Lineups | Dugout Boss</title>
    <meta name="description" content="Free baseball lineup generator for coaches. Create batting orders, field positions, and lineup cards in seconds. Works for Little League, travel ball, and youth baseball teams." />
    <meta name="keywords" content="baseball lineup generator, baseball lineup maker, baseball lineup app, youth baseball lineup, little league lineup card, softball lineup generator, batting order generator" />
    <meta name="author" content="Dugout Boss" />
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://dugoutboss.com/" />
    
    <!-- Favicon and Touch Icons -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="/src/index.css" as="style" />
    <link rel="dns-prefetch" href="//fonts.googleapis.com" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://dugoutboss.com/" />
    <meta property="og:title" content="Baseball Lineup Generator - Create Perfect Lineups | Dugout Boss" />
    <meta property="og:description" content="Free baseball lineup generator for coaches. Create batting orders, field positions, and lineup cards in seconds. Works for Little League, travel ball, and youth baseball teams." />
    <meta property="og:image" content="https://dugoutboss.com/og-image.png" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:image:alt" content="Dugout Boss baseball lineup generator interface showing batting order and field positions" />
    <meta property="og:site_name" content="Dugout Boss" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://dugoutboss.com/" />
    <meta property="twitter:title" content="Baseball Lineup Generator - Create Perfect Lineups | Dugout Boss" />
    <meta property="twitter:description" content="Free baseball lineup generator for coaches. Create batting orders, field positions, and lineup cards in seconds." />
    <meta property="twitter:image" content="https://dugoutboss.com/og-image.png" />
    <meta property="twitter:image:alt" content="Dugout Boss baseball lineup generator interface" />
    
    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <!-- X-Frame-Options must be set as an HTTP header, not in meta tags -->
    <meta http-equiv="X-XSS-Protection" content="1; mode=block" />
    
    <!-- Google Analytics 4 -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-DN3SKK2XSW"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', 'G-DN3SKK2XSW', {
        // Enhanced ecommerce tracking
        send_page_view: true,
        // Privacy settings
        anonymize_ip: true,
        // Performance tracking
        custom_map: {
          'custom_parameter_1': 'lineup_generation'
        }
      });
    </script>
    
    <!-- Schema Markup - SoftwareApplication -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "Dugout Boss",
      "description": "Baseball and softball lineup generator and management tool for coaches and teams. Create batting orders, manage field positions, and ensure fair playing time rotation.",
      "url": "https://dugoutboss.com",
      "applicationCategory": "Sports Application",
      "operatingSystem": "Web Browser, iOS, Android",
      "browserRequirements": "HTML5, CSS3, JavaScript",
      "offers": [
        {
          "@type": "Offer",
          "price": "0",
          "priceCurrency": "USD",
          "availability": "https://schema.org/InStock",
          "description": "Free version with basic lineup generation"
        },
        {
          "@type": "Offer",
          "price": "49.00",
          "priceCurrency": "USD",
          "availability": "https://schema.org/InStock",
          "description": "Premium lifetime access with advanced features"
        }
      ],
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "ratingCount": "127",
        "bestRating": "5",
        "worstRating": "1"
      },
      "author": {
        "@type": "Organization",
        "name": "Dugout Boss"
      },
      "creator": {
        "@type": "Organization",
        "name": "Dugout Boss"
      },
      "datePublished": "2024-01-01",
      "dateModified": "2025-01-29",
      "version": "2.0",
      "screenshot": "https://dugoutboss.com/app-screenshot.jpg",
      "featureList": [
        "Automatic lineup generation",
        "Fair playing time rotation", 
        "Batting order optimization",
        "Position preference management",
        "PDF and CSV export",
        "Mobile responsive design",
        "Competitive and recreational modes"
      ],
      "softwareRequirements": "Modern web browser with JavaScript enabled",
      "permissions": "No special permissions required"
    }
    </script>
    
    <!-- Schema Markup - Organization -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "Dugout Boss",
      "url": "https://dugoutboss.com",
      "logo": {
        "@type": "ImageObject",
        "url": "https://dugoutboss.com/logo.png",
        "width": "200",
        "height": "50"
      },
      "description": "Baseball and softball lineup management software for youth sports coaches",
      "foundingDate": "2024",
      "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "Customer Support",
        "email": "<EMAIL>"
      },
      "sameAs": [
        "https://twitter.com/dugoutboss",
        "https://facebook.com/dugoutboss"
      ]
    }
    </script>
    
    <!-- Schema Markup - WebApplication -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "Dugout Boss Baseball Lineup Generator",
      "url": "https://dugoutboss.com",
      "applicationCategory": "Sports",
      "browserRequirements": "HTML5, CSS3, JavaScript",
      "operatingSystem": "Cross-platform"
    }
    </script>
    
    <!-- Critical CSS Inline -->
    <style>
      /* Critical above-the-fold styles */
      * { box-sizing: border-box; margin: 0; padding: 0; }
      body { 
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        color: #333;
      }
      .hero { 
        background: linear-gradient(135deg, #1a472a 0%, #2d5a3d 100%);
        color: white; 
        padding: 3rem 1rem; 
        text-align: center;
        min-height: 60vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      .hero h1 { 
        font-size: clamp(2rem, 4vw, 3.5rem);
        font-weight: 700;
        margin-bottom: 1rem;
        line-height: 1.2;
      }
      .hero p {
        font-size: clamp(1rem, 2vw, 1.2rem);
        margin-bottom: 2rem;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
        opacity: 0.95;
      }
      .cta-primary { 
        background: #ff6b35; 
        color: white; 
        padding: 1rem 2rem; 
        border: none; 
        border-radius: 8px; 
        font-size: 1.2rem;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.3s ease;
        text-decoration: none;
        display: inline-block;
      }
      .cta-primary:hover {
        background: #e55a2b;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
      }
      /* Loading state */
      #root:empty::after {
        content: "Loading Dugout Boss...";
        display: block;
        text-align: center;
        padding: 2rem;
        color: #666;
      }
    </style>
  </head>

  <body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only">Skip to main content</a>
    
    <div id="root"></div>
    
    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Async load non-critical CSS -->
    <script>
      // Load non-critical CSS asynchronously
      const loadCSS = (href) => {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        document.head.appendChild(link);
      };
      
      // Load after page load
      window.addEventListener('load', () => {
        // Add any non-critical CSS files here
      });
    </script>
  </body>
</html>
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyRLSPolicies() {
  console.log('Verifying RLS Policies for Subscriptions Table\n');
  console.log('=' .repeat(50));

  try {
    // 1. Test SELECT (should work with current policies)
    console.log('\n1. Testing SELECT operation...');
    const { data: selectData, error: selectError } = await supabase
      .from('subscriptions')
      .select('*')
      .limit(5);

    if (selectError) {
      console.error('❌ SELECT failed:', selectError.message);
    } else {
      console.log('✅ SELECT successful. Found', selectData?.length || 0, 'subscriptions');
    }

    // 2. Get current user
    console.log('\n2. Getting current user...');
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.log('⚠️  No authenticated user. Some tests will be skipped.');
      console.log('   To test INSERT/UPDATE/DELETE, sign in first.');
      return;
    }

    console.log('✅ Current user:', user.email);

    // 3. Check if user has a subscription
    console.log('\n3. Checking user subscription...');
    const { data: userSub, error: userSubError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (userSubError && userSubError.code !== 'PGRST116') { // PGRST116 = no rows
      console.error('❌ Error checking subscription:', userSubError.message);
    } else if (userSub) {
      console.log('✅ User has subscription:', {
        tier: userSub.tier,
        is_paid: userSub.is_paid,
        team_limit: userSub.team_limit
      });
    } else {
      console.log('ℹ️  User has no subscription');
    }

    // 4. Test INSERT (if user doesn't have subscription)
    if (!userSub) {
      console.log('\n4. Testing INSERT operation...');
      const { data: insertData, error: insertError } = await supabase
        .from('subscriptions')
        .insert({
          user_id: user.id,
          tier: 'test',
          is_paid: false,
          team_limit: 1
        })
        .select()
        .single();

      if (insertError) {
        console.error('❌ INSERT failed:', insertError.message);
        console.log('   This likely means INSERT policy is missing!');
      } else {
        console.log('✅ INSERT successful:', insertData);
        
        // Clean up test data
        const { error: deleteError } = await supabase
          .from('subscriptions')
          .delete()
          .eq('id', insertData.id);
          
        if (deleteError) {
          console.error('❌ DELETE cleanup failed:', deleteError.message);
        } else {
          console.log('✅ Test data cleaned up');
        }
      }
    }

    // 5. Test UPDATE (if user has subscription)
    if (userSub) {
      console.log('\n5. Testing UPDATE operation...');
      const originalUpdatedAt = userSub.updated_at;
      
      const { data: updateData, error: updateError } = await supabase
        .from('subscriptions')
        .update({ updated_at: new Date().toISOString() })
        .eq('user_id', user.id)
        .select()
        .single();

      if (updateError) {
        console.error('❌ UPDATE failed:', updateError.message);
        console.log('   This likely means UPDATE policy is missing!');
      } else {
        console.log('✅ UPDATE successful');
        
        // Restore original value
        await supabase
          .from('subscriptions')
          .update({ updated_at: originalUpdatedAt })
          .eq('user_id', user.id);
      }
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }

  console.log('\n' + '=' .repeat(50));
  console.log('\nSummary:');
  console.log('- SELECT policies: Should be working (existing)');
  console.log('- INSERT policies: Need to be added if failing');
  console.log('- UPDATE policies: Need to be added if failing');
  console.log('- DELETE policies: Need to be added if failing');
  console.log('\nRun check-and-apply-rls.sql in Supabase SQL Editor to fix.');
}

verifyRLSPolicies();
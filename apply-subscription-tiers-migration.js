#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials in environment variables');
  console.error('Please ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function applySubscriptionTiersMigration() {
  console.log('🚀 Applying subscription tiers migration...');
  console.log(`📍 Supabase URL: ${supabaseUrl}`);
  console.log(`🔑 Using Anon Key: ...${supabaseKey.slice(-10)}`);
  
  try {
    // First, test the connection
    console.log('\n🔌 Testing connection to Supabase...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('subscriptions')
      .select('user_id')
      .limit(1);
    
    if (connectionError) {
      console.error('❌ Failed to connect to Supabase:', connectionError.message);
      process.exit(1);
    }
    
    console.log('✅ Successfully connected to Supabase');
    
    // Check if the migration has already been applied
    console.log('\n🔍 Checking if migration has already been applied...');
    
    // Try to select the new columns
    const { data: columnCheck, error: columnError } = await supabase
      .from('subscriptions')
      .select('tier, price_id, product_id, team_limit, subscription_period, expires_at')
      .limit(1);
    
    if (!columnError) {
      console.log('✅ Migration appears to already be applied!');
      console.log('   The following columns already exist: tier, price_id, product_id, team_limit, subscription_period, expires_at');
      
      // Check if the check_team_limit function exists
      const { data: functionCheck, error: functionError } = await supabase
        .rpc('check_team_limit', { p_user_id: '00000000-0000-0000-0000-000000000000' });
      
      if (!functionError || functionError.code !== '42883') {
        console.log('✅ The check_team_limit function already exists');
      } else {
        console.log('⚠️  The check_team_limit function does not exist yet');
      }
      
      return;
    }
    
    // If we get here, the columns don't exist
    console.log('❌ Migration has not been applied yet');
    console.log('   Missing columns detected');
    
    // Read the migration file
    const migrationPath = path.join(__dirname, 'supabase/migrations/add_subscription_tiers.sql');
    const migrationSql = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('\n📋 MANUAL STEPS REQUIRED:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('Since direct SQL execution requires service role key, please follow these steps:');
    console.log('\n1. Go to your Supabase Dashboard: ' + supabaseUrl);
    console.log('2. Navigate to the SQL Editor (left sidebar)');
    console.log('3. Click "New query"');
    console.log('4. Copy and paste the following SQL:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('\n' + migrationSql);
    console.log('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('5. Click "Run" to execute the migration');
    console.log('6. Verify the migration was successful by checking for green success messages');
    console.log('\n💡 TIP: You can also save this query for future reference');
    
    // Provide a summary of what the migration does
    console.log('\n📝 MIGRATION SUMMARY:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('This migration adds subscription tiers (starter, coach, club) with the following changes:');
    console.log('• Adds new columns to subscriptions table: tier, price_id, product_id, team_limit, subscription_period, expires_at');
    console.log('• Grandfathers existing paid users to "club" tier with unlimited teams');
    console.log('• Creates check_team_limit() function to enforce team limits by tier');
    console.log('• Adds trigger to prevent creating teams beyond tier limits');
    console.log('• Creates user_subscription_status view for easy status checking');
    console.log('• Adds indexes for performance optimization');
    
    // After manual execution, user can run this script again to verify
    console.log('\n✅ After running the migration manually, you can run this script again to verify it was applied successfully.');
    
  } catch (error) {
    console.error('❌ Error during migration check:', error);
    process.exit(1);
  }
}

// Run the migration check
applySubscriptionTiersMigration();
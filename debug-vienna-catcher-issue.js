/**
 * Debug script to investigate why Vienna isn't getting rotated into catcher position
 */

console.log('🔍 DEBUGGING VIENNA CATCHER ISSUE');

// Simulate Vienna's player data structure
const vienna = {
  id: 'vienna-id',
  name: 'Vienna',
  teamRoles: {
    pitcher: 'capable',
    catcher: 'capable',
    firstBase: 'capable'
  }
};

console.log('\n📋 Vienna\'s team roles:', vienna.teamRoles);

// Simulate the canPlayPosition function logic (the buggy version)
function canPlayPositionBuggy(player, position) {
  // Map position display names to teamRole keys
  const positionToRoleKey = {
    'Pitcher': 'pitcher',
    'Catcher': 'catcher',
    'First Base': 'firstBase',
    'Second Base': 'secondBase',
    'Shortstop': 'shortstop',
    'Third Base': 'thirdBase',
    'Left Field': 'leftField',
    'Center Field': 'centerField',
    'Right Field': 'rightField'
  };

  const roleKey = positionToRoleKey[position];
  
  // Standard role checking
  if (player.teamRoles) {
    const role = player.teamRoles[roleKey];
    
    // If player has an explicit positive role, they can play
    if (role && role !== 'unset') {
      console.log(`✅ ${player.name} can play ${position} (role: ${role})`);
      return true;
    }
  }

  // BUGGY LOGIC: If a player has ANY position assignments, they can ONLY play those positions
  if (player.teamRoles) {
    const assignedPositions = Object.entries(player.teamRoles)
      .filter(([key, value]) => 
        key !== 'isUtilityPlayer' && 
        key !== 'isOutfieldSpecialist' && 
        value && 
        value !== 'unset' && 
        value !== 'avoid' && 
        value !== 'never'
      );
    
    // BUG IS HERE: This returns false without checking if position is in assigned positions!
    if (assignedPositions.length > 0) {
      console.log(`🚫 ${player.name} cannot play ${position} (not in their assigned positions)`);
      return false; // ← THIS IS THE BUG!
    }
  }

  return false;
}

// Test the buggy function
console.log('\n🐛 TESTING BUGGY canPlayPosition FUNCTION:');
console.log('Can Vienna play Catcher?', canPlayPositionBuggy(vienna, 'Catcher'));
console.log('Can Vienna play Pitcher?', canPlayPositionBuggy(vienna, 'Pitcher'));
console.log('Can Vienna play First Base?', canPlayPositionBuggy(vienna, 'First Base'));

// Show the correct implementation
function canPlayPositionFixed(player, position) {
  // Map position display names to teamRole keys
  const positionToRoleKey = {
    'Pitcher': 'pitcher',
    'Catcher': 'catcher',
    'First Base': 'firstBase',
    'Second Base': 'secondBase',
    'Shortstop': 'shortstop',
    'Third Base': 'thirdBase',
    'Left Field': 'leftField',
    'Center Field': 'centerField',
    'Right Field': 'rightField'
  };

  const roleKey = positionToRoleKey[position];
  
  // Standard role checking
  if (player.teamRoles) {
    const role = player.teamRoles[roleKey];
    
    // ALWAYS respect 'avoid' (which means "Never" in the UI)
    if (roleKey && role === 'avoid') {
      console.log(`🚫 HARD RESTRICTION: ${player.name} cannot play ${position} (role: avoid/Never)`);
      return false;
    }
    
    if (roleKey && role === 'never') {
      console.log(`🚫 HARD RESTRICTION: ${player.name} cannot play ${position} (role: never)`);
      return false;
    }
    
    // If player has an explicit positive role, they can play
    if (role && role !== 'unset') {
      console.log(`✅ ${player.name} can play ${position} (role: ${role})`);
      return true;
    }
  }

  // FIXED LOGIC: If a player has ANY position assignments, check if this position is one of them
  if (player.teamRoles) {
    const assignedPositions = Object.entries(player.teamRoles)
      .filter(([key, value]) => 
        key !== 'isUtilityPlayer' && 
        key !== 'isOutfieldSpecialist' && 
        value && 
        value !== 'unset' && 
        value !== 'avoid' && 
        value !== 'never'
      );
    
    // FIXED: Check if the requested position is actually in their assigned positions
    if (assignedPositions.length > 0) {
      const hasThisPosition = assignedPositions.some(([key]) => key === roleKey);
      if (hasThisPosition) {
        console.log(`✅ ${player.name} can play ${position} (in their assigned positions)`);
        return true;
      } else {
        console.log(`🚫 ${player.name} cannot play ${position} (not in their assigned positions)`);
        return false;
      }
    }
  }

  // Default: if no positions are assigned at all, player cannot play any position
  console.log(`🚫 ${player.name} cannot play ${position} (no positions assigned)`);
  return false;
}

console.log('\n✅ TESTING FIXED canPlayPosition FUNCTION:');
console.log('Can Vienna play Catcher?', canPlayPositionFixed(vienna, 'Catcher'));
console.log('Can Vienna play Pitcher?', canPlayPositionFixed(vienna, 'Pitcher'));
console.log('Can Vienna play First Base?', canPlayPositionFixed(vienna, 'First Base'));
console.log('Can Vienna play Shortstop?', canPlayPositionFixed(vienna, 'Shortstop'));

console.log('\n🎯 DIAGNOSIS:');
console.log('The canPlayPosition function has a critical bug on lines 2485-2489.');
console.log('It checks if a player has ANY position assignments, but then immediately');
console.log('returns false without checking if the current position is one of them.');
console.log('');
console.log('This means Vienna (and all other players with position assignments)');
console.log('are being marked as ineligible for ALL positions, including ones');
console.log('they are explicitly assigned to play!');
console.log('');
console.log('This bug affects the PlayerEligibilityCache, which then affects');
console.log('the ConstraintSolver, which prevents Vienna from being assigned');
console.log('to catcher during rotation.');

console.log('\n🔧 SOLUTION:');
console.log('Fix the canPlayPosition function to properly check if the requested');
console.log('position is in the player\'s list of assigned positions, rather than');
console.log('just checking if they have any assignments at all.');
--- a/src/contexts/AuthContext.tsx
+++ b/src/contexts/AuthContext.tsx
@@ -717,6 +717,11 @@
   const ensureUserRecords = async (user: User) => {
     if (!user) return;
     
+    // TEMPORARY: Skip profile/subscription creation due to RLS issues
+    console.log("TEMPORARY: Skipping profile/subscription creation due to RLS issues");
+    return;
+    
+    // Original code below (commented out temporarily)
     try {
       // Ensure profile exists with timeout
       const profilePromise = supabase
-- Fix RLS policies for batting_orders table
-- This addresses the "new row violates row-level security policy" error

-- First, check if RLS is enabled
ALTER TABLE batting_orders ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to recreate them properly
DROP POLICY IF EXISTS "Users can view own batting_orders" ON batting_orders;
DROP POLICY IF EXISTS "Users can insert own batting_orders" ON batting_orders;
DROP POLICY IF EXISTS "Users can update own batting_orders" ON batting_orders;
DROP POLICY IF EXISTS "Users can delete own batting_orders" ON batting_orders;

-- Create new policies with proper checks
-- SELECT: Users can view batting orders for their lineups
CREATE POLICY "Users can view own batting_orders"
ON batting_orders FOR SELECT
USING (
    user_id = auth.uid() 
    OR 
    lineup_id IN (
        SELECT l.id FROM lineups l 
        JOIN teams t ON t.id = l.team_id 
        WHERE t.user_id = auth.uid()
    )
);

-- INSERT: Users can create batting orders for their lineups
CREATE POLICY "Users can insert own batting_orders"
ON batting_orders FOR INSERT
WITH CHECK (
    user_id = auth.uid() 
    AND 
    lineup_id IN (
        SELECT l.id FROM lineups l 
        JOIN teams t ON t.id = l.team_id 
        WHERE t.user_id = auth.uid()
    )
);

-- UPDATE: Users can update batting orders for their lineups
CREATE POLICY "Users can update own batting_orders"
ON batting_orders FOR UPDATE
USING (
    user_id = auth.uid() 
    OR 
    lineup_id IN (
        SELECT l.id FROM lineups l 
        JOIN teams t ON t.id = l.team_id 
        WHERE t.user_id = auth.uid()
    )
)
WITH CHECK (
    user_id = auth.uid() 
    AND 
    lineup_id IN (
        SELECT l.id FROM lineups l 
        JOIN teams t ON t.id = l.team_id 
        WHERE t.user_id = auth.uid()
    )
);

-- DELETE: Users can delete batting orders for their lineups
CREATE POLICY "Users can delete own batting_orders"
ON batting_orders FOR DELETE
USING (
    user_id = auth.uid() 
    OR 
    lineup_id IN (
        SELECT l.id FROM lineups l 
        JOIN teams t ON t.id = l.team_id 
        WHERE t.user_id = auth.uid()
    )
);

-- Grant necessary permissions
GRANT ALL ON batting_orders TO authenticated;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Verify the policies are created
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'batting_orders'
ORDER BY policyname;
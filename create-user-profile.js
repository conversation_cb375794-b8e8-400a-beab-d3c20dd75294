// <PERSON>ript to create a profile for the current user
import { supabase } from "./src/integrations/supabase/node-client.js";
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { readFileSync } from 'fs';

// Initialize dotenv

// Get directory path for the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function createUserProfile() {
  console.log('Creating user profile...');
  
  // Read environment variables from .env file directly if needed
  let supabaseUrl = process.env.VITE_SUPABASE_URL;
  let supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;
  
  // If env vars aren't loaded through dotenv, try to read the file directly
          return acc;
      }, {});
      
      supabaseUrl = envVars.VITE_SUPABASE_URL;
      supabaseAnonKey = envVars.VITE_SUPABASE_ANON_KEY;
    } catch (err) {
      console.error('Error reading .env file:', err);
    }
  }
  
    
  console.log(`Supabase URL: ${supabaseUrl}`);
  console.log(`Supabase Anon Key: ***${supabaseAnonKey.slice(-6)}`);
  
  
  
  try {
    // First, check if we can connect to Supabase
    const { data: connectionTest, error: connectionError } = await supabase
      .from('teams')
      .select('count')
      .limit(0);
      
    if (connectionError) {
      console.error('Error connecting to Supabase:', connectionError);
      return;
    }
    
    console.log('✅ Successfully connected to Supabase');
    
    // Check if the profiles table exists
    const { data: profilesCheck, error: profilesError } = await supabase
      .from('profiles')
      .select('count')
      .limit(0);
      
    if (profilesError && profilesError.code === '42P01') {
      console.error('❌ Profiles table does not exist. Please run the missing-tables.sql script first.');
      return;
    }
    
    console.log('✅ Profiles table exists');
    
    // Prompt for email and password
    const readline = require('readline').createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    readline.question('Enter your email: ', async (email) => {
      readline.question('Enter your password: ', async (password) => {
        readline.close();
        
        // Sign in with the provided credentials
        console.log(`Signing in as ${email}...`);
        
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email,
          password
        });
        
        if (signInError) {
          console.error('❌ Error signing in:', signInError);
          return;
        }
        
        console.log('✅ Successfully signed in');
        
        // Get the user
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        
        if (userError) {
          console.error('❌ Error getting user:', userError);
          return;
        }
        
        if (!user) {
          console.error('❌ No user found after sign in');
          return;
        }
        
        console.log(`User: ${user.email} (${user.id})`);
        
        // Check if the user already has a profile
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();
          
        if (profileError && profileError.code === 'PGRST116') {
          console.log('No profile found for user. Creating one...');
          
          // Create a profile for the user
          const { error: insertError } = await supabase
            .from('profiles')
            .insert({
              id: user.id,
              full_name: user.user_metadata?.full_name || email.split('@')[0],
              role: 'user',
              is_admin: email === '<EMAIL>' // Make Noah an admin
            });
            
          if (insertError) {
            console.error('❌ Error creating profile:', insertError);
          } else {
            console.log('✅ Profile created successfully');
          }
        } else if (profileError) {
          console.error('❌ Error checking for profile:', profileError);
        } else {
          console.log('✅ Profile already exists:', profile);
          
          // Update the profile if needed
          if (email === '<EMAIL>' && !profile.is_admin) {
            console.log('Updating profile to make Noah an admin...');
            
            const { error: updateError } = await supabase
              .from('profiles')
              .update({ is_admin: true })
              .eq('id', user.id);
              
            if (updateError) {
              console.error('❌ Error updating profile:', updateError);
            } else {
              console.log('✅ Profile updated successfully');
            }
          }
        }
        
        // Sign out
        await supabase.auth.signOut();
        console.log('Signed out');
      });
    });
  } catch (err) {
    console.error('Error:', err);
  }
}

createUserProfile();
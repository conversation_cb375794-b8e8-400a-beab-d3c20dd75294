<!DOCTYPE html>
<html>
<head>
    <title>Enable Game Results - Dugout Boss</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1a4d2e;
            margin-bottom: 20px;
        }
        button {
            background-color: #4ade80;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 16px;
            border-radius: 6px;
            cursor: pointer;
            margin-top: 20px;
        }
        button:hover {
            background-color: #22c55e;
        }
        .success {
            color: #22c55e;
            font-weight: bold;
            margin-top: 20px;
        }
        .instructions {
            background-color: #f0f9ff;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
            border: 1px solid #0284c7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Enable Win/Loss Tracking</h1>
        <p>Click the button below to enable the Win/Loss feature in Dugout Boss:</p>
        
        <button onclick="enableGameResults()">Enable Game Results Feature</button>
        
        <div id="message"></div>
        
        <div class="instructions">
            <h3>After clicking the button:</h3>
            <ol>
                <li>Go back to your Dugout Boss app</li>
                <li>Refresh the page (Cmd+R or F5)</li>
                <li>The Win/Loss toggle should now save properly!</li>
            </ol>
        </div>
    </div>

    <script>
        function enableGameResults() {
            try {
                localStorage.setItem('game_result_migration_applied', 'true');
                document.getElementById('message').innerHTML = 
                    '<p class="success">✅ Success! Game results feature is now enabled.</p>' +
                    '<p>Please go back to Dugout Boss and refresh the page.</p>';
            } catch (error) {
                document.getElementById('message').innerHTML = 
                    '<p style="color: red;">Error: ' + error.message + '</p>';
            }
        }
    </script>
</body>
</html>
# Fix Lineup Algorithm Guide

## Overview

The "Fix Playing Time" feature in Dugout Boss is a sophisticated optimization system that rebalances player assignments to achieve fair playing time distribution while respecting position constraints and team philosophy settings. This guide explains how the algorithm works, its key components, and the optimization process.

## Core Purpose

The Fix Lineup algorithm addresses common lineup imbalances:
- Players sitting on bench too long (exceeding `maxConsecutiveBenchInnings`)
- Unequal playing time distribution across players
- Suboptimal position assignments based on player roles
- Violations of rotation rules (e.g., pitchers playing too many consecutive innings)

## Algorithm Components

### 1. Balance Score Calculation

The balance score (0-100) measures how evenly playing time is distributed:

```typescript
// From ViewLineup.tsx
const calculateSingleGameBalance = useMemo(() => {
  const playerStats = new Map<string, { 
    fieldInnings: number; 
    benchInnings: number;
    consecutiveBench: number;
    maxConsecutiveBench: number;
  }>();
  
  // Initialize all players
  attendingPlayers.forEach(player => {
    playerStats.set(player.name, { 
      fieldInnings: 0, 
      benchInnings: 0,
      consecutiveBench: 0,
      maxConsecutiveBench: 0
    });
  });
  
  // Count innings for each player
  currentLineup?.innings.forEach((inning) => {
    // Field positions
    Object.entries(inning.positions).forEach(([position, playerName]) => {
      if (position !== 'bench' && playerName) {
        const stats = playerStats.get(playerName);
        if (stats) {
          stats.fieldInnings++;
          stats.consecutiveBench = 0;
        }
      }
    });
    
    // Bench
    inning.positions.bench.forEach(playerName => {
      const stats = playerStats.get(playerName);
      if (stats) {
        stats.benchInnings++;
        stats.consecutiveBench++;
        stats.maxConsecutiveBench = Math.max(
          stats.maxConsecutiveBench, 
          stats.consecutiveBench
        );
      }
    });
  });
  
  // Calculate balance score
  const fieldInnings = Array.from(playerStats.values())
    .map(stats => stats.fieldInnings);
  
  const min = Math.min(...fieldInnings);
  const max = Math.max(...fieldInnings);
  const range = max - min;
  
  let score = 100;
  
  // Apply penalties
  if (range > 2) {
    score -= (range - 2) * 10;
  }
  
  // Check for bench violations
  let violations = 0;
  playerStats.forEach((stats, playerName) => {
    if (stats.maxConsecutiveBench > rotationRules.maxConsecutiveBenchInnings) {
      violations++;
    }
  });
  
  score -= violations * 15;
  
  return Math.max(0, Math.round(score));
}, [attendingPlayers, currentLineup, rotationRules]);
```

### 2. Lineup Generation Process

The optimization uses `generateCompleteLineup` from `utils-enhanced.ts`:

```typescript
export function generateCompleteLineup(
  players: Player[],
  totalInnings: number,
  rotationRules: RotationRules,
  existingInnings: InningLineup[] = [],
  competitiveRatingScale?: CompetitiveRatingScale
): InningLineup[] {
  // Initialize constraint solver and rotator
  const solver = new ConstraintSolver(players, rotationRules);
  const rotator = new LineupRotator(players, rotationRules);
  
  // Step 1: Generate initial assignments
  let currentAssignments = solver.solve();
  
  if (!currentAssignments || Object.keys(currentAssignments).length === 0) {
    throw new Error("Failed to generate initial position assignments");
  }
  
  const innings: InningLineup[] = [];
  
  // Step 2: Generate innings with rotation
  for (let i = 0; i < totalInnings; i++) {
    // Apply rotation if needed
    if (i > 0 && rotator.shouldRotate(i)) {
      currentAssignments = rotator.rotate(currentAssignments, i);
    }
    
    // Create inning with current assignments
    const inning = createInningFromAssignments(currentAssignments, i + 1);
    innings.push(inning);
  }
  
  // Step 3: Apply fallback strategies if needed
  return applyFallbackStrategies(innings, players, rotationRules);
}
```

### 3. Constraint Solver

The `ConstraintSolver` class handles position assignment logic:

```typescript
class ConstraintSolver {
  constructor(
    private players: Player[], 
    private rules: RotationRules
  ) {}
  
  solve(): Record<string, string> {
    const assignments: Record<string, string> = {};
    const assignedPlayers = new Set<string>();
    const positions = this.getPositionOrder();
    
    // Try to assign each position
    for (const position of positions) {
      const eligiblePlayers = this.getEligiblePlayers(
        position, 
        assignedPlayers
      );
      
      if (eligiblePlayers.length > 0) {
        // Sort by role priority and playing time
        const bestPlayer = this.selectBestPlayer(
          eligiblePlayers, 
          position
        );
        
        assignments[position] = bestPlayer.name;
        assignedPlayers.add(bestPlayer.name);
      }
    }
    
    return assignments;
  }
  
  private getEligiblePlayers(
    position: string, 
    assigned: Set<string>
  ): Player[] {
    return this.players.filter(player => {
      if (assigned.has(player.name)) return false;
      
      const role = player.teamRoles?.[position];
      return role !== 'avoid' && role !== 'never';
    });
  }
}
```

### 4. Lineup Rotator

The `LineupRotator` manages position changes between innings:

```typescript
class LineupRotator {
  rotate(
    currentAssignments: Record<string, string>, 
    inningNumber: number
  ): Record<string, string> {
    // Check if rotation is needed
    if (!this.shouldRotate(inningNumber)) {
      return currentAssignments;
    }
    
    // Get players who have been on bench too long
    const forcedRotations = this.getPlayersNeedingRotation();
    
    // Apply smart rotation
    if (this.rules.competitiveMode) {
      return this.competitiveRotation(
        currentAssignments, 
        forcedRotations
      );
    } else {
      return this.fairPlayRotation(
        currentAssignments, 
        forcedRotations
      );
    }
  }
  
  private fairPlayRotation(
    assignments: Record<string, string>,
    forcedPlayers: string[]
  ): Record<string, string> {
    // Rotate players through positions
    const newAssignments = { ...assignments };
    const positionGroups = this.groupPositionsByType();
    
    // Rotate within position groups
    for (const group of positionGroups) {
      this.rotateWithinGroup(newAssignments, group);
    }
    
    // Ensure forced players get field time
    for (const playerName of forcedPlayers) {
      this.forcePlayerToField(newAssignments, playerName);
    }
    
    return newAssignments;
  }
}
```

### 5. Balance Optimizer

The `balance-optimizer.ts` module provides multi-game optimization:

```typescript
export function optimizeSeriesBalance(
  lineups: Lineup[],
  players: Player[]
): OptimizationResult {
  // Calculate current imbalances
  const playerStats = calculatePlayerStats(lineups, players);
  const targetInnings = calculateTargetInnings(lineups, players);
  
  // Identify over/under played players
  const overPlayers = findOverPlayedPlayers(playerStats, targetInnings);
  const underPlayers = findUnderPlayedPlayers(playerStats, targetInnings);
  
  // Perform strategic swaps
  const optimizedLineups = performSwaps(
    lineups,
    overPlayers,
    underPlayers,
    players
  );
  
  // Calculate new balance score
  const balanceScore = calculateBalanceScore(
    calculatePlayerStats(optimizedLineups, players)
  );
  
  return {
    optimizedLineups,
    balanceScore,
    changes: swapLog
  };
}
```

## Optimization Process Flow

### 1. Initial Analysis
```typescript
// ViewLineup.tsx - handleOptimizeLineup()
const handleOptimizeLineup = async () => {
  setIsOptimizing(true);
  
  try {
    // Get current balance score
    const currentBalance = calculateSingleGameBalance;
    
    if (currentBalance >= 95) {
      toast.info("Lineup is already well-balanced!");
      return;
    }
    
    // Generate optimized lineup
    const optimizedInnings = generateCompleteLineup(
      attendingPlayers,
      currentLineup.innings.length,
      rotationRules,
      currentLineup.innings // Pass existing innings as hint
    );
    
    // Update lineup
    await updateLineup(optimizedInnings);
    
  } catch (error) {
    console.error('Optimization failed:', error);
    toast.error("Failed to optimize lineup");
  } finally {
    setIsOptimizing(false);
  }
};
```

### 2. Position Assignment Priority

The algorithm uses this priority order:

1. **Primary/Go-To Players**: Get first priority for their positions
2. **In the Mix Players**: Fill remaining spots in their position groups
3. **Emergency/Fill-In Players**: Used when primary options unavailable
4. **Avoid/Never**: Excluded from these positions

### 3. Rotation Rules Enforcement

Key rules enforced during optimization:

- **rotateLineupEvery**: How often to change positions (1-3 innings)
- **rotatePitcherEvery**: Special rotation frequency for pitchers (2-4 innings)
- **maxConsecutiveBenchInnings**: Maximum bench time (2-3 innings)
- **competitiveMode**: Whether to prioritize winning vs equal time

### 4. Fallback Strategies

When the primary algorithm can't satisfy all constraints:

```typescript
function applyFallbackStrategies(
  innings: InningLineup[],
  players: Player[],
  rules: RotationRules
): InningLineup[] {
  // Strategy 1: Force rotation for bench violations
  innings = forceBenchRotations(innings, players, rules);
  
  // Strategy 2: Fill empty positions
  innings = fillEmptyPositions(innings, players);
  
  // Strategy 3: Balance playing time
  innings = rebalancePlayingTime(innings, players, rules);
  
  return innings;
}
```

## Common Scenarios

### Scenario 1: Player on Bench Too Long
```
Problem: Charlotte has been on bench for 4 consecutive innings
Solution: Force rotation to get Charlotte on field
- Find player with most field time
- Swap Charlotte with that player
- Respect position eligibility
```

### Scenario 2: Unequal Playing Time
```
Problem: Some players have 5 field innings, others have 2
Solution: Strategic swaps
- Identify over/under played players
- Find compatible position swaps
- Execute swaps to balance time
```

### Scenario 3: Position Conflicts
```
Problem: All catchers are already playing other positions
Solution: Emergency assignment
- Use emergency/fill-in players
- Temporarily reassign if needed
- Maintain minimum safety requirements
```

## Debugging and Diagnostics

The `LineupDiagnostics` component provides insights:

```typescript
<LineupDiagnostics 
  players={attendingPlayers}
  lineup={currentLineup.innings[inningIndex]}
  inningNumber={inningIndex + 1}
/>
```

This shows:
- Why players are on bench
- Position eligibility
- Rotation reasoning
- Balance metrics

## Performance Considerations

The algorithm uses several optimizations:

1. **Memoization**: Balance calculations are memoized to avoid recalculation
2. **Early Exit**: Stops optimization if already well-balanced (>95 score)
3. **Incremental Updates**: Tries to preserve existing good assignments
4. **Parallel Processing**: Multi-game optimization processes games concurrently

## Configuration via Team Philosophy

The Team Philosophy selector controls optimization behavior:

- **Equal Playing Time**: Aggressive rotation, everyone plays everywhere
- **Position Specialists**: Balanced approach, respects primary positions
- **Competitive Team**: Minimal rotation, optimize for winning

## Validation and Error Handling

The algorithm includes comprehensive validation:

```typescript
// Validate all positions filled
if (!validateAllPositionsFilled(innings)) {
  throw new Error("Failed to fill all positions");
}

// Validate rotation rules
if (!validateRotationRules(innings, rules)) {
  console.warn("Rotation rules not fully satisfied");
}

// Validate playing time balance
if (!validatePlayingTimeBalance(innings, players)) {
  console.warn("Playing time imbalance detected");
}
```

## Future Enhancements

Potential improvements to the algorithm:

1. **Machine Learning**: Learn from coach adjustments
2. **Historical Analysis**: Consider past game performance
3. **Weather/Field Conditions**: Adjust for environmental factors
4. **Player Preferences**: Honor player requests when possible
5. **Real-time Adjustments**: Live optimization during games
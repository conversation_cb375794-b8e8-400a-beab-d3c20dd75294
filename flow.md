# Dugout Boss - User Flow Documentation

## Overview

This document outlines the current user flows within the Dugout Boss application, a production-ready MVP for youth baseball and softball lineup management.

## Authentication Flows ✅

### New User Registration Flow

1. User visits the homepage with baseball imagery and clear value proposition
2. User clicks "Get Started" or "Sign Up" button
3. User enters email and password with real-time validation
4. System sends verification email via Supabase Auth
5. User clicks verification link and is redirected to login
6. User logs in with verified credentials
7. System checks payment status:
   - If paid, redirect to dashboard
   - If not paid, redirect to pricing page for subscription
8. User can access features based on payment status

### Demo Mode Flow

1. User visits the homepage
2. User clicks "Try Demo Mode" button (no registration required)
3. System automatically creates demo session with demo user credentials
4. <PERSON> redirects to dashboard with demo banner
5. User has full access to comprehensive demo data:
   - Demo Softball Team with 12 players
   - 3 complete saved lineups with different scenarios
   - Player position restrictions (pitcher, catcher, first base)
   - Configured rotation rules
6. User can create, edit, and delete lineups during session
7. All features work identically to paid experience
8. Demo session persists until user logs out

### Standard Login Flow

1. User visits the homepage
2. User clicks "Sign In" button
3. User enters email and password
4. System authenticates via Supabase Auth with JWT tokens
5. System checks payment status and redirects appropriately
6. User accesses dashboard with their teams and lineups

## Team Management Flow ✅

### Creating a Team

1. User navigates to dashboard
2. User clicks "Create Team" button
3. User enters team name with validation
4. System creates team and redirects to team roster page
5. Team is immediately available for lineup creation

### Managing Team Roster

1. User navigates to team roster page
2. User can add new players:
   - Enter player name (required)
   - Set position restrictions:
     - Pitcher restriction (cannot play pitcher)
     - Catcher restriction (cannot play catcher)
     - First base restriction (cannot play first base)
   - System validates and saves player
3. User can edit existing players:
   - Modify name and position restrictions
   - Changes apply to future lineups
4. User can delete players:
   - System confirms deletion
   - Player removed from all future lineups

### Setting Rotation Rules

1. User navigates to rotation rules page from dashboard
2. User configures team-specific rotation preferences:
   - **Equal playing time**: Ensures fair distribution of playing time
   - **Respect position lockouts**: Enforces player position restrictions
   - **Maximum consecutive bench time**: Limits how long players sit
   - **Rotation algorithm preferences**: Customizes rotation logic
3. System saves rotation rules for the team
4. Rules apply to all future lineup generation for the team

## Lineup Creation Flow ✅

### Creating a New Lineup

1. User navigates to dashboard
2. User clicks "Create New Lineup" button
3. User enters lineup details:
   - Lineup name (required)
   - Game date (date picker)
4. System validates input and creates lineup
5. System redirects to attendance page

### Setting Lineup Attendance

1. User views complete team roster on attendance page
2. User marks each player as present/absent with toggle switches
3. System records attendance and calculates available players
4. System shows count of present players
5. User proceeds to set first inning positions

### Setting First Inning Positions

1. User views position assignment interface:
   - 9 field positions (P, C, 1B, 2B, 3B, SS, LF, CF, RF)
   - Bench positions (Bench 1, Bench 2, etc.) based on present players
2. User assigns present players to positions via dropdown menus
3. System provides real-time validation:
   - Prevents duplicate assignments
   - Warns about position restriction violations
   - Ensures all present players are assigned
4. User confirms assignments
5. System generates remaining innings using rotation algorithm

### Setting Batting Order

1. User navigates to batting order page
2. User can choose from multiple options:
   - **Manual arrangement**: Drag and drop players in desired order
   - **Random generation**: System creates random batting order
   - **Custom optimization**: User manually orders all players
3. System validates batting order includes all present players
4. System saves batting order
5. User can view complete lineup with positions and batting order

## Lineup Management Flow ✅

### Viewing a Lineup

1. User navigates to dashboard
2. User selects a saved lineup from the list
3. System displays comprehensive lineup details:
   - Complete positions by inning (up to 7 innings)
   - Batting order with all present players
   - Attendance information
   - Game date and lineup name
4. User can export lineup as PDF or CSV
5. User can edit individual innings or batting order

### Editing an Inning

1. User views lineup and selects an inning to edit
2. User modifies player positions using dropdown menus
3. System provides real-time validation:
   - Prevents duplicate assignments
   - Warns about position restrictions
   - Ensures all positions are filled
4. System updates the lineup immediately
5. User can choose to regenerate subsequent innings based on rotation rules
6. Changes are automatically saved

### Deleting a Lineup

1. User views lineup
2. User clicks "Delete Lineup" button
3. System prompts for confirmation
4. System permanently removes lineup from database
5. User is redirected to dashboard

## Payment and Subscription Flow ✅

### Subscription Purchase

1. User visits pricing page
2. User selects subscription plan
3. User is redirected to Stripe checkout
4. User enters payment information securely
5. Stripe processes payment
6. System receives webhook confirmation
7. User subscription status is updated
8. User gains immediate access to all features

## Admin Flow 🚧

### Admin Dashboard (In Development)

1. Admin user logs in with admin privileges
2. System detects admin status
3. Admin navigates to admin dashboard
4. Admin can access:
   - User management interface
   - Billing and subscription management
   - Team overview and management
   - System analytics and settings

## Export Flow ✅

### PDF Export

1. User views a lineup
2. User clicks "Export as PDF" button
3. System generates professional PDF with:
   - Team name and game information
   - Complete lineup by inning
   - Batting order
   - Print-friendly formatting
4. System automatically downloads PDF to user's device

### CSV Export

1. User views a lineup
2. User clicks "Export as CSV" button
3. System generates CSV with structured lineup data
4. CSV includes all innings and batting order
5. System downloads CSV compatible with spreadsheet applications

## Error Handling Flow ✅

1. System detects an error (network, validation, etc.)
2. System displays user-friendly error message via toast notification
3. System provides recovery options when possible
4. System logs error details for troubleshooting
5. User can retry action or navigate to safe state

## Logout Flow ✅

1. User clicks "Logout" button from dashboard
2. System clears authentication tokens and session data
3. System redirects to homepage
4. If in demo mode, demo session is preserved for next user
5. User must log in again to access protected features

## Mobile Experience ✅

All flows are optimized for mobile devices with:
- Touch-friendly interface elements
- Responsive design for all screen sizes
- Mobile-optimized navigation
- Swipe gestures where appropriate
- Optimized form inputs for mobile keyboards

# Stripe Product Setup Guide for Three-Tier Pricing

## Step 1: Create Products in Stripe Dashboard

Go to your Stripe Dashboard → Products and create these three products:

### 1. Starter Plan
- **Product Name**: Dugout Boss Starter
- **Description**: Perfect for single team coaches - 1 team limit
- **Price**: $20.00
- **Billing Period**: Yearly
- **Price ID**: Will be generated (save this!)

### 2. Coach Plan
- **Product Name**: Dugout Boss Coach
- **Description**: Ideal for coaches managing multiple teams - 5 team limit
- **Price**: $30.00
- **Billing Period**: Yearly
- **Price ID**: Will be generated (save this!)

### 3. Club Plan
- **Product Name**: Dugout Boss Club
- **Description**: Perfect for clubs, leagues & organizations - unlimited teams, multi-coach access coming soon
- **Price**: $500.00
- **Billing Period**: Yearly
- **Price ID**: Will be generated (save this!)

## Step 2: Save Your Price IDs

After creating each product, <PERSON>e will generate price IDs that look like:
- `price_1ABC123...` 

Save these! We'll need them for the code.

## Step 3: Configure Webhook Events

Make sure your webhook is subscribed to these events:
- `checkout.session.completed`
- `customer.subscription.created`
- `customer.subscription.updated`
- `customer.subscription.deleted`

## Your Price Configuration

Once you have the price IDs, I'll update the code with them. They'll look something like:

```javascript
const PRICE_IDS = {
  starter: 'price_xxx', // Your Starter price ID
  coach: 'price_yyy',   // Your Coach price ID  
  club: 'price_zzz'     // Your Club price ID
}
```

## Team Limits by Tier

- **Starter**: 1 team max
- **Coach**: 5 teams max
- **Club**: Unlimited teams (999 in database)

Ready to create these in Stripe?
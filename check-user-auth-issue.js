import { createServiceClient } from "./src/integrations/supabase/node-client.js";

const supabase = createServiceClient();

async function checkUserAuthIssue(email) {
  console.log(`\n🔍 Checking auth issue for: ${email}\n`);

  try {
    // 1. Check auth.users
    console.log('1. Checking auth.users table...');
    const { data: authUser, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.error('Error checking auth users:', authError);
      return;
    }

    const user = authUser.users.find(u => u.email === email);
    
    if (user) {
      console.log('✅ User found in auth.users');
      console.log('   User ID:', user.id);
      console.log('   Created:', new Date(user.created_at).toLocaleString());
      console.log('   Last Sign In:', user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'Never');
    } else {
      console.log('❌ User NOT found in auth.users');
      console.log('   This user needs to be created first');
      return;
    }

    // 2. Check profiles table
    console.log('\n2. Checking profiles table...');
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (profileError && profileError.code !== 'PGRST116') {
      console.error('Error checking profile:', profileError);
    }

    if (profile) {
      console.log('✅ Profile found');
      console.log('   Email:', profile.email);
      console.log('   Is Admin:', profile.is_admin);
      console.log('   Created:', new Date(profile.created_at).toLocaleString());
    } else {
      console.log('❌ Profile NOT found - THIS IS THE ISSUE!');
      console.log('   The user exists in auth but has no profile record');
      console.log('\n🔧 SOLUTION: Create the missing profile');
      
      // Offer to create the profile
      console.log('\nCreating missing profile...');
      
      const { data: newProfile, error: createError } = await supabase
        .from('profiles')
        .insert({
          id: user.id,
          email: user.email,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (createError) {
        console.error('❌ Error creating profile:', createError);
      } else {
        console.log('✅ Profile created successfully!');
      }
    }

    // 3. Check subscriptions
    console.log('\n3. Checking subscriptions table...');
    const { data: subscription, error: subError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(1);

    if (subError) {
      console.error('Error checking subscription:', subError);
    }

    if (subscription && subscription.length > 0) {
      const sub = subscription[0];
      console.log('✅ Subscription found');
      console.log('   Is Paid:', sub.is_paid);
      console.log('   Tier:', sub.tier || '❌ MISSING');
      console.log('   Team Limit:', sub.team_limit || '❌ MISSING');
      console.log('   Created:', new Date(sub.created_at).toLocaleString());
      
      if (sub.is_paid && (!sub.tier || !sub.team_limit)) {
        console.log('\n⚠️  ISSUE: Paid subscription missing tier/team_limit!');
        console.log('🔧 SOLUTION: Update subscription with missing fields');
        
        const { error: updateError } = await supabase
          .from('subscriptions')
          .update({
            tier: sub.tier || 'pro',
            team_limit: sub.team_limit || 10,
            updated_at: new Date().toISOString()
          })
          .eq('id', sub.id);

        if (updateError) {
          console.error('❌ Error updating subscription:', updateError);
        } else {
          console.log('✅ Subscription updated successfully!');
        }
      }
    } else {
      console.log('❌ No subscription found');
      if (profile || user) {
        console.log('\n🔧 SOLUTION: Create a subscription for this user');
        
        const { data: newSub, error: createSubError } = await supabase
          .from('subscriptions')
          .insert({
            user_id: user.id,
            is_paid: true,
            tier: 'pro',
            team_limit: 10,
            currency: 'usd',
            amount: 0,
            payment_date: new Date().toISOString(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (createSubError) {
          console.error('❌ Error creating subscription:', createSubError);
        } else {
          console.log('✅ Subscription created successfully!');
        }
      }
    }

    console.log('\n📊 SUMMARY:');
    console.log('- Auth User:', user ? '✅' : '❌');
    console.log('- Profile:', profile ? '✅' : '❌ NEEDS CREATION');
    console.log('- Subscription:', subscription?.length > 0 ? '✅' : '❌ NEEDS CREATION');
    console.log('\n✨ After running this script, have the user sign out and sign back in.');

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the check
const email = process.argv[2];
if (!email) {
  console.error('Please provide an email address');
  console.log('Usage: node check-user-auth-issue.js <EMAIL>');
  process.exit(1);
}

checkUserAuthIssue(email);
-- EMERGENCY FIX: Disable R<PERSON> to fix data isolation issue
-- RUN THIS IMMEDIATELY IN SUPABASE SQL EDITOR

-- 1. Disable RLS on all tables (temporary fix)
-- Only disable on tables that exist
DO $$ 
BEGIN
    -- Core tables
    IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'profiles') THEN
        ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'subscriptions') THEN
        ALTER TABLE public.subscriptions DISABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'teams') THEN
        ALTER TABLE public.teams DISABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'players') THEN
        ALTER TABLE public.players DISABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'lineups') THEN
        ALTER TABLE public.lineups DISABLE ROW LEVEL SECURITY;
    END IF;
    
    -- Additional tables (may not exist)
    IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'innings') THEN
        ALTER TABLE public.innings DISABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'lineup_players') THEN
        ALTER TABLE public.lineup_players DISABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'lineup_attendance') THEN
        ALTER TABLE public.lineup_attendance DISABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'rotation_rules') THEN
        ALTER TABLE public.rotation_rules DISABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'player_position_history') THEN
        ALTER TABLE public.player_position_history DISABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'contact_messages') THEN
        ALTER TABLE public.contact_messages DISABLE ROW LEVEL SECURITY;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'admin_audit_logs') THEN
        ALTER TABLE public.admin_audit_logs DISABLE ROW LEVEL SECURITY;
    END IF;
END $$;

-- 2. Verify RLS is disabled
SELECT 
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY tablename;

-- 3. <NAME_EMAIL> has their own data
SELECT 
    u.email,
    u.id as user_id,
    COUNT(DISTINCT t.id) as team_count,
    COUNT(DISTINCT p.id) as player_count,
    COUNT(DISTINCT l.id) as lineup_count
FROM auth.users u
LEFT JOIN public.teams t ON t.user_id = u.id
LEFT JOIN public.players p ON p.team_id = t.id
LEFT JOIN public.lineups l ON l.team_id = t.id
WHERE u.email IN ('<EMAIL>', '<EMAIL>')
GROUP BY u.email, u.id
ORDER BY u.email;
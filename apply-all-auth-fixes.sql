-- Comprehensive fix for all RLS policies to support both regular users and admin operations
-- This fixes lineup_innings, lineups, lineup_attendance, rotation_rules, and players tables

-- Fix lineup_innings policies
DROP POLICY IF EXISTS "Users can view their own lineup innings" ON public.lineup_innings;
DROP POLICY IF EXISTS "Users can insert their own lineup innings" ON public.lineup_innings;
DROP POLICY IF EXISTS "Users can update their own lineup innings" ON public.lineup_innings;
DROP POLICY IF EXISTS "Users can delete their own lineup innings" ON public.lineup_innings;
DROP POLICY IF EXISTS "Allow admins to read all lineup innings" ON public.lineup_innings;
DROP POLICY IF EXISTS "Allow admins to update all lineup innings" ON public.lineup_innings;
DROP POLICY IF EXISTS "Allow admins to insert all lineup innings" ON public.lineup_innings;
DROP POLICY IF EXISTS "Allow admins to delete all lineup innings" ON public.lineup_innings;

CREATE POLICY "lineup_innings_select_policy" ON public.lineup_innings
  FOR SELECT USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "lineup_innings_insert_policy" ON public.lineup_innings
  FOR INSERT WITH CHECK (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "lineup_innings_update_policy" ON public.lineup_innings
  FOR UPDATE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "lineup_innings_delete_policy" ON public.lineup_innings
  FOR DELETE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

-- Fix lineups policies
DROP POLICY IF EXISTS "Users can view their own lineups" ON public.lineups;
DROP POLICY IF EXISTS "Users can insert their own lineups" ON public.lineups;
DROP POLICY IF EXISTS "Users can update their own lineups" ON public.lineups;
DROP POLICY IF EXISTS "Users can delete their own lineups" ON public.lineups;
DROP POLICY IF EXISTS "Allow admins to read all lineups" ON public.lineups;
DROP POLICY IF EXISTS "Allow admins to update all lineups" ON public.lineups;
DROP POLICY IF EXISTS "Allow admins to insert all lineups" ON public.lineups;
DROP POLICY IF EXISTS "Allow admins to delete all lineups" ON public.lineups;

CREATE POLICY "lineups_select_policy" ON public.lineups
  FOR SELECT USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "lineups_insert_policy" ON public.lineups
  FOR INSERT WITH CHECK (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "lineups_update_policy" ON public.lineups
  FOR UPDATE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "lineups_delete_policy" ON public.lineups
  FOR DELETE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

-- Fix lineup_attendance policies
DROP POLICY IF EXISTS "Users can view their own lineup attendance" ON public.lineup_attendance;
DROP POLICY IF EXISTS "Users can insert their own lineup attendance" ON public.lineup_attendance;
DROP POLICY IF EXISTS "Users can update their own lineup attendance" ON public.lineup_attendance;
DROP POLICY IF EXISTS "Users can delete their own lineup attendance" ON public.lineup_attendance;

CREATE POLICY "lineup_attendance_select_policy" ON public.lineup_attendance
  FOR SELECT USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "lineup_attendance_insert_policy" ON public.lineup_attendance
  FOR INSERT WITH CHECK (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "lineup_attendance_update_policy" ON public.lineup_attendance
  FOR UPDATE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "lineup_attendance_delete_policy" ON public.lineup_attendance
  FOR DELETE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

-- Fix rotation_rules policies
DROP POLICY IF EXISTS "Users can view their own rotation rules" ON public.rotation_rules;
DROP POLICY IF EXISTS "Users can insert their own rotation rules" ON public.rotation_rules;
DROP POLICY IF EXISTS "Users can update their own rotation rules" ON public.rotation_rules;
DROP POLICY IF EXISTS "Users can delete their own rotation rules" ON public.rotation_rules;

CREATE POLICY "rotation_rules_select_policy" ON public.rotation_rules
  FOR SELECT USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "rotation_rules_insert_policy" ON public.rotation_rules
  FOR INSERT WITH CHECK (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "rotation_rules_update_policy" ON public.rotation_rules
  FOR UPDATE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "rotation_rules_delete_policy" ON public.rotation_rules
  FOR DELETE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

-- Fix players table policies
DROP POLICY IF EXISTS "Users can view their own players" ON public.players;
DROP POLICY IF EXISTS "Users can insert their own players" ON public.players;
DROP POLICY IF EXISTS "Users can update their own players" ON public.players;
DROP POLICY IF EXISTS "Users can delete their own players" ON public.players;
DROP POLICY IF EXISTS "Allow admins to read all players" ON public.players;
DROP POLICY IF EXISTS "Allow admins to update all players" ON public.players;
DROP POLICY IF EXISTS "Allow admins to insert all players" ON public.players;
DROP POLICY IF EXISTS "Allow admins to delete all players" ON public.players;

CREATE POLICY "players_select_policy" ON public.players
  FOR SELECT USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "players_insert_policy" ON public.players
  FOR INSERT WITH CHECK (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "players_update_policy" ON public.players
  FOR UPDATE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );

CREATE POLICY "players_delete_policy" ON public.players
  FOR DELETE USING (
    auth.uid() = user_id 
    OR auth.email() = '<EMAIL>' 
    OR auth.email() = '<EMAIL>'
  );
#!/usr/bin/env node

/**
 * Standalone Algorithm Test
 * 
 * This test extracts and tests the core lineup generation logic
 * without any external dependencies.
 */

// Core types and interfaces
class LineupGenerationError extends Error {
  constructor(message, code, context) {
    super(message);
    this.code = code;
    this.context = context;
  }
}

class InsufficientPlayersError extends LineupGenerationError {
  constructor(required, available) {
    super(
      `Not enough players to create a lineup. Need at least ${required} players, but only ${available} available.`,
      'INSUFFICIENT_PLAYERS',
      { availablePlayers: [] }
    );
  }
}

// Seeded random number generator
class SeededRandom {
  constructor(seed = Date.now()) {
    this.seed = seed;
  }
  
  next() {
    this.seed = (this.seed * 1664525 + 1013904223) % (2**32);
    return this.seed / (2**32);
  }
  
  nextInt(max) {
    return Math.floor(this.next() * max);
  }
  
  shuffle(array) {
    const result = [...array];
    for (let i = result.length - 1; i > 0; i--) {
      const j = this.nextInt(i + 1);
      [result[i], result[j]] = [result[j], result[i]];
    }
    return result;
  }
}

// Position configuration
const POSITION_CONFIG = {
  pitcher: { key: 'pitcher', display: 'Pitcher', abbrev: 'P' },
  catcher: { key: 'catcher', display: 'Catcher', abbrev: 'C' },
  firstBase: { key: 'firstBase', display: 'First Base', abbrev: '1B' },
  secondBase: { key: 'secondBase', display: 'Second Base', abbrev: '2B' },
  shortstop: { key: 'shortstop', display: 'Shortstop', abbrev: 'SS' },
  thirdBase: { key: 'thirdBase', display: 'Third Base', abbrev: '3B' },
  leftField: { key: 'leftField', display: 'Left Field', abbrev: 'LF' },
  centerField: { key: 'centerField', display: 'Center Field', abbrev: 'CF' },
  rightField: { key: 'rightField', display: 'Right Field', abbrev: 'RF' }
};

// Helper function to check if player can play position (matches utils-enhanced.ts)
function canPlayPosition(player, position) {
  if (!player || !player.positionRestrictions) {
    return true;
  }

  // Check position restrictions - true means restricted FROM that position
  if (position === "Pitcher" && player.positionRestrictions.pitcher) return false;
  if (position === "Catcher" && player.positionRestrictions.catcher) return false;
  if (position === "First Base" && player.positionRestrictions.firstBase) return false;

  // Check "other" restriction
  if (player.positionRestrictions.other) {
    if (player.positionRestrictions.other === position) return false;

    // Handle combined position restrictions
    if (player.positionRestrictions.other === "Middle Infield" &&
        (position === "Shortstop" || position === "Second Base")) {
      return false;
    }

    if (player.positionRestrictions.other === "3B/MI/SS/2B" &&
        (position === "Third Base" || position === "Shortstop" ||
         position === "Second Base" || position === "Middle Infield")) {
      return false;
    }
  }

  return true;
}

// Player eligibility cache
class PlayerEligibilityCache {
  constructor() {
    this.cache = new Map();
    this.competitiveCache = new Map();
  }

  getEligiblePositions(player, competitiveMode = false) {
    const cacheKey = player.id;
    const targetCache = competitiveMode ? this.competitiveCache : this.cache;

    if (!targetCache.has(cacheKey)) {
      const eligible = new Set();

      Object.values(POSITION_CONFIG).forEach(pos => {
        if (canPlayPosition(player, pos.display)) {
          eligible.add(pos.key);
        }
      });

      targetCache.set(cacheKey, eligible);
    }
    return targetCache.get(cacheKey);
  }
}

// Constraint solver
class ConstraintSolver {
  constructor(players, eligibilityCache) {
    this.players = players;
    this.eligibilityCache = eligibilityCache;
  }
  
  findValidAssignment(positions, constraints, assignments = new Map(), timeout = 10000) {
    const startTime = Date.now();
    let attempts = 0;

    const backtrack = (remainingPositions, currentAssignments) => {
      attempts++;

      // Check timeout
      if (Date.now() - startTime > timeout) {
        console.warn(`⏰ Constraint solver timeout after ${attempts} attempts`);
        return null;
      }

      // Base case: all positions assigned
      if (remainingPositions.length === 0) {
        return currentAssignments;
      }

      const [currentPos, ...restPositions] = remainingPositions;

      // Get available players (not yet assigned)
      const availablePlayers = this.players.filter(p =>
        !Array.from(currentAssignments.values()).includes(p.name)
      );

      // Check eligibility for each player
      const eligiblePlayers = availablePlayers.filter(player => {
        if (!constraints.respectPositionLockouts) {
          return true;
        }
        return this.eligibilityCache.getEligiblePositions(player).has(currentPos);
      });

      if (eligiblePlayers.length === 0) {
        return null;
      }

      // Try each eligible player for this position
      for (const player of eligiblePlayers) {
        // Make assignment
        currentAssignments.set(currentPos, player.name);

        // Recurse
        const result = backtrack(restPositions, currentAssignments);
        if (result) {
          return result;
        }

        // Backtrack
        currentAssignments.delete(currentPos);
      }

      return null;
    };

    return backtrack(positions, assignments);
  }
}

// Simple lineup generation function
function generateCompleteLineup(availablePlayers, totalInnings, rules = {}) {
  console.log(`🚀 GENERATING COMPLETE LINEUP - ${totalInnings} innings for ${availablePlayers.length} players`);

  if (availablePlayers.length < 8) {
    throw new InsufficientPlayersError(8, availablePlayers.length);
  }

  // Default rules
  const defaultRules = {
    limitBenchTime: true,
    maxConsecutiveBenchInnings: 2,
    allowPitcherRotation: true,
    allowCatcherRotation: true,
    respectPositionLockouts: true,
    equalPlayingTime: true,
    rotateLineupEvery: 1,
    rotatePitcherEvery: 2,
    competitiveMode: false,
    _randomSeed: 12345
  };
  
  const finalRules = { ...defaultRules, ...rules };
  
  const random = new SeededRandom(finalRules._randomSeed);
  const eligibilityCache = new PlayerEligibilityCache();
  const solver = new ConstraintSolver(availablePlayers, eligibilityCache);

  const lineup = [];
  
  // Generate each inning
  for (let inning = 1; inning <= totalInnings; inning++) {
    console.log(`📋 Generating inning ${inning}`);
    
    // Determine positions to fill
    const fieldPositions = availablePlayers.length >= 9 ? 
      ['pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'] :
      ['pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop', 'thirdBase', 'leftField', 'centerField'];
    
    // Try to assign positions
    const assignments = solver.findValidAssignment(fieldPositions, { respectPositionLockouts: finalRules.respectPositionLockouts });
    
    if (!assignments) {
      console.warn(`⚠️ Could not find valid assignment for inning ${inning}, using fallback`);
      // Fallback: assign players without restrictions
      const fallbackAssignments = new Map();
      const shuffledPlayers = random.shuffle([...availablePlayers]);
      fieldPositions.forEach((pos, index) => {
        if (index < shuffledPlayers.length) {
          fallbackAssignments.set(pos, shuffledPlayers[index].name);
        }
      });
      assignments = fallbackAssignments;
    }
    
    // Create inning lineup
    const positions = {};
    assignments.forEach((playerName, position) => {
      positions[position] = playerName;
    });
    
    // Add bench players
    const assignedPlayers = new Set(assignments.values());
    const benchPlayers = availablePlayers
      .filter(p => !assignedPlayers.has(p.name))
      .map(p => p.name);
    
    if (benchPlayers.length > 0) {
      positions.bench = benchPlayers;
    }
    
    lineup.push({
      inning: inning,
      positions: positions
    });
  }

  console.log(`✅ Successfully generated ${lineup.length} innings`);
  return lineup;
}

// Test data - using correct Player interface format
const NOAH_SELECTS_PLAYERS = [
  {
    id: '1', name: 'Alex Thompson', jerseyNumber: 1, attendance: 'present',
    positionRestrictions: { pitcher: false, catcher: true, firstBase: true, other: null }, // Can ONLY play pitcher
    positionRatings: { pitcher: 5, firstBase: 3, leftField: 2 },
    starPositions: ['pitcher']
  },
  {
    id: '2', name: 'Ben Rodriguez', jerseyNumber: 2, attendance: 'present',
    positionRestrictions: { pitcher: true, catcher: false, firstBase: true, other: null }, // Can ONLY play catcher
    positionRatings: { catcher: 5, thirdBase: 4, rightField: 3 },
    starPositions: ['catcher']
  },
  {
    id: '3', name: 'Charlie Kim', jerseyNumber: 3, attendance: 'present',
    positionRestrictions: { pitcher: false, catcher: false, firstBase: false, other: null }, // No restrictions
    positionRatings: { shortstop: 5, secondBase: 4, centerField: 3 },
    starPositions: ['shortstop']
  },
  {
    id: '4', name: 'David Chen', jerseyNumber: 4, attendance: 'present',
    positionRestrictions: { pitcher: false, catcher: false, firstBase: false, other: null },
    positionRatings: { firstBase: 4, thirdBase: 3, leftField: 3 },
    starPositions: []
  },
  {
    id: '5', name: 'Emma Wilson', jerseyNumber: 5, attendance: 'present',
    positionRestrictions: { pitcher: false, catcher: false, firstBase: false, other: null },
    positionRatings: { secondBase: 4, shortstop: 3, rightField: 3 },
    starPositions: []
  },
  {
    id: '6', name: 'Frank Miller', jerseyNumber: 6, attendance: 'present',
    positionRestrictions: { pitcher: false, catcher: false, firstBase: false, other: null },
    positionRatings: { thirdBase: 4, firstBase: 3, centerField: 2 },
    starPositions: []
  },
  {
    id: '7', name: 'Grace Lee', jerseyNumber: 7, attendance: 'present',
    positionRestrictions: { pitcher: false, catcher: false, firstBase: false, other: null },
    positionRatings: { leftField: 4, centerField: 4, rightField: 3 },
    starPositions: []
  },
  {
    id: '8', name: 'Henry Davis', jerseyNumber: 8, attendance: 'present',
    positionRestrictions: { pitcher: false, catcher: false, firstBase: false, other: null },
    positionRatings: { centerField: 5, leftField: 4, rightField: 4 },
    starPositions: ['centerField']
  },
  {
    id: '9', name: 'Ivy Johnson', jerseyNumber: 9, attendance: 'present',
    positionRestrictions: { pitcher: false, catcher: false, firstBase: false, other: null },
    positionRatings: { rightField: 4, centerField: 3, firstBase: 2 },
    starPositions: []
  }
];

const HOUSE_LEAGUE_PLAYERS = Array.from({ length: 12 }, (_, i) => ({
  id: `hl${i + 1}`,
  name: `Player ${i + 1}`,
  jerseyNumber: i + 1,
  attendance: 'present',
  positionRestrictions: { pitcher: false, catcher: false, firstBase: false, other: null }, // No restrictions
  positionRatings: {},
  starPositions: []
}));

// Test suite
class LineupTestSuite {
  constructor() {
    this.passedTests = 0;
    this.failedTests = 0;
    this.testResults = [];
  }

  assert(condition, message, details = {}) {
    if (!condition) {
      throw new Error(`Assertion failed: ${message}. Details: ${JSON.stringify(details)}`);
    }
  }

  async runTest(testName, testFunction) {
    console.log(`\n🧪 Running test: ${testName}`);
    try {
      await testFunction();
      console.log(`✅ PASS: ${testName}`);
      this.passedTests++;
      this.testResults.push({ name: testName, status: 'PASS' });
    } catch (error) {
      console.log(`❌ FAIL: ${testName}`);
      console.log(`🔍 Error: ${error.message}`);
      this.failedTests++;
      this.testResults.push({ name: testName, status: 'FAIL', error: error.message });
    }
  }

  async testMinimumPlayers() {
    const players = HOUSE_LEAGUE_PLAYERS.slice(0, 8);
    const innings = 5;
    
    const lineup = generateCompleteLineup(players, innings, { equalPlayingTime: true });
    
    this.assert(lineup.length === innings, `Should generate ${innings} innings`, { actualInnings: lineup.length });
    this.assert(lineup[0].inning === 1, 'First inning should be numbered 1');
    this.assert(lineup[innings - 1].inning === innings, `Last inning should be numbered ${innings}`);
    
    // Validate each inning has correct number of field positions
    lineup.forEach((inning, index) => {
      const fieldPositions = Object.keys(inning.positions).filter(pos => pos !== 'bench');
      this.assert(fieldPositions.length === 8, `Inning ${index + 1} should have 8 field positions with 8 players`, { actualPositions: fieldPositions.length });
    });
  }

  async testManyPlayers() {
    const players = HOUSE_LEAGUE_PLAYERS.slice(0, 12);
    const innings = 6;
    
    const lineup = generateCompleteLineup(players, innings, { equalPlayingTime: true });
    
    this.assert(lineup.length === innings, `Should generate ${innings} innings`);
    
    // Each inning should have 9 field positions + 3 bench
    lineup.forEach((inning, index) => {
      const fieldPositions = Object.keys(inning.positions).filter(pos => pos !== 'bench');
      const benchPlayers = inning.positions.bench || [];
      this.assert(fieldPositions.length === 9, `Inning ${index + 1} should have 9 field positions`);
      this.assert(benchPlayers.length === 3, `Inning ${index + 1} should have 3 bench players`);
    });
  }

  async testPositionRestrictions() {
    const lineup = generateCompleteLineup(NOAH_SELECTS_PLAYERS, 5, { respectPositionLockouts: true });
    
    lineup.forEach((inning, index) => {
      // Alex Thompson should only be at pitcher (restricted)
      const alexPosition = Object.entries(inning.positions).find(([pos, player]) => 
        player === 'Alex Thompson' && pos !== 'bench'
      );
      if (alexPosition) {
        this.assert(alexPosition[0] === 'pitcher', `Alex Thompson should only play pitcher, found at ${alexPosition[0]} in inning ${index + 1}`);
      }
      
      // Ben Rodriguez should only be at catcher (restricted)
      const benPosition = Object.entries(inning.positions).find(([pos, player]) => 
        player === 'Ben Rodriguez' && pos !== 'bench'
      );
      if (benPosition) {
        this.assert(benPosition[0] === 'catcher', `Ben Rodriguez should only play catcher, found at ${benPosition[0]} in inning ${index + 1}`);
      }
    });
  }

  async runAllTests() {
    console.log('🚀 STANDALONE LINEUP GENERATION TEST SUITE');
    console.log('='.repeat(60));
    
    await this.runTest('Minimum Players (8)', () => this.testMinimumPlayers());
    await this.runTest('Many Players (12)', () => this.testManyPlayers());
    await this.runTest('Position Restrictions', () => this.testPositionRestrictions());
    
    // Print summary
    console.log('\n' + '='.repeat(60));
    console.log('📋 TEST SUITE SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total Tests: ${this.passedTests + this.failedTests}`);
    console.log(`✅ Passed: ${this.passedTests}`);
    console.log(`❌ Failed: ${this.failedTests}`);
    console.log(`📊 Success Rate: ${((this.passedTests / (this.passedTests + this.failedTests)) * 100).toFixed(1)}%`);
    
    if (this.failedTests > 0) {
      console.log('\n❌ FAILED TESTS:');
      this.testResults.filter(r => r.status === 'FAIL').forEach(result => {
        console.log(`❌   - ${result.name}: ${result.error}`);
      });
    }
    
    return this.failedTests === 0;
  }
}

// Run the tests
async function main() {
  const testSuite = new LineupTestSuite();
  const success = await testSuite.runAllTests();
  process.exit(success ? 0 : 1);
}

main().catch(error => {
  console.error('Test suite failed to run:', error);
  process.exit(1);
});

// Script to list all tables in Supabase
import { supabase } from "./src/integrations/supabase/node-client.js";

// Initialize dotenv

async function listSupabaseTables() {
  console.log('Listing Supabase tables...');
  
      
    
  
  
  try {
    // Query the information schema to get table names
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public');

    if (error) {
      console.error('Error fetching tables:', error);
      
      // Try an alternative approach
      console.log('Trying alternative approach to list tables...');
      
      // Try to query some known tables to see what's available
      const tables = ['teams', 'players', 'lineups', 'lineup_innings', 'lineup_attendance', 'batting_orders', 'rotation_rules', 'profiles', 'users'];
      
      for (const table of tables) {
        const { data: tableData, error: tableError } = await supabase
          .from(table)
          .select('count')
          .limit(0);
          
        if (!tableError) {
          console.log(`✅ Table exists: ${table}`);
        } else {
          console.log(`❌ Table does not exist or not accessible: ${table} (${tableError.message})`);
        }
      }
      
      return;
    }

    console.log('Available tables in public schema:');
    data.forEach(table => {
      console.log(`- ${table.table_name}`);
    });
    
  } catch (err) {
    console.error('Error listing tables:', err);
  }
}

listSupabaseTables();
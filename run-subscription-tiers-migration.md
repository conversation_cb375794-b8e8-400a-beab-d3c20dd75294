# Running the Subscription Tiers Migration

This migration adds subscription tier support (starter, coach, club) to Dugout Boss.

## Option 1: Check Migration Status (Recommended First Step)

Run this command to check if the migration is needed and get instructions:

```bash
node apply-subscription-tiers-migration.js
```

This script will:
- Check if the migration has already been applied
- Provide the exact SQL to run if needed
- Give you step-by-step instructions

## Option 2: Manual Migration via Supabase Dashboard

1. Go to your Supabase Dashboard: https://mhuuptkgohuztjrovpxz.supabase.co
2. Navigate to **SQL Editor** in the left sidebar
3. Click **New query**
4. Copy the contents of `supabase/migrations/add_subscription_tiers.sql`
5. Paste it into the SQL editor
6. Click **Run**

## Option 3: Using Supabase CLI (If Installed)

```bash
# Install Supabase CLI if you haven't already
npm install -g supabase

# Login to Supabase
supabase login

# Link to your project
supabase link --project-ref mhuuptkgohuztjrovpxz

# Run the migration
supabase db push
```

## Option 4: Using Service Role Key (Advanced)

If you have your Supabase service role key:

```bash
# Add to .env.local (never commit this!)
echo "SUPABASE_SERVICE_ROLE_KEY=your-service-role-key" >> .env.local

# Run the service role migration script
node apply-subscription-tiers-migration-service.js
```

## What This Migration Does

- Adds subscription tier columns to the `subscriptions` table
- Creates three tiers: `starter`, `coach`, and `club`
- Adds team limits per tier
- Grandfathers existing paid users to the `club` tier
- Creates functions to check and enforce team limits
- Adds a helpful view for checking subscription status

## Verifying the Migration

After running the migration, you can verify it worked by:

1. Running `node apply-subscription-tiers-migration.js` again
2. Checking the Supabase Dashboard → Table Editor → subscriptions table
3. Looking for the new columns: tier, team_limit, expires_at, etc.

## Troubleshooting

- **"column already exists" errors**: The migration has already been partially applied
- **Permission errors**: Make sure you're using the correct Supabase project
- **Function errors**: Some parts may need to be run separately in the SQL editor
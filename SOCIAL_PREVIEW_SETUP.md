# Social Preview Setup Instructions

To remove the Lovable branding and add your own preview images, you need to add the following files to your `public/` directory:

## Required Files:

### 1. **og-image.png** (Open Graph preview image)
- **Size**: 1200 x 630 pixels (recommended)
- **Format**: PNG
- **Purpose**: This is what shows when someone shares your link on Facebook, LinkedIn, Twitter, etc.
- **Design tips**: 
  - Include your logo/brand
  - Add text like "Dugout Boss - Baseball & Softball Lineup Manager"
  - Use your brand colors
  - Keep text large and readable

### 2. **favicon.ico** (Browser tab icon)
- **Size**: 16x16, 32x32, or multi-resolution
- **Format**: ICO
- **Purpose**: Shows in browser tabs
- You already have one, but you may want to update it with your logo

### 3. **apple-touch-icon.png** (Optional but recommended)
- **Size**: 180x180 pixels
- **Format**: PNG
- **Purpose**: Icon when someone saves your site to their iPhone home screen

## How to Add These Files:

1. Create your images with the specifications above
2. Place them in the `/public/` directory:
   ```
   /public/
     ├── favicon.ico (update existing)
     ├── og-image.png (new)
     └── apple-touch-icon.png (new)
   ```

3. Deploy your changes

## Testing Your Preview:

After deploying, test your social preview using these tools:
- Facebook: https://developers.facebook.com/tools/debug/
- Twitter: https://cards-dev.twitter.com/validator
- LinkedIn: https://www.linkedin.com/post-inspector/

## Current Setup:

I've already updated your `index.html` to:
- Remove Lovable's preview image
- Point to `/og-image.png` for social previews
- Add proper meta tags for better SEO
- Link to favicon and apple touch icon

Once you add the image files, your site will show your custom preview instead of Lovable's!
import { createServiceClient } from "./src/integrations/supabase/node-client.js";

const supabase = createServiceClient();

async function deleteOrphanedUser(email) {
  console.log(`\nLooking for user with email: ${email}`);
  
  // Protected accounts that should never be deleted
  const PROTECTED_EMAILS = ['<EMAIL>', '<EMAIL>'];
  
  if (PROTECTED_EMAILS.includes(email.toLowerCase())) {
    console.log('❌ This is a PROTECTED account and cannot be deleted!');
    return;
  }
  
  try {
    // Find user in auth
    const { data: authData, error: listError } = await supabase.auth.admin.listUsers();
    
    if (listError) {
      console.error('Error listing users:', listError);
      return;
    }

    const authUser = authData.users.find(u => u.email === email);
    
    if (!authUser) {
      console.log('❌ User not found in auth system');
      return;
    }

    console.log(`✓ Found user: ${authUser.email} (ID: ${authUser.id})`);

    // Check if they have a profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, is_protected')
      .eq('id', authUser.id)
      .single();

    if (profile && !profileError) {
      if (profile.is_protected) {
        console.log('❌ This user is marked as PROTECTED in the database and cannot be deleted!');
        return;
      }
      console.log('⚠️  User has a profile record. Use the admin panel to delete properly.');
      return;
    }

    // Confirm deletion
    console.log('\n⚠️  This user is orphaned (no profile record).');
    console.log('They cannot sign in and are blocking email reuse.');
    
    const readline = await import('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    rl.question('\nDelete this user from auth? (yes/no): ', async (answer) => {
      if (answer.toLowerCase() === 'yes') {
        console.log('\nDeleting user from auth...');
        
        const { error: deleteError } = await supabase.auth.admin.deleteUser(authUser.id);
        
        if (deleteError) {
          console.error('❌ Error deleting user:', deleteError);
        } else {
          console.log('✅ User deleted successfully!');
          console.log(`You can now create a new user with email: ${email}`);
        }
      } else {
        console.log('Deletion cancelled.');
      }
      
      rl.close();
      process.exit(0);
    });

  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

// Get email from command line
const email = process.argv[2];

if (!email) {
  console.log('Usage: node delete-orphaned-user.js <email>');
  console.log('Example: node delete-orphaned-user.js <EMAIL>');
  process.exit(1);
}

// Add service role key instructions
console.log('\nIf you get an error, make sure you have SUPABASE_SERVICE_ROLE_KEY in .env.local');
console.log('Get it from: https://supabase.com/dashboard/project/mhuuptkgohuztjrovpxz/settings/api');
console.log('Look for "service_role" under Project API keys (click Reveal)\n');

deleteOrphanedUser(email);
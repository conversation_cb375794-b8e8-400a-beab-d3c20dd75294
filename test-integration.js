// Test integration with actual codebase types and structure
// This simulates how the enhanced algorithm would work with real Player objects

// Mock the Player type structure from your codebase
const players = [
  {
    id: '1',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: false,
      catcher: true,  // <PERSON> is restricted from Catcher
      firstBase: false,
      other: null
    }
  },
  {
    id: '2', 
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    }
  },
  {
    id: '3',
    name: '<PERSON>', 
    positionRestrictions: {
      pitcher: true,  // <PERSON> cannot pitch
      catcher: false,
      firstBase: false,
      other: null
    }
  },
  {
    id: '4',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    }
  },
  {
    id: '5',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: true,  // Eve cannot play First Base
      other: null
    }
  },
  {
    id: '6',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    }
  },
  {
    id: '7',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    }
  },
  {
    id: '8',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    }
  },
  {
    id: '9',
    name: '<PERSON>',
    positionRestrictions: {
      pitcher: true,  // Ivy cannot pitch
      catcher: true,  // Ivy cannot catch
      firstBase: false,
      other: null
    }
  },
  {
    id: '10',
    name: 'Jack',
    positionRestrictions: {
      pitcher: false,
      catcher: false,
      firstBase: false,
      other: null
    }
  }
];

const rules = {
  limitBenchTime: true,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  respectPositionLockouts: true,
  equalPlayingTime: true,
  rotateLineupEvery: 1,
  rotatePitcherEvery: 2
};

const totalInnings = 6;

console.log("=".repeat(80));
console.log("INTEGRATION TEST - Real Player Objects, 10 Players, 6 Innings");
console.log("=".repeat(80));
console.log("Players:", players.map(p => `${p.name}(${p.id})`));
console.log("Rules:", rules);
console.log("Total Innings:", totalInnings);
console.log("=".repeat(80));

// Mock the canPlayPosition function from utils.ts
function canPlayPosition(player, position) {
  if (!player || !player.positionRestrictions) {
    return true;
  }

  if (position === "Pitcher" && player.positionRestrictions.pitcher) return false;
  if (position === "Catcher" && player.positionRestrictions.catcher) return false;
  if (position === "First Base" && player.positionRestrictions.firstBase) return false;

  if (player.positionRestrictions.other) {
    if (player.positionRestrictions.other === position) return false;

    if (player.positionRestrictions.other === "Middle Infield" &&
        (position === "Shortstop" || position === "Second Base")) {
      return false;
    }

    if (player.positionRestrictions.other === "3B/MI/SS/2B" &&
        (position === "Third Base" || position === "Shortstop" ||
         position === "Second Base" || position === "Middle Infield")) {
      return false;
    }
  }

  return true;
}

// Mock the getPositionDisplayName function
function getPositionDisplayName(positionId) {
  switch (positionId) {
    case "pitcher": return "Pitcher";
    case "catcher": return "Catcher";
    case "firstBase": return "First Base";
    case "secondBase": return "Second Base";
    case "thirdBase": return "Third Base";
    case "shortstop": return "Shortstop";
    case "leftField": return "Left Field";
    case "centerField": return "Center Field";
    case "rightField": return "Right Field";
    default: return positionId;
  }
}

// Enhanced generateOptimalLineup function (adapted for Node.js testing)
function generateOptimalLineup(availablePlayers, totalInnings, rules) {
  console.log(`🚀 GENERATING OPTIMAL LINEUP - ${totalInnings} innings`);
  
  // Initialize enhanced tracking maps
  let inningsPlayed = {};
  let benchStreak = {};
  let lastPositionTimestamp = {};
  let inningsPitched = {};
  
  // Define all baseball positions
  const positions = [
    'pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop',
    'thirdBase', 'leftField', 'centerField', 'rightField'
  ];
  
  // Initialize all players
  availablePlayers.forEach(player => {
    inningsPlayed[player.name] = 0;
    benchStreak[player.name] = 0;
    inningsPitched[player.name] = 0;
    lastPositionTimestamp[player.name] = {};
    positions.forEach(pos => {
      lastPositionTimestamp[player.name][pos] = 0;
    });
  });
  
  const lineup = [];
  
  // Helper function to check if player can play position
  function canAssignPosition(player, position, assignedThisInning) {
    if (assignedThisInning.has(player.name)) return false;
    
    if (!rules.respectPositionLockouts) return true;
    
    // Check position restrictions
    if (!canPlayPosition(player, getPositionDisplayName(position))) return false;
    
    // Check bench streak limit (only if we have excess players)
    if (availablePlayers.length > positions.length && benchStreak[player.name] >= (rules.limitBenchTime ? 2 : 999)) {
      return false;
    }
    
    return true;
  }
  
  // Generate each inning
  for (let inning = 1; inning <= totalInnings; inning++) {
    console.log(`\n🏟️  GENERATING INNING ${inning}`);
    
    const inningLineup = {
      inning: inning,
      positions: {
        pitcher: '',
        catcher: '',
        firstBase: '',
        secondBase: '',
        shortstop: '',
        thirdBase: '',
        leftField: '',
        centerField: '',
        rightField: '',
        bench: []
      }
    };
    
    const assignedThisInning = new Set();
    
    // Step 1: Handle bench assignments if we have more players than positions
    if (availablePlayers.length > positions.length) {
      const playersToBeach = availablePlayers.length - positions.length;
      
      // Sort players by inningsPlayed (desc) then benchStreak (asc) to find who should be benched
      const benchCandidates = [...availablePlayers].sort((a, b) => {
        const aPlayed = inningsPlayed[a.name];
        const bPlayed = inningsPlayed[b.name];
        if (aPlayed !== bPlayed) {
          return bPlayed - aPlayed; // Descending - most played first
        }
        // If tied on playing time, bench those with LOWER bench streak
        return benchStreak[a.name] - benchStreak[b.name]; // Ascending - least benched first
      });
      
      console.log(`🪑 Benching ${playersToBeach} players:`);
      for (let i = 0; i < playersToBeach; i++) {
        const playerToBench = benchCandidates[i];
        inningLineup.positions.bench.push(playerToBench.name);
        assignedThisInning.add(playerToBench.name);
        benchStreak[playerToBench.name]++;
        console.log(`   📉 ${playerToBench.name}: benched (played=${inningsPlayed[playerToBench.name]}, benchStreak=${benchStreak[playerToBench.name]})`);
      }
    }
    
    // Step 2: Assign field positions
    for (const position of positions) {
      console.log(`\n   🎯 Position ${position}:`);
      
      // Get eligible players for this position
      let eligiblePlayers = availablePlayers.filter(player => 
        canAssignPosition(player, position, assignedThisInning)
      );
      
      console.log(`      📋 Eligible: [${eligiblePlayers.map(p => p.name).join(', ')}]`);
      
      // Special handling for Pitcher position
      if (position === 'pitcher') {
        eligiblePlayers.sort((a, b) => {
          const aPitched = inningsPitched[a.name];
          const bPitched = inningsPitched[b.name];
          if (aPitched !== bPitched) {
            return aPitched - bPitched; // Ascending - fewest innings pitched first
          }
          const aLastPitched = lastPositionTimestamp[a.name][position];
          const bLastPitched = lastPositionTimestamp[b.name][position];
          return aLastPitched - bLastPitched; // Ascending - longest since last pitch first
        });
        
        console.log(`      ⚾ Sorted by pitching: [${eligiblePlayers.slice(0,3).map(p => `${p.name}(${inningsPitched[p.name]})`).join(', ')}]`);
      } else {
        // Regular position sorting
        eligiblePlayers.sort((a, b) => {
          const aPlayed = inningsPlayed[a.name];
          const bPlayed = inningsPlayed[b.name];
          if (aPlayed !== bPlayed) {
            return aPlayed - bPlayed; // Ascending - least played first
          }
          const aLastPlayed = lastPositionTimestamp[a.name][position];
          const bLastPlayed = lastPositionTimestamp[b.name][position];
          return aLastPlayed - bLastPlayed; // Ascending - least recently at this position first
        });
        
        console.log(`      📊 Sorted by playing time: [${eligiblePlayers.slice(0,3).map(p => `${p.name}(${inningsPlayed[p.name]})`).join(', ')}]`);
      }
      
      // If no eligible players, use backtracking fallback
      if (eligiblePlayers.length === 0) {
        eligiblePlayers = availablePlayers.filter(player => !assignedThisInning.has(player.name));
        console.log(`      ⚠️ BACKTRACK: Using [${eligiblePlayers.map(p => p.name).join(', ')}]`);
      }
      
      // Assign the first (best) player
      const selectedPlayer = eligiblePlayers[0];
      inningLineup.positions[position] = selectedPlayer.name;
      assignedThisInning.add(selectedPlayer.name);
      
      console.log(`      ✅ SELECTED: ${selectedPlayer.name}`);
      
      // Update tracking maps
      inningsPlayed[selectedPlayer.name]++;
      benchStreak[selectedPlayer.name] = 0; // Reset bench streak since they're playing
      lastPositionTimestamp[selectedPlayer.name][position] = inning;
      
      if (position === 'pitcher') {
        inningsPitched[selectedPlayer.name]++;
      }
    }
    
    lineup.push(inningLineup);
  }
  
  return lineup;
}

// Run the test
const result = generateOptimalLineup(players, totalInnings, rules);

console.log(`\n${"=".repeat(80)}`);
console.log("📋 FINAL LINEUP SUMMARY:");
console.log(`${"=".repeat(80)}`);

result.forEach((inning) => {
  console.log(`\nInning ${inning.inning}:`);
  console.log(`  Pitcher: ${inning.positions.pitcher}`);
  console.log(`  Catcher: ${inning.positions.catcher}`);
  console.log(`  Infield: ${inning.positions.firstBase}, ${inning.positions.secondBase}, ${inning.positions.shortstop}, ${inning.positions.thirdBase}`);
  console.log(`  Outfield: ${inning.positions.leftField}, ${inning.positions.centerField}, ${inning.positions.rightField}`);
  if (inning.positions.bench.length > 0) {
    console.log(`  Bench: [${inning.positions.bench.join(', ')}]`);
  }
});

// Check for restriction violations
console.log(`\n${"=".repeat(80)}`);
console.log("🚨 RESTRICTION VIOLATIONS CHECK:");
console.log(`${"=".repeat(80)}`);
let violationFound = false;

result.forEach((inning) => {
  Object.entries(inning.positions).forEach(([position, playerName]) => {
    if (position === 'bench') return; // Skip bench
    
    const player = players.find(p => p.name === playerName);
    if (player && !canPlayPosition(player, getPositionDisplayName(position))) {
      console.log(`❌ VIOLATION: ${playerName} is playing ${position} but is restricted!`);
      violationFound = true;
    }
  });
});

if (!violationFound) {
  console.log("✅ No restriction violations found!");
}

// Final statistics
console.log(`\n${"=".repeat(80)}`);
console.log("📊 FINAL PLAYING TIME ANALYSIS:");
console.log(`${"=".repeat(80)}`);

const finalStats = {};
players.forEach(player => {
  let fieldInnings = 0;
  let benchInnings = 0;
  
  result.forEach(inning => {
    if (Object.values(inning.positions).includes(player.name) && !inning.positions.bench.includes(player.name)) {
      fieldInnings++;
    } else if (inning.positions.bench.includes(player.name)) {
      benchInnings++;
    }
  });
  
  finalStats[player.name] = { field: fieldInnings, bench: benchInnings, total: fieldInnings + benchInnings };
});

Object.entries(finalStats)
  .sort(([,a], [,b]) => b.field - a.field)
  .forEach(([player, stats]) => {
    console.log(`${player}: ${stats.field} field, ${stats.bench} bench (${stats.total} total)`);
  });

-- Verify Player RLS Fix is Working

-- 1. Check current RLS policies on players table
SELECT 
    policyname,
    cmd,
    qual,
    with_check
FROM pg_policies
WHERE schemaname = 'public' 
AND tablename = 'players'
ORDER BY policyname;

-- 2. Test if a regular user can see their own players
-- This simulates what happens when the app queries players
DO $$
DECLARE
    test_user_id UUID;
    test_team_id UUID;
    player_count INTEGER;
BEGIN
    -- Get a non-admin user with a team
    SELECT u.id, t.id INTO test_user_id, test_team_id
    FROM auth.users u
    JOIN teams t ON t.user_id = u.id
    WHERE u.email = '<EMAIL>'
    LIMIT 1;

    IF test_user_id IS NOT NULL THEN
        -- Count players for this team
        SELECT COUNT(*) INTO player_count
        FROM players p
        WHERE p.team_id = test_team_id;

        RAISE NOTICE 'User <EMAIL> has % players', player_count;
        
        -- Test if user could theoretically insert a player
        IF EXISTS (
            SELECT 1 FROM teams t
            WHERE t.id = test_team_id
            AND t.user_id = test_user_id
        ) THEN
            RAISE NOTICE 'User CAN insert players to their team ✓';
        ELSE
            RAISE NOTICE 'User CANNOT insert players to their team ✗';
        END IF;
    ELSE
        RAISE NOTICE 'Test user not found';
    END IF;
END $$;

-- 3. Verify no cross-user data leakage
-- Each user should only see players from their own teams
SELECT 
    u.email,
    COUNT(DISTINCT t.id) as team_count,
    COUNT(DISTINCT p.id) as player_count
FROM auth.users u
LEFT JOIN teams t ON t.user_id = u.id
LEFT JOIN players p ON p.team_id = t.id
WHERE u.email IN ('<EMAIL>', '<EMAIL>')
GROUP BY u.email
ORDER BY u.email;

-- 4. Check if the INSERT policy syntax is correct
-- This should show the new policy without the problematic user_id check
SELECT 
    policyname,
    with_check
FROM pg_policies
WHERE schemaname = 'public' 
AND tablename = 'players'
AND cmd = 'INSERT';
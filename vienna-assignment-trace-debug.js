/**
 * Debug script to trace the exact assignment process for Vienna
 * Simulate the strict assignment logic step by step
 */

console.log('🔍 VIENNA ASSIGNMENT TRACE DEBUG');

// Simulate the exact scenario
const players = [
  {
    id: 'vienna-id',
    name: 'Vienna',
    teamRoles: {
      pitcher: 'capable',
      catcher: 'capable', 
      firstBase: 'capable'
    }
  },
  {
    id: 'mikayla-id',
    name: '<PERSON><PERSON><PERSON>',
    teamRoles: {
      catcher: 'go-to',
      shortstop: 'capable'
    }
  },
  {
    id: 'grace-id',
    name: '<PERSON>',
    teamRoles: {
      catcher: 'capable',
      rightField: 'capable'
    }
  },
  {
    id: 'emma-id',
    name: '<PERSON>',
    teamRoles: {
      pitcher: 'go-to',
      leftField: 'capable'
    }
  },
  {
    id: 'sophia-id',
    name: '<PERSON>',
    teamRoles: {
      pitcher: 'capable',
      centerField: 'capable'
    }
  },
  {
    id: 'player6-id',
    name: 'Player6',
    teamRoles: {
      secondBase: 'capable',
      thirdBase: 'capable'
    }
  },
  {
    id: 'player7-id',
    name: 'Player<PERSON>',
    teamRoles: {
      firstBase: 'capable',
      leftField: 'capable'
    }
  },
  {
    id: 'player8-id',
    name: 'Player8',
    teamRoles: {
      shortstop: 'capable',
      centerField: 'capable'
    }
  },
  {
    id: 'player9-id',
    name: 'Player9',
    teamRoles: {
      thirdBase: 'capable',
      rightField: 'capable'
    }
  }
];

// Simulate pitcher plan for Game 1
const pitcherPlan = {
  gameNumber: 1,
  plannedPitchers: [
    {
      player: players.find(p => p.name === 'Vienna'),
      targetInnings: [1, 2],
      role: 'starter'
    },
    {
      player: players.find(p => p.name === 'Emma'),
      targetInnings: [3, 4], 
      role: 'any'
    },
    {
      player: players.find(p => p.name === 'Sophia'),
      targetInnings: [5, 6],
      role: 'any'
    }
  ]
};

console.log('\n⚾ PITCHER PLAN FOR GAME 1:');
pitcherPlan.plannedPitchers.forEach(pp => {
  console.log(`${pp.player.name}: innings ${pp.targetInnings.join(', ')} (${pp.role})`);
});

// Simulate inning-by-inning assignment
function simulateInningAssignment(inningNumber, previousLineups, pitcherPlan) {
  console.log(`\n🏟️ SIMULATING INNING ${inningNumber} ASSIGNMENT:`);
  
  const positions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 
                    'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
  
  const assignments = new Map();
  const assignedPlayers = new Set();
  
  // 1. Handle pitcher assignment (pre-planned)
  const plannedPitcher = pitcherPlan.plannedPitchers.find(pp => 
    pp.targetInnings.includes(inningNumber)
  );
  
  if (plannedPitcher) {
    assignments.set('pitcher', plannedPitcher.player.name);
    assignedPlayers.add(plannedPitcher.player.name);
    console.log(`  ✅ Pitcher: ${plannedPitcher.player.name} (planned)`);
  }
  
  // 2. Check catcher rotation
  const shouldRotateCatcher = inningNumber > 1 && ((inningNumber - 1) % 2) === 0;
  const previousCatcher = previousLineups.length > 0 ? 
    previousLineups[previousLineups.length - 1].catcher : null;
  
  console.log(`  🥎 Catcher rotation check: should rotate = ${shouldRotateCatcher}, previous = ${previousCatcher}`);
  
  // 3. Get available catchers sorted by priority
  const availableCatchers = players
    .filter(p => !assignedPlayers.has(p.name))
    .filter(p => p.teamRoles?.catcher && p.teamRoles.catcher !== 'never' && p.teamRoles.catcher !== 'avoid')
    .sort((a, b) => {
      const aRole = a.teamRoles.catcher;
      const bRole = b.teamRoles.catcher;
      const rolePriority = {
        'go-to': 5,
        'primary': 4,
        'capable': 3,
        'in the mix': 2,
        'emergency': 1
      };
      return (rolePriority[bRole] || 0) - (rolePriority[aRole] || 0);
    });
  
  console.log(`  🥎 Available catchers:`, availableCatchers.map(p => `${p.name} (${p.teamRoles.catcher})`));
  
  // 4. Assign catcher
  let assignedCatcher = null;
  
  if (!shouldRotateCatcher && previousCatcher && !assignedPlayers.has(previousCatcher)) {
    // Keep same catcher
    const previousCatcherPlayer = players.find(p => p.name === previousCatcher);
    if (previousCatcherPlayer && availableCatchers.includes(previousCatcherPlayer)) {
      assignedCatcher = previousCatcher;
      console.log(`  ✅ Catcher: ${assignedCatcher} (keeping same)`);
    }
  }
  
  if (!assignedCatcher && availableCatchers.length > 0) {
    assignedCatcher = availableCatchers[0].name;
    console.log(`  ✅ Catcher: ${assignedCatcher} (new assignment, priority: ${availableCatchers[0].teamRoles.catcher})`);
  }
  
  if (assignedCatcher) {
    assignments.set('catcher', assignedCatcher);
    assignedPlayers.add(assignedCatcher);
  }
  
  // 5. Check if Vienna got assigned
  const viennaPosition = Array.from(assignments.entries()).find(([pos, name]) => name === 'Vienna');
  if (viennaPosition) {
    console.log(`  🎯 VIENNA ASSIGNED: ${viennaPosition[0]}`);
  } else {
    console.log(`  ❌ VIENNA NOT ASSIGNED to pitcher or catcher`);
    if (assignedPlayers.has('Vienna')) {
      console.log(`  ⚠️ Vienna is already assigned (shouldn't happen in this simulation)`);
    } else {
      console.log(`  📝 Vienna available for other positions`);
    }
  }
  
  return {
    pitcher: assignments.get('pitcher'),
    catcher: assignments.get('catcher'),
    assignedPlayers: Array.from(assignedPlayers)
  };
}

// Simulate each inning
const lineups = [];
console.log('\n📋 INNING-BY-INNING SIMULATION:');

for (let inning = 1; inning <= 6; inning++) {
  const result = simulateInningAssignment(inning, lineups, pitcherPlan);
  lineups.push(result);
}

console.log('\n📊 SUMMARY:');
lineups.forEach((lineup, index) => {
  const inning = index + 1;
  console.log(`Inning ${inning}: Pitcher=${lineup.pitcher}, Catcher=${lineup.catcher}`);
});

console.log('\n🎯 VIENNA CATCHER ANALYSIS:');
const viennaCatcherInnings = lineups
  .map((lineup, index) => ({ inning: index + 1, catcher: lineup.catcher }))
  .filter(data => data.catcher === 'Vienna');

if (viennaCatcherInnings.length > 0) {
  console.log(`✅ Vienna catches in innings: ${viennaCatcherInnings.map(d => d.inning).join(', ')}`);
} else {
  console.log(`❌ Vienna NEVER catches`);
  
  // Analyze why
  console.log('\n🔍 ROOT CAUSE ANALYSIS:');
  console.log('Vienna is capable of catching but never gets assigned. Possible reasons:');
  console.log('1. Mikayla (go-to) gets higher priority');
  console.log('2. Grace (capable) gets assigned before Vienna');
  console.log('3. When Vienna is not pitching, catcher rotation isn\'t occurring');
  console.log('4. Vienna\'s pitcher assignments conflict with catcher rotation timing');
  
  // Check rotation timing vs Vienna's pitcher schedule
  const rotationInnings = [3, 5]; // Innings where catcher rotation should occur
  const viennaPitchingInnings = [1, 2]; // From pitcher plan
  const viennaAvailableForCatcher = rotationInnings.filter(inning => 
    !viennaPitchingInnings.includes(inning)
  );
  
  console.log(`\nTiming analysis:`);
  console.log(`Catcher rotation scheduled: innings ${rotationInnings.join(', ')}`);
  console.log(`Vienna pitching: innings ${viennaPitchingInnings.join(', ')}`);
  console.log(`Vienna available for catcher rotation: innings ${viennaAvailableForCatcher.join(', ')}`);
  
  if (viennaAvailableForCatcher.length > 0) {
    console.log(`✅ Vienna SHOULD be available for catcher in innings ${viennaAvailableForCatcher.join(', ')}`);
    console.log(`🐛 The issue is likely in the role priority system favoring Mikayla and Grace`);
  } else {
    console.log(`❌ Vienna is never available when catcher rotation occurs`);
    console.log(`🐛 The issue is in the pitcher planning vs catcher rotation timing`);
  }
}

console.log('\n💡 RECOMMENDATIONS:');
console.log('1. Add console logging to track Vienna specifically during batch generation');
console.log('2. Check if Mikayla is dominating catcher position due to go-to priority');
console.log('3. Consider adjusting role priority to give capable players more rotation opportunities');
console.log('4. Look for "Vienna" in position assignment logs during actual batch generation');
// Quick test to check initial lineup assignment

const players = [];
for (let i = 1; i <= 12; i++) {
  players.push({ id: i.toString(), name: `Player${i}`, teamRoles: {} });
}

console.log('Players:', players.length);
console.log('Field positions: 9');
console.log('Bench players should be: 3');
console.log('');

// Simulate initial assignment
const fieldPositions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
const assigned = new Set();

// Random assignment for testing
fieldPositions.forEach((pos, idx) => {
  const player = players[idx % players.length];
  console.log(`${pos}: ${player.name}`);
  assigned.add(player.name);
});

console.log('\nBench:');
players.forEach(p => {
  if (!assigned.has(p.name)) {
    console.log(`- ${p.name}`);
  }
});

console.log('\nProblem: With 12 players and rotateEvery=1, we need to rotate 3 players each inning.');
console.log('But if the same 9 players keep getting selected, the 3 bench players never get enough playing time!');
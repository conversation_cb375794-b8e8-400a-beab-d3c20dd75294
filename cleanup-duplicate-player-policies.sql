-- CLEANUP: Remove duplicate player policies
-- The verification showed we have duplicate INSERT policies

BEGIN;

-- Drop the old problematic policy
DROP POLICY IF EXISTS "Users can only insert players on their teams" ON players;

-- Also drop any other duplicate policies
DROP POLICY IF EXISTS "Users can only see players on their teams" ON players;
DROP POLICY IF EXISTS "Users can only update players on their teams" ON players;
DROP POLICY IF EXISTS "Users can only delete players on their teams" ON players;

-- Verify we now have only the correct policies
SELECT 
    policyname,
    cmd,
    with_check
FROM pg_policies
WHERE schemaname = 'public' 
AND tablename = 'players'
ORDER BY policyname, cmd;

-- Test that a regular user can still insert players
DO $$
DECLARE
    test_user_id UUID;
    test_team_id UUID;
BEGIN
    -- Get a non-admin user with a team
    SELECT u.id, t.id INTO test_user_id, test_team_id
    FROM auth.users u
    JOIN teams t ON t.user_id = u.id
    WHERE u.email = '<EMAIL>'
    LIMIT 1;

    IF test_user_id IS NOT NULL THEN
        -- Test if user could insert a player with the remaining policy
        IF EXISTS (
            SELECT 1 FROM teams t
            WHERE t.id = test_team_id
            AND t.user_id = test_user_id
        ) THEN
            RAISE NOTICE 'SUCCESS: User can still insert players to their team ✓';
        ELSE
            RAISE NOTICE 'ERROR: User cannot insert players to their team ✗';
        END IF;
    END IF;
END $$;

COMMIT;

-- Final check: List all remaining policies
SELECT 
    'Final Policy Check' as status,
    policyname,
    cmd
FROM pg_policies
WHERE schemaname = 'public' 
AND tablename = 'players'
ORDER BY cmd, policyname;
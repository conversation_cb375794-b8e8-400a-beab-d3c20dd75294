-- Verification script to ensure the roster fix is complete
-- This script checks that both teams have proper rosters and the database schema is correct

-- 1. Check team roster counts
SELECT 
    'Team Roster Counts' as check_type,
    t.name as team_name,
    COUNT(p.id) as player_count
FROM teams t 
LEFT JOIN players p ON t.id = p.team_id 
WHERE t.user_id = (SELECT id FROM auth.users WHERE email = '<EMAIL>')
GROUP BY t.id, t.name
ORDER BY t.name;

-- 2. Check that competitive mode columns exist and work
SELECT 
    'Competitive Mode Schema' as check_type,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'players' 
AND table_schema = 'public' 
AND column_name IN ('position_ratings', 'is_star_player')
ORDER BY column_name;

-- 3. Verify U15 Selects roster specifically
SELECT 
    'U15 Selects Roster' as check_type,
    p.name,
    p.position_ratings,
    p.is_star_player,
    p.created_at
FROM players p
JOIN teams t ON p.team_id = t.id
WHERE t.name = 'U15 Selects' 
AND t.user_id = (SELECT id FROM auth.users WHERE email = '<EMAIL>')
ORDER BY p.name;

-- 4. Check that lineups still reference the correct players
SELECT 
    'Lineup Player References' as check_type,
    l.name as lineup_name,
    COUNT(DISTINCT li.id) as innings_count,
    l.created_at
FROM lineups l
LEFT JOIN lineup_innings li ON l.id = li.lineup_id
WHERE l.team_id = '079f5fc4-1f41-49fa-a016-4741d319a2f0'
GROUP BY l.id, l.name, l.created_at
ORDER BY l.created_at DESC;

-- 5. Verify rotation rules exist for both teams
SELECT 
    'Rotation Rules' as check_type,
    t.name as team_name,
    rr.competitive_mode,
    rr.competitive_min_playing_time,
    rr.key_positions,
    rr.star_player_rotation_delay
FROM teams t
LEFT JOIN rotation_rules rr ON t.id = rr.team_id
WHERE t.user_id = (SELECT id FROM auth.users WHERE email = '<EMAIL>')
ORDER BY t.name;

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing environment variables. Please check your .env.local file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testUserPaymentStatus(email) {
  console.log(`\n🔍 Testing payment status for: ${email}\n`);

  try {
    // 1. Find the user profile
    console.log('1. Looking up user profile...');
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('email', email)
      .maybeSingle();

    if (profileError) {
      console.error('❌ Error fetching profile:', profileError);
      return;
    }

    if (!profile) {
      console.error('❌ No profile found for this email');
      return;
    }

    console.log('✅ Found profile:', {
      id: profile.id,
      email: profile.email,
      full_name: profile.full_name,
      is_admin: profile.is_admin
    });

    // 2. Check subscriptions using the EXACT query from checkPaymentStatus
    console.log('\n2. Checking payment status (exact query from app)...');
    const { data: paymentCheck, error: paymentError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', profile.id)
      .eq('is_paid', true);

    if (paymentError) {
      console.error('❌ Payment check query failed:', paymentError);
      return;
    }

    console.log(`\n📊 Payment Status Check Results:`);
    console.log(`- Query returned ${paymentCheck?.length || 0} paid subscription(s)`);

    if (paymentCheck && paymentCheck.length > 0) {
      console.log('\n✅ This user SHOULD be recognized as paid');
      
      // Show subscription details
      paymentCheck.forEach((sub, index) => {
        console.log(`\n📋 Subscription ${index + 1}:`);
        console.log(`  - ID: ${sub.id}`);
        console.log(`  - is_paid: ${sub.is_paid}`);
        console.log(`  - amount: $${sub.amount / 100}`);
        console.log(`  - tier: ${sub.tier || '❌ MISSING'}`);
        console.log(`  - team_limit: ${sub.team_limit || '❌ MISSING'}`);
        console.log(`  - created_at: ${sub.created_at}`);
        console.log(`  - payment_date: ${sub.payment_date}`);
        
        // Check expiration
        if (sub.expires_at) {
          const isExpired = new Date(sub.expires_at) < new Date();
          console.log(`  - expires_at: ${sub.expires_at} ${isExpired ? '❌ EXPIRED' : '✅ ACTIVE'}`);
        }
      });

      // Check for missing fields
      const missingFields = paymentCheck.some(sub => !sub.tier || !sub.team_limit);
      if (missingFields) {
        console.log('\n⚠️  WARNING: Some subscriptions are missing tier/team_limit fields!');
        console.log('Run: node fix-subscription-fields.js to fix this');
      }
    } else {
      console.log('\n❌ This user will NOT be recognized as paid');
      console.log('\nPossible reasons:');
      console.log('- No subscription record exists');
      console.log('- Subscription exists but is_paid = false');
      console.log('- Subscription is expired');
    }

    // 3. Check all subscriptions (paid and unpaid)
    console.log('\n3. Checking ALL subscriptions for this user...');
    const { data: allSubs, error: allSubsError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', profile.id);

    if (!allSubsError && allSubs) {
      console.log(`Found ${allSubs.length} total subscription(s):`);
      allSubs.forEach((sub, index) => {
        console.log(`  ${index + 1}. is_paid=${sub.is_paid}, tier=${sub.tier || 'null'}, team_limit=${sub.team_limit || 'null'}`);
      });
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Get email from command line
const email = process.argv[2];

if (!email) {
  console.log('Usage: node test-user-payment-status.js <email>');
  console.log('Example: node test-user-payment-status.js "<EMAIL>"');
  process.exit(1);
}

testUserPaymentStatus(email);
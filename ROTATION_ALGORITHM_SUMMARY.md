# Dugout Boss Rotation Algorithm Summary

## Overview
The rotation algorithm manages fair playing time and position assignments for baseball/softball teams. It supports both recreational (equal playing time) and competitive (skill-based) modes.

## Core Algorithm Files

### 1. Main Rotation Logic (`/src/lib/utils-enhanced.ts`)

#### Key Interfaces
```typescript
interface Player {
  id: string;
  name: string;
  positionRestrictions?: string[];  // Positions they CANNOT play
  teamRoles?: TeamRolePreferences;   // New position preference system
  isStarPlayer?: boolean;            // For competitive mode
  competitiveRating?: number;        // 1-5 skill rating
  defaultBattingOrder?: number;      // Preferred batting position
}

interface RotationRules {
  rotateLineupEvery: number;         // Rotate every X innings (default: 1)
  rotatePitcherEvery: number;        // Pitcher rotation frequency (default: 2)
  maxConsecutiveBenchInnings: number;// Max bench time (default: 2)
  competitiveMode: boolean;          // Skill-based vs fair play
  allowPitcherRotation: boolean;     // Can pitchers play other positions
  allowCatcherRotation: boolean;     // Can catchers play other positions
}

interface TeamRolePreferences {
  [position: string]: PositionRole;
}

type PositionRole = 'primary' | 'inTheMix' | 'emergency' | 'never';
```

#### Main Entry Point
```typescript
export function generateLineup(
  players: Player[],
  gameDate: Date,
  teamName: string,
  rotationRules: RotationRules = defaultRotationRules,
  numInnings: number = 6,
  existingLineup?: any
): LineupState | null {
  // 1. Initialize lineup state
  const lineup = new LineupState(
    generateId(),
    `${teamName} - ${gameDate.toLocaleDateString()}`,
    gameDate.toISOString().split('T')[0],
    teamName
  );

  // 2. Set up initial assignments using constraint solver
  const solver = new ConstraintSolver(players, rotationRules);
  const initialAssignment = rotationRules.competitiveMode
    ? solver.findCompetitiveAssignment()
    : solver.findBalancedAssignment();

  // 3. Create rotator to handle inning transitions
  const rotator = new LineupRotator(players, rotationRules);
  
  // 4. Generate innings with rotations
  for (let i = 1; i <= numInnings; i++) {
    if (shouldRotate(i, rotationRules)) {
      rotator.rotateLineup(currentPositions);
    }
    lineup.addInning(i, currentPositions);
  }

  return lineup;
}
```

### 2. Constraint Solver Class
```typescript
class ConstraintSolver {
  constructor(private players: Player[], private rules: RotationRules) {}

  findBalancedAssignment(): Map<string, string> {
    const assignment = new Map<string, string>();
    const availablePlayers = new Set(this.players.map(p => p.id));
    
    // Priority order for position assignment
    const positionPriority = this.rules.competitiveMode
      ? ['pitcher', 'catcher', 'shortstop', 'firstBase', 'thirdBase', 
         'secondBase', 'centerField', 'leftField', 'rightField']
      : ['pitcher', 'catcher', ...shuffled(otherPositions)];

    for (const position of positionPriority) {
      const candidates = Array.from(availablePlayers)
        .filter(playerId => {
          const player = this.getPlayer(playerId);
          return this.canPlayPosition(player, position);
        })
        .sort((a, b) => {
          // Sort by position preference
          const scoreA = this.getPositionScore(this.getPlayer(a), position);
          const scoreB = this.getPositionScore(this.getPlayer(b), position);
          return scoreB - scoreA; // Higher score = better fit
        });

      if (candidates.length > 0) {
        const selected = candidates[0];
        assignment.set(position, selected);
        availablePlayers.delete(selected);
      }
    }

    return assignment;
  }

  private canPlayPosition(player: Player, position: string): boolean {
    // Check position restrictions (legacy)
    if (player.positionRestrictions?.includes(position)) {
      return false;
    }

    // Check team roles (new system)
    const role = player.teamRoles?.[position];
    if (role === 'never') return false;
    
    // Special handling for pitcher/catcher
    if (position === 'pitcher' && !this.rules.allowPitcherRotation) {
      return role === 'primary' || role === 'inTheMix';
    }
    
    return true;
  }

  private getPositionScore(player: Player, position: string): number {
    const role = player.teamRoles?.[position];
    
    // Base scores by role
    const roleScores = {
      'primary': 100,
      'inTheMix': 75,
      'emergency': 25,
      'never': 0,
      undefined: 50 // No preference set
    };
    
    let score = roleScores[role || 'undefined'];
    
    // Competitive mode bonuses
    if (this.rules.competitiveMode) {
      if (player.isStarPlayer && this.isKeyPosition(position)) {
        score += 50; // Star player bonus for key positions
      }
      if (player.competitiveRating) {
        score += player.competitiveRating * 10;
      }
    }
    
    return score;
  }
}
```

### 3. Lineup Rotator Class
```typescript
class LineupRotator {
  private inningCounts: Map<string, number> = new Map();
  private positionHistory: Map<string, string[]> = new Map();
  private benchStreak: Map<string, number> = new Map();

  rotateLineup(currentPositions: Map<string, string>): Map<string, string> {
    const newPositions = new Map<string, string>();
    const benchedPlayers: string[] = [];
    
    // 1. Check for forced rotations (max bench time exceeded)
    const forcedRotations = this.getForcedRotations();
    
    // 2. Identify players to rotate
    const playersToRotate = this.getPlayersToRotate(currentPositions);
    
    // 3. Find bench players who should play
    const benchToField = this.getBenchPlayersForField();
    
    // 4. Perform rotation
    // Complex logic to swap players while maintaining position eligibility
    
    return newPositions;
  }

  private getForcedRotations(): Set<string> {
    const forced = new Set<string>();
    
    for (const [playerId, streak] of this.benchStreak) {
      if (streak >= this.rules.maxConsecutiveBenchInnings) {
        forced.add(playerId);
      }
    }
    
    return forced;
  }
}
```

### 4. Multi-Game Orchestrator (`/src/lib/multi-game-orchestrator.ts`)
```typescript
export async function generateBatchLineups(
  players: Player[],
  games: GameDetails[],
  rotationRules: RotationRules,
  seriesTitle: string
): Promise<BatchGenerationResult> {
  const lineups: LineupState[] = [];
  const crossGameTracker = new CrossGameTracker(players);
  
  for (const game of games) {
    // 1. Adjust available players based on previous games
    const adjustedPlayers = crossGameTracker.getAdjustedPlayers();
    
    // 2. Generate lineup with cross-game awareness
    const lineup = generateLineup(
      adjustedPlayers,
      game.date,
      game.name,
      rotationRules,
      game.innings
    );
    
    // 3. Update cross-game statistics
    crossGameTracker.updateFromLineup(lineup);
    
    lineups.push(lineup);
  }
  
  // 4. Balance across entire series if needed
  if (rotationRules.competitiveMode) {
    return optimizeSeriesBalance(lineups, players);
  }
  
  return { lineups, seriesId: generateId() };
}
```

### 5. Fallback Algorithm (`/src/lib/improvedRotation.ts`)
```typescript
// Simpler, more aggressive rotation used as fallback
export function improvedRotateLineup(
  currentInning: InningPositions,
  allPlayers: Player[],
  inningNumber: number,
  rotationRules: RotationRules
): InningPositions {
  // Simple rotation: move everyone one position clockwise
  const positions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 
                    'thirdBase', 'shortstop', 'leftField', 'centerField', 
                    'rightField'];
  
  const newPositions: InningPositions = { ...currentInning.positions };
  const positionMap = new Map<string, string>();
  
  // Rotate players through positions
  positions.forEach((pos, idx) => {
    const nextPos = positions[(idx + 1) % positions.length];
    const currentPlayer = currentInning.positions[pos];
    
    if (this.canPlayPosition(currentPlayer, nextPos)) {
      positionMap.set(nextPos, currentPlayer);
    }
  });
  
  return { 
    inning: inningNumber, 
    positions: Object.fromEntries(positionMap) 
  };
}
```

## Key Algorithm Features

### 1. **Position Assignment Priority**
- **Competitive Mode**: Prioritizes key positions (P, C, SS) for star players
- **Fair Play Mode**: Randomizes non-critical position assignments
- **Position Preferences**: 4-tier system (Primary → In the Mix → Emergency → Never)

### 2. **Rotation Triggers**
```typescript
function shouldRotate(inning: number, rules: RotationRules): boolean {
  // Regular rotation
  if (inning % rules.rotateLineupEvery === 0) return true;
  
  // Pitcher-specific rotation
  if (isPitcherDue && inning % rules.rotatePitcherEvery === 0) return true;
  
  // Forced rotation for bench streaks
  if (hasExceededBenchLimit) return true;
  
  return false;
}
```

### 3. **Balancing Metrics**
- **Innings Played**: Tracks total playing time per player
- **Position Distribution**: Ensures variety in positions played
- **Bench Time**: Prevents excessive consecutive bench innings
- **Cross-Game Balance**: For series, balances across multiple games

### 4. **Constraint Satisfaction**
The algorithm uses a constraint satisfaction approach:
1. **Hard Constraints**: Must be satisfied (9 positions filled, no invalid assignments)
2. **Soft Constraints**: Optimized (balance, preferences, competitive advantages)
3. **Fallback Logic**: If perfect solution impossible, relaxes constraints gradually

### 5. **Edge Case Handling**
- **Insufficient Players**: Warns if < 9 players available
- **Position Conflicts**: Uses emergency assignments when needed
- **Missing Preferences**: Falls back to allowing any position
- **Bench Overflow**: Forces rotations when bench limits exceeded

## Testing the Algorithm

Key test scenarios in `/src/__tests__/`:
```typescript
// Test equal playing time
test('all players get equal innings over 6 innings', () => {
  const lineup = generateLineup(players, date, 'Team', {
    rotateLineupEvery: 1,
    maxConsecutiveBenchInnings: 2
  });
  
  // Verify innings played variance is minimal
  expect(maxInnings - minInnings).toBeLessThanOrEqual(1);
});

// Test position restrictions
test('players never assigned to restricted positions', () => {
  const player = { 
    id: '1', 
    name: 'No Catcher', 
    teamRoles: { catcher: 'never' }
  };
  
  // Verify never assigned to catcher
});

// Test competitive mode
test('star players get key positions in competitive mode', () => {
  const lineup = generateLineup(players, date, 'Team', {
    competitiveMode: true
  });
  
  // Verify star players at pitcher, catcher, shortstop
});
```

## Performance Considerations

1. **Time Complexity**: O(n² × p) where n = players, p = positions
2. **Space Complexity**: O(n × i) for tracking history across innings
3. **Optimization**: Uses memoization for position eligibility checks
4. **Caching**: Stores frequently accessed calculations

## Common Issues & Solutions

1. **"Can't fill all positions"**
   - Solution: Relaxes constraints, uses emergency assignments

2. **"Unfair playing time"**
   - Solution: Forces rotations when imbalance detected

3. **"Same players always pitch"**
   - Solution: Pitcher rotation frequency setting

4. **"Bench streaks too long"**
   - Solution: maxConsecutiveBenchInnings enforcement

This algorithm balances mathematical optimization with practical baseball considerations, ensuring fair play while respecting the sport's positional requirements.
#!/usr/bin/env node

import { createServiceClient } from "./src/integrations/supabase/node-client.js";
import { readFileSync } from 'fs';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

console.log(`${colors.bright}${colors.cyan}
╔═══════════════════════════════════════════════════════════╗
║           Edge Function Diagnostics                       ║
╚═══════════════════════════════════════════════════════════╝
${colors.reset}`);

// Get Supabase credentials
let SUPABASE_URL = 'https://mhuuptkgohuztjrovpxz.supabase.co';
let SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1odXVwdGtnb2h1enRqcm92cHh6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY0NzU0NDEsImV4cCI6MjA2MjA1MTQ0MX0.aGUpltDKIHYJECOuFHeES7VJp7RKlMjArSg7NxFai_k';

const supabase = createServiceClient();

console.log(`${colors.bright}1. Checking Database Migration${colors.reset}`);
console.log('─'.repeat(40));

// Test if the migration has been applied
async function checkDatabase() {
  try {
    // Check if tier column exists
    const { data, error } = await supabase
      .from('subscriptions')
      .select('tier, team_limit')
      .limit(1);
    
    if (error && error.message.includes('column subscriptions.tier does not exist')) {
      console.log(`${colors.red}✗ CRITICAL: Database migration NOT applied!${colors.reset}`);
      console.log(`\n${colors.yellow}This is likely causing the 500 error.${colors.reset}`);
      console.log(`\nTo fix:`);
      console.log(`1. Go to: ${colors.cyan}https://supabase.com/dashboard/project/mhuuptkgohuztjrovpxz/sql/new${colors.reset}`);
      console.log(`2. Copy and paste the contents of: subscription-migration-to-run.sql`);
      console.log(`3. Click "Run"`);
      return false;
    } else if (!error) {
      console.log(`${colors.green}✓ Database migration is applied${colors.reset}`);
      return true;
    } else {
      console.log(`${colors.yellow}⚠ Unexpected error:${colors.reset}`, error.message);
      return false;
    }
  } catch (err) {
    console.log(`${colors.red}✗ Database check failed:${colors.reset}`, err.message);
    return false;
  }
}

console.log(`\n${colors.bright}2. Edge Function Checklist${colors.reset}`);
console.log('─'.repeat(40));

console.log(`\n${colors.yellow}Check these in Supabase Dashboard:${colors.reset}`);
console.log(`Go to: ${colors.cyan}https://supabase.com/dashboard/project/mhuuptkgohuztjrovpxz/functions${colors.reset}`);

console.log(`\n${colors.blue}Required Secrets:${colors.reset}`);
console.log(`□ STRIPE_SECRET_KEY - Must be your LIVE key (sk_live_...)`);
console.log(`□ STRIPE_WEBHOOK_SECRET - From your webhook endpoint (whsec_...)`);
console.log(`□ SUPABASE_SERVICE_ROLE_KEY - For database access`);

console.log(`\n${colors.blue}Function Logs:${colors.reset}`);
console.log(`Check the logs for 'create-payment' function to see the actual error`);

console.log(`\n${colors.bright}3. Common 500 Error Causes${colors.reset}`);
console.log('─'.repeat(40));

console.log(`\n1. ${colors.yellow}Missing STRIPE_SECRET_KEY${colors.reset}`);
console.log(`   - Error: "Missing Stripe API key"`);
console.log(`   - Fix: Add secret in Edge Functions settings`);

console.log(`\n2. ${colors.yellow}Database columns missing${colors.reset}`);
console.log(`   - Error: "column subscriptions.tier does not exist"`);
console.log(`   - Fix: Run the migration SQL`);

console.log(`\n3. ${colors.yellow}Invalid Stripe key${colors.reset}`);
console.log(`   - Error: "Invalid API Key provided"`);
console.log(`   - Fix: Make sure you're using LIVE key, not TEST`);

// Run the check
(async () => {
  const dbOk = await checkDatabase();
  
  if (!dbOk) {
    console.log(`\n${colors.red}⚠ CRITICAL: Fix the database migration first!${colors.reset}`);
  }
  
  console.log(`\n${colors.bright}Next Steps:${colors.reset}`);
  console.log(`1. Check Edge Function logs for the exact error`);
  console.log(`2. Verify all secrets are set correctly`);
  console.log(`3. If database migration is missing, run it immediately`);
  
  console.log(`\n${colors.cyan}Edge Function Logs:${colors.reset}`);
  console.log(`https://supabase.com/dashboard/project/mhuuptkgohuztjrovpxz/logs/edge-functions`);
})();
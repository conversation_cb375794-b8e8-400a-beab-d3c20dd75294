#!/usr/bin/env node

/**
 * Direct test of rotation algorithm without Supabase dependencies
 * This extracts just the core rotation logic to test the "Invalid rotation plan" error
 */

// <PERSON>'s actual U15 Selects team data
const NOAH_SELECTS_PLAYERS = [
  { 
    id: '1', 
    name: '<PERSON>', 
    positionRestrictions: { pitcher: true, catcher: false, firstBase: false, other: null },
    positionRatings: { catcher: 5, pitcher: 3, firstBase: 4, shortstop: 4 },
    isStarPlayer: true
  },
  { 
    id: '2', 
    name: '<PERSON>', 
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null },
    positionRatings: {},
    isStarPlayer: false
  },
  { 
    id: '3', 
    name: '<PERSON>', 
    positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: 'Middle Infield' },
    positionRatings: { pitcher: 4, thirdBase: 4 },
    isStarPlayer: false
  },
  { 
    id: '4', 
    name: '<PERSON>', 
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null },
    positionRatings: { shortstop: 2, thirdBase: 3, secondBase: 2 },
    isStarPlayer: false
  },
  { 
    id: '5', 
    name: '<PERSON>', 
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null },
    positionRatings: {},
    isStarPlayer: false
  },
  { 
    id: '6', 
    name: 'Evelynn', 
    positionRestrictions: { pitcher: true, catcher: false, firstBase: true, other: '3B/MI/SS/2B' },
    positionRatings: {},
    isStarPlayer: false
  },
  { 
    id: '7', 
    name: 'Finn', 
    positionRestrictions: { pitcher: true, catcher: false, firstBase: true, other: 'Shortstop' },
    positionRatings: {},
    isStarPlayer: false
  },
  { 
    id: '8', 
    name: 'Grace', 
    positionRestrictions: { pitcher: false, catcher: true, firstBase: true, other: null },
    positionRatings: { pitcher: 4, secondBase: 5 },
    isStarPlayer: true
  },
  { 
    id: '9', 
    name: 'Kaitlyn', 
    positionRestrictions: { pitcher: false, catcher: true, firstBase: true, other: 'Middle Infield' },
    positionRatings: { pitcher: 2 },
    isStarPlayer: false
  },
  { 
    id: '10', 
    name: 'Kenzie', 
    positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: null },
    positionRatings: { pitcher: 5, firstBase: 4 },
    isStarPlayer: true
  },
  { 
    id: '11', 
    name: 'Mikayla', 
    positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: '3B/MI/SS/2B' },
    positionRatings: { pitcher: 3, firstBase: 4 },
    isStarPlayer: false
  },
  { 
    id: '12', 
    name: 'Morgan', 
    positionRestrictions: { pitcher: false, catcher: true, firstBase: false, other: null },
    positionRatings: { pitcher: 4, firstBase: 3, shortstop: 3, secondBase: 3 },
    isStarPlayer: false
  },
  { 
    id: '13', 
    name: 'Presley', 
    positionRestrictions: { pitcher: false, catcher: true, firstBase: true, other: null },
    positionRatings: { pitcher: 5, shortstop: 5 },
    isStarPlayer: true
  },
  { 
    id: '14', 
    name: 'Vienna', 
    positionRestrictions: { pitcher: true, catcher: true, firstBase: true, other: null },
    positionRatings: { thirdBase: 5 },
    isStarPlayer: true
  }
];

// Competitive mode rules
const COMPETITIVE_RULES = {
  limitBenchTime: true,
  maxConsecutiveBenchInnings: 2,
  allowPitcherRotation: true,
  allowCatcherRotation: true,
  respectPositionLockouts: true,
  equalPlayingTime: false,
  rotateLineupEvery: 2,
  rotatePitcherEvery: 3,
  competitiveMode: true,
  starPlayerRotationDelay: 2,
  _randomSeed: 12345
};

// Simplified constraint solver for testing
class TestConstraintSolver {
  constructor(players) {
    this.players = players;
  }

  findValidAssignment(positions, constraints = {}) {
    console.log(`🧩 Constraint solver: Finding assignments for ${positions.length} positions`);
    console.log(`📋 Positions: ${positions.join(', ')}`);
    console.log(`🚫 Respect restrictions: ${constraints.respectPositionLockouts}`);
    
    const assignments = new Map();
    const usedPlayers = new Set();
    
    for (const position of positions) {
      let assigned = false;
      
      for (const player of this.players) {
        if (usedPlayers.has(player.name)) continue;
        
        // Check position restrictions if enabled
        if (constraints.respectPositionLockouts) {
          if (position === 'pitcher' && player.positionRestrictions.pitcher) {
            console.log(`  ❌ ${player.name} restricted from pitcher`);
            continue;
          }
          if (position === 'catcher' && player.positionRestrictions.catcher) {
            console.log(`  ❌ ${player.name} restricted from catcher`);
            continue;
          }
          if (position === 'firstBase' && player.positionRestrictions.firstBase) {
            console.log(`  ❌ ${player.name} restricted from first base`);
            continue;
          }
          
          // Check "other" restrictions
          if (player.positionRestrictions.other) {
            const otherRestrictions = player.positionRestrictions.other.toLowerCase();
            if (otherRestrictions.includes('middle infield') && 
                ['secondBase', 'shortstop', 'thirdBase'].includes(position)) {
              console.log(`  ❌ ${player.name} restricted from middle infield (${position})`);
              continue;
            }
            if (otherRestrictions.includes('shortstop') && position === 'shortstop') {
              console.log(`  ❌ ${player.name} restricted from shortstop`);
              continue;
            }
          }
        }
        
        // Assign player to position
        assignments.set(position, player.name);
        usedPlayers.add(player.name);
        console.log(`  ✅ Assigned ${player.name} to ${position}`);
        assigned = true;
        break;
      }
      
      if (!assigned) {
        console.log(`  ❌ Could not assign anyone to ${position}`);
        return null; // Failed to find valid assignment
      }
    }
    
    console.log(`✅ Constraint solver found valid assignments for all ${positions.length} positions`);
    return assignments;
  }
}

// Test the constraint solver with Noah's team
function testConstraintSolver() {
  console.log('🧪 TESTING CONSTRAINT SOLVER WITH NOAH\'S SELECTS TEAM');
  console.log('=' .repeat(60));
  
  const solver = new TestConstraintSolver(NOAH_SELECTS_PLAYERS);
  const positions = ['pitcher', 'catcher', 'firstBase', 'secondBase', 'shortstop', 'thirdBase', 'leftField', 'centerField', 'rightField'];
  
  console.log(`📊 Team analysis:`);
  console.log(`  Total players: ${NOAH_SELECTS_PLAYERS.length}`);
  console.log(`  Star players: ${NOAH_SELECTS_PLAYERS.filter(p => p.isStarPlayer).length}`);
  console.log(`  Pitcher restrictions: ${NOAH_SELECTS_PLAYERS.filter(p => p.positionRestrictions.pitcher).length}`);
  console.log(`  Catcher restrictions: ${NOAH_SELECTS_PLAYERS.filter(p => p.positionRestrictions.catcher).length}`);
  console.log(`  First base restrictions: ${NOAH_SELECTS_PLAYERS.filter(p => p.positionRestrictions.firstBase).length}`);
  console.log(`  Other restrictions: ${NOAH_SELECTS_PLAYERS.filter(p => p.positionRestrictions.other).length}`);
  
  // Test different scenarios
  const scenarios = [
    { name: 'No restrictions', respectPositionLockouts: false },
    { name: 'With restrictions', respectPositionLockouts: true },
    { name: 'Reduced team (9 players)', respectPositionLockouts: true, players: NOAH_SELECTS_PLAYERS.slice(0, 9) },
    { name: 'Highly restricted subset', respectPositionLockouts: true, players: NOAH_SELECTS_PLAYERS.filter(p => 
      p.positionRestrictions.pitcher || p.positionRestrictions.catcher || p.positionRestrictions.firstBase
    ).slice(0, 9) }
  ];
  
  for (const scenario of scenarios) {
    console.log(`\n🎯 Testing scenario: ${scenario.name}`);
    const testSolver = new TestConstraintSolver(scenario.players || NOAH_SELECTS_PLAYERS);
    const assignments = testSolver.findValidAssignment(positions, { respectPositionLockouts: scenario.respectPositionLockouts });
    
    if (assignments) {
      console.log(`  ✅ SUCCESS: Found valid assignments`);
      console.log(`  📋 Assignments:`, Object.fromEntries(assignments));
    } else {
      console.log(`  ❌ FAILED: Could not find valid assignments`);
      console.log(`  🚨 This scenario would trigger "Invalid rotation plan" error!`);
    }
  }
}

// Run the test
testConstraintSolver();

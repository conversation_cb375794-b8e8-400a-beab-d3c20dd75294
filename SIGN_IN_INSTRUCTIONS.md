# How to Sign In to Dugout Boss

There are multiple ways to sign in to the Dugout Boss app. This document provides clear instructions for each method.

## Option 1: Use Demo Mode (Recommended)

The easiest way to use the app is with Demo Mode:

1. Click the "Demo Mode" button on the homepage
2. The app will automatically log you in with a dedicated demo account
3. You will be redirected to the dashboard with full access to all features
4. This demo account has a pre-populated team with players

## Option 2: Use the Floating Sign In Button

There is a floating Sign In button at the bottom-right corner of the homepage:

1. Look for the green "Sign In" button at the bottom-right corner of the screen
2. Click this button to go to the Sign In page
3. Enter your email and password
4. Click the "Sign In" button to log in

## Option 3: Use the Sign In Button in the Header

There is a Sign In button in the header of the homepage:

1. Look for the "Sign In" button in the top-right corner of the homepage
2. Click this button to go to the Sign In page
3. Enter your email and password
4. Click the "Sign In" button to log in

## Option 4: Use the Mobile Sign In Banner

On mobile devices, there is a prominent Sign In banner at the top of the homepage:

1. Look for the green banner at the top of the homepage on mobile
2. Click the "Sign In to Your Account" button
3. Enter your email and password
4. Click the "Sign In" button to log in

## Option 5: Use the Footer Sign In Button

There is a large Sign In button in the footer of the homepage:

1. Scroll to the bottom of the homepage
2. Look for the "Sign In to Your Account" button
3. Click this button to go to the Sign In page
4. Enter your email and password
5. Click the "Sign In" button to log in

## Troubleshooting

If you're having trouble signing in:

1. Try using Demo Mode:
   - Click the "Demo Mode" button on the homepage
   - This is the most reliable way to access the app

2. If you're redirected to the pricing page after signing in:
   - Make sure you're using a demo account or a paid account
   - Try logging out and using Demo Mode instead

3. If the dashboard is empty:
   - Try creating a new team
   - Or log out and use Demo Mode which comes with a pre-populated team

4. Try clearing your browser cache and cookies, then try again

5. Try using a different sign-in method from the options above

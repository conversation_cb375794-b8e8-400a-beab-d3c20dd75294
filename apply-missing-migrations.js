// Script to apply missing migrations to Supabase
import { supabase } from "./src/integrations/supabase/node-client.js";
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { readFileSync } from 'fs';

// Initialize dotenv

// Get directory path for the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function applyMissingMigrations() {
  console.log('Applying missing migrations to Supabase...');
  
  // Read environment variables from .env file directly if needed
  let supabaseUrl = process.env.VITE_SUPABASE_URL;
  let supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;
  
  // If env vars aren't loaded through dotenv, try to read the file directly
          return acc;
      }, {});
      
      supabaseUrl = envVars.VITE_SUPABASE_URL;
      supabaseAnonKey = envVars.VITE_SUPABASE_ANON_KEY;
    } catch (err) {
      console.error('Error reading .env file:', err);
    }
  }
  
    
  console.log(`Supabase URL: ${supabaseUrl}`);
  console.log(`Supabase Anon Key: ***${supabaseAnonKey.slice(-6)}`);
  
  
  
  try {
    console.log('Checking for missing tables...');
    
    // First, check if we can connect to Supabase
    const { data: connectionTest, error: connectionError } = await supabase
      .from('teams')
      .select('count')
      .limit(0);
      
    if (connectionError) {
      console.error('Error connecting to Supabase:', connectionError);
      return;
    }
    
    console.log('✅ Successfully connected to Supabase');
    
    // Check if profiles table exists
    const { data: profilesCheck, error: profilesError } = await supabase
      .from('profiles')
      .select('count')
      .limit(0);
      
    if (profilesError && profilesError.code === '42P01') {
      console.log('❌ Profiles table does not exist');
      console.log('To create the profiles table, you need to run the SQL migrations manually.');
      console.log('Please run the following SQL in your Supabase SQL editor:');
      
      const profilesSql = readFileSync(join(__dirname, 'supabase/migrations/20240701000000_add_profiles_table.sql'), 'utf8');
      console.log('\n--- SQL to create profiles table ---');
      console.log(profilesSql);
      console.log('--- End of SQL ---\n');
    } else {
      console.log('✅ Profiles table exists');
    }
    
    // Check if admin_audit_logs table exists
    const { data: logsCheck, error: logsError } = await supabase
      .from('admin_audit_logs')
      .select('count')
      .limit(0);
      
    if (logsError && logsError.code === '42P01') {
      console.log('❌ Admin audit logs table does not exist');
      console.log('To create the admin_audit_logs table, you need to run the SQL migrations manually.');
      console.log('Please run the following SQL in your Supabase SQL editor:');
      
      const adminSql = readFileSync(join(__dirname, 'supabase/migrations/20240512000000_add_admin_functionality.sql'), 'utf8');
      console.log('\n--- SQL to create admin functionality ---');
      console.log(adminSql);
      console.log('--- End of SQL ---\n');
    } else {
      console.log('✅ Admin audit logs table exists');
    }
    
    // Create a profile for the current user
    console.log('Creating a profile for the current user...');
    
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      console.error('Error getting current user:', userError);
    } else if (user) {
      console.log(`Current user: ${user.email}`);
      
      // Check if the user already has a profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();
        
      if (profileError && profileError.code === 'PGRST116') {
        console.log('No profile found for current user. Attempting to create one...');
        
        // Try to create a profile for the current user
        // Note: This will only work if the profiles table exists
        const { error: insertError } = await supabase
          .from('profiles')
          .insert({
            id: user.id,
            full_name: user.user_metadata?.full_name || '',
            role: 'user',
            is_admin: user.email === '<EMAIL>' // Make Noah an admin
          });
          
        if (insertError) {
          if (insertError.code === '42P01') {
            console.error('Cannot create profile: profiles table does not exist');
          } else {
            console.error('Error creating profile:', insertError);
          }
        } else {
          console.log('✅ Created profile for current user');
        }
      } else if (!profileError) {
        console.log('✅ Profile already exists for current user');
      }
    } else {
      console.log('No authenticated user found');
    }
    
    console.log('\nMigration check complete!');
    console.log('If any tables are missing, please run the SQL migrations manually using the Supabase SQL editor.');
  } catch (err) {
    console.error('Error checking migrations:', err);
  }
}

applyMissingMigrations();
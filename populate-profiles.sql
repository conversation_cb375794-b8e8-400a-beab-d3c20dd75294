-- Populate profiles table from auth.users

-- First, check what users exist in auth.users
SELECT id, email, created_at 
FROM auth.users
ORDER BY created_at DESC;

-- Create profiles for all auth users that don't have one
INSERT INTO public.profiles (id, email, full_name, role, is_admin, created_at, updated_at)
SELECT 
    u.id,
    u.email,
    COALESCE(u.raw_user_meta_data->>'full_name', u.email),
    'user',
    CASE 
        WHEN u.email IN ('<EMAIL>', '<EMAIL>') THEN TRUE
        ELSE FALSE
    END,
    u.created_at,
    NOW()
FROM auth.users u
LEFT JOIN public.profiles p ON u.id = p.id
WHERE p.id IS NULL;

-- Update existing profiles that might be missing email
UPDATE public.profiles p
SET 
    email = u.email,
    updated_at = NOW()
FROM auth.users u
WHERE p.id = u.id 
AND (p.email IS NULL OR p.email = '');

-- Set admin status for specific emails
UPDATE public.profiles
SET is_admin = TRUE
WHERE email IN ('<EMAIL>', '<EMAIL>');

-- Verify the results
SELECT 
    p.id,
    p.email,
    p.full_name,
    p.role,
    p.is_admin,
    p.created_at,
    CASE 
        WHEN t.user_id IS NOT NULL THEN 'Has teams'
        ELSE 'No teams'
    END as team_status,
    CASE 
        WHEN s.user_id IS NOT NULL THEN 'Has subscription'
        ELSE 'No subscription'
    END as subscription_status
FROM public.profiles p
LEFT JOIN (SELECT DISTINCT user_id FROM public.teams) t ON p.id = t.user_id
LEFT JOIN (SELECT DISTINCT user_id FROM public.subscriptions) s ON p.id = s.user_id
ORDER BY p.created_at DESC;
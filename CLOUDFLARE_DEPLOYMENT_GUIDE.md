# Cloudflare Pages Deployment Guide for Stripe Production

## Setting Environment Variables in Cloudflare Pages

1. **Go to Cloudflare Dashboard**
   - Visit: https://dash.cloudflare.com
   - Select your account
   - Navigate to "Workers & Pages" in the left sidebar

2. **Find Your Project**
   - Look for "dugoutboss" or your project name
   - Click on the project

3. **Go to Settings → Environment Variables**
   - Click on "Settings" tab
   - Scroll down to "Environment variables"
   - Click "Add variables"

4. **Add Production Variables**
   Add these three variables:

   | Variable Name | Value |
   |--------------|-------|
   | `VITE_STRIPE_PUBLISHABLE_KEY` | Your pk_live_... key from Stripe |
   | `VITE_SUPABASE_URL` | `https://mhuuptkgohuztjrovpxz.supabase.co` |
   | `VITE_SUPABASE_ANON_KEY` | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.aGUpltDKIHYJECOuFHeES7VJp7RKlMjArSg7NxFai_k` |

5. **Deploy the Changes**
   - After adding variables, go to "Deployments" tab
   - Click "Retry deployment" on the latest deployment
   - OR push a new commit to trigger a fresh deployment

## Deploying Edge Functions to Supabase

Since you're using Cloudflare Pages for the frontend, the Supabase Edge Functions need to be deployed separately:

```bash
# If you have Docker Desktop running:
./deploy-edge-functions.sh

# If not, deploy through Supabase Dashboard:
# 1. Go to your Supabase project
# 2. Navigate to Edge Functions
# 3. Deploy each function manually
```

## Testing Your Live Setup

1. **Clear your browser cache** (important!)
2. Visit https://dugoutboss.com/pricing
3. Click "Get Started" on any plan
4. Complete checkout with a real credit card
5. Verify:
   - Payment succeeds
   - You're redirected to success page
   - Check Stripe Dashboard for the payment
   - Check Supabase logs for webhook processing

## Troubleshooting

If payments aren't working:

1. **Check Browser Console** for errors (F12)
2. **Verify in Stripe Dashboard**:
   - Webhook endpoint is active
   - Webhook URL: `https://mhuuptkgohuztjrovpxz.supabase.co/functions/v1/stripe-webhook`
3. **Check Cloudflare deployment**:
   - Ensure environment variables are set
   - Check deployment logs for errors
4. **Verify Supabase Edge Functions**:
   - Check function logs in Supabase Dashboard
   - Ensure secrets are set (STRIPE_SECRET_KEY, STRIPE_WEBHOOK_SECRET)